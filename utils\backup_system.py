"""
Система резервного копирования для ресторанной системы управления
Автоматическое создание резервных копий базы данных и важных файлов
"""

import os
import shutil
import sqlite3
import zipfile
import threading
import time
import schedule
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import json

class BackupSystem:
    """Система резервного копирования"""
    
    def __init__(self, db_path: str = "restaurant.db", backup_dir: str = "backups"):
        self.db_path = db_path
        self.backup_dir = backup_dir
        self.ensure_backup_directory()
        self.backup_settings = self.load_backup_settings()
        self.setup_automatic_backup()
    
    def ensure_backup_directory(self):
        """Создать директорию для резервных копий"""
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
        
        # Создать поддиректории
        subdirs = ['daily', 'weekly', 'monthly', 'manual']
        for subdir in subdirs:
            path = os.path.join(self.backup_dir, subdir)
            if not os.path.exists(path):
                os.makedirs(path)
    
    def load_backup_settings(self) -> Dict:
        """Загрузить настройки резервного копирования"""
        settings_file = os.path.join(self.backup_dir, "backup_settings.json")
        default_settings = {
            "auto_backup_enabled": True,
            "daily_backup_time": "02:00",
            "weekly_backup_day": "sunday",
            "monthly_backup_day": 1,
            "max_daily_backups": 7,
            "max_weekly_backups": 4,
            "max_monthly_backups": 12,
            "compress_backups": True,
            "include_logs": True,
            "include_exports": True
        }
        
        try:
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                # Объединить с настройками по умолчанию
                default_settings.update(settings)
            return default_settings
        except Exception as e:
            print(f"Ошибка загрузки настроек резервного копирования: {e}")
            return default_settings
    
    def save_backup_settings(self):
        """Сохранить настройки резервного копирования"""
        settings_file = os.path.join(self.backup_dir, "backup_settings.json")
        try:
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.backup_settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Ошибка сохранения настроек резервного копирования: {e}")
    
    def create_database_backup(self, backup_type: str = "manual") -> Optional[str]:
        """Создать резервную копию базы данных"""
        try:
            if not os.path.exists(self.db_path):
                raise FileNotFoundError(f"База данных не найдена: {self.db_path}")
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"restaurant_db_{backup_type}_{timestamp}.db"
            backup_path = os.path.join(self.backup_dir, backup_type, backup_filename)
            
            # Создать резервную копию с помощью SQLite backup API
            source_conn = sqlite3.connect(self.db_path)
            backup_conn = sqlite3.connect(backup_path)
            
            source_conn.backup(backup_conn)
            
            source_conn.close()
            backup_conn.close()
            
            # Сжать если включено
            if self.backup_settings.get("compress_backups", True):
                compressed_path = backup_path + ".zip"
                with zipfile.ZipFile(compressed_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    zipf.write(backup_path, backup_filename)
                os.remove(backup_path)  # Удалить несжатую версию
                backup_path = compressed_path
            
            print(f"Резервная копия базы данных создана: {backup_path}")
            return backup_path
            
        except Exception as e:
            print(f"Ошибка создания резервной копии базы данных: {e}")
            return None
    
    def create_full_backup(self, backup_type: str = "manual") -> Optional[str]:
        """Создать полную резервную копию системы"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"restaurant_full_{backup_type}_{timestamp}.zip"
            backup_path = os.path.join(self.backup_dir, backup_type, backup_filename)
            
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Добавить базу данных
                if os.path.exists(self.db_path):
                    zipf.write(self.db_path, "database/restaurant.db")
                
                # Добавить логи если включено
                if self.backup_settings.get("include_logs", True):
                    logs_dir = "logs"
                    if os.path.exists(logs_dir):
                        for root, dirs, files in os.walk(logs_dir):
                            for file in files:
                                file_path = os.path.join(root, file)
                                arc_path = os.path.relpath(file_path)
                                zipf.write(file_path, arc_path)
                
                # Добавить экспорты если включено
                if self.backup_settings.get("include_exports", True):
                    exports_dir = "exports"
                    if os.path.exists(exports_dir):
                        for root, dirs, files in os.walk(exports_dir):
                            for file in files:
                                file_path = os.path.join(root, file)
                                arc_path = os.path.relpath(file_path)
                                zipf.write(file_path, arc_path)
                
                # Добавить конфигурационные файлы
                config_files = ["config.json", "settings.json"]
                for config_file in config_files:
                    if os.path.exists(config_file):
                        zipf.write(config_file, f"config/{config_file}")
                
                # Добавить информацию о резервной копии
                backup_info = {
                    "backup_type": backup_type,
                    "timestamp": datetime.now().isoformat(),
                    "version": "1.0.0",
                    "includes": {
                        "database": True,
                        "logs": self.backup_settings.get("include_logs", True),
                        "exports": self.backup_settings.get("include_exports", True)
                    }
                }
                
                zipf.writestr("backup_info.json", 
                            json.dumps(backup_info, ensure_ascii=False, indent=2))
            
            print(f"Полная резервная копия создана: {backup_path}")
            return backup_path
            
        except Exception as e:
            print(f"Ошибка создания полной резервной копии: {e}")
            return None
    
    def restore_database(self, backup_path: str) -> bool:
        """Восстановить базу данных из резервной копии"""
        try:
            if not os.path.exists(backup_path):
                raise FileNotFoundError(f"Резервная копия не найдена: {backup_path}")
            
            # Создать резервную копию текущей базы данных
            if os.path.exists(self.db_path):
                current_backup = f"{self.db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copy2(self.db_path, current_backup)
                print(f"Текущая база данных сохранена как: {current_backup}")
            
            # Восстановить из резервной копии
            if backup_path.endswith('.zip'):
                # Распаковать сжатую резервную копию
                with zipfile.ZipFile(backup_path, 'r') as zipf:
                    # Найти файл базы данных в архиве
                    db_files = [f for f in zipf.namelist() if f.endswith('.db')]
                    if db_files:
                        zipf.extract(db_files[0], os.path.dirname(self.db_path))
                        extracted_path = os.path.join(os.path.dirname(self.db_path), db_files[0])
                        shutil.move(extracted_path, self.db_path)
                    else:
                        raise ValueError("Файл базы данных не найден в архиве")
            else:
                # Простое копирование
                shutil.copy2(backup_path, self.db_path)
            
            print(f"База данных восстановлена из: {backup_path}")
            return True
            
        except Exception as e:
            print(f"Ошибка восстановления базы данных: {e}")
            return False
    
    def cleanup_old_backups(self):
        """Очистить старые резервные копии"""
        try:
            backup_types = {
                'daily': self.backup_settings.get('max_daily_backups', 7),
                'weekly': self.backup_settings.get('max_weekly_backups', 4),
                'monthly': self.backup_settings.get('max_monthly_backups', 12)
            }
            
            for backup_type, max_count in backup_types.items():
                backup_dir = os.path.join(self.backup_dir, backup_type)
                if not os.path.exists(backup_dir):
                    continue
                
                # Получить список файлов резервных копий
                backup_files = []
                for file in os.listdir(backup_dir):
                    file_path = os.path.join(backup_dir, file)
                    if os.path.isfile(file_path):
                        backup_files.append((file_path, os.path.getmtime(file_path)))
                
                # Сортировать по времени модификации (новые первыми)
                backup_files.sort(key=lambda x: x[1], reverse=True)
                
                # Удалить старые файлы
                if len(backup_files) > max_count:
                    for file_path, _ in backup_files[max_count:]:
                        os.remove(file_path)
                        print(f"Удалена старая резервная копия: {file_path}")
                        
        except Exception as e:
            print(f"Ошибка очистки старых резервных копий: {e}")
    
    def get_backup_list(self) -> Dict[str, List[Dict]]:
        """Получить список всех резервных копий"""
        backup_list = {}
        
        for backup_type in ['daily', 'weekly', 'monthly', 'manual']:
            backup_dir = os.path.join(self.backup_dir, backup_type)
            backup_list[backup_type] = []
            
            if os.path.exists(backup_dir):
                for file in os.listdir(backup_dir):
                    file_path = os.path.join(backup_dir, file)
                    if os.path.isfile(file_path):
                        stat = os.stat(file_path)
                        backup_list[backup_type].append({
                            'filename': file,
                            'path': file_path,
                            'size': stat.st_size,
                            'created': datetime.fromtimestamp(stat.st_ctime),
                            'modified': datetime.fromtimestamp(stat.st_mtime)
                        })
                
                # Сортировать по дате создания (новые первыми)
                backup_list[backup_type].sort(key=lambda x: x['created'], reverse=True)
        
        return backup_list
    
    def setup_automatic_backup(self):
        """Настроить автоматическое резервное копирование"""
        if not self.backup_settings.get("auto_backup_enabled", True):
            return
        
        # Ежедневное резервное копирование
        daily_time = self.backup_settings.get("daily_backup_time", "02:00")
        schedule.every().day.at(daily_time).do(self.daily_backup_job)
        
        # Еженедельное резервное копирование
        weekly_day = self.backup_settings.get("weekly_backup_day", "sunday")
        getattr(schedule.every(), weekly_day).at(daily_time).do(self.weekly_backup_job)
        
        # Ежемесячное резервное копирование
        monthly_day = self.backup_settings.get("monthly_backup_day", 1)
        # Примерная реализация ежемесячного резервного копирования
        schedule.every().day.at(daily_time).do(self.check_monthly_backup)
        
        # Запустить планировщик в отдельном потоке
        def run_scheduler():
            while True:
                schedule.run_pending()
                time.sleep(60)  # Проверять каждую минуту
        
        scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        scheduler_thread.start()
        
        print("Автоматическое резервное копирование настроено")
    
    def daily_backup_job(self):
        """Задача ежедневного резервного копирования"""
        print("Выполнение ежедневного резервного копирования...")
        self.create_database_backup("daily")
        self.cleanup_old_backups()
    
    def weekly_backup_job(self):
        """Задача еженедельного резервного копирования"""
        print("Выполнение еженедельного резервного копирования...")
        self.create_full_backup("weekly")
        self.cleanup_old_backups()
    
    def check_monthly_backup(self):
        """Проверить необходимость ежемесячного резервного копирования"""
        today = datetime.now()
        monthly_day = self.backup_settings.get("monthly_backup_day", 1)
        
        if today.day == monthly_day:
            print("Выполнение ежемесячного резервного копирования...")
            self.create_full_backup("monthly")
            self.cleanup_old_backups()

# Глобальный экземпляр системы резервного копирования
backup_system = None

def init_backup_system(db_path: str = "restaurant.db", backup_dir: str = "backups"):
    """Инициализировать систему резервного копирования"""
    global backup_system
    backup_system = BackupSystem(db_path, backup_dir)
    return backup_system

def get_backup_system():
    """Получить экземпляр системы резервного копирования"""
    global backup_system
    if backup_system is None:
        backup_system = BackupSystem()
    return backup_system
