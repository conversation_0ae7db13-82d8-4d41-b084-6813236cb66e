import json
import os

CONFIG_FILE = 'config.json'

class ConfigManager:
    """Manages loading and saving of the application configuration."""

    def __init__(self, config_path=CONFIG_FILE):
        self.config_path = config_path
        self.config = self.load_config()

    def get_default_config(self):
        """Returns the default configuration dictionary."""
        # Use paths relative to the project root to ensure portability
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

        return {
            "database": {
                "last_used_db_name": "