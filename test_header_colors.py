#!/usr/bin/env python3
"""
Test header colors and contrast
"""

import tkinter as tk
from tkinter import ttk

def test_header_colors():
    """Test different header color combinations"""
    root = tk.Tk()
    root.title("Тест Цветов Заголовков")
    root.geometry("800x600")
    root.configure(bg='#f8fafc')
    
    # Create main frame
    main_frame = tk.Frame(root, bg='#f8fafc')
    main_frame.pack(fill='both', expand=True, padx=20, pady=20)
    
    # Title
    title_label = tk.Label(main_frame, text="Тест Читаемости Заголовков Таблиц",
                          font=('Arial', 16, 'bold'),
                          bg='#f8fafc',
                          fg='#1f2937')
    title_label.pack(pady=(0, 20))
    
    # Test different color combinations
    color_tests = [
        ("Тёмно-синий (ТЕКУЩИЙ)", "#1e3a8a", "#ffffff"),
        ("Очень тёмно-синий", "#0f172a", "#ffffff"),
        ("Средний синий", "#3b82f6", "#ffffff"),
        ("Светло-синий", "#60a5fa", "#000000"),
        ("Серый", "#6b7280", "#ffffff"),
    ]
    
    for i, (name, bg_color, fg_color) in enumerate(color_tests):
        # Create frame for each test
        test_frame = tk.Frame(main_frame, bg='#ffffff', relief='solid', bd=1)
        test_frame.pack(fill='x', pady=5)
        
        # Label with color name
        name_label = tk.Label(test_frame, text=name,
                             font=('Arial', 10, 'bold'),
                             bg='#ffffff',
                             fg='#1f2937')
        name_label.pack(side='left', padx=10, pady=5)
        
        # Sample header
        header_label = tk.Label(test_frame, text="Название Блюда | Отдел | Количество | Цена",
                               font=('Arial', 11, 'bold'),
                               bg=bg_color,
                               fg=fg_color,
                               relief='solid',
                               bd=2,
                               padx=20,
                               pady=8)
        header_label.pack(side='right', padx=10, pady=5)
    
    # Add recommendation
    rec_frame = tk.Frame(main_frame, bg='#dcfce7', relief='solid', bd=2)
    rec_frame.pack(fill='x', pady=(20, 0))
    
    rec_label = tk.Label(rec_frame,
                        text="✅ ИСПОЛЬЗУЕТСЯ: Тёмно-синий (#1e3a8a) - классический тёмно-синий цвет с отличной читаемостью",
                        font=('Arial', 12, 'bold'),
                        bg='#dcfce7',
                        fg='#166534',
                        wraplength=700)
    rec_label.pack(padx=20, pady=15)
    
    # Color codes info
    info_frame = tk.Frame(main_frame, bg='#f1f5f9', relief='solid', bd=1)
    info_frame.pack(fill='x', pady=(10, 0))
    
    info_text = """
Цветовые коды:
• Тёмно-синий: #1e3a8a (blue-800) - ТЕКУЩИЙ ЦВЕТ - отличная читаемость
• Очень тёмно-синий: #0f172a (slate-900) - максимальный контраст
• Средний синий: #3b82f6 (blue-500) - средний контраст
• Белый текст: #ffffff - для тёмных фонов
• Эффекты наведения: #2563eb (светлее при наведении)
    """
    
    info_label = tk.Label(info_frame, text=info_text,
                         font=('Arial', 9),
                         bg='#f1f5f9',
                         fg='#475569',
                         justify='left')
    info_label.pack(padx=20, pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    test_header_colors()
