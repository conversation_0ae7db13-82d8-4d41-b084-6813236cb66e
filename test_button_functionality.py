#!/usr/bin/env python3
"""
Тестирование функциональности кнопок главного меню
"""

import sys
import os
import importlib
import traceback

# Добавляем путь к модулям
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_module_imports():
    """Тестировать импорт всех модулей"""
    
    # Список модулей из main_window.py
    modules_to_test = [
        ('realtime_dashboard', 'create_realtime_dashboard'),
        ('enhanced_reporting_system', 'create_enhanced_reporting_system'),
        ('enhanced_mobile_web', 'show_enhanced_mobile_web'),
        ('integration_apis', 'create_integration_apis_manager'),
        ('advanced_security', 'create_advanced_security_manager'),
        ('automated_backup', 'create_automated_backup_manager'),
        ('multi_location_manager', 'create_multi_location_manager'),
        ('advanced_analytics', 'show_advanced_analytics'),
        ('security_management', 'show_security_management'),
        ('advanced_inventory', 'show_advanced_inventory'),
        ('kitchen_display_system', 'create_kitchen_display_system'),
        ('sales_data_viewer', 'create_sales_data_viewer'),
        ('inventory_manager', 'create_inventory_manager'),
        ('recipe_manager_working', 'create_recipe_manager'),
        ('financial_dashboard', 'create_financial_dashboard'),
        ('accounting_module', 'create_accounting_module'),
        ('payroll_module', 'create_payroll_module'),
        ('staff_scheduling', 'create_staff_scheduling_system'),
        ('customer_crm', 'create_customer_crm'),
        ('table_booking', 'create_table_booking_system'),
        ('menu_planning', 'create_menu_planning_system'),
        ('quality_control', 'create_quality_control_system'),
        ('reports_system', 'create_reports_system'),
        ('notification_center', 'create_notification_center'),
        ('backup_manager', 'create_backup_manager'),
        ('style_configurator', 'create_style_configurator'),
        ('payment_codes_manager', 'create_payment_codes_manager'),
        ('settings_manager', 'create_settings_manager'),
        ('reports_simple', 'create_reports_manager'),
        ('ai_insights', 'create_ai_insights_manager'),
        ('language_settings', 'create_language_settings_manager'),
        ('advanced_reports_simple', 'create_advanced_reports_manager'),
        ('professional_reporting', 'show_professional_reporting'),
        ('mobile_web_interface', 'create_mobile_web_interface'),
        ('performance_optimization', 'show_performance_optimization'),
        ('sync_monitor', 'SyncMonitorWindow'),
        ('customer_relationship_management', 'create_customer_relationship_management'),
        ('loyalty_rewards_system', 'create_loyalty_rewards_system'),
        ('advanced_business_intelligence', 'create_advanced_business_intelligence'),
        ('table_reservation_queue_management', 'create_table_reservation_queue_management'),
        ('supply_chain_vendor_management', 'create_supply_chain_vendor_management'),
        ('financial_planning_budgeting', 'create_financial_planning_budgeting'),
        ('performance_dashboard', 'create_performance_dashboard'),
        ('data_export_import', 'create_data_export_import_system'),
        ('cost_control_simple', 'create_cost_control_system'),
        ('security_audit_system', 'create_security_audit_system'),
        ('mobile_api_integration', 'create_mobile_api_integration'),
    ]
    
    print("🧪 ТЕСТИРОВАНИЕ ИМПОРТА МОДУЛЕЙ")
    print("=" * 60)
    
    working_modules = []
    broken_modules = []
    
    for module_name, function_name in modules_to_test:
        try:
            # Пытаемся импортировать модуль
            module = importlib.import_module(f'modules.{module_name}')
            
            # Проверяем наличие функции
            if hasattr(module, function_name):
                working_modules.append((module_name, function_name, "✅ OK"))
                print(f"✅ {module_name}.{function_name}")
            else:
                broken_modules.append((module_name, function_name, f"❌ Функция {function_name} не найдена"))
                print(f"❌ {module_name}.{function_name} - функция не найдена")
                
        except ImportError as e:
            broken_modules.append((module_name, function_name, f"❌ Ошибка импорта: {e}"))
            print(f"❌ {module_name} - ошибка импорта: {e}")
        except Exception as e:
            broken_modules.append((module_name, function_name, f"❌ Ошибка: {e}"))
            print(f"❌ {module_name} - ошибка: {e}")
    
    print(f"\n📊 РЕЗУЛЬТАТЫ:")
    print(f"   ✅ Работающих модулей: {len(working_modules)}")
    print(f"   ❌ Проблемных модулей: {len(broken_modules)}")
    
    if broken_modules:
        print(f"\n🚨 ПРОБЛЕМНЫЕ МОДУЛИ:")
        for i, (module, func, error) in enumerate(broken_modules, 1):
            print(f"   {i:2d}. {module}.{func}")
            print(f"       {error}")
    
    return working_modules, broken_modules

if __name__ == "__main__":
    working, broken = test_module_imports()
    
    if broken:
        print(f"\n🔧 РЕКОМЕНДАЦИИ:")
        print("   1. Проверьте отсутствующие функции в модулях")
        print("   2. Исправьте ошибки импорта")
        print("   3. Убедитесь, что все зависимости установлены")
    else:
        print(f"\n🎉 ВСЕ МОДУЛИ РАБОТАЮТ КОРРЕКТНО!")
