"""
Менеджер баз данных для управления несколькими ресторанами
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import sqlite3
import os
import json
from datetime import datetime
import shutil

class DatabaseManager:
    """Менеджер для управления несколькими базами данных ресторанов"""
    
    def __init__(self):
        self.databases_config_file = 'config/databases.json'
        self.databases_folder = 'databases'
        self.selected_database = None
        self.window = None
        
        # Создать необходимые папки
        os.makedirs('config', exist_ok=True)
        os.makedirs(self.databases_folder, exist_ok=True)
        
        # Загрузить конфигурацию баз данных
        self.load_databases_config()
    
    def load_databases_config(self):
        """Загрузить конфигурацию баз данных"""
        try:
            if os.path.exists(self.databases_config_file):
                with open(self.databases_config_file, 'r', encoding='utf-8') as f:
                    self.databases = json.load(f)
            else:
                # Создать конфигурацию по умолчанию
                self.databases = {
                    "databases": [
                        {
                            "id": "default_restaurant",
                            "name": "Основной Ресторан",
                            "description": "База данных по умолчанию",
                            "file_path": "databases/default_restaurant.db",
                            "created_date": datetime.now().isoformat(),
                            "last_accessed": datetime.now().isoformat(),
                            "is_active": True
                        }
                    ],
                    "last_selected": "default_restaurant"
                }
                self.save_databases_config()
        except Exception as e:
            print(f"Ошибка загрузки конфигурации баз данных: {e}")
            self.databases = {"databases": [], "last_selected": None}
    
    def save_databases_config(self):
        """Сохранить конфигурацию баз данных"""
        try:
            with open(self.databases_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.databases, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"Ошибка сохранения конфигурации: {e}")
    
    def create_database_selector_window(self):
        """Создать окно выбора базы данных"""
        self.window = tk.Tk()
        self.window.title("🏪 Выбор Ресторана")
        self.window.configure(bg='#f8f9fa')
        self.window.resizable(True, True)

        # Сделать окно максимального размера
        self.window.state('zoomed')  # Для Windows
        # Альтернативно для других ОС:
        # self.window.attributes('-zoomed', True)  # Для Linux
        # self.window.attributes('-fullscreen', True)  # Для macOS

        self.create_selector_interface()
        return self.window
    
    def create_selector_interface(self):
        """Создать интерфейс выбора базы данных"""
        # Заголовок
        header_frame = tk.Frame(self.window, bg='#2c3e50', height=100)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="🏪 Система Управления Ресторанами",
                font=('Cambria', 24, 'bold italic'), bg='#2c3e50', fg='white').pack(side='left', padx=30, pady=30)
        
        # Кнопки управления
        btn_frame = tk.Frame(header_frame, bg='#2c3e50')
        btn_frame.pack(side='right', padx=30, pady=25)
        
        tk.Button(btn_frame, text="➕ Новый Ресторан", command=self.create_new_database,
                 bg='#27ae60', fg='white', font=('Cambria', 14, 'bold italic'),
                 relief='flat', padx=20, pady=10).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="📁 Импорт БД", command=self.import_database,
                 bg='#3498db', fg='white', font=('Cambria', 14, 'bold italic'),
                 relief='flat', padx=20, pady=10).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="🔧 Управление", command=self.manage_databases,
                 bg='#f39c12', fg='white', font=('Cambria', 14, 'bold italic'),
                 relief='flat', padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(btn_frame, text="🗑️ Очистить БД", command=self.clear_database,
                 bg='#e74c3c', fg='white', font=('Cambria', 14, 'bold italic'),
                 relief='flat', padx=20, pady=10).pack(side='left', padx=5)
        
        # Основной контент
        main_frame = tk.Frame(self.window, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True, padx=30, pady=30)
        
        # Заголовок списка
        tk.Label(main_frame, text="📋 Выберите Ресторан для Работы:",
                font=('Cambria', 20, 'bold italic'), bg='#f8f9fa', fg='#2c3e50').pack(pady=(0, 20))
        
        # Скроллируемая область для списка ресторанов
        self.canvas = tk.Canvas(main_frame, bg='#f8f9fa')
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = tk.Frame(self.canvas, bg='#f8f9fa')
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)

        # Добавить поддержку прокрутки колесом мыши
        def _on_mousewheel(event):
            self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        # Привязать прокрутку колесом мыши ко всему окну
        self.window.bind_all("<MouseWheel>", _on_mousewheel)

        # Создать карточки ресторанов
        self.refresh_restaurant_list()

        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Нижняя панель
        bottom_frame = tk.Frame(self.window, bg='#ecf0f1', height=60)
        bottom_frame.pack(fill='x')
        bottom_frame.pack_propagate(False)
        
        tk.Label(bottom_frame, text="💡 Совет: Вы можете создать отдельную базу данных для каждого ресторана",
                font=('Cambria', 12, 'italic'), bg='#ecf0f1', fg='#7f8c8d').pack(side='left', padx=20, pady=20)
        
        tk.Button(bottom_frame, text="❌ Выход", command=self.window.quit,
                 bg='#e74c3c', fg='white', font=('Cambria', 12, 'bold'),
                 relief='flat', padx=15, pady=8).pack(side='right', padx=20, pady=15)
    
    def refresh_restaurant_list(self):
        """Обновить список ресторанов"""
        # Очистить текущий список
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
        
        # Создать карточки для каждого ресторана
        for db_config in self.databases.get('databases', []):
            self.create_restaurant_card(db_config)
        
        # Если нет ресторанов, показать сообщение
        if not self.databases.get('databases', []):
            empty_frame = tk.Frame(self.scrollable_frame, bg='#f8f9fa')
            empty_frame.pack(fill='x', pady=50)
            
            tk.Label(empty_frame, text="📭 Нет доступных ресторанов",
                    font=('Cambria', 18, 'bold italic'), bg='#f8f9fa', fg='#95a5a6').pack()
            
            tk.Label(empty_frame, text="Нажмите '➕ Новый Ресторан' для создания первой базы данных",
                    font=('Cambria', 14, 'italic'), bg='#f8f9fa', fg='#7f8c8d').pack(pady=10)
    
    def create_restaurant_card(self, db_config):
        """Создать карточку ресторана"""
        card_frame = tk.Frame(self.scrollable_frame, bg='white', relief='solid', bd=2)
        card_frame.pack(fill='x', pady=10, padx=20)

        # Добавить поддержку прокрутки для карточки
        def _on_mousewheel(event):
            self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def bind_mousewheel_to_widget(widget):
            """Привязать прокрутку колесом мыши к виджету и всем его дочерним элементам"""
            widget.bind("<MouseWheel>", _on_mousewheel)
            for child in widget.winfo_children():
                bind_mousewheel_to_widget(child)

        bind_mousewheel_to_widget(card_frame)
        
        # Заголовок карточки
        header_card = tk.Frame(card_frame, bg='#34495e', height=60)
        header_card.pack(fill='x')
        header_card.pack_propagate(False)

        
        # Название ресторана
        tk.Label(header_card, text=f"🏪 {db_config['name']}",
                font=('Cambria', 18, 'bold italic'), bg='#34495e', fg='white').pack(side='left', padx=20, pady=15)
        
        # Статус
        status_text = "🟢 Активен" if db_config.get('is_active', True) else "🔴 Неактивен"
        tk.Label(header_card, text=status_text,
                font=('Cambria', 12, 'bold'), bg='#34495e', fg='white').pack(side='right', padx=20, pady=15)
        
        # Содержимое карточки
        content_frame = tk.Frame(card_frame, bg='white')
        content_frame.pack(fill='x', padx=20, pady=15)

        
        # Описание
        tk.Label(content_frame, text=f"📝 {db_config.get('description', 'Нет описания')}",
                font=('Cambria', 14), bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 10))
        
        # Информация
        info_frame = tk.Frame(content_frame, bg='white')
        info_frame.pack(fill='x', pady=(0, 15))
        
        # Дата создания
        created_date = datetime.fromisoformat(db_config['created_date']).strftime("%d.%m.%Y %H:%M")
        tk.Label(info_frame, text=f"📅 Создан: {created_date}",
                font=('Cambria', 11), bg='white', fg='#7f8c8d').pack(side='left')
        
        # Последний доступ
        if 'last_accessed' in db_config:
            last_accessed = datetime.fromisoformat(db_config['last_accessed']).strftime("%d.%m.%Y %H:%M")
            tk.Label(info_frame, text=f"🕒 Последний вход: {last_accessed}",
                    font=('Cambria', 11), bg='white', fg='#7f8c8d').pack(side='right')
        
        # Кнопки действий
        buttons_frame = tk.Frame(content_frame, bg='white')
        buttons_frame.pack(fill='x')
        
        # Кнопка входа
        tk.Button(buttons_frame, text="🚀 Войти в Систему", 
                 command=lambda: self.select_database(db_config),
                 bg='#27ae60', fg='white', font=('Cambria', 14, 'bold italic'),
                 relief='flat', padx=25, pady=10).pack(side='left')
        
        # Кнопка редактирования
        tk.Button(buttons_frame, text="✏️ Редактировать", 
                 command=lambda: self.edit_database(db_config),
                 bg='#3498db', fg='white', font=('Cambria', 12, 'bold'),
                 relief='flat', padx=15, pady=8).pack(side='left', padx=10)
        
        # Кнопка резервной копии
        tk.Button(buttons_frame, text="💾 Резервная копия", 
                 command=lambda: self.backup_database(db_config),
                 bg='#f39c12', fg='white', font=('Cambria', 12, 'bold'),
                 relief='flat', padx=15, pady=8).pack(side='left', padx=5)
        
        # Кнопка удаления
        tk.Button(buttons_frame, text="🗑️ Удалить", 
                 command=lambda: self.delete_database(db_config),
                 bg='#e74c3c', fg='white', font=('Cambria', 12, 'bold'),
                 relief='flat', padx=15, pady=8).pack(side='right')
    
    def select_database(self, db_config):
        """Выбрать базу данных для работы"""
        try:
            # Проверить существование файла базы данных
            if not os.path.exists(db_config['file_path']):
                if messagebox.askyesno("База данных не найдена", 
                                     f"Файл базы данных {db_config['file_path']} не найден.\n"
                                     "Создать новую базу данных?"):
                    self.create_database_file(db_config['file_path'])
                else:
                    return
            
            # Обновить время последнего доступа
            db_config['last_accessed'] = datetime.now().isoformat()
            self.databases['last_selected'] = db_config['id']
            self.save_databases_config()
            
            # Сохранить выбранную базу данных
            self.selected_database = db_config
            
            # Закрыть окно выбора
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось выбрать базу данных: {e}")
    
    def create_database_file(self, file_path):
        """Создать файл базы данных"""
        try:
            # Создать папку если не существует
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # Создать базу данных
            conn = sqlite3.connect(file_path)
            cursor = conn.cursor()
            
            # Создать основные таблицы
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    role TEXT NOT NULL DEFAULT 'user',
                    full_name TEXT,
                    email TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )
            ''')
            
            # Создать пользователя по умолчанию (admin/admin)
            import hashlib
            admin_password = hashlib.sha256("admin".encode()).hexdigest()
            cursor.execute('''
                INSERT OR IGNORE INTO users (username, password_hash, role, full_name)
                VALUES (?, ?, ?, ?)
            ''', ("admin", admin_password, "admin", "Администратор"))
            
            conn.commit()
            conn.close()
            
            messagebox.showinfo("Успех", f"База данных создана: {file_path}\nПользователь по умолчанию: admin/admin")
            
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось создать базу данных: {e}")
    
    def get_selected_database(self):
        """Получить выбранную базу данных"""
        return self.selected_database

    def create_new_database(self):
        """Создать новую базу данных ресторана"""
        dialog = DatabaseCreationDialog(self.window, self)
        self.window.wait_window(dialog.window)

        # Обновить список после создания
        self.refresh_restaurant_list()

    def edit_database(self, db_config):
        """Редактировать конфигурацию базы данных"""
        dialog = DatabaseEditDialog(self.window, self, db_config)
        self.window.wait_window(dialog.window)

        # Обновить список после редактирования
        self.refresh_restaurant_list()

    def delete_database(self, db_config):
        """Удалить базу данных"""
        if messagebox.askyesno("Подтверждение удаления",
                              f"Вы уверены, что хотите удалить ресторан '{db_config['name']}'?\n"
                              "Это действие нельзя отменить!"):
            try:
                # Удалить из конфигурации
                self.databases['databases'] = [db for db in self.databases['databases']
                                             if db['id'] != db_config['id']]

                # Если это была последняя выбранная база данных, сбросить выбор
                if self.databases.get('last_selected') == db_config['id']:
                    self.databases['last_selected'] = None

                # Сохранить конфигурацию
                self.save_databases_config()

                # Спросить об удалении файла
                if os.path.exists(db_config['file_path']):
                    if messagebox.askyesno("Удалить файл",
                                         "Также удалить файл базы данных с диска?"):
                        os.remove(db_config['file_path'])

                messagebox.showinfo("Успех", f"Ресторан '{db_config['name']}' удалён")
                self.refresh_restaurant_list()

            except Exception as e:
                messagebox.showerror("Ошибка", f"Не удалось удалить ресторан: {e}")

    def backup_database(self, db_config):
        """Создать резервную копию базы данных"""
        try:
            if not os.path.exists(db_config['file_path']):
                messagebox.showerror("Ошибка", "Файл базы данных не найден")
                return

            # Создать папку для резервных копий
            backup_folder = f"backups/{db_config['id']}"
            os.makedirs(backup_folder, exist_ok=True)

            # Создать имя файла резервной копии
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{db_config['id']}_backup_{timestamp}.db"
            backup_path = os.path.join(backup_folder, backup_filename)

            # Скопировать файл
            shutil.copy2(db_config['file_path'], backup_path)

            messagebox.showinfo("Успех", f"Резервная копия создана:\n{backup_path}")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось создать резервную копию: {e}")

    def import_database(self):
        """Импортировать существующую базу данных"""
        from tkinter import filedialog

        file_path = filedialog.askopenfilename(
            title="Выберите файл базы данных",
            filetypes=[("SQLite files", "*.db"), ("All files", "*.*")]
        )

        if file_path:
            dialog = DatabaseImportDialog(self.window, self, file_path)
            self.window.wait_window(dialog.window)

            # Обновить список после импорта
            self.refresh_restaurant_list()

    def manage_databases(self):
        """Открыть окно управления базами данных"""
        dialog = DatabaseManagementDialog(self.window, self)
        self.window.wait_window(dialog.window)

        # Обновить список после управления
        self.refresh_restaurant_list()

    def clear_database(self):
        """Очистить выбранную базу данных от всех записей"""
        try:
            # Получить список доступных баз данных для выбора
            available_databases = [db for db in self.databases.get('databases', []) if db.get('is_active', True)]

            if not available_databases:
                messagebox.showwarning("Предупреждение", "Нет доступных баз данных для очистки")
                return

            # Создать диалог выбора базы данных для очистки
            dialog = DatabaseClearDialog(self.window, self, available_databases)
            self.window.wait_window(dialog.window)

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка при очистке базы данных: {e}")


class DatabaseCreationDialog:
    """Диалог создания новой базы данных"""

    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.create_dialog()

    def create_dialog(self):
        """Создать диалог"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("➕ Создание Нового Ресторана")
        self.window.geometry("700x700")  # Увеличили размер
        self.window.configure(bg='white')
        self.window.resizable(True, True)  # Разрешили изменение размера

        # Центрировать окно
        self.window.transient(self.parent)
        self.window.grab_set()

        # Центрировать на экране
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (700 // 2)
        y = (self.window.winfo_screenheight() // 2) - (700 // 2)
        self.window.geometry(f"700x700+{x}+{y}")

        # Заголовок
        header_frame = tk.Frame(self.window, bg='#27ae60', height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="➕ Создание Нового Ресторана",
                font=('Cambria', 18, 'bold italic'), bg='#27ae60', fg='white').pack(side='left', padx=25, pady=25)

        # Форма
        form_frame = tk.Frame(self.window, bg='white')
        form_frame.pack(fill='both', expand=True, padx=30, pady=30)

        # Название ресторана
        tk.Label(form_frame, text="🏪 Название ресторана:",
                font=('Cambria', 14, 'bold'), bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))

        self.name_var = tk.StringVar()
        name_entry = tk.Entry(form_frame, textvariable=self.name_var,
                             font=('Cambria', 14), width=40)
        name_entry.pack(fill='x', pady=(0, 20))
        name_entry.focus()

        # Описание
        tk.Label(form_frame, text="📝 Описание:",
                font=('Cambria', 14, 'bold'), bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))

        self.description_var = tk.StringVar()
        description_entry = tk.Entry(form_frame, textvariable=self.description_var,
                                   font=('Cambria', 14), width=40)
        description_entry.pack(fill='x', pady=(0, 20))

        # ID ресторана (автоматически генерируется)
        tk.Label(form_frame, text="🆔 ID ресторана:",
                font=('Cambria', 14, 'bold'), bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))

        self.id_var = tk.StringVar()
        id_entry = tk.Entry(form_frame, textvariable=self.id_var,
                           font=('Cambria', 14), width=40)
        id_entry.pack(fill='x', pady=(0, 10))

        tk.Label(form_frame, text="💡 ID будет использоваться для имени файла базы данных",
                font=('Cambria', 11, 'italic'), bg='white', fg='#7f8c8d').pack(anchor='w', pady=(0, 20))

        # Автоматическое заполнение ID при вводе названия
        def update_id(*args):
            name = self.name_var.get()
            # Создать ID из названия (убрать пробелы, спецсимволы, сделать lowercase)
            import re
            id_text = re.sub(r'[^a-zA-Zа-яА-Я0-9]', '_', name).lower()
            id_text = re.sub(r'_+', '_', id_text).strip('_')
            self.id_var.set(id_text)

        self.name_var.trace('w', update_id)

        # Создать пользователя администратора
        admin_frame = tk.LabelFrame(form_frame, text="👤 Администратор по умолчанию",
                                   font=('Cambria', 12, 'bold'), bg='white', fg='#2c3e50')
        admin_frame.pack(fill='x', pady=20)

        tk.Label(admin_frame, text="Имя пользователя:",
                font=('Cambria', 12), bg='white', fg='#2c3e50').pack(anchor='w', padx=15, pady=(10, 5))

        self.admin_username_var = tk.StringVar(value="admin")
        admin_username_entry = tk.Entry(admin_frame, textvariable=self.admin_username_var,
                                       font=('Cambria', 12), width=30)
        admin_username_entry.pack(anchor='w', padx=15, pady=(0, 10))

        tk.Label(admin_frame, text="Пароль:",
                font=('Cambria', 12), bg='white', fg='#2c3e50').pack(anchor='w', padx=15, pady=(0, 5))

        self.admin_password_var = tk.StringVar(value="admin")
        admin_password_entry = tk.Entry(admin_frame, textvariable=self.admin_password_var,
                                       font=('Cambria', 12), width=30, show="*")
        admin_password_entry.pack(anchor='w', padx=15, pady=(0, 15))

        # Разделитель перед кнопками
        separator = tk.Frame(form_frame, height=2, bg='#dee2e6')
        separator.pack(fill='x', pady=(20, 0))

        # Кнопки
        buttons_frame = tk.Frame(self.window, bg='white')
        buttons_frame.pack(fill='x', padx=30, pady=(20, 30))

        # Кнопка отмены
        cancel_btn = tk.Button(buttons_frame, text="❌ Отмена", command=self.window.destroy,
                              bg='#6c757d', fg='white', font=('Cambria', 14, 'bold'),
                              relief='flat', padx=30, pady=12, cursor='hand2')
        cancel_btn.pack(side='right', padx=(10, 0))

        # Кнопка создания
        create_btn = tk.Button(buttons_frame, text="✅ Создать Ресторан", command=self.create_database,
                              bg='#27ae60', fg='white', font=('Cambria', 14, 'bold'),
                              relief='flat', padx=30, pady=12, cursor='hand2')
        create_btn.pack(side='right')

    def create_database(self):
        """Создать новую базу данных"""
        try:
            name = self.name_var.get().strip()
            description = self.description_var.get().strip()
            db_id = self.id_var.get().strip()
            admin_username = self.admin_username_var.get().strip()
            admin_password = self.admin_password_var.get().strip()

            # Валидация
            if not name:
                messagebox.showerror("Ошибка", "Введите название ресторана")
                return

            if not db_id:
                messagebox.showerror("Ошибка", "Введите ID ресторана")
                return

            if not admin_username or not admin_password:
                messagebox.showerror("Ошибка", "Введите данные администратора")
                return

            # Проверить уникальность ID
            existing_ids = [db['id'] for db in self.db_manager.databases.get('databases', [])]
            if db_id in existing_ids:
                messagebox.showerror("Ошибка", "Ресторан с таким ID уже существует")
                return

            # Создать конфигурацию
            db_config = {
                "id": db_id,
                "name": name,
                "description": description or "Нет описания",
                "file_path": f"databases/{db_id}.db",
                "created_date": datetime.now().isoformat(),
                "last_accessed": datetime.now().isoformat(),
                "is_active": True
            }

            # Создать файл базы данных
            self.create_database_file(db_config['file_path'], admin_username, admin_password)

            # Добавить в конфигурацию
            self.db_manager.databases['databases'].append(db_config)
            self.db_manager.save_databases_config()

            messagebox.showinfo("Успех", f"Ресторан '{name}' успешно создан!")
            self.window.destroy()

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось создать ресторан: {e}")

    def create_database_file(self, file_path, admin_username, admin_password):
        """Создать файл базы данных с пользователем"""
        try:
            # Создать папку если не существует
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # Создать базу данных
            conn = sqlite3.connect(file_path)
            cursor = conn.cursor()

            # Создать таблицу пользователей
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    role TEXT NOT NULL DEFAULT 'user',
                    full_name TEXT,
                    email TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )
            ''')

            # Создать пользователя администратора
            import hashlib
            password_hash = hashlib.sha256(admin_password.encode()).hexdigest()
            cursor.execute('''
                INSERT INTO users (username, password_hash, role, full_name)
                VALUES (?, ?, ?, ?)
            ''', (admin_username, password_hash, "admin", "Администратор"))

            # Создать другие необходимые таблицы
            self.create_additional_tables(cursor)

            conn.commit()
            conn.close()

        except Exception as e:
            raise Exception(f"Ошибка создания базы данных: {e}")

    def create_additional_tables(self, cursor):
        """Создать дополнительные таблицы"""
        # Таблица настроек
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT UNIQUE NOT NULL,
                value TEXT,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Добавить базовые настройки
        settings = [
            ('restaurant_name', 'Новый Ресторан', 'Название ресторана'),
            ('currency', '₽', 'Валюта'),
            ('timezone', 'Europe/Moscow', 'Часовой пояс'),
            ('language', 'ru', 'Язык интерфейса')
        ]

        for key, value, description in settings:
            cursor.execute('''
                INSERT OR IGNORE INTO settings (key, value, description)
                VALUES (?, ?, ?)
            ''', (key, value, description))


class DatabaseEditDialog:
    """Диалог редактирования базы данных"""

    def __init__(self, parent, db_manager, db_config):
        self.parent = parent
        self.db_manager = db_manager
        self.db_config = db_config
        self.window = None
        self.create_dialog()

    def create_dialog(self):
        """Создать диалог редактирования"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("✏️ Редактирование Ресторана")
        self.window.geometry("650x550")  # Увеличен размер для показа всех полей
        self.window.configure(bg='white')
        self.window.resizable(True, True)  # Разрешить изменение размера

        # Центрировать окно
        self.window.transient(self.parent)
        self.window.grab_set()

        # Центрировать диалог
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (650 // 2)
        y = (self.window.winfo_screenheight() // 2) - (550 // 2)
        self.window.geometry(f"650x550+{x}+{y}")

        # Заголовок
        header_frame = tk.Frame(self.window, bg='#3498db', height=70)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="✏️ Редактирование Ресторана",
                font=('Cambria', 16, 'bold italic'), bg='#3498db', fg='white').pack(side='left', padx=20, pady=20)

        # Форма
        form_frame = tk.Frame(self.window, bg='white')
        form_frame.pack(fill='both', expand=True, padx=25, pady=25)

        # Название
        tk.Label(form_frame, text="🏪 Название:",
                font=('Cambria', 12, 'bold'), bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))

        self.name_var = tk.StringVar(value=self.db_config['name'])
        name_entry = tk.Entry(form_frame, textvariable=self.name_var,
                             font=('Cambria', 12), width=40)
        name_entry.pack(fill='x', pady=(0, 15))

        # Описание
        tk.Label(form_frame, text="📝 Описание:",
                font=('Cambria', 12, 'bold'), bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))

        self.description_var = tk.StringVar(value=self.db_config.get('description', ''))
        description_entry = tk.Entry(form_frame, textvariable=self.description_var,
                                   font=('Cambria', 12), width=40)
        description_entry.pack(fill='x', pady=(0, 15))

        # Статус
        self.active_var = tk.BooleanVar(value=self.db_config.get('is_active', True))
        active_check = tk.Checkbutton(form_frame, text="🟢 Ресторан активен",
                                     variable=self.active_var,
                                     font=('Cambria', 12, 'bold'), bg='white', fg='#2c3e50')
        active_check.pack(anchor='w', pady=15)

        # Информация о файле
        info_frame = tk.LabelFrame(form_frame, text="📁 Информация о файле",
                                  font=('Cambria', 11, 'bold'), bg='white', fg='#2c3e50')
        info_frame.pack(fill='x', pady=15)

        tk.Label(info_frame, text=f"Путь: {self.db_config['file_path']}",
                font=('Cambria', 10), bg='white', fg='#7f8c8d').pack(anchor='w', padx=10, pady=5)

        file_exists = os.path.exists(self.db_config['file_path'])
        status_text = "✅ Файл существует" if file_exists else "❌ Файл не найден"
        status_color = "#27ae60" if file_exists else "#e74c3c"

        tk.Label(info_frame, text=status_text,
                font=('Cambria', 10, 'bold'), bg='white', fg=status_color).pack(anchor='w', padx=10, pady=(0, 10))

        # Кнопки
        buttons_frame = tk.Frame(self.window, bg='white')
        buttons_frame.pack(fill='x', padx=25, pady=(0, 25))

        tk.Button(buttons_frame, text="💾 Сохранить", command=self.save_changes,
                 bg='#27ae60', fg='white', font=('Cambria', 12, 'bold'),
                 relief='flat', padx=20, pady=8).pack(side='right', padx=5)

        tk.Button(buttons_frame, text="❌ Отмена", command=self.window.destroy,
                 bg='#e74c3c', fg='white', font=('Cambria', 12, 'bold'),
                 relief='flat', padx=20, pady=8).pack(side='right', padx=5)

    def save_changes(self):
        """Сохранить изменения"""
        try:
            name = self.name_var.get().strip()
            description = self.description_var.get().strip()
            is_active = self.active_var.get()

            if not name:
                messagebox.showerror("Ошибка", "Введите название ресторана")
                return

            # Обновить конфигурацию
            for db in self.db_manager.databases['databases']:
                if db['id'] == self.db_config['id']:
                    db['name'] = name
                    db['description'] = description
                    db['is_active'] = is_active
                    break

            # Сохранить
            self.db_manager.save_databases_config()

            messagebox.showinfo("Успех", "Изменения сохранены")
            self.window.destroy()

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось сохранить изменения: {e}")


class DatabaseImportDialog:
    """Диалог импорта базы данных"""

    def __init__(self, parent, db_manager, file_path):
        self.parent = parent
        self.db_manager = db_manager
        self.source_file_path = file_path
        self.window = None
        self.create_dialog()

    def create_dialog(self):
        """Создать диалог импорта"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("📁 Импорт Базы Данных")
        self.window.geometry("650x550")  # Увеличили размер
        self.window.configure(bg='white')
        self.window.resizable(True, True)  # Разрешили изменение размера

        # Центрировать окно
        self.window.transient(self.parent)
        self.window.grab_set()

        # Центрировать на экране
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (650 // 2)
        y = (self.window.winfo_screenheight() // 2) - (550 // 2)
        self.window.geometry(f"650x550+{x}+{y}")

        # Заголовок
        header_frame = tk.Frame(self.window, bg='#3498db', height=70)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="📁 Импорт Базы Данных",
                font=('Cambria', 16, 'bold italic'), bg='#3498db', fg='white').pack(side='left', padx=20, pady=20)

        # Информация о файле
        info_frame = tk.Frame(self.window, bg='white')
        info_frame.pack(fill='x', padx=25, pady=20)

        tk.Label(info_frame, text="📄 Выбранный файл:",
                font=('Cambria', 12, 'bold'), bg='white', fg='#2c3e50').pack(anchor='w')

        tk.Label(info_frame, text=self.source_file_path,
                font=('Cambria', 10), bg='white', fg='#7f8c8d', wraplength=500).pack(anchor='w', pady=(5, 15))

        # Форма
        form_frame = tk.Frame(self.window, bg='white')
        form_frame.pack(fill='both', expand=True, padx=25)

        # Название ресторана
        tk.Label(form_frame, text="🏪 Название ресторана:",
                font=('Cambria', 12, 'bold'), bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))

        self.name_var = tk.StringVar()
        name_entry = tk.Entry(form_frame, textvariable=self.name_var,
                             font=('Cambria', 12), width=40)
        name_entry.pack(fill='x', pady=(0, 15))
        name_entry.focus()

        # Описание
        tk.Label(form_frame, text="📝 Описание:",
                font=('Cambria', 12, 'bold'), bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))

        self.description_var = tk.StringVar()
        description_entry = tk.Entry(form_frame, textvariable=self.description_var,
                                   font=('Cambria', 12), width=40)
        description_entry.pack(fill='x', pady=(0, 15))

        # ID ресторана
        tk.Label(form_frame, text="🆔 ID ресторана:",
                font=('Cambria', 12, 'bold'), bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))

        self.id_var = tk.StringVar()
        id_entry = tk.Entry(form_frame, textvariable=self.id_var,
                           font=('Cambria', 12), width=40)
        id_entry.pack(fill='x', pady=(0, 10))

        tk.Label(form_frame, text="💡 ID должен быть уникальным",
                font=('Cambria', 10, 'italic'), bg='white', fg='#7f8c8d').pack(anchor='w', pady=(0, 15))

        # Автоматическое заполнение ID
        def update_id(*args):
            name = self.name_var.get()
            import re
            id_text = re.sub(r'[^a-zA-Zа-яА-Я0-9]', '_', name).lower()
            id_text = re.sub(r'_+', '_', id_text).strip('_')
            self.id_var.set(id_text)

        self.name_var.trace('w', update_id)

        # Опции импорта
        options_frame = tk.LabelFrame(form_frame, text="⚙️ Опции импорта",
                                     font=('Cambria', 11, 'bold'), bg='white', fg='#2c3e50')
        options_frame.pack(fill='x', pady=15)

        self.copy_file_var = tk.BooleanVar(value=True)
        copy_check = tk.Checkbutton(options_frame, text="📋 Скопировать файл в папку databases",
                                   variable=self.copy_file_var,
                                   font=('Cambria', 11), bg='white', fg='#2c3e50')
        copy_check.pack(anchor='w', padx=10, pady=10)

        # Разделитель перед кнопками
        separator = tk.Frame(form_frame, height=2, bg='#dee2e6')
        separator.pack(fill='x', pady=(20, 0))

        # Кнопки
        buttons_frame = tk.Frame(self.window, bg='white')
        buttons_frame.pack(fill='x', padx=25, pady=(20, 25))

        # Кнопка отмены
        cancel_btn = tk.Button(buttons_frame, text="❌ Отмена", command=self.window.destroy,
                              bg='#6c757d', fg='white', font=('Cambria', 14, 'bold'),
                              relief='flat', padx=30, pady=12, cursor='hand2')
        cancel_btn.pack(side='right', padx=(10, 0))

        # Кнопка импорта
        import_btn = tk.Button(buttons_frame, text="📁 Импортировать", command=self.import_database,
                              bg='#27ae60', fg='white', font=('Cambria', 14, 'bold'),
                              relief='flat', padx=30, pady=12, cursor='hand2')
        import_btn.pack(side='right')

    def import_database(self):
        """Импортировать базу данных"""
        try:
            name = self.name_var.get().strip()
            description = self.description_var.get().strip()
            db_id = self.id_var.get().strip()
            copy_file = self.copy_file_var.get()

            # Валидация
            if not name:
                messagebox.showerror("Ошибка", "Введите название ресторана")
                return

            if not db_id:
                messagebox.showerror("Ошибка", "Введите ID ресторана")
                return

            # Проверить уникальность ID
            existing_ids = [db['id'] for db in self.db_manager.databases.get('databases', [])]
            if db_id in existing_ids:
                messagebox.showerror("Ошибка", "Ресторан с таким ID уже существует")
                return

            # Определить путь к файлу
            if copy_file:
                target_path = f"databases/{db_id}.db"
                # Скопировать файл
                os.makedirs('databases', exist_ok=True)
                shutil.copy2(self.source_file_path, target_path)
            else:
                target_path = self.source_file_path

            # Создать конфигурацию
            db_config = {
                "id": db_id,
                "name": name,
                "description": description or "Импортированная база данных",
                "file_path": target_path,
                "created_date": datetime.now().isoformat(),
                "last_accessed": datetime.now().isoformat(),
                "is_active": True
            }

            # Добавить в конфигурацию
            self.db_manager.databases['databases'].append(db_config)
            self.db_manager.save_databases_config()

            messagebox.showinfo("Успех", f"База данных '{name}' успешно импортирована!")
            self.window.destroy()

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось импортировать базу данных: {e}")


class DatabaseManagementDialog:
    """Диалог управления базами данных"""

    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.create_dialog()

    def create_dialog(self):
        """Создать диалог управления"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("🔧 Управление Базами Данных")
        self.window.geometry("800x600")
        self.window.configure(bg='white')
        self.window.resizable(True, True)

        # Центрировать окно
        self.window.transient(self.parent)
        self.window.grab_set()

        # Заголовок
        header_frame = tk.Frame(self.window, bg='#f39c12', height=70)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="🔧 Управление Базами Данных",
                font=('Cambria', 16, 'bold italic'), bg='#f39c12', fg='white').pack(side='left', padx=20, pady=20)

        # Статистика
        stats_frame = tk.Frame(self.window, bg='#ecf0f1')
        stats_frame.pack(fill='x', padx=20, pady=20)

        total_dbs = len(self.db_manager.databases.get('databases', []))
        active_dbs = len([db for db in self.db_manager.databases.get('databases', []) if db.get('is_active', True)])

        tk.Label(stats_frame, text=f"📊 Всего ресторанов: {total_dbs} | Активных: {active_dbs}",
                font=('Cambria', 14, 'bold'), bg='#ecf0f1', fg='#2c3e50').pack(pady=15)

        # Таблица баз данных
        table_frame = tk.Frame(self.window, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        # Создать Treeview
        columns = ('ID', 'Название', 'Описание', 'Статус', 'Создан', 'Файл')
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # Настроить заголовки
        headers_config = {
            'ID': 120,
            'Название': 200,
            'Описание': 250,
            'Статус': 80,
            'Создан': 120,
            'Файл': 100
        }

        for col, width in headers_config.items():
            self.tree.heading(col, text=col)
            self.tree.column(col, width=width, anchor='center' if col in ['Статус', 'Файл'] else 'w')

        # Заполнить данными
        self.refresh_table()

        # Скроллбар
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        # Упаковка
        self.tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # Кнопки действий
        actions_frame = tk.Frame(self.window, bg='white')
        actions_frame.pack(fill='x', padx=20, pady=(0, 20))

        tk.Button(actions_frame, text="🔄 Обновить", command=self.refresh_table,
                 bg='#3498db', fg='white', font=('Cambria', 11, 'bold'),
                 relief='flat', padx=15, pady=6).pack(side='left', padx=5)

        tk.Button(actions_frame, text="📊 Проверить Целостность", command=self.check_integrity,
                 bg='#9b59b6', fg='white', font=('Cambria', 11, 'bold'),
                 relief='flat', padx=15, pady=6).pack(side='left', padx=5)

        tk.Button(actions_frame, text="🧹 Очистить Неактивные", command=self.cleanup_inactive,
                 bg='#e67e22', fg='white', font=('Cambria', 11, 'bold'),
                 relief='flat', padx=15, pady=6).pack(side='left', padx=5)

        tk.Button(actions_frame, text="❌ Закрыть", command=self.window.destroy,
                 bg='#e74c3c', fg='white', font=('Cambria', 11, 'bold'),
                 relief='flat', padx=15, pady=6).pack(side='right', padx=5)

    def refresh_table(self):
        """Обновить таблицу"""
        # Очистить таблицу
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Заполнить данными
        for db_config in self.db_manager.databases.get('databases', []):
            created_date = datetime.fromisoformat(db_config['created_date']).strftime("%d.%m.%Y")
            status = "🟢 Активен" if db_config.get('is_active', True) else "🔴 Неактивен"
            file_status = "✅" if os.path.exists(db_config['file_path']) else "❌"

            values = (
                db_config['id'],
                db_config['name'],
                db_config.get('description', '')[:50] + ('...' if len(db_config.get('description', '')) > 50 else ''),
                status,
                created_date,
                file_status
            )

            self.tree.insert('', 'end', values=values)

    def check_integrity(self):
        """Проверить целостность баз данных"""
        try:
            issues = []

            for db_config in self.db_manager.databases.get('databases', []):
                # Проверить существование файла
                if not os.path.exists(db_config['file_path']):
                    issues.append(f"❌ {db_config['name']}: файл не найден")
                    continue

                # Проверить возможность подключения к базе данных
                try:
                    conn = sqlite3.connect(db_config['file_path'])
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                    tables = cursor.fetchall()
                    conn.close()

                    if not tables:
                        issues.append(f"⚠️ {db_config['name']}: база данных пуста")
                    else:
                        issues.append(f"✅ {db_config['name']}: OK ({len(tables)} таблиц)")

                except Exception as e:
                    issues.append(f"❌ {db_config['name']}: ошибка подключения - {e}")

            # Показать результаты
            result_text = "\n".join(issues)
            messagebox.showinfo("Проверка Целостности", result_text)

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка проверки целостности: {e}")

    def cleanup_inactive(self):
        """Очистить неактивные базы данных"""
        inactive_dbs = [db for db in self.db_manager.databases.get('databases', [])
                       if not db.get('is_active', True)]

        if not inactive_dbs:
            messagebox.showinfo("Информация", "Нет неактивных баз данных для очистки")
            return

        if messagebox.askyesno("Подтверждение",
                              f"Удалить {len(inactive_dbs)} неактивных баз данных?\n"
                              "Это действие нельзя отменить!"):
            try:
                # Удалить из конфигурации
                self.db_manager.databases['databases'] = [
                    db for db in self.db_manager.databases['databases']
                    if db.get('is_active', True)
                ]

                # Сохранить конфигурацию
                self.db_manager.save_databases_config()

                # Обновить таблицу
                self.refresh_table()

                messagebox.showinfo("Успех", f"Удалено {len(inactive_dbs)} неактивных баз данных")

            except Exception as e:
                messagebox.showerror("Ошибка", f"Ошибка очистки: {e}")


class DatabaseClearDialog:
    """Диалог для очистки базы данных"""

    def __init__(self, parent, db_manager, available_databases):
        self.parent = parent
        self.db_manager = db_manager
        self.available_databases = available_databases
        self.selected_database = None
        self.create_dialog()

    def create_dialog(self):
        """Создать диалог очистки базы данных"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("🗑️ Очистка Базы Данных")
        self.window.geometry("700x650")  # Увеличили размер
        self.window.configure(bg='white')
        self.window.resizable(True, True)  # Разрешили изменение размера

        # Центрировать окно
        self.window.transient(self.parent)
        self.window.grab_set()

        # Центрировать на экране
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (700 // 2)
        y = (self.window.winfo_screenheight() // 2) - (650 // 2)
        self.window.geometry(f"700x650+{x}+{y}")

        # Заголовок
        header_frame = tk.Frame(self.window, bg='#e74c3c', height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="🗑️ Очистка Базы Данных",
                font=('Cambria', 18, 'bold italic'), bg='#e74c3c', fg='white').pack(pady=25)

        # Основной контент
        main_frame = tk.Frame(self.window, bg='white')
        main_frame.pack(fill='both', expand=True, padx=30, pady=30)

        # Предупреждение
        warning_frame = tk.Frame(main_frame, bg='#fff3cd', relief='solid', bd=2)
        warning_frame.pack(fill='x', pady=(0, 20))

        tk.Label(warning_frame, text="⚠️ ВНИМАНИЕ!",
                font=('Cambria', 16, 'bold'), bg='#fff3cd', fg='#856404').pack(pady=10)

        warning_text = ("Эта операция удалит ВСЕ данные из выбранной базы данных:\n"
                       "• Все записи о продажах\n"
                       "• Данные о складе и товарах\n"
                       "• Рецепты и меню\n"
                       "• Информацию о поставщиках\n"
                       "• Финансовые записи\n\n"
                       "Это действие НЕОБРАТИМО!")

        tk.Label(warning_frame, text=warning_text,
                font=('Cambria', 12), bg='#fff3cd', fg='#856404',
                justify='left').pack(padx=15, pady=(0, 15))

        # Выбор базы данных
        selection_frame = tk.Frame(main_frame, bg='white')
        selection_frame.pack(fill='x', pady=(0, 20))

        tk.Label(selection_frame, text="Выберите базу данных для очистки:",
                font=('Cambria', 14, 'bold'), bg='white').pack(anchor='w', pady=(0, 10))

        # Список баз данных
        self.db_listbox = tk.Listbox(selection_frame, font=('Cambria', 12), height=4)
        self.db_listbox.pack(fill='x', pady=(0, 10))

        for db in self.available_databases:
            display_text = f"{db['name']} ({db['description']})"
            self.db_listbox.insert(tk.END, display_text)

        # Поле подтверждения
        confirm_frame = tk.Frame(main_frame, bg='white')
        confirm_frame.pack(fill='x', pady=(0, 20))

        tk.Label(confirm_frame, text="Для подтверждения введите название базы данных:",
                font=('Cambria', 12, 'bold'), bg='white').pack(anchor='w', pady=(0, 5))

        self.confirm_entry = tk.Entry(confirm_frame, font=('Cambria', 12), width=40)
        self.confirm_entry.pack(anchor='w')

        # Кнопки
        buttons_frame = tk.Frame(main_frame, bg='white')
        buttons_frame.pack(fill='x', pady=(20, 0))

        # Кнопка отмены
        cancel_btn = tk.Button(buttons_frame, text="❌ Отмена", command=self.window.destroy,
                              bg='#6c757d', fg='white', font=('Cambria', 14, 'bold'),
                              relief='flat', padx=30, pady=12, cursor='hand2')
        cancel_btn.pack(side='right', padx=(10, 0))

        # Кнопка очистки
        clear_btn = tk.Button(buttons_frame, text="🗑️ ОЧИСТИТЬ БАЗУ ДАННЫХ", command=self.confirm_clear,
                             bg='#dc3545', fg='white', font=('Cambria', 14, 'bold'),
                             relief='flat', padx=30, pady=12, cursor='hand2')
        clear_btn.pack(side='right')

        # Добавить разделитель перед кнопками
        separator = tk.Frame(main_frame, height=2, bg='#dee2e6')
        separator.pack(fill='x', pady=(15, 0))

    def confirm_clear(self):
        """Подтвердить и выполнить очистку базы данных"""
        try:
            # Проверить выбор базы данных
            selection = self.db_listbox.curselection()
            if not selection:
                messagebox.showerror("Ошибка", "Пожалуйста, выберите базу данных для очистки")
                return

            selected_db = self.available_databases[selection[0]]

            # Проверить подтверждение
            entered_name = self.confirm_entry.get().strip()
            if entered_name != selected_db['name']:
                messagebox.showerror("Ошибка",
                    f"Введенное название '{entered_name}' не совпадает с выбранной базой данных '{selected_db['name']}'")
                return

            # Финальное подтверждение
            if not messagebox.askyesno("Финальное подтверждение",
                f"Вы ДЕЙСТВИТЕЛЬНО хотите удалить ВСЕ данные из базы данных '{selected_db['name']}'?\n\n"
                "Это действие НЕОБРАТИМО!\n\n"
                "Нажмите 'Да' только если вы полностью уверены."):
                return

            # Выполнить очистку
            self.clear_database_data(selected_db)

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка при очистке базы данных: {e}")

    def clear_database_data(self, db_config):
        """Очистить все данные из базы данных"""
        try:
            db_path = db_config['file_path']

            if not os.path.exists(db_path):
                messagebox.showerror("Ошибка", f"Файл базы данных не найден: {db_path}")
                return

            # Подключиться к базе данных и очистить все таблицы
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()

                # Получить список всех таблиц
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
                tables = cursor.fetchall()

                # Отключить проверку внешних ключей
                cursor.execute("PRAGMA foreign_keys = OFF")

                # Очистить каждую таблицу (кроме пользователей для сохранения доступа)
                cleared_tables = []
                for table in tables:
                    table_name = table[0]
                    if table_name != 'users':  # Сохранить таблицу пользователей
                        cursor.execute(f"DELETE FROM {table_name}")
                        cleared_tables.append(table_name)

                # Сбросить автоинкремент
                for table in cleared_tables:
                    cursor.execute(f"DELETE FROM sqlite_sequence WHERE name='{table}'")

                # Включить проверку внешних ключей
                cursor.execute("PRAGMA foreign_keys = ON")

                conn.commit()

            # Показать результат
            messagebox.showinfo("Успех",
                f"✅ База данных '{db_config['name']}' успешно очищена!\n\n"
                f"Очищено таблиц: {len(cleared_tables)}\n"
                f"Таблица пользователей сохранена для доступа к системе.\n\n"
                f"Очищенные таблицы:\n" + "\n".join(f"• {table}" for table in cleared_tables))

            # Закрыть диалог
            self.window.destroy()

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось очистить базу данных: {e}")


def create_database_selector():
    """Создать селектор базы данных"""
    db_manager = DatabaseManager()
    window = db_manager.create_database_selector_window()
    window.mainloop()
    return db_manager.get_selected_database()
