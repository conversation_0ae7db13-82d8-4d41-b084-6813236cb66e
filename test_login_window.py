"""
Тестовый скрипт для демонстрации изменений в окне входа
Показывает окно входа с автоматическим фокусом и цветом maroon
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager
from gui.login_window import LoginWindow

def test_login_window():
    """Тестирование окна входа с новыми настройками"""
    print("🧪 Тестирование окна входа в систему")
    print("=" * 50)
    print("Изменения:")
    print("✅ Автоматическая установка фокуса на поле имени пользователя")
    print("✅ Цвет шрифта изменен на maroon (темно-красный)")
    print("✅ Применено ко всем текстовым элементам окна входа")
    print("=" * 50)
    
    try:
        # Инициализация базы данных
        db_manager = DatabaseManager()
        print("✅ База данных инициализирована")
        
        # Создание окна входа
        print("🚀 Запуск окна входа...")
        print("Обратите внимание:")
        print("- Фокус автоматически установлен на поле 'Имя пользователя'")
        print("- Все тексты отображаются цветом maroon")
        print("- Можно использовать Tab для перехода между полями")
        print("- Enter в поле пароля запускает аутентификацию")
        
        # Показать окно входа
        login_window = LoginWindow(db_manager)
        authenticated_user = login_window.run()
        
        if authenticated_user:
            print(f"✅ Успешный вход: {authenticated_user['username']}")
            print(f"Роль: {authenticated_user['role']}")
            print(f"Полное имя: {authenticated_user['full_name']}")
        else:
            print("❌ Вход отменен или неуспешен")
            
    except Exception as e:
        print(f"❌ Ошибка тестирования: {e}")
        import traceback
        traceback.print_exc()

def test_simple_login_window():
    """Тестирование простого окна входа"""
    print("\n🧪 Тестирование простого окна входа")
    print("=" * 50)
    
    try:
        from gui.simple_login import SimpleLoginWindow
        
        # Инициализация базы данных
        db_manager = DatabaseManager()
        
        # Создание простого окна входа
        print("🚀 Запуск простого окна входа...")
        simple_login = SimpleLoginWindow(db_manager)
        authenticated_user = simple_login.run()
        
        if authenticated_user:
            print(f"✅ Успешный вход: {authenticated_user['username']}")
        else:
            print("❌ Вход отменен или неуспешен")
            
    except Exception as e:
        print(f"❌ Ошибка тестирования простого окна: {e}")

def main():
    """Главная функция тестирования"""
    print("🏪 Тестирование окон входа в систему управления рестораном")
    print("=" * 60)
    
    choice = input("Выберите тест:\n1 - Основное окно входа\n2 - Простое окно входа\n3 - Оба окна\nВаш выбор (1-3): ")
    
    if choice == "1":
        test_login_window()
    elif choice == "2":
        test_simple_login_window()
    elif choice == "3":
        test_login_window()
        test_simple_login_window()
    else:
        print("Неверный выбор. Запуск основного окна входа...")
        test_login_window()
    
    print("\n✅ Тестирование завершено!")
    print("Изменения успешно применены:")
    print("- Автоматический фокус на поле имени пользователя ✅")
    print("- Цвет шрифта maroon для всех текстовых элементов ✅")

if __name__ == "__main__":
    main()
