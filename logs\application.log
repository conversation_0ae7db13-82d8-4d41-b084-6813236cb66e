2025-06-08 01:00:39 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T01:00:39.136699", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 01:00:39 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 01:00:39 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 01:00:51 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 01:00:51 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 01:11:02 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T01:11:02.468927", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 01:11:02 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 01:11:02 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 01:11:19 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 01:11:19 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 01:29:55 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T01:29:55.217518", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 01:29:55 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 01:29:55 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 01:30:01 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 01:30:01 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 01:41:07 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T01:41:07.069701", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 01:41:07 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 01:41:07 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 01:41:13 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 01:41:13 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 01:56:17 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T01:56:17.350507", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 01:56:17 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 01:56:17 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 01:56:26 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 01:56:26 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 02:01:29 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T02:01:29.631547", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 02:01:29 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 02:01:29 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 02:03:30 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T02:03:30.625307", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 02:03:30 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 02:03:30 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 02:06:15 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T02:06:15.114673", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 02:06:15 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 02:06:15 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 02:16:17 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T02:16:17.106507", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 02:16:17 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 02:16:17 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 02:16:28 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 02:16:28 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 02:31:32 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T02:31:32.574267", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 02:31:32 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 02:31:32 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 02:31:47 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 02:31:47 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 02:35:33 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T02:35:33.895917", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 02:35:33 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 02:35:33 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 02:35:40 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 02:35:40 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 02:43:06 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T02:43:06.963694", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 02:43:06 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 02:43:06 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 02:43:21 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 02:43:22 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 02:57:29 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T02:57:29.000352", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 02:57:29 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 02:57:29 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 02:57:34 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 02:57:34 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 18:47:05 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T18:47:05.012464", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 18:47:05 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 18:47:05 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 18:47:12 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 18:47:12 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 18:51:16 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T18:51:16.560017", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 18:51:16 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 18:51:16 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 18:51:24 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 18:51:24 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 18:58:54 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T18:58:54.759827", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 18:58:54 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 18:58:54 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 18:59:02 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 18:59:02 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 19:08:01 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T19:08:01.021357", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 19:08:01 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 19:08:01 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 19:08:11 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 19:08:11 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 19:13:34 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T19:13:34.397778", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 19:13:34 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 19:13:34 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 19:13:41 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 19:13:41 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 19:21:25 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T19:21:25.574582", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 19:21:25 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 19:21:25 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 19:21:31 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 19:21:31 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 19:31:52 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T19:31:52.871487", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 19:31:52 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 19:31:52 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 19:32:01 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 19:32:01 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 20:21:28 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T20:21:28.706492", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 20:21:28 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 20:21:28 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 20:21:39 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 20:21:39 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 20:42:19 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T20:42:19.083841", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 20:42:19 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 20:42:19 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 20:47:11 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T20:47:11.963974", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 20:47:11 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 20:47:11 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 20:48:33 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T20:48:33.709329", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 20:48:33 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 20:48:33 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 20:52:26 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T20:52:26.994692", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 20:52:26 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 20:52:27 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 20:58:13 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T20:58:13.382161", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 20:58:13 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 20:58:13 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 20:58:19 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 20:58:19 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 21:15:01 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T21:15:01.746544", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 21:15:01 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 21:15:01 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 21:15:17 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 21:15:17 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 21:31:23 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T21:31:23.191883", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 21:31:23 | restaurant_app | INFO | log_app_event:119 | Database initialized successfully
2025-06-08 21:31:23 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 21:31:28 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 21:31:28 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 21:58:45 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T21:58:45.268526", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 21:58:45 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-08 21:58:45 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 21:59:16 | restaurant_app | INFO | log_app_event:119 | Authentication cancelled or failed
2025-06-08 22:12:59 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T22:12:59.142404", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 22:12:59 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-08 22:12:59 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 22:13:29 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 22:13:29 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 22:41:12 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T22:41:12.289912", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 22:41:12 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-08 22:41:12 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 22:41:55 | restaurant_app | INFO | log_app_event:119 | Authentication cancelled or failed
2025-06-08 22:52:11 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T22:52:11.232803", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 22:52:11 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-08 22:52:11 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 22:52:29 | restaurant_app | INFO | log_app_event:119 | Authentication cancelled or failed
2025-06-08 23:08:05 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T23:08:05.669588", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 23:08:05 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-08 23:08:05 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 23:08:28 | restaurant_app | INFO | log_app_event:119 | Authentication cancelled or failed
2025-06-08 23:10:00 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T23:10:00.871958", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 23:10:00 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-08 23:10:00 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 23:10:09 | restaurant_app | INFO | log_app_event:119 | Authentication cancelled or failed
2025-06-08 23:11:15 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T23:11:15.339891", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 23:11:15 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-08 23:11:15 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 23:11:18 | restaurant_app | INFO | log_app_event:119 | Authentication cancelled or failed
2025-06-08 23:16:23 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T23:16:23.717762", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 23:16:23 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-08 23:16:23 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 23:17:04 | restaurant_app | INFO | log_app_event:119 | Authentication cancelled or failed
2025-06-08 23:20:35 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T23:20:35.406929", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 23:20:35 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-08 23:20:35 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 23:21:23 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 23:21:23 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 23:22:11 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T23:22:11.582376", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 23:22:11 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-08 23:22:11 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 23:22:37 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 23:22:37 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 23:23:52 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T23:23:52.741200", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 23:23:52 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-08 23:23:52 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 23:24:29 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 23:24:29 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 23:30:49 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T23:30:49.759602", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 23:30:49 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-08 23:30:49 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 23:31:36 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 23:31:36 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 23:36:50 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T23:36:50.191952", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 23:36:50 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-08 23:36:50 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 23:37:35 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 23:37:35 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-08 23:44:55 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-08T23:44:55.599370", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-08 23:44:55 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-08 23:44:55 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-08 23:45:56 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-08 23:45:56 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-09 00:01:04 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-09T00:01:04.201097", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-09 00:01:04 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-09 00:01:04 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-09 00:01:28 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-09 00:01:28 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-09 00:18:19 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-09T00:18:19.475408", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-09 00:18:19 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-09 00:18:19 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-09 00:18:33 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-09 00:18:33 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-09 00:22:25 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-09T00:22:25.351305", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-09 00:22:25 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-09 00:22:25 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-09 00:22:45 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-09 00:22:45 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-09 17:53:35 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-09T17:53:35.717366", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-09 17:53:35 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-09 17:53:35 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-09 17:53:55 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-09 17:53:55 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-09 18:14:02 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-09T18:14:02.471737", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-09 18:14:02 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-09 18:14:02 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-09 18:14:21 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-09 18:14:21 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-09 18:25:44 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-09T18:25:44.540395", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-09 18:25:44 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-09 18:25:44 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-09 18:26:03 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-09 18:26:03 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-09 18:31:09 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-09T18:31:09.668661", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-09 18:31:09 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-09 18:31:09 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-09 18:31:36 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-09 18:31:36 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-09 19:32:04 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-09T19:32:04.627925", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-09 19:32:04 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-09 19:32:04 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-09 19:32:19 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-09 19:32:19 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-09 19:46:04 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-09T19:46:04.263220", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-09 19:46:04 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-09 19:46:04 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-09 19:47:09 | restaurant_app | INFO | log_app_event:119 | Authentication cancelled or failed
2025-06-09 19:50:48 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-09T19:50:48.491094", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-09 19:50:48 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-09 19:50:48 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-09 19:51:11 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-09T19:51:11.212609", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-09 19:51:11 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-09 19:51:11 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-09 19:51:38 | restaurant_app | INFO | log_app_event:119 | Authentication cancelled or failed
2025-06-09 21:41:33 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-09T21:41:33.463187", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-09 21:41:33 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-09 21:41:33 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-09 21:41:53 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-09 21:41:53 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-09 21:49:34 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-09T21:49:34.780802", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-09 21:49:34 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-09 21:49:34 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-09 21:49:59 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-09 21:49:59 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-09 22:00:06 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-09T22:00:06.883393", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-09 22:00:06 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-09 22:00:06 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-09 22:00:40 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-09 22:00:40 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 09:17:12 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T09:17:12.505283", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 09:17:12 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 09:17:12 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 09:18:59 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T09:18:59.332337", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 09:18:59 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 09:18:59 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 09:19:10 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 09:19:10 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 09:25:52 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T09:25:52.713762", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 09:25:52 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 09:25:52 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 09:26:04 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 09:26:04 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 09:50:03 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T09:50:03.697570", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 09:50:03 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 09:50:03 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 09:50:23 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 09:50:23 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 09:56:47 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T09:56:47.054139", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 09:56:47 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 09:56:47 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 09:57:01 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 09:57:01 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 10:14:07 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T10:14:07.307844", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 10:14:07 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 10:14:07 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 10:14:19 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 10:14:19 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 10:27:15 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T10:27:15.748882", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 10:27:15 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 10:27:15 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 10:27:36 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 10:27:36 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 10:35:21 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T10:35:21.051088", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 10:35:21 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 10:35:21 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 10:35:30 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 10:35:30 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 10:40:03 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T10:40:03.971645", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 10:40:03 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 10:40:03 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 10:40:16 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 10:40:16 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 10:48:41 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T10:48:41.532620", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 10:48:41 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 10:48:41 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 10:48:59 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 10:48:59 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 11:05:20 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T11:05:20.504687", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 11:05:20 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 11:05:20 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 11:05:32 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 11:05:32 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 11:10:37 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T11:10:37.265452", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 11:10:37 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 11:10:37 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 11:10:54 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 11:10:54 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 11:17:34 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T11:17:34.220530", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 11:17:34 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 11:17:34 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 11:17:52 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 11:17:52 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 11:19:03 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T11:19:03.663567", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 11:19:03 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 11:19:03 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 11:19:16 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 11:19:16 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 11:23:31 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T11:23:31.522827", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 11:23:31 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 11:23:31 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 11:23:47 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 11:23:47 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 11:35:57 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T11:35:57.230046", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 11:35:57 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 11:35:57 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 11:36:13 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 11:36:13 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 11:39:56 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T11:39:56.273030", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 11:39:56 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 11:39:56 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 11:40:12 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 11:40:12 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 11:48:40 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T11:48:40.822337", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 11:48:40 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 11:48:40 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 11:49:00 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 11:49:00 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 11:56:25 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T11:56:25.492466", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 11:56:25 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 11:56:25 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 11:56:39 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 11:56:39 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 12:11:05 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T12:11:05.283289", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 12:11:05 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 12:11:05 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 12:11:19 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 12:11:19 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 12:27:32 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T12:27:32.376994", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 12:27:32 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 12:27:32 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 12:27:47 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 12:27:47 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 12:44:08 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T12:44:08.606370", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 12:44:08 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 12:44:08 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 12:44:24 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 12:44:24 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 13:06:58 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T13:06:58.313143", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 13:06:58 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 13:06:58 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 13:07:20 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 13:07:20 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 13:23:00 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T13:23:00.212443", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 13:23:00 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 13:23:00 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 13:23:24 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 13:23:24 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 13:40:13 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T13:40:13.187446", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 13:40:13 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 13:40:13 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 13:40:29 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 13:40:29 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 13:43:17 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T13:43:17.957270", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 13:43:17 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 13:43:17 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 13:43:29 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 13:43:29 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 14:06:04 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T14:06:04.645418", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 14:06:04 | restaurant_app | INFO | log_app_event:119 | Database initialized: Основной Ресторан
2025-06-15 14:06:04 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 14:06:32 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 14:06:32 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 14:26:40 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T14:26:40.562435", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 14:26:40 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос Гриль"
2025-06-15 14:26:40 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 14:26:58 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 14:26:58 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 14:36:38 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T14:36:38.912513", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 14:36:38 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос Гриль"
2025-06-15 14:36:38 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 14:36:57 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 14:36:57 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 14:40:14 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T14:40:14.875730", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 14:40:14 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 14:40:14 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 14:40:31 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 14:40:31 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 14:48:08 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T14:48:08.556695", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 14:48:08 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 14:48:08 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 14:48:25 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 14:48:25 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 14:55:26 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T14:55:26.098013", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 14:55:26 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 14:55:26 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 14:55:44 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 14:55:44 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 15:11:54 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T15:11:54.647909", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 15:11:54 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 15:11:54 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 15:12:16 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 15:12:16 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 15:26:00 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T15:26:00.202356", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 15:26:00 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 15:26:00 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 15:26:21 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 15:26:21 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 15:33:39 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T15:33:39.619747", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 15:33:39 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 15:33:39 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 15:33:54 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 15:33:54 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 15:34:19 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T15:34:19.160598", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 15:34:19 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос Гриль"
2025-06-15 15:34:19 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 15:34:49 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 15:34:49 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 15:41:11 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T15:41:11.817449", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 15:41:11 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Чайхана Дамаск"
2025-06-15 15:41:11 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 15:41:38 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 15:41:38 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 15:42:06 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T15:42:06.503928", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 15:42:06 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Чайхана Дамаск"
2025-06-15 15:42:06 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 15:42:18 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 15:42:18 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 15:52:54 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T15:52:54.356661", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 15:52:54 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Чайхана Дамаск 2"
2025-06-15 15:52:54 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 15:53:13 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 15:53:13 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 16:03:06 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T16:03:06.704891", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 16:03:06 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Чайхана Дамаск 1"
2025-06-15 16:03:06 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 16:03:20 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 16:03:20 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 16:06:54 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T16:06:54.925327", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 16:06:54 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Чайхана Дамаск 1"
2025-06-15 16:06:54 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 16:07:09 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 16:07:09 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 16:13:24 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T16:13:24.761474", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 16:13:24 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 16:13:24 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 16:13:43 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 16:13:44 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 16:22:43 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T16:22:43.875423", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 16:22:43 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 16:22:43 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 16:22:56 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 16:22:56 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 16:38:42 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T16:38:42.530191", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 16:38:42 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 16:38:42 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 16:38:55 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 16:38:55 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 16:49:36 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T16:49:36.661362", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 16:49:36 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 16:49:36 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 16:49:48 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 16:49:48 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 16:59:03 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T16:59:03.180849", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 16:59:03 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 16:59:03 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 16:59:20 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 16:59:20 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 17:06:57 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T17:06:57.157797", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 17:06:57 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 17:06:57 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 17:07:08 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 17:07:08 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 17:08:06 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T17:08:06.283392", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 17:08:06 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 17:08:06 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 17:08:15 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 17:08:15 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 17:08:41 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T17:08:41.345269", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 17:08:41 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 17:08:41 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 17:08:58 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 17:08:58 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 17:12:20 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T17:12:20.352982", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 17:12:20 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 17:12:20 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 17:12:33 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 17:12:33 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 17:13:20 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T17:13:20.550112", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 17:13:20 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 17:13:20 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 17:13:30 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 17:13:30 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 17:16:56 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T17:16:56.531321", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 17:16:56 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 17:16:56 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 17:17:09 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 17:17:09 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 17:20:58 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T17:20:58.713168", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 17:20:58 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 17:20:58 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 17:21:09 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 17:21:10 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 17:27:10 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T17:27:10.652948", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 17:27:10 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 17:27:10 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 17:27:23 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 17:27:23 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 17:35:59 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T17:35:59.904046", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 17:35:59 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 17:35:59 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 17:36:13 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 17:36:13 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 17:45:45 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T17:45:45.314967", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 17:45:45 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 17:45:45 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 17:45:59 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 17:45:59 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 17:56:48 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T17:56:48.183602", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 17:56:48 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 17:56:48 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 17:57:00 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 17:57:00 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 18:01:42 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T18:01:42.680099", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 18:01:42 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 18:01:42 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 18:01:54 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 18:01:54 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 18:09:19 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T18:09:19.112982", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 18:09:19 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 18:09:19 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 18:09:29 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 18:09:29 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 18:12:34 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T18:12:34.184329", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 18:12:34 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 18:12:34 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 18:12:43 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 18:12:43 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 18:17:07 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T18:17:07.613247", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 18:17:07 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 18:17:07 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 18:17:17 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 18:17:17 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 18:27:36 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T18:27:36.003843", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 18:27:36 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 18:27:36 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 18:27:46 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 18:27:46 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 18:35:14 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T18:35:14.506587", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 18:35:14 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 18:35:14 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 18:35:23 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 18:35:23 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 18:36:53 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T18:36:53.369664", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 18:36:53 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 18:36:53 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 18:37:10 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 18:37:10 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 18:37:52 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T18:37:52.723812", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 18:37:52 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 18:37:52 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 18:38:03 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 18:38:03 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 18:47:54 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T18:47:54.098050", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 18:47:54 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 18:47:54 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 18:48:04 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 18:48:04 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 18:53:44 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T18:53:44.721854", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 18:53:44 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 18:53:44 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 18:53:57 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 18:53:57 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 19:01:03 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T19:01:03.413020", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 19:01:03 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 19:01:03 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 19:01:19 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 19:01:19 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 19:07:38 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T19:07:38.474250", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 19:07:38 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 19:07:38 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 19:07:49 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 19:07:49 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 19:12:13 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T19:12:13.600592", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 19:12:13 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 19:12:13 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 19:12:28 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 19:12:28 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 19:19:05 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T19:19:05.015897", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 19:19:05 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 19:19:05 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 19:19:15 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 19:19:15 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 19:21:30 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T19:21:30.755793", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 19:21:30 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 19:21:30 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 19:21:43 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 19:21:43 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 19:24:36 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T19:24:36.265369", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 19:24:36 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 19:24:36 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 19:24:48 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 19:24:48 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 19:27:51 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T19:27:51.329781", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 19:27:51 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 19:27:51 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 19:28:02 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 19:28:02 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 19:32:08 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T19:32:08.363252", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 19:32:08 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 19:32:08 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 19:32:19 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 19:32:19 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 19:34:05 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T19:34:05.664606", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 19:34:05 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 19:34:05 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 19:34:15 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 19:34:15 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 19:39:20 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T19:39:20.784848", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 19:39:20 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 19:39:20 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 19:39:29 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 19:39:30 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 19:46:33 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T19:46:33.795637", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 19:46:33 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 19:46:33 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 19:46:44 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 19:46:44 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 19:48:11 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T19:48:11.101697", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 19:48:11 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 19:48:11 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 19:48:23 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 19:48:23 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 19:51:16 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T19:51:16.983095", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 19:51:16 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 19:51:16 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 19:51:28 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 19:51:28 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 19:53:37 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T19:53:37.082393", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 19:53:37 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 19:53:37 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 19:53:47 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 19:53:47 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 19:59:05 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T19:59:05.280855", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 19:59:05 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 19:59:05 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 19:59:27 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 19:59:27 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 20:02:07 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T20:02:07.519214", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 20:02:07 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 20:02:07 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 20:02:19 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 20:02:19 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 20:08:24 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T20:08:24.771366", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 20:08:24 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 20:08:24 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 20:08:37 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 20:08:37 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 20:15:37 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T20:15:37.804703", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 20:15:37 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 20:15:37 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 20:15:53 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 20:15:53 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 20:24:02 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T20:24:02.627561", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 20:24:02 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 20:24:02 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 20:24:13 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 20:24:13 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 20:29:21 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T20:29:21.255961", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 20:29:21 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 20:29:21 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 20:30:10 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 20:30:10 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 20:30:57 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T20:30:57.254981", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 20:30:57 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 20:30:57 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 20:31:08 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 20:31:08 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 20:34:00 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T20:34:00.556180", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 20:34:00 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 20:34:00 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 20:34:29 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 20:34:29 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 20:36:27 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T20:36:27.618104", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 20:36:27 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 20:36:27 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 20:36:39 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 20:36:39 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 21:17:04 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T21:17:04.368913", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 21:17:04 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 21:17:04 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 21:17:36 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 21:17:36 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 21:24:18 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T21:24:18.992541", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 21:24:19 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 21:24:19 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 21:24:34 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 21:24:34 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 21:31:06 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T21:31:06.317276", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 21:31:06 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 21:31:06 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 21:31:22 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 21:31:22 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 21:40:11 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T21:40:11.185744", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 21:40:11 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 21:40:11 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 21:40:23 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 21:40:23 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 22:33:40 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T22:33:40.860212", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 22:33:40 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 22:33:40 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 22:33:59 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 22:33:59 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 22:41:35 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T22:41:35.800253", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 22:41:35 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 22:41:35 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 22:41:55 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 22:41:55 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 22:50:39 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T22:50:39.942979", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 22:50:39 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 22:50:39 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 22:50:54 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 22:50:54 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 22:56:38 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T22:56:38.978452", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 22:56:38 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 22:56:38 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 22:56:54 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 22:56:54 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 23:01:21 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T23:01:21.581295", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 23:01:21 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 23:01:21 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 23:01:33 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 23:01:33 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 23:07:02 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T23:07:02.133087", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 23:07:02 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 23:07:02 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 23:07:14 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 23:07:15 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 23:12:42 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T23:12:42.468823", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 23:12:42 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 23:12:42 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 23:12:54 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 23:12:54 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 23:18:56 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T23:18:56.166134", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 23:18:56 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 23:18:56 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 23:19:16 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 23:19:16 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 23:26:58 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T23:26:58.137205", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 23:26:58 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 23:26:58 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 23:27:14 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 23:27:14 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 23:42:18 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T23:42:18.302420", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 23:42:18 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 23:42:18 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 23:42:33 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 23:42:33 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-15 23:51:13 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-15T23:51:13.743594", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-15 23:51:13 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-15 23:51:13 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-15 23:51:29 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-15 23:51:29 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-16 00:05:26 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-16T00:05:26.298189", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-16 00:05:26 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-16 00:05:26 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-16 00:05:40 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-16 00:05:40 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-06-16 00:09:59 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-06-16T00:09:59.911481", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-06-16 00:09:59 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-06-16 00:09:59 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-06-16 00:10:39 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-06-16 00:10:39 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-07-05 19:44:51 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-07-05T19:44:51.266714", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-07-05 19:44:51 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-07-05 19:44:51 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-07-05 19:45:04 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-07-05 19:45:04 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-07-05 23:34:30 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-07-05T23:34:30.106274", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-07-05 23:34:30 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-07-05 23:34:30 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-07-05 23:34:42 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-07-05 23:34:42 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-07-06 06:05:56 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-07-06T06:05:56.493644", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-07-06 06:05:56 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-07-06 06:05:56 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-07-06 06:06:21 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-07-06 06:06:21 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-07-06 06:10:08 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-07-06T06:10:08.269090", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-07-06 06:10:08 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-07-06 06:10:08 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-07-06 06:10:21 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-07-06 06:10:21 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-07-06 06:11:06 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-07-06T06:11:06.400415", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-07-06 06:11:06 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-07-06 06:11:06 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-07-06 06:11:18 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-07-06 06:11:18 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-07-06 06:11:57 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-07-06T06:11:57.316680", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-07-06 06:11:57 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-07-06 06:11:57 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-07-06 06:12:07 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-07-06 06:12:07 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-07-06 06:13:06 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-07-06T06:13:06.054413", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-07-06 06:13:06 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-07-06 06:13:06 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-07-06 06:13:15 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-07-06 06:13:15 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-07-06 06:15:38 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-07-06T06:15:38.263561", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-07-06 06:15:38 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-07-06 06:15:38 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-07-06 06:15:49 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-07-06 06:15:49 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-07-06 06:17:38 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-07-06T06:17:38.022770", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-07-06 06:17:38 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-07-06 06:17:38 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-07-06 06:17:49 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-07-06 06:17:49 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-07-06 06:19:51 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-07-06T06:19:51.539464", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-07-06 06:19:51 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-07-06 06:19:51 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-07-06 06:20:02 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-07-06 06:20:02 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-07-06 06:20:55 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-07-06T06:20:55.690711", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-07-06 06:20:55 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-07-06 06:20:55 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-07-06 06:21:06 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-07-06 06:21:06 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-07-06 06:21:45 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-07-06T06:21:45.989316", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-07-06 06:21:45 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-07-06 06:21:45 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-07-06 06:22:01 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-07-06 06:22:01 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-07-06 07:11:56 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-07-06T07:11:56.551018", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-07-06 07:11:56 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-07-06 07:11:56 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-07-06 07:12:06 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-07-06 07:12:06 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-07-06 11:45:41 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-07-06T11:45:41.198786", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-07-06 11:45:41 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-07-06 11:45:41 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-07-06 11:45:57 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-07-06 11:45:57 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-07-06 12:04:09 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-07-06T12:04:09.382998", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-07-06 12:04:09 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-07-06 12:04:09 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-07-06 12:04:19 | restaurant_app | INFO | log_app_event:119 | Authentication cancelled or failed
2025-07-06 12:12:47 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-07-06T12:12:47.303196", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-07-06 12:12:47 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-07-06 12:12:47 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-07-06 12:13:08 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-07-06 12:13:08 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-07-06 12:30:09 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-07-06T12:30:09.929903", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-07-06 12:30:09 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-07-06 12:30:09 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-07-06 12:58:43 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-07-06T12:58:43.581125", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-07-06 12:58:43 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-07-06 12:58:43 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-07-06 12:58:53 | restaurant_app | INFO | log_app_event:119 | Authentication cancelled or failed
2025-07-06 12:59:14 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-07-06T12:59:14.181972", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-07-06 12:59:14 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-07-06 12:59:14 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-07-06 12:59:28 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-07-06 12:59:28 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-07-06 14:13:15 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-07-06T14:13:15.854799", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-07-06 14:13:15 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-07-06 14:13:15 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-07-06 14:13:46 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-07-06 14:13:46 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
2025-07-06 14:22:27 | restaurant_app | INFO | log_system_startup:193 | SYSTEM STARTUP | {"version": "1.0.0", "startup_time": "2025-07-06T14:22:27.363113", "user_count": 0, "python_version": "3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]", "platform": "win32"}
2025-07-06 14:22:27 | restaurant_app | INFO | log_app_event:119 | Database initialized: Кафе "Библос"
2025-07-06 14:22:27 | restaurant_app | INFO | log_app_event:119 | Backup system initialized
2025-07-06 14:22:46 | restaurant_app | INFO | log_app_event:119 | Notification system initialized
2025-07-06 14:22:46 | restaurant_app | INFO | log_app_event:119 | Application initialized successfully | {"user_id": 1}
