#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import ctypes
import time

def get_current_keyboard_layout():
    """Получить текущую раскладку клавиатуры"""
    try:
        user32 = ctypes.windll.user32
        hwnd = user32.GetForegroundWindow()
        thread_id = user32.GetWindowThreadProcessId(hwnd, None)
        hkl = user32.GetKeyboardLayout(thread_id)
        
        lang_id = hkl & 0xFFFF
        if lang_id == 0x0409:
            return "Английская 🇺🇸"
        elif lang_id == 0x0419:
            return "Русская 🇷🇺"
        else:
            return f"Неизвестная (ID: {hex(lang_id)})"
    except:
        return "Ошибка определения"

def switch_to_russian_keyboard():
    """Переключить раскладку на русскую (как в login_window.py)"""
    try:
        user32 = ctypes.windll.user32
        
        # Метод 1: LoadKeyboardLayout и ActivateKeyboardLayout
        try:
            hkl = user32.LoadKeyboardLayoutW("00000419", 0x00000001)
            if hkl:
                result = user32.ActivateKeyboardLayout(hkl, 0)
                if result:
                    print("✅ Раскладка переключена на русский (LoadKeyboardLayout)")
                    return True
        except Exception as e:
            print(f"Ошибка в методе 1: {e}")
        
        # Метод 2: Alt+Shift
        try:
            user32.keybd_event(0x12, 0, 0, 0)  # Alt down
            user32.keybd_event(0x10, 0, 0, 0)  # Shift down
            user32.keybd_event(0x10, 0, 2, 0)  # Shift up
            user32.keybd_event(0x12, 0, 2, 0)  # Alt up
            print("✅ Раскладка переключена на русский (Alt+Shift)")
            return True
        except Exception as e:
            print(f"Ошибка в методе 2: {e}")
        
        # Метод 3: PostMessage
        try:
            HWND_BROADCAST = 0xFFFF
            WM_INPUTLANGCHANGEREQUEST = 0x0050
            result = user32.PostMessageW(HWND_BROADCAST, WM_INPUTLANGCHANGEREQUEST, 0, 0x04190419)
            if result:
                print("✅ Раскладка переключена на русский (PostMessage)")
                return True
        except Exception as e:
            print(f"Ошибка в методе 3: {e}")
        
        print("❌ Не удалось переключить раскладку")
        return False
    except Exception as e:
        print(f"❌ Общая ошибка: {e}")
        return False

def main():
    print("🔧 ФИНАЛЬНОЕ ТЕСТИРОВАНИЕ ПЕРЕКЛЮЧЕНИЯ РАСКЛАДКИ КЛАВИАТУРЫ")
    print("=" * 80)
    print("Это тестирование имитирует функциональность из login_window.py")
    print("=" * 80)
    
    # Показать текущую раскладку
    print(f"\n📋 Текущая раскладка: {get_current_keyboard_layout()}")
    
    print("\n🔄 Попытка переключения на русскую раскладку...")
    print("-" * 50)
    
    # Попробуем переключить несколько раз (как в коде)
    for attempt in range(1, 5):
        print(f"\n🔄 Попытка #{attempt}:")
        success = switch_to_russian_keyboard()
        
        # Проверим результат
        time.sleep(1)
        current_layout = get_current_keyboard_layout()
        print(f"   📋 Текущая раскладка: {current_layout}")
        
        if "Русская" in current_layout:
            print(f"   🎉 УСПЕХ! Раскладка переключена на русскую с попытки #{attempt}")
            break
        else:
            print(f"   ⚠️ Раскладка все еще не русская, продолжаем...")
        
        time.sleep(2)
    
    print("\n" + "=" * 80)
    print("🏁 ТЕСТИРОВАНИЕ ЗАВЕРШЕНО")
    print("=" * 80)
    
    final_layout = get_current_keyboard_layout()
    print(f"📋 Финальная раскладка: {final_layout}")
    
    if "Русская" in final_layout:
        print("🎉 РЕЗУЛЬТАТ: УСПЕШНО! Раскладка переключена на русскую")
        print("✅ Функциональность в login_window.py должна работать корректно")
    else:
        print("❌ РЕЗУЛЬТАТ: НЕ УДАЛОСЬ переключить на русскую раскладку")
        print("⚠️ Возможные причины:")
        print("   - Русская раскладка не установлена в системе")
        print("   - Требуются права администратора")
        print("   - Системные ограничения Windows")
        print("   - Антивирус блокирует изменение раскладки")
    
    print("\n💡 ИНСТРУКЦИИ ДЛЯ ПРОВЕРКИ В ПРИЛОЖЕНИИ:")
    print("1. Запустите main.py")
    print("2. В окне входа введите логин и пароль")
    print("3. Нажмите OK")
    print("4. Сразу после входа попробуйте печатать - должна быть русская раскладка")
    print("5. Если раскладка не переключилась, проверьте консоль на сообщения об ошибках")

if __name__ == "__main__":
    main()
    input("\nНажмите Enter для выхода...")
