"""
Performance Dashboard for Restaurant Management System
Provides real-time performance monitoring, cache statistics, and optimization recommendations
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from datetime import datetime
from gui.styles import ModernStyles
from utils.window_utils import create_standard_window, apply_standard_styling
from utils.error_handling import handle_module_error, log_info
from utils.performance_optimizer import get_performance_optimizer, memory_cleanup


class PerformanceDashboard:
    """Performance monitoring and optimization dashboard"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.performance_optimizer = get_performance_optimizer(db_manager)
        self.update_thread = None
        self.running = False
        
        # Performance data
        self.cache_stats = {}
        self.performance_report = {}
        self.optimization_suggestions = []
        
    def show_dashboard(self):
        """Show performance dashboard"""
        try:
            if self.window and self.window.winfo_exists():
                self.window.lift()
                self.window.focus_set()
                return
            
            log_info("Открытие панели производительности", "PerformanceDashboard")
            
            self.create_window()
            self.create_interface()
            self.start_monitoring()
            
        except Exception as e:
            handle_module_error(e, "Панель производительности", "открытие панели")
    
    def create_window(self):
        """Create dashboard window"""
        try:
            self.window = create_standard_window(
                parent=self.parent,
                title="Мониторинг Производительности",
                width=1200,
                height=800,
                resizable=True,
                maximized=False,
                modal=False,
                icon_emoji="📊"
            )
            
            # Handle window close
            self.window.protocol("WM_DELETE_WINDOW", self.close_dashboard)
            
        except Exception as e:
            handle_module_error(e, "Панель производительности", "создание окна")
            raise
    
    def create_interface(self):
        """Create dashboard interface"""
        try:
            # Apply standard styling
            main_container = apply_standard_styling(
                self.window, 
                "Мониторинг Производительности Системы", 
                "📊"
            )
            
            # Create notebook for different views
            self.notebook = ttk.Notebook(main_container)
            self.notebook.pack(fill='both', expand=True, pady=(0, 20))
            
            # Create tabs
            self.create_overview_tab()
            self.create_cache_tab()
            self.create_queries_tab()
            self.create_optimization_tab()
            
            # Create control buttons
            self.create_control_buttons(main_container)
            
        except Exception as e:
            handle_module_error(e, "Панель производительности", "создание интерфейса")
    
    def create_overview_tab(self):
        """Create system overview tab"""
        try:
            tab_frame = ttk.Frame(self.notebook)
            self.notebook.add(tab_frame, text="📈 Обзор Системы")
            
            # Create scrollable frame
            canvas = tk.Canvas(tab_frame, bg=ModernStyles.COLORS['bg_main'])
            scrollbar = ttk.Scrollbar(tab_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg=ModernStyles.COLORS['bg_main'])
            
            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )
            
            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)
            
            # System metrics frame
            metrics_frame = tk.LabelFrame(scrollable_frame, text="📊 Системные Метрики",
                                        font=('Cambria', 14, 'bold'),
                                        fg=ModernStyles.COLORS['text_primary'],
                                        bg=ModernStyles.COLORS['bg_secondary'])
            metrics_frame.pack(fill='x', padx=20, pady=10)
            
            # Create metric labels
            self.uptime_label = tk.Label(metrics_frame, text="Время работы: --",
                                       font=('Cambria', 12),
                                       fg=ModernStyles.COLORS['text_primary'],
                                       bg=ModernStyles.COLORS['bg_secondary'])
            self.uptime_label.pack(anchor='w', padx=10, pady=5)
            
            self.memory_label = tk.Label(metrics_frame, text="Использование памяти: --",
                                       font=('Cambria', 12),
                                       fg=ModernStyles.COLORS['text_primary'],
                                       bg=ModernStyles.COLORS['bg_secondary'])
            self.memory_label.pack(anchor='w', padx=10, pady=5)
            
            self.queries_label = tk.Label(metrics_frame, text="Всего запросов: --",
                                        font=('Cambria', 12),
                                        fg=ModernStyles.COLORS['text_primary'],
                                        bg=ModernStyles.COLORS['bg_secondary'])
            self.queries_label.pack(anchor='w', padx=10, pady=5)
            
            # Cache metrics frame
            cache_frame = tk.LabelFrame(scrollable_frame, text="💾 Метрики Кэша",
                                      font=('Cambria', 14, 'bold'),
                                      fg=ModernStyles.COLORS['text_primary'],
                                      bg=ModernStyles.COLORS['bg_secondary'])
            cache_frame.pack(fill='x', padx=20, pady=10)
            
            self.cache_size_label = tk.Label(cache_frame, text="Размер кэша: --",
                                           font=('Cambria', 12),
                                           fg=ModernStyles.COLORS['text_primary'],
                                           bg=ModernStyles.COLORS['bg_secondary'])
            self.cache_size_label.pack(anchor='w', padx=10, pady=5)
            
            self.cache_hit_rate_label = tk.Label(cache_frame, text="Эффективность кэша: --",
                                               font=('Cambria', 12),
                                               fg=ModernStyles.COLORS['text_primary'],
                                               bg=ModernStyles.COLORS['bg_secondary'])
            self.cache_hit_rate_label.pack(anchor='w', padx=10, pady=5)
            
            self.cache_memory_label = tk.Label(cache_frame, text="Память кэша: --",
                                             font=('Cambria', 12),
                                             fg=ModernStyles.COLORS['text_primary'],
                                             bg=ModernStyles.COLORS['bg_secondary'])
            self.cache_memory_label.pack(anchor='w', padx=10, pady=5)
            
            # Pack canvas and scrollbar
            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")
            
        except Exception as e:
            handle_module_error(e, "Панель производительности", "создание вкладки обзора")
    
    def create_cache_tab(self):
        """Create cache statistics tab"""
        try:
            tab_frame = ttk.Frame(self.notebook)
            self.notebook.add(tab_frame, text="💾 Кэш")
            
            # Control frame
            control_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
            control_frame.pack(fill='x', padx=20, pady=10)
            
            tk.Button(control_frame, text="🔄 Обновить",
                     command=self.refresh_cache_stats,
                     bg=ModernStyles.COLORS['info'], fg='white',
                     font=('Cambria', 12, 'bold'), relief='flat',
                     padx=20, pady=8).pack(side='left', padx=5)
            
            tk.Button(control_frame, text="🗑️ Очистить Кэш",
                     command=self.clear_cache,
                     bg=ModernStyles.COLORS['warning'], fg='white',
                     font=('Cambria', 12, 'bold'), relief='flat',
                     padx=20, pady=8).pack(side='left', padx=5)
            
            tk.Button(control_frame, text="🧹 Очистить Память",
                     command=self.cleanup_memory,
                     bg=ModernStyles.COLORS['secondary'], fg='white',
                     font=('Cambria', 12, 'bold'), relief='flat',
                     padx=20, pady=8).pack(side='left', padx=5)
            
            # Cache statistics text widget
            self.cache_text = tk.Text(tab_frame, font=('Consolas', 11),
                                    bg=ModernStyles.COLORS['bg_main'],
                                    fg=ModernStyles.COLORS['text_primary'],
                                    wrap='word', height=25)
            
            cache_scrollbar = ttk.Scrollbar(tab_frame, orient='vertical', command=self.cache_text.yview)
            self.cache_text.configure(yscrollcommand=cache_scrollbar.set)
            
            self.cache_text.pack(side='left', fill='both', expand=True, padx=(20, 0), pady=10)
            cache_scrollbar.pack(side='right', fill='y', padx=(0, 20), pady=10)
            
        except Exception as e:
            handle_module_error(e, "Панель производительности", "создание вкладки кэша")
    
    def create_queries_tab(self):
        """Create query performance tab"""
        try:
            tab_frame = ttk.Frame(self.notebook)
            self.notebook.add(tab_frame, text="🔍 Запросы")
            
            # Query performance text widget
            self.queries_text = tk.Text(tab_frame, font=('Consolas', 11),
                                      bg=ModernStyles.COLORS['bg_main'],
                                      fg=ModernStyles.COLORS['text_primary'],
                                      wrap='word', height=30)
            
            queries_scrollbar = ttk.Scrollbar(tab_frame, orient='vertical', command=self.queries_text.yview)
            self.queries_text.configure(yscrollcommand=queries_scrollbar.set)
            
            self.queries_text.pack(side='left', fill='both', expand=True, padx=(20, 0), pady=20)
            queries_scrollbar.pack(side='right', fill='y', padx=(0, 20), pady=20)
            
        except Exception as e:
            handle_module_error(e, "Панель производительности", "создание вкладки запросов")
    
    def create_optimization_tab(self):
        """Create optimization recommendations tab"""
        try:
            tab_frame = ttk.Frame(self.notebook)
            self.notebook.add(tab_frame, text="⚡ Оптимизация")
            
            # Recommendations text widget
            self.optimization_text = tk.Text(tab_frame, font=('Cambria', 12),
                                           bg=ModernStyles.COLORS['bg_main'],
                                           fg=ModernStyles.COLORS['text_primary'],
                                           wrap='word', height=30)
            
            opt_scrollbar = ttk.Scrollbar(tab_frame, orient='vertical', command=self.optimization_text.yview)
            self.optimization_text.configure(yscrollcommand=opt_scrollbar.set)
            
            self.optimization_text.pack(side='left', fill='both', expand=True, padx=(20, 0), pady=20)
            opt_scrollbar.pack(side='right', fill='y', padx=(0, 20), pady=20)
            
        except Exception as e:
            handle_module_error(e, "Панель производительности", "создание вкладки оптимизации")
    
    def create_control_buttons(self, parent):
        """Create control buttons"""
        try:
            button_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
            button_frame.pack(fill='x', pady=(10, 0))
            
            tk.Button(button_frame, text="📊 Экспорт Отчёта",
                     command=self.export_report,
                     bg=ModernStyles.COLORS['info'], fg='white',
                     font=('Cambria', 12, 'bold'), relief='flat',
                     padx=30, pady=10).pack(side='left', padx=5)
            
            tk.Button(button_frame, text="🔄 Автообновление",
                     command=self.toggle_auto_update,
                     bg=ModernStyles.COLORS['success'], fg='white',
                     font=('Cambria', 12, 'bold'), relief='flat',
                     padx=30, pady=10).pack(side='left', padx=5)
            
            tk.Button(button_frame, text="❌ Закрыть",
                     command=self.close_dashboard,
                     bg=ModernStyles.COLORS['secondary'], fg='white',
                     font=('Cambria', 12, 'bold'), relief='flat',
                     padx=30, pady=10).pack(side='right', padx=5)
            
        except Exception as e:
            handle_module_error(e, "Панель производительности", "создание кнопок управления")
    
    def start_monitoring(self):
        """Start performance monitoring"""
        try:
            self.running = True
            self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
            self.update_thread.start()
            log_info("Мониторинг производительности запущен", "PerformanceDashboard")
            
        except Exception as e:
            handle_module_error(e, "Панель производительности", "запуск мониторинга")
    
    def _update_loop(self):
        """Background update loop"""
        while self.running:
            try:
                if self.window and self.window.winfo_exists():
                    self.update_data()
                    time.sleep(5)  # Update every 5 seconds
                else:
                    break
            except Exception as e:
                log_info(f"Ошибка обновления данных: {e}", "PerformanceDashboard")
                time.sleep(10)
    
    def update_data(self):
        """Update performance data"""
        try:
            if not self.performance_optimizer:
                return
            
            # Get current data
            self.cache_stats = self.performance_optimizer.get_cache_stats()
            self.performance_report = self.performance_optimizer.get_performance_report()
            self.optimization_suggestions = self.performance_optimizer.get_optimization_suggestions()
            
            # Update UI in main thread
            self.window.after(0, self._update_ui)
            
        except Exception as e:
            log_info(f"Ошибка получения данных производительности: {e}", "PerformanceDashboard")
    
    def _update_ui(self):
        """Update UI with current data"""
        try:
            # Update overview labels
            if self.performance_report:
                uptime = self.performance_report.get('uptime', 0)
                hours = int(uptime // 3600)
                minutes = int((uptime % 3600) // 60)
                self.uptime_label.config(text=f"Время работы: {hours}ч {minutes}м")
                
                memory_mb = self.performance_report.get('memory_usage_mb', 0)
                memory_percent = self.performance_report.get('memory_percent', 0)
                self.memory_label.config(text=f"Использование памяти: {memory_mb:.1f} МБ ({memory_percent:.1f}%)")
                
                total_queries = self.performance_report.get('total_queries', 0)
                self.queries_label.config(text=f"Всего запросов: {total_queries}")
            
            # Update cache labels
            if self.cache_stats:
                cache_size = self.cache_stats.get('size', 0)
                max_size = self.cache_stats.get('max_size', 0)
                self.cache_size_label.config(text=f"Размер кэша: {cache_size}/{max_size}")
                
                hit_rate = self.cache_stats.get('hit_rate', 0)
                self.cache_hit_rate_label.config(text=f"Эффективность кэша: {hit_rate}%")
                
                memory_usage = self.cache_stats.get('memory_usage', 'Unknown')
                self.cache_memory_label.config(text=f"Память кэша: {memory_usage}")
            
            # Update cache text
            self._update_cache_text()
            
            # Update queries text
            self._update_queries_text()
            
            # Update optimization text
            self._update_optimization_text()
            
        except Exception as e:
            log_info(f"Ошибка обновления интерфейса: {e}", "PerformanceDashboard")
    
    def _update_cache_text(self):
        """Update cache statistics text"""
        try:
            self.cache_text.delete(1.0, tk.END)
            
            if self.cache_stats:
                content = "📊 СТАТИСТИКА КЭША\n"
                content += "=" * 50 + "\n\n"
                
                for key, value in self.cache_stats.items():
                    content += f"{key}: {value}\n"
                
                content += "\n" + "=" * 50 + "\n"
                content += f"Последнее обновление: {datetime.now().strftime('%H:%M:%S')}\n"
                
                self.cache_text.insert(tk.END, content)
            
        except Exception as e:
            log_info(f"Ошибка обновления текста кэша: {e}", "PerformanceDashboard")
    
    def _update_queries_text(self):
        """Update query performance text"""
        try:
            self.queries_text.delete(1.0, tk.END)
            
            if self.performance_report:
                content = "🔍 ПРОИЗВОДИТЕЛЬНОСТЬ ЗАПРОСОВ\n"
                content += "=" * 50 + "\n\n"
                
                # Slow queries
                slow_queries = self.performance_report.get('top_slow_queries', [])
                if slow_queries:
                    content += "🐌 МЕДЛЕННЫЕ ЗАПРОСЫ:\n"
                    for i, query_info in enumerate(slow_queries[:5], 1):
                        content += f"{i}. Время: {query_info['time']:.2f}s\n"
                        content += f"   Запрос: {query_info['query'][:100]}...\n\n"
                
                # Average times
                avg_times = self.performance_report.get('average_query_times', {})
                if avg_times:
                    content += "⏱️ СРЕДНИЕ ВРЕМЕНА ВЫПОЛНЕНИЯ:\n"
                    for query, avg_time in list(avg_times.items())[:10]:
                        content += f"• {avg_time:.3f}s: {query[:80]}...\n"
                
                self.queries_text.insert(tk.END, content)
            
        except Exception as e:
            log_info(f"Ошибка обновления текста запросов: {e}", "PerformanceDashboard")
    
    def _update_optimization_text(self):
        """Update optimization recommendations text"""
        try:
            self.optimization_text.delete(1.0, tk.END)
            
            content = "⚡ РЕКОМЕНДАЦИИ ПО ОПТИМИЗАЦИИ\n"
            content += "=" * 50 + "\n\n"
            
            if self.optimization_suggestions:
                for i, suggestion in enumerate(self.optimization_suggestions, 1):
                    content += f"{i}. {suggestion}\n\n"
            else:
                content += "✅ Система работает оптимально!\n"
                content += "Рекомендаций по оптимизации не найдено.\n"
            
            content += "\n" + "=" * 50 + "\n"
            content += f"Последнее обновление: {datetime.now().strftime('%H:%M:%S')}\n"
            
            self.optimization_text.insert(tk.END, content)
            
        except Exception as e:
            log_info(f"Ошибка обновления текста оптимизации: {e}", "PerformanceDashboard")
    
    def refresh_cache_stats(self):
        """Manually refresh cache statistics"""
        try:
            self.update_data()
            messagebox.showinfo("Обновление", "Статистика кэша обновлена")
            
        except Exception as e:
            handle_module_error(e, "Панель производительности", "обновление статистики кэша")
    
    def clear_cache(self):
        """Clear all cache"""
        try:
            if messagebox.askyesno("Подтверждение", "Очистить весь кэш?"):
                if self.performance_optimizer:
                    self.performance_optimizer.invalidate_cache()
                    messagebox.showinfo("Успех", "Кэш очищен")
                    self.update_data()
                
        except Exception as e:
            handle_module_error(e, "Панель производительности", "очистка кэша")
    
    def cleanup_memory(self):
        """Force memory cleanup"""
        try:
            collected = memory_cleanup()
            messagebox.showinfo("Очистка памяти", f"Очищено {collected} объектов из памяти")
            self.update_data()
            
        except Exception as e:
            handle_module_error(e, "Панель производительности", "очистка памяти")
    
    def export_report(self):
        """Export performance report"""
        try:
            messagebox.showinfo("Экспорт", "Функция экспорта отчёта будет реализована")
            
        except Exception as e:
            handle_module_error(e, "Панель производительности", "экспорт отчёта")
    
    def toggle_auto_update(self):
        """Toggle auto-update"""
        try:
            messagebox.showinfo("Автообновление", "Автообновление включено (каждые 5 секунд)")
            
        except Exception as e:
            handle_module_error(e, "Панель производительности", "переключение автообновления")
    
    def close_dashboard(self):
        """Close dashboard"""
        try:
            self.running = False
            if self.window:
                self.window.destroy()
            log_info("Панель производительности закрыта", "PerformanceDashboard")
            
        except Exception as e:
            log_info(f"Ошибка закрытия панели: {e}", "PerformanceDashboard")


def create_performance_dashboard(parent, db_manager):
    """Create and show performance dashboard"""
    try:
        dashboard = PerformanceDashboard(parent, db_manager)
        dashboard.show_dashboard()
        return dashboard
        
    except Exception as e:
        handle_module_error(e, "Панель производительности")
        return None
