"""
Advanced Query Optimizer for Restaurant Management System
Provides query analysis, optimization suggestions, and automatic indexing
"""

import sqlite3
import re
import time
import logging
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import threading

class QueryOptimizer:
    """Advanced query optimizer with automatic indexing and performance analysis"""
    
    def __init__(self, connection_pool):
        self.connection_pool = connection_pool
        self.query_cache = {}
        self.query_stats = defaultdict(lambda: {
            'count': 0,
            'total_time': 0.0,
            'avg_time': 0.0,
            'last_executed': None,
            'slow_executions': 0
        })
        self.suggested_indexes = set()
        self.created_indexes = set()
        self.lock = threading.RLock()
        self.logger = logging.getLogger(__name__)
        
        # Initialize existing indexes
        self._discover_existing_indexes()
        
        # Create essential indexes
        self._create_essential_indexes()
    
    def _discover_existing_indexes(self):
        """Discover existing indexes in the database"""
        try:
            result = self.connection_pool.execute_query(
                "SELECT name FROM sqlite_master WHERE type='index' AND sql IS NOT NULL"
            )
            for row in result:
                self.created_indexes.add(row['name'])
        except Exception as e:
            self.logger.error(f"Error discovering indexes: {e}")
    
    def _create_essential_indexes(self):
        """Create essential indexes for common queries"""
        essential_indexes = [
            # Sales table indexes
            ("idx_sales_order_date", "CREATE INDEX IF NOT EXISTS idx_sales_order_date ON sales(order_date)"),
            ("idx_sales_payment_method", "CREATE INDEX IF NOT EXISTS idx_sales_payment_method ON sales(payment_method)"),
            ("idx_sales_department", "CREATE INDEX IF NOT EXISTS idx_sales_department ON sales(department)"),
            ("idx_sales_dish_code", "CREATE INDEX IF NOT EXISTS idx_sales_dish_code ON sales(dish_code)"),
            ("idx_sales_order_number", "CREATE INDEX IF NOT EXISTS idx_sales_order_number ON sales(order_number)"),
            ("idx_sales_composite", "CREATE INDEX IF NOT EXISTS idx_sales_composite ON sales(order_date, department, payment_method)"),
            
            # Inventory indexes
            ("idx_inventory_item_name", "CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)"),
            ("idx_inventory_category", "CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)"),
            ("idx_inventory_supplier", "CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)"),
            ("idx_inventory_low_stock", "CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)"),
            
            # Recipe indexes
            ("idx_recipes_name", "CREATE INDEX IF NOT EXISTS idx_recipes_name ON recipes(name)"),
            ("idx_recipes_category", "CREATE INDEX IF NOT EXISTS idx_recipes_category ON recipes(category)"),
            ("idx_recipe_ingredients_recipe", "CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_recipe ON recipe_ingredients(recipe_id)"),
            ("idx_recipe_ingredients_ingredient", "CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)"),
            
            # Purchase orders indexes
            ("idx_purchase_orders_date", "CREATE INDEX IF NOT EXISTS idx_purchase_orders_date ON purchase_orders(order_date)"),
            ("idx_purchase_orders_supplier", "CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)"),
            ("idx_purchase_orders_status", "CREATE INDEX IF NOT EXISTS idx_purchase_orders_status ON purchase_orders(status)"),
            
            # Staff and scheduling indexes
            ("idx_staff_role", "CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)"),
            ("idx_staff_department", "CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)"),
            ("idx_schedules_date", "CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)"),
            ("idx_schedules_staff", "CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)"),
            
            # Customer and loyalty indexes
            ("idx_customers_phone", "CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone)"),
            ("idx_customers_email", "CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email)"),
            ("idx_loyalty_customer", "CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)"),
            ("idx_loyalty_date", "CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)"),
            
            # Financial indexes
            ("idx_expenses_date", "CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)"),
            ("idx_expenses_category", "CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)"),
            ("idx_revenue_date", "CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)"),
            
            # Audit and logging indexes
            ("idx_audit_timestamp", "CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)"),
            ("idx_audit_user", "CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)"),
            ("idx_audit_action", "CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)"),
        ]
        
        for index_name, create_sql in essential_indexes:
            if index_name not in self.created_indexes:
                try:
                    self.connection_pool.execute_query(create_sql)
                    self.created_indexes.add(index_name)
                    self.logger.info(f"Created essential index: {index_name}")
                except Exception as e:
                    self.logger.error(f"Error creating index {index_name}: {e}")
    
    def analyze_query(self, query: str, params: Tuple = None) -> Dict[str, Any]:
        """Analyze a query and provide optimization suggestions"""
        query_hash = hash(query)
        
        with self.lock:
            # Update query statistics
            stats = self.query_stats[query_hash]
            stats['count'] += 1
            stats['last_executed'] = datetime.now()
        
        analysis = {
            'query_hash': query_hash,
            'query_type': self._get_query_type(query),
            'tables_accessed': self._extract_tables(query),
            'where_conditions': self._extract_where_conditions(query),
            'join_conditions': self._extract_joins(query),
            'order_by_columns': self._extract_order_by(query),
            'group_by_columns': self._extract_group_by(query),
            'suggested_indexes': [],
            'optimization_suggestions': []
        }
        
        # Analyze for potential optimizations
        self._suggest_indexes(analysis)
        self._suggest_optimizations(analysis)
        
        return analysis
    
    def _get_query_type(self, query: str) -> str:
        """Determine the type of SQL query"""
        query_upper = query.strip().upper()
        if query_upper.startswith('SELECT'):
            return 'SELECT'
        elif query_upper.startswith('INSERT'):
            return 'INSERT'
        elif query_upper.startswith('UPDATE'):
            return 'UPDATE'
        elif query_upper.startswith('DELETE'):
            return 'DELETE'
        else:
            return 'OTHER'
    
    def _extract_tables(self, query: str) -> List[str]:
        """Extract table names from query"""
        tables = []
        
        # FROM clause
        from_match = re.search(r'\bFROM\s+(\w+)', query, re.IGNORECASE)
        if from_match:
            tables.append(from_match.group(1))
        
        # JOIN clauses
        join_matches = re.findall(r'\bJOIN\s+(\w+)', query, re.IGNORECASE)
        tables.extend(join_matches)
        
        # INSERT INTO
        insert_match = re.search(r'\bINSERT\s+INTO\s+(\w+)', query, re.IGNORECASE)
        if insert_match:
            tables.append(insert_match.group(1))
        
        # UPDATE
        update_match = re.search(r'\bUPDATE\s+(\w+)', query, re.IGNORECASE)
        if update_match:
            tables.append(update_match.group(1))
        
        return list(set(tables))
    
    def _extract_where_conditions(self, query: str) -> List[Dict[str, str]]:
        """Extract WHERE conditions from query"""
        conditions = []
        
        where_match = re.search(r'\bWHERE\s+(.+?)(?:\s+ORDER\s+BY|\s+GROUP\s+BY|\s+LIMIT|$)', query, re.IGNORECASE | re.DOTALL)
        if where_match:
            where_clause = where_match.group(1)
            
            # Simple condition extraction (can be enhanced)
            condition_patterns = [
                r'(\w+)\s*=\s*[?:]',  # column = ?
                r'(\w+)\s*IN\s*\(',   # column IN (...)
                r'(\w+)\s*BETWEEN',   # column BETWEEN
                r'(\w+)\s*LIKE',      # column LIKE
                r'(\w+)\s*>\s*[?:]',  # column > ?
                r'(\w+)\s*<\s*[?:]',  # column < ?
            ]
            
            for pattern in condition_patterns:
                matches = re.findall(pattern, where_clause, re.IGNORECASE)
                for match in matches:
                    conditions.append({
                        'column': match,
                        'operator': 'various'
                    })
        
        return conditions
    
    def _extract_joins(self, query: str) -> List[Dict[str, str]]:
        """Extract JOIN conditions from query"""
        joins = []
        
        join_pattern = r'\b(INNER\s+JOIN|LEFT\s+JOIN|RIGHT\s+JOIN|JOIN)\s+(\w+)\s+ON\s+([^WHERE^ORDER^GROUP^LIMIT]+)'
        matches = re.findall(join_pattern, query, re.IGNORECASE)
        
        for match in matches:
            joins.append({
                'type': match[0],
                'table': match[1],
                'condition': match[2].strip()
            })
        
        return joins
    
    def _extract_order_by(self, query: str) -> List[str]:
        """Extract ORDER BY columns from query"""
        order_match = re.search(r'\bORDER\s+BY\s+([^LIMIT^GROUP]+)', query, re.IGNORECASE)
        if order_match:
            order_clause = order_match.group(1).strip()
            columns = [col.strip().split()[0] for col in order_clause.split(',')]
            return columns
        return []
    
    def _extract_group_by(self, query: str) -> List[str]:
        """Extract GROUP BY columns from query"""
        group_match = re.search(r'\bGROUP\s+BY\s+([^ORDER^LIMIT]+)', query, re.IGNORECASE)
        if group_match:
            group_clause = group_match.group(1).strip()
            columns = [col.strip() for col in group_clause.split(',')]
            return columns
        return []
    
    def _suggest_indexes(self, analysis: Dict[str, Any]):
        """Suggest indexes based on query analysis"""
        suggestions = []
        
        for table in analysis['tables_accessed']:
            # Suggest indexes for WHERE conditions
            for condition in analysis['where_conditions']:
                index_name = f"idx_{table}_{condition['column']}"
                if index_name not in self.created_indexes:
                    suggestions.append({
                        'index_name': index_name,
                        'table': table,
                        'columns': [condition['column']],
                        'reason': f"WHERE condition on {condition['column']}"
                    })
            
            # Suggest indexes for ORDER BY
            if analysis['order_by_columns']:
                index_name = f"idx_{table}_order_{'_'.join(analysis['order_by_columns'])}"
                if index_name not in self.created_indexes:
                    suggestions.append({
                        'index_name': index_name,
                        'table': table,
                        'columns': analysis['order_by_columns'],
                        'reason': f"ORDER BY on {', '.join(analysis['order_by_columns'])}"
                    })
            
            # Suggest composite indexes for complex queries
            if len(analysis['where_conditions']) > 1:
                columns = [cond['column'] for cond in analysis['where_conditions']]
                index_name = f"idx_{table}_composite_{'_'.join(columns)}"
                if index_name not in self.created_indexes:
                    suggestions.append({
                        'index_name': index_name,
                        'table': table,
                        'columns': columns,
                        'reason': f"Composite index for multiple WHERE conditions"
                    })
        
        analysis['suggested_indexes'] = suggestions
    
    def _suggest_optimizations(self, analysis: Dict[str, Any]):
        """Suggest query optimizations"""
        suggestions = []
        
        # Check for SELECT *
        if 'SELECT *' in analysis.get('query', '').upper():
            suggestions.append("Consider selecting only needed columns instead of SELECT *")
        
        # Check for missing LIMIT on large tables
        large_tables = ['sales', 'audit_log', 'loyalty_transactions']
        if any(table in large_tables for table in analysis['tables_accessed']):
            if 'LIMIT' not in analysis.get('query', '').upper():
                suggestions.append("Consider adding LIMIT clause for large table queries")
        
        # Check for inefficient LIKE patterns
        if any('LIKE' in str(cond) for cond in analysis['where_conditions']):
            suggestions.append("Consider using full-text search for LIKE patterns starting with %")
        
        analysis['optimization_suggestions'] = suggestions
    
    def record_execution_time(self, query_hash: int, execution_time: float):
        """Record query execution time for analysis"""
        with self.lock:
            stats = self.query_stats[query_hash]
            stats['total_time'] += execution_time
            stats['avg_time'] = stats['total_time'] / stats['count']
            
            if execution_time > 1.0:  # Slow query threshold
                stats['slow_executions'] += 1
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Generate performance report"""
        with self.lock:
            slow_queries = []
            frequent_queries = []
            
            for query_hash, stats in self.query_stats.items():
                if stats['slow_executions'] > 0:
                    slow_queries.append({
                        'query_hash': query_hash,
                        'avg_time': stats['avg_time'],
                        'slow_executions': stats['slow_executions'],
                        'total_executions': stats['count']
                    })
                
                if stats['count'] > 100:  # Frequent query threshold
                    frequent_queries.append({
                        'query_hash': query_hash,
                        'count': stats['count'],
                        'avg_time': stats['avg_time']
                    })
            
            return {
                'total_queries_analyzed': len(self.query_stats),
                'indexes_created': len(self.created_indexes),
                'indexes_suggested': len(self.suggested_indexes),
                'slow_queries': sorted(slow_queries, key=lambda x: x['avg_time'], reverse=True)[:10],
                'frequent_queries': sorted(frequent_queries, key=lambda x: x['count'], reverse=True)[:10]
            }
    
    def create_suggested_index(self, suggestion: Dict[str, Any]) -> bool:
        """Create a suggested index"""
        try:
            columns_str = ', '.join(suggestion['columns'])
            create_sql = f"CREATE INDEX IF NOT EXISTS {suggestion['index_name']} ON {suggestion['table']}({columns_str})"
            
            self.connection_pool.execute_query(create_sql)
            self.created_indexes.add(suggestion['index_name'])
            self.logger.info(f"Created suggested index: {suggestion['index_name']}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating index {suggestion['index_name']}: {e}")
            return False
