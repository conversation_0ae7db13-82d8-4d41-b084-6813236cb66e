#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import ctypes
import time

def get_current_keyboard_layout():
    """Получить текущую раскладку клавиатуры"""
    try:
        user32 = ctypes.windll.user32
        hwnd = user32.GetForegroundWindow()
        thread_id = user32.GetWindowThreadProcessId(hwnd, None)
        hkl = user32.GetKeyboardLayout(thread_id)
        
        lang_id = hkl & 0xFFFF
        if lang_id == 0x0409:
            return "Английская 🇺🇸", hkl
        elif lang_id == 0x0419:
            return "Русская 🇷🇺", hkl
        else:
            return f"Неизвестная (ID: {hex(lang_id)})", hkl
    except:
        return "Ошибка определения", 0

def switch_to_russian_keyboard_updated():
    """Обновленная функция переключения на русскую раскладку"""
    try:
        user32 = ctypes.windll.user32
        
        print("🔄 Тестирование обновленных методов переключения...")
        
        # Метод 1: Использование правильного HKL из диагностики (0x4190419)
        try:
            print("   Метод 1: ActivateKeyboardLayout с точным HKL...")
            russian_hkl = 0x4190419  # Из диагностики системы
            
            # Активируем раскладку для всех окон
            result = user32.ActivateKeyboardLayout(russian_hkl, 0x00000001)  # KLF_SETFORPROCESS
            if result:
                print("   ✅ Успех с флагом KLF_SETFORPROCESS")
                return True
                
            # Попробуем без флага
            result = user32.ActivateKeyboardLayout(russian_hkl, 0)
            if result:
                print("   ✅ Успех без флага")
                return True
                
        except Exception as e:
            print(f"   ❌ Ошибка в методе 1: {e}")
        
        # Метод 2: LoadKeyboardLayout с активацией
        try:
            print("   Метод 2: LoadKeyboardLayout...")
            hkl = user32.LoadKeyboardLayoutW("00000419", 0x00000001)  # KLF_ACTIVATE
            if hkl:
                print(f"   ✅ Успех: {hex(hkl)}")
                return True
        except Exception as e:
            print(f"   ❌ Ошибка в методе 2: {e}")
        
        # Метод 3: PostMessage с точным кодом
        try:
            print("   Метод 3: PostMessage...")
            HWND_BROADCAST = 0xFFFF
            WM_INPUTLANGCHANGEREQUEST = 0x0050
            
            result = user32.PostMessageW(HWND_BROADCAST, WM_INPUTLANGCHANGEREQUEST, 0, 0x4190419)
            if result:
                print("   ✅ Успех PostMessage")
                return True
        except Exception as e:
            print(f"   ❌ Ошибка в методе 3: {e}")
        
        # Метод 4: Ctrl+Shift
        try:
            print("   Метод 4: Ctrl+Shift...")
            user32.keybd_event(0x11, 0, 0, 0)  # Ctrl down
            user32.keybd_event(0x10, 0, 0, 0)  # Shift down
            user32.keybd_event(0x10, 0, 2, 0)  # Shift up
            user32.keybd_event(0x11, 0, 2, 0)  # Ctrl up
            print("   ✅ Ctrl+Shift отправлен")
            return True
        except Exception as e:
            print(f"   ❌ Ошибка в методе 4: {e}")
        
        # Метод 5: Alt+Shift
        try:
            print("   Метод 5: Alt+Shift...")
            user32.keybd_event(0x12, 0, 0, 0)  # Alt down
            user32.keybd_event(0x10, 0, 0, 0)  # Shift down
            user32.keybd_event(0x10, 0, 2, 0)  # Shift up
            user32.keybd_event(0x12, 0, 2, 0)  # Alt up
            print("   ✅ Alt+Shift отправлен")
            return True
        except Exception as e:
            print(f"   ❌ Ошибка в методе 5: {e}")
        
        print("   ❌ Все методы не сработали")
        return False
        
    except Exception as e:
        print(f"❌ Общая ошибка: {e}")
        return False

def main():
    print("🔧 ТЕСТИРОВАНИЕ ОБНОВЛЕННОЙ ФУНКЦИИ ПЕРЕКЛЮЧЕНИЯ РАСКЛАДКИ")
    print("=" * 70)
    
    # Показать начальную раскладку
    layout_name, layout_hkl = get_current_keyboard_layout()
    print(f"📋 Начальная раскладка: {layout_name} (HKL: {hex(layout_hkl)})")
    
    print("\n🔄 Запуск обновленной функции переключения...")
    print("-" * 50)
    
    # Попробуем переключить
    success = switch_to_russian_keyboard_updated()
    
    # Подождем немного для применения изменений
    print("\n⏳ Ожидание применения изменений...")
    time.sleep(2)
    
    # Проверим результат
    layout_name, layout_hkl = get_current_keyboard_layout()
    print(f"📋 Текущая раскладка: {layout_name} (HKL: {hex(layout_hkl)})")
    
    print("\n" + "=" * 70)
    
    if "Русская" in layout_name:
        print("🎉 УСПЕХ! Раскладка переключена на русскую!")
        print("✅ Функциональность в login_window.py должна работать")
        print("\n💡 Теперь проверьте в приложении:")
        print("   1. Запустите main.py")
        print("   2. Войдите в систему")
        print("   3. Сразу после входа попробуйте печатать русские буквы")
    else:
        print("❌ Раскладка не переключилась на русскую")
        print("\n🔍 Возможные причины:")
        print("   1. Системные ограничения Windows")
        print("   2. Антивирус блокирует изменение раскладки")
        print("   3. Требуются права администратора")
        print("   4. Раскладка переключается только в активном окне")
        print("\n💡 Попробуйте:")
        print("   1. Запустить как администратор")
        print("   2. Временно отключить антивирус")
        print("   3. Проверить настройки переключения раскладок в Windows")
    
    print(f"\n📊 Статус функции: {'✅ Работает' if success else '❌ Не работает'}")

if __name__ == "__main__":
    main()
    input("\nНажмите Enter для выхода...")
