"""
Payroll Management Module for Restaurant Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from datetime import datetime, date, timedelta
from typing import List, Dict, Any
from gui.styles import ModernStyles
from utils.helpers import <PERSON><PERSON><PERSON><PERSON>, DateHelper

class PayrollManager:
    """Complete payroll management functionality"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
    
    def create_payroll_window(self):
        """Create payroll management window"""
        window = tk.Toplevel(self.parent)
        window.title("Payroll Management")
        window.geometry("1400x900")
        window.configure(bg=ModernStyles.COLORS['bg_main'])
        
        # Main container
        main_frame = tk.Frame(window, **ModernStyles.WIDGET_STYLES['frame_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Title
        title_label = tk.Label(main_frame, text="Payroll Management",
                              **ModernStyles.WIDGET_STYLES['label_title'])
        title_label.pack(anchor='w', pady=(0, 20))
        
        # Create notebook for payroll modules
        notebook = ttk.Notebook(main_frame, style="Modern.TNotebook")
        notebook.pack(fill='both', expand=True)
        
        # Employees Tab
        employees_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(employees_frame, text="Employees")
        self.create_employees_tab(employees_frame)
        
        # Time Tracking Tab
        time_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(time_frame, text="Time Tracking")
        self.create_time_tracking_tab(time_frame)
        
        # Payroll Processing Tab
        payroll_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(payroll_frame, text="Payroll Processing")
        self.create_payroll_processing_tab(payroll_frame)
        
        # Payroll Reports Tab
        reports_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(reports_frame, text="Payroll Reports")
        self.create_payroll_reports_tab(reports_frame)
        
        return window
    
    def create_employees_tab(self, parent):
        """Create employee management tab"""
        # Controls frame
        controls_frame = ModernStyles.create_card_frame(parent)
        controls_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(controls_frame, text="Employee Management",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Buttons
        btn_frame = tk.Frame(controls_frame, bg=ModernStyles.COLORS['bg_card'])
        btn_frame.pack(fill='x')
        
        tk.Button(btn_frame, text="Add Employee", command=self.add_employee,
                 **ModernStyles.WIDGET_STYLES['button_success']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Edit Employee", command=self.edit_employee,
                 **ModernStyles.WIDGET_STYLES['button_warning']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Deactivate", command=self.deactivate_employee,
                 **ModernStyles.WIDGET_STYLES['button_danger']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Refresh", command=self.refresh_employees,
                 **ModernStyles.WIDGET_STYLES['button_primary']).pack(side='left')
        
        # Employee list
        emp_frame = ModernStyles.create_card_frame(parent)
        emp_frame.pack(fill='both', expand=True)
        
        columns = ('ID', 'Name', 'Position', 'Department', 'Hire Date', 'Salary/Rate', 'Status')
        self.employees_tree = ttk.Treeview(emp_frame, columns=columns, show='headings',
                                          style="Modern.Treeview")
        
        for col in columns:
            self.employees_tree.heading(col, text=col)
            self.employees_tree.column(col, width=120)
        
        # Scrollbars
        emp_v_scroll = ttk.Scrollbar(emp_frame, orient='vertical', command=self.employees_tree.yview)
        emp_h_scroll = ttk.Scrollbar(emp_frame, orient='horizontal', command=self.employees_tree.xview)
        self.employees_tree.configure(yscrollcommand=emp_v_scroll.set, xscrollcommand=emp_h_scroll.set)
        
        self.employees_tree.pack(side='left', fill='both', expand=True)
        emp_v_scroll.pack(side='right', fill='y')
        emp_h_scroll.pack(side='bottom', fill='x')
        
        self.refresh_employees()
    
    def create_time_tracking_tab(self, parent):
        """Create time tracking tab"""
        # Controls frame
        controls_frame = ModernStyles.create_card_frame(parent)
        controls_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(controls_frame, text="Time Tracking",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Date selection
        date_frame = tk.Frame(controls_frame, bg=ModernStyles.COLORS['bg_card'])
        date_frame.pack(fill='x', pady=(0, 10))
        
        tk.Label(date_frame, text="Date:",
                **ModernStyles.WIDGET_STYLES['label_body']).pack(side='left')
        
        self.time_date_var = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
        tk.Entry(date_frame, textvariable=self.time_date_var, width=12,
                **ModernStyles.WIDGET_STYLES['entry']).pack(side='left', padx=(10, 0))
        
        # Buttons
        btn_frame = tk.Frame(controls_frame, bg=ModernStyles.COLORS['bg_card'])
        btn_frame.pack(fill='x')
        
        tk.Button(btn_frame, text="Clock In", command=self.clock_in,
                 **ModernStyles.WIDGET_STYLES['button_success']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Clock Out", command=self.clock_out,
                 **ModernStyles.WIDGET_STYLES['button_warning']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Add Manual Entry", command=self.add_manual_time,
                 **ModernStyles.WIDGET_STYLES['button_secondary']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="View Timesheet", command=self.view_timesheet,
                 **ModernStyles.WIDGET_STYLES['button_primary']).pack(side='left')
        
        # Time entries display
        time_frame = ModernStyles.create_card_frame(parent)
        time_frame.pack(fill='both', expand=True)
        
        columns = ('Employee', 'Date', 'Clock In', 'Clock Out', 'Regular Hours', 'Overtime', 'Total Hours')
        self.time_tree = ttk.Treeview(time_frame, columns=columns, show='headings',
                                     style="Modern.Treeview")
        
        for col in columns:
            self.time_tree.heading(col, text=col)
            self.time_tree.column(col, width=100)
        
        # Scrollbars
        time_v_scroll = ttk.Scrollbar(time_frame, orient='vertical', command=self.time_tree.yview)
        time_h_scroll = ttk.Scrollbar(time_frame, orient='horizontal', command=self.time_tree.xview)
        self.time_tree.configure(yscrollcommand=time_v_scroll.set, xscrollcommand=time_h_scroll.set)
        
        self.time_tree.pack(side='left', fill='both', expand=True)
        time_v_scroll.pack(side='right', fill='y')
        time_h_scroll.pack(side='bottom', fill='x')
    
    def create_payroll_processing_tab(self, parent):
        """Create payroll processing tab"""
        # Controls frame
        controls_frame = ModernStyles.create_card_frame(parent)
        controls_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(controls_frame, text="Payroll Processing",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Pay period selection
        period_frame = tk.Frame(controls_frame, bg=ModernStyles.COLORS['bg_card'])
        period_frame.pack(fill='x', pady=(0, 10))
        
        tk.Label(period_frame, text="Pay Period:",
                **ModernStyles.WIDGET_STYLES['label_body']).pack(side='left')
        
        self.pay_period_start = tk.StringVar()
        self.pay_period_end = tk.StringVar()
        
        tk.Entry(period_frame, textvariable=self.pay_period_start, width=12,
                **ModernStyles.WIDGET_STYLES['entry']).pack(side='left', padx=(10, 5))
        
        tk.Label(period_frame, text="to", bg=ModernStyles.COLORS['bg_card']).pack(side='left', padx=5)
        
        tk.Entry(period_frame, textvariable=self.pay_period_end, width=12,
                **ModernStyles.WIDGET_STYLES['entry']).pack(side='left', padx=(5, 10))
        
        # Buttons
        btn_frame = tk.Frame(controls_frame, bg=ModernStyles.COLORS['bg_card'])
        btn_frame.pack(fill='x')
        
        tk.Button(btn_frame, text="Calculate Payroll", command=self.calculate_payroll,
                 **ModernStyles.WIDGET_STYLES['button_primary']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Process Payroll", command=self.process_payroll,
                 **ModernStyles.WIDGET_STYLES['button_success']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Generate Paystubs", command=self.generate_paystubs,
                 **ModernStyles.WIDGET_STYLES['button_secondary']).pack(side='left')
        
        # Payroll summary
        summary_frame = ModernStyles.create_card_frame(parent)
        summary_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(summary_frame, text="Payroll Summary",
                **ModernStyles.WIDGET_STYLES['label_subheading']).pack(anchor='w', pady=(0, 10))
        
        # Summary stats
        stats_frame = tk.Frame(summary_frame, bg=ModernStyles.COLORS['bg_card'])
        stats_frame.pack(fill='x')
        
        self.create_payroll_stat(stats_frame, "Total Employees", "0")
        self.create_payroll_stat(stats_frame, "Total Hours", "0.0")
        self.create_payroll_stat(stats_frame, "Gross Pay", "$0.00")
        self.create_payroll_stat(stats_frame, "Net Pay", "$0.00")
        
        # Payroll details
        payroll_frame = ModernStyles.create_card_frame(parent)
        payroll_frame.pack(fill='both', expand=True)
        
        columns = ('Employee', 'Regular Hours', 'Overtime', 'Gross Pay', 'Deductions', 'Net Pay', 'Status')
        self.payroll_tree = ttk.Treeview(payroll_frame, columns=columns, show='headings',
                                        style="Modern.Treeview")
        
        for col in columns:
            self.payroll_tree.heading(col, text=col)
            self.payroll_tree.column(col, width=100)
        
        # Scrollbars
        pay_v_scroll = ttk.Scrollbar(payroll_frame, orient='vertical', command=self.payroll_tree.yview)
        pay_h_scroll = ttk.Scrollbar(payroll_frame, orient='horizontal', command=self.payroll_tree.xview)
        self.payroll_tree.configure(yscrollcommand=pay_v_scroll.set, xscrollcommand=pay_h_scroll.set)
        
        self.payroll_tree.pack(side='left', fill='both', expand=True)
        pay_v_scroll.pack(side='right', fill='y')
        pay_h_scroll.pack(side='bottom', fill='x')
    
    def create_payroll_reports_tab(self, parent):
        """Create payroll reports tab"""
        # Controls frame
        controls_frame = ModernStyles.create_card_frame(parent)
        controls_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(controls_frame, text="Payroll Reports",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Report buttons
        btn_frame = tk.Frame(controls_frame, bg=ModernStyles.COLORS['bg_card'])
        btn_frame.pack(fill='x')
        
        tk.Button(btn_frame, text="Payroll Register", command=self.payroll_register_report,
                 **ModernStyles.WIDGET_STYLES['button_primary']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Tax Summary", command=self.tax_summary_report,
                 **ModernStyles.WIDGET_STYLES['button_secondary']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Labor Cost Report", command=self.labor_cost_report,
                 **ModernStyles.WIDGET_STYLES['button_success']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Employee Summary", command=self.employee_summary_report,
                 **ModernStyles.WIDGET_STYLES['button_warning']).pack(side='left')
        
        # Reports display
        self.payroll_reports_frame = ModernStyles.create_card_frame(parent)
        self.payroll_reports_frame.pack(fill='both', expand=True)
        
        # Initial placeholder
        tk.Label(self.payroll_reports_frame, text="Select a report to generate payroll analysis",
                font=ModernStyles.FONTS['body'],
                fg=ModernStyles.COLORS['text_secondary'],
                bg=ModernStyles.COLORS['bg_card']).pack(expand=True)
    
    def create_payroll_stat(self, parent, label, value):
        """Create a payroll statistic display"""
        stat_frame = tk.Frame(parent, bg=ModernStyles.COLORS['primary'], relief='flat', bd=0)
        stat_frame.pack(side='left', fill='both', expand=True, padx=5)
        
        inner_frame = tk.Frame(stat_frame, bg=ModernStyles.COLORS['primary'])
        inner_frame.pack(fill='both', expand=True, padx=15, pady=15)
        
        value_label = tk.Label(inner_frame, text=value,
                              font=ModernStyles.FONTS['heading'],
                              fg=ModernStyles.COLORS['text_white'],
                              bg=ModernStyles.COLORS['primary'])
        value_label.pack()
        
        label_label = tk.Label(inner_frame, text=label,
                              font=ModernStyles.FONTS['body'],
                              fg=ModernStyles.COLORS['text_white'],
                              bg=ModernStyles.COLORS['primary'])
        label_label.pack()
    
    # Employee Management Methods
    def add_employee(self):
        """Add new employee"""
        dialog = EmployeeDialog(self.parent, "Add Employee", self.db_manager)
        if dialog.result:
            self.refresh_employees()
    
    def edit_employee(self):
        """Edit selected employee"""
        selection = self.employees_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select an employee to edit.")
            return
        
        messagebox.showinfo("Feature", "Edit employee functionality will be implemented.")
    
    def deactivate_employee(self):
        """Deactivate selected employee"""
        selection = self.employees_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select an employee to deactivate.")
            return
        
        if messagebox.askyesno("Confirm", "Are you sure you want to deactivate this employee?"):
            messagebox.showinfo("Feature", "Employee deactivation will be implemented.")
    
    def refresh_employees(self):
        """Refresh employee list"""
        try:
            # Clear existing data
            for item in self.employees_tree.get_children():
                self.employees_tree.delete(item)
            
            # Get employees from database
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT employee_id, first_name, last_name, position, department, 
                           hire_date, salary, hourly_rate, is_active
                    FROM employees
                    ORDER BY last_name, first_name
                ''')
                
                employees = cursor.fetchall()
                
                for emp in employees:
                    name = f"{emp[1]} {emp[2]}"
                    salary_rate = NumberHelper.format_currency(emp[6]) if emp[6] > 0 else f"${emp[7]:.2f}/hr"
                    status = "Active" if emp[8] else "Inactive"
                    
                    self.employees_tree.insert('', 'end', values=(
                        emp[0],  # ID
                        name,    # Name
                        emp[3] or '',  # Position
                        emp[4] or '',  # Department
                        emp[5] or '',  # Hire Date
                        salary_rate,   # Salary/Rate
                        status         # Status
                    ))
        
        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh employees: {str(e)}")
    
    # Time Tracking Methods
    def clock_in(self):
        """Clock in employee"""
        messagebox.showinfo("Feature", "Clock in functionality will be implemented.")
    
    def clock_out(self):
        """Clock out employee"""
        messagebox.showinfo("Feature", "Clock out functionality will be implemented.")
    
    def add_manual_time(self):
        """Add manual time entry"""
        messagebox.showinfo("Feature", "Manual time entry will be implemented.")
    
    def view_timesheet(self):
        """View employee timesheet"""
        messagebox.showinfo("Feature", "Timesheet view will be implemented.")
    
    # Payroll Processing Methods
    def calculate_payroll(self):
        """Calculate payroll for pay period"""
        messagebox.showinfo("Feature", "Payroll calculation will be implemented.")
    
    def process_payroll(self):
        """Process calculated payroll"""
        messagebox.showinfo("Feature", "Payroll processing will be implemented.")
    
    def generate_paystubs(self):
        """Generate paystubs for employees"""
        messagebox.showinfo("Feature", "Paystub generation will be implemented.")
    
    # Report Methods
    def payroll_register_report(self):
        """Generate payroll register report"""
        messagebox.showinfo("Feature", "Payroll register report will be implemented.")
    
    def tax_summary_report(self):
        """Generate tax summary report"""
        messagebox.showinfo("Feature", "Tax summary report will be implemented.")
    
    def labor_cost_report(self):
        """Generate labor cost report"""
        messagebox.showinfo("Feature", "Labor cost report will be implemented.")
    
    def employee_summary_report(self):
        """Generate employee summary report"""
        messagebox.showinfo("Feature", "Employee summary report will be implemented.")

class EmployeeDialog:
    """Dialog for adding/editing employees"""
    
    def __init__(self, parent, title="Employee", db_manager=None):
        self.result = None
        self.db_manager = db_manager
        self.create_dialog(parent, title)
    
    def create_dialog(self, parent, title):
        """Create the employee dialog"""
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x600")
        self.dialog.configure(bg=ModernStyles.COLORS['bg_main'])
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Main frame
        main_frame = tk.Frame(self.dialog, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Form fields
        self.fields = {}
        field_configs = [
            ('employee_id', 'Employee ID*', 'text'),
            ('first_name', 'First Name*', 'text'),
            ('last_name', 'Last Name*', 'text'),
            ('position', 'Position', 'text'),
            ('department', 'Department', 'text'),
            ('hire_date', 'Hire Date (YYYY-MM-DD)', 'text'),
            ('salary', 'Annual Salary', 'float'),
            ('hourly_rate', 'Hourly Rate', 'float'),
            ('email', 'Email', 'text'),
            ('phone', 'Phone', 'text'),
            ('address', 'Address', 'text')
        ]
        
        for field_key, field_label, field_type in field_configs:
            # Label
            tk.Label(main_frame, text=field_label,
                    **ModernStyles.WIDGET_STYLES['label_body']).pack(anchor='w', pady=(10, 0))
            
            # Entry
            var = tk.StringVar()
            entry = tk.Entry(main_frame, textvariable=var,
                           **ModernStyles.WIDGET_STYLES['entry'])
            entry.pack(fill='x', pady=(0, 5))
            
            self.fields[field_key] = {'var': var, 'type': field_type}
        
        # Buttons
        button_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_main'])
        button_frame.pack(fill='x', pady=(20, 0))
        
        tk.Button(button_frame, text="Save", command=self.save_employee,
                 **ModernStyles.WIDGET_STYLES['button_success']).pack(side='right')
        
        tk.Button(button_frame, text="Cancel", command=self.dialog.destroy,
                 **ModernStyles.WIDGET_STYLES['button_secondary']).pack(side='right', padx=(0, 10))
    
    def save_employee(self):
        """Save the employee"""
        try:
            # Validate required fields
            if not self.fields['employee_id']['var'].get().strip():
                messagebox.showerror("Error", "Employee ID is required.")
                return
            
            if not self.fields['first_name']['var'].get().strip():
                messagebox.showerror("Error", "First name is required.")
                return
            
            if not self.fields['last_name']['var'].get().strip():
                messagebox.showerror("Error", "Last name is required.")
                return
            
            # Collect data
            employee_data = {}
            for field_key, field_data in self.fields.items():
                value = field_data['var'].get().strip()
                
                if field_data['type'] == 'float':
                    try:
                        employee_data[field_key] = float(value) if value else 0.0
                    except ValueError:
                        messagebox.showerror("Error", f"Invalid numeric value for {field_key}")
                        return
                else:
                    employee_data[field_key] = value
            
            # Insert into database
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO employees 
                    (employee_id, first_name, last_name, position, department, 
                     hire_date, salary, hourly_rate, email, phone, address)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    employee_data['employee_id'],
                    employee_data['first_name'],
                    employee_data['last_name'],
                    employee_data['position'],
                    employee_data['department'],
                    employee_data['hire_date'] or None,
                    employee_data['salary'],
                    employee_data['hourly_rate'],
                    employee_data['email'],
                    employee_data['phone'],
                    employee_data['address']
                ))
                conn.commit()
            
            self.result = employee_data
            self.dialog.destroy()
            messagebox.showinfo("Success", "Employee added successfully.")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save employee: {str(e)}")


def create_payroll_system(parent, db_manager):
    """Create and show the payroll management window"""
    manager = PayrollManager(parent, db_manager)
    return manager.create_payroll_window()
