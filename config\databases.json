{"databases": [{"id": "default_restaurant", "name": "Кафе \"Библос\"", "description": "Кафе \"Библос\"", "file_path": "databases/default_restaurant.db", "created_date": "2025-06-08T21:55:47.918697", "last_accessed": "2025-07-06T12:04:09.349367", "is_active": true}, {"id": "кафе_библос_гриль", "name": "Кафе \"Библос Гриль\"", "description": "Кафе \"Библос Гриль\" ТЦ \"Сити Молл\"", "file_path": "databases/кафе_библос_гриль.db", "created_date": "2025-06-15T14:26:00.706129", "last_accessed": "2025-06-15T15:34:19.130486", "is_active": true}, {"id": "кафе_чайхана_дамаск_1", "name": "Кафе \"Чайхана Дамаск 1\"", "description": "Ка<PERSON>е \"Ч<PERSON>йхана Дамаск\" ТЦ \"Сити Молл\"", "file_path": "databases/кафе_чайхана_дамаск_1.db", "created_date": "2025-06-15T15:38:34.277492", "last_accessed": "2025-06-15T16:06:54.891468", "is_active": true}, {"id": "кафе_чайхана_дамаск_2", "name": "Кафе \"Чайхана Дамаск 2\"", "description": "Ка<PERSON>е \"Ч<PERSON>йхана Дамаск\" ТЦ \"Призма\"", "file_path": "databases/кафе_чайхана_дамаск_2.db", "created_date": "2025-06-15T15:40:58.189972", "last_accessed": "2025-06-15T15:52:54.323859", "is_active": true}], "last_selected": "default_restaurant"}