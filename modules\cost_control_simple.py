"""
Простая система контроля затрат без зависимости от matplotlib
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
from gui.styles import ModernStyles

class CostControlSystem:
    """Система контроля затрат"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
    
    def create_cost_control_window(self):
        """Создать окно контроля затрат"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("📊 Контроль Затрат")
        self.window.geometry("1400x900")
        self.window.configure(bg='white')
        self.window.resizable(True, True)
        
        self.create_interface()
        return self.window
    
    def create_interface(self):
        """Создать интерфейс"""
        # Заголовок
        header_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['primary'], height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="📊 Контроль Затрат",
                font=('Arial', 16, 'bold'), bg=ModernStyles.COLORS['primary'], 
                fg='white').pack(side='left', padx=20, pady=15)
        
        # Кнопки действий
        btn_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['primary'])
        btn_frame.pack(side='right', padx=20, pady=10)
        
        tk.Button(btn_frame, text="📤 Экспорт", command=self.export_report,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="🔄 Обновить", command=self.refresh_data,
                 bg=ModernStyles.COLORS['secondary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        # Основной контент
        main_frame = tk.Frame(self.window, bg='white')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Создать вкладки
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill='both', expand=True)
        
        # Вкладка "Обзор затрат"
        overview_frame = tk.Frame(notebook, bg='white')
        notebook.add(overview_frame, text="📊 Обзор Затрат")
        self.create_overview_tab(overview_frame)
        
        # Вкладка "Анализ продуктов"
        food_frame = tk.Frame(notebook, bg='white')
        notebook.add(food_frame, text="🍽️ Продукты")
        self.create_food_analysis_tab(food_frame)
        
        # Вкладка "Анализ персонала"
        labor_frame = tk.Frame(notebook, bg='white')
        notebook.add(labor_frame, text="👥 Персонал")
        self.create_labor_analysis_tab(labor_frame)
        
        # Вкладка "Калькулятор рецептов"
        recipe_frame = tk.Frame(notebook, bg='white')
        notebook.add(recipe_frame, text="📋 Рецепты")
        self.create_recipe_calculator_tab(recipe_frame)
        
        # Вкладка "Бюджет vs Факт"
        budget_frame = tk.Frame(notebook, bg='white')
        notebook.add(budget_frame, text="💰 Бюджет")
        self.create_budget_tab(budget_frame)
    
    def create_overview_tab(self, parent):
        """Создать вкладку обзора затрат"""
        # KPI карточки
        kpi_frame = tk.Frame(parent, bg='white')
        kpi_frame.pack(fill='x', pady=(0, 20))
        
        kpis = [
            ("Общие затраты", "485,600₽", ModernStyles.COLORS['primary']),
            ("Затраты на продукты", "312,400₽", ModernStyles.COLORS['warning']),
            ("Затраты на персонал", "156,200₽", ModernStyles.COLORS['info']),
            ("Прочие затраты", "17,000₽", ModernStyles.COLORS['secondary'])
        ]
        
        for title, value, color in kpis:
            kpi_card = tk.Frame(kpi_frame, bg=color, relief='solid', bd=1)
            kpi_card.pack(side='left', fill='both', expand=True, padx=5)
            
            tk.Label(kpi_card, text=title, font=('Arial', 10),
                    bg=color, fg='white').pack(pady=(10, 5))
            tk.Label(kpi_card, text=value, font=('Arial', 16, 'bold'),
                    bg=color, fg='white').pack(pady=(0, 10))
        
        # Детальная информация
        details_frame = tk.Frame(parent, bg='white')
        details_frame.pack(fill='both', expand=True)
        
        # Левая колонка - структура затрат
        left_frame = tk.Frame(details_frame, bg='#f8f9fa', relief='solid', bd=1)
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        tk.Label(left_frame, text="📊 Структура Затрат",
                font=('Arial', 14, 'bold'), bg='#f8f9fa').pack(pady=15)
        
        cost_structure = [
            ("Продукты питания", "312,400₽", "64.3%"),
            ("Заработная плата", "156,200₽", "32.2%"),
            ("Аренда помещения", "8,500₽", "1.8%"),
            ("Коммунальные услуги", "5,200₽", "1.1%"),
            ("Прочие расходы", "3,300₽", "0.7%")
        ]
        
        for category, amount, percent in cost_structure:
            item_frame = tk.Frame(left_frame, bg='#f8f9fa')
            item_frame.pack(fill='x', padx=15, pady=5)
            
            tk.Label(item_frame, text=category, font=('Arial', 11),
                    bg='#f8f9fa').pack(side='left')
            tk.Label(item_frame, text=f"{amount} ({percent})", font=('Arial', 11, 'bold'),
                    fg=ModernStyles.COLORS['primary'], bg='#f8f9fa').pack(side='right')
        
        # Правая колонка - тренды
        right_frame = tk.Frame(details_frame, bg='#f8f9fa', relief='solid', bd=1)
        right_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        tk.Label(right_frame, text="📈 Тренды Затрат",
                font=('Arial', 14, 'bold'), bg='#f8f9fa').pack(pady=15)
        
        trends_data = [
            ("Январь", "445,200₽", "+2.3%"),
            ("Февраль", "467,800₽", "+5.1%"),
            ("Март", "452,100₽", "-3.4%"),
            ("Апрель", "478,900₽", "+5.9%"),
            ("Май", "485,600₽", "+1.4%"),
            ("Прогноз Июнь", "492,000₽", "+1.3%")
        ]
        
        for month, amount, change in trends_data:
            trend_frame = tk.Frame(right_frame, bg='#f8f9fa')
            trend_frame.pack(fill='x', padx=15, pady=5)
            
            tk.Label(trend_frame, text=month, font=('Arial', 11),
                    bg='#f8f9fa').pack(side='left')
            
            change_color = ModernStyles.COLORS['success'] if '+' in change else ModernStyles.COLORS['danger']
            tk.Label(trend_frame, text=f"{amount} ({change})", font=('Arial', 11, 'bold'),
                    fg=change_color, bg='#f8f9fa').pack(side='right')
    
    def create_food_analysis_tab(self, parent):
        """Создать вкладку анализа продуктов"""
        # Заголовок
        tk.Label(parent, text="🍽️ Анализ Затрат на Продукты",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)
        
        # Контролы
        controls_frame = tk.Frame(parent, bg='#f8f9fa', relief='solid', bd=1)
        controls_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        tk.Label(controls_frame, text="Период анализа:",
                font=('Arial', 11), bg='#f8f9fa').pack(side='left', padx=15, pady=10)
        
        period_var = tk.StringVar(value="Текущий месяц")
        period_combo = ttk.Combobox(controls_frame, textvariable=period_var,
                                   values=["Текущий месяц", "Последние 3 месяца", "Последние 6 месяцев"],
                                   width=20)
        period_combo.pack(side='left', padx=10, pady=10)
        
        tk.Button(controls_frame, text="🔍 Анализировать",
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=15)
        
        # Таблица данных
        table_frame = tk.Frame(parent, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20)
        
        # Заголовки таблицы
        headers = ["Категория", "Бюджет", "Факт", "Отклонение", "% от общих затрат"]
        
        # Создать заголовки
        header_frame = tk.Frame(table_frame, bg=ModernStyles.COLORS['primary'])
        header_frame.pack(fill='x')
        
        for header in headers:
            tk.Label(header_frame, text=header, font=('Arial', 11, 'bold'),
                    bg=ModernStyles.COLORS['primary'], fg='white',
                    relief='solid', bd=1).pack(side='left', fill='both', expand=True)
        
        # Данные таблицы
        food_data = [
            ("Мясо и птица", "125,000₽", "132,400₽", "+7,400₽", "27.3%"),
            ("Рыба и морепродукты", "45,000₽", "42,800₽", "-2,200₽", "8.8%"),
            ("Овощи и фрукты", "65,000₽", "68,200₽", "+3,200₽", "14.1%"),
            ("Молочные продукты", "35,000₽", "33,600₽", "-1,400₽", "6.9%"),
            ("Крупы и мука", "25,000₽", "26,800₽", "+1,800₽", "5.5%"),
            ("Напитки", "15,000₽", "14,200₽", "-800₽", "2.9%"),
            ("Прочее", "10,000₽", "12,400₽", "+2,400₽", "2.6%")
        ]
        
        for row_data in food_data:
            row_frame = tk.Frame(table_frame, bg='white')
            row_frame.pack(fill='x')
            
            for i, cell_data in enumerate(row_data):
                color = 'white'
                text_color = 'black'
                
                if i == 3:  # Колонка отклонения
                    if '+' in cell_data:
                        text_color = ModernStyles.COLORS['danger']
                    elif '-' in cell_data:
                        text_color = ModernStyles.COLORS['success']
                
                tk.Label(row_frame, text=cell_data, font=('Arial', 10),
                        bg=color, fg=text_color,
                        relief='solid', bd=1).pack(side='left', fill='both', expand=True)
    
    def create_labor_analysis_tab(self, parent):
        """Создать вкладку анализа персонала"""
        tk.Label(parent, text="👥 Анализ Затрат на Персонал",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)
        
        # Информационный блок
        info_frame = tk.Frame(parent, bg='#f8f9fa', relief='solid', bd=1)
        info_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        info_text = """
📊 АНАЛИЗ ЗАТРАТ НА ПЕРСОНАЛ:

💰 Общие затраты на персонал: 156,200₽
👥 Количество сотрудников: 24 человека
⏰ Общее количество часов: 3,840 часов
💵 Средняя ставка: 40.7₽/час

📈 ПО ОТДЕЛАМ:
• Кухня: 89,400₽ (57.2%) - 14 человек
• Обслуживание: 45,600₽ (29.2%) - 8 человек
• Администрация: 21,200₽ (13.6%) - 2 человека

📊 АНАЛИЗ ЭФФЕКТИВНОСТИ:
• Затраты на персонал к выручке: 24.8%
• Производительность: 2,025₽ выручки на час работы
• Средняя зарплата: 6,508₽ на сотрудника

🎯 РЕКОМЕНДАЦИИ:
• Оптимизировать график работы в непиковые часы
• Рассмотреть возможность перераспределения нагрузки
• Внедрить систему премирования за эффективность
        """
        
        tk.Label(info_frame, text=info_text, font=('Arial', 11),
                bg='#f8f9fa', justify='left', anchor='nw').pack(fill='both', expand=True, padx=20, pady=20)
    
    def create_recipe_calculator_tab(self, parent):
        """Создать вкладку калькулятора рецептов"""
        tk.Label(parent, text="📋 Калькулятор Себестоимости Рецептов",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)
        
        # Форма расчёта
        calc_frame = tk.Frame(parent, bg='#f8f9fa', relief='solid', bd=1)
        calc_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        # Выбор рецепта
        recipe_frame = tk.Frame(calc_frame, bg='#f8f9fa')
        recipe_frame.pack(fill='x', padx=20, pady=15)
        
        tk.Label(recipe_frame, text="Выберите рецепт:",
                font=('Arial', 11), bg='#f8f9fa').pack(side='left')
        
        recipe_var = tk.StringVar(value="Борщ украинский")
        recipe_combo = ttk.Combobox(recipe_frame, textvariable=recipe_var,
                                   values=["Борщ украинский", "Стейк рибай", "Салат Цезарь", "Котлета по-киевски"],
                                   width=25)
        recipe_combo.pack(side='left', padx=15)
        
        tk.Button(recipe_frame, text="💰 Рассчитать",
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=15)
        
        # Результат расчёта
        result_frame = tk.Frame(parent, bg='white')
        result_frame.pack(fill='both', expand=True, padx=20)
        
        result_text = """
📋 РАСЧЁТ СЕБЕСТОИМОСТИ: Борщ украинский

🥘 ИНГРЕДИЕНТЫ:
• Говядина (300г) - 180₽
• Свёкла (150г) - 12₽
• Капуста (200г) - 15₽
• Морковь (100g) - 8₽
• Лук (80г) - 6₽
• Картофель (200г) - 14₽
• Томатная паста (50г) - 8₽
• Сметана (50г) - 25₽
• Специи и зелень - 12₽

💰 РАСЧЁТ СТОИМОСТИ:
• Общая стоимость ингредиентов: 280₽
• Потери при обработке (5%): 14₽
• Себестоимость порции: 294₽

📊 АНАЛИЗ РЕНТАБЕЛЬНОСТИ:
• Цена продажи: 450₽
• Валовая прибыль: 156₽
• Маржинальность: 34.7%
• Рекомендуемая цена: 420-480₽

🎯 РЕКОМЕНДАЦИИ:
• Текущая цена оптимальна
• Возможно снижение затрат на 8-12₽ при оптимизации закупок
• Рассмотреть сезонные вариации цен на овощи
        """
        
        tk.Label(result_frame, text=result_text, font=('Arial', 11),
                bg='white', justify='left', anchor='nw').pack(fill='both', expand=True)
    
    def create_budget_tab(self, parent):
        """Создать вкладку бюджета"""
        tk.Label(parent, text="💰 Бюджет vs Фактические Затраты",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)
        
        # Сводка
        summary_frame = tk.Frame(parent, bg='#f8f9fa', relief='solid', bd=1)
        summary_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        summary_text = """
📊 СВОДКА ЗА МАЙ 2024:

💰 ОБЩИЙ БЮДЖЕТ: 465,000₽
💸 ФАКТИЧЕСКИЕ ЗАТРАТЫ: 485,600₽
📈 ОТКЛОНЕНИЕ: +20,600₽ (+4.4%)

🔍 ДЕТАЛИЗАЦИЯ ОТКЛОНЕНИЙ:
• Продукты: +12,400₽ (+4.1%)
• Персонал: +6,200₽ (+4.1%)  
• Аренда: 0₽ (0%)
• Коммунальные: +1,200₽ (+30%)
• Прочее: +800₽ (+32%)

⚠️ ОСНОВНЫЕ ПРИЧИНЫ ПРЕВЫШЕНИЯ:
• Рост цен на мясо и птицу (+8%)
• Дополнительные смены в выходные
• Повышенное потребление электроэнергии
• Незапланированный ремонт оборудования

🎯 ПЛАН ДЕЙСТВИЙ:
• Пересмотреть поставщиков мяса
• Оптимизировать график работы персонала
• Внедрить энергосберегающие технологии
• Создать резерв на непредвиденные расходы
        """
        
        tk.Label(summary_frame, text=summary_text, font=('Arial', 11),
                bg='#f8f9fa', justify='left', anchor='nw').pack(fill='both', expand=True, padx=20, pady=20)
    
    def export_report(self):
        """Экспорт отчёта"""
        messagebox.showinfo("Экспорт", "Отчёт по контролю затрат экспортирован")
    
    def refresh_data(self):
        """Обновить данные"""
        messagebox.showinfo("Обновление", "Данные обновлены")

def create_cost_control_system(parent, db_manager):
    """Создать систему контроля затрат"""
    system = CostControlSystem(parent, db_manager)
    return system.create_cost_control_window()
