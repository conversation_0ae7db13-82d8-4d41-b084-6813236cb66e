"""
Restaurant Accounting and Inventory Management System
Main Application Entry Point

A comprehensive system for managing restaurant operations including:
- Sales data import and management
- Inventory tracking and management
- Purchase order management with VAT and discounts
- Recipe and semi-finished products management
- Reporting and analytics
- User access control

Author: Restaurant Management System
Version: 1.0.0
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


try:
    from gui.main_window import MainWindow
    from gui.simple_login import SimpleLoginWindow
    from database.db_manager import DatabaseManager
    from modules.database_manager import create_database_selector
    from utils.logger import init_logger, get_logger
    from utils.notification_system import init_notifications
    from utils.backup_system import init_backup_system
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all required modules are installed.")
    sys.exit(1)

class RestaurantManagementApp:
    """Main application class"""
    
    def __init__(self):
        self.app = None
        self.initialize_app()
    
    def initialize_app(self):
        """Initialize the application with professional systems"""
        try:
            # Initialize professional systems
            print("Initializing professional systems...")

            # 1. Show database selector first
            print("Starting database selector...")
            selected_db = create_database_selector()  # Assuming this handles database selection

            if not selected_db:
                print("База данных не выбрана. Выход из приложения.")
                sys.exit(0)

            print(f"✅ Выбрана база данных: {selected_db['name']}")

            # 2. Initialize logging system
            logger = init_logger()
            logger.log_system_startup("1.0.0")
            print("✅ Logging system initialized")

            # 3. Initialize database with selected database
            db_manager = DatabaseManager(selected_db['file_path'])
            logger.log_app_event(f"Database initialized: {selected_db['name']}")
            print(f"✅ Database initialized: {selected_db['file_path']}")

            # 4. Initialize backup system
            backup_system = init_backup_system(selected_db['file_path'])
            logger.log_app_event("Backup system initialized")
            print("✅ Backup system initialized")
            
            # 5. Show login window
            print("Starting login system...")
            login_window = SimpleLoginWindow(db_manager, selected_db)
            authenticated_user = login_window.run()

            if authenticated_user:
                logger.log_login_attempt(authenticated_user['username'], True)
                print(f"User authenticated: {authenticated_user['username']}")

                # 5. Create main window with professional features
                self.app = MainWindow(selected_db)
                self.app.current_user = authenticated_user
                self.app.db_manager = db_manager
                self.app.logger = logger
                self.app.backup_system = backup_system

                # 6. Initialize notification system
                notification_system = init_notifications(self.app.root)
                self.app.notification_system = notification_system
                logger.log_app_event("Notification system initialized")
                print("✅ Notification system initialized")

                # 7. Update user info display
                self.app.update_user_info()

                # 8. Update status bar with user info
                if hasattr(self.app, 'status_label'):
                    self.app.status_label.config(text=f"Вошёл: {authenticated_user['full_name']} | Система готова")

                # 9. Send welcome notification
                notification_system.add_notification(
                    "Добро пожаловать!",
                    f"Пользователь {authenticated_user['full_name']} успешно вошёл в систему",
                    "info",
                    auto_dismiss=3000
                )

                logger.log_app_event("Application initialized successfully",
                                   user_id=authenticated_user['id'])
                print("✅ Application initialized successfully!")
            else:
                logger.log_app_event("Authentication cancelled or failed")
                print("Authentication cancelled or failed.")
                sys.exit(0)

        except Exception as e:
            error_msg = f"Failed to initialize application: {str(e)}"
            print(error_msg)
            print("Full error traceback:")
            import traceback
            traceback.print_exc()

            # Show error dialog if tkinter is available
            try:
                root = tk.Tk()
                root.withdraw()  # Hide main window
                messagebox.showerror("Ошибка инициализации", f"Не удалось инициализировать приложение:\n\n{error_msg}")
                root.destroy()
            except:
                pass

            sys.exit(1)
    
    def run(self):
        """Run the application"""
        if self.app:
            print("Starting Restaurant Management System...")
            self.app.run()
        else:
            print("Application not initialized properly.")
            sys.exit(1)

def handle_exception(exc_type, exc_value, exc_traceback):
    """Global exception handler"""
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return

    print("=" * 60)
    print("UNHANDLED EXCEPTION OCCURRED")
    print("=" * 60)
    print(f"Exception Type: {exc_type.__name__}")
    print(f"Exception Value: {exc_value}")
    print("Traceback:")
    import traceback
    traceback.print_exception(exc_type, exc_value, exc_traceback)
    print("=" * 60)

    # Show error dialog
    try:
        import tkinter as tk
        from tkinter import messagebox
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("Критическая ошибка",
                           f"Произошла неожиданная ошибка:\n\n{exc_type.__name__}: {exc_value}\n\nПроверьте консоль для подробностей.")
        root.destroy()
    except:
        pass

def main():
    """Main function"""
    # Set global exception handler
    sys.excepthook = handle_exception

    print("=" * 60)
    print("Restaurant Accounting & Inventory Management System")
    print("=" * 60)
    print("Initializing application...")

    try:
        app = RestaurantManagementApp()
        app.run()
    except KeyboardInterrupt:
        print("\nApplication interrupted by user.")
    except Exception as e:
        print(f"Unexpected error in main: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    finally:
        print("Application closed.")

if __name__ == "__main__":
    main()
