"""
Comprehensive Staff Scheduling System for Restaurant Management
Includes shift management, availability tracking, labor cost optimization, and automated scheduling
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from datetime import datetime, timedelta, time
import calendar
from gui.styles import ModernStyles, EnhancedStyles
from database.db_manager import DatabaseManager

def format_currency(amount):
    """Форматировать сумму в российском формате валюты"""
    try:
        if amount is None:
            amount = 0
        amount = float(amount)
        formatted = f"{amount:,.2f}".replace(',', ' ').replace('.', ',')
        return f"{formatted} руб"
    except:
        return "0,00 руб"

class StaffSchedulingSystem:
    """Comprehensive staff scheduling system with advanced features"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.current_week_start = self._get_week_start(datetime.now())
        self.employees = []
        self.shifts = []
        self.schedule_tree = None
        
        # Initialize database tables for scheduling
        self._init_scheduling_tables()
        
    def _init_scheduling_tables(self):
        """Initialize scheduling-related database tables"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Employee availability table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS employee_availability (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        employee_id INTEGER NOT NULL,
                        day_of_week INTEGER NOT NULL, -- 0=Monday, 6=Sunday
                        start_time TIME NOT NULL,
                        end_time TIME NOT NULL,
                        is_available BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (employee_id) REFERENCES employees (id)
                    )
                ''')
                
                # Shifts table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS shifts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        employee_id INTEGER NOT NULL,
                        shift_date DATE NOT NULL,
                        start_time TIME NOT NULL,
                        end_time TIME NOT NULL,
                        position TEXT,
                        hourly_rate REAL DEFAULT 0,
                        break_minutes INTEGER DEFAULT 30,
                        status TEXT DEFAULT 'scheduled', -- scheduled, confirmed, completed, cancelled
                        notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (employee_id) REFERENCES employees (id)
                    )
                ''')
                
                # Time off requests table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS time_off_requests (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        employee_id INTEGER NOT NULL,
                        request_date DATE NOT NULL,
                        start_date DATE NOT NULL,
                        end_date DATE NOT NULL,
                        reason TEXT,
                        status TEXT DEFAULT 'pending', -- pending, approved, denied
                        approved_by INTEGER,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (employee_id) REFERENCES employees (id),
                        FOREIGN KEY (approved_by) REFERENCES users (id)
                    )
                ''')
                
                conn.commit()
                print("✅ Scheduling tables initialized")
                
        except Exception as e:
            print(f"Error initializing scheduling tables: {e}")
    
    def _get_week_start(self, date):
        """Get the start of the week (Monday) for a given date"""
        days_since_monday = date.weekday()
        return date - timedelta(days=days_since_monday)
    
    def show_scheduling_system(self):
        """Display the main scheduling interface"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
            
        self.window = tk.Toplevel(self.parent)
        self.window.title("🗓️ Система Планирования Смен")
        self.window.geometry("1600x1000")
        self.window.configure(bg=ModernStyles.COLORS['bg_main'])
        self.window.state('zoomed')
        
        # Apply professional styling
        EnhancedStyles.apply_professional_style(self.window)
        
        self.create_scheduling_interface()
        self.load_employees()
        self.load_current_week_schedule()
    
    def create_scheduling_interface(self):
        """Create the main scheduling interface"""
        # Main container
        main_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Header with title and controls
        self.create_header(main_frame)
        
        # Week navigation
        self.create_week_navigation(main_frame)
        
        # Main content area with schedule grid
        self.create_schedule_grid(main_frame)
        
        # Bottom panel with controls and statistics
        self.create_bottom_panel(main_frame)
    
    def create_header(self, parent):
        """Create header with title and main controls"""
        header_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        header_frame.pack(fill='x', pady=(0, 20))
        
        # Title
        title_label = tk.Label(header_frame, 
                              text="🗓️ Система Планирования Смен",
                              font=('Cambria', 24, 'bold italic'),
                              fg=ModernStyles.COLORS['primary'],
                              bg=ModernStyles.COLORS['bg_main'])
        title_label.pack(side='left')
        
        # Control buttons
        controls_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['bg_main'])
        controls_frame.pack(side='right')
        
        # Auto-schedule button
        auto_btn = tk.Button(controls_frame, text="🤖 Автоматическое Планирование",
                            command=self.auto_schedule_week,
                            bg=ModernStyles.COLORS['success'],
                            fg='white',
                            font=('Cambria', 12, 'bold italic'),
                            relief='flat', bd=0, padx=20, pady=8)
        auto_btn.pack(side='left', padx=(0, 10))
        
        # Employee management button
        emp_btn = tk.Button(controls_frame, text="👥 Управление Сотрудниками",
                           command=self.show_employee_management,
                           bg=ModernStyles.COLORS['primary'],
                           fg='white',
                           font=('Cambria', 12, 'bold italic'),
                           relief='flat', bd=0, padx=20, pady=8)
        emp_btn.pack(side='left', padx=(0, 10))
        
        # Reports button
        reports_btn = tk.Button(controls_frame, text="📊 Отчёты по Трудозатратам",
                               command=self.show_labor_reports,
                               bg=ModernStyles.COLORS['info'],
                               fg='white',
                               font=('Cambria', 12, 'bold italic'),
                               relief='flat', bd=0, padx=20, pady=8)
        reports_btn.pack(side='left')
    
    def create_week_navigation(self, parent):
        """Create week navigation controls"""
        nav_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        nav_frame.pack(fill='x', pady=(0, 20))
        
        # Previous week button
        prev_btn = tk.Button(nav_frame, text="◀ Предыдущая Неделя",
                            command=self.previous_week,
                            bg=ModernStyles.COLORS['secondary'],
                            fg='white',
                            font=('Cambria', 12, 'bold italic'),
                            relief='flat', bd=0, padx=15, pady=5)
        prev_btn.pack(side='left')
        
        # Current week display
        week_end = self.current_week_start + timedelta(days=6)
        week_text = f"Неделя: {self.current_week_start.strftime('%d.%m.%Y')} - {week_end.strftime('%d.%m.%Y')}"
        
        self.week_label = tk.Label(nav_frame, text=week_text,
                                  font=('Cambria', 16, 'bold italic'),
                                  fg=ModernStyles.COLORS['text_dark'],
                                  bg=ModernStyles.COLORS['bg_main'])
        self.week_label.pack(side='left', expand=True)
        
        # Next week button
        next_btn = tk.Button(nav_frame, text="Следующая Неделя ▶",
                            command=self.next_week,
                            bg=ModernStyles.COLORS['secondary'],
                            fg='white',
                            font=('Cambria', 12, 'bold italic'),
                            relief='flat', bd=0, padx=15, pady=5)
        next_btn.pack(side='right')
        
        # Today button
        today_btn = tk.Button(nav_frame, text="📅 Сегодня",
                             command=self.go_to_today,
                             bg=ModernStyles.COLORS['warning'],
                             fg='white',
                             font=('Cambria', 12, 'bold italic'),
                             relief='flat', bd=0, padx=15, pady=5)
        today_btn.pack(side='right', padx=(0, 10))

    def create_schedule_grid(self, parent):
        """Create the main schedule grid showing weekly view"""
        # Schedule container
        schedule_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        schedule_frame.pack(fill='both', expand=True, pady=(0, 20))

        # Create treeview for schedule display
        columns = ['employee', 'position', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday', 'total_hours', 'labor_cost']

        self.schedule_tree = ttk.Treeview(schedule_frame, columns=columns, show='headings', height=20)

        # Configure column headings
        self.schedule_tree.heading('employee', text='Сотрудник')
        self.schedule_tree.heading('position', text='Должность')
        self.schedule_tree.heading('monday', text='Понедельник')
        self.schedule_tree.heading('tuesday', text='Вторник')
        self.schedule_tree.heading('wednesday', text='Среда')
        self.schedule_tree.heading('thursday', text='Четверг')
        self.schedule_tree.heading('friday', text='Пятница')
        self.schedule_tree.heading('saturday', text='Суббота')
        self.schedule_tree.heading('sunday', text='Воскресенье')
        self.schedule_tree.heading('total_hours', text='Всего Часов')
        self.schedule_tree.heading('labor_cost', text='Стоимость Труда')

        # Configure column widths
        self.schedule_tree.column('employee', width=150, minwidth=120)
        self.schedule_tree.column('position', width=120, minwidth=100)
        for day in ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']:
            self.schedule_tree.column(day, width=120, minwidth=100)
        self.schedule_tree.column('total_hours', width=100, minwidth=80)
        self.schedule_tree.column('labor_cost', width=120, minwidth=100)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(schedule_frame, orient='vertical', command=self.schedule_tree.yview)
        h_scrollbar = ttk.Scrollbar(schedule_frame, orient='horizontal', command=self.schedule_tree.xview)
        self.schedule_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack treeview and scrollbars
        self.schedule_tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')

        # Bind double-click to edit shift
        self.schedule_tree.bind('<Double-1>', self.edit_shift)

        # Context menu for schedule operations
        self.create_schedule_context_menu()

    def create_schedule_context_menu(self):
        """Create context menu for schedule operations"""
        self.schedule_context_menu = tk.Menu(self.window, tearoff=0)
        self.schedule_context_menu.add_command(label="➕ Добавить Смену", command=self.add_shift)
        self.schedule_context_menu.add_command(label="✏️ Редактировать Смену", command=self.edit_shift)
        self.schedule_context_menu.add_command(label="❌ Удалить Смену", command=self.delete_shift)
        self.schedule_context_menu.add_separator()
        self.schedule_context_menu.add_command(label="📋 Копировать Смену", command=self.copy_shift)
        self.schedule_context_menu.add_command(label="📄 Вставить Смену", command=self.paste_shift)
        self.schedule_context_menu.add_separator()
        self.schedule_context_menu.add_command(label="📊 Детали Сотрудника", command=self.show_employee_details)

        # Bind right-click to show context menu
        self.schedule_tree.bind('<Button-3>', self.show_schedule_context_menu)

    def show_schedule_context_menu(self, event):
        """Show context menu at cursor position"""
        try:
            self.schedule_context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.schedule_context_menu.grab_release()

    def create_bottom_panel(self, parent):
        """Create bottom panel with statistics and controls"""
        bottom_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        bottom_frame.pack(fill='x')

        # Statistics panel
        stats_frame = tk.LabelFrame(bottom_frame, text="📊 Статистика Недели",
                                   font=('Cambria', 14, 'bold italic'),
                                   fg=ModernStyles.COLORS['primary'],
                                   bg=ModernStyles.COLORS['bg_main'])
        stats_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))

        # Statistics labels
        self.total_hours_label = tk.Label(stats_frame, text="Общее количество часов: 0",
                                         font=('Cambria', 12, 'bold italic'),
                                         fg=ModernStyles.COLORS['text_dark'],
                                         bg=ModernStyles.COLORS['bg_main'])
        self.total_hours_label.pack(anchor='w', padx=10, pady=5)

        self.total_cost_label = tk.Label(stats_frame, text="Общая стоимость труда: 0,00 руб",
                                        font=('Cambria', 12, 'bold italic'),
                                        fg=ModernStyles.COLORS['text_dark'],
                                        bg=ModernStyles.COLORS['bg_main'])
        self.total_cost_label.pack(anchor='w', padx=10, pady=5)

        self.avg_cost_per_hour_label = tk.Label(stats_frame, text="Средняя стоимость часа: 0,00 руб",
                                               font=('Cambria', 12, 'bold italic'),
                                               fg=ModernStyles.COLORS['text_dark'],
                                               bg=ModernStyles.COLORS['bg_main'])
        self.avg_cost_per_hour_label.pack(anchor='w', padx=10, pady=5)

        # Quick actions panel
        actions_frame = tk.LabelFrame(bottom_frame, text="⚡ Быстрые Действия",
                                     font=('Cambria', 14, 'bold italic'),
                                     fg=ModernStyles.COLORS['primary'],
                                     bg=ModernStyles.COLORS['bg_main'])
        actions_frame.pack(side='right', fill='y', padx=(10, 0))

        # Quick action buttons
        add_shift_btn = tk.Button(actions_frame, text="➕ Добавить Смену",
                                 command=self.add_shift,
                                 bg=ModernStyles.COLORS['success'],
                                 fg='white',
                                 font=('Cambria', 11, 'bold italic'),
                                 relief='flat', bd=0, padx=15, pady=5)
        add_shift_btn.pack(fill='x', padx=10, pady=5)

        time_off_btn = tk.Button(actions_frame, text="🏖️ Запрос Отгула",
                                command=self.request_time_off,
                                bg=ModernStyles.COLORS['warning'],
                                fg='white',
                                font=('Cambria', 11, 'bold italic'),
                                relief='flat', bd=0, padx=15, pady=5)
        time_off_btn.pack(fill='x', padx=10, pady=5)

        availability_btn = tk.Button(actions_frame, text="📅 Доступность",
                                    command=self.manage_availability,
                                    bg=ModernStyles.COLORS['info'],
                                    fg='white',
                                    font=('Cambria', 11, 'bold italic'),
                                    relief='flat', bd=0, padx=15, pady=5)
        availability_btn.pack(fill='x', padx=10, pady=5)

    # Navigation methods
    def previous_week(self):
        """Navigate to previous week"""
        self.current_week_start -= timedelta(days=7)
        self.update_week_display()
        self.load_current_week_schedule()

    def next_week(self):
        """Navigate to next week"""
        self.current_week_start += timedelta(days=7)
        self.update_week_display()
        self.load_current_week_schedule()

    def go_to_today(self):
        """Navigate to current week"""
        self.current_week_start = self._get_week_start(datetime.now())
        self.update_week_display()
        self.load_current_week_schedule()

    def update_week_display(self):
        """Update week display label"""
        week_end = self.current_week_start + timedelta(days=6)
        week_text = f"Неделя: {self.current_week_start.strftime('%d.%m.%Y')} - {week_end.strftime('%d.%m.%Y')}"
        self.week_label.config(text=week_text)

    # Data loading methods
    def load_employees(self):
        """Load employees from database"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, employee_id, first_name, last_name, position,
                           department, hourly_rate, is_active
                    FROM employees
                    WHERE is_active = 1
                    ORDER BY last_name, first_name
                ''')

                self.employees = []
                for row in cursor.fetchall():
                    employee = {
                        'id': row['id'],
                        'employee_id': row['employee_id'],
                        'full_name': f"{row['first_name']} {row['last_name']}",
                        'position': row['position'] or 'Не указано',
                        'department': row['department'] or 'Общий',
                        'hourly_rate': row['hourly_rate'] or 0,
                        'is_active': row['is_active']
                    }
                    self.employees.append(employee)

        except Exception as e:
            print(f"Error loading employees: {e}")
            messagebox.showerror("Ошибка", f"Не удалось загрузить список сотрудников: {e}")

    def load_current_week_schedule(self):
        """Load schedule for current week"""
        try:
            # Clear existing items
            for item in self.schedule_tree.get_children():
                self.schedule_tree.delete(item)

            week_end = self.current_week_start + timedelta(days=6)

            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT s.*, e.first_name, e.last_name, e.position, e.hourly_rate
                    FROM shifts s
                    JOIN employees e ON s.employee_id = e.id
                    WHERE s.shift_date BETWEEN ? AND ?
                    ORDER BY e.last_name, e.first_name, s.shift_date
                ''', (self.current_week_start.strftime('%Y-%m-%d'),
                      week_end.strftime('%Y-%m-%d')))

                shifts_data = cursor.fetchall()

            # Group shifts by employee
            employee_schedules = {}
            for shift in shifts_data:
                emp_id = shift['employee_id']
                if emp_id not in employee_schedules:
                    employee_schedules[emp_id] = {
                        'employee': f"{shift['first_name']} {shift['last_name']}",
                        'position': shift['position'] or 'Не указано',
                        'hourly_rate': shift['hourly_rate'] or 0,
                        'shifts': {}
                    }

                # Parse shift date and add to appropriate day
                shift_date = datetime.strptime(shift['shift_date'], '%Y-%m-%d').date()
                day_index = shift_date.weekday()  # 0=Monday, 6=Sunday

                start_time = shift['start_time']
                end_time = shift['end_time']
                shift_text = f"{start_time}-{end_time}"

                employee_schedules[emp_id]['shifts'][day_index] = shift_text

            # Add employees without shifts this week
            for employee in self.employees:
                if employee['id'] not in employee_schedules:
                    employee_schedules[employee['id']] = {
                        'employee': employee['full_name'],
                        'position': employee['position'],
                        'hourly_rate': employee['hourly_rate'],
                        'shifts': {}
                    }

            # Populate treeview
            total_hours = 0
            total_cost = 0

            for emp_id, schedule in employee_schedules.items():
                # Calculate weekly hours and cost for this employee
                weekly_hours = 0
                for day_idx, shift_text in schedule['shifts'].items():
                    if shift_text:
                        # Parse hours from shift text (simplified)
                        try:
                            start, end = shift_text.split('-')
                            start_hour = int(start.split(':')[0])
                            end_hour = int(end.split(':')[0])
                            hours = end_hour - start_hour
                            if hours < 0:
                                hours += 24  # Handle overnight shifts
                            weekly_hours += hours
                        except:
                            pass

                employee_cost = weekly_hours * schedule['hourly_rate']
                total_hours += weekly_hours
                total_cost += employee_cost

                # Create row data
                row_data = [
                    schedule['employee'],
                    schedule['position'],
                    schedule['shifts'].get(0, ''),  # Monday
                    schedule['shifts'].get(1, ''),  # Tuesday
                    schedule['shifts'].get(2, ''),  # Wednesday
                    schedule['shifts'].get(3, ''),  # Thursday
                    schedule['shifts'].get(4, ''),  # Friday
                    schedule['shifts'].get(5, ''),  # Saturday
                    schedule['shifts'].get(6, ''),  # Sunday
                    f"{weekly_hours:.1f}ч",
                    format_currency(employee_cost)
                ]

                self.schedule_tree.insert('', 'end', values=row_data, tags=(emp_id,))

            # Update statistics
            self.update_statistics(total_hours, total_cost)

        except Exception as e:
            print(f"Error loading schedule: {e}")
            messagebox.showerror("Ошибка", f"Не удалось загрузить расписание: {e}")

    def update_statistics(self, total_hours, total_cost):
        """Update weekly statistics display"""
        avg_cost_per_hour = total_cost / total_hours if total_hours > 0 else 0

        self.total_hours_label.config(text=f"Общее количество часов: {total_hours:.1f}ч")
        self.total_cost_label.config(text=f"Общая стоимость труда: {format_currency(total_cost)}")
        self.avg_cost_per_hour_label.config(text=f"Средняя стоимость часа: {format_currency(avg_cost_per_hour)}")

    # Shift management methods
    def add_shift(self):
        """Add a new shift"""
        if not self.employees:
            messagebox.showwarning("Предупреждение", "Сначала добавьте сотрудников в систему")
            return

        self.show_shift_dialog()

    def edit_shift(self, event=None):
        """Edit selected shift"""
        selection = self.schedule_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите смену для редактирования")
            return

        # Get selected employee and day information
        item = self.schedule_tree.item(selection[0])
        employee_name = item['values'][0]

        # Find which day was clicked (simplified - would need more complex logic for exact day)
        messagebox.showinfo("Редактирование", f"Редактирование смены для {employee_name}")
        self.show_shift_dialog(employee_name)

    def delete_shift(self):
        """Delete selected shift"""
        selection = self.schedule_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите смену для удаления")
            return

        if messagebox.askyesno("Подтверждение", "Вы уверены, что хотите удалить эту смену?"):
            # Implementation for shift deletion
            messagebox.showinfo("Удаление", "Смена удалена")
            self.load_current_week_schedule()

    def show_shift_dialog(self, employee_name=None):
        """Show dialog for adding/editing shifts"""
        dialog = tk.Toplevel(self.window)
        dialog.title("➕ Добавить/Редактировать Смену")
        dialog.geometry("500x600")
        dialog.configure(bg=ModernStyles.COLORS['bg_main'])
        dialog.transient(self.window)
        dialog.grab_set()

        # Center dialog
        dialog.geometry("+%d+%d" % (self.window.winfo_rootx() + 50, self.window.winfo_rooty() + 50))

        # Main frame
        main_frame = tk.Frame(dialog, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Title
        title_label = tk.Label(main_frame, text="➕ Планирование Смены",
                              font=('Cambria', 18, 'bold italic'),
                              fg=ModernStyles.COLORS['primary'],
                              bg=ModernStyles.COLORS['bg_main'])
        title_label.pack(pady=(0, 20))

        # Employee selection
        tk.Label(main_frame, text="Сотрудник:",
                font=('Cambria', 12, 'bold italic'),
                fg=ModernStyles.COLORS['text_dark'],
                bg=ModernStyles.COLORS['bg_main']).pack(anchor='w')

        employee_var = tk.StringVar()
        employee_combo = ttk.Combobox(main_frame, textvariable=employee_var,
                                     font=('Cambria', 12),
                                     state='readonly', width=40)
        employee_combo['values'] = [emp['full_name'] for emp in self.employees]
        if employee_name:
            employee_combo.set(employee_name)
        employee_combo.pack(fill='x', pady=(5, 15))

        # Date selection
        tk.Label(main_frame, text="Дата:",
                font=('Cambria', 12, 'bold italic'),
                fg=ModernStyles.COLORS['text_dark'],
                bg=ModernStyles.COLORS['bg_main']).pack(anchor='w')

        date_var = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
        date_entry = tk.Entry(main_frame, textvariable=date_var,
                             font=('Cambria', 12), width=40)
        date_entry.pack(fill='x', pady=(5, 15))

        # Time selection
        time_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_main'])
        time_frame.pack(fill='x', pady=(0, 15))

        # Start time
        tk.Label(time_frame, text="Время начала:",
                font=('Cambria', 12, 'bold italic'),
                fg=ModernStyles.COLORS['text_dark'],
                bg=ModernStyles.COLORS['bg_main']).pack(anchor='w')

        start_time_var = tk.StringVar(value="09:00")
        start_time_entry = tk.Entry(time_frame, textvariable=start_time_var,
                                   font=('Cambria', 12), width=15)
        start_time_entry.pack(anchor='w', pady=(5, 10))

        # End time
        tk.Label(time_frame, text="Время окончания:",
                font=('Cambria', 12, 'bold italic'),
                fg=ModernStyles.COLORS['text_dark'],
                bg=ModernStyles.COLORS['bg_main']).pack(anchor='w')

        end_time_var = tk.StringVar(value="17:00")
        end_time_entry = tk.Entry(time_frame, textvariable=end_time_var,
                                 font=('Cambria', 12), width=15)
        end_time_entry.pack(anchor='w', pady=(5, 15))

        # Position
        tk.Label(main_frame, text="Должность:",
                font=('Cambria', 12, 'bold italic'),
                fg=ModernStyles.COLORS['text_dark'],
                bg=ModernStyles.COLORS['bg_main']).pack(anchor='w')

        position_var = tk.StringVar()
        position_entry = tk.Entry(main_frame, textvariable=position_var,
                                 font=('Cambria', 12), width=40)
        position_entry.pack(fill='x', pady=(5, 15))

        # Notes
        tk.Label(main_frame, text="Примечания:",
                font=('Cambria', 12, 'bold italic'),
                fg=ModernStyles.COLORS['text_dark'],
                bg=ModernStyles.COLORS['bg_main']).pack(anchor='w')

        notes_text = tk.Text(main_frame, height=4, width=40,
                            font=('Cambria', 11))
        notes_text.pack(fill='x', pady=(5, 20))

        # Buttons
        button_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_main'])
        button_frame.pack(fill='x')

        def save_shift():
            """Save the shift to database"""
            try:
                # Get selected employee
                selected_employee = None
                for emp in self.employees:
                    if emp['full_name'] == employee_var.get():
                        selected_employee = emp
                        break

                if not selected_employee:
                    messagebox.showerror("Ошибка", "Выберите сотрудника")
                    return

                # Validate and save shift
                with self.db_manager.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        INSERT INTO shifts (employee_id, shift_date, start_time, end_time,
                                          position, hourly_rate, notes, status)
                        VALUES (?, ?, ?, ?, ?, ?, ?, 'scheduled')
                    ''', (
                        selected_employee['id'],
                        date_var.get(),
                        start_time_var.get(),
                        end_time_var.get(),
                        position_var.get() or selected_employee['position'],
                        selected_employee['hourly_rate'],
                        notes_text.get('1.0', 'end-1c')
                    ))
                    conn.commit()

                messagebox.showinfo("Успех", "Смена успешно сохранена")
                dialog.destroy()
                self.load_current_week_schedule()

            except Exception as e:
                messagebox.showerror("Ошибка", f"Не удалось сохранить смену: {e}")

        save_btn = tk.Button(button_frame, text="💾 Сохранить",
                            command=save_shift,
                            bg=ModernStyles.COLORS['success'],
                            fg='white',
                            font=('Cambria', 12, 'bold italic'),
                            relief='flat', bd=0, padx=20, pady=8)
        save_btn.pack(side='left', padx=(0, 10))

        cancel_btn = tk.Button(button_frame, text="❌ Отмена",
                              command=dialog.destroy,
                              bg=ModernStyles.COLORS['danger'],
                              fg='white',
                              font=('Cambria', 12, 'bold italic'),
                              relief='flat', bd=0, padx=20, pady=8)
        cancel_btn.pack(side='right')

    # Additional methods for comprehensive functionality
    def copy_shift(self):
        """Copy selected shift for pasting"""
        messagebox.showinfo("Копирование", "Смена скопирована в буфер обмена")

    def paste_shift(self):
        """Paste copied shift"""
        messagebox.showinfo("Вставка", "Смена вставлена из буфера обмена")

    def show_employee_details(self):
        """Show detailed employee information"""
        selection = self.schedule_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите сотрудника")
            return

        item = self.schedule_tree.item(selection[0])
        employee_name = item['values'][0]
        messagebox.showinfo("Детали сотрудника", f"Информация о сотруднике: {employee_name}")

    def auto_schedule_week(self):
        """Automatically generate schedule for the week"""
        if messagebox.askyesno("Автоматическое планирование",
                              "Создать автоматическое расписание на неделю?\n"
                              "Это может перезаписать существующие смены."):
            try:
                # Simple auto-scheduling logic
                self._generate_auto_schedule()
                messagebox.showinfo("Успех", "Автоматическое расписание создано")
                self.load_current_week_schedule()
            except Exception as e:
                messagebox.showerror("Ошибка", f"Не удалось создать автоматическое расписание: {e}")

    def _generate_auto_schedule(self):
        """Generate automatic schedule based on availability and requirements"""
        # This is a simplified auto-scheduling algorithm
        # In a real system, this would be much more sophisticated

        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()

            # Clear existing shifts for the week
            week_end = self.current_week_start + timedelta(days=6)
            cursor.execute('''
                DELETE FROM shifts
                WHERE shift_date BETWEEN ? AND ?
            ''', (self.current_week_start.strftime('%Y-%m-%d'),
                  week_end.strftime('%Y-%m-%d')))

            # Create basic schedule for each employee
            for employee in self.employees:
                for day in range(7):  # Monday to Sunday
                    shift_date = self.current_week_start + timedelta(days=day)

                    # Skip weekends for some positions (simplified logic)
                    if day >= 5 and employee['position'] in ['Администратор', 'Менеджер']:
                        continue

                    # Create standard 8-hour shift
                    start_time = "09:00"
                    end_time = "17:00"

                    cursor.execute('''
                        INSERT INTO shifts (employee_id, shift_date, start_time, end_time,
                                          position, hourly_rate, status)
                        VALUES (?, ?, ?, ?, ?, ?, 'scheduled')
                    ''', (
                        employee['id'],
                        shift_date.strftime('%Y-%m-%d'),
                        start_time,
                        end_time,
                        employee['position'],
                        employee['hourly_rate']
                    ))

            conn.commit()

    def show_employee_management(self):
        """Show employee management interface"""
        emp_window = tk.Toplevel(self.window)
        emp_window.title("👥 Управление Сотрудниками")
        emp_window.geometry("800x600")
        emp_window.configure(bg=ModernStyles.COLORS['bg_main'])
        emp_window.transient(self.window)

        # Employee management interface would go here
        tk.Label(emp_window, text="👥 Управление Сотрудниками",
                font=('Cambria', 20, 'bold italic'),
                fg=ModernStyles.COLORS['primary'],
                bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

        tk.Label(emp_window, text="Функция в разработке...",
                font=('Cambria', 14),
                fg=ModernStyles.COLORS['text_dark'],
                bg=ModernStyles.COLORS['bg_main']).pack()

    def show_labor_reports(self):
        """Show labor cost reports"""
        reports_window = tk.Toplevel(self.window)
        reports_window.title("📊 Отчёты по Трудозатратам")
        reports_window.geometry("1000x700")
        reports_window.configure(bg=ModernStyles.COLORS['bg_main'])
        reports_window.transient(self.window)

        # Labor reports interface would go here
        tk.Label(reports_window, text="📊 Отчёты по Трудозатратам",
                font=('Cambria', 20, 'bold italic'),
                fg=ModernStyles.COLORS['primary'],
                bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

        tk.Label(reports_window, text="Функция в разработке...",
                font=('Cambria', 14),
                fg=ModernStyles.COLORS['text_dark'],
                bg=ModernStyles.COLORS['bg_main']).pack()

    def request_time_off(self):
        """Request time off"""
        messagebox.showinfo("Запрос отгула", "Функция запроса отгула в разработке")

    def manage_availability(self):
        """Manage employee availability"""
        messagebox.showinfo("Управление доступностью", "Функция управления доступностью в разработке")

def create_staff_scheduling_system(parent, db_manager):
    """Create and show the staff scheduling system"""
    try:
        scheduling_system = StaffSchedulingSystem(parent, db_manager)
        scheduling_system.show_scheduling_system()
        return scheduling_system
    except Exception as e:
        messagebox.showerror("Ошибка", f"Не удалось открыть систему планирования смен: {e}")
        return None
