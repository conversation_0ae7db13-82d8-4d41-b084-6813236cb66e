"""
Advanced Analytics Engine with Machine Learning
Provides predictive analytics, trend analysis, and business intelligence for restaurant management
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import json
import logging
from dataclasses import dataclass
from enum import Enum
import warnings
warnings.filterwarnings('ignore')

# Simple ML implementations without external dependencies
class SimpleLinearRegression:
    """Simple linear regression implementation"""
    
    def __init__(self):
        self.slope = 0
        self.intercept = 0
        self.fitted = False
    
    def fit(self, X, y):
        """Fit the linear regression model"""
        if len(X) != len(y) or len(X) < 2:
            return
        
        X = np.array(X)
        y = np.array(y)
        
        # Calculate slope and intercept
        n = len(X)
        sum_x = np.sum(X)
        sum_y = np.sum(y)
        sum_xy = np.sum(X * y)
        sum_x2 = np.sum(X * X)
        
        denominator = n * sum_x2 - sum_x * sum_x
        if denominator != 0:
            self.slope = (n * sum_xy - sum_x * sum_y) / denominator
            self.intercept = (sum_y - self.slope * sum_x) / n
            self.fitted = True
    
    def predict(self, X):
        """Make predictions"""
        if not self.fitted:
            return [0] * len(X)
        return [self.slope * x + self.intercept for x in X]

class MovingAverage:
    """Moving average implementation for trend analysis"""
    
    @staticmethod
    def simple_moving_average(data, window):
        """Calculate simple moving average"""
        if len(data) < window:
            return data
        
        result = []
        for i in range(len(data)):
            if i < window - 1:
                result.append(data[i])
            else:
                avg = sum(data[i-window+1:i+1]) / window
                result.append(avg)
        return result
    
    @staticmethod
    def exponential_moving_average(data, alpha=0.3):
        """Calculate exponential moving average"""
        if not data:
            return []
        
        result = [data[0]]
        for i in range(1, len(data)):
            ema = alpha * data[i] + (1 - alpha) * result[-1]
            result.append(ema)
        return result

@dataclass
class PredictionResult:
    """Result of a prediction analysis"""
    prediction_type: str
    predicted_values: List[float]
    confidence_score: float
    trend_direction: str
    insights: List[str]
    recommendations: List[str]

@dataclass
class AnalyticsReport:
    """Comprehensive analytics report"""
    report_type: str
    period: str
    key_metrics: Dict[str, Any]
    trends: Dict[str, Any]
    predictions: Dict[str, PredictionResult]
    insights: List[str]
    recommendations: List[str]
    generated_at: datetime

class AnalyticsEngine:
    """Advanced Analytics Engine with ML capabilities"""
    
    def __init__(self, db_connection):
        self.db = db_connection
        self.logger = logging.getLogger(__name__)
        self.models = {}
        
    def analyze_sales_trends(self, days_back: int = 30) -> Dict[str, Any]:
        """Analyze sales trends and patterns"""
        try:
            # Get sales data
            query = """
            SELECT DATE(order_date) as date, 
                   SUM(CAST(REPLACE(REPLACE(total_amount, ' руб', ''), ',', '.') AS REAL)) as daily_total,
                   COUNT(*) as order_count
            FROM sales 
            WHERE order_date >= date('now', '-{} days')
            GROUP BY DATE(order_date)
            ORDER BY date
            """.format(days_back)
            
            cursor = self.db.cursor()
            cursor.execute(query)
            data = cursor.fetchall()
            
            if not data:
                return {"error": "No sales data available"}
            
            dates = [row[0] for row in data]
            amounts = [row[1] for row in data]
            orders = [row[2] for row in data]
            
            # Calculate trends
            trend_analysis = self._calculate_trend(amounts)
            seasonal_patterns = self._detect_seasonal_patterns(dates, amounts)
            
            # Generate predictions
            predictions = self._predict_sales(amounts, days_ahead=7)
            
            return {
                "period": f"Last {days_back} days",
                "total_sales": sum(amounts),
                "total_orders": sum(orders),
                "average_daily_sales": np.mean(amounts),
                "trend": trend_analysis,
                "seasonal_patterns": seasonal_patterns,
                "predictions": predictions,
                "insights": self._generate_sales_insights(amounts, orders, trend_analysis)
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing sales trends: {e}")
            return {"error": str(e)}
    
    def analyze_inventory_optimization(self) -> Dict[str, Any]:
        """Analyze inventory and provide optimization recommendations"""
        try:
            # Get inventory data
            query = """
            SELECT item_name, current_stock, minimum_stock, maximum_stock, 
                   unit_cost, supplier, category
            FROM inventory
            WHERE current_stock IS NOT NULL
            """
            
            cursor = self.db.cursor()
            cursor.execute(query)
            data = cursor.fetchall()
            
            if not data:
                return {"error": "No inventory data available"}
            
            # Analyze stock levels
            low_stock_items = []
            overstock_items = []
            optimal_items = []
            
            for row in data:
                name, current, minimum, maximum, cost, supplier, category = row
                
                if current <= minimum:
                    low_stock_items.append({
                        "item": name,
                        "current": current,
                        "minimum": minimum,
                        "shortage": minimum - current,
                        "category": category,
                        "supplier": supplier
                    })
                elif maximum and current >= maximum:
                    overstock_items.append({
                        "item": name,
                        "current": current,
                        "maximum": maximum,
                        "excess": current - maximum,
                        "category": category
                    })
                else:
                    optimal_items.append(name)
            
            # Calculate inventory value
            total_value = sum(row[1] * row[4] for row in data if row[1] and row[4])
            
            return {
                "total_items": len(data),
                "total_inventory_value": total_value,
                "low_stock_items": low_stock_items,
                "overstock_items": overstock_items,
                "optimal_stock_count": len(optimal_items),
                "recommendations": self._generate_inventory_recommendations(low_stock_items, overstock_items),
                "insights": [
                    f"Всего товаров в наличии: {len(data)}",
                    f"Товаров с низким запасом: {len(low_stock_items)}",
                    f"Товаров с избыточным запасом: {len(overstock_items)}",
                    f"Общая стоимость запасов: {total_value:,.2f} руб"
                ]
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing inventory: {e}")
            return {"error": str(e)}
    
    def analyze_customer_behavior(self) -> Dict[str, Any]:
        """Analyze customer behavior patterns"""
        try:
            # Get customer and sales data
            query = """
            SELECT c.name, c.phone, c.email, c.registration_date,
                   COUNT(s.id) as order_count,
                   SUM(CAST(REPLACE(REPLACE(s.total_amount, ' руб', ''), ',', '.') AS REAL)) as total_spent,
                   AVG(CAST(REPLACE(REPLACE(s.total_amount, ' руб', ''), ',', '.') AS REAL)) as avg_order_value
            FROM customers c
            LEFT JOIN sales s ON c.phone = s.customer_phone
            GROUP BY c.id
            ORDER BY total_spent DESC
            """
            
            cursor = self.db.cursor()
            cursor.execute(query)
            data = cursor.fetchall()
            
            if not data:
                return {"error": "No customer data available"}
            
            # Analyze customer segments
            high_value_customers = []
            regular_customers = []
            new_customers = []
            
            for row in data:
                name, phone, email, reg_date, orders, total, avg_order = row
                
                if total and total > 10000:  # High value threshold
                    high_value_customers.append({
                        "name": name,
                        "phone": phone,
                        "total_spent": total,
                        "order_count": orders,
                        "avg_order": avg_order
                    })
                elif orders and orders >= 5:  # Regular customer threshold
                    regular_customers.append({
                        "name": name,
                        "phone": phone,
                        "order_count": orders,
                        "total_spent": total or 0
                    })
                else:
                    new_customers.append({
                        "name": name,
                        "phone": phone,
                        "registration_date": reg_date
                    })
            
            return {
                "total_customers": len(data),
                "high_value_customers": high_value_customers[:10],  # Top 10
                "regular_customers_count": len(regular_customers),
                "new_customers_count": len(new_customers),
                "insights": [
                    f"Всего клиентов: {len(data)}",
                    f"VIP клиентов (>10,000 руб): {len(high_value_customers)}",
                    f"Постоянных клиентов (≥5 заказов): {len(regular_customers)}",
                    f"Новых клиентов: {len(new_customers)}"
                ],
                "recommendations": self._generate_customer_recommendations(
                    len(high_value_customers), len(regular_customers), len(new_customers)
                )
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing customer behavior: {e}")
            return {"error": str(e)}
    
    def _calculate_trend(self, data: List[float]) -> Dict[str, Any]:
        """Calculate trend direction and strength"""
        if len(data) < 2:
            return {"direction": "insufficient_data", "strength": 0}
        
        # Simple linear regression for trend
        X = list(range(len(data)))
        model = SimpleLinearRegression()
        model.fit(X, data)
        
        if model.slope > 0.1:
            direction = "increasing"
        elif model.slope < -0.1:
            direction = "decreasing"
        else:
            direction = "stable"
        
        strength = abs(model.slope) / (np.mean(data) + 1e-6)  # Avoid division by zero
        
        return {
            "direction": direction,
            "strength": min(strength, 1.0),  # Cap at 1.0
            "slope": model.slope
        }
    
    def _detect_seasonal_patterns(self, dates: List[str], values: List[float]) -> Dict[str, Any]:
        """Detect seasonal patterns in data"""
        if len(dates) < 7:
            return {"pattern": "insufficient_data"}
        
        # Group by day of week
        day_averages = {}
        for date_str, value in zip(dates, values):
            try:
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                day_name = date_obj.strftime('%A')
                
                if day_name not in day_averages:
                    day_averages[day_name] = []
                day_averages[day_name].append(value)
            except:
                continue
        
        # Calculate averages
        day_stats = {}
        for day, values_list in day_averages.items():
            day_stats[day] = {
                "average": np.mean(values_list),
                "count": len(values_list)
            }
        
        return {
            "pattern": "weekly",
            "day_statistics": day_stats,
            "insights": self._generate_seasonal_insights(day_stats)
        }
    
    def _predict_sales(self, historical_data: List[float], days_ahead: int = 7) -> Dict[str, Any]:
        """Predict future sales using simple methods"""
        if len(historical_data) < 3:
            return {"error": "Insufficient data for prediction"}
        
        # Moving average prediction
        ma_window = min(7, len(historical_data))
        recent_avg = np.mean(historical_data[-ma_window:])
        
        # Trend-based prediction
        X = list(range(len(historical_data)))
        model = SimpleLinearRegression()
        model.fit(X, historical_data)
        
        future_X = list(range(len(historical_data), len(historical_data) + days_ahead))
        trend_predictions = model.predict(future_X)
        
        # Combine predictions (weighted average)
        combined_predictions = []
        for trend_pred in trend_predictions:
            combined = 0.7 * recent_avg + 0.3 * trend_pred
            combined_predictions.append(max(0, combined))  # Ensure non-negative
        
        return {
            "method": "combined_trend_ma",
            "predictions": combined_predictions,
            "confidence": 0.7,  # Simple confidence estimate
            "period": f"Next {days_ahead} days"
        }
    
    def _generate_sales_insights(self, amounts: List[float], orders: List[int], trend: Dict) -> List[str]:
        """Generate insights from sales analysis"""
        insights = []
        
        avg_amount = np.mean(amounts)
        avg_orders = np.mean(orders)
        
        if trend["direction"] == "increasing":
            insights.append(f"📈 Продажи растут со средним трендом {trend['strength']:.2f}")
        elif trend["direction"] == "decreasing":
            insights.append(f"📉 Продажи снижаются со средним трендом {trend['strength']:.2f}")
        else:
            insights.append("📊 Продажи стабильны")
        
        insights.append(f"💰 Средняя дневная выручка: {avg_amount:,.2f} руб")
        insights.append(f"📋 Среднее количество заказов в день: {avg_orders:.1f}")
        
        return insights
    
    def _generate_inventory_recommendations(self, low_stock: List, overstock: List) -> List[str]:
        """Generate inventory optimization recommendations"""
        recommendations = []
        
        if low_stock:
            recommendations.append(f"🔴 Срочно пополнить запасы для {len(low_stock)} товаров")
            
        if overstock:
            recommendations.append(f"🟡 Рассмотреть акции для {len(overstock)} товаров с избытком")
            
        if not low_stock and not overstock:
            recommendations.append("✅ Уровни запасов оптимальны")
            
        return recommendations
    
    def _generate_customer_recommendations(self, high_value: int, regular: int, new: int) -> List[str]:
        """Generate customer engagement recommendations"""
        recommendations = []
        
        if high_value > 0:
            recommendations.append(f"👑 Создать VIP программу для {high_value} ценных клиентов")
            
        if regular > 0:
            recommendations.append(f"🎯 Программа лояльности для {regular} постоянных клиентов")
            
        if new > 0:
            recommendations.append(f"🆕 Приветственные акции для {new} новых клиентов")
            
        return recommendations
    
    def _generate_seasonal_insights(self, day_stats: Dict) -> List[str]:
        """Generate insights from seasonal patterns"""
        insights = []
        
        if not day_stats:
            return ["Недостаточно данных для анализа сезонности"]
        
        # Find best and worst days
        best_day = max(day_stats.keys(), key=lambda k: day_stats[k]["average"])
        worst_day = min(day_stats.keys(), key=lambda k: day_stats[k]["average"])
        
        insights.append(f"📅 Лучший день недели: {best_day}")
        insights.append(f"📅 Слабый день недели: {worst_day}")
        
        return insights
