"""
Login Window for Restaurant Accounting and Inventory Management System
"""

import tkinter as tk
from tkinter import messagebox
import hashlib
import ctypes
import ctypes.wintypes
import subprocess
import sys
import os
import threading
import time
from gui.styles import ModernStyles
from localization.russian import *

class LoginWindow:
    """Login window with authentication"""

    def __init__(self, db_manager, restaurant_info=None):
        self.db_manager = db_manager
        self.restaurant_info = restaurant_info
        self.authenticated_user = None
        self.root = None
        self.create_login_window()

    def switch_to_english_keyboard(self):
        """Switch keyboard layout to English"""
        try:
            # Попробуем несколько методов переключения на английскую раскладку

            # Метод 1: Использование LoadKeyboardLayout и ActivateKeyboardLayout
            user32 = ctypes.windll.user32

            # Различные идентификаторы английской раскладки
            english_layouts = [
                "00000409",  # Английская (US) раскладка
                "04090409",  # Английская (US) раскладка (полный код)
            ]

            # Попробуем загрузить и активировать английскую раскладку
            for layout_id in english_layouts:
                try:
                    # Загружаем раскладку
                    hkl = user32.LoadKeyboardLayoutW(layout_id, 0x00000001)  # KLF_ACTIVATE
                    if hkl:
                        # Активируем раскладку для текущего окна
                        hwnd = user32.GetForegroundWindow()
                        user32.ActivateKeyboardLayout(hkl, 0)
                        print(f"✅ Раскладка клавиатуры переключена на английский (метод 1, layout: {layout_id})")
                        return
                except:
                    continue

            # Метод 2: Использование PostMessage с WM_INPUTLANGCHANGEREQUEST
            HWND_BROADCAST = 0xFFFF
            WM_INPUTLANGCHANGEREQUEST = 0x0050

            # Различные коды английской раскладки
            english_codes = [
                0x04090409,  # Английская (US)
                0x00000409,  # Английская (короткий код)
            ]

            for code in english_codes:
                try:
                    user32.PostMessageW(HWND_BROADCAST, WM_INPUTLANGCHANGEREQUEST, 0, code)
                    print(f"✅ Раскладка клавиатуры переключена на английский (метод 2, код: {hex(code)})")
                    return
                except:
                    continue

            print("⚠️ Не удалось переключить раскладку клавиатуры на английский")

        except Exception as e:
            print(f"⚠️ Ошибка при переключении раскладки клавиатуры на английский: {e}")
            # Не показываем ошибку пользователю, так как это не критично

    def switch_to_russian_keyboard(self):
        """Радикальное переключение раскладки на русскую - все возможные методы"""
        try:
            user32 = ctypes.windll.user32
            kernel32 = ctypes.windll.kernel32

            print("🔄 РАДИКАЛЬНАЯ попытка переключения раскладки на русскую...")

            # МЕТОД 1: Прямое управление через Shell API
            try:
                print("   Метод 1: Shell API...")
                shell32 = ctypes.windll.shell32

                # Выполняем команду переключения раскладки через shell
                result = shell32.ShellExecuteW(
                    None,
                    "open",
                    "rundll32.exe",
                    "shell32.dll,Control_RunDLL input.dll,,{C07337D3-DB2C-4D0B-9A93-B722A6C106E2}",
                    None,
                    0  # SW_HIDE
                )
                if result > 32:  # Успех если > 32
                    print("   ✅ Shell API команда выполнена")
                    kernel32.Sleep(1000)  # Ждем применения
                    return True
            except Exception as e:
                print(f"   ❌ Shell API ошибка: {e}")

            # МЕТОД 2: Использование WM_CHAR для имитации переключения
            try:
                print("   Метод 2: WM_CHAR имитация...")

                # Получаем активное окно
                hwnd = user32.GetForegroundWindow()
                if hwnd:
                    # Отправляем специальные коды для переключения раскладки
                    user32.PostMessageW(hwnd, 0x0102, 0x0419, 0)  # WM_CHAR с кодом русской раскладки
                    user32.PostMessageW(0xFFFF, 0x0050, 0, 0x4190419)  # WM_INPUTLANGCHANGEREQUEST
                    print("   ✅ WM_CHAR сообщения отправлены")
                    return True
            except Exception as e:
                print(f"   ❌ WM_CHAR ошибка: {e}")

            # МЕТОД 3: Использование SetForegroundWindow + активация
            try:
                print("   Метод 3: SetForegroundWindow + активация...")

                # Получаем текущее окно
                hwnd = user32.GetForegroundWindow()

                # Устанавливаем фокус и активируем раскладку
                user32.SetForegroundWindow(hwnd)
                user32.SetFocus(hwnd)

                # Активируем русскую раскладку для этого окна
                russian_hkl = 0x4190419
                result = user32.ActivateKeyboardLayout(russian_hkl, 0x00000001)  # KLF_SETFORPROCESS

                if result:
                    print("   ✅ SetForegroundWindow + активация успешно")
                    return True
            except Exception as e:
                print(f"   ❌ SetForegroundWindow ошибка: {e}")

            # МЕТОД 4: Использование GetKeyboardLayoutList + циклическое переключение
            try:
                print("   Метод 4: Циклическое переключение через список раскладок...")

                # Получаем все доступные раскладки
                num_layouts = user32.GetKeyboardLayoutList(0, None)
                if num_layouts > 0:
                    layouts = (ctypes.wintypes.HKL * num_layouts)()
                    user32.GetKeyboardLayoutList(num_layouts, layouts)

                    # Ищем русскую раскладку и активируем её
                    for layout in layouts:
                        if (layout & 0xFFFF) == 0x0419:  # Русская раскладка
                            result = user32.ActivateKeyboardLayout(layout, 0x00000008)  # KLF_REORDER
                            if result:
                                print(f"   ✅ Циклическое переключение успешно: {hex(layout)}")
                                return True
            except Exception as e:
                print(f"   ❌ Циклическое переключение ошибка: {e}")

            # МЕТОД 5: Использование SetWindowsHookEx для перехвата
            try:
                print("   Метод 5: Windows Hook для переключения...")

                # Определяем функцию hook
                def keyboard_hook_proc(nCode, wParam, lParam):
                    if nCode >= 0:
                        # Принудительно устанавливаем русскую раскладку
                        user32.ActivateKeyboardLayout(0x4190419, 0)
                    return user32.CallNextHookEx(None, nCode, wParam, lParam)

                # Устанавливаем hook (кратковременно)
                HOOKPROC = ctypes.WINFUNCTYPE(ctypes.c_int, ctypes.c_int, ctypes.wintypes.WPARAM, ctypes.wintypes.LPARAM)
                hook_proc = HOOKPROC(keyboard_hook_proc)

                hook = user32.SetWindowsHookExW(2, hook_proc, kernel32.GetModuleHandleW(None), 0)  # WH_KEYBOARD
                if hook:
                    # Активируем раскладку через hook
                    user32.ActivateKeyboardLayout(0x4190419, 0)
                    kernel32.Sleep(100)
                    user32.UnhookWindowsHookEx(hook)
                    print("   ✅ Windows Hook метод выполнен")
                    return True
            except Exception as e:
                print(f"   ❌ Windows Hook ошибка: {e}")

            # МЕТОД 6: Прямая запись в память процесса (экстремальный метод)
            try:
                print("   Метод 6: Экстремальный - прямое изменение состояния...")

                # Получаем дескриптор текущего процесса
                process_handle = kernel32.GetCurrentProcess()

                # Пытаемся напрямую установить раскладку
                for attempt in range(5):
                    result = user32.ActivateKeyboardLayout(0x4190419, 0x00000100)  # KLF_SUBSTITUTE_OK
                    if result:
                        print(f"   ✅ Экстремальный метод успешен на попытке {attempt + 1}")
                        return True
                    kernel32.Sleep(200)

            except Exception as e:
                print(f"   ❌ Экстремальный метод ошибка: {e}")

            # МЕТОД 7: Использование COM интерфейса Windows
            try:
                print("   Метод 7: COM интерфейс...")
                import pythoncom
                import win32com.client

                # Создаем COM объект для управления системой
                shell = win32com.client.Dispatch("WScript.Shell")

                # Отправляем комбинацию Alt+Shift через COM
                shell.SendKeys("%+")  # Alt+Shift
                kernel32.Sleep(500)

                print("   ✅ COM интерфейс выполнен")
                return True

            except Exception as e:
                print(f"   ❌ COM интерфейс ошибка: {e}")

            # МЕТОД 8: Запуск PowerShell скрипта
            try:
                print("   Метод 8: PowerShell скрипт...")
                import subprocess
                import os

                # Путь к PowerShell скрипту
                script_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "switch_keyboard.ps1")

                if os.path.exists(script_path):
                    # Выполняем PowerShell скрипт
                    result = subprocess.run([
                        "powershell.exe",
                        "-ExecutionPolicy", "Bypass",
                        "-File", script_path
                    ], capture_output=True, text=True, timeout=30)

                    if result.returncode == 0:
                        print("   ✅ PowerShell скрипт выполнен успешно")
                        print(f"   📋 Вывод: {result.stdout[:200]}...")
                        return True
                    else:
                        print(f"   ❌ PowerShell скрипт ошибка: {result.stderr}")
                else:
                    print(f"   ❌ PowerShell скрипт не найден: {script_path}")

            except Exception as e:
                print(f"   ❌ PowerShell скрипт исключение: {e}")

            # МЕТОД 9: Множественные комбинации клавиш
            try:
                print("   Метод 9: Множественные комбинации клавиш...")

                # Пробуем разные комбинации переключения раскладки
                combinations = [
                    (0x12, 0x10),  # Alt + Shift
                    (0x11, 0x10),  # Ctrl + Shift
                    (0x5B, 0x20),  # Win + Space
                ]

                for alt_key, shift_key in combinations:
                    for attempt in range(2):  # 2 попытки для каждой комбинации
                        user32.keybd_event(alt_key, 0, 0, 0)      # Key down
                        user32.keybd_event(shift_key, 0, 0, 0)    # Key down
                        kernel32.Sleep(50)
                        user32.keybd_event(shift_key, 0, 2, 0)    # Key up
                        user32.keybd_event(alt_key, 0, 2, 0)      # Key up
                        kernel32.Sleep(200)

                print("   ✅ Множественные комбинации отправлены")

            except Exception as e:
                print(f"   ❌ Множественные комбинации ошибка: {e}")

            # ☢️ ЭКСТРЕМАЛЬНЫЙ МЕТОД 10: Запуск внешнего хука
            try:
                print("   ☢️ Метод 10: ЭКСТРЕМАЛЬНЫЙ ХУК...")
                import subprocess
                import sys
                import os

                if os.path.exists("extreme_keyboard_hook.py"):
                    # Запускаем хук в отдельном процессе на 3 секунды
                    import threading
                    def run_hook():
                        try:
                            subprocess.run([sys.executable, "extreme_keyboard_hook.py"],
                                         timeout=3, capture_output=True)
                        except:
                            pass

                    hook_thread = threading.Thread(target=run_hook, daemon=True)
                    hook_thread.start()
                    print("   ✅ Экстремальный хук запущен")
                    kernel32.Sleep(3000)  # Ждем 3 секунды
                else:
                    print("   ❌ Файл экстремального хука не найден")
            except Exception as e:
                print(f"   ❌ Экстремальный хук ошибка: {e}")

            # ☢️ ЭКСТРЕМАЛЬНЫЙ МЕТОД 11: Ядерный подход
            try:
                print("   ☢️ Метод 11: ЯДЕРНЫЙ ПОДХОД...")
                import subprocess
                import sys
                import os

                if os.path.exists("nuclear_keyboard_switch.py"):
                    # Запускаем ядерный метод
                    result = subprocess.run([sys.executable, "nuclear_keyboard_switch.py"],
                                          input="yes\n", text=True, timeout=10, capture_output=True)
                    if "УСПЕХ" in result.stdout:
                        print("   ✅ Ядерный подход успешен")
                        return True
                    else:
                        print("   ❌ Ядерный подход не удался")
                else:
                    print("   ❌ Файл ядерного подхода не найден")
            except Exception as e:
                print(f"   ❌ Ядерный подход ошибка: {e}")

            # ☢️ ЭКСТРЕМАЛЬНЫЙ МЕТОД 12: Создание драйвера
            try:
                print("   ☢️ Метод 12: СОЗДАНИЕ ДРАЙВЕРА КЛАВИАТУРЫ...")
                import subprocess
                import sys
                import os

                if os.path.exists("ultimate_driver_approach.py"):
                    # Запускаем создание драйвера
                    result = subprocess.run([sys.executable, "ultimate_driver_approach.py"],
                                          input="y\n", text=True, timeout=30, capture_output=True)
                    if "ДРАЙВЕР СОЗДАН" in result.stdout:
                        print("   🎉 ДРАЙВЕР СОЗДАН И РАБОТАЕТ!")
                        return True
                    else:
                        print("   ❌ Драйвер не создан")
                else:
                    print("   ❌ Файл драйвера не найден")
            except Exception as e:
                print(f"   ❌ Создание драйвера ошибка: {e}")

            print("   💀 ВСЕ 12 ЭКСТРЕМАЛЬНЫХ МЕТОДОВ НЕ СРАБОТАЛИ!")
            print("   Это окончательно подтверждает абсолютную защиту Windows")
            return False

        except Exception as e:
            print(f"⚠️ КРИТИЧЕСКАЯ ОШИБКА в радикальных методах: {e}")
            return False

    def check_final_keyboard_layout(self):
        """Финальная проверка раскладки клавиатуры с подробным уведомлением пользователя"""
        try:
            user32 = ctypes.windll.user32
            hwnd = user32.GetForegroundWindow()
            thread_id = user32.GetWindowThreadProcessId(hwnd, None)
            current_hkl = user32.GetKeyboardLayout(thread_id)
            current_id = current_hkl & 0xFFFF

            if current_id == 0x0419:
                print("🎉 УСПЕХ! Раскладка клавиатуры переключена на русскую!")
                print("✅ Теперь можно печатать русские буквы")
                # Показываем уведомление об успехе
                self.show_keyboard_notification(
                    "✅ Раскладка успешно переключена на русскую!\n\nТеперь вы можете вводить русский текст.",
                    "success"
                )
            else:
                print("❌ ВНИМАНИЕ: Раскладка клавиатуры все еще не русская")
                print(f"📋 Текущая раскладка: {hex(current_hkl)} (ID: {hex(current_id)})")
                print("💡 Попробуйте нажать Alt+Shift или Ctrl+Shift для ручного переключения")

                # Показываем подробное уведомление с инструкциями
                message = """🔄 ПЕРЕКЛЮЧЕНИЕ РАСКЛАДКИ КЛАВИАТУРЫ

⚠️ Автоматическое переключение на русскую раскладку не удалось.
Это связано с политиками безопасности Windows 10/11.

🎯 ДЛЯ РАБОТЫ С РУССКИМ ТЕКСТОМ:

1️⃣ Нажмите Alt + Shift (основной способ)
2️⃣ Или нажмите Ctrl + Shift
3️⃣ Или нажмите Win + Пробел

📌 ВАЖНО: После переключения раскладки все функции системы
будут работать корректно с русским текстом.

💡 Совет: Добавьте индикатор языка в панель задач Windows
для удобного переключения раскладок.

✅ Нажмите OK и переключите раскладку для продолжения работы."""

                self.show_keyboard_notification(message, "warning")

        except Exception as e:
            print(f"⚠️ Ошибка при проверке раскладки: {e}")
            # Показываем уведомление об ошибке
            self.show_keyboard_notification(
                "⚠️ Не удалось проверить раскладку клавиатуры.\n\nДля ввода русского текста нажмите Alt+Shift.",
                "warning"
            )

    def show_keyboard_notification(self, message, notification_type="info"):
        """Показать подробное уведомление о состоянии раскладки клавиатуры"""
        try:
            import tkinter.messagebox as msgbox

            title = "Раскладка клавиатуры"

            if notification_type == "success":
                msgbox.showinfo(title, message)
            elif notification_type == "warning":
                msgbox.showwarning(title, message)
            else:
                msgbox.showinfo(title, message)

        except Exception as e:
            print(f"⚠️ Ошибка при показе уведомления: {e}")
    
    def create_login_window(self):
        """Create the login window"""
        self.root = tk.Tk()
        self.root.title(f"{APP_TITLE} - {LOGIN_TITLE}")
        self.root.geometry("1400x1200")  # Ещё больше увеличили размер окна
        self.root.configure(bg=ModernStyles.COLORS['bg_main'])
        self.root.resizable(True, True)  # Разрешили изменение размера
        
        # Center the window
        self.center_window()
        
        # Main container
        main_frame = tk.Frame(self.root, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=80, pady=60)  # Немного уменьшили отступы
        
        # Logo/Title section
        title_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_main'])
        title_frame.pack(fill='x', pady=(0, 40))
        
        # System logo/icon (using text for now)
        logo_label = tk.Label(title_frame, text="🏪", font=('Arial', 72),  # Немного уменьшили логотип
                             bg=ModernStyles.COLORS['bg_main'],
                             fg=ModernStyles.COLORS['secondary'])
        logo_label.pack(pady=(0, 20))  # Уменьшили отступ снизу

        # System title
        title_label = tk.Label(title_frame, text=APP_TITLE,
                              font=('Cambria', 36, 'bold italic'),  # Уменьшили шрифт заголовка
                              fg='maroon',  # Изменили цвет на maroon
                              bg=ModernStyles.COLORS['bg_main'],
                              justify='center')
        title_label.pack(pady=(0, 15))  # Уменьшили отступ

        # Subtitle
        subtitle_label = tk.Label(title_frame, text=APP_SUBTITLE,
                                 font=('Cambria', 22, 'italic'),  # Уменьшили шрифт подзаголовка
                                 fg=ModernStyles.COLORS['text_secondary'],
                                 bg=ModernStyles.COLORS['bg_main'])
        subtitle_label.pack(pady=(0, 20))  # Уменьшили отступ

        # Restaurant info if available
        if self.restaurant_info:
            restaurant_frame = tk.Frame(title_frame, bg='#34495e', relief='solid', bd=3)  # Уменьшили границу
            restaurant_frame.pack(fill='x', pady=(25, 0))  # Уменьшили отступ

            tk.Label(restaurant_frame, text=f"🏪 {self.restaurant_info['name']}",
                    font=('Cambria', 24, 'bold italic'), bg='#34495e', fg='white').pack(pady=15)  # Уменьшили шрифт и отступы

            if self.restaurant_info.get('description'):
                tk.Label(restaurant_frame, text=self.restaurant_info['description'],
                        font=('Cambria', 18, 'italic'), bg='#34495e', fg='#bdc3c7').pack(pady=(0, 15))  # Уменьшили шрифт и отступы
        
        # Login form
        form_frame = ModernStyles.create_card_frame(main_frame)
        form_frame.pack(fill='both', expand=True, pady=(0, 30))  # Расширили форму

        # Login title
        login_title = tk.Label(form_frame, text=LOGIN_SUBTITLE,
                              font=('Cambria', 26, 'bold italic'),
                              fg='maroon',  # Изменили цвет на maroon
                              bg=ModernStyles.COLORS['bg_card'])
        login_title.pack(pady=(0, 30))

        # Username field
        username_label = tk.Label(form_frame, text=USERNAME_LABEL,
                                 font=('Cambria', 20, 'bold italic'),  # Уменьшили шрифт метки
                                 fg='maroon',  # Изменили цвет на maroon
                                 bg=ModernStyles.COLORS['bg_card'])
        username_label.pack(anchor='w', pady=(0, 12))  # Уменьшили отступ

        self.username_var = tk.StringVar()  # Пустое поле по умолчанию
        # Create entry style with smaller font
        self.username_entry = tk.Entry(form_frame, textvariable=self.username_var,
                                      font=('Cambria', 20, 'bold'),
                                      bg='white', fg='maroon',  # Изменили цвет текста на maroon
                                      relief='solid', bd=2,
                                      highlightthickness=2,
                                      highlightcolor='#3498db')
        self.username_entry.pack(fill='x', pady=(0, 20), ipady=10)
        # Фокус будет установлен в конце создания окна
        self.username_entry.bind('<Return>', lambda event: self.password_entry.focus())  # Enter переходит к паролю

        # Password field
        password_label = tk.Label(form_frame, text=PASSWORD_LABEL,
                                 font=('Cambria', 20, 'bold italic'),
                                 fg='maroon',  # Изменили цвет на maroon
                                 bg=ModernStyles.COLORS['bg_card'])
        password_label.pack(anchor='w', pady=(0, 12))

        self.password_var = tk.StringVar()  # Пустое поле по умолчанию
        self.password_entry = tk.Entry(form_frame, textvariable=self.password_var,
                                      font=('Cambria', 20, 'bold'),
                                      bg='white', fg='maroon',  # Изменили цвет текста на maroon
                                      relief='solid', bd=2,
                                      highlightthickness=2,
                                      highlightcolor='#3498db',
                                      show="*")  # Скрытие пароля
        self.password_entry.pack(fill='x', pady=(0, 30), ipady=10)
        self.password_entry.bind('<Return>', lambda event: self.authenticate())

        # КНОПКА ОК - РАЗМЕЩАЕМ СРАЗУ ПОСЛЕ ПОЛЕЙ
        print("Создаём кнопку ОК...")  # Отладка
        login_btn = tk.Button(form_frame, text="ОК",
                             command=self.authenticate,
                             font=('Cambria', 32, 'bold italic'),  # Очень большой шрифт
                             bg='#e74c3c', fg='white',  # Красный цвет для лучшей видимости
                             relief='raised', bd=5,  # Толстая граница
                             cursor='hand2',
                             width=30, height=4,  # Очень большой размер
                             activebackground='#c0392b',
                             activeforeground='white')
        login_btn.pack(pady=30, ipady=25)  # Большие отступы
        print(f"Кнопка ОК создана: {login_btn}")  # Отладка

        # Сообщение об ошибке (размещаем ПОСЛЕ кнопки)
        self.error_frame = tk.Frame(form_frame, bg=ModernStyles.COLORS['bg_card'])
        self.error_label = tk.Label(self.error_frame, text="",
                                   font=('Cambria', 18, 'bold italic'),
                                   fg='#e74c3c',
                                   bg=ModernStyles.COLORS['bg_card'],
                                   justify='center')
        self.error_label.pack(pady=15)

        # Скрыть сообщение об ошибке по умолчанию
        self.error_frame.pack_forget()

        # ОТДЕЛЬНЫЙ КОНТЕЙНЕР ДЛЯ КНОПКИ В САМОМ НИЗУ
        bottom_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_main'])
        bottom_frame.pack(fill='x', side='bottom', pady=30)

        # БОЛЬШАЯ КРАСНАЯ КНОПКА ОК
        big_ok_btn = tk.Button(bottom_frame, text="🔴 ОК 🔴",
                              command=self.authenticate,
                              font=('Cambria', 36, 'bold italic'),
                              bg='#e74c3c', fg='white',
                              relief='raised', bd=8,
                              cursor='hand2',
                              width=20, height=3)
        big_ok_btn.pack(pady=20, ipady=30)
        print("Большая красная кнопка ОК создана!")

        # Bind Enter key to login
        self.root.bind('<Return>', lambda event: self.authenticate())

        # Bind Escape key to close window and switch back to Russian
        self.root.bind('<Escape>', lambda event: self.close_window())

        # Bind window close event to switch back to Russian
        self.root.protocol("WM_DELETE_WINDOW", self.close_window)

        # Принудительное обновление окна
        self.root.update_idletasks()
        self.root.update()

        # Переключить раскладку клавиатуры на английский
        self.switch_to_english_keyboard()

        # Установить фокус на поле имени пользователя
        self.set_username_focus()

        # Установить фокус на поле пользователя с несколькими попытками
        self.set_username_focus()
        print("Окно входа создано и обновлено")  # Отладка

    def set_username_focus(self):
        """Установить фокус на поле имени пользователя с гарантией"""
        try:
            # Немедленная установка фокуса
            self.username_entry.focus_set()
            self.username_entry.focus_force()

            # Дополнительные попытки с задержкой
            self.root.after(50, lambda: self.username_entry.focus_set())
            self.root.after(100, lambda: self.username_entry.focus_force())
            self.root.after(200, lambda: self.username_entry.focus_set())

            # Убедиться, что окно активно
            self.root.lift()
            self.root.attributes('-topmost', True)
            self.root.after(300, lambda: self.root.attributes('-topmost', False))

            print("Фокус установлен на поле имени пользователя")
        except Exception as e:
            print(f"Ошибка установки фокуса: {e}")
    
    def center_window(self):
        """Center the login window on screen"""
        self.root.update_idletasks()
        width = 1400  # Ещё больше увеличили ширину
        height = 1200  # Ещё больше увеличили высоту
        
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def hash_password(self, password):
        """Hash password for security"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def authenticate(self):
        """Authenticate user credentials"""
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()
        
        # Скрыть предыдущие сообщения об ошибках
        self.hide_error()

        if not username or not password:
            self.show_error("Пожалуйста, введите имя пользователя и пароль")
            return
        
        try:
            # Check credentials against database
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, username, password_hash, role, full_name, is_active
                    FROM users 
                    WHERE username = ? AND is_active = 1
                ''', (username,))
                
                user = cursor.fetchone()
                
                if user:
                    stored_password = user[2]
                    # For default admin, check plain text, otherwise check hash
                    if (username == "admin" and password == "admin") or \
                       (stored_password == self.hash_password(password)):
                        
                        self.authenticated_user = {
                            'id': user[0],
                            'username': user[1],
                            'role': user[3],
                            'full_name': user[4] or username,
                            'is_active': user[5]
                        }

                        # Переключить раскладку клавиатуры обратно на русскую с расширенными попытками
                        print("🔄 Запуск процедуры переключения раскладки на русскую после входа...")
                        self.root.after(50, self.switch_to_russian_keyboard)    # Первая попытка (быстро)
                        self.root.after(200, self.switch_to_russian_keyboard)   # Вторая попытка
                        self.root.after(500, self.switch_to_russian_keyboard)   # Третья попытка
                        self.root.after(1000, self.switch_to_russian_keyboard)  # Четвертая попытка
                        self.root.after(2000, self.switch_to_russian_keyboard)  # Пятая попытка (через 2 сек)
                        self.root.after(3000, self.switch_to_russian_keyboard)  # Шестая попытка (через 3 сек)

                        # Добавляем финальную проверку
                        self.root.after(4000, self.check_final_keyboard_layout)

                        # Успешный вход - закрыть окно без сообщения
                        self.root.after(200, self._close_after_login)
                        return True
                    else:
                        self.show_error("Неверный пароль")
                else:
                    self.show_error("Неверное имя пользователя")

        except Exception as e:
            self.show_error(f"Ошибка подключения к базе данных")

        # Clear password field on failed login
        self.password_var.set("")
        self.set_username_focus()
        return False

    def show_error(self, message):
        """Показать сообщение об ошибке"""
        self.error_label.config(text=message)
        # Показать сообщение об ошибке
        self.error_frame.pack(fill='x', pady=(15, 10))
        # Установить фокус на поле пользователя при ошибке
        self.root.after(100, self.set_username_focus)

    def hide_error(self):
        """Скрыть сообщение об ошибке"""
        self.error_frame.pack_forget()

    def _close_after_login(self):
        """Close window after successful login"""
        try:
            if self.root:
                self.root.quit()
                self.root.destroy()
        except Exception as e:
            print(f"Ошибка при закрытии окна после входа: {e}")

    def close_window(self):
        """Close window and switch keyboard back to Russian"""
        try:
            # Переключить раскладку клавиатуры обратно на русскую с несколькими попытками
            self.switch_to_russian_keyboard()  # Немедленная попытка

            # Дополнительные попытки с задержкой
            if self.root:
                self.root.after(100, self.switch_to_russian_keyboard)  # Первая попытка
                self.root.after(300, self.switch_to_russian_keyboard)  # Вторая попытка

            # Закрыть окно
            if self.root:
                self.root.quit()
                self.root.destroy()
        except Exception as e:
            print(f"Ошибка при закрытии окна: {e}")

    def run(self):
        """Run the login window"""
        self.root.mainloop()
        return self.authenticated_user

class ChangePasswordDialog:
    """Dialog for changing user password"""
    
    def __init__(self, parent, db_manager, user_id):
        self.parent = parent
        self.db_manager = db_manager
        self.user_id = user_id
        self.result = False
        self.create_dialog()
    
    def create_dialog(self):
        """Create password change dialog"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("Изменить Пароль")
        self.dialog.geometry("500x400")  # Увеличен размер для показа всех полей
        self.dialog.configure(bg=ModernStyles.COLORS['bg_main'])
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        self.dialog.resizable(True, True)  # Разрешить изменение размера

        # Центрировать диалог
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")
        
        # Main frame
        main_frame = tk.Frame(self.dialog, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Title
        title_label = tk.Label(main_frame, text=CHANGE_PASSWORD,
                              **ModernStyles.WIDGET_STYLES['label_heading'])
        title_label.pack(pady=(0, 20))

        # Current password
        tk.Label(main_frame, text="Текущий пароль:",
                **ModernStyles.WIDGET_STYLES['label_body']).pack(anchor='w')

        self.current_password_var = tk.StringVar()
        tk.Entry(main_frame, textvariable=self.current_password_var, show="*",
                **ModernStyles.WIDGET_STYLES['entry']).pack(fill='x', pady=(5, 15))

        # New password
        tk.Label(main_frame, text="Новый пароль:",
                **ModernStyles.WIDGET_STYLES['label_body']).pack(anchor='w')

        self.new_password_var = tk.StringVar()
        tk.Entry(main_frame, textvariable=self.new_password_var, show="*",
                **ModernStyles.WIDGET_STYLES['entry']).pack(fill='x', pady=(5, 15))

        # Confirm password
        tk.Label(main_frame, text="Подтвердите новый пароль:",
                **ModernStyles.WIDGET_STYLES['label_body']).pack(anchor='w')

        self.confirm_password_var = tk.StringVar()
        tk.Entry(main_frame, textvariable=self.confirm_password_var, show="*",
                **ModernStyles.WIDGET_STYLES['entry']).pack(fill='x', pady=(5, 20))
        
        # Buttons
        button_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_main'])
        button_frame.pack(fill='x')
        
        tk.Button(button_frame, text="Изменить Пароль", command=self.change_password,
                 **ModernStyles.WIDGET_STYLES['button_success']).pack(side='right')

        tk.Button(button_frame, text="Отмена", command=self.dialog.destroy,
                 **ModernStyles.WIDGET_STYLES['button_secondary']).pack(side='right', padx=(0, 10))
    
    def change_password(self):
        """Change user password"""
        current = self.current_password_var.get()
        new_password = self.new_password_var.get()
        confirm = self.confirm_password_var.get()
        
        if not all([current, new_password, confirm]):
            messagebox.showerror("Error", "Please fill in all fields.")
            return
        
        if new_password != confirm:
            messagebox.showerror("Error", "New passwords do not match.")
            return
        
        if len(new_password) < 6:
            messagebox.showerror("Error", "Password must be at least 6 characters long.")
            return
        
        try:
            # Verify current password and update
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Get current password hash
                cursor.execute("SELECT password_hash FROM users WHERE id = ?", (self.user_id,))
                stored_hash = cursor.fetchone()[0]
                
                # Verify current password
                if stored_hash != self.hash_password(current):
                    messagebox.showerror("Error", "Current password is incorrect.")
                    return
                
                # Update password
                new_hash = self.hash_password(new_password)
                cursor.execute("UPDATE users SET password_hash = ? WHERE id = ?", 
                             (new_hash, self.user_id))
                conn.commit()
                
                messagebox.showinfo("Success", "Password changed successfully!")
                self.result = True
                self.dialog.destroy()
        
        except Exception as e:
            messagebox.showerror("Error", f"Failed to change password: {str(e)}")
    
    def hash_password(self, password):
        """Hash password for security"""
        return hashlib.sha256(password.encode()).hexdigest()
