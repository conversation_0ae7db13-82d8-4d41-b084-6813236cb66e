"""
Loyalty Programs and Rewards System
Comprehensive loyalty management module with points system, tier-based rewards,
promotional campaigns, referral bonuses, and automated reward distribution.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from datetime import datetime, timedelta
import json
import uuid
from gui.styles import ModernStyles

class LoyaltyRewardsSystem:
    """Comprehensive Loyalty Programs and Rewards Management System"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.notebook = None
        
        # Loyalty program configuration
        self.loyalty_config = {
            "points_per_ruble": 1,
            "welcome_bonus_points": 500,
            "birthday_bonus_points": 1000,
            "referral_bonus_points": 2000,
            "review_bonus_points": 100,
            "social_share_bonus_points": 50,
            "points_expiry_months": 12,
            "min_redemption_points": 100,
            "point_value_rubles": 0.5
        }
        
        # Loyalty tiers
        self.loyalty_tiers = {
            "bronze": {
                "name": "Бронзовый",
                "min_points": 0,
                "max_points": 2999,
                "color": "#CD7F32",
                "benefits": ["1 балл за 1 рубль", "Скидка 5% в день рождения"],
                "multiplier": 1.0
            },
            "silver": {
                "name": "Серебряный", 
                "min_points": 3000,
                "max_points": 9999,
                "color": "#C0C0C0",
                "benefits": ["1.2 балла за 1 рубль", "Скидка 10% в день рождения", "Приоритетное бронирование"],
                "multiplier": 1.2
            },
            "gold": {
                "name": "Золотой",
                "min_points": 10000,
                "max_points": 24999,
                "color": "#FFD700",
                "benefits": ["1.5 балла за 1 рубль", "Скидка 15% в день рождения", "Бесплатный десерт", "VIP поддержка"],
                "multiplier": 1.5
            },
            "platinum": {
                "name": "Платиновый",
                "min_points": 25000,
                "max_points": 999999,
                "color": "#E5E4E2",
                "benefits": ["2 балла за 1 рубль", "Скидка 20% в день рождения", "Бесплатный напиток", "Персональный менеджер"],
                "multiplier": 2.0
            }
        }
        
        # Reward types
        self.reward_types = {
            "discount_percent": "Процентная скидка",
            "discount_amount": "Фиксированная скидка",
            "free_item": "Бесплатное блюдо",
            "free_drink": "Бесплатный напиток",
            "free_dessert": "Бесплатный десерт",
            "upgrade": "Улучшение заказа",
            "priority_seating": "Приоритетная посадка",
            "special_menu": "Доступ к специальному меню"
        }
        
        # Campaign types
        self.campaign_types = {
            "points_multiplier": "Множитель баллов",
            "bonus_points": "Бонусные баллы",
            "tier_upgrade": "Повышение уровня",
            "special_offer": "Специальное предложение",
            "referral_bonus": "Реферальный бонус",
            "seasonal": "Сезонная акция"
        }
        
        # Initialize database tables
        self._init_loyalty_tables()
    
    def _init_loyalty_tables(self):
        """Initialize loyalty-related database tables"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Loyalty programs table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS loyalty_programs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        program_name TEXT NOT NULL,
                        program_type TEXT NOT NULL,
                        description TEXT,
                        points_per_ruble DECIMAL(5,2) DEFAULT 1.0,
                        welcome_bonus INTEGER DEFAULT 0,
                        birthday_bonus INTEGER DEFAULT 0,
                        referral_bonus INTEGER DEFAULT 0,
                        min_redemption_points INTEGER DEFAULT 100,
                        point_value_rubles DECIMAL(5,2) DEFAULT 0.5,
                        points_expiry_months INTEGER DEFAULT 12,
                        is_active BOOLEAN DEFAULT 1,
                        start_date DATE,
                        end_date DATE,
                        created_by INTEGER,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (created_by) REFERENCES users (id)
                    )
                ''')
                
                # Customer loyalty accounts table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS customer_loyalty_accounts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        customer_id INTEGER NOT NULL,
                        program_id INTEGER NOT NULL,
                        loyalty_tier TEXT DEFAULT 'bronze',
                        total_points_earned INTEGER DEFAULT 0,
                        current_points_balance INTEGER DEFAULT 0,
                        points_redeemed INTEGER DEFAULT 0,
                        tier_points INTEGER DEFAULT 0,
                        join_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_activity_date TIMESTAMP,
                        tier_upgrade_date TIMESTAMP,
                        next_tier_points_needed INTEGER DEFAULT 0,
                        lifetime_value DECIMAL(10,2) DEFAULT 0.00,
                        referral_code TEXT UNIQUE,
                        referred_by INTEGER,
                        referrals_count INTEGER DEFAULT 0,
                        is_active BOOLEAN DEFAULT 1,
                        FOREIGN KEY (customer_id) REFERENCES customers (id),
                        FOREIGN KEY (program_id) REFERENCES loyalty_programs (id),
                        FOREIGN KEY (referred_by) REFERENCES customer_loyalty_accounts (id)
                    )
                ''')
                
                # Points transactions table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS loyalty_points_transactions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        customer_loyalty_id INTEGER NOT NULL,
                        transaction_type TEXT NOT NULL,
                        points_amount INTEGER NOT NULL,
                        transaction_reason TEXT,
                        order_id INTEGER,
                        campaign_id INTEGER,
                        expiry_date DATE,
                        transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        processed_by INTEGER,
                        notes TEXT,
                        FOREIGN KEY (customer_loyalty_id) REFERENCES customer_loyalty_accounts (id),
                        FOREIGN KEY (order_id) REFERENCES orders (id),
                        FOREIGN KEY (campaign_id) REFERENCES loyalty_campaigns (id),
                        FOREIGN KEY (processed_by) REFERENCES users (id)
                    )
                ''')
                
                # Rewards catalog table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS loyalty_rewards_catalog (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        reward_name TEXT NOT NULL,
                        reward_type TEXT NOT NULL,
                        description TEXT,
                        points_cost INTEGER NOT NULL,
                        discount_percent DECIMAL(5,2),
                        discount_amount DECIMAL(10,2),
                        free_item_id INTEGER,
                        min_order_amount DECIMAL(10,2),
                        max_uses_per_customer INTEGER DEFAULT 1,
                        tier_requirement TEXT,
                        validity_days INTEGER DEFAULT 30,
                        is_active BOOLEAN DEFAULT 1,
                        start_date DATE,
                        end_date DATE,
                        image_url TEXT,
                        terms_conditions TEXT,
                        created_by INTEGER,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (free_item_id) REFERENCES menu_items (id),
                        FOREIGN KEY (created_by) REFERENCES users (id)
                    )
                ''')
                
                # Reward redemptions table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS loyalty_reward_redemptions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        customer_loyalty_id INTEGER NOT NULL,
                        reward_id INTEGER NOT NULL,
                        points_used INTEGER NOT NULL,
                        redemption_code TEXT UNIQUE,
                        redemption_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        expiry_date DATE,
                        order_id INTEGER,
                        used_date TIMESTAMP,
                        status TEXT DEFAULT 'active',
                        discount_applied DECIMAL(10,2),
                        notes TEXT,
                        FOREIGN KEY (customer_loyalty_id) REFERENCES customer_loyalty_accounts (id),
                        FOREIGN KEY (reward_id) REFERENCES loyalty_rewards_catalog (id),
                        FOREIGN KEY (order_id) REFERENCES orders (id)
                    )
                ''')
                
                # Loyalty campaigns table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS loyalty_campaigns (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        campaign_name TEXT NOT NULL,
                        campaign_type TEXT NOT NULL,
                        description TEXT,
                        target_tier TEXT,
                        points_multiplier DECIMAL(5,2) DEFAULT 1.0,
                        bonus_points INTEGER DEFAULT 0,
                        min_purchase_amount DECIMAL(10,2),
                        max_bonus_per_customer INTEGER,
                        start_date DATE NOT NULL,
                        end_date DATE NOT NULL,
                        is_active BOOLEAN DEFAULT 1,
                        participants_count INTEGER DEFAULT 0,
                        total_points_awarded INTEGER DEFAULT 0,
                        total_revenue_generated DECIMAL(10,2) DEFAULT 0.00,
                        created_by INTEGER,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (created_by) REFERENCES users (id)
                    )
                ''')
                
                # Referral tracking table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS loyalty_referrals (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        referrer_loyalty_id INTEGER NOT NULL,
                        referee_customer_id INTEGER NOT NULL,
                        referral_code TEXT NOT NULL,
                        referral_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        first_purchase_date TIMESTAMP,
                        referrer_bonus_points INTEGER DEFAULT 0,
                        referee_bonus_points INTEGER DEFAULT 0,
                        status TEXT DEFAULT 'pending',
                        bonus_awarded_date TIMESTAMP,
                        FOREIGN KEY (referrer_loyalty_id) REFERENCES customer_loyalty_accounts (id),
                        FOREIGN KEY (referee_customer_id) REFERENCES customers (id)
                    )
                ''')
                
                conn.commit()
                print("Loyalty system database tables initialized successfully")
                
        except Exception as e:
            print(f"Error initializing loyalty tables: {e}")
    
    def show_loyalty_system(self):
        """Show loyalty system window"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
            
        self.create_window()
        self.create_interface()
        self.load_loyalty_data()
    
    def create_window(self):
        """Create the loyalty system window"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("🎁 Система Лояльности и Вознаграждений")
        self.window.geometry("1600x1000")
        self.window.configure(bg=ModernStyles.COLORS['bg_main'])
        self.window.resizable(True, True)
        self.window.state('zoomed')
        
        # Center window
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # Configure styles
        ModernStyles.configure_ttk_styles(self.window)
    
    def create_interface(self):
        """Create the loyalty system interface"""
        # Main container
        main_container = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        main_container.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Header
        header_frame = tk.Frame(main_container, bg=ModernStyles.COLORS['bg_main'])
        header_frame.pack(fill='x', pady=(0, 20))
        
        title_label = tk.Label(header_frame, text="🎁 Система Лояльности и Вознаграждений",
                              font=('Cambria', 24, 'bold italic'),
                              fg=ModernStyles.COLORS['text_primary'],
                              bg=ModernStyles.COLORS['bg_main'])
        title_label.pack(side='left')
        
        # Quick stats
        self.stats_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['bg_main'])
        self.stats_frame.pack(side='right')
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_container)
        self.notebook.pack(fill='both', expand=True)
        
        # Create tabs
        self.create_loyalty_tiers_tab()
        self.create_rewards_catalog_tab()
        self.create_points_management_tab()
        self.create_campaigns_tab()
        self.create_referrals_tab()
        self.create_analytics_tab()

    def create_loyalty_tiers_tab(self):
        """Create loyalty tiers management tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="🏆 Уровни Лояльности")

        # Tiers overview
        tiers_header_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        tiers_header_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(tiers_header_frame, text="🏆 Уровни Программы Лояльности",
                font=('Cambria', 16, 'bold'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_secondary']).pack(pady=15)

        # Tier cards container
        tiers_container = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        tiers_container.pack(fill='both', expand=True, padx=20, pady=10)

        # Create tier cards
        self.create_tier_cards(tiers_container)

        # Tier management buttons
        tier_buttons_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        tier_buttons_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(tier_buttons_frame, text="⚙️ Настроить Уровни",
                 command=self.configure_tiers,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(tier_buttons_frame, text="📊 Анализ Уровней",
                 command=self.analyze_tiers,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(tier_buttons_frame, text="⬆️ Массовое Повышение",
                 command=self.bulk_tier_upgrade,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

    def create_tier_cards(self, parent):
        """Create loyalty tier cards"""
        row = 0
        col = 0

        for tier_key, tier_info in self.loyalty_tiers.items():
            # Tier card frame
            card_frame = tk.Frame(parent, bg=tier_info['color'], relief='raised', bd=3)
            card_frame.grid(row=row, column=col, padx=15, pady=15, sticky='nsew')

            # Tier name and icon
            tier_header = tk.Frame(card_frame, bg=tier_info['color'])
            tier_header.pack(fill='x', pady=(15, 10))

            tier_icons = {"bronze": "🥉", "silver": "🥈", "gold": "🥇", "platinum": "💎"}
            icon = tier_icons.get(tier_key, "🏆")

            tk.Label(tier_header, text=f"{icon} {tier_info['name']}",
                    font=('Cambria', 16, 'bold'),
                    fg='white', bg=tier_info['color']).pack()

            # Points range
            points_range = f"{tier_info['min_points']:,} - {tier_info['max_points']:,} баллов"
            if tier_info['max_points'] == 999999:
                points_range = f"от {tier_info['min_points']:,} баллов"

            tk.Label(card_frame, text=points_range,
                    font=('Cambria', 12, 'bold'),
                    fg='white', bg=tier_info['color']).pack(pady=(0, 10))

            # Benefits
            benefits_frame = tk.Frame(card_frame, bg=tier_info['color'])
            benefits_frame.pack(fill='both', expand=True, padx=15, pady=(0, 15))

            tk.Label(benefits_frame, text="Преимущества:",
                    font=('Cambria', 12, 'bold'),
                    fg='white', bg=tier_info['color']).pack(anchor='w')

            for benefit in tier_info['benefits']:
                tk.Label(benefits_frame, text=f"• {benefit}",
                        font=('Cambria', 10),
                        fg='white', bg=tier_info['color'],
                        wraplength=200, justify='left').pack(anchor='w', pady=2)

            # Customer count (placeholder)
            tk.Label(card_frame, text="Клиентов: 0",  # Will be updated with real data
                    font=('Cambria', 11, 'bold'),
                    fg='white', bg=tier_info['color']).pack(pady=(10, 15))

            col += 1
            if col > 1:
                col = 0
                row += 1

        # Configure grid weights
        for i in range(2):
            parent.columnconfigure(i, weight=1)
        for i in range(2):
            parent.rowconfigure(i, weight=1)

    def create_rewards_catalog_tab(self):
        """Create rewards catalog tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="🎁 Каталог Наград")

        # Rewards controls
        rewards_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        rewards_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(rewards_controls_frame, text="➕ Добавить Награду",
                 command=self.add_reward,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(rewards_controls_frame, text="📊 Популярные Награды",
                 command=self.show_popular_rewards,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Rewards filter
        tk.Label(rewards_controls_frame, text="Фильтр:",
                font=('Cambria', 12, 'bold'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_secondary']).pack(side='left', padx=(20, 5))

        self.rewards_filter = ttk.Combobox(rewards_controls_frame,
                                          values=['Все награды', 'Скидки', 'Бесплатные блюда', 'Напитки', 'Десерты'],
                                          font=('Cambria', 11), width=15, state='readonly')
        self.rewards_filter.set('Все награды')
        self.rewards_filter.pack(side='left', padx=5)

        # Rewards list
        rewards_list_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        rewards_list_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Rewards treeview
        reward_columns = ('name', 'type', 'points_cost', 'tier_requirement', 'redemptions', 'status')
        self.rewards_tree = ttk.Treeview(rewards_list_frame, columns=reward_columns, show='headings', height=15)

        # Configure reward columns
        self.rewards_tree.heading('name', text='Название')
        self.rewards_tree.heading('type', text='Тип')
        self.rewards_tree.heading('points_cost', text='Стоимость (баллы)')
        self.rewards_tree.heading('tier_requirement', text='Требуемый Уровень')
        self.rewards_tree.heading('redemptions', text='Использований')
        self.rewards_tree.heading('status', text='Статус')

        self.rewards_tree.column('name', width=250)
        self.rewards_tree.column('type', width=150)
        self.rewards_tree.column('points_cost', width=150)
        self.rewards_tree.column('tier_requirement', width=150)
        self.rewards_tree.column('redemptions', width=120)
        self.rewards_tree.column('status', width=100)

        # Rewards scrollbar
        rewards_scrollbar = ttk.Scrollbar(rewards_list_frame, orient='vertical', command=self.rewards_tree.yview)
        self.rewards_tree.configure(yscrollcommand=rewards_scrollbar.set)

        self.rewards_tree.pack(side='left', fill='both', expand=True)
        rewards_scrollbar.pack(side='right', fill='y')

    def create_points_management_tab(self):
        """Create points management tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="💎 Управление Баллами")

        # Points operations
        points_operations_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        points_operations_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(points_operations_frame, text="💎 Операции с Баллами",
                font=('Cambria', 16, 'bold'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_secondary']).pack(pady=15)

        # Points operation buttons
        points_buttons_frame = tk.Frame(points_operations_frame, bg=ModernStyles.COLORS['bg_secondary'])
        points_buttons_frame.pack(pady=(0, 15))

        tk.Button(points_buttons_frame, text="➕ Начислить Баллы",
                 command=self.award_points,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(points_buttons_frame, text="➖ Списать Баллы",
                 command=self.deduct_points,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(points_buttons_frame, text="🔄 Перенести Баллы",
                 command=self.transfer_points,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(points_buttons_frame, text="⏰ Продлить Срок",
                 command=self.extend_points_expiry,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Points transactions history
        transactions_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        transactions_frame.pack(fill='both', expand=True, padx=20, pady=10)

        tk.Label(transactions_frame, text="📋 История Транзакций Баллов",
                font=('Cambria', 14, 'bold'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_main']).pack(pady=(0, 10))

        # Transactions treeview
        transaction_columns = ('date', 'customer', 'type', 'points', 'reason', 'expiry', 'processed_by')
        self.transactions_tree = ttk.Treeview(transactions_frame, columns=transaction_columns, show='headings', height=12)

        # Configure transaction columns
        self.transactions_tree.heading('date', text='Дата')
        self.transactions_tree.heading('customer', text='Клиент')
        self.transactions_tree.heading('type', text='Тип')
        self.transactions_tree.heading('points', text='Баллы')
        self.transactions_tree.heading('reason', text='Причина')
        self.transactions_tree.heading('expiry', text='Срок Действия')
        self.transactions_tree.heading('processed_by', text='Обработал')

        self.transactions_tree.column('date', width=120)
        self.transactions_tree.column('customer', width=180)
        self.transactions_tree.column('type', width=120)
        self.transactions_tree.column('points', width=100)
        self.transactions_tree.column('reason', width=200)
        self.transactions_tree.column('expiry', width=120)
        self.transactions_tree.column('processed_by', width=150)

        # Transactions scrollbar
        transactions_scrollbar = ttk.Scrollbar(transactions_frame, orient='vertical', command=self.transactions_tree.yview)
        self.transactions_tree.configure(yscrollcommand=transactions_scrollbar.set)

        self.transactions_tree.pack(side='left', fill='both', expand=True)
        transactions_scrollbar.pack(side='right', fill='y')

    def create_campaigns_tab(self):
        """Create loyalty campaigns tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="🚀 Кампании Лояльности")

        # Campaign controls
        campaign_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        campaign_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(campaign_controls_frame, text="➕ Создать Кампанию",
                 command=self.create_loyalty_campaign,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(campaign_controls_frame, text="⚡ Двойные Баллы",
                 command=self.create_double_points_campaign,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(campaign_controls_frame, text="🎁 Бонусные Баллы",
                 command=self.create_bonus_points_campaign,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Campaigns list
        campaigns_list_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        campaigns_list_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Campaigns treeview
        campaign_columns = ('name', 'type', 'target_tier', 'multiplier', 'bonus_points', 'start_date', 'end_date', 'participants', 'status')
        self.loyalty_campaigns_tree = ttk.Treeview(campaigns_list_frame, columns=campaign_columns, show='headings', height=12)

        # Configure campaign columns
        self.loyalty_campaigns_tree.heading('name', text='Название')
        self.loyalty_campaigns_tree.heading('type', text='Тип')
        self.loyalty_campaigns_tree.heading('target_tier', text='Целевой Уровень')
        self.loyalty_campaigns_tree.heading('multiplier', text='Множитель')
        self.loyalty_campaigns_tree.heading('bonus_points', text='Бонус Баллов')
        self.loyalty_campaigns_tree.heading('start_date', text='Начало')
        self.loyalty_campaigns_tree.heading('end_date', text='Конец')
        self.loyalty_campaigns_tree.heading('participants', text='Участников')
        self.loyalty_campaigns_tree.heading('status', text='Статус')

        self.loyalty_campaigns_tree.column('name', width=200)
        self.loyalty_campaigns_tree.column('type', width=120)
        self.loyalty_campaigns_tree.column('target_tier', width=120)
        self.loyalty_campaigns_tree.column('multiplier', width=100)
        self.loyalty_campaigns_tree.column('bonus_points', width=120)
        self.loyalty_campaigns_tree.column('start_date', width=100)
        self.loyalty_campaigns_tree.column('end_date', width=100)
        self.loyalty_campaigns_tree.column('participants', width=100)
        self.loyalty_campaigns_tree.column('status', width=100)

        # Campaigns scrollbar
        campaigns_scrollbar = ttk.Scrollbar(campaigns_list_frame, orient='vertical', command=self.loyalty_campaigns_tree.yview)
        self.loyalty_campaigns_tree.configure(yscrollcommand=campaigns_scrollbar.set)

        self.loyalty_campaigns_tree.pack(side='left', fill='both', expand=True)
        campaigns_scrollbar.pack(side='right', fill='y')

    def create_referrals_tab(self):
        """Create referrals management tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="👥 Реферальная Программа")

        # Referral stats
        referral_stats_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        referral_stats_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(referral_stats_frame, text="👥 Реферальная Программа",
                font=('Cambria', 16, 'bold'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_secondary']).pack(pady=15)

        # Referral metrics
        metrics_container = tk.Frame(referral_stats_frame, bg=ModernStyles.COLORS['bg_secondary'])
        metrics_container.pack(pady=(0, 15))

        self.create_referral_metric(metrics_container, "Всего Рефералов", "1,234", ModernStyles.COLORS['primary'], 0, 0)
        self.create_referral_metric(metrics_container, "Активных за Месяц", "89", ModernStyles.COLORS['success'], 0, 1)
        self.create_referral_metric(metrics_container, "Конверсия", "67%", ModernStyles.COLORS['info'], 0, 2)
        self.create_referral_metric(metrics_container, "Выплачено Бонусов", "125,000", ModernStyles.COLORS['warning'], 0, 3)

        # Configure grid weights
        for i in range(4):
            metrics_container.columnconfigure(i, weight=1)

        # Referrals list
        referrals_list_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        referrals_list_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Referrals treeview
        referral_columns = ('referrer', 'referee', 'referral_code', 'referral_date', 'first_purchase', 'referrer_bonus', 'referee_bonus', 'status')
        self.referrals_tree = ttk.Treeview(referrals_list_frame, columns=referral_columns, show='headings', height=12)

        # Configure referral columns
        self.referrals_tree.heading('referrer', text='Реферер')
        self.referrals_tree.heading('referee', text='Приглашенный')
        self.referrals_tree.heading('referral_code', text='Код')
        self.referrals_tree.heading('referral_date', text='Дата Приглашения')
        self.referrals_tree.heading('first_purchase', text='Первая Покупка')
        self.referrals_tree.heading('referrer_bonus', text='Бонус Реферера')
        self.referrals_tree.heading('referee_bonus', text='Бонус Приглашенного')
        self.referrals_tree.heading('status', text='Статус')

        self.referrals_tree.column('referrer', width=150)
        self.referrals_tree.column('referee', width=150)
        self.referrals_tree.column('referral_code', width=120)
        self.referrals_tree.column('referral_date', width=120)
        self.referrals_tree.column('first_purchase', width=120)
        self.referrals_tree.column('referrer_bonus', width=120)
        self.referrals_tree.column('referee_bonus', width=120)
        self.referrals_tree.column('status', width=100)

        # Referrals scrollbar
        referrals_scrollbar = ttk.Scrollbar(referrals_list_frame, orient='vertical', command=self.referrals_tree.yview)
        self.referrals_tree.configure(yscrollcommand=referrals_scrollbar.set)

        self.referrals_tree.pack(side='left', fill='both', expand=True)
        referrals_scrollbar.pack(side='right', fill='y')

    def create_referral_metric(self, parent, title, value, color, row, col):
        """Create a referral metric card"""
        card_frame = tk.Frame(parent, bg=color, relief='raised', bd=2)
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky='nsew')

        tk.Label(card_frame, text=title, font=('Cambria', 11, 'bold'),
                fg='white', bg=color).pack(pady=(10, 5))

        tk.Label(card_frame, text=value, font=('Cambria', 16, 'bold'),
                fg='white', bg=color).pack(pady=(0, 10))

    def create_analytics_tab(self):
        """Create loyalty analytics tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="📈 Аналитика Лояльности")

        # Analytics dashboard
        analytics_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        analytics_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Key loyalty metrics
        metrics_frame = tk.LabelFrame(analytics_frame, text="Ключевые Показатели Лояльности",
                                    font=('Cambria', 14, 'bold'),
                                    fg=ModernStyles.COLORS['text_primary'],
                                    bg=ModernStyles.COLORS['bg_secondary'])
        metrics_frame.pack(fill='x', pady=(0, 20))

        # Create loyalty metrics cards
        loyalty_metrics_container = tk.Frame(metrics_frame, bg=ModernStyles.COLORS['bg_secondary'])
        loyalty_metrics_container.pack(fill='x', padx=15, pady=15)

        self.create_loyalty_metric_card(loyalty_metrics_container, "Участников Программы", "5,678", ModernStyles.COLORS['primary'], 0, 0)
        self.create_loyalty_metric_card(loyalty_metrics_container, "Активных Участников", "3,456", ModernStyles.COLORS['success'], 0, 1)
        self.create_loyalty_metric_card(loyalty_metrics_container, "Средние Баллы", "1,250", ModernStyles.COLORS['info'], 0, 2)
        self.create_loyalty_metric_card(loyalty_metrics_container, "Погашено Наград", "2,890", ModernStyles.COLORS['warning'], 1, 0)
        self.create_loyalty_metric_card(loyalty_metrics_container, "ROI Программы", "340%", ModernStyles.COLORS['secondary'], 1, 1)
        self.create_loyalty_metric_card(loyalty_metrics_container, "Удержание Клиентов", "85%", ModernStyles.COLORS['success'], 1, 2)

        # Configure grid weights
        for i in range(3):
            loyalty_metrics_container.columnconfigure(i, weight=1)
        for i in range(2):
            loyalty_metrics_container.rowconfigure(i, weight=1)

        # Loyalty program performance analysis
        performance_frame = tk.LabelFrame(analytics_frame, text="Анализ Эффективности Программы Лояльности",
                                        font=('Cambria', 14, 'bold'),
                                        fg=ModernStyles.COLORS['text_primary'],
                                        bg=ModernStyles.COLORS['bg_secondary'])
        performance_frame.pack(fill='both', expand=True)

        performance_text = tk.Text(performance_frame, height=12, font=('Cambria', 11),
                                 bg=ModernStyles.COLORS['bg_main'],
                                 fg=ModernStyles.COLORS['text_primary'],
                                 wrap='word', state='disabled')
        performance_text.pack(fill='both', expand=True, padx=15, pady=15)

        # Sample performance analysis
        performance_analysis = """📊 АНАЛИЗ ЭФФЕКТИВНОСТИ ПРОГРАММЫ ЛОЯЛЬНОСТИ

🎯 ОБЩИЕ ПОКАЗАТЕЛИ:
• Участников программы: 5,678 (78% от общего числа клиентов)
• Активных участников: 3,456 (61% от участников программы)
• Средний баланс баллов: 1,250 баллов на участника
• Общий объем выданных баллов: 7,097,500 баллов

💰 ФИНАНСОВЫЕ ПОКАЗАТЕЛИ:
• ROI программы лояльности: 340%
• Увеличение среднего чека: +25%
• Увеличение частоты покупок: +40%
• Дополнительная выручка: 2,450,000 руб/месяц

🏆 РАСПРЕДЕЛЕНИЕ ПО УРОВНЯМ:
• Бронзовый: 3,200 участников (56%)
• Серебряный: 1,800 участников (32%)
• Золотой: 578 участников (10%)
• Платиновый: 100 участников (2%)

🎁 ПОПУЛЯРНЫЕ НАГРАДЫ:
• Скидка 10%: 1,250 погашений
• Бесплатный десерт: 890 погашений
• Бесплатный напиток: 750 погашений
• Скидка 500 руб: 650 погашений

📈 РЕКОМЕНДАЦИИ:
• Увеличить привлекательность серебряного уровня
• Создать специальные предложения для платиновых клиентов
• Развивать реферальную программу
• Добавить сезонные бонусы и акции
"""

        performance_text.config(state='normal')
        performance_text.insert('1.0', performance_analysis)
        performance_text.config(state='disabled')

    def create_loyalty_metric_card(self, parent, title, value, color, row, col):
        """Create a loyalty metric card"""
        card_frame = tk.Frame(parent, bg=color, relief='raised', bd=2)
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky='nsew')

        tk.Label(card_frame, text=title, font=('Cambria', 12, 'bold'),
                fg='white', bg=color).pack(pady=(15, 5))

        tk.Label(card_frame, text=value, font=('Cambria', 18, 'bold'),
                fg='white', bg=color).pack(pady=(0, 15))

    # Action methods
    def load_loyalty_data(self):
        """Load loyalty system data"""
        try:
            self.load_rewards_data()
            self.load_transactions_data()
            self.load_loyalty_campaigns_data()
            self.load_referrals_data()
            self.update_loyalty_stats()
        except Exception as e:
            print(f"Error loading loyalty data: {e}")

    def load_rewards_data(self):
        """Load rewards catalog data"""
        try:
            # Clear existing data
            for item in self.rewards_tree.get_children():
                self.rewards_tree.delete(item)

            # Sample rewards data
            rewards = [
                ("Скидка 10%", "Процентная скидка", "500", "Бронзовый", "1,250", "Активна"),
                ("Бесплатный десерт", "Бесплатное блюдо", "800", "Серебряный", "890", "Активна"),
                ("Скидка 500 руб", "Фиксированная скидка", "1000", "Золотой", "650", "Активна"),
                ("Бесплатный напиток", "Бесплатный напиток", "300", "Бронзовый", "750", "Активна"),
                ("VIP столик", "Приоритетная посадка", "2000", "Платиновый", "45", "Активна")
            ]

            for reward in rewards:
                self.rewards_tree.insert('', 'end', values=reward)

        except Exception as e:
            print(f"Error loading rewards data: {e}")

    def load_transactions_data(self):
        """Load points transactions data"""
        try:
            # Clear existing data
            for item in self.transactions_tree.get_children():
                self.transactions_tree.delete(item)

            # Sample transactions data
            transactions = [
                ("15.01.2024", "Иванов И.И.", "Начисление", "+125", "Покупка на 2,500 руб", "15.01.2025", "Система"),
                ("14.01.2024", "Петрова М.С.", "Списание", "-500", "Скидка 10%", "—", "Кассир"),
                ("13.01.2024", "Сидоров А.П.", "Начисление", "+200", "Бонус за отзыв", "13.01.2025", "Менеджер"),
                ("12.01.2024", "Козлова Е.В.", "Начисление", "+1000", "Бонус в день рождения", "12.01.2025", "Система")
            ]

            for transaction in transactions:
                self.transactions_tree.insert('', 'end', values=transaction)

        except Exception as e:
            print(f"Error loading transactions data: {e}")

    def load_loyalty_campaigns_data(self):
        """Load loyalty campaigns data"""
        try:
            # Clear existing data
            for item in self.loyalty_campaigns_tree.get_children():
                self.loyalty_campaigns_tree.delete(item)

            # Sample campaigns data
            campaigns = [
                ("Двойные Баллы Январь", "Множитель баллов", "Все", "2.0x", "—", "01.01.2024", "31.01.2024", "1,250", "Активна"),
                ("Бонус Новичкам", "Бонусные баллы", "Бронзовый", "—", "500", "15.01.2024", "15.02.2024", "89", "Активна"),
                ("VIP Акция", "Специальное предложение", "Платиновый", "1.5x", "1000", "01.01.2024", "28.02.2024", "45", "Активна")
            ]

            for campaign in campaigns:
                self.loyalty_campaigns_tree.insert('', 'end', values=campaign)

        except Exception as e:
            print(f"Error loading loyalty campaigns data: {e}")

    def load_referrals_data(self):
        """Load referrals data"""
        try:
            # Clear existing data
            for item in self.referrals_tree.get_children():
                self.referrals_tree.delete(item)

            # Sample referrals data
            referrals = [
                ("Иванов И.И.", "Петров П.П.", "REF123456", "10.01.2024", "12.01.2024", "2,000", "500", "Выполнен"),
                ("Сидорова А.А.", "Козлов К.К.", "REF789012", "08.01.2024", "—", "—", "—", "Ожидание"),
                ("Федорова Ф.Ф.", "Николаев Н.Н.", "REF345678", "05.01.2024", "07.01.2024", "2,000", "500", "Выполнен")
            ]

            for referral in referrals:
                self.referrals_tree.insert('', 'end', values=referral)

        except Exception as e:
            print(f"Error loading referrals data: {e}")

    def update_loyalty_stats(self):
        """Update loyalty statistics in header"""
        try:
            # Clear existing stats
            for widget in self.stats_frame.winfo_children():
                widget.destroy()

            # Create stats labels
            stats = [
                ("👥 Участников:", "5,678"),
                ("💎 Средние Баллы:", "1,250"),
                ("🎁 Погашено Наград:", "2,890"),
                ("📈 ROI:", "340%")
            ]

            for i, (label, value) in enumerate(stats):
                stat_frame = tk.Frame(self.stats_frame, bg=ModernStyles.COLORS['bg_main'])
                stat_frame.grid(row=0, column=i, padx=10)

                tk.Label(stat_frame, text=label, font=('Cambria', 10, 'bold'),
                        fg=ModernStyles.COLORS['text_secondary'],
                        bg=ModernStyles.COLORS['bg_main']).pack()

                tk.Label(stat_frame, text=value, font=('Cambria', 14, 'bold'),
                        fg=ModernStyles.COLORS['primary'],
                        bg=ModernStyles.COLORS['bg_main']).pack()

        except Exception as e:
            print(f"Error updating loyalty stats: {e}")

    # Placeholder action methods (to be implemented)
    def configure_tiers(self):
        """Configure loyalty tiers"""
        messagebox.showinfo("Настройка", "Конфигурация уровней лояльности")

    def analyze_tiers(self):
        """Analyze tier distribution"""
        messagebox.showinfo("Анализ", "Анализ распределения по уровням")

    def bulk_tier_upgrade(self):
        """Bulk tier upgrade"""
        messagebox.showinfo("Повышение", "Массовое повышение уровня")

    def add_reward(self):
        """Add new reward"""
        messagebox.showinfo("Награда", "Добавление новой награды")

    def show_popular_rewards(self):
        """Show popular rewards analysis"""
        messagebox.showinfo("Популярные", "Анализ популярных наград")

    def award_points(self):
        """Award points to customer"""
        messagebox.showinfo("Начисление", "Начисление баллов клиенту")

    def deduct_points(self):
        """Deduct points from customer"""
        messagebox.showinfo("Списание", "Списание баллов у клиента")

    def transfer_points(self):
        """Transfer points between customers"""
        messagebox.showinfo("Перенос", "Перенос баллов между клиентами")

    def extend_points_expiry(self):
        """Extend points expiry date"""
        messagebox.showinfo("Продление", "Продление срока действия баллов")

    def create_loyalty_campaign(self):
        """Create new loyalty campaign"""
        messagebox.showinfo("Кампания", "Создание кампании лояльности")

    def create_double_points_campaign(self):
        """Create double points campaign"""
        messagebox.showinfo("Двойные Баллы", "Кампания двойных баллов")

    def create_bonus_points_campaign(self):
        """Create bonus points campaign"""
        messagebox.showinfo("Бонусные Баллы", "Кампания бонусных баллов")

def create_loyalty_rewards_system(parent, db_manager):
    """Create and show the loyalty rewards system"""
    try:
        loyalty_system = LoyaltyRewardsSystem(parent, db_manager)
        loyalty_system.show_loyalty_system()
        return loyalty_system
    except Exception as e:
        messagebox.showerror("Ошибка", f"Не удалось открыть систему лояльности: {e}")
        return None
