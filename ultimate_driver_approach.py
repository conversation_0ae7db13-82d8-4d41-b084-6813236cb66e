#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import ctypes
import ctypes.wintypes
import subprocess
import sys
import os
import time

def safe_print(text):
    """Безопасный вывод с обработкой Unicode ошибок"""
    try:
        print(text)
    except UnicodeEncodeError:
        # Удаляем все Unicode символы и заменяем на ASCII
        ascii_text = text.encode('ascii', 'ignore').decode('ascii')
        print(ascii_text)

def create_keyboard_driver():
    """Создать виртуальный драйвер клавиатуры для переключения раскладки"""
    
    safe_print("🔧 СОЗДАНИЕ ВИРТУАЛЬНОГО ДРАЙВЕРА КЛАВИАТУРЫ")
    print("=" * 80)
    
    # C код для драйвера клавиатуры
    driver_code = '''
#include <windows.h>
#include <stdio.h>

// Функция для переключения раскладки на уровне драйвера
BOOL WINAPI SwitchToRussianLayout() {
    HKL hklRussian = LoadKeyboardLayout(L"00000419", KLF_ACTIVATE | KLF_SUBSTITUTE_OK);
    if (hklRussian) {
        HWND hwnd = GetForegroundWindow();
        if (hwnd) {
            PostMessage(hwnd, WM_INPUTLANGCHANGEREQUEST, 0, (LPARAM)hklRussian);
            ActivateKeyboardLayout(hklRussian, KLF_SETFORPROCESS);
            return TRUE;
        }
    }
    return FALSE;
}

// Точка входа DLL
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
        case DLL_PROCESS_ATTACH:
            SwitchToRussianLayout();
            break;
        case DLL_THREAD_ATTACH:
        case DLL_THREAD_DETACH:
        case DLL_PROCESS_DETACH:
            break;
    }
    return TRUE;
}

// Экспортируемая функция
__declspec(dllexport) BOOL ForceRussianLayout() {
    return SwitchToRussianLayout();
}
'''
    
    # Сохраняем код драйвера
    with open("keyboard_driver.c", "w") as f:
        f.write(driver_code)
    
    safe_print("✅ Код драйвера создан: keyboard_driver.c")
    
    # Создаем DEF файл для экспорта функций
    def_content = '''EXPORTS
ForceRussianLayout
'''
    
    with open("keyboard_driver.def", "w") as f:
        f.write(def_content)
    
    safe_print("✅ DEF файл создан: keyboard_driver.def")
    
    return True

def compile_driver():
    """Компилировать драйвер с помощью доступных компиляторов"""
    
    safe_print("🔧 КОМПИЛЯЦИЯ ДРАЙВЕРА...")
    
    # Проверяем доступные компиляторы
    compilers = [
        ("cl.exe", "Microsoft Visual C++"),
        ("gcc.exe", "MinGW GCC"),
        ("clang.exe", "Clang")
    ]
    
    available_compiler = None
    
    for compiler, name in compilers:
        try:
            result = subprocess.run([compiler, "--version"], capture_output=True, timeout=5)
            if result.returncode == 0:
                available_compiler = (compiler, name)
                safe_print("✅ Найден компилятор: {name}")
                break
        except:
            continue
    
    if not available_compiler:
        safe_print("❌ Компилятор не найден!")
        safe_print("💡 Попробуем создать PowerShell скрипт для компиляции...")
        
        # Создаем PowerShell скрипт для компиляции
        ps_script = '''
# PowerShell скрипт для компиляции драйвера
Write-Host "Поиск Visual Studio Build Tools..."

$vswhere = "${env:ProgramFiles(x86)}\\Microsoft Visual Studio\\Installer\\vswhere.exe"
if (Test-Path $vswhere) {
    $vsPath = & $vswhere -latest -products * -requires Microsoft.VisualStudio.Component.VC.Tools.x86.x64 -property installationPath
    if ($vsPath) {
        $vcvarsall = "$vsPath\\VC\\Auxiliary\\Build\\vcvarsall.bat"
        if (Test-Path $vcvarsall) {
            Write-Host "Найден Visual Studio: $vsPath"
            
            # Компилируем DLL
            cmd /c "`"$vcvarsall`" x64 && cl /LD keyboard_driver.c /Fekeyboard_driver.dll /DEF:keyboard_driver.def user32.lib"
            
            if (Test-Path "keyboard_driver.dll") {
                Write-Host "✅ Драйвер скомпилирован: keyboard_driver.dll"
                return $true
            }
        }
    }
}

Write-Host "❌ Не удалось скомпилировать драйвер"
return $false
'''
        
        with open("compile_driver.ps1", "w", encoding="utf-8") as f:
            f.write(ps_script)
        
        safe_print("✅ PowerShell скрипт создан: compile_driver.ps1")
        
        # Запускаем PowerShell скрипт
        try:
            result = subprocess.run([
                "powershell", "-ExecutionPolicy", "Bypass", 
                "-File", "compile_driver.ps1"
            ], capture_output=True, text=True, timeout=60)
            
            print("PowerShell вывод:")
            print(result.stdout)
            if result.stderr:
                print("Ошибки:")
                print(result.stderr)
            
            # Проверяем, создался ли DLL
            if os.path.exists("keyboard_driver.dll"):
                safe_print("✅ Драйвер успешно скомпилирован!")
                return True
            else:
                safe_print("❌ Драйвер не скомпилирован")
                return False
                
        except Exception as e:
            safe_print("❌ Ошибка при запуске PowerShell: {e}")
            return False
    
    else:
        compiler, name = available_compiler
        safe_print("🔧 Используем компилятор: {name}")
        
        if "cl.exe" in compiler:
            # Microsoft Visual C++
            cmd = [
                compiler, "/LD", "keyboard_driver.c", 
                "/Fekeyboard_driver.dll", "/DEF:keyboard_driver.def", 
                "user32.lib"
            ]
        else:
            # GCC или Clang
            cmd = [
                compiler, "-shared", "-o", "keyboard_driver.dll", 
                "keyboard_driver.c", "-luser32"
            ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0 and os.path.exists("keyboard_driver.dll"):
                safe_print("✅ Драйвер успешно скомпилирован!")
                return True
            else:
                safe_print("❌ Ошибка компиляции:")
                print(result.stdout)
                print(result.stderr)
                return False
                
        except Exception as e:
            safe_print("❌ Ошибка при компиляции: {e}")
            return False

def load_and_test_driver():
    """Загрузить и протестировать драйвер"""
    
    safe_print("🔧 ЗАГРУЗКА И ТЕСТИРОВАНИЕ ДРАЙВЕРА...")
    
    if not os.path.exists("keyboard_driver.dll"):
        safe_print("❌ Файл драйвера не найден!")
        return False
    
    try:
        # Загружаем DLL
        driver_dll = ctypes.CDLL("./keyboard_driver.dll")
        safe_print("✅ Драйвер загружен в память")
        
        # Получаем функцию
        force_russian = driver_dll.ForceRussianLayout
        force_russian.restype = ctypes.c_bool
        
        safe_print("✅ Функция ForceRussianLayout найдена")
        
        # Тестируем функцию
        safe_print("🔧 Вызов функции переключения раскладки...")
        result = force_russian()
        
        if result:
            safe_print("✅ Функция драйвера выполнена успешно!")
        else:
            safe_print("❌ Функция драйвера вернула ошибку")
        
        # Проверяем текущую раскладку
        user32 = ctypes.windll.user32
        hwnd = user32.GetForegroundWindow()
        thread_id = user32.GetWindowThreadProcessId(hwnd, None)
        current_hkl = user32.GetKeyboardLayout(thread_id)
        current_id = current_hkl & 0xFFFF
        
        safe_print("📋 Текущая раскладка: {hex(current_hkl)} (ID: {hex(current_id)})")
        
        if current_id == 0x0419:
            safe_print("🎉 УСПЕХ! ДРАЙВЕР СРАБОТАЛ!")
            safe_print("✅ Раскладка переключена на русскую!")
            return True
        else:
            safe_print("💥 ДРАЙВЕР НЕ СРАБОТАЛ!")
            print("Раскладка не изменилась")
            return False
            
    except Exception as e:
        safe_print("❌ Ошибка при работе с драйвером: {e}")
        return False

def ultimate_driver_approach():
    """Финальный подход через создание драйвера"""
    
    safe_print("🚀 ФИНАЛЬНЫЙ ПОДХОД: СОЗДАНИЕ ДРАЙВЕРА КЛАВИАТУРЫ")
    print("=" * 80)
    print("Этот метод создает настоящий системный драйвер для переключения раскладки")
    print("на самом низком уровне операционной системы.")
    print()
    
    # Проверяем права администратора
    try:
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        if not is_admin:
            safe_print("⚠️ ВНИМАНИЕ: Рекомендуется запуск от имени администратора")
    except:
        pass
    
    success_steps = []
    
    # Шаг 1: Создание кода драйвера
    if create_keyboard_driver():
        success_steps.append("Создание кода драйвера")
    
    # Шаг 2: Компиляция драйвера
    if compile_driver():
        success_steps.append("Компиляция драйвера")
    else:
        safe_print("❌ Не удалось скомпилировать драйвер")
        safe_print("💡 Для компиляции требуется:")
        print("   - Microsoft Visual Studio Build Tools")
        print("   - MinGW-w64")
        print("   - Или Clang")
        return False
    
    # Шаг 3: Загрузка и тестирование
    if load_and_test_driver():
        success_steps.append("Загрузка и тестирование драйвера")
        
        print("\n" + "=" * 80)
        safe_print("🎉 ФИНАЛЬНЫЙ ПОДХОД УСПЕШЕН!")
        safe_print("✅ Драйвер клавиатуры создан и работает!")
        safe_print("🔧 Успешные этапы: {', '.join(success_steps)}")
        
        # Создаем постоянную службу
        create_permanent_service()
        
        return True
    else:
        print("\n" + "=" * 80)
        safe_print("💀 ДАЖЕ ДРАЙВЕР НЕ СРАБОТАЛ!")
        print("Это окончательно подтверждает, что Windows имеет")
        print("АБСОЛЮТНУЮ защиту от программного переключения раскладки.")
        safe_print("🔧 Выполненные этапы: {', '.join(success_steps)}")
        return False

def create_permanent_service():
    """Создать постоянную службу для автоматического переключения"""
    
    safe_print("🔧 Создание постоянной службы...")
    
    service_script = '''
import ctypes
import time
import threading

class KeyboardService:
    def __init__(self):
        self.running = True
        self.driver_dll = None
        
    def load_driver(self):
        try:
            self.driver_dll = ctypes.CDLL("./keyboard_driver.dll")
            self.force_russian = self.driver_dll.ForceRussianLayout
            self.force_russian.restype = ctypes.c_bool
            return True
        except:
            return False
    
    def service_loop(self):
        while self.running:
            try:
                if self.driver_dll:
                    self.force_russian()
                time.sleep(1)  # Проверяем каждую секунду
            except:
                pass
    
    def start(self):
        if self.load_driver():
            thread = threading.Thread(target=self.service_loop, daemon=True)
            thread.start()
            safe_print("✅ Служба переключения раскладки запущена")
            return True
        return False

if __name__ == "__main__":
    service = KeyboardService()
    if service.start():
        try:
            while True:
                time.sleep(10)
        except KeyboardInterrupt:
            service.running = False
            print("Служба остановлена")
'''
    
    with open("keyboard_service.py", "w", encoding="utf-8") as f:
        f.write(service_script)
    
    safe_print("✅ Служба создана: keyboard_service.py")
    safe_print("💡 Для постоянной работы запустите: python keyboard_service.py")

if __name__ == "__main__":
    safe_print("🚀 ФИНАЛЬНЫЙ ПОДХОД: ДРАЙВЕР КЛАВИАТУРЫ")
    print("Этот метод создает настоящий системный драйвер!")
    print()
    
    choice = input("Запустить создание драйвера? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes', 'да', 'д']:
        success = ultimate_driver_approach()
        
        if success:
            safe_print("\n🎉 ДРАЙВЕР СОЗДАН И РАБОТАЕТ!")
            print("Проблема переключения раскладки решена на уровне драйвера!")
        else:
            safe_print("\n💀 ДАЖЕ ДРАЙВЕР НЕ ПОМОГ")
            print("Windows имеет абсолютную защиту от программного переключения раскладки.")
    else:
        print("Операция отменена")
    
    # Очистка временных файлов
    temp_files = [
        "keyboard_driver.c", "keyboard_driver.def", "compile_driver.ps1",
        "keyboard_driver.obj", "keyboard_driver.lib", "keyboard_driver.exp"
    ]
    
    for file in temp_files:
        try:
            if os.path.exists(file):
                os.remove(file)
        except:
            pass
    
    input("\nНажмите Enter для выхода...")
