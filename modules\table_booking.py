"""
Полностью функциональная система управления столами и бронирования
Визуальная схема зала, бронирование, управление столами
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from datetime import datetime, timedelta
import json
import os
from gui.styles import ModernStyles

class TableBookingSystem:
    """Система управления столами и бронирования"""

    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None

        # Конфигурация столов
        self.tables = {
            1: {"seats": 2, "status": "free", "x": 50, "y": 50, "type": "small"},
            2: {"seats": 2, "status": "occupied", "x": 150, "y": 50, "type": "small"},
            3: {"seats": 4, "status": "free", "x": 250, "y": 50, "type": "medium"},
            4: {"seats": 4, "status": "reserved", "x": 350, "y": 50, "type": "medium"},
            5: {"seats": 6, "status": "free", "x": 50, "y": 150, "type": "large"},
            6: {"seats": 6, "status": "occupied", "x": 150, "y": 150, "type": "large"},
            7: {"seats": 8, "status": "free", "x": 250, "y": 150, "type": "large"},
            8: {"seats": 4, "status": "free", "x": 350, "y": 150, "type": "medium"},
            9: {"seats": 2, "status": "free", "x": 50, "y": 250, "type": "small"},
            10: {"seats": 2, "status": "reserved", "x": 150, "y": 250, "type": "small"},
            11: {"seats": 4, "status": "free", "x": 250, "y": 250, "type": "medium"},
            12: {"seats": 6, "status": "free", "x": 350, "y": 250, "type": "large"},
        }

        # Бронирования
        self.reservations = [
            {
                "id": 1, "table_id": 4, "customer_name": "Иванов А.П.",
                "phone": "+7 (495) 123-45-67", "date": "2024-01-15",
                "time": "19:00", "guests": 4, "notes": "День рождения",
                "status": "confirmed"
            },
            {
                "id": 2, "table_id": 10, "customer_name": "Петрова М.С.",
                "phone": "+7 (495) 234-56-78", "date": "2024-01-15",
                "time": "20:30", "guests": 2, "notes": "Романтический ужин",
                "status": "confirmed"
            },
            {
                "id": 3, "table_id": 7, "customer_name": "Сидоров Д.А.",
                "phone": "+7 (495) 345-67-89", "date": "2024-01-16",
                "time": "18:00", "guests": 8, "notes": "Корпоратив",
                "status": "pending"
            }
        ]

        # Цвета статусов столов
        self.table_colors = {
            "free": "#2ecc71",      # Зелёный - свободен
            "occupied": "#e74c3c",   # Красный - занят
            "reserved": "#f39c12",   # Оранжевый - забронирован
            "cleaning": "#95a5a6"    # Серый - уборка
        }

    def create_window(self):
        """Создать окно системы управления столами"""
        try:
            from utils.window_utils import create_centered_dialog
            self.window = create_centered_dialog(
                self.parent,
                "🍽️ Управление Столами и Бронирование",
                width=1600,
                height=1000,
                resizable=True
            )
        except ImportError:
            # Fallback если utils не доступны
            self.window = tk.Toplevel(self.parent)
            self.window.title("🍽️ Управление Столами и Бронирование")
            self.window.geometry("1600x1000")
            self.window.configure(bg=ModernStyles.COLORS['bg_main'])
            self.window.resizable(True, True)

            # Центрировать окно
            self.window.update_idletasks()
            x = (self.window.winfo_screenwidth() // 2) - (1600 // 2)
            y = (self.window.winfo_screenheight() // 2) - (1000 // 2)
            self.window.geometry(f"1600x1000+{x}+{y}")

        self.create_interface()

    def create_interface(self):
        """Создать интерфейс системы"""
        # Заголовок
        header_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['primary'], height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="🍽️ Управление Столами и Бронирование",
                font=('Cambria', 16, 'bold italic'), bg=ModernStyles.COLORS['primary'],
                fg='white').pack(side='left', padx=20, pady=15)

        # Кнопки действий
        btn_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['primary'])
        btn_frame.pack(side='right', padx=20, pady=10)

        tk.Button(btn_frame, text="📅 Новое Бронирование", command=self.add_reservation,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(btn_frame, text="🔄 Обновить", command=self.refresh_all,
                 bg=ModernStyles.COLORS['secondary'], fg='white',
                 font=('Cambria', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(btn_frame, text="📊 Отчёт", command=self.generate_report,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Cambria', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        # Основной контент
        main_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Создать вкладки
        self.create_tabs(main_frame)

    def create_tabs(self, parent):
        """Создать вкладки системы"""
        notebook = ttk.Notebook(parent)
        notebook.pack(fill='both', expand=True)

        # Вкладка схемы зала
        hall_frame = tk.Frame(notebook, bg='white')
        notebook.add(hall_frame, text="🏛️ Схема Зала")
        self.create_hall_tab(hall_frame)

        # Вкладка бронирований
        reservations_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(reservations_frame, text="📅 Бронирования")
        self.create_reservations_tab(reservations_frame)

        # Вкладка управления столами
        tables_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(tables_frame, text="🍽️ Управление Столами")
        self.create_tables_tab(tables_frame)

        # Вкладка статистики
        stats_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(stats_frame, text="📊 Статистика")
        self.create_stats_tab(stats_frame)

    def create_hall_tab(self, parent):
        """Создать вкладку схемы зала"""
        # Заголовок и легенда
        header_frame = tk.Frame(parent, bg='white')
        header_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(header_frame, text="🏛️ Схема Ресторанного Зала",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(side='left')

        # Легенда
        legend_frame = tk.Frame(header_frame, bg='white')
        legend_frame.pack(side='right')

        legend_items = [
            ("🟢 Свободен", self.table_colors['free']),
            ("🔴 Занят", self.table_colors['occupied']),
            ("🟠 Забронирован", self.table_colors['reserved']),
            ("⚪ Уборка", self.table_colors['cleaning'])
        ]

        for text, color in legend_items:
            legend_item = tk.Frame(legend_frame, bg='white')
            legend_item.pack(side='left', padx=5)

            color_box = tk.Frame(legend_item, bg=color, width=15, height=15)
            color_box.pack(side='left', padx=(0, 5))

            tk.Label(legend_item, text=text, font=('Cambria', 10), bg='white').pack(side='left')

        # Холст для схемы зала
        canvas_frame = tk.Frame(parent, bg='white', relief='sunken', bd=2)
        canvas_frame.pack(fill='both', expand=True, padx=20, pady=10)

        self.hall_canvas = tk.Canvas(canvas_frame, bg='#f8f9fa', width=800, height=600)
        self.hall_canvas.pack(fill='both', expand=True)

        # Нарисовать схему зала
        self.draw_hall_layout()

        # Привязать события
        self.hall_canvas.bind("<Button-1>", self.on_table_click)

        # Информационная панель
        info_frame = tk.Frame(parent, bg='white')
        info_frame.pack(fill='x', padx=20, pady=10)

        self.info_label = tk.Label(info_frame, text="Кликните на стол для управления",
                                  font=('Cambria', 12), bg='white')
        self.info_label.pack()

    def draw_hall_layout(self):
        """Нарисовать схему зала"""
        self.hall_canvas.delete("all")

        # Нарисовать стены и элементы зала
        # Стены
        self.hall_canvas.create_rectangle(10, 10, 790, 590, outline='#2c3e50', width=3, fill='#ecf0f1')

        # Вход
        self.hall_canvas.create_rectangle(350, 10, 450, 30, fill='#3498db', outline='#2980b9', width=2)
        self.hall_canvas.create_text(400, 20, text="ВХОД", font=('Cambria', 10, 'bold'), fill='white')

        # Кухня
        self.hall_canvas.create_rectangle(600, 450, 780, 580, fill='#e74c3c', outline='#c0392b', width=2)
        self.hall_canvas.create_text(690, 515, text="КУХНЯ", font=('Cambria', 12, 'bold'), fill='white')

        # Бар
        self.hall_canvas.create_rectangle(20, 450, 200, 580, fill='#9b59b6', outline='#8e44ad', width=2)
        self.hall_canvas.create_text(110, 515, text="БАР", font=('Cambria', 12, 'bold'), fill='white')

        # Туалеты
        self.hall_canvas.create_rectangle(20, 350, 100, 430, fill='#95a5a6', outline='#7f8c8d', width=2)
        self.hall_canvas.create_text(60, 390, text="WC", font=('Cambria', 10, 'bold'), fill='white')

        # Нарисовать столы
        for table_id, table_info in self.tables.items():
            self.draw_table(table_id, table_info)

    def draw_table(self, table_id, table_info):
        """Нарисовать стол"""
        x, y = table_info['x'], table_info['y']
        seats = table_info['seats']
        status = table_info['status']
        color = self.table_colors[status]

        # Размер стола в зависимости от количества мест
        if seats <= 2:
            size = 40
        elif seats <= 4:
            size = 60
        else:
            size = 80

        # Нарисовать стол
        table_obj = self.hall_canvas.create_oval(
            x, y, x + size, y + size,
            fill=color, outline='#2c3e50', width=2,
            tags=f"table_{table_id}"
        )

        # Номер стола
        self.hall_canvas.create_text(
            x + size//2, y + size//2 - 8,
            text=f"№{table_id}",
            font=('Cambria', 10, 'bold'),
            fill='white' if status != 'free' else 'black',
            tags=f"table_{table_id}"
        )

        # Количество мест
        self.hall_canvas.create_text(
            x + size//2, y + size//2 + 8,
            text=f"{seats} мест",
            font=('Cambria', 8),
            fill='white' if status != 'free' else 'black',
            tags=f"table_{table_id}"
        )

    def on_table_click(self, event):
        """Обработка клика по столу"""
        # Найти объект под курсором
        item = self.hall_canvas.find_closest(event.x, event.y)[0]
        tags = self.hall_canvas.gettags(item)

        # Найти ID стола
        table_id = None
        for tag in tags:
            if tag.startswith("table_"):
                table_id = int(tag.split("_")[1])
                break

        if table_id:
            self.show_table_menu(table_id, event.x_root, event.y_root)

    def show_table_menu(self, table_id, x, y):
        """Показать контекстное меню стола"""
        table_info = self.tables[table_id]

        menu = tk.Menu(self.window, tearoff=0)
        menu.add_command(label=f"Стол №{table_id} ({table_info['seats']} мест)")
        menu.add_separator()

        if table_info['status'] == 'free':
            menu.add_command(label="🔴 Занять стол", command=lambda: self.change_table_status(table_id, 'occupied'))
            menu.add_command(label="📅 Забронировать", command=lambda: self.reserve_table(table_id))
        elif table_info['status'] == 'occupied':
            menu.add_command(label="🟢 Освободить стол", command=lambda: self.change_table_status(table_id, 'free'))
            menu.add_command(label="🧹 Отправить на уборку", command=lambda: self.change_table_status(table_id, 'cleaning'))
        elif table_info['status'] == 'reserved':
            menu.add_command(label="🔴 Посадить гостей", command=lambda: self.change_table_status(table_id, 'occupied'))
            menu.add_command(label="❌ Отменить бронь", command=lambda: self.change_table_status(table_id, 'free'))
        elif table_info['status'] == 'cleaning':
            menu.add_command(label="🟢 Уборка завершена", command=lambda: self.change_table_status(table_id, 'free'))

        menu.add_separator()
        menu.add_command(label="ℹ️ Информация о столе", command=lambda: self.show_table_info(table_id))

        try:
            menu.tk_popup(x, y)
        finally:
            menu.grab_release()

    def change_table_status(self, table_id, new_status):
        """Изменить статус стола"""
        self.tables[table_id]['status'] = new_status
        self.draw_hall_layout()

        status_names = {
            'free': 'свободен',
            'occupied': 'занят',
            'reserved': 'забронирован',
            'cleaning': 'на уборке'
        }

        self.info_label.config(text=f"Стол №{table_id} теперь {status_names[new_status]}")

    def reserve_table(self, table_id):
        """Забронировать стол"""
        self.add_reservation(table_id)

    def show_table_info(self, table_id):
        """Показать информацию о столе"""
        table_info = self.tables[table_id]

        info_window = tk.Toplevel(self.window)
        info_window.title(f"ℹ️ Информация о столе №{table_id}")
        info_window.geometry("500x400")  # Увеличен размер для показа всех полей
        info_window.configure(bg='white')
        info_window.resizable(True, True)  # Разрешить изменение размера

        # Центрировать диалог
        info_window.update_idletasks()
        x = (info_window.winfo_screenwidth() // 2) - (500 // 2)
        y = (info_window.winfo_screenheight() // 2) - (400 // 2)
        info_window.geometry(f"500x400+{x}+{y}")

        # Заголовок
        tk.Label(info_window, text=f"Стол №{table_id}",
                font=('Cambria', 16, 'bold'), bg='white').pack(pady=20)

        # Информация
        info_text = f"""
Количество мест: {table_info['seats']}
Статус: {table_info['status']}
Тип стола: {table_info['type']}
Позиция: ({table_info['x']}, {table_info['y']})
        """

        tk.Label(info_window, text=info_text, font=('Cambria', 12),
                bg='white', justify='left').pack(pady=20)

        # Найти бронирования для этого стола
        table_reservations = [r for r in self.reservations if r['table_id'] == table_id]
        if table_reservations:
            tk.Label(info_window, text="Бронирования:",
                    font=('Cambria', 12, 'bold'), bg='white').pack()

            for res in table_reservations:
                res_text = f"{res['date']} {res['time']} - {res['customer_name']}"
                tk.Label(info_window, text=res_text, font=('Cambria', 10),
                        bg='white').pack()

    def create_reservations_tab(self, parent):
        """Создать вкладку бронирований"""
        # Заголовок
        tk.Label(parent, text="📅 Управление Бронированиями",
                font=('Cambria', 16, 'bold italic'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)

        # Фильтры
        filter_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        filter_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(filter_frame, text="Дата:", font=('Cambria', 10, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).pack(side='left')

        self.date_filter = tk.Entry(filter_frame, font=('Cambria', 10))
        self.date_filter.insert(0, datetime.now().strftime("%Y-%m-%d"))
        self.date_filter.pack(side='left', padx=10)

        tk.Button(filter_frame, text="🔍 Фильтр", command=self.filter_reservations,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 9, 'bold'), relief='flat', padx=10, pady=3).pack(side='left', padx=5)

        # Таблица бронирований
        columns = ('ID', 'Стол', 'Клиент', 'Телефон', 'Дата', 'Время', 'Гости', 'Статус')
        self.reservations_tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)

        # Настройка колонок
        column_widths = {'ID': 50, 'Стол': 60, 'Клиент': 150, 'Телефон': 120, 'Дата': 100, 'Время': 80, 'Гости': 60, 'Статус': 100}
        for col in columns:
            self.reservations_tree.heading(col, text=col)
            self.reservations_tree.column(col, width=column_widths.get(col, 100))

        # Заполнить данными
        self.refresh_reservations()

        # Скроллбары
        res_v_scroll = ttk.Scrollbar(parent, orient='vertical', command=self.reservations_tree.yview)
        res_h_scroll = ttk.Scrollbar(parent, orient='horizontal', command=self.reservations_tree.xview)
        self.reservations_tree.configure(yscrollcommand=res_v_scroll.set, xscrollcommand=res_h_scroll.set)

        self.reservations_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        res_v_scroll.pack(side='right', fill='y')
        res_h_scroll.pack(side='bottom', fill='x')

        # Кнопки управления
        res_btn_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        res_btn_frame.pack(fill='x', padx=10, pady=10)

        tk.Button(res_btn_frame, text="✏️ Редактировать", command=self.edit_reservation,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(res_btn_frame, text="✅ Подтвердить", command=self.confirm_reservation,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(res_btn_frame, text="❌ Отменить", command=self.cancel_reservation,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Cambria', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

    def refresh_reservations(self):
        """Обновить список бронирований"""
        for item in self.reservations_tree.get_children():
            self.reservations_tree.delete(item)

        for res in self.reservations:
            status_icon = "✅" if res['status'] == 'confirmed' else "⏳" if res['status'] == 'pending' else "❌"

            self.reservations_tree.insert('', 'end', values=(
                res['id'],
                f"№{res['table_id']}",
                res['customer_name'],
                res['phone'],
                res['date'],
                res['time'],
                res['guests'],
                f"{status_icon} {res['status']}"
            ))

    def filter_reservations(self):
        """Фильтровать бронирования по дате"""
        filter_date = self.date_filter.get()

        for item in self.reservations_tree.get_children():
            self.reservations_tree.delete(item)

        filtered_reservations = [r for r in self.reservations if r['date'] == filter_date]

        for res in filtered_reservations:
            status_icon = "✅" if res['status'] == 'confirmed' else "⏳" if res['status'] == 'pending' else "❌"

            self.reservations_tree.insert('', 'end', values=(
                res['id'],
                f"№{res['table_id']}",
                res['customer_name'],
                res['phone'],
                res['date'],
                res['time'],
                res['guests'],
                f"{status_icon} {res['status']}"
            ))

    def add_reservation(self, table_id=None):
        """Добавить новое бронирование"""
        try:
            from utils.window_utils import create_centered_dialog
            res_window = create_centered_dialog(
                self.window,
                "📅 Новое Бронирование",
                width=600,
                height=700,
                resizable=True
            )
        except ImportError:
            # Fallback если utils не доступны
            res_window = tk.Toplevel(self.window)
            res_window.title("📅 Новое Бронирование")
            res_window.geometry("600x700")
            res_window.configure(bg=ModernStyles.COLORS['bg_main'])
            res_window.resizable(True, True)

            # Центрировать окно
            res_window.update_idletasks()
            x = (res_window.winfo_screenwidth() // 2) - (600 // 2)
            y = (res_window.winfo_screenheight() // 2) - (700 // 2)
            res_window.geometry(f"600x700+{x}+{y}")

        # Заголовок
        tk.Label(res_window, text="Новое Бронирование",
                font=('Cambria', 16, 'bold italic'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

        # Форма
        form_frame = tk.Frame(res_window, bg=ModernStyles.COLORS['bg_main'])
        form_frame.pack(fill='both', expand=True, padx=20)

        # Поля формы
        fields = [
            ("Имя клиента:", "customer_name"),
            ("Телефон:", "phone"),
            ("Дата:", "date"),
            ("Время:", "time"),
            ("Количество гостей:", "guests"),
            ("Заметки:", "notes")
        ]

        entries = {}
        for i, (label, field) in enumerate(fields):
            tk.Label(form_frame, text=label, font=('Cambria', 12, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).grid(row=i, column=0, sticky='w', pady=10)

            if field == "notes":
                entry = tk.Text(form_frame, font=('Cambria', 11), height=4, width=30)
            else:
                entry = tk.Entry(form_frame, font=('Cambria', 11), width=30)

            entry.grid(row=i, column=1, sticky='ew', padx=(10, 0), pady=10)
            entries[field] = entry

        # Выбор стола
        tk.Label(form_frame, text="Стол:", font=('Cambria', 12, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).grid(row=len(fields), column=0, sticky='w', pady=10)

        table_combo = ttk.Combobox(form_frame, font=('Cambria', 11), width=27)
        available_tables = [f"№{tid} ({info['seats']} мест)" for tid, info in self.tables.items() if info['status'] == 'free']
        table_combo['values'] = available_tables
        if table_id and self.tables[table_id]['status'] == 'free':
            table_combo.set(f"№{table_id} ({self.tables[table_id]['seats']} мест)")
        table_combo.grid(row=len(fields), column=1, sticky='ew', padx=(10, 0), pady=10)

        # Предзаполнить дату и время
        entries['date'].insert(0, datetime.now().strftime("%Y-%m-%d"))
        entries['time'].insert(0, "19:00")

        form_frame.grid_columnconfigure(1, weight=1)

        # Кнопки
        btn_frame = tk.Frame(res_window, bg=ModernStyles.COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=20, pady=20)

        def save_reservation():
            try:
                table_text = table_combo.get()
                if not table_text:
                    messagebox.showerror("Ошибка", "Выберите стол")
                    return

                table_id = int(table_text.split('№')[1].split(' ')[0])
                notes_text = entries['notes'].get('1.0', 'end-1c') if hasattr(entries['notes'], 'get') else entries['notes'].get()

                new_reservation = {
                    "id": max(r['id'] for r in self.reservations) + 1 if self.reservations else 1,
                    "table_id": table_id,
                    "customer_name": entries['customer_name'].get(),
                    "phone": entries['phone'].get(),
                    "date": entries['date'].get(),
                    "time": entries['time'].get(),
                    "guests": int(entries['guests'].get()),
                    "notes": notes_text,
                    "status": "confirmed"
                }

                self.reservations.append(new_reservation)
                self.tables[table_id]['status'] = 'reserved'

                self.refresh_reservations()
                self.draw_hall_layout()
                res_window.destroy()
                messagebox.showinfo("Успех", "Бронирование создано")

            except Exception as e:
                messagebox.showerror("Ошибка", f"Ошибка создания бронирования: {e}")

        tk.Button(btn_frame, text="💾 Сохранить", command=save_reservation,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat', padx=20, pady=8).pack(side='left')

        tk.Button(btn_frame, text="❌ Отмена", command=res_window.destroy,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat', padx=20, pady=8).pack(side='right')

    def edit_reservation(self):
        """Редактировать бронирование"""
        selection = self.reservations_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите бронирование для редактирования")
            return

        messagebox.showinfo("В разработке", "Функция редактирования будет добавлена в следующей версии")

    def confirm_reservation(self):
        """Подтвердить бронирование"""
        selection = self.reservations_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите бронирование для подтверждения")
            return

        item = self.reservations_tree.item(selection[0])
        res_id = int(item['values'][0])

        for res in self.reservations:
            if res['id'] == res_id:
                res['status'] = 'confirmed'
                break

        self.refresh_reservations()
        messagebox.showinfo("Успех", "Бронирование подтверждено")

    def cancel_reservation(self):
        """Отменить бронирование"""
        selection = self.reservations_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите бронирование для отмены")
            return

        if messagebox.askyesno("Подтверждение", "Отменить выбранное бронирование?"):
            item = self.reservations_tree.item(selection[0])
            res_id = int(item['values'][0])

            # Найти и удалить бронирование
            for i, res in enumerate(self.reservations):
                if res['id'] == res_id:
                    table_id = res['table_id']
                    self.tables[table_id]['status'] = 'free'
                    del self.reservations[i]
                    break

            self.refresh_reservations()
            self.draw_hall_layout()
            messagebox.showinfo("Успех", "Бронирование отменено")

    def create_tables_tab(self, parent):
        """Создать вкладку управления столами"""
        # Заголовок
        tk.Label(parent, text="🍽️ Управление Столами",
                font=('Cambria', 16, 'bold italic'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)

        # Таблица столов
        columns = ('Номер', 'Места', 'Статус', 'Тип', 'Действия')
        tables_tree = ttk.Treeview(parent, columns=columns, show='headings', height=12)

        for col in columns:
            tables_tree.heading(col, text=col)
            tables_tree.column(col, width=120)

        # Заполнить данными
        for table_id, table_info in self.tables.items():
            status_icon = {"free": "🟢", "occupied": "🔴", "reserved": "🟠", "cleaning": "⚪"}[table_info['status']]

            tables_tree.insert('', 'end', values=(
                f"№{table_id}",
                f"{table_info['seats']} мест",
                f"{status_icon} {table_info['status']}",
                table_info['type'],
                "Управление"
            ))

        tables_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def create_stats_tab(self, parent):
        """Создать вкладку статистики"""
        # Заголовок
        tk.Label(parent, text="📊 Статистика Столов и Бронирований",
                font=('Cambria', 16, 'bold italic'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

        # Статистика столов
        stats_frame = tk.LabelFrame(parent, text="Статистика Столов",
                                   font=('Cambria', 14, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        stats_frame.pack(fill='x', padx=20, pady=10)

        total_tables = len(self.tables)
        free_tables = len([t for t in self.tables.values() if t['status'] == 'free'])
        occupied_tables = len([t for t in self.tables.values() if t['status'] == 'occupied'])
        reserved_tables = len([t for t in self.tables.values() if t['status'] == 'reserved'])

        stats_text = f"""
Общее количество столов: {total_tables}
🟢 Свободных: {free_tables} ({free_tables/total_tables*100:.1f}%)
🔴 Занятых: {occupied_tables} ({occupied_tables/total_tables*100:.1f}%)
🟠 Забронированных: {reserved_tables} ({reserved_tables/total_tables*100:.1f}%)
        """

        tk.Label(stats_frame, text=stats_text, font=('Cambria', 12),
                bg=ModernStyles.COLORS['bg_main'], justify='left').pack(padx=20, pady=20)

        # Статистика бронирований
        res_stats_frame = tk.LabelFrame(parent, text="Статистика Бронирований",
                                       font=('Cambria', 14, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        res_stats_frame.pack(fill='x', padx=20, pady=10)

        total_reservations = len(self.reservations)
        confirmed_res = len([r for r in self.reservations if r['status'] == 'confirmed'])
        pending_res = len([r for r in self.reservations if r['status'] == 'pending'])

        res_stats_text = f"""
Всего бронирований: {total_reservations}
✅ Подтверждённых: {confirmed_res}
⏳ Ожидающих: {pending_res}
        """

        tk.Label(res_stats_frame, text=res_stats_text, font=('Cambria', 12),
                bg=ModernStyles.COLORS['bg_main'], justify='left').pack(padx=20, pady=20)

    def refresh_all(self):
        """Обновить все данные"""
        self.draw_hall_layout()
        self.refresh_reservations()
        messagebox.showinfo("Обновление", "Данные обновлены")

    def generate_report(self):
        """Генерировать отчёт"""
        report_window = tk.Toplevel(self.window)
        report_window.title("📊 Отчёт по Столам и Бронированиям")
        report_window.geometry("700x600")
        report_window.configure(bg='white')

        # Заголовок
        tk.Label(report_window, text="📊 Отчёт по Столам и Бронированиям",
                font=('Cambria', 16, 'bold'), bg='white').pack(pady=20)

        # Содержимое отчёта
        report_text = tk.Text(report_window, font=('Cambria', 11), wrap='word')
        report_text.pack(fill='both', expand=True, padx=20, pady=20)

        # Генерировать отчёт
        report_content = f"""
ОТЧЁТ ПО УПРАВЛЕНИЮ СТОЛАМИ И БРОНИРОВАНИЯМ
==========================================

Дата формирования: {datetime.now().strftime('%d.%m.%Y %H:%M')}

СТАТИСТИКА СТОЛОВ:
• Общее количество столов: {len(self.tables)}
• Свободных: {len([t for t in self.tables.values() if t['status'] == 'free'])}
• Занятых: {len([t for t in self.tables.values() if t['status'] == 'occupied'])}
• Забронированных: {len([t for t in self.tables.values() if t['status'] == 'reserved'])}
• На уборке: {len([t for t in self.tables.values() if t['status'] == 'cleaning'])}

СТАТИСТИКА БРОНИРОВАНИЙ:
• Всего бронирований: {len(self.reservations)}
• Подтверждённых: {len([r for r in self.reservations if r['status'] == 'confirmed'])}
• Ожидающих: {len([r for r in self.reservations if r['status'] == 'pending'])}

ДЕТАЛИЗАЦИЯ БРОНИРОВАНИЙ:
"""

        for res in self.reservations:
            report_content += f"""
{res['date']} {res['time']} - Стол №{res['table_id']}
Клиент: {res['customer_name']} ({res['phone']})
Гостей: {res['guests']}, Статус: {res['status']}
Заметки: {res['notes']}
"""

        report_text.insert('1.0', report_content)
        report_text.config(state='disabled')

def create_table_booking_system(parent, db_manager):
    """Создать систему управления столами"""
    system = TableBookingSystem(parent, db_manager)
    system.create_window()
    return system