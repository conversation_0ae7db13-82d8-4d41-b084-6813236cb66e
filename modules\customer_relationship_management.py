"""
Customer Relationship Management (CRM) System
Comprehensive CRM module for restaurant management system with customer profiles,
purchase history, preferences tracking, and marketing campaigns.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from datetime import datetime, timedelta
import json
import hashlib
import uuid
from gui.styles import ModernStyles

class CustomerRelationshipManagement:
    """Comprehensive Customer Relationship Management System"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.notebook = None
        
        # CRM configuration
        self.crm_config = {
            "loyalty_points_per_ruble": 1,
            "birthday_discount_percent": 15,
            "anniversary_discount_percent": 10,
            "vip_threshold_amount": 50000,
            "email_marketing_enabled": True,
            "sms_marketing_enabled": True,
            "auto_birthday_reminders": True,
            "customer_feedback_enabled": True
        }
        
        # Customer segments
        self.customer_segments = {
            "new": {"name": "Новые Клиенты", "color": "#3498db", "criteria": "< 30 дней"},
            "regular": {"name": "Постоянные Клиенты", "color": "#2ecc71", "criteria": "> 5 заказов"},
            "vip": {"name": "VIP Клиенты", "color": "#f39c12", "criteria": "> 50,000 руб"},
            "inactive": {"name": "Неактивные", "color": "#e74c3c", "criteria": "> 90 дней без заказов"}
        }
        
        # Marketing campaign types
        self.campaign_types = {
            "birthday": "День Рождения",
            "anniversary": "Годовщина",
            "seasonal": "Сезонная Акция",
            "loyalty": "Программа Лояльности",
            "reactivation": "Возврат Клиентов",
            "new_product": "Новое Блюдо"
        }
        
        # Initialize database tables
        self._init_crm_tables()
    
    def _init_crm_tables(self):
        """Initialize CRM-related database tables"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Customers table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS customers (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        customer_code TEXT UNIQUE NOT NULL,
                        first_name TEXT NOT NULL,
                        last_name TEXT NOT NULL,
                        middle_name TEXT,
                        phone TEXT UNIQUE,
                        email TEXT,
                        birth_date DATE,
                        anniversary_date DATE,
                        gender TEXT CHECK(gender IN ('М', 'Ж')),
                        address TEXT,
                        city TEXT DEFAULT 'Москва',
                        postal_code TEXT,
                        registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_visit_date TIMESTAMP,
                        total_visits INTEGER DEFAULT 0,
                        total_spent DECIMAL(10,2) DEFAULT 0.00,
                        loyalty_points INTEGER DEFAULT 0,
                        customer_segment TEXT DEFAULT 'new',
                        preferred_table TEXT,
                        dietary_restrictions TEXT,
                        favorite_dishes TEXT,
                        notes TEXT,
                        marketing_consent BOOLEAN DEFAULT 1,
                        sms_consent BOOLEAN DEFAULT 1,
                        email_consent BOOLEAN DEFAULT 1,
                        is_active BOOLEAN DEFAULT 1,
                        created_by INTEGER,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (created_by) REFERENCES users (id)
                    )
                ''')
                
                # Customer purchase history table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS customer_purchases (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        customer_id INTEGER NOT NULL,
                        order_id INTEGER,
                        purchase_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        total_amount DECIMAL(10,2) NOT NULL,
                        payment_method TEXT,
                        items_purchased TEXT,
                        discount_applied DECIMAL(10,2) DEFAULT 0.00,
                        loyalty_points_earned INTEGER DEFAULT 0,
                        loyalty_points_used INTEGER DEFAULT 0,
                        table_number TEXT,
                        server_name TEXT,
                        rating INTEGER CHECK(rating BETWEEN 1 AND 5),
                        feedback TEXT,
                        FOREIGN KEY (customer_id) REFERENCES customers (id),
                        FOREIGN KEY (order_id) REFERENCES orders (id)
                    )
                ''')
                
                # Customer preferences table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS customer_preferences (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        customer_id INTEGER NOT NULL,
                        preference_type TEXT NOT NULL,
                        preference_value TEXT NOT NULL,
                        preference_notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (customer_id) REFERENCES customers (id)
                    )
                ''')
                
                # Marketing campaigns table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS marketing_campaigns (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        campaign_name TEXT NOT NULL,
                        campaign_type TEXT NOT NULL,
                        description TEXT,
                        target_segment TEXT,
                        start_date DATE NOT NULL,
                        end_date DATE NOT NULL,
                        discount_percent DECIMAL(5,2),
                        discount_amount DECIMAL(10,2),
                        min_order_amount DECIMAL(10,2),
                        max_uses_per_customer INTEGER DEFAULT 1,
                        total_budget DECIMAL(10,2),
                        spent_budget DECIMAL(10,2) DEFAULT 0.00,
                        target_customers_count INTEGER DEFAULT 0,
                        reached_customers_count INTEGER DEFAULT 0,
                        conversion_count INTEGER DEFAULT 0,
                        revenue_generated DECIMAL(10,2) DEFAULT 0.00,
                        campaign_status TEXT DEFAULT 'draft',
                        created_by INTEGER,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (created_by) REFERENCES users (id)
                    )
                ''')
                
                # Customer campaign interactions table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS customer_campaign_interactions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        customer_id INTEGER NOT NULL,
                        campaign_id INTEGER NOT NULL,
                        interaction_type TEXT NOT NULL,
                        interaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        message_sent TEXT,
                        response_received TEXT,
                        conversion_achieved BOOLEAN DEFAULT 0,
                        order_id INTEGER,
                        revenue_generated DECIMAL(10,2) DEFAULT 0.00,
                        FOREIGN KEY (customer_id) REFERENCES customers (id),
                        FOREIGN KEY (campaign_id) REFERENCES marketing_campaigns (id),
                        FOREIGN KEY (order_id) REFERENCES orders (id)
                    )
                ''')
                
                # Customer feedback table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS customer_feedback (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        customer_id INTEGER NOT NULL,
                        feedback_type TEXT NOT NULL,
                        rating INTEGER CHECK(rating BETWEEN 1 AND 5),
                        subject TEXT,
                        message TEXT NOT NULL,
                        order_id INTEGER,
                        feedback_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        response_text TEXT,
                        response_date TIMESTAMP,
                        status TEXT DEFAULT 'new',
                        priority TEXT DEFAULT 'medium',
                        assigned_to INTEGER,
                        FOREIGN KEY (customer_id) REFERENCES customers (id),
                        FOREIGN KEY (order_id) REFERENCES orders (id),
                        FOREIGN KEY (assigned_to) REFERENCES users (id)
                    )
                ''')
                
                conn.commit()
                print("CRM database tables initialized successfully")
                
        except Exception as e:
            print(f"Error initializing CRM tables: {e}")
    
    def show_crm_system(self):
        """Show CRM system window"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
            
        self.create_window()
        self.create_interface()
        self.load_crm_data()
    
    def create_window(self):
        """Create the CRM system window"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("👥 Управление Взаимоотношениями с Клиентами (CRM)")
        self.window.geometry("1600x1000")
        self.window.configure(bg=ModernStyles.COLORS['bg_main'])
        self.window.resizable(True, True)
        self.window.state('zoomed')
        
        # Center window
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # Configure styles
        ModernStyles.configure_ttk_styles(self.window)
    
    def create_interface(self):
        """Create the CRM interface"""
        # Main container
        main_container = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        main_container.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Header
        header_frame = tk.Frame(main_container, bg=ModernStyles.COLORS['bg_main'])
        header_frame.pack(fill='x', pady=(0, 20))
        
        title_label = tk.Label(header_frame, text="👥 Управление Взаимоотношениями с Клиентами",
                              font=('Cambria', 24, 'bold italic'),
                              fg=ModernStyles.COLORS['text_primary'],
                              bg=ModernStyles.COLORS['bg_main'])
        title_label.pack(side='left')
        
        # Quick stats
        self.stats_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['bg_main'])
        self.stats_frame.pack(side='right')
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_container)
        self.notebook.pack(fill='both', expand=True)
        
        # Create tabs
        self.create_customers_tab()
        self.create_purchase_history_tab()
        self.create_marketing_campaigns_tab()
        self.create_customer_segments_tab()
        self.create_feedback_tab()
        self.create_analytics_tab()

    def create_customers_tab(self):
        """Create customers management tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="👥 Клиенты")

        # Customer search and filters
        search_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        search_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(search_frame, text="🔍 Поиск клиентов:",
                font=('Cambria', 14, 'bold'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_secondary']).pack(side='left', padx=5)

        self.customer_search_entry = tk.Entry(search_frame, font=('Cambria', 12), width=30)
        self.customer_search_entry.pack(side='left', padx=5)
        self.customer_search_entry.bind('<KeyRelease>', self.search_customers)

        tk.Button(search_frame, text="➕ Добавить Клиента",
                 command=self.add_new_customer,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=15, pady=8).pack(side='right', padx=5)

        # Customer list
        customers_list_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        customers_list_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Customers treeview
        customer_columns = ('name', 'phone', 'email', 'segment', 'total_spent', 'last_visit', 'loyalty_points')
        self.customers_tree = ttk.Treeview(customers_list_frame, columns=customer_columns, show='headings', height=15)

        # Configure customer columns
        self.customers_tree.heading('name', text='ФИО')
        self.customers_tree.heading('phone', text='Телефон')
        self.customers_tree.heading('email', text='Email')
        self.customers_tree.heading('segment', text='Сегмент')
        self.customers_tree.heading('total_spent', text='Потрачено')
        self.customers_tree.heading('last_visit', text='Последний Визит')
        self.customers_tree.heading('loyalty_points', text='Баллы')

        self.customers_tree.column('name', width=200)
        self.customers_tree.column('phone', width=120)
        self.customers_tree.column('email', width=180)
        self.customers_tree.column('segment', width=120)
        self.customers_tree.column('total_spent', width=120)
        self.customers_tree.column('last_visit', width=150)
        self.customers_tree.column('loyalty_points', width=100)

        # Customer scrollbar
        customer_scrollbar = ttk.Scrollbar(customers_list_frame, orient='vertical', command=self.customers_tree.yview)
        self.customers_tree.configure(yscrollcommand=customer_scrollbar.set)

        self.customers_tree.pack(side='left', fill='both', expand=True)
        customer_scrollbar.pack(side='right', fill='y')

        # Customer action buttons
        customer_buttons_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        customer_buttons_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(customer_buttons_frame, text="✏️ Редактировать",
                 command=self.edit_customer,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 11, 'bold'), relief='flat',
                 padx=15, pady=8).pack(side='left', padx=5)

        tk.Button(customer_buttons_frame, text="📊 История Покупок",
                 command=self.view_customer_history,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 11, 'bold'), relief='flat',
                 padx=15, pady=8).pack(side='left', padx=5)

        tk.Button(customer_buttons_frame, text="💌 Отправить Сообщение",
                 command=self.send_customer_message,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Cambria', 11, 'bold'), relief='flat',
                 padx=15, pady=8).pack(side='left', padx=5)

        tk.Button(customer_buttons_frame, text="🎁 Начислить Баллы",
                 command=self.add_loyalty_points,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 11, 'bold'), relief='flat',
                 padx=15, pady=8).pack(side='left', padx=5)

    def create_purchase_history_tab(self):
        """Create purchase history tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="🛒 История Покупок")

        # Purchase history filters
        filter_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        filter_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(filter_frame, text="📅 Период:",
                font=('Cambria', 12, 'bold'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_secondary']).grid(row=0, column=0, padx=5, pady=5, sticky='w')

        self.history_start_date = tk.Entry(filter_frame, font=('Cambria', 12), width=12)
        self.history_start_date.insert(0, (datetime.now() - timedelta(days=30)).strftime('%d.%m.%Y'))
        self.history_start_date.grid(row=0, column=1, padx=5, pady=5)

        tk.Label(filter_frame, text="—",
                font=('Cambria', 12, 'bold'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_secondary']).grid(row=0, column=2, padx=5, pady=5)

        self.history_end_date = tk.Entry(filter_frame, font=('Cambria', 12), width=12)
        self.history_end_date.insert(0, datetime.now().strftime('%d.%m.%Y'))
        self.history_end_date.grid(row=0, column=3, padx=5, pady=5)

        tk.Button(filter_frame, text="🔍 Применить Фильтр",
                 command=self.filter_purchase_history,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 11, 'bold'), relief='flat',
                 padx=15, pady=6).grid(row=0, column=4, padx=10, pady=5)

        # Purchase history list
        history_list_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        history_list_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Purchase history treeview
        history_columns = ('date', 'customer', 'amount', 'payment_method', 'items', 'points_earned', 'rating')
        self.history_tree = ttk.Treeview(history_list_frame, columns=history_columns, show='headings', height=15)

        # Configure history columns
        self.history_tree.heading('date', text='Дата')
        self.history_tree.heading('customer', text='Клиент')
        self.history_tree.heading('amount', text='Сумма')
        self.history_tree.heading('payment_method', text='Способ Оплаты')
        self.history_tree.heading('items', text='Блюда')
        self.history_tree.heading('points_earned', text='Баллы')
        self.history_tree.heading('rating', text='Оценка')

        self.history_tree.column('date', width=120)
        self.history_tree.column('customer', width=200)
        self.history_tree.column('amount', width=120)
        self.history_tree.column('payment_method', width=150)
        self.history_tree.column('items', width=300)
        self.history_tree.column('points_earned', width=100)
        self.history_tree.column('rating', width=80)

        # History scrollbar
        history_scrollbar = ttk.Scrollbar(history_list_frame, orient='vertical', command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=history_scrollbar.set)

        self.history_tree.pack(side='left', fill='both', expand=True)
        history_scrollbar.pack(side='right', fill='y')

    def create_marketing_campaigns_tab(self):
        """Create marketing campaigns tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="📢 Маркетинговые Кампании")

        # Campaign controls
        campaign_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        campaign_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(campaign_controls_frame, text="➕ Создать Кампанию",
                 command=self.create_marketing_campaign,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(campaign_controls_frame, text="🎂 Дни Рождения",
                 command=self.create_birthday_campaign,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(campaign_controls_frame, text="🔄 Возврат Клиентов",
                 command=self.create_reactivation_campaign,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Campaigns list
        campaigns_list_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        campaigns_list_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Campaigns treeview
        campaign_columns = ('name', 'type', 'status', 'start_date', 'end_date', 'target_segment', 'reached', 'conversion', 'revenue')
        self.campaigns_tree = ttk.Treeview(campaigns_list_frame, columns=campaign_columns, show='headings', height=12)

        # Configure campaign columns
        self.campaigns_tree.heading('name', text='Название')
        self.campaigns_tree.heading('type', text='Тип')
        self.campaigns_tree.heading('status', text='Статус')
        self.campaigns_tree.heading('start_date', text='Начало')
        self.campaigns_tree.heading('end_date', text='Конец')
        self.campaigns_tree.heading('target_segment', text='Сегмент')
        self.campaigns_tree.heading('reached', text='Охват')
        self.campaigns_tree.heading('conversion', text='Конверсия')
        self.campaigns_tree.heading('revenue', text='Доход')

        self.campaigns_tree.column('name', width=200)
        self.campaigns_tree.column('type', width=120)
        self.campaigns_tree.column('status', width=100)
        self.campaigns_tree.column('start_date', width=100)
        self.campaigns_tree.column('end_date', width=100)
        self.campaigns_tree.column('target_segment', width=120)
        self.campaigns_tree.column('reached', width=80)
        self.campaigns_tree.column('conversion', width=100)
        self.campaigns_tree.column('revenue', width=120)

        # Campaign scrollbar
        campaign_scrollbar = ttk.Scrollbar(campaigns_list_frame, orient='vertical', command=self.campaigns_tree.yview)
        self.campaigns_tree.configure(yscrollcommand=campaign_scrollbar.set)

        self.campaigns_tree.pack(side='left', fill='both', expand=True)
        campaign_scrollbar.pack(side='right', fill='y')

    def create_customer_segments_tab(self):
        """Create customer segments analysis tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="📊 Сегменты Клиентов")

        # Segments overview
        segments_overview_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        segments_overview_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(segments_overview_frame, text="📈 Анализ Сегментов Клиентов",
                font=('Cambria', 16, 'bold'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_secondary']).pack(pady=10)

        # Segment cards
        segments_cards_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        segments_cards_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Create segment cards
        self.create_segment_cards(segments_cards_frame)

    def create_segment_cards(self, parent):
        """Create customer segment cards"""
        row = 0
        col = 0

        for segment_key, segment_info in self.customer_segments.items():
            card_frame = tk.Frame(parent, bg=segment_info['color'], relief='raised', bd=2)
            card_frame.grid(row=row, column=col, padx=15, pady=15, sticky='nsew')

            tk.Label(card_frame, text=segment_info['name'],
                    font=('Cambria', 14, 'bold'),
                    fg='white', bg=segment_info['color']).pack(pady=(15, 5))

            tk.Label(card_frame, text="0 клиентов",  # Will be updated with real data
                    font=('Cambria', 18, 'bold'),
                    fg='white', bg=segment_info['color']).pack(pady=(0, 5))

            tk.Label(card_frame, text=segment_info['criteria'],
                    font=('Cambria', 10),
                    fg='white', bg=segment_info['color']).pack(pady=(0, 15))

            col += 1
            if col > 1:
                col = 0
                row += 1

        # Configure grid weights
        for i in range(2):
            parent.columnconfigure(i, weight=1)
        for i in range(2):
            parent.rowconfigure(i, weight=1)

    def create_feedback_tab(self):
        """Create customer feedback tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="💬 Отзывы и Обратная Связь")

        # Feedback filters
        feedback_filter_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        feedback_filter_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(feedback_filter_frame, text="🔍 Фильтр по рейтингу:",
                font=('Cambria', 12, 'bold'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_secondary']).pack(side='left', padx=5)

        self.rating_filter = ttk.Combobox(feedback_filter_frame, values=['Все', '⭐', '⭐⭐', '⭐⭐⭐', '⭐⭐⭐⭐', '⭐⭐⭐⭐⭐'],
                                         font=('Cambria', 11), width=15, state='readonly')
        self.rating_filter.set('Все')
        self.rating_filter.pack(side='left', padx=5)

        tk.Button(feedback_filter_frame, text="🔄 Обновить",
                 command=self.refresh_feedback,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 11, 'bold'), relief='flat',
                 padx=15, pady=8).pack(side='left', padx=10)

        # Feedback list
        feedback_list_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        feedback_list_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Feedback treeview
        feedback_columns = ('date', 'customer', 'rating', 'subject', 'message', 'status')
        self.feedback_tree = ttk.Treeview(feedback_list_frame, columns=feedback_columns, show='headings', height=15)

        # Configure feedback columns
        self.feedback_tree.heading('date', text='Дата')
        self.feedback_tree.heading('customer', text='Клиент')
        self.feedback_tree.heading('rating', text='Рейтинг')
        self.feedback_tree.heading('subject', text='Тема')
        self.feedback_tree.heading('message', text='Сообщение')
        self.feedback_tree.heading('status', text='Статус')

        self.feedback_tree.column('date', width=120)
        self.feedback_tree.column('customer', width=180)
        self.feedback_tree.column('rating', width=80)
        self.feedback_tree.column('subject', width=200)
        self.feedback_tree.column('message', width=300)
        self.feedback_tree.column('status', width=100)

        # Feedback scrollbar
        feedback_scrollbar = ttk.Scrollbar(feedback_list_frame, orient='vertical', command=self.feedback_tree.yview)
        self.feedback_tree.configure(yscrollcommand=feedback_scrollbar.set)

        self.feedback_tree.pack(side='left', fill='both', expand=True)
        feedback_scrollbar.pack(side='right', fill='y')

    def create_analytics_tab(self):
        """Create CRM analytics tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="📈 Аналитика CRM")

        # Analytics dashboard
        analytics_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        analytics_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Key metrics
        metrics_frame = tk.LabelFrame(analytics_frame, text="Ключевые Показатели",
                                    font=('Cambria', 14, 'bold'),
                                    fg=ModernStyles.COLORS['text_primary'],
                                    bg=ModernStyles.COLORS['bg_secondary'])
        metrics_frame.pack(fill='x', pady=(0, 20))

        # Create metrics cards
        metrics_container = tk.Frame(metrics_frame, bg=ModernStyles.COLORS['bg_secondary'])
        metrics_container.pack(fill='x', padx=15, pady=15)

        self.create_metric_card(metrics_container, "Всего Клиентов", "1,234", ModernStyles.COLORS['primary'], 0, 0)
        self.create_metric_card(metrics_container, "Новые за Месяц", "89", ModernStyles.COLORS['success'], 0, 1)
        self.create_metric_card(metrics_container, "Средний Чек", "2,450 руб", ModernStyles.COLORS['info'], 0, 2)
        self.create_metric_card(metrics_container, "Повторные Покупки", "67%", ModernStyles.COLORS['warning'], 1, 0)
        self.create_metric_card(metrics_container, "Активные Кампании", "5", ModernStyles.COLORS['secondary'], 1, 1)
        self.create_metric_card(metrics_container, "Средний Рейтинг", "4.7⭐", ModernStyles.COLORS['success'], 1, 2)

        # Configure grid weights
        for i in range(3):
            metrics_container.columnconfigure(i, weight=1)
        for i in range(2):
            metrics_container.rowconfigure(i, weight=1)

        # Customer lifetime value analysis
        clv_frame = tk.LabelFrame(analytics_frame, text="Анализ Жизненной Ценности Клиента (CLV)",
                                font=('Cambria', 14, 'bold'),
                                fg=ModernStyles.COLORS['text_primary'],
                                bg=ModernStyles.COLORS['bg_secondary'])
        clv_frame.pack(fill='both', expand=True)

        clv_text = tk.Text(clv_frame, height=10, font=('Cambria', 11),
                          bg=ModernStyles.COLORS['bg_main'],
                          fg=ModernStyles.COLORS['text_primary'],
                          wrap='word', state='disabled')
        clv_text.pack(fill='both', expand=True, padx=15, pady=15)

        # Sample CLV analysis
        clv_analysis = """📊 АНАЛИЗ ЖИЗНЕННОЙ ЦЕННОСТИ КЛИЕНТА

🎯 Средняя жизненная ценность клиента: 15,750 руб
📈 Средний период жизни клиента: 18 месяцев
💰 Средняя частота покупок: 2.3 раза в месяц
🛒 Средний размер заказа: 1,850 руб

📋 СЕГМЕНТАЦИЯ ПО CLV:
• VIP клиенты (>50,000 руб): 8% клиентов, 35% дохода
• Постоянные клиенты (15,000-50,000 руб): 25% клиентов, 45% дохода
• Обычные клиенты (5,000-15,000 руб): 45% клиентов, 18% дохода
• Новые клиенты (<5,000 руб): 22% клиентов, 2% дохода

🎯 РЕКОМЕНДАЦИИ:
• Увеличить частоту посещений постоянных клиентов
• Развивать программы лояльности для VIP сегмента
• Создать кампании по возврату неактивных клиентов
• Улучшить опыт новых клиентов для повышения retention
"""

        clv_text.config(state='normal')
        clv_text.insert('1.0', clv_analysis)
        clv_text.config(state='disabled')

    def create_metric_card(self, parent, title, value, color, row, col):
        """Create a metric card"""
        card_frame = tk.Frame(parent, bg=color, relief='raised', bd=2)
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky='nsew')

        tk.Label(card_frame, text=title, font=('Cambria', 12, 'bold'),
                fg='white', bg=color).pack(pady=(15, 5))

        tk.Label(card_frame, text=value, font=('Cambria', 18, 'bold'),
                fg='white', bg=color).pack(pady=(0, 15))

    # Action methods
    def load_crm_data(self):
        """Load CRM data"""
        try:
            self.load_customers_data()
            self.load_purchase_history_data()
            self.load_campaigns_data()
            self.load_feedback_data()
            self.update_quick_stats()
        except Exception as e:
            print(f"Error loading CRM data: {e}")

    def load_customers_data(self):
        """Load customers data"""
        try:
            # Clear existing data
            for item in self.customers_tree.get_children():
                self.customers_tree.delete(item)

            # Sample customers data (in real implementation, load from database)
            customers = [
                ("Иванов Иван Иванович", "+7 (495) 123-45-67", "<EMAIL>", "VIP", "75,250 руб", "15.01.2024", "1,250"),
                ("Петрова Мария Сергеевна", "+7 (495) 234-56-78", "<EMAIL>", "Постоянный", "32,100 руб", "14.01.2024", "650"),
                ("Сидоров Алексей Петрович", "+7 (495) 345-67-89", "<EMAIL>", "Обычный", "15,750 руб", "13.01.2024", "320"),
                ("Козлова Елена Владимировна", "+7 (495) 456-78-90", "<EMAIL>", "Новый", "3,200 руб", "12.01.2024", "65")
            ]

            for customer in customers:
                self.customers_tree.insert('', 'end', values=customer)

        except Exception as e:
            print(f"Error loading customers data: {e}")

    def load_purchase_history_data(self):
        """Load purchase history data"""
        try:
            # Clear existing data
            for item in self.history_tree.get_children():
                self.history_tree.delete(item)

            # Sample purchase history data
            purchases = [
                ("15.01.2024", "Иванов И.И.", "3,250 руб", "Карта", "Стейк, Салат Цезарь, Вино", "65", "⭐⭐⭐⭐⭐"),
                ("14.01.2024", "Петрова М.С.", "1,850 руб", "Наличные", "Паста, Десерт, Кофе", "37", "⭐⭐⭐⭐"),
                ("13.01.2024", "Сидоров А.П.", "2,100 руб", "Карта", "Пицца, Салат, Сок", "42", "⭐⭐⭐⭐⭐"),
                ("12.01.2024", "Козлова Е.В.", "950 руб", "Карта", "Суп, Хлеб, Чай", "19", "⭐⭐⭐")
            ]

            for purchase in purchases:
                self.history_tree.insert('', 'end', values=purchase)

        except Exception as e:
            print(f"Error loading purchase history data: {e}")

    def load_campaigns_data(self):
        """Load marketing campaigns data"""
        try:
            # Clear existing data
            for item in self.campaigns_tree.get_children():
                self.campaigns_tree.delete(item)

            # Sample campaigns data
            campaigns = [
                ("Новогодняя Акция", "Сезонная", "Активна", "01.01.2024", "31.01.2024", "Все", "1,250", "15%", "125,000 руб"),
                ("Дни Рождения Январь", "День Рождения", "Активна", "01.01.2024", "31.01.2024", "Именинники", "89", "45%", "45,000 руб"),
                ("Возврат Клиентов", "Реактивация", "Завершена", "15.12.2023", "15.01.2024", "Неактивные", "456", "12%", "78,000 руб")
            ]

            for campaign in campaigns:
                self.campaigns_tree.insert('', 'end', values=campaign)

        except Exception as e:
            print(f"Error loading campaigns data: {e}")

    def load_feedback_data(self):
        """Load customer feedback data"""
        try:
            # Clear existing data
            for item in self.feedback_tree.get_children():
                self.feedback_tree.delete(item)

            # Sample feedback data
            feedback = [
                ("15.01.2024", "Иванов И.И.", "⭐⭐⭐⭐⭐", "Отличный сервис", "Прекрасный ужин, все понравилось!", "Обработан"),
                ("14.01.2024", "Петрова М.С.", "⭐⭐⭐⭐", "Хорошая еда", "Вкусно, но долго ждали", "Обработан"),
                ("13.01.2024", "Сидоров А.П.", "⭐⭐⭐", "Средне", "Еда нормальная, но шумно", "Новый"),
                ("12.01.2024", "Козлова Е.В.", "⭐⭐⭐⭐⭐", "Превосходно", "Лучший ресторан в городе!", "Обработан")
            ]

            for fb in feedback:
                self.feedback_tree.insert('', 'end', values=fb)

        except Exception as e:
            print(f"Error loading feedback data: {e}")

    def update_quick_stats(self):
        """Update quick statistics in header"""
        try:
            # Clear existing stats
            for widget in self.stats_frame.winfo_children():
                widget.destroy()

            # Create stats labels
            stats = [
                ("👥 Клиентов:", "1,234"),
                ("💰 Средний CLV:", "15,750 руб"),
                ("📈 Конверсия:", "67%"),
                ("⭐ Рейтинг:", "4.7")
            ]

            for i, (label, value) in enumerate(stats):
                stat_frame = tk.Frame(self.stats_frame, bg=ModernStyles.COLORS['bg_main'])
                stat_frame.grid(row=0, column=i, padx=10)

                tk.Label(stat_frame, text=label, font=('Cambria', 10, 'bold'),
                        fg=ModernStyles.COLORS['text_secondary'],
                        bg=ModernStyles.COLORS['bg_main']).pack()

                tk.Label(stat_frame, text=value, font=('Cambria', 14, 'bold'),
                        fg=ModernStyles.COLORS['primary'],
                        bg=ModernStyles.COLORS['bg_main']).pack()

        except Exception as e:
            print(f"Error updating quick stats: {e}")

    # Placeholder action methods (to be implemented)
    def search_customers(self, event=None):
        """Search customers"""
        messagebox.showinfo("Поиск", "Функция поиска клиентов")

    def add_new_customer(self):
        """Add new customer"""
        messagebox.showinfo("Добавление", "Форма добавления нового клиента")

    def edit_customer(self):
        """Edit selected customer"""
        messagebox.showinfo("Редактирование", "Форма редактирования клиента")

    def view_customer_history(self):
        """View customer purchase history"""
        messagebox.showinfo("История", "Детальная история покупок клиента")

    def send_customer_message(self):
        """Send message to customer"""
        messagebox.showinfo("Сообщение", "Отправка сообщения клиенту")

    def add_loyalty_points(self):
        """Add loyalty points to customer"""
        messagebox.showinfo("Баллы", "Начисление бонусных баллов")

    def filter_purchase_history(self):
        """Filter purchase history by date range"""
        messagebox.showinfo("Фильтр", "Применение фильтра по датам")

    def create_marketing_campaign(self):
        """Create new marketing campaign"""
        messagebox.showinfo("Кампания", "Создание новой маркетинговой кампании")

    def create_birthday_campaign(self):
        """Create birthday campaign"""
        messagebox.showinfo("День Рождения", "Создание кампании для именинников")

    def create_reactivation_campaign(self):
        """Create customer reactivation campaign"""
        messagebox.showinfo("Реактивация", "Кампания возврата неактивных клиентов")

    def refresh_feedback(self):
        """Refresh feedback list"""
        self.load_feedback_data()
        messagebox.showinfo("Обновление", "Список отзывов обновлен")

def create_customer_relationship_management(parent, db_manager):
    """Create and show the CRM system"""
    try:
        crm_system = CustomerRelationshipManagement(parent, db_manager)
        crm_system.show_crm_system()
        return crm_system
    except Exception as e:
        messagebox.showerror("Ошибка", f"Не удалось открыть CRM систему: {e}")
        return None
