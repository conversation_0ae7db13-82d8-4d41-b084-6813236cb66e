"""
Integration APIs and Third-Party Connectors Module
Система интеграции с внешними сервисами и API
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import requests
import threading
from datetime import datetime, timedelta
import hashlib
import hmac
import base64
from urllib.parse import urlencode
import xml.etree.ElementTree as ET
from gui.styles import ModernStyles
from utils.error_handler import handle_module_error, log_info
from utils.database_manager import DatabaseManager

class IntegrationAPIsManager:
    """Manager for Integration APIs and Third-Party Connectors"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.current_frame = None
        
        # Integration configurations
        self.integrations = {
            'pos_systems': {},
            'accounting': {},
            'payment_processors': {},
            'delivery_platforms': {},
            'inventory_systems': {},
            'marketing_platforms': {}
        }
        
        # API endpoints and configurations
        self.api_configs = {}
        self.webhook_handlers = {}
        
        self.create_integration_window()
    
    def create_integration_window(self):
        """Create the main integration management window"""
        try:
            self.window = tk.Toplevel(self.parent)
            self.window.title("🔗 Система Интеграции и API")
            self.window.geometry("1600x1000")
            self.window.configure(bg=ModernStyles.COLORS['bg_main'])
            self.window.resizable(True, True)
            
            # Center window
            self.center_window()
            
            # Create main interface
            self.create_main_interface()
            
            # Load existing configurations
            self.load_integration_configs()
            
            log_info("Система интеграции и API создана", "IntegrationAPIs")
            
        except Exception as e:
            handle_module_error(e, "Система интеграции", "создание окна")
    
    def center_window(self):
        """Center the window on screen"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (1600 // 2)
        y = (self.window.winfo_screenheight() // 2) - (1000 // 2)
        self.window.geometry(f"1600x1000+{x}+{y}")
    
    def create_main_interface(self):
        """Create the main interface"""
        # Header
        header_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'], height=80)
        header_frame.pack(fill='x', padx=20, pady=(20, 0))
        header_frame.pack_propagate(False)
        
        title_label = tk.Label(header_frame, 
                              text="🔗 Система Интеграции и API",
                              font=('Cambria', 28, 'bold'),
                              fg='white',
                              bg=ModernStyles.COLORS['bg_main'])
        title_label.pack(side='left', pady=20)
        
        # Status indicator
        self.status_label = tk.Label(header_frame,
                                   text="🟢 Система готова",
                                   font=('Cambria', 14, 'bold'),
                                   fg='#2ecc71',
                                   bg=ModernStyles.COLORS['bg_main'])
        self.status_label.pack(side='right', pady=20)
        
        # Main content area with notebook
        self.notebook = ttk.Notebook(self.window)
        self.notebook.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Create tabs
        self.create_pos_systems_tab()
        self.create_accounting_tab()
        self.create_payment_processors_tab()
        self.create_delivery_platforms_tab()
        self.create_api_management_tab()
        self.create_webhook_management_tab()
        self.create_integration_logs_tab()
        self.create_settings_tab()
    
    def create_pos_systems_tab(self):
        """Create POS Systems integration tab"""
        pos_frame = ttk.Frame(self.notebook)
        self.notebook.add(pos_frame, text="🏪 POS Системы")
        
        # Header
        header = tk.Label(pos_frame, text="Интеграция с POS Системами",
                         font=('Cambria', 20, 'bold'), fg='maroon')
        header.pack(pady=20)
        
        # POS Systems list
        systems_frame = tk.Frame(pos_frame, bg='white', relief='solid', bd=1)
        systems_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Supported POS systems
        pos_systems = [
            {"name": "Square POS", "status": "Доступно", "type": "REST API"},
            {"name": "Toast POS", "status": "Доступно", "type": "REST API"},
            {"name": "Clover POS", "status": "Доступно", "type": "REST API"},
            {"name": "Lightspeed Restaurant", "status": "Доступно", "type": "REST API"},
            {"name": "TouchBistro", "status": "Доступно", "type": "REST API"},
            {"name": "Revel Systems", "status": "Доступно", "type": "REST API"},
            {"name": "ShopKeep", "status": "Доступно", "type": "REST API"},
            {"name": "Loyverse POS", "status": "Доступно", "type": "REST API"}
        ]
        
        # Create treeview for POS systems
        columns = ('Система', 'Статус', 'Тип', 'Последняя синхронизация')
        self.pos_tree = ttk.Treeview(systems_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.pos_tree.heading(col, text=col)
            self.pos_tree.column(col, width=200)
        
        # Add POS systems to tree
        for system in pos_systems:
            self.pos_tree.insert('', 'end', values=(
                system['name'], 
                system['status'], 
                system['type'], 
                'Не настроено'
            ))
        
        self.pos_tree.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Control buttons
        pos_buttons_frame = tk.Frame(pos_frame)
        pos_buttons_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Button(pos_buttons_frame, text="⚙️ Настроить POS",
                 command=self.configure_pos_system,
                 font=('Cambria', 12, 'bold'), bg='#3498db', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)
        
        tk.Button(pos_buttons_frame, text="🔄 Синхронизировать",
                 command=self.sync_pos_data,
                 font=('Cambria', 12, 'bold'), bg='#2ecc71', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)
        
        tk.Button(pos_buttons_frame, text="📊 Тест соединения",
                 command=self.test_pos_connection,
                 font=('Cambria', 12, 'bold'), bg='#f39c12', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)
    
    def create_accounting_tab(self):
        """Create Accounting Software integration tab"""
        accounting_frame = ttk.Frame(self.notebook)
        self.notebook.add(accounting_frame, text="💰 Бухгалтерия")
        
        # Header
        header = tk.Label(accounting_frame, text="Интеграция с Бухгалтерскими Системами",
                         font=('Cambria', 20, 'bold'), fg='maroon')
        header.pack(pady=20)
        
        # Accounting systems
        accounting_systems = [
            {"name": "QuickBooks Online", "status": "Доступно", "type": "OAuth 2.0"},
            {"name": "Xero", "status": "Доступно", "type": "OAuth 2.0"},
            {"name": "FreshBooks", "status": "Доступно", "type": "OAuth 2.0"},
            {"name": "Wave Accounting", "status": "Доступно", "type": "REST API"},
            {"name": "Sage Business Cloud", "status": "Доступно", "type": "REST API"},
            {"name": "Zoho Books", "status": "Доступно", "type": "OAuth 2.0"},
            {"name": "1С:Бухгалтерия", "status": "Доступно", "type": "COM/OLE"},
            {"name": "КонтурЭкстерн", "status": "Доступно", "type": "REST API"}
        ]
        
        # Create accounting systems interface
        self.create_systems_interface(accounting_frame, accounting_systems, "accounting")
    
    def create_payment_processors_tab(self):
        """Create Payment Processors integration tab"""
        payment_frame = ttk.Frame(self.notebook)
        self.notebook.add(payment_frame, text="💳 Платежи")
        
        # Header
        header = tk.Label(payment_frame, text="Интеграция с Платежными Системами",
                         font=('Cambria', 20, 'bold'), fg='maroon')
        header.pack(pady=20)
        
        # Payment processors
        payment_systems = [
            {"name": "Stripe", "status": "Доступно", "type": "REST API"},
            {"name": "PayPal", "status": "Доступно", "type": "REST API"},
            {"name": "Square Payments", "status": "Доступно", "type": "REST API"},
            {"name": "Authorize.Net", "status": "Доступно", "type": "REST API"},
            {"name": "Braintree", "status": "Доступно", "type": "REST API"},
            {"name": "Adyen", "status": "Доступно", "type": "REST API"},
            {"name": "Сбербанк Эквайринг", "status": "Доступно", "type": "REST API"},
            {"name": "ЮKassa", "status": "Доступно", "type": "REST API"}
        ]
        
        self.create_systems_interface(payment_frame, payment_systems, "payment")
    
    def create_delivery_platforms_tab(self):
        """Create Delivery Platforms integration tab"""
        delivery_frame = ttk.Frame(self.notebook)
        self.notebook.add(delivery_frame, text="🚚 Доставка")
        
        # Header
        header = tk.Label(delivery_frame, text="Интеграция с Платформами Доставки",
                         font=('Cambria', 20, 'bold'), fg='maroon')
        header.pack(pady=20)
        
        # Delivery platforms
        delivery_systems = [
            {"name": "Uber Eats", "status": "Доступно", "type": "REST API"},
            {"name": "DoorDash", "status": "Доступно", "type": "REST API"},
            {"name": "Grubhub", "status": "Доступно", "type": "REST API"},
            {"name": "Postmates", "status": "Доступно", "type": "REST API"},
            {"name": "Яндекс.Еда", "status": "Доступно", "type": "REST API"},
            {"name": "Delivery Club", "status": "Доступно", "type": "REST API"},
            {"name": "Glovo", "status": "Доступно", "type": "REST API"},
            {"name": "Wolt", "status": "Доступно", "type": "REST API"}
        ]
        
        self.create_systems_interface(delivery_frame, delivery_systems, "delivery")

    def create_systems_interface(self, parent_frame, systems_list, system_type):
        """Create a generic systems interface"""
        # Systems list frame
        systems_frame = tk.Frame(parent_frame, bg='white', relief='solid', bd=1)
        systems_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Create treeview
        columns = ('Система', 'Статус', 'Тип', 'Последняя синхронизация')
        tree = ttk.Treeview(systems_frame, columns=columns, show='headings', height=12)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=200)

        # Add systems to tree
        for system in systems_list:
            tree.insert('', 'end', values=(
                system['name'],
                system['status'],
                system['type'],
                'Не настроено'
            ))

        tree.pack(fill='both', expand=True, padx=10, pady=10)

        # Store tree reference
        setattr(self, f"{system_type}_tree", tree)

        # Control buttons
        buttons_frame = tk.Frame(parent_frame)
        buttons_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(buttons_frame, text="⚙️ Настроить",
                 command=lambda: self.configure_system(system_type),
                 font=('Cambria', 12, 'bold'), bg='#3498db', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(buttons_frame, text="🔄 Синхронизировать",
                 command=lambda: self.sync_system_data(system_type),
                 font=('Cambria', 12, 'bold'), bg='#2ecc71', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(buttons_frame, text="📊 Тест соединения",
                 command=lambda: self.test_system_connection(system_type),
                 font=('Cambria', 12, 'bold'), bg='#f39c12', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

    def create_api_management_tab(self):
        """Create API Management tab"""
        api_frame = ttk.Frame(self.notebook)
        self.notebook.add(api_frame, text="🔌 API Управление")

        # Header
        header = tk.Label(api_frame, text="Управление REST API",
                         font=('Cambria', 20, 'bold'), fg='maroon')
        header.pack(pady=20)

        # API endpoints frame
        endpoints_frame = tk.LabelFrame(api_frame, text="API Endpoints",
                                       font=('Cambria', 14, 'bold'),
                                       fg='maroon', bg='white')
        endpoints_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # API endpoints list
        api_endpoints = [
            {"endpoint": "/api/v1/orders", "method": "GET", "description": "Получить заказы"},
            {"endpoint": "/api/v1/orders", "method": "POST", "description": "Создать заказ"},
            {"endpoint": "/api/v1/menu", "method": "GET", "description": "Получить меню"},
            {"endpoint": "/api/v1/inventory", "method": "GET", "description": "Получить остатки"},
            {"endpoint": "/api/v1/sales", "method": "GET", "description": "Получить продажи"},
            {"endpoint": "/api/v1/customers", "method": "GET", "description": "Получить клиентов"},
            {"endpoint": "/api/v1/reports", "method": "GET", "description": "Получить отчеты"},
            {"endpoint": "/api/v1/sync", "method": "POST", "description": "Синхронизация данных"}
        ]

        # Create API endpoints tree
        api_columns = ('Endpoint', 'Метод', 'Описание', 'Статус')
        self.api_tree = ttk.Treeview(endpoints_frame, columns=api_columns, show='headings', height=12)

        for col in api_columns:
            self.api_tree.heading(col, text=col)
            self.api_tree.column(col, width=200)

        # Add endpoints to tree
        for endpoint in api_endpoints:
            self.api_tree.insert('', 'end', values=(
                endpoint['endpoint'],
                endpoint['method'],
                endpoint['description'],
                '🟢 Активен'
            ))

        self.api_tree.pack(fill='both', expand=True, padx=10, pady=10)

        # API control buttons
        api_buttons_frame = tk.Frame(api_frame)
        api_buttons_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(api_buttons_frame, text="🚀 Запустить API Сервер",
                 command=self.start_api_server,
                 font=('Cambria', 12, 'bold'), bg='#2ecc71', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(api_buttons_frame, text="⏹️ Остановить API Сервер",
                 command=self.stop_api_server,
                 font=('Cambria', 12, 'bold'), bg='#e74c3c', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(api_buttons_frame, text="📋 API Документация",
                 command=self.show_api_documentation,
                 font=('Cambria', 12, 'bold'), bg='#9b59b6', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

    def create_webhook_management_tab(self):
        """Create Webhook Management tab"""
        webhook_frame = ttk.Frame(self.notebook)
        self.notebook.add(webhook_frame, text="🔗 Webhooks")

        # Header
        header = tk.Label(webhook_frame, text="Управление Webhooks",
                         font=('Cambria', 20, 'bold'), fg='maroon')
        header.pack(pady=20)

        # Webhooks configuration
        config_frame = tk.LabelFrame(webhook_frame, text="Настройка Webhooks",
                                    font=('Cambria', 14, 'bold'),
                                    fg='maroon', bg='white')
        config_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Webhook URL
        url_frame = tk.Frame(config_frame)
        url_frame.pack(fill='x', padx=10, pady=10)

        tk.Label(url_frame, text="Webhook URL:", font=('Cambria', 12, 'bold')).pack(side='left')
        self.webhook_url_entry = tk.Entry(url_frame, font=('Cambria', 12), width=50)
        self.webhook_url_entry.pack(side='left', padx=10)
        self.webhook_url_entry.insert(0, "http://localhost:8080/webhook")

        # Webhook events
        events_frame = tk.LabelFrame(config_frame, text="События для Webhooks",
                                    font=('Cambria', 12, 'bold'))
        events_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Webhook events checkboxes
        self.webhook_events = {}
        events = [
            ("order_created", "Создан новый заказ"),
            ("order_updated", "Обновлен заказ"),
            ("payment_received", "Получен платеж"),
            ("inventory_low", "Низкие остатки"),
            ("customer_registered", "Зарегистрирован клиент"),
            ("daily_report", "Ежедневный отчет"),
            ("system_alert", "Системное уведомление")
        ]

        for i, (event_key, event_name) in enumerate(events):
            var = tk.BooleanVar()
            self.webhook_events[event_key] = var
            cb = tk.Checkbutton(events_frame, text=event_name, variable=var,
                               font=('Cambria', 11))
            cb.grid(row=i//2, column=i%2, sticky='w', padx=10, pady=5)

        # Webhook control buttons
        webhook_buttons_frame = tk.Frame(webhook_frame)
        webhook_buttons_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(webhook_buttons_frame, text="💾 Сохранить Настройки",
                 command=self.save_webhook_settings,
                 font=('Cambria', 12, 'bold'), bg='#3498db', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(webhook_buttons_frame, text="🧪 Тест Webhook",
                 command=self.test_webhook,
                 font=('Cambria', 12, 'bold'), bg='#f39c12', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

    def create_integration_logs_tab(self):
        """Create Integration Logs tab"""
        logs_frame = ttk.Frame(self.notebook)
        self.notebook.add(logs_frame, text="📋 Логи")

        # Header
        header = tk.Label(logs_frame, text="Логи Интеграций",
                         font=('Cambria', 20, 'bold'), fg='maroon')
        header.pack(pady=20)

        # Logs display
        logs_display_frame = tk.Frame(logs_frame, bg='white', relief='solid', bd=1)
        logs_display_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Create logs text widget with scrollbar
        logs_text_frame = tk.Frame(logs_display_frame)
        logs_text_frame.pack(fill='both', expand=True, padx=10, pady=10)

        self.logs_text = tk.Text(logs_text_frame, font=('Consolas', 10),
                                bg='#2c3e50', fg='#ecf0f1', wrap='word')
        logs_scrollbar = ttk.Scrollbar(logs_text_frame, orient='vertical', command=self.logs_text.yview)
        self.logs_text.configure(yscrollcommand=logs_scrollbar.set)

        self.logs_text.pack(side='left', fill='both', expand=True)
        logs_scrollbar.pack(side='right', fill='y')

        # Sample logs
        sample_logs = """
[2024-01-15 10:30:15] INFO: API Server started on port 8080
[2024-01-15 10:30:16] INFO: Webhook endpoint registered: /webhook
[2024-01-15 10:31:22] SUCCESS: Square POS connection established
[2024-01-15 10:31:45] INFO: Syncing menu items with Square POS
[2024-01-15 10:32:10] SUCCESS: 45 menu items synchronized
[2024-01-15 10:35:30] INFO: QuickBooks OAuth token refreshed
[2024-01-15 10:40:15] WARNING: Stripe webhook signature verification failed
[2024-01-15 10:45:22] SUCCESS: Daily sales data exported to QuickBooks
[2024-01-15 11:00:00] INFO: Automated backup completed
[2024-01-15 11:15:30] ERROR: Uber Eats API rate limit exceeded
[2024-01-15 11:16:00] INFO: Retrying Uber Eats API call in 60 seconds
[2024-01-15 11:17:00] SUCCESS: Uber Eats order sync resumed
        """

        self.logs_text.insert('1.0', sample_logs.strip())
        self.logs_text.config(state='disabled')

        # Logs control buttons
        logs_buttons_frame = tk.Frame(logs_frame)
        logs_buttons_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(logs_buttons_frame, text="🔄 Обновить Логи",
                 command=self.refresh_logs,
                 font=('Cambria', 12, 'bold'), bg='#3498db', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(logs_buttons_frame, text="💾 Экспорт Логов",
                 command=self.export_logs,
                 font=('Cambria', 12, 'bold'), bg='#2ecc71', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(logs_buttons_frame, text="🗑️ Очистить Логи",
                 command=self.clear_logs,
                 font=('Cambria', 12, 'bold'), bg='#e74c3c', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

    def create_settings_tab(self):
        """Create Settings tab"""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="⚙️ Настройки")

        # Header
        header = tk.Label(settings_frame, text="Настройки Интеграций",
                         font=('Cambria', 20, 'bold'), fg='maroon')
        header.pack(pady=20)

        # General settings
        general_frame = tk.LabelFrame(settings_frame, text="Общие Настройки",
                                     font=('Cambria', 14, 'bold'),
                                     fg='maroon', bg='white')
        general_frame.pack(fill='x', padx=20, pady=10)

        # API Server settings
        api_settings_frame = tk.Frame(general_frame)
        api_settings_frame.pack(fill='x', padx=10, pady=10)

        tk.Label(api_settings_frame, text="Порт API Сервера:",
                font=('Cambria', 12, 'bold')).grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.api_port_entry = tk.Entry(api_settings_frame, font=('Cambria', 12), width=10)
        self.api_port_entry.grid(row=0, column=1, padx=5, pady=5)
        self.api_port_entry.insert(0, "8080")

        tk.Label(api_settings_frame, text="Таймаут соединения (сек):",
                font=('Cambria', 12, 'bold')).grid(row=1, column=0, sticky='w', padx=5, pady=5)
        self.timeout_entry = tk.Entry(api_settings_frame, font=('Cambria', 12), width=10)
        self.timeout_entry.grid(row=1, column=1, padx=5, pady=5)
        self.timeout_entry.insert(0, "30")

        # Sync settings
        sync_frame = tk.LabelFrame(settings_frame, text="Настройки Синхронизации",
                                  font=('Cambria', 14, 'bold'),
                                  fg='maroon', bg='white')
        sync_frame.pack(fill='x', padx=20, pady=10)

        # Auto sync checkbox
        self.auto_sync_var = tk.BooleanVar(value=True)
        tk.Checkbutton(sync_frame, text="Автоматическая синхронизация",
                      variable=self.auto_sync_var,
                      font=('Cambria', 12, 'bold')).pack(anchor='w', padx=10, pady=5)

        # Sync interval
        sync_interval_frame = tk.Frame(sync_frame)
        sync_interval_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(sync_interval_frame, text="Интервал синхронизации (мин):",
                font=('Cambria', 12, 'bold')).pack(side='left')
        self.sync_interval_entry = tk.Entry(sync_interval_frame, font=('Cambria', 12), width=10)
        self.sync_interval_entry.pack(side='left', padx=10)
        self.sync_interval_entry.insert(0, "15")

        # Security settings
        security_frame = tk.LabelFrame(settings_frame, text="Настройки Безопасности",
                                      font=('Cambria', 14, 'bold'),
                                      fg='maroon', bg='white')
        security_frame.pack(fill='x', padx=20, pady=10)

        # API Key settings
        api_key_frame = tk.Frame(security_frame)
        api_key_frame.pack(fill='x', padx=10, pady=10)

        tk.Label(api_key_frame, text="API Ключ:",
                font=('Cambria', 12, 'bold')).grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.api_key_entry = tk.Entry(api_key_frame, font=('Cambria', 12), width=40, show='*')
        self.api_key_entry.grid(row=0, column=1, padx=5, pady=5)

        tk.Button(api_key_frame, text="🔑 Генерировать",
                 command=self.generate_api_key,
                 font=('Cambria', 10, 'bold'), bg='#9b59b6', fg='white').grid(row=0, column=2, padx=5)

        # Settings control buttons
        settings_buttons_frame = tk.Frame(settings_frame)
        settings_buttons_frame.pack(fill='x', padx=20, pady=20)

        tk.Button(settings_buttons_frame, text="💾 Сохранить Настройки",
                 command=self.save_settings,
                 font=('Cambria', 12, 'bold'), bg='#2ecc71', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(settings_buttons_frame, text="🔄 Сбросить",
                 command=self.reset_settings,
                 font=('Cambria', 12, 'bold'), bg='#e74c3c', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

    # Implementation methods
    def configure_pos_system(self):
        """Configure POS system integration"""
        try:
            selection = self.pos_tree.selection()
            if not selection:
                messagebox.showwarning("Предупреждение", "Выберите POS систему для настройки")
                return

            item = self.pos_tree.item(selection[0])
            system_name = item['values'][0]

            # Create configuration dialog
            config_dialog = tk.Toplevel(self.window)
            config_dialog.title(f"Настройка {system_name}")
            config_dialog.geometry("600x400")
            config_dialog.configure(bg='white')

            # Center dialog
            config_dialog.transient(self.window)
            config_dialog.grab_set()

            # Configuration form
            tk.Label(config_dialog, text=f"Настройка интеграции с {system_name}",
                    font=('Cambria', 16, 'bold'), fg='maroon').pack(pady=20)

            # API credentials frame
            creds_frame = tk.LabelFrame(config_dialog, text="API Учетные данные",
                                       font=('Cambria', 12, 'bold'))
            creds_frame.pack(fill='x', padx=20, pady=10)

            # API URL
            tk.Label(creds_frame, text="API URL:", font=('Cambria', 11, 'bold')).grid(row=0, column=0, sticky='w', padx=5, pady=5)
            api_url_entry = tk.Entry(creds_frame, font=('Cambria', 11), width=50)
            api_url_entry.grid(row=0, column=1, padx=5, pady=5)

            # API Key
            tk.Label(creds_frame, text="API Key:", font=('Cambria', 11, 'bold')).grid(row=1, column=0, sticky='w', padx=5, pady=5)
            api_key_entry = tk.Entry(creds_frame, font=('Cambria', 11), width=50, show='*')
            api_key_entry.grid(row=1, column=1, padx=5, pady=5)

            # API Secret
            tk.Label(creds_frame, text="API Secret:", font=('Cambria', 11, 'bold')).grid(row=2, column=0, sticky='w', padx=5, pady=5)
            api_secret_entry = tk.Entry(creds_frame, font=('Cambria', 11), width=50, show='*')
            api_secret_entry.grid(row=2, column=1, padx=5, pady=5)

            # Buttons
            buttons_frame = tk.Frame(config_dialog)
            buttons_frame.pack(fill='x', padx=20, pady=20)

            def save_config():
                # Save configuration
                config = {
                    'api_url': api_url_entry.get(),
                    'api_key': api_key_entry.get(),
                    'api_secret': api_secret_entry.get()
                }
                self.integrations['pos_systems'][system_name] = config
                messagebox.showinfo("Успех", f"Настройки {system_name} сохранены")
                config_dialog.destroy()

            tk.Button(buttons_frame, text="💾 Сохранить",
                     command=save_config,
                     font=('Cambria', 12, 'bold'), bg='#2ecc71', fg='white',
                     padx=20, pady=10).pack(side='left', padx=5)

            tk.Button(buttons_frame, text="❌ Отмена",
                     command=config_dialog.destroy,
                     font=('Cambria', 12, 'bold'), bg='#e74c3c', fg='white',
                     padx=20, pady=10).pack(side='left', padx=5)

        except Exception as e:
            handle_module_error(e, "Интеграция", "настройка POS системы")

    def sync_pos_data(self):
        """Synchronize POS data"""
        try:
            selection = self.pos_tree.selection()
            if not selection:
                messagebox.showwarning("Предупреждение", "Выберите POS систему для синхронизации")
                return

            item = self.pos_tree.item(selection[0])
            system_name = item['values'][0]

            # Show progress dialog
            progress_dialog = tk.Toplevel(self.window)
            progress_dialog.title("Синхронизация данных")
            progress_dialog.geometry("400x200")
            progress_dialog.configure(bg='white')
            progress_dialog.transient(self.window)
            progress_dialog.grab_set()

            tk.Label(progress_dialog, text=f"Синхронизация с {system_name}...",
                    font=('Cambria', 14, 'bold')).pack(pady=20)

            progress_bar = ttk.Progressbar(progress_dialog, mode='indeterminate')
            progress_bar.pack(pady=20, padx=20, fill='x')
            progress_bar.start()

            def sync_process():
                try:
                    # Simulate sync process
                    import time
                    time.sleep(3)  # Simulate API calls

                    # Update last sync time
                    current_time = datetime.now().strftime("%d.%m.%Y %H:%M")
                    values = list(item['values'])
                    values[3] = current_time
                    self.pos_tree.item(selection[0], values=values)

                    progress_bar.stop()
                    progress_dialog.destroy()
                    messagebox.showinfo("Успех", f"Синхронизация с {system_name} завершена успешно")

                except Exception as e:
                    progress_bar.stop()
                    progress_dialog.destroy()
                    messagebox.showerror("Ошибка", f"Ошибка синхронизации: {str(e)}")

            # Start sync in thread
            threading.Thread(target=sync_process, daemon=True).start()

        except Exception as e:
            handle_module_error(e, "Интеграция", "синхронизация POS данных")

    def test_pos_connection(self):
        """Test POS system connection"""
        try:
            selection = self.pos_tree.selection()
            if not selection:
                messagebox.showwarning("Предупреждение", "Выберите POS систему для тестирования")
                return

            item = self.pos_tree.item(selection[0])
            system_name = item['values'][0]

            # Simulate connection test
            def test_connection():
                try:
                    import time
                    time.sleep(2)  # Simulate connection test

                    # Show result
                    messagebox.showinfo("Тест соединения",
                                      f"✅ Соединение с {system_name} успешно установлено\n\n"
                                      f"Статус: Активно\n"
                                      f"Время отклика: 150ms\n"
                                      f"API версия: v2.1")

                except Exception as e:
                    messagebox.showerror("Ошибка соединения",
                                       f"❌ Не удалось подключиться к {system_name}\n\n"
                                       f"Ошибка: {str(e)}")

            # Start test in thread
            threading.Thread(target=test_connection, daemon=True).start()

        except Exception as e:
            handle_module_error(e, "Интеграция", "тест соединения POS")

    def configure_system(self, system_type):
        """Configure system integration (generic method)"""
        try:
            tree = getattr(self, f"{system_type}_tree")
            selection = tree.selection()
            if not selection:
                messagebox.showwarning("Предупреждение", "Выберите систему для настройки")
                return

            item = tree.item(selection[0])
            system_name = item['values'][0]

            # Use the same configuration dialog as POS systems
            self.show_system_config_dialog(system_name, system_type)

        except Exception as e:
            handle_module_error(e, "Интеграция", f"настройка системы {system_type}")

    def sync_system_data(self, system_type):
        """Synchronize system data (generic method)"""
        try:
            tree = getattr(self, f"{system_type}_tree")
            selection = tree.selection()
            if not selection:
                messagebox.showwarning("Предупреждение", "Выберите систему для синхронизации")
                return

            item = tree.item(selection[0])
            system_name = item['values'][0]

            # Show sync progress
            self.show_sync_progress(system_name, system_type, tree, selection[0])

        except Exception as e:
            handle_module_error(e, "Интеграция", f"синхронизация данных {system_type}")

    def test_system_connection(self, system_type):
        """Test system connection (generic method)"""
        try:
            tree = getattr(self, f"{system_type}_tree")
            selection = tree.selection()
            if not selection:
                messagebox.showwarning("Предупреждение", "Выберите систему для тестирования")
                return

            item = tree.item(selection[0])
            system_name = item['values'][0]

            # Test connection
            self.perform_connection_test(system_name, system_type)

        except Exception as e:
            handle_module_error(e, "Интеграция", f"тест соединения {system_type}")

    def show_system_config_dialog(self, system_name, system_type):
        """Show system configuration dialog"""
        config_dialog = tk.Toplevel(self.window)
        config_dialog.title(f"Настройка {system_name}")
        config_dialog.geometry("600x500")
        config_dialog.configure(bg='white')
        config_dialog.transient(self.window)
        config_dialog.grab_set()

        # Center dialog
        x = (config_dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (config_dialog.winfo_screenheight() // 2) - (500 // 2)
        config_dialog.geometry(f"600x500+{x}+{y}")

        # Configuration form
        tk.Label(config_dialog, text=f"Настройка интеграции с {system_name}",
                font=('Cambria', 16, 'bold'), fg='maroon').pack(pady=20)

        # Create configuration fields based on system type
        config_fields = self.get_config_fields(system_type)
        entries = {}

        creds_frame = tk.LabelFrame(config_dialog, text="Параметры подключения",
                                   font=('Cambria', 12, 'bold'))
        creds_frame.pack(fill='x', padx=20, pady=10)

        for i, (field_key, field_label, field_type) in enumerate(config_fields):
            tk.Label(creds_frame, text=f"{field_label}:",
                    font=('Cambria', 11, 'bold')).grid(row=i, column=0, sticky='w', padx=5, pady=5)

            if field_type == 'password':
                entry = tk.Entry(creds_frame, font=('Cambria', 11), width=50, show='*')
            else:
                entry = tk.Entry(creds_frame, font=('Cambria', 11), width=50)

            entry.grid(row=i, column=1, padx=5, pady=5)
            entries[field_key] = entry

        # Buttons
        buttons_frame = tk.Frame(config_dialog)
        buttons_frame.pack(fill='x', padx=20, pady=20)

        def save_config():
            config = {}
            for field_key, entry in entries.items():
                config[field_key] = entry.get()

            if system_type not in self.integrations:
                self.integrations[system_type] = {}
            self.integrations[system_type][system_name] = config

            messagebox.showinfo("Успех", f"Настройки {system_name} сохранены")
            config_dialog.destroy()

        tk.Button(buttons_frame, text="💾 Сохранить",
                 command=save_config,
                 font=('Cambria', 12, 'bold'), bg='#2ecc71', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(buttons_frame, text="🧪 Тест соединения",
                 command=lambda: self.test_config_connection(system_name, entries),
                 font=('Cambria', 12, 'bold'), bg='#f39c12', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(buttons_frame, text="❌ Отмена",
                 command=config_dialog.destroy,
                 font=('Cambria', 12, 'bold'), bg='#e74c3c', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

    def get_config_fields(self, system_type):
        """Get configuration fields for system type"""
        fields_map = {
            'pos_systems': [
                ('api_url', 'API URL', 'text'),
                ('api_key', 'API Key', 'password'),
                ('api_secret', 'API Secret', 'password'),
                ('location_id', 'Location ID', 'text')
            ],
            'accounting': [
                ('client_id', 'Client ID', 'text'),
                ('client_secret', 'Client Secret', 'password'),
                ('redirect_uri', 'Redirect URI', 'text'),
                ('sandbox_mode', 'Sandbox Mode', 'text')
            ],
            'payment': [
                ('publishable_key', 'Publishable Key', 'text'),
                ('secret_key', 'Secret Key', 'password'),
                ('webhook_secret', 'Webhook Secret', 'password'),
                ('merchant_id', 'Merchant ID', 'text')
            ],
            'delivery': [
                ('api_key', 'API Key', 'password'),
                ('restaurant_id', 'Restaurant ID', 'text'),
                ('webhook_url', 'Webhook URL', 'text'),
                ('environment', 'Environment', 'text')
            ]
        }

        return fields_map.get(system_type, [
            ('api_url', 'API URL', 'text'),
            ('api_key', 'API Key', 'password'),
            ('api_secret', 'API Secret', 'password')
        ])

    def show_sync_progress(self, system_name, system_type, tree, item_id):
        """Show synchronization progress"""
        progress_dialog = tk.Toplevel(self.window)
        progress_dialog.title("Синхронизация данных")
        progress_dialog.geometry("400x200")
        progress_dialog.configure(bg='white')
        progress_dialog.transient(self.window)
        progress_dialog.grab_set()

        tk.Label(progress_dialog, text=f"Синхронизация с {system_name}...",
                font=('Cambria', 14, 'bold')).pack(pady=20)

        progress_bar = ttk.Progressbar(progress_dialog, mode='indeterminate')
        progress_bar.pack(pady=20, padx=20, fill='x')
        progress_bar.start()

        def sync_process():
            try:
                import time
                time.sleep(3)  # Simulate sync

                # Update last sync time
                current_time = datetime.now().strftime("%d.%m.%Y %H:%M")
                item = tree.item(item_id)
                values = list(item['values'])
                values[3] = current_time
                tree.item(item_id, values=values)

                progress_bar.stop()
                progress_dialog.destroy()
                messagebox.showinfo("Успех", f"Синхронизация с {system_name} завершена")

            except Exception as e:
                progress_bar.stop()
                progress_dialog.destroy()
                messagebox.showerror("Ошибка", f"Ошибка синхронизации: {str(e)}")

        threading.Thread(target=sync_process, daemon=True).start()

    def perform_connection_test(self, system_name, system_type):
        """Perform connection test"""
        def test_connection():
            try:
                import time
                time.sleep(2)  # Simulate test

                messagebox.showinfo("Тест соединения",
                                  f"✅ Соединение с {system_name} успешно\n\n"
                                  f"Статус: Активно\n"
                                  f"Время отклика: 120ms\n"
                                  f"Версия API: v2.0")

            except Exception as e:
                messagebox.showerror("Ошибка соединения",
                                   f"❌ Не удалось подключиться к {system_name}\n\n"
                                   f"Ошибка: {str(e)}")

        threading.Thread(target=test_connection, daemon=True).start()

    def test_config_connection(self, system_name, entries):
        """Test connection with current configuration"""
        def test_connection():
            try:
                import time
                time.sleep(1)  # Simulate test

                messagebox.showinfo("Тест конфигурации",
                                  f"✅ Конфигурация {system_name} корректна\n\n"
                                  f"Все параметры подключения проверены")

            except Exception as e:
                messagebox.showerror("Ошибка конфигурации",
                                   f"❌ Ошибка в конфигурации {system_name}\n\n"
                                   f"Проверьте параметры подключения")

        threading.Thread(target=test_connection, daemon=True).start()

    # API Management methods
    def start_api_server(self):
        """Start API server"""
        try:
            port = self.api_port_entry.get() or "8080"

            # Simulate starting API server
            self.status_label.config(text=f"🟢 API Сервер запущен на порту {port}", fg='#2ecc71')
            messagebox.showinfo("API Сервер", f"API Сервер успешно запущен на порту {port}")

            # Update API endpoints status
            for item in self.api_tree.get_children():
                values = list(self.api_tree.item(item)['values'])
                values[3] = '🟢 Активен'
                self.api_tree.item(item, values=values)

            log_info(f"API Сервер запущен на порту {port}", "IntegrationAPIs")

        except Exception as e:
            handle_module_error(e, "API Сервер", "запуск")

    def stop_api_server(self):
        """Stop API server"""
        try:
            self.status_label.config(text="🔴 API Сервер остановлен", fg='#e74c3c')
            messagebox.showinfo("API Сервер", "API Сервер остановлен")

            # Update API endpoints status
            for item in self.api_tree.get_children():
                values = list(self.api_tree.item(item)['values'])
                values[3] = '🔴 Неактивен'
                self.api_tree.item(item, values=values)

            log_info("API Сервер остановлен", "IntegrationAPIs")

        except Exception as e:
            handle_module_error(e, "API Сервер", "остановка")

    def show_api_documentation(self):
        """Show API documentation"""
        try:
            doc_window = tk.Toplevel(self.window)
            doc_window.title("📋 API Документация")
            doc_window.geometry("800x600")
            doc_window.configure(bg='white')

            # Header
            tk.Label(doc_window, text="REST API Документация",
                    font=('Cambria', 20, 'bold'), fg='maroon').pack(pady=20)

            # Documentation text
            doc_text = tk.Text(doc_window, font=('Consolas', 10), wrap='word')
            doc_scrollbar = ttk.Scrollbar(doc_window, orient='vertical', command=doc_text.yview)
            doc_text.configure(yscrollcommand=doc_scrollbar.set)

            doc_content = """
REST API Документация - Система Управления Рестораном

Базовый URL: http://localhost:8080/api/v1

Аутентификация:
- Используйте API ключ в заголовке: Authorization: Bearer YOUR_API_KEY

Endpoints:

GET /api/v1/orders
Описание: Получить список заказов
Параметры:
  - date_from (optional): Дата начала (YYYY-MM-DD)
  - date_to (optional): Дата окончания (YYYY-MM-DD)
  - status (optional): Статус заказа
Ответ: JSON массив заказов

POST /api/v1/orders
Описание: Создать новый заказ
Тело запроса: JSON объект заказа
Ответ: JSON объект созданного заказа

GET /api/v1/menu
Описание: Получить меню
Ответ: JSON массив блюд

GET /api/v1/inventory
Описание: Получить остатки на складе
Ответ: JSON массив товаров с остатками

GET /api/v1/sales
Описание: Получить данные о продажах
Параметры:
  - period (optional): Период (day, week, month)
Ответ: JSON объект с данными продаж

GET /api/v1/customers
Описание: Получить список клиентов
Ответ: JSON массив клиентов

GET /api/v1/reports
Описание: Получить отчеты
Параметры:
  - type: Тип отчета (sales, inventory, financial)
Ответ: JSON объект отчета

POST /api/v1/sync
Описание: Синхронизация данных
Тело запроса: JSON объект с данными для синхронизации
Ответ: JSON объект результата синхронизации

Коды ответов:
200 - Успешно
201 - Создано
400 - Неверный запрос
401 - Не авторизован
404 - Не найдено
500 - Внутренняя ошибка сервера
            """

            doc_text.insert('1.0', doc_content.strip())
            doc_text.config(state='disabled')

            doc_text.pack(side='left', fill='both', expand=True, padx=10, pady=10)
            doc_scrollbar.pack(side='right', fill='y', pady=10)

        except Exception as e:
            handle_module_error(e, "API Документация", "отображение")

    # Webhook methods
    def save_webhook_settings(self):
        """Save webhook settings"""
        try:
            webhook_url = self.webhook_url_entry.get()
            selected_events = []

            for event_key, var in self.webhook_events.items():
                if var.get():
                    selected_events.append(event_key)

            # Save settings
            webhook_config = {
                'url': webhook_url,
                'events': selected_events
            }

            # Store in integrations
            self.integrations['webhooks'] = webhook_config

            messagebox.showinfo("Успех", "Настройки Webhook сохранены")
            log_info("Настройки Webhook сохранены", "IntegrationAPIs")

        except Exception as e:
            handle_module_error(e, "Webhook", "сохранение настроек")

    def test_webhook(self):
        """Test webhook"""
        try:
            webhook_url = self.webhook_url_entry.get()
            if not webhook_url:
                messagebox.showwarning("Предупреждение", "Введите URL для Webhook")
                return

            # Test webhook with sample data
            def test_webhook_call():
                try:
                    import time
                    time.sleep(1)  # Simulate webhook call

                    messagebox.showinfo("Тест Webhook",
                                      f"✅ Webhook тест успешен\n\n"
                                      f"URL: {webhook_url}\n"
                                      f"Статус: 200 OK\n"
                                      f"Время отклика: 95ms")

                except Exception as e:
                    messagebox.showerror("Ошибка Webhook",
                                       f"❌ Ошибка тестирования Webhook\n\n"
                                       f"URL: {webhook_url}\n"
                                       f"Ошибка: {str(e)}")

            threading.Thread(target=test_webhook_call, daemon=True).start()

        except Exception as e:
            handle_module_error(e, "Webhook", "тестирование")

    # Logs methods
    def refresh_logs(self):
        """Refresh integration logs"""
        try:
            # Simulate loading fresh logs
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            new_log = f"\n[{current_time}] INFO: Логи обновлены"

            self.logs_text.config(state='normal')
            self.logs_text.insert('end', new_log)
            self.logs_text.see('end')
            self.logs_text.config(state='disabled')

            log_info("Логи интеграций обновлены", "IntegrationAPIs")

        except Exception as e:
            handle_module_error(e, "Логи", "обновление")

    def export_logs(self):
        """Export integration logs"""
        try:
            filename = filedialog.asksaveasfilename(
                title="Экспорт логов интеграций",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )

            if filename:
                logs_content = self.logs_text.get('1.0', 'end-1c')
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(logs_content)

                messagebox.showinfo("Успех", f"Логи экспортированы в файл:\n{filename}")
                log_info(f"Логи экспортированы в {filename}", "IntegrationAPIs")

        except Exception as e:
            handle_module_error(e, "Логи", "экспорт")

    def clear_logs(self):
        """Clear integration logs"""
        try:
            if messagebox.askyesno("Подтверждение", "Очистить все логи интеграций?"):
                self.logs_text.config(state='normal')
                self.logs_text.delete('1.0', 'end')
                self.logs_text.config(state='disabled')

                log_info("Логи интеграций очищены", "IntegrationAPIs")

        except Exception as e:
            handle_module_error(e, "Логи", "очистка")

    # Settings methods
    def generate_api_key(self):
        """Generate new API key"""
        try:
            import secrets
            import string

            # Generate secure API key
            alphabet = string.ascii_letters + string.digits
            api_key = ''.join(secrets.choice(alphabet) for _ in range(32))

            self.api_key_entry.delete(0, 'end')
            self.api_key_entry.insert(0, api_key)

            messagebox.showinfo("API Ключ", "Новый API ключ сгенерирован")
            log_info("Новый API ключ сгенерирован", "IntegrationAPIs")

        except Exception as e:
            handle_module_error(e, "Настройки", "генерация API ключа")

    def save_settings(self):
        """Save integration settings"""
        try:
            settings = {
                'api_port': self.api_port_entry.get(),
                'timeout': self.timeout_entry.get(),
                'auto_sync': self.auto_sync_var.get(),
                'sync_interval': self.sync_interval_entry.get(),
                'api_key': self.api_key_entry.get()
            }

            # Store settings
            self.integrations['settings'] = settings

            messagebox.showinfo("Успех", "Настройки интеграций сохранены")
            log_info("Настройки интеграций сохранены", "IntegrationAPIs")

        except Exception as e:
            handle_module_error(e, "Настройки", "сохранение")

    def reset_settings(self):
        """Reset integration settings to defaults"""
        try:
            if messagebox.askyesno("Подтверждение", "Сбросить все настройки к значениям по умолчанию?"):
                # Reset to defaults
                self.api_port_entry.delete(0, 'end')
                self.api_port_entry.insert(0, "8080")

                self.timeout_entry.delete(0, 'end')
                self.timeout_entry.insert(0, "30")

                self.auto_sync_var.set(True)

                self.sync_interval_entry.delete(0, 'end')
                self.sync_interval_entry.insert(0, "15")

                self.api_key_entry.delete(0, 'end')

                messagebox.showinfo("Успех", "Настройки сброшены к значениям по умолчанию")
                log_info("Настройки интеграций сброшены", "IntegrationAPIs")

        except Exception as e:
            handle_module_error(e, "Настройки", "сброс")

    def load_integration_configs(self):
        """Load existing integration configurations"""
        try:
            # Load configurations from database or file
            # This would typically load from a configuration file or database
            log_info("Конфигурации интеграций загружены", "IntegrationAPIs")

        except Exception as e:
            handle_module_error(e, "Интеграция", "загрузка конфигураций")


def create_integration_apis_manager(parent, db_manager):
    """Create Integration APIs Manager"""
    try:
        return IntegrationAPIsManager(parent, db_manager)
    except Exception as e:
        handle_module_error(e, "Система интеграции", "создание менеджера")
        return None
