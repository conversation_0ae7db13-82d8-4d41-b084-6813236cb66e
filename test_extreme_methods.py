#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ТЕСТИРОВАНИЕ ВСЕХ ЭКСТРЕМАЛЬНЫХ МЕТОДОВ ПЕРЕКЛЮЧЕНИЯ РАСКЛАДКИ
Этот скрипт напрямую тестирует все 12 экстремальных методов
"""

import subprocess
import sys
import os
import time
import threading

def test_extreme_method_10():
    """Тест экстремального хука"""
    print("\n" + "="*80)
    print("☢️ ТЕСТИРОВАНИЕ МЕТОДА 10: ЭКСТРЕМАЛЬНЫЙ ХУК")
    print("="*80)
    
    if os.path.exists("extreme_keyboard_hook.py"):
        print("✅ Файл extreme_keyboard_hook.py найден")
        
        def run_hook():
            try:
                result = subprocess.run([sys.executable, "extreme_keyboard_hook.py"], 
                                      input="y\n", text=True, timeout=10, capture_output=True)
                print(f"Результат хука: {result.returncode}")
                if result.stdout:
                    print(f"Вывод: {result.stdout}")
                if result.stderr:
                    print(f"Ошибки: {result.stderr}")
            except subprocess.TimeoutExpired:
                print("⏰ Хук работает в фоновом режиме (таймаут)")
            except Exception as e:
                print(f"❌ Ошибка запуска хука: {e}")
        
        # Запускаем в отдельном потоке
        hook_thread = threading.Thread(target=run_hook, daemon=True)
        hook_thread.start()
        
        print("🚀 Хук запущен в фоновом режиме на 3 секунды...")
        time.sleep(3)
        print("✅ Тест хука завершен")
        
    else:
        print("❌ Файл extreme_keyboard_hook.py не найден")

def test_extreme_method_11():
    """Тест ядерного подхода"""
    print("\n" + "="*80)
    print("☢️ ТЕСТИРОВАНИЕ МЕТОДА 11: ЯДЕРНЫЙ ПОДХОД")
    print("="*80)
    
    if os.path.exists("nuclear_keyboard_switch.py"):
        print("✅ Файл nuclear_keyboard_switch.py найден")
        
        try:
            result = subprocess.run([sys.executable, "nuclear_keyboard_switch.py"], 
                                  input="yes\n", text=True, timeout=10, capture_output=True)
            print(f"Результат ядерного подхода: {result.returncode}")
            if result.stdout:
                print(f"Вывод: {result.stdout}")
            if result.stderr:
                print(f"Ошибки: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print("⏰ Ядерный подход работает (таймаут)")
        except Exception as e:
            print(f"❌ Ошибка запуска ядерного подхода: {e}")
            
    else:
        print("❌ Файл nuclear_keyboard_switch.py не найден")

def test_extreme_method_12():
    """Тест создания драйвера"""
    print("\n" + "="*80)
    print("☢️ ТЕСТИРОВАНИЕ МЕТОДА 12: СОЗДАНИЕ ДРАЙВЕРА")
    print("="*80)
    
    if os.path.exists("ultimate_driver_approach.py"):
        print("✅ Файл ultimate_driver_approach.py найден")
        
        try:
            result = subprocess.run([sys.executable, "ultimate_driver_approach.py"], 
                                  input="y\n", text=True, timeout=30, capture_output=True)
            print(f"Результат создания драйвера: {result.returncode}")
            if result.stdout:
                print(f"Вывод: {result.stdout}")
            if result.stderr:
                print(f"Ошибки: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print("⏰ Создание драйвера работает (таймаут)")
        except Exception as e:
            print(f"❌ Ошибка создания драйвера: {e}")
            
    else:
        print("❌ Файл ultimate_driver_approach.py не найден")

def check_current_layout():
    """Проверить текущую раскладку"""
    try:
        import ctypes
        user32 = ctypes.windll.user32
        hwnd = user32.GetForegroundWindow()
        thread_id = user32.GetWindowThreadProcessId(hwnd, None)
        hkl = user32.GetKeyboardLayout(thread_id)
        layout_id = hkl & 0xFFFF
        
        print(f"\n🔍 ТЕКУЩАЯ РАСКЛАДКА: {hex(layout_id)}")
        if layout_id == 0x0419:
            print("✅ РУССКАЯ РАСКЛАДКА АКТИВНА!")
            return True
        else:
            print("❌ Раскладка НЕ русская")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка проверки раскладки: {e}")
        return False

def main():
    """Главная функция тестирования"""
    print("🚀 ТЕСТИРОВАНИЕ ВСЕХ ЭКСТРЕМАЛЬНЫХ МЕТОДОВ ПЕРЕКЛЮЧЕНИЯ РАСКЛАДКИ")
    print("="*80)
    print("Этот скрипт протестирует все 3 экстремальных метода:")
    print("- Метод 10: Экстремальный системный хук")
    print("- Метод 11: Ядерный подход с модификацией системы")
    print("- Метод 12: Создание драйвера клавиатуры")
    print("="*80)
    
    # Проверяем начальную раскладку
    print("\n📋 НАЧАЛЬНАЯ ПРОВЕРКА:")
    initial_russian = check_current_layout()
    
    # Тестируем все экстремальные методы
    test_extreme_method_10()
    test_extreme_method_11() 
    test_extreme_method_12()
    
    # Финальная проверка
    print("\n📋 ФИНАЛЬНАЯ ПРОВЕРКА:")
    final_russian = check_current_layout()
    
    # Результаты
    print("\n" + "="*80)
    print("📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print("="*80)
    print(f"Начальная раскладка русская: {'✅ ДА' if initial_russian else '❌ НЕТ'}")
    print(f"Финальная раскладка русская: {'✅ ДА' if final_russian else '❌ НЕТ'}")
    
    if not initial_russian and final_russian:
        print("🎉 УСПЕХ! Один из экстремальных методов сработал!")
    elif initial_russian and final_russian:
        print("ℹ️ Раскладка уже была русской")
    else:
        print("💀 ВСЕ ЭКСТРЕМАЛЬНЫЕ МЕТОДЫ НЕ СРАБОТАЛИ")
        print("Это окончательно подтверждает абсолютную защиту Windows")
    
    print("="*80)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Тестирование прервано пользователем")
    except Exception as e:
        print(f"\n❌ Критическая ошибка: {e}")
    
    input("\nНажмите Enter для выхода...")
