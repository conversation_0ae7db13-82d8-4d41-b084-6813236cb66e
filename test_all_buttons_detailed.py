#!/usr/bin/env python3
"""
Детальный тест всех кнопок главного меню
Проверяет каждую кнопку и показывает подробные результаты
"""

import sys
import os
import tkinter as tk
from tkinter import ttk
import traceback
import time

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_all_buttons():
    """Тестирует все кнопки главного меню"""
    print("🧪 ДЕТАЛЬНЫЙ ТЕСТ ВСЕХ КНОПОК ГЛАВНОГО МЕНЮ")
    print("=" * 60)
    
    try:
        # Import required modules
        from gui.main_window import MainWindow
        from database.db_manager import DatabaseManager
        print("✅ Модули импортированы успешно")
        
        # Create test database
        test_db_path = "test_restaurant.db"
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
        
        db_manager = DatabaseManager(test_db_path)
        print("✅ Тестовая база данных создана")
        
        # Create main window
        root = tk.Tk()
        root.withdraw()  # Hide main window during testing
        
        # Create selected_db dict for MainWindow
        selected_db = {
            'name': 'Test Database',
            'file_path': test_db_path
        }
        
        main_window = MainWindow(selected_db)
        main_window.db_manager = db_manager
        print("✅ Главное окно создано")
        
        # Get all button methods
        button_methods = []
        for attr_name in dir(main_window):
            if attr_name.startswith('show_') and callable(getattr(main_window, attr_name)):
                button_methods.append(attr_name)
        
        print(f"📊 Найдено {len(button_methods)} методов кнопок")
        print()
        
        working_buttons = []
        broken_buttons = []
        error_details = {}
        
        # Test each button
        for i, method_name in enumerate(sorted(button_methods), 1):
            print(f"[{i:2d}/{len(button_methods)}] Тестирование {method_name}...", end=" ")
            
            try:
                method = getattr(main_window, method_name)
                method()  # Call the method
                working_buttons.append(method_name)
                print("✅ РАБОТАЕТ")
                
            except Exception as e:
                broken_buttons.append(method_name)
                error_details[method_name] = {
                    'error': str(e),
                    'type': type(e).__name__,
                    'traceback': traceback.format_exc()
                }
                print(f"❌ ОШИБКА: {type(e).__name__}: {str(e)[:50]}...")
            
            # Small delay to prevent overwhelming the system
            time.sleep(0.1)
        
        # Print detailed results
        print()
        print("=" * 60)
        print("📊 ДЕТАЛЬНЫЕ РЕЗУЛЬТАТЫ:")
        print("=" * 60)
        print(f"✅ Работающих кнопок: {len(working_buttons)}")
        print(f"❌ Неработающих кнопок: {len(broken_buttons)}")
        print()
        
        if working_buttons:
            print("✅ РАБОТАЮЩИЕ КНОПКИ:")
            for button in sorted(working_buttons):
                print(f"   • {button}")
            print()
        
        if broken_buttons:
            print("❌ НЕРАБОТАЮЩИЕ КНОПКИ:")
            for button in sorted(broken_buttons):
                error_info = error_details[button]
                print(f"   • {button}")
                print(f"     Ошибка: {error_info['type']}: {error_info['error']}")
                print()
        
        # Show detailed error analysis
        if broken_buttons:
            print("🔍 АНАЛИЗ ОШИБОК:")
            print("=" * 40)
            
            error_types = {}
            for button, details in error_details.items():
                error_type = details['type']
                if error_type not in error_types:
                    error_types[error_type] = []
                error_types[error_type].append(button)
            
            for error_type, buttons in error_types.items():
                print(f"📋 {error_type} ({len(buttons)} кнопок):")
                for button in buttons:
                    print(f"   • {button}: {error_details[button]['error'][:80]}...")
                print()
        
        # Cleanup
        root.destroy()
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
        
        print("✅ Тест завершен")
        return working_buttons, broken_buttons, error_details
        
    except Exception as e:
        print(f"❌ КРИТИЧЕСКАЯ ОШИБКА ТЕСТА: {e}")
        traceback.print_exc()
        return [], [], {}

if __name__ == "__main__":
    test_all_buttons()
