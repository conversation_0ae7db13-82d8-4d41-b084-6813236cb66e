#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
АВТОМАТИЧЕСКИЙ МОНИТОР РАСКЛАДКИ КЛАВИАТУРЫ
Постоянно следит за раскладкой и автоматически переключает на русскую
"""

import ctypes
import time
import threading
import tkinter as tk
from tkinter import messagebox

class KeyboardMonitor:
    def __init__(self):
        self.monitoring = False
        self.monitor_thread = None
        self.notification_shown = False
        
    def safe_print(self, text):
        """Безопасный вывод с обработкой Unicode ошибок"""
        try:
            print(text)
        except UnicodeEncodeError:
            ascii_text = text.encode('ascii', 'ignore').decode('ascii')
            print(ascii_text)

    def get_current_layout(self):
        """Получить текущую раскладку клавиатуры"""
        try:
            user32 = ctypes.windll.user32
            hwnd = user32.GetForegroundWindow()
            thread_id = user32.GetWindowThreadProcessId(hwnd, None)
            hkl = user32.GetKeyboardLayout(thread_id)
            layout_id = hkl & 0xFFFF
            return layout_id
        except:
            return None

    def force_russian_layout(self):
        """Принудительное переключение на русскую раскладку"""
        try:
            user32 = ctypes.windll.user32
            
            # Метод 1: Базовое переключение
            hkl_russian = user32.LoadKeyboardLayoutW("00000419", 0x00000001)
            if hkl_russian:
                result = user32.ActivateKeyboardLayout(hkl_russian, 0x00000008)
                if result:
                    time.sleep(0.1)
                    current = self.get_current_layout()
                    if current == 0x0419:
                        return True
            
            # Метод 2: Физическая эмуляция Alt+Shift
            VK_MENU = 0x12      # Alt
            VK_LSHIFT = 0xA0    # Left Shift
            
            # Множественные попытки
            for i in range(3):
                user32.keybd_event(VK_MENU, 0, 0, 0)        # Alt down
                time.sleep(0.05)
                user32.keybd_event(VK_LSHIFT, 0, 0, 0)      # Left Shift down
                time.sleep(0.1)
                user32.keybd_event(VK_LSHIFT, 0, 2, 0)      # Left Shift up
                time.sleep(0.05)
                user32.keybd_event(VK_MENU, 0, 2, 0)        # Alt up
                time.sleep(0.3)
                
                current = self.get_current_layout()
                if current == 0x0419:
                    return True
            
            return False
            
        except Exception as e:
            self.safe_print(f"❌ Ошибка принудительного переключения: {e}")
            return False

    def show_notification_once(self):
        """Показать уведомление пользователю (только один раз)"""
        if self.notification_shown:
            return
            
        self.notification_shown = True
        
        def show_dialog():
            try:
                root = tk.Tk()
                root.title("🔄 АВТОМАТИЧЕСКИЙ МОНИТОР РАСКЛАДКИ")
                root.geometry("600x400")
                root.configure(bg='#34495e')
                
                # Делаем окно поверх всех
                root.attributes('-topmost', True)
                
                # Центрируем окно
                root.update_idletasks()
                x = (root.winfo_screenwidth() // 2) - (600 // 2)
                y = (root.winfo_screenheight() // 2) - (400 // 2)
                root.geometry(f"600x400+{x}+{y}")
                
                # Заголовок
                title_label = tk.Label(root, 
                                     text="🔄 АВТОМАТИЧЕСКИЙ МОНИТОР РАСКЛАДКИ", 
                                     font=("Arial", 16, "bold"),
                                     fg='white', bg='#34495e')
                title_label.pack(pady=20)
                
                # Основное сообщение
                message = """⚠️ СИСТЕМА НЕ МОЖЕТ АВТОМАТИЧЕСКИ ПЕРЕКЛЮЧИТЬ РАСКЛАДКУ

🎯 ПОЖАЛУЙСТА, НАЖМИТЕ ОДНУ ИЗ КОМБИНАЦИЙ:

🔹 Alt + Shift (основной способ)
🔹 Ctrl + Shift (альтернативный)
🔹 Win + Пробел (Windows 10/11)

💡 После нажатия система будет работать на русском языке

🤖 Монитор будет следить за раскладкой и напоминать при необходимости"""
                
                msg_label = tk.Label(root, 
                                   text=message,
                                   font=("Arial", 11),
                                   fg='white', bg='#34495e',
                                   justify='left')
                msg_label.pack(pady=20, padx=20)
                
                # Кнопки
                button_frame = tk.Frame(root, bg='#34495e')
                button_frame.pack(pady=20)
                
                def close_dialog():
                    root.destroy()
                
                def stop_monitoring():
                    self.stop_monitoring()
                    root.destroy()
                
                ok_btn = tk.Button(button_frame, 
                                  text="✅ ПОНЯТНО", 
                                  font=("Arial", 12, "bold"),
                                  bg='#27ae60', fg='white',
                                  command=close_dialog,
                                  width=12, height=2)
                ok_btn.pack(side='left', padx=10)
                
                stop_btn = tk.Button(button_frame, 
                                   text="⏹️ ОСТАНОВИТЬ МОНИТОР", 
                                   font=("Arial", 12, "bold"),
                                   bg='#e74c3c', fg='white',
                                   command=stop_monitoring,
                                   width=18, height=2)
                stop_btn.pack(side='left', padx=10)
                
                # Автоматическое закрытие через 15 секунд
                root.after(15000, close_dialog)
                
                root.mainloop()
                
            except Exception as e:
                self.safe_print(f"Ошибка показа уведомления: {e}")
        
        # Запускаем в отдельном потоке
        thread = threading.Thread(target=show_dialog, daemon=True)
        thread.start()

    def monitor_keyboard_layout(self):
        """Основной цикл мониторинга раскладки"""
        self.safe_print("🤖 ЗАПУСК АВТОМАТИЧЕСКОГО МОНИТОРА РАСКЛАДКИ")
        self.safe_print("=" * 50)
        
        check_count = 0
        
        while self.monitoring:
            try:
                current_layout = self.get_current_layout()
                check_count += 1
                
                if current_layout is None:
                    self.safe_print(f"⚠️ Проверка {check_count}: Не удалось получить раскладку")
                elif current_layout == 0x0419:
                    if check_count % 10 == 0:  # Каждые 10 проверок
                        self.safe_print(f"✅ Проверка {check_count}: Раскладка русская")
                elif current_layout != 0x0419:
                    self.safe_print(f"🔄 Проверка {check_count}: Раскладка НЕ русская ({hex(current_layout)})")
                    
                    # Пробуем автоматически переключить
                    if self.force_russian_layout():
                        self.safe_print("✅ Автоматическое переключение успешно!")
                    else:
                        self.safe_print("❌ Автоматическое переключение не сработало")
                        # Показываем уведомление пользователю
                        self.show_notification_once()
                
                time.sleep(2)  # Проверяем каждые 2 секунды
                
            except Exception as e:
                self.safe_print(f"❌ Ошибка мониторинга: {e}")
                time.sleep(5)
        
        self.safe_print("⏹️ МОНИТОР РАСКЛАДКИ ОСТАНОВЛЕН")

    def start_monitoring(self):
        """Запуск мониторинга"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self.monitor_keyboard_layout, daemon=True)
            self.monitor_thread.start()
            self.safe_print("🚀 Монитор раскладки запущен")

    def stop_monitoring(self):
        """Остановка мониторинга"""
        self.monitoring = False
        self.safe_print("⏹️ Остановка монитора раскладки...")

def main():
    """Главная функция"""
    monitor = KeyboardMonitor()
    
    try:
        # Сначала пробуем переключить раскладку
        print("🔄 Попытка переключения раскладки...")
        if monitor.force_russian_layout():
            print("✅ Раскладка переключена на русскую!")
        else:
            print("❌ Не удалось переключить раскладку автоматически")
        
        # Запускаем мониторинг
        monitor.start_monitoring()
        
        # Ждем 30 секунд, затем останавливаем
        time.sleep(30)
        monitor.stop_monitoring()
        
    except KeyboardInterrupt:
        print("\n⏹️ Остановка по запросу пользователя")
        monitor.stop_monitoring()

if __name__ == "__main__":
    main()
