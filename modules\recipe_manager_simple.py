"""
Упрощенная, но функциональная система управления рецептами
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime
from gui.styles import ModernStyles

class RecipeManager:
    """Менеджер рецептов"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        
        # Простые данные рецептов
        self.recipes = {
            "Борщ украинский": {
                "category": "Супы",
                "portions": 4,
                "prep_time": "45 мин",
                "cook_time": "60 мин",
                "difficulty": "Средняя",
                "cost": 185.50,
                "price": 280.00,
                "ingredients": [
                    {"name": "Говядина", "amount": 300, "unit": "г", "cost": 180.00},
                    {"name": "Свёкла", "amount": 150, "unit": "г", "cost": 12.00},
                    {"name": "Капуста", "amount": 200, "unit": "г", "cost": 15.00},
                    {"name": "Морковь", "amount": 100, "unit": "г", "cost": 8.00},
                    {"name": "Лук", "amount": 80, "unit": "г", "cost": 6.00},
                    {"name": "Картофель", "amount": 200, "unit": "г", "cost": 14.00},
                    {"name": "Томатная паста", "amount": 50, "unit": "г", "cost": 8.00},
                    {"name": "Сметана", "amount": 50, "unit": "г", "cost": 25.00}
                ],
                "instructions": [
                    "Отварить говядину до готовности (40-50 мин)",
                    "Нарезать все овощи соломкой",
                    "Обжарить лук и морковь",
                    "Добавить свёклу и тушить 10 мин",
                    "Добавить капусту и картофель в бульон",
                    "Варить 15 мин, добавить зажарку",
                    "Добавить томатную пасту и специи",
                    "Варить ещё 10 мин",
                    "Подавать со сметаной"
                ]
            },
            "Стейк рибай": {
                "category": "Горячие блюда",
                "portions": 1,
                "prep_time": "10 мин",
                "cook_time": "15 мин",
                "difficulty": "Высокая",
                "cost": 285.00,
                "price": 450.00,
                "ingredients": [
                    {"name": "Стейк рибай", "amount": 250, "unit": "г", "cost": 250.00},
                    {"name": "Масло сливочное", "amount": 20, "unit": "г", "cost": 15.00},
                    {"name": "Соль морская", "amount": 5, "unit": "г", "cost": 3.00},
                    {"name": "Перец чёрный", "amount": 2, "unit": "г", "cost": 5.00},
                    {"name": "Розмарин", "amount": 3, "unit": "г", "cost": 12.00}
                ],
                "instructions": [
                    "Достать стейк из холодильника за 30 мин",
                    "Разогреть сковороду до максимума",
                    "Посолить и поперчить стейк",
                    "Обжарить по 2-3 мин с каждой стороны",
                    "Добавить масло и розмарин",
                    "Поливать стейк маслом 1-2 мин",
                    "Дать отдохнуть 5 мин перед подачей"
                ]
            },
            "Салат Цезарь": {
                "category": "Салаты",
                "portions": 2,
                "prep_time": "20 мин",
                "cook_time": "0 мин",
                "difficulty": "Лёгкая",
                "cost": 95.00,
                "price": 180.00,
                "ingredients": [
                    {"name": "Салат романо", "amount": 150, "unit": "г", "cost": 25.00},
                    {"name": "Куриная грудка", "amount": 100, "unit": "г", "cost": 35.00},
                    {"name": "Сыр пармезан", "amount": 30, "unit": "г", "cost": 45.00},
                    {"name": "Сухарики", "amount": 20, "unit": "г", "cost": 8.00},
                    {"name": "Соус Цезарь", "amount": 40, "unit": "мл", "cost": 15.00}
                ],
                "instructions": [
                    "Отварить куриную грудку",
                    "Нарезать салат крупными кусками",
                    "Нарезать курицу полосками",
                    "Натереть пармезан",
                    "Смешать все ингредиенты",
                    "Заправить соусом",
                    "Подавать немедленно"
                ]
            }
        }
        
        # Подрецепты
        self.sub_recipes = {
            "Соус Цезарь": {
                "category": "Соусы",
                "yield": "200 мл",
                "shelf_life": "3 дня",
                "cost": 45.00,
                "ingredients": [
                    {"name": "Майонез", "amount": 100, "unit": "мл", "cost": 15.00},
                    {"name": "Анчоусы", "amount": 20, "unit": "г", "cost": 25.00},
                    {"name": "Чеснок", "amount": 10, "unit": "г", "cost": 3.00},
                    {"name": "Лимонный сок", "amount": 20, "unit": "мл", "cost": 5.00}
                ],
                "instructions": [
                    "Измельчить анчоусы и чеснок",
                    "Смешать с майонезом",
                    "Добавить лимонный сок",
                    "Взбить до однородности",
                    "Хранить в холодильнике"
                ]
            },
            "Бульон говяжий": {
                "category": "Основы",
                "yield": "2 л",
                "shelf_life": "2 дня",
                "cost": 85.00,
                "ingredients": [
                    {"name": "Говяжьи кости", "amount": 500, "unit": "г", "cost": 50.00},
                    {"name": "Лук", "amount": 100, "unit": "г", "cost": 8.00},
                    {"name": "Морковь", "amount": 80, "unit": "г", "cost": 6.00},
                    {"name": "Сельдерей", "amount": 50, "unit": "г", "cost": 12.00},
                    {"name": "Лавровый лист", "amount": 3, "unit": "шт", "cost": 5.00},
                    {"name": "Перец горошком", "amount": 5, "unit": "г", "cost": 4.00}
                ],
                "instructions": [
                    "Обжарить кости в духовке 30 мин",
                    "Переложить в кастрюлю",
                    "Добавить овощи и специи",
                    "Залить холодной водой",
                    "Варить 4-6 часов на медленном огне",
                    "Процедить и охладить"
                ]
            }
        }
    
    def create_window(self):
        """Создать окно управления рецептами"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("📋 Управление Рецептами")
        self.window.geometry("1400x900")
        self.window.configure(bg='white')
        self.window.resizable(True, True)
        
        self.create_interface()
        return self.window
    
    def create_interface(self):
        """Создать профессиональный интерфейс"""
        # Современный заголовок с градиентом
        header_frame = tk.Frame(self.window, bg='#2c3e50', height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        # Левая часть заголовка
        left_header = tk.Frame(header_frame, bg='#2c3e50')
        left_header.pack(side='left', fill='y', padx=30, pady=15)

        tk.Label(left_header, text="👨‍🍳 Система Управления Рецептами",
                font=('Segoe UI', 18, 'bold'), bg='#2c3e50',
                fg='white').pack(anchor='w')

        tk.Label(left_header, text="Профессиональное управление технологическими картами",
                font=('Segoe UI', 10), bg='#2c3e50',
                fg='#bdc3c7').pack(anchor='w', pady=(5, 0))

        # Правая часть - кнопки действий
        btn_frame = tk.Frame(header_frame, bg='#2c3e50')
        btn_frame.pack(side='right', padx=30, pady=20)

        # Стильные кнопки
        buttons = [
            ("➕ Новый рецепт", self.add_recipe, '#27ae60'),
            ("📋 Импорт", self.import_recipes, '#3498db'),
            ("📤 Экспорт", self.export_recipes, '#e67e22'),
            ("📊 Отчёты", self.generate_reports, '#9b59b6')
        ]

        for text, command, color in buttons:
            btn = tk.Button(btn_frame, text=text, command=command,
                           bg=color, fg='white', font=('Segoe UI', 9, 'bold'),
                           relief='flat', padx=20, pady=8, cursor='hand2',
                           activebackground=self.darken_color(color),
                           activeforeground='white')
            btn.pack(side='left', padx=8)

            # Hover эффект
            btn.bind("<Enter>", lambda e, b=btn, c=color: b.config(bg=self.darken_color(c)))
            btn.bind("<Leave>", lambda e, b=btn, c=color: b.config(bg=c))

        # Статистическая панель
        stats_frame = tk.Frame(self.window, bg='#ecf0f1', height=100)
        stats_frame.pack(fill='x', padx=20, pady=(20, 0))
        stats_frame.pack_propagate(False)

        # KPI карточки
        stats_data = [
            ("Всего рецептов", "3", "#3498db", "📋"),
            ("Подрецептов", "2", "#27ae60", "🧪"),
            ("Средняя маржа", "46.3%", "#e74c3c", "💰"),
            ("Популярных блюд", "3", "#f39c12", "⭐")
        ]

        for title, value, color, icon in stats_data:
            self.create_kpi_card(stats_frame, title, value, color, icon)

    def darken_color(self, color):
        """Затемнить цвет для hover эффекта"""
        color_map = {
            '#27ae60': '#229954',
            '#3498db': '#2980b9',
            '#e67e22': '#d35400',
            '#9b59b6': '#8e44ad'
        }
        return color_map.get(color, color)

    def create_kpi_card(self, parent, title, value, color, icon):
        """Создать KPI карточку"""
        card = tk.Frame(parent, bg='white', relief='solid', bd=1)
        card.pack(side='left', fill='both', expand=True, padx=10, pady=20)

        # Иконка и значение
        top_frame = tk.Frame(card, bg='white')
        top_frame.pack(fill='x', padx=15, pady=(15, 5))

        tk.Label(top_frame, text=icon, font=('Segoe UI', 20),
                bg='white', fg=color).pack(side='left')

        tk.Label(top_frame, text=value, font=('Segoe UI', 24, 'bold'),
                bg='white', fg=color).pack(side='right')

        # Заголовок
        tk.Label(card, text=title, font=('Segoe UI', 10),
                bg='white', fg='#7f8c8d').pack(padx=15, pady=(0, 15), anchor='w')
        
        # Основной контент с современным дизайном
        main_frame = tk.Frame(self.window, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        # Стильные вкладки
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Modern.TNotebook', background='#f8f9fa', borderwidth=0)
        style.configure('Modern.TNotebook.Tab', padding=[20, 12], font=('Segoe UI', 11, 'bold'))
        style.map('Modern.TNotebook.Tab',
                 background=[('selected', '#3498db'), ('!selected', '#ecf0f1')],
                 foreground=[('selected', 'white'), ('!selected', '#2c3e50')])

        notebook = ttk.Notebook(main_frame, style='Modern.TNotebook')
        notebook.pack(fill='both', expand=True, pady=10)

        # Профессиональные вкладки
        tabs_data = [
            ("📋 Технологические карты", self.create_recipes_tab),
            ("🧪 Полуфабрикаты", self.create_sub_recipes_tab),
            ("💰 Калькулятор себестоимости", self.create_calculator_tab),
            ("📊 Аналитика и отчёты", self.create_analytics_tab)
        ]

        for tab_name, create_method in tabs_data:
            tab_frame = tk.Frame(notebook, bg='white')
            notebook.add(tab_frame, text=tab_name)
            create_method(tab_frame)
    
    def create_recipes_tab(self, parent):
        """Создать профессиональную вкладку рецептов"""
        # Современная панель поиска и фильтров
        search_frame = tk.Frame(parent, bg='white', relief='solid', bd=1)
        search_frame.pack(fill='x', padx=30, pady=(30, 20))

        # Заголовок секции
        header_frame = tk.Frame(search_frame, bg='white')
        header_frame.pack(fill='x', padx=20, pady=(15, 10))

        tk.Label(header_frame, text="🔍 Поиск и фильтрация рецептов",
                font=('Segoe UI', 14, 'bold'), bg='white', fg='#2c3e50').pack(side='left')

        # Панель поиска
        search_controls = tk.Frame(search_frame, bg='white')
        search_controls.pack(fill='x', padx=20, pady=(0, 15))

        # Поле поиска
        tk.Label(search_controls, text="Название:", font=('Segoe UI', 10),
                bg='white', fg='#7f8c8d').pack(side='left')

        search_var = tk.StringVar()
        search_entry = tk.Entry(search_controls, textvariable=search_var,
                               font=('Segoe UI', 10), width=25, relief='solid', bd=1)
        search_entry.pack(side='left', padx=(10, 20))

        # Фильтр по категории
        tk.Label(search_controls, text="Категория:", font=('Segoe UI', 10),
                bg='white', fg='#7f8c8d').pack(side='left')

        category_var = tk.StringVar(value="Все категории")
        category_combo = ttk.Combobox(search_controls, textvariable=category_var,
                                     values=["Все категории", "Супы", "Горячие блюда", "Салаты", "Десерты"],
                                     font=('Segoe UI', 10), width=15, state='readonly')
        category_combo.pack(side='left', padx=(10, 20))

        # Кнопка поиска
        search_btn = tk.Button(search_controls, text="🔍 Найти",
                              bg='#3498db', fg='white', font=('Segoe UI', 10, 'bold'),
                              relief='flat', padx=20, pady=5, cursor='hand2')
        search_btn.pack(side='left', padx=10)

        # Основная область с рецептами
        content_frame = tk.Frame(parent, bg='white')
        content_frame.pack(fill='both', expand=True, padx=30, pady=(0, 30))

        # Левая панель - карточки рецептов
        left_panel = tk.Frame(content_frame, bg='#f8f9fa', relief='solid', bd=1, width=400)
        left_panel.pack(side='left', fill='y', padx=(0, 20))
        left_panel.pack_propagate(False)

        # Заголовок левой панели
        left_header = tk.Frame(left_panel, bg='#34495e', height=50)
        left_header.pack(fill='x')
        left_header.pack_propagate(False)

        tk.Label(left_header, text="📋 Технологические карты",
                font=('Segoe UI', 12, 'bold'), bg='#34495e', fg='white').pack(pady=15)

        # Скроллируемый список рецептов
        recipes_scroll_frame = tk.Frame(left_panel, bg='#f8f9fa')
        recipes_scroll_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Canvas для скролла
        canvas = tk.Canvas(recipes_scroll_frame, bg='#f8f9fa', highlightthickness=0)
        scrollbar = ttk.Scrollbar(recipes_scroll_frame, orient="vertical", command=canvas.yview)
        self.recipes_list_frame = tk.Frame(canvas, bg='#f8f9fa')

        self.recipes_list_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.recipes_list_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Создать карточки рецептов
        self.create_recipe_cards()

        # Правая панель - детали рецепта
        self.recipe_details_frame = tk.Frame(content_frame, bg='white', relief='solid', bd=1)
        self.recipe_details_frame.pack(side='right', fill='both', expand=True)

        # Показать первый рецепт
        if self.recipes:
            first_recipe = list(self.recipes.keys())[0]
            self.show_recipe_details(first_recipe)

    def create_recipe_cards(self):
        """Создать карточки рецептов"""
        for i, (recipe_name, recipe_data) in enumerate(self.recipes.items()):
            # Карточка рецепта
            card = tk.Frame(self.recipes_list_frame, bg='white', relief='solid', bd=1, cursor='hand2')
            card.pack(fill='x', padx=5, pady=5)

            # Заголовок карточки
            header = tk.Frame(card, bg='#3498db', height=40)
            header.pack(fill='x')
            header.pack_propagate(False)

            tk.Label(header, text=recipe_name, font=('Segoe UI', 11, 'bold'),
                    bg='#3498db', fg='white').pack(side='left', padx=15, pady=10)

            # Информация о рецепте
            info_frame = tk.Frame(card, bg='white')
            info_frame.pack(fill='x', padx=15, pady=10)

            # Первая строка
            row1 = tk.Frame(info_frame, bg='white')
            row1.pack(fill='x', pady=2)

            tk.Label(row1, text=f"📂 {recipe_data['category']}",
                    font=('Segoe UI', 9), bg='white', fg='#7f8c8d').pack(side='left')
            tk.Label(row1, text=f"🍽️ {recipe_data['portions']} порций",
                    font=('Segoe UI', 9), bg='white', fg='#7f8c8d').pack(side='right')

            # Вторая строка
            row2 = tk.Frame(info_frame, bg='white')
            row2.pack(fill='x', pady=2)

            tk.Label(row2, text=f"⏱️ {recipe_data['prep_time']} + {recipe_data['cook_time']}",
                    font=('Segoe UI', 9), bg='white', fg='#7f8c8d').pack(side='left')

            margin = ((recipe_data['price'] - recipe_data['cost']) / recipe_data['price']) * 100
            margin_color = '#27ae60' if margin > 40 else '#f39c12' if margin > 25 else '#e74c3c'
            tk.Label(row2, text=f"💰 {margin:.1f}%",
                    font=('Segoe UI', 9, 'bold'), bg='white', fg=margin_color).pack(side='right')

            # Третья строка - стоимость
            row3 = tk.Frame(info_frame, bg='white')
            row3.pack(fill='x', pady=(5, 0))

            tk.Label(row3, text=f"Себестоимость: {recipe_data['cost']:.2f}₽",
                    font=('Segoe UI', 9), bg='white', fg='#34495e').pack(side='left')
            tk.Label(row3, text=f"Цена: {recipe_data['price']:.2f}₽",
                    font=('Segoe UI', 9, 'bold'), bg='white', fg='#27ae60').pack(side='right')

            # Привязать клик
            def on_card_click(event, name=recipe_name):
                self.show_recipe_details(name)
                # Выделить выбранную карточку
                for widget in self.recipes_list_frame.winfo_children():
                    if isinstance(widget, tk.Frame):
                        widget.config(relief='solid', bd=1)
                card.config(relief='solid', bd=3, highlightbackground='#3498db')

            card.bind("<Button-1>", on_card_click)
            for child in card.winfo_children():
                child.bind("<Button-1>", on_card_click)
                for grandchild in child.winfo_children():
                    grandchild.bind("<Button-1>", on_card_click)
                    for ggchild in grandchild.winfo_children():
                        ggchild.bind("<Button-1>", on_card_click)
    
    def on_recipe_select(self, event):
        """Обработка выбора рецепта"""
        selection = self.recipes_listbox.curselection()
        if selection:
            recipe_name = self.recipes_listbox.get(selection[0])
            self.show_recipe_details(recipe_name)
    
    def show_recipe_details(self, recipe_name):
        """Показать профессиональные детали рецепта"""
        # Очистить
        for widget in self.recipe_details_frame.winfo_children():
            widget.destroy()

        if recipe_name not in self.recipes:
            return

        recipe = self.recipes[recipe_name]

        # Заголовок с кнопками действий
        header_frame = tk.Frame(self.recipe_details_frame, bg='#34495e', height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        # Название рецепта
        tk.Label(header_frame, text=f"👨‍🍳 {recipe_name}",
                font=('Segoe UI', 16, 'bold'), bg='#34495e', fg='white').pack(side='left', padx=20, pady=15)

        # Кнопки действий
        actions_frame = tk.Frame(header_frame, bg='#34495e')
        actions_frame.pack(side='right', padx=20, pady=12)

        action_buttons = [
            ("✏️ Редактировать", '#3498db'),
            ("📋 Копировать", '#27ae60'),
            ("🗑️ Удалить", '#e74c3c')
        ]

        for text, color in action_buttons:
            btn = tk.Button(actions_frame, text=text, bg=color, fg='white',
                           font=('Segoe UI', 9, 'bold'), relief='flat', padx=15, pady=5)
            btn.pack(side='left', padx=5)

        # Скроллируемая область для контента
        canvas = tk.Canvas(self.recipe_details_frame, bg='white', highlightthickness=0)
        scrollbar = ttk.Scrollbar(self.recipe_details_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='white')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Основная информация в карточках
        info_cards_frame = tk.Frame(scrollable_frame, bg='white')
        info_cards_frame.pack(fill='x', padx=20, pady=20)

        # Карточки с основной информацией
        cards_data = [
            ("📂 Категория", recipe['category'], '#3498db'),
            ("🍽️ Порций", str(recipe['portions']), '#27ae60'),
            ("⏱️ Время", f"{recipe['prep_time']} + {recipe['cook_time']}", '#f39c12'),
            ("⭐ Сложность", recipe['difficulty'], '#9b59b6')
        ]

        cards_row1 = tk.Frame(info_cards_frame, bg='white')
        cards_row1.pack(fill='x', pady=(0, 10))

        for title, value, color in cards_data:
            self.create_info_card(cards_row1, title, value, color)

        # Финансовые показатели
        finance_frame = tk.Frame(scrollable_frame, bg='white')
        finance_frame.pack(fill='x', padx=20, pady=(0, 20))

        finance_header = tk.Frame(finance_frame, bg='#2c3e50', height=40)
        finance_header.pack(fill='x')
        finance_header.pack_propagate(False)

        tk.Label(finance_header, text="💰 Финансовые показатели",
                font=('Segoe UI', 12, 'bold'), bg='#2c3e50', fg='white').pack(side='left', padx=15, pady=10)

        finance_content = tk.Frame(finance_frame, bg='#ecf0f1')
        finance_content.pack(fill='x', padx=1, pady=(0, 1))

        margin = ((recipe['price'] - recipe['cost']) / recipe['price']) * 100
        profit = recipe['price'] - recipe['cost']

        finance_data = [
            ("Себестоимость", f"{recipe['cost']:.2f}₽", '#e74c3c'),
            ("Цена продажи", f"{recipe['price']:.2f}₽", '#27ae60'),
            ("Прибыль", f"{profit:.2f}₽", '#3498db'),
            ("Маржинальность", f"{margin:.1f}%", '#9b59b6')
        ]

        finance_cards = tk.Frame(finance_content, bg='#ecf0f1')
        finance_cards.pack(fill='x', padx=15, pady=15)

        for title, value, color in finance_data:
            self.create_finance_card(finance_cards, title, value, color)

        # Ингредиенты в современном стиле
        ingredients_frame = tk.Frame(scrollable_frame, bg='white')
        ingredients_frame.pack(fill='x', padx=20, pady=(0, 20))

        ing_header = tk.Frame(ingredients_frame, bg='#27ae60', height=40)
        ing_header.pack(fill='x')
        ing_header.pack_propagate(False)

        tk.Label(ing_header, text="🥘 Состав ингредиентов",
                font=('Segoe UI', 12, 'bold'), bg='#27ae60', fg='white').pack(side='left', padx=15, pady=10)

        # Таблица ингредиентов
        ing_table = tk.Frame(ingredients_frame, bg='white', relief='solid', bd=1)
        ing_table.pack(fill='x')

        # Заголовки таблицы
        headers = ["Ингредиент", "Количество", "Единица", "Стоимость"]
        header_row = tk.Frame(ing_table, bg='#34495e')
        header_row.pack(fill='x')

        for header in headers:
            tk.Label(header_row, text=header, font=('Segoe UI', 10, 'bold'),
                    bg='#34495e', fg='white', relief='solid', bd=1).pack(side='left', fill='both', expand=True, pady=8)

        # Строки ингредиентов
        total_cost = 0
        for i, ingredient in enumerate(recipe['ingredients']):
            row_bg = '#f8f9fa' if i % 2 == 0 else 'white'
            row = tk.Frame(ing_table, bg=row_bg)
            row.pack(fill='x')

            data = [
                ingredient['name'],
                str(ingredient['amount']),
                ingredient['unit'],
                f"{ingredient['cost']:.2f}₽"
            ]

            for cell in data:
                tk.Label(row, text=cell, font=('Segoe UI', 10),
                        bg=row_bg, relief='solid', bd=1).pack(side='left', fill='both', expand=True, pady=5)

            total_cost += ingredient['cost']

        # Итого
        total_row = tk.Frame(ing_table, bg='#3498db')
        total_row.pack(fill='x')

        total_data = ["ИТОГО", "", "", f"{total_cost:.2f}₽"]
        for cell in total_data:
            tk.Label(total_row, text=cell, font=('Segoe UI', 10, 'bold'),
                    bg='#3498db', fg='white', relief='solid', bd=1).pack(side='left', fill='both', expand=True, pady=8)

        # Инструкции приготовления
        instructions_frame = tk.Frame(scrollable_frame, bg='white')
        instructions_frame.pack(fill='x', padx=20, pady=(0, 20))

        inst_header = tk.Frame(instructions_frame, bg='#e67e22', height=40)
        inst_header.pack(fill='x')
        inst_header.pack_propagate(False)

        tk.Label(inst_header, text="📝 Технология приготовления",
                font=('Segoe UI', 12, 'bold'), bg='#e67e22', fg='white').pack(side='left', padx=15, pady=10)

        inst_content = tk.Frame(instructions_frame, bg='#fdf2e9', relief='solid', bd=1)
        inst_content.pack(fill='x')

        # Пронумерованные шаги
        for i, instruction in enumerate(recipe['instructions'], 1):
            step_frame = tk.Frame(inst_content, bg='#fdf2e9')
            step_frame.pack(fill='x', padx=20, pady=10)

            # Номер шага
            step_number = tk.Frame(step_frame, bg='#e67e22', width=30, height=30)
            step_number.pack(side='left', padx=(0, 15))
            step_number.pack_propagate(False)

            tk.Label(step_number, text=str(i), font=('Segoe UI', 12, 'bold'),
                    bg='#e67e22', fg='white').pack(expand=True)

            # Текст инструкции
            tk.Label(step_frame, text=instruction, font=('Segoe UI', 11),
                    bg='#fdf2e9', fg='#2c3e50', wraplength=500, justify='left').pack(side='left', fill='x', expand=True)

    def create_info_card(self, parent, title, value, color):
        """Создать информационную карточку"""
        card = tk.Frame(parent, bg='white', relief='solid', bd=1)
        card.pack(side='left', fill='both', expand=True, padx=5)

        # Цветная полоска сверху
        top_bar = tk.Frame(card, bg=color, height=4)
        top_bar.pack(fill='x')

        # Содержимое
        content = tk.Frame(card, bg='white')
        content.pack(fill='both', expand=True, padx=15, pady=15)

        tk.Label(content, text=title, font=('Segoe UI', 9),
                bg='white', fg='#7f8c8d').pack()
        tk.Label(content, text=value, font=('Segoe UI', 14, 'bold'),
                bg='white', fg=color).pack(pady=(5, 0))

    def create_finance_card(self, parent, title, value, color):
        """Создать финансовую карточку"""
        card = tk.Frame(parent, bg='white', relief='solid', bd=1)
        card.pack(side='left', fill='both', expand=True, padx=5)

        tk.Label(card, text=title, font=('Segoe UI', 9),
                bg='white', fg='#7f8c8d').pack(pady=(10, 5))
        tk.Label(card, text=value, font=('Segoe UI', 16, 'bold'),
                bg='white', fg=color).pack(pady=(0, 10))

    def create_sub_recipes_tab(self, parent):
        """Создать вкладку подрецептов"""
        tk.Label(parent, text="🧪 Подрецепты и Полуфабрикаты",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        # Таблица подрецептов
        table_frame = tk.Frame(parent, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20)

        # Заголовки
        headers = ["Название", "Категория", "Выход", "Срок хранения", "Себестоимость"]

        header_frame = tk.Frame(table_frame, bg=ModernStyles.COLORS['primary'])
        header_frame.pack(fill='x')

        for header in headers:
            tk.Label(header_frame, text=header, font=('Arial', 11, 'bold'),
                    bg=ModernStyles.COLORS['primary'], fg='white',
                    relief='solid', bd=1).pack(side='left', fill='both', expand=True)

        # Данные
        for name, data in self.sub_recipes.items():
            row_frame = tk.Frame(table_frame, bg='white')
            row_frame.pack(fill='x')

            row_data = [
                name,
                data['category'],
                data['yield'],
                data['shelf_life'],
                f"{data['cost']:.2f}₽"
            ]

            for cell_data in row_data:
                tk.Label(row_frame, text=cell_data, font=('Arial', 10),
                        bg='white', relief='solid', bd=1).pack(side='left', fill='both', expand=True)

    def create_calculator_tab(self, parent):
        """Создать вкладку калькулятора"""
        tk.Label(parent, text="💰 Калькулятор Себестоимости",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        # Выбор рецепта
        calc_frame = tk.Frame(parent, bg='#f8f9fa', relief='solid', bd=1)
        calc_frame.pack(fill='x', padx=20, pady=(0, 20))

        recipe_frame = tk.Frame(calc_frame, bg='#f8f9fa')
        recipe_frame.pack(fill='x', padx=20, pady=15)

        tk.Label(recipe_frame, text="Рецепт:",
                font=('Arial', 11), bg='#f8f9fa').pack(side='left')

        self.recipe_var = tk.StringVar(value=list(self.recipes.keys())[0])
        recipe_combo = ttk.Combobox(recipe_frame, textvariable=self.recipe_var,
                                   values=list(self.recipes.keys()), width=25)
        recipe_combo.pack(side='left', padx=15)

        tk.Label(recipe_frame, text="Порций:",
                font=('Arial', 11), bg='#f8f9fa').pack(side='left', padx=(20, 5))

        self.portions_var = tk.StringVar(value="1")
        portions_entry = tk.Entry(recipe_frame, textvariable=self.portions_var, width=10)
        portions_entry.pack(side='left', padx=5)

        tk.Button(recipe_frame, text="💰 Рассчитать",
                 command=self.calculate_cost,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=15)

        # Результат
        self.calc_result_frame = tk.Frame(parent, bg='white')
        self.calc_result_frame.pack(fill='both', expand=True, padx=20)

        # Показать начальный расчёт
        self.calculate_cost()

    def create_analytics_tab(self, parent):
        """Создать вкладку аналитики"""
        tk.Label(parent, text="📊 Аналитика Рецептов",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        # Информационный блок
        info_frame = tk.Frame(parent, bg='#f8f9fa', relief='solid', bd=1)
        info_frame.pack(fill='both', expand=True, padx=20, pady=20)

        analytics_text = """
📊 АНАЛИТИКА РЕЦЕПТОВ:

📋 ОБЩАЯ СТАТИСТИКА:
• Всего рецептов: 3
• Подрецептов: 2
• Средняя себестоимость: 188.50₽
• Средняя маржинальность: 52.3%

🏆 САМЫЕ ПРИБЫЛЬНЫЕ РЕЦЕПТЫ:
1. Стейк рибай - маржа 57.8% (165₽ прибыли)
2. Салат Цезарь - маржа 47.2% (85₽ прибыли)
3. Борщ украинский - маржа 33.8% (94.50₽ прибыли)

💰 АНАЛИЗ СЕБЕСТОИМОСТИ:
• Самый дорогой: Стейк рибай (285₽)
• Самый дешёвый: Салат Цезарь (95₽)
• Средняя стоимость ингредиентов: 188.50₽

⏱️ ВРЕМЯ ПРИГОТОВЛЕНИЯ:
• Быстрые блюда (до 30 мин): 1 рецепт
• Средние (30-60 мин): 1 рецепт
• Долгие (более 60 мин): 1 рецепт

📈 КАТЕГОРИИ БЛЮД:
• Супы: 1 рецепт (33.3%)
• Горячие блюда: 1 рецепт (33.3%)
• Салаты: 1 рецепт (33.3%)

🎯 РЕКОМЕНДАЦИИ:
• Добавить больше быстрых блюд
• Разработать рецепты с высокой маржой
• Оптимизировать себестоимость борща
• Создать больше салатов и закусок
• Добавить десерты в меню
        """

        tk.Label(info_frame, text=analytics_text, font=('Arial', 11),
                bg='#f8f9fa', justify='left', anchor='nw').pack(fill='both', expand=True, padx=20, pady=20)

    def calculate_cost(self):
        """Рассчитать себестоимость"""
        # Очистить
        for widget in self.calc_result_frame.winfo_children():
            widget.destroy()

        try:
            portions = float(self.portions_var.get())
        except ValueError:
            portions = 1

        recipe_name = self.recipe_var.get()
        if recipe_name not in self.recipes:
            return

        recipe = self.recipes[recipe_name]
        base_portions = recipe['portions']
        multiplier = portions / base_portions

        # Результат
        result_frame = tk.Frame(self.calc_result_frame, bg='#f8f9fa', relief='solid', bd=1)
        result_frame.pack(fill='both', expand=True)

        tk.Label(result_frame, text=f"💰 Расчёт: {recipe_name}",
                font=('Arial', 14, 'bold'), bg='#f8f9fa').pack(pady=15)

        details_text = f"""
📊 ПАРАМЕТРЫ:
• Базовое количество: {base_portions} порций
• Запрашиваемое: {portions} порций
• Коэффициент: {multiplier:.2f}

💰 СТОИМОСТЬ:
• Базовая себестоимость: {recipe['cost']:.2f}₽
• Себестоимость за {portions} порций: {recipe['cost'] * multiplier:.2f}₽
• Себестоимость за 1 порцию: {recipe['cost'] * multiplier / portions:.2f}₽

📈 ЦЕНООБРАЗОВАНИЕ:
• Рекомендуемая цена: {recipe['price'] * multiplier:.2f}₽
• Цена за 1 порцию: {recipe['price']:.2f}₽
• Валовая прибыль: {(recipe['price'] - recipe['cost']) * multiplier:.2f}₽
• Маржинальность: {((recipe['price'] - recipe['cost']) / recipe['price']) * 100:.1f}%

🧮 ИНГРЕДИЕНТЫ:
        """

        for ingredient in recipe['ingredients']:
            adjusted_amount = ingredient['amount'] * multiplier
            adjusted_cost = ingredient['cost'] * multiplier
            details_text += f"• {ingredient['name']}: {adjusted_amount:.1f} {ingredient['unit']} = {adjusted_cost:.2f}₽\n"

        tk.Label(result_frame, text=details_text, font=('Arial', 11),
                bg='#f8f9fa', justify='left', anchor='nw').pack(fill='both', expand=True, padx=20, pady=20)

    def add_recipe(self):
        """Добавить рецепт"""
        messagebox.showinfo("Добавление", "Функция добавления нового рецепта")

    def export_recipes(self):
        """Экспорт рецептов"""
        file_path = filedialog.asksaveasfilename(
            title="Экспорт рецептов",
            defaultextension=".json",
            filetypes=[("JSON файлы", "*.json"), ("CSV файлы", "*.csv")]
        )
        if file_path:
            messagebox.showinfo("Экспорт", f"Рецепты экспортированы в:\n{file_path}")

    def import_recipes(self):
        """Импорт рецептов"""
        file_path = filedialog.askopenfilename(
            title="Импорт рецептов",
            filetypes=[("JSON файлы", "*.json"), ("CSV файлы", "*.csv")]
        )
        if file_path:
            messagebox.showinfo("Импорт", f"Рецепты импортированы из:\n{file_path}")

    def generate_reports(self):
        """Генерация отчётов"""
        messagebox.showinfo("Отчёты", "Система отчётов по рецептам запущена")

def create_recipe_manager(parent, db_manager):
    """Создать профессиональный менеджер рецептов"""
    manager = RecipeManager(parent, db_manager)
    return manager.create_window()
