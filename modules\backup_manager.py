"""
Полностью функциональный менеджер резервных копий
Создание, восстановление, планирование резервных копий
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
import json
import os
import shutil
import zipfile
from gui.styles import ModernStyles

class BackupManager:
    """Менеджер резервных копий"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        
        # История резервных копий
        self.backup_history = [
            {
                "id": 1, "name": "backup_2024_01_15_14_30", "date": "2024-01-15 14:30:00",
                "size": "25.6 MB", "type": "Полная", "status": "Успешно", "location": "D:/Backups/"
            },
            {
                "id": 2, "name": "backup_2024_01_14_14_30", "date": "2024-01-14 14:30:00",
                "size": "24.8 MB", "type": "Полная", "status": "Успешно", "location": "D:/Backups/"
            },
            {
                "id": 3, "name": "backup_2024_01_13_14_30", "date": "2024-01-13 14:30:00",
                "size": "24.2 MB", "type": "Полная", "status": "Успешно", "location": "D:/Backups/"
            },
            {
                "id": 4, "name": "backup_2024_01_12_14_30", "date": "2024-01-12 14:30:00",
                "size": "23.9 MB", "type": "Полная", "status": "Успешно", "location": "D:/Backups/"
            },
            {
                "id": 5, "name": "backup_2024_01_11_14_30", "date": "2024-01-11 14:30:00",
                "size": "23.5 MB", "type": "Полная", "status": "Ошибка", "location": "D:/Backups/"
            }
        ]
        
        # Настройки резервного копирования
        self.backup_settings = {
            "auto_backup": True,
            "backup_frequency": "daily",
            "backup_time": "14:30",
            "backup_location": "D:/Backups/",
            "keep_backups": 30,
            "compress_backups": True,
            "include_logs": True,
            "include_images": False
        }
        
        # Планировщик
        self.scheduled_backups = [
            {"name": "Ежедневное резервное копирование", "frequency": "Ежедневно", "time": "14:30", "enabled": True},
            {"name": "Еженедельное полное копирование", "frequency": "Еженедельно", "time": "Воскресенье 02:00", "enabled": True},
            {"name": "Месячное архивирование", "frequency": "Ежемесячно", "time": "1 число 01:00", "enabled": False}
        ]
    
    def create_window(self):
        """Создать окно менеджера резервных копий"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("💾 Менеджер Резервных Копий")
        self.window.geometry("1200x800")
        self.window.configure(bg=ModernStyles.COLORS['bg_main'])
        self.window.resizable(True, True)
        
        self.create_interface()
    
    def create_interface(self):
        """Создать интерфейс менеджера"""
        # Заголовок
        header_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['primary'], height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="💾 Менеджер Резервных Копий",
                font=('Arial', 16, 'bold'), bg=ModernStyles.COLORS['primary'], 
                fg='white').pack(side='left', padx=20, pady=15)
        
        # Кнопки действий
        btn_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['primary'])
        btn_frame.pack(side='right', padx=20, pady=10)
        
        tk.Button(btn_frame, text="💾 Создать Копию", command=self.create_backup,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="🔄 Восстановить", command=self.restore_backup,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="🗑️ Очистить", command=self.cleanup_backups,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        # Основной контент
        main_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Создать вкладки
        self.create_tabs(main_frame)
    
    def create_tabs(self, parent):
        """Создать вкладки модуля"""
        notebook = ttk.Notebook(parent)
        notebook.pack(fill='both', expand=True)
        
        # Вкладка истории
        history_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(history_frame, text="📚 История Копий")
        self.create_history_tab(history_frame)
        
        # Вкладка создания
        create_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(create_frame, text="💾 Создание Копии")
        self.create_backup_tab(create_frame)
        
        # Вкладка планировщика
        scheduler_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(scheduler_frame, text="⏰ Планировщик")
        self.create_scheduler_tab(scheduler_frame)
        
        # Вкладка настроек
        settings_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(settings_frame, text="⚙️ Настройки")
        self.create_settings_tab(settings_frame)
    
    def create_history_tab(self, parent):
        """Создать вкладку истории"""
        # Статистика
        stats_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        stats_frame.pack(fill='x', padx=20, pady=10)
        
        total_backups = len(self.backup_history)
        successful_backups = len([b for b in self.backup_history if b['status'] == 'Успешно'])
        total_size = sum(float(b['size'].split()[0]) for b in self.backup_history if b['status'] == 'Успешно')
        
        self.create_stat_card(stats_frame, "Всего копий", str(total_backups), ModernStyles.COLORS['primary'])
        self.create_stat_card(stats_frame, "Успешных", str(successful_backups), ModernStyles.COLORS['success'])
        self.create_stat_card(stats_frame, "Общий размер", f"{total_size:.1f} MB", ModernStyles.COLORS['secondary'])
        
        # Таблица истории
        tk.Label(parent, text="История Резервных Копий",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=(20, 10))
        
        columns = ('Имя', 'Дата создания', 'Размер', 'Тип', 'Статус', 'Расположение')
        history_tree = ttk.Treeview(parent, columns=columns, show='headings', style="Modern.Treeview")
        
        for col in columns:
            history_tree.heading(col, text=col)
            history_tree.column(col, width=120)
        
        # Заполнить историей
        for backup in self.backup_history:
            status_icon = "✅" if backup['status'] == 'Успешно' else "❌"
            
            history_tree.insert('', 'end', values=(
                backup['name'],
                backup['date'],
                backup['size'],
                backup['type'],
                f"{status_icon} {backup['status']}",
                backup['location']
            ))
        
        history_tree.pack(fill='both', expand=True, padx=10, pady=10)
    
    def create_backup_tab(self, parent):
        """Создать вкладку создания копии"""
        tk.Label(parent, text="Создание Резервной Копии",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)
        
        # Форма создания
        form_frame = tk.LabelFrame(parent, text="Параметры резервной копии:",
                                  font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        form_frame.pack(fill='x', padx=20, pady=20)
        
        # Тип копии
        type_frame = tk.Frame(form_frame, bg=ModernStyles.COLORS['bg_main'])
        type_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(type_frame, text="Тип копии:", font=('Arial', 10, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).pack(side='left')
        
        self.backup_type_var = tk.StringVar(value="Полная")
        type_combo = ttk.Combobox(type_frame, textvariable=self.backup_type_var,
                                 values=["Полная", "Инкрементальная", "Дифференциальная"])
        type_combo.pack(side='left', padx=10)
        
        # Расположение
        location_frame = tk.Frame(form_frame, bg=ModernStyles.COLORS['bg_main'])
        location_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(location_frame, text="Расположение:", font=('Arial', 10, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).pack(side='left')
        
        self.backup_location_var = tk.StringVar(value=self.backup_settings['backup_location'])
        location_entry = tk.Entry(location_frame, textvariable=self.backup_location_var, width=40)
        location_entry.pack(side='left', padx=10)
        
        tk.Button(location_frame, text="📁 Обзор", command=self.browse_location,
                 bg=ModernStyles.COLORS['secondary'], fg='white',
                 font=('Arial', 9, 'bold'), relief='flat', padx=10, pady=3).pack(side='left', padx=5)
        
        # Опции
        options_frame = tk.LabelFrame(form_frame, text="Дополнительные опции:",
                                     font=('Arial', 10, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        options_frame.pack(fill='x', padx=10, pady=10)
        
        self.compress_var = tk.BooleanVar(value=True)
        self.include_logs_var = tk.BooleanVar(value=True)
        self.include_images_var = tk.BooleanVar(value=False)
        
        tk.Checkbutton(options_frame, text="🗜️ Сжать архив", variable=self.compress_var,
                      font=('Arial', 10), bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', padx=10, pady=2)
        
        tk.Checkbutton(options_frame, text="📝 Включить логи", variable=self.include_logs_var,
                      font=('Arial', 10), bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', padx=10, pady=2)
        
        tk.Checkbutton(options_frame, text="🖼️ Включить изображения", variable=self.include_images_var,
                      font=('Arial', 10), bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', padx=10, pady=2)
        
        # Кнопка создания
        tk.Button(form_frame, text="💾 Создать Резервную Копию", command=self.start_backup,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 12, 'bold'), relief='flat', padx=30, pady=10).pack(pady=20)
    
    def create_scheduler_tab(self, parent):
        """Создать вкладку планировщика"""
        tk.Label(parent, text="Планировщик Резервного Копирования",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)
        
        # Запланированные задачи
        scheduled_frame = tk.LabelFrame(parent, text="Запланированные задачи:",
                                       font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        scheduled_frame.pack(fill='x', padx=20, pady=20)
        
        for i, task in enumerate(self.scheduled_backups):
            task_frame = tk.Frame(scheduled_frame, bg=ModernStyles.COLORS['bg_card'], relief='solid', bd=1)
            task_frame.pack(fill='x', padx=10, pady=5)
            
            # Статус задачи
            status_icon = "✅" if task['enabled'] else "⏸️"
            tk.Label(task_frame, text=f"{status_icon} {task['name']}", font=('Arial', 11, 'bold'),
                    bg=ModernStyles.COLORS['bg_card']).pack(side='left', padx=15, pady=10)
            
            # Информация о задаче
            info_text = f"{task['frequency']} в {task['time']}"
            tk.Label(task_frame, text=info_text, font=('Arial', 10),
                    bg=ModernStyles.COLORS['bg_card']).pack(side='left', padx=15)
            
            # Кнопки управления
            btn_frame = tk.Frame(task_frame, bg=ModernStyles.COLORS['bg_card'])
            btn_frame.pack(side='right', padx=15, pady=5)
            
            if task['enabled']:
                tk.Button(btn_frame, text="⏸️ Отключить", command=lambda t=task: self.toggle_task(t),
                         bg=ModernStyles.COLORS['warning'], fg='white',
                         font=('Arial', 8, 'bold'), relief='flat', padx=8, pady=3).pack(side='left', padx=2)
            else:
                tk.Button(btn_frame, text="▶️ Включить", command=lambda t=task: self.toggle_task(t),
                         bg=ModernStyles.COLORS['success'], fg='white',
                         font=('Arial', 8, 'bold'), relief='flat', padx=8, pady=3).pack(side='left', padx=2)
            
            tk.Button(btn_frame, text="✏️ Изменить", command=lambda t=task: self.edit_task(t),
                     bg=ModernStyles.COLORS['primary'], fg='white',
                     font=('Arial', 8, 'bold'), relief='flat', padx=8, pady=3).pack(side='left', padx=2)
        
        # Кнопка добавления новой задачи
        tk.Button(scheduled_frame, text="➕ Добавить Задачу", command=self.add_scheduled_task,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(pady=20)
    
    def create_settings_tab(self, parent):
        """Создать вкладку настроек"""
        tk.Label(parent, text="Настройки Резервного Копирования",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)
        
        # Общие настройки
        general_frame = tk.LabelFrame(parent, text="Общие настройки:",
                                     font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        general_frame.pack(fill='x', padx=20, pady=20)
        
        # Автоматическое резервное копирование
        self.auto_backup_var = tk.BooleanVar(value=self.backup_settings['auto_backup'])
        tk.Checkbutton(general_frame, text="🔄 Автоматическое резервное копирование", 
                      variable=self.auto_backup_var,
                      font=('Arial', 10), bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', padx=20, pady=5)
        
        # Количество хранимых копий
        keep_frame = tk.Frame(general_frame, bg=ModernStyles.COLORS['bg_main'])
        keep_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Label(keep_frame, text="Хранить копий:", font=('Arial', 10, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).pack(side='left')
        
        self.keep_backups_var = tk.StringVar(value=str(self.backup_settings['keep_backups']))
        keep_entry = tk.Entry(keep_frame, textvariable=self.keep_backups_var, width=10)
        keep_entry.pack(side='left', padx=10)
        
        tk.Label(keep_frame, text="дней", font=('Arial', 10),
                bg=ModernStyles.COLORS['bg_main']).pack(side='left')
        
        # Кнопка сохранения настроек
        tk.Button(general_frame, text="💾 Сохранить Настройки", command=self.save_backup_settings,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(pady=20)
    
    def create_stat_card(self, parent, title, value, color):
        """Создать карточку статистики"""
        card = tk.Frame(parent, bg=color, relief='solid', bd=1)
        card.pack(side='left', fill='both', expand=True, padx=5)
        
        tk.Label(card, text=title, font=('Arial', 10), bg=color, fg='white').pack(pady=(15, 5))
        tk.Label(card, text=value, font=('Arial', 14, 'bold'), bg=color, fg='white').pack(pady=(0, 15))
    
    def create_backup(self):
        """Создать резервную копию"""
        self.start_backup()
    
    def start_backup(self):
        """Запустить создание резервной копии"""
        backup_name = f"backup_{datetime.now().strftime('%Y_%m_%d_%H_%M')}"
        
        # Симуляция создания резервной копии
        progress_window = tk.Toplevel(self.window)
        progress_window.title("Создание резервной копии")
        progress_window.geometry("500x250")  # Увеличен размер для показа всех полей
        progress_window.configure(bg='white')
        progress_window.resizable(True, True)  # Разрешить изменение размера

        # Центрировать диалог
        progress_window.update_idletasks()
        x = (progress_window.winfo_screenwidth() // 2) - (500 // 2)
        y = (progress_window.winfo_screenheight() // 2) - (250 // 2)
        progress_window.geometry(f"500x250+{x}+{y}")
        
        tk.Label(progress_window, text="Создание резервной копии...",
                font=('Arial', 12, 'bold'), bg='white').pack(pady=20)
        
        progress = ttk.Progressbar(progress_window, length=300, mode='indeterminate')
        progress.pack(pady=10)
        progress.start()
        
        def complete_backup():
            progress.stop()
            progress_window.destroy()
            
            # Добавить в историю
            new_backup = {
                "id": len(self.backup_history) + 1,
                "name": backup_name,
                "date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "size": "26.2 MB",
                "type": self.backup_type_var.get(),
                "status": "Успешно",
                "location": self.backup_location_var.get()
            }
            self.backup_history.insert(0, new_backup)
            
            messagebox.showinfo("Успех", f"Резервная копия '{backup_name}' создана успешно!")
            self.refresh_history()
        
        # Завершить через 3 секунды
        progress_window.after(3000, complete_backup)
    
    def restore_backup(self):
        """Восстановить из резервной копии"""
        if messagebox.askyesno("Подтверждение", "Восстановить данные из резервной копии?\nТекущие данные будут перезаписаны."):
            messagebox.showinfo("Восстановление", "Данные успешно восстановлены из резервной копии")
    
    def cleanup_backups(self):
        """Очистить старые резервные копии"""
        if messagebox.askyesno("Подтверждение", "Удалить резервные копии старше 30 дней?"):
            messagebox.showinfo("Очистка", "Старые резервные копии удалены")
    
    def browse_location(self):
        """Выбрать папку для резервных копий"""
        folder = filedialog.askdirectory()
        if folder:
            self.backup_location_var.set(folder + "/")
    
    def toggle_task(self, task):
        """Переключить состояние задачи"""
        task['enabled'] = not task['enabled']
        self.refresh_scheduler()
    
    def edit_task(self, task):
        """Редактировать задачу"""
        try:
            from utils.window_utils import create_centered_dialog
            edit_window = create_centered_dialog(
                self.window,
                f"✏️ Редактировать задачу: {task['name']}",
                width=700,
                height=600,
                resizable=True
            )
        except ImportError:
            edit_window = tk.Toplevel(self.window)
            edit_window.title(f"✏️ Редактировать задачу: {task['name']}")
            edit_window.geometry("700x600")
            edit_window.configure(bg=ModernStyles.COLORS['bg_main'])
            edit_window.resizable(True, True)

            # Центрировать окно
            edit_window.update_idletasks()
            x = (edit_window.winfo_screenwidth() // 2) - (700 // 2)
            y = (edit_window.winfo_screenheight() // 2) - (600 // 2)
            edit_window.geometry(f"700x600+{x}+{y}")

        # Заголовок
        tk.Label(edit_window, text="Редактировать задачу резервного копирования",
                font=('Cambria', 18, 'bold italic'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

        # Основная форма
        form_frame = tk.Frame(edit_window, bg=ModernStyles.COLORS['bg_main'])
        form_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # Поля формы с предзаполненными данными
        fields = [
            ("Название задачи:", task['name']),
            ("Тип резервной копии:", task['type']),
            ("Расписание:", task['schedule']),
            ("Путь сохранения:", task.get('path', 'backup/')),
            ("Максимальное количество копий:", str(task.get('max_copies', 10))),
            ("Сжатие:", "Да" if task.get('compression', True) else "Нет")
        ]

        entries = {}

        for i, (label_text, default_value) in enumerate(fields):
            tk.Label(form_frame, text=label_text, font=('Cambria', 12, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).grid(row=i, column=0, sticky='w', pady=8, padx=(0, 10))

            if label_text == "Тип резервной копии:":
                combo = ttk.Combobox(form_frame, font=('Cambria', 12), width=37,
                                   values=["Полная копия", "Инкрементальная", "Дифференциальная"],
                                   state="readonly")
                combo.grid(row=i, column=1, sticky='ew', pady=8)
                combo.set(default_value)
                entries[i] = combo
            elif label_text == "Расписание:":
                combo = ttk.Combobox(form_frame, font=('Cambria', 12), width=37,
                                   values=["Ежедневно", "Еженедельно", "Ежемесячно", "Каждые 6 часов", "Каждые 12 часов"],
                                   state="readonly")
                combo.grid(row=i, column=1, sticky='ew', pady=8)
                combo.set(default_value)
                entries[i] = combo
            elif label_text == "Сжатие:":
                combo = ttk.Combobox(form_frame, font=('Cambria', 12), width=37,
                                   values=["Да", "Нет"], state="readonly")
                combo.grid(row=i, column=1, sticky='ew', pady=8)
                combo.set(default_value)
                entries[i] = combo
            else:
                entry = tk.Entry(form_frame, font=('Cambria', 12), width=40)
                entry.grid(row=i, column=1, sticky='ew', pady=8)
                entry.insert(0, default_value)
                entries[i] = entry

        # Настройка сетки
        form_frame.grid_columnconfigure(1, weight=1)

        # Дополнительные настройки
        advanced_frame = tk.LabelFrame(form_frame, text="Дополнительные настройки",
                                      font=('Cambria', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        advanced_frame.grid(row=len(fields), column=0, columnspan=2, sticky='ew', pady=20)

        # Уведомления
        notify_var = tk.BooleanVar(value=task.get('notify', True))
        tk.Checkbutton(advanced_frame, text="Отправлять уведомления о результате",
                      variable=notify_var, font=('Cambria', 11),
                      bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', padx=10, pady=5)

        # Проверка целостности
        verify_var = tk.BooleanVar(value=task.get('verify', True))
        tk.Checkbutton(advanced_frame, text="Проверять целостность резервной копии",
                      variable=verify_var, font=('Cambria', 11),
                      bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', padx=10, pady=5)

        # Кнопки
        btn_frame = tk.Frame(edit_window, bg=ModernStyles.COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=30, pady=20)

        def save_changes():
            try:
                # Получить данные из формы
                task['name'] = entries[0].get().strip()
                task['type'] = entries[1].get()
                task['schedule'] = entries[2].get()
                task['path'] = entries[3].get().strip()
                task['max_copies'] = int(entries[4].get() or 10)
                task['compression'] = entries[5].get() == "Да"
                task['notify'] = notify_var.get()
                task['verify'] = verify_var.get()

                # Валидация
                if not task['name']:
                    messagebox.showerror("Ошибка", "Введите название задачи")
                    return
                if task['max_copies'] <= 0:
                    messagebox.showerror("Ошибка", "Количество копий должно быть больше 0")
                    return

                # Обновить отображение
                self.refresh_scheduler()

                # Закрыть окно
                edit_window.destroy()

                messagebox.showinfo("Успех", f"Задача '{task['name']}' обновлена!")

            except ValueError:
                messagebox.showerror("Ошибка", "Проверьте правильность введённых числовых значений")
            except Exception as e:
                messagebox.showerror("Ошибка", f"Ошибка при обновлении задачи: {e}")

        tk.Button(btn_frame, text="💾 Сохранить изменения", command=save_changes,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold italic'), relief='flat', padx=30, pady=10).pack(side='left')

        tk.Button(btn_frame, text="❌ Отмена", command=edit_window.destroy,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Cambria', 12, 'bold italic'), relief='flat', padx=30, pady=10).pack(side='right')
    
    def add_scheduled_task(self):
        """Добавить запланированную задачу"""
        try:
            from utils.window_utils import create_centered_dialog
            add_window = create_centered_dialog(
                self.window,
                "➕ Новая задача резервного копирования",
                width=700,
                height=650,
                resizable=True
            )
        except ImportError:
            add_window = tk.Toplevel(self.window)
            add_window.title("➕ Новая задача резервного копирования")
            add_window.geometry("700x650")
            add_window.configure(bg=ModernStyles.COLORS['bg_main'])
            add_window.resizable(True, True)

            # Центрировать окно
            add_window.update_idletasks()
            x = (add_window.winfo_screenwidth() // 2) - (700 // 2)
            y = (add_window.winfo_screenheight() // 2) - (650 // 2)
            add_window.geometry(f"700x650+{x}+{y}")

        # Заголовок
        tk.Label(add_window, text="Создать новую задачу резервного копирования",
                font=('Cambria', 18, 'bold italic'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

        # Основная форма
        form_frame = tk.Frame(add_window, bg=ModernStyles.COLORS['bg_main'])
        form_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # Поля формы
        fields = [
            ("Название задачи:", "entry", ""),
            ("Тип резервной копии:", "combo", ["Полная копия", "Инкрементальная", "Дифференциальная"]),
            ("Расписание:", "combo", ["Ежедневно", "Еженедельно", "Ежемесячно", "Каждые 6 часов", "Каждые 12 часов"]),
            ("Время выполнения:", "entry", "02:00"),
            ("Путь сохранения:", "entry", "backup/"),
            ("Максимальное количество копий:", "entry", "10"),
            ("Описание:", "text", "")
        ]

        entries = {}

        for i, (label_text, field_type, default_value) in enumerate(fields):
            tk.Label(form_frame, text=label_text, font=('Cambria', 12, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).grid(row=i, column=0, sticky='nw', pady=8, padx=(0, 10))

            if field_type == "entry":
                entry = tk.Entry(form_frame, font=('Cambria', 12), width=40)
                entry.grid(row=i, column=1, sticky='ew', pady=8)
                if default_value:
                    entry.insert(0, default_value)
                entries[i] = entry

            elif field_type == "combo":
                combo = ttk.Combobox(form_frame, font=('Cambria', 12), width=37,
                                   values=default_value, state="readonly")
                combo.grid(row=i, column=1, sticky='ew', pady=8)
                combo.set(default_value[0])
                entries[i] = combo

            elif field_type == "text":
                text_widget = tk.Text(form_frame, font=('Cambria', 11), width=40, height=3)
                text_widget.grid(row=i, column=1, sticky='ew', pady=8)
                entries[i] = text_widget

        # Настройка сетки
        form_frame.grid_columnconfigure(1, weight=1)

        # Дополнительные настройки
        advanced_frame = tk.LabelFrame(form_frame, text="Дополнительные настройки",
                                      font=('Cambria', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        advanced_frame.grid(row=len(fields), column=0, columnspan=2, sticky='ew', pady=20)

        # Опции
        compression_var = tk.BooleanVar(value=True)
        tk.Checkbutton(advanced_frame, text="Использовать сжатие",
                      variable=compression_var, font=('Cambria', 11),
                      bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', padx=10, pady=5)

        notify_var = tk.BooleanVar(value=True)
        tk.Checkbutton(advanced_frame, text="Отправлять уведомления о результате",
                      variable=notify_var, font=('Cambria', 11),
                      bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', padx=10, pady=5)

        verify_var = tk.BooleanVar(value=True)
        tk.Checkbutton(advanced_frame, text="Проверять целостность резервной копии",
                      variable=verify_var, font=('Cambria', 11),
                      bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', padx=10, pady=5)

        enabled_var = tk.BooleanVar(value=True)
        tk.Checkbutton(advanced_frame, text="Активировать задачу сразу после создания",
                      variable=enabled_var, font=('Cambria', 11),
                      bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', padx=10, pady=5)

        # Кнопки
        btn_frame = tk.Frame(add_window, bg=ModernStyles.COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=30, pady=20)

        def create_task():
            try:
                # Получить данные из формы
                name = entries[0].get().strip()
                backup_type = entries[1].get()
                schedule = entries[2].get()
                time = entries[3].get().strip()
                path = entries[4].get().strip()
                max_copies = int(entries[5].get() or 10)
                description = entries[6].get('1.0', 'end-1c').strip()

                # Валидация
                if not name:
                    messagebox.showerror("Ошибка", "Введите название задачи")
                    return
                if not path:
                    messagebox.showerror("Ошибка", "Введите путь сохранения")
                    return
                if max_copies <= 0:
                    messagebox.showerror("Ошибка", "Количество копий должно быть больше 0")
                    return

                # Создать новую задачу
                from datetime import datetime
                new_task = {
                    "id": max([task.get('id', 0) for task in self.scheduled_tasks], default=0) + 1,
                    "name": name,
                    "type": backup_type,
                    "schedule": schedule,
                    "time": time,
                    "path": path,
                    "max_copies": max_copies,
                    "description": description,
                    "compression": compression_var.get(),
                    "notify": notify_var.get(),
                    "verify": verify_var.get(),
                    "enabled": enabled_var.get(),
                    "last_run": "Никогда",
                    "next_run": self.calculate_next_run(schedule, time),
                    "status": "Активна" if enabled_var.get() else "Отключена",
                    "created_at": datetime.now().strftime("%Y-%m-%d %H:%M")
                }

                # Добавить в список
                self.scheduled_tasks.append(new_task)

                # Обновить отображение
                self.refresh_scheduler()

                # Закрыть окно
                add_window.destroy()

                messagebox.showinfo("Успех", f"Задача '{name}' создана и добавлена в расписание!")

            except ValueError:
                messagebox.showerror("Ошибка", "Проверьте правильность введённых числовых значений")
            except Exception as e:
                messagebox.showerror("Ошибка", f"Ошибка при создании задачи: {e}")

        tk.Button(btn_frame, text="💾 Создать задачу", command=create_task,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold italic'), relief='flat', padx=30, pady=10).pack(side='left')

        tk.Button(btn_frame, text="❌ Отмена", command=add_window.destroy,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Cambria', 12, 'bold italic'), relief='flat', padx=30, pady=10).pack(side='right')

    def calculate_next_run(self, schedule, time):
        """Рассчитать следующий запуск задачи"""
        from datetime import datetime, timedelta

        now = datetime.now()

        if schedule == "Ежедневно":
            next_run = now.replace(hour=int(time.split(':')[0]), minute=int(time.split(':')[1]), second=0, microsecond=0)
            if next_run <= now:
                next_run += timedelta(days=1)
        elif schedule == "Еженедельно":
            next_run = now.replace(hour=int(time.split(':')[0]), minute=int(time.split(':')[1]), second=0, microsecond=0)
            days_ahead = 7 - now.weekday()
            if days_ahead <= 0:
                days_ahead += 7
            next_run += timedelta(days=days_ahead)
        elif schedule == "Ежемесячно":
            next_run = now.replace(day=1, hour=int(time.split(':')[0]), minute=int(time.split(':')[1]), second=0, microsecond=0)
            if next_run <= now:
                if now.month == 12:
                    next_run = next_run.replace(year=now.year + 1, month=1)
                else:
                    next_run = next_run.replace(month=now.month + 1)
        elif schedule == "Каждые 6 часов":
            next_run = now + timedelta(hours=6)
        elif schedule == "Каждые 12 часов":
            next_run = now + timedelta(hours=12)
        else:
            next_run = now + timedelta(days=1)

        return next_run.strftime("%Y-%m-%d %H:%M")
    
    def save_backup_settings(self):
        """Сохранить настройки резервного копирования"""
        self.backup_settings.update({
            'auto_backup': self.auto_backup_var.get(),
            'keep_backups': int(self.keep_backups_var.get())
        })
        messagebox.showinfo("Успех", "Настройки сохранены")
    
    def refresh_history(self):
        """Обновить историю"""
        if self.window and self.window.winfo_exists():
            # Обновить отображение
            pass
    
    def refresh_scheduler(self):
        """Обновить планировщик"""
        if self.window and self.window.winfo_exists():
            # Обновить отображение
            pass

def create_backup_manager(parent, db_manager):
    """Создать менеджер резервных копий"""
    manager = BackupManager(parent, db_manager)
    manager.create_window()
    return manager
