"""
Test script for login system
"""

import sys
import os

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_login_system():
    """Test the login system components"""
    print("Testing login system...")
    
    try:
        from gui.login_window import LoginWindow
        from database.db_manager import DatabaseManager
        
        print("✓ Login window module imported successfully")
        
        # Test database connection
        db_manager = DatabaseManager()
        print("✓ Database manager initialized")
        
        # Test user table exists
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            print(f"✓ Users table exists with {user_count} users")
        
        print("✓ Login system components are working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Login system test failed: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("Login System Test")
    print("=" * 50)
    
    if test_login_system():
        print("\n🎉 Login system is ready!")
        print("\nTo test the full login flow:")
        print("1. Run: python main.py")
        print("2. Login with: admin / admin123")
        print("3. Explore the system!")
    else:
        print("\n❌ Login system has issues")
    
    print("=" * 50)
