"""
Test script for Data Export/Import System
Tests the functionality of the advanced data export/import system
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.data_transformation import data_validator, data_transformer, data_mapper
from datetime import datetime
import json

def test_data_validator():
    """Test data validation functionality"""
    print("🧪 Testing Data Validator...")
    
    # Test date validation
    test_dates = [
        "25.12.2023",
        "2023-12-25", 
        "25/12/2023",
        "invalid_date",
        ""
    ]
    
    for date_str in test_dates:
        is_valid, parsed_date = data_validator.validate_date(date_str)
        print(f"  Date '{date_str}': {'✅ Valid' if is_valid else '❌ Invalid'} - {parsed_date}")
    
    # Test number validation
    test_numbers = [
        "1 234,56",
        "1234.56",
        "1,234.56",
        "-100",
        "invalid",
        ""
    ]
    
    for number_str in test_numbers:
        is_valid, parsed_number = data_validator.validate_number(number_str)
        print(f"  Number '{number_str}': {'✅ Valid' if is_valid else '❌ Invalid'} - {parsed_number}")
    
    # Test email validation
    test_emails = [
        "<EMAIL>",
        "invalid.email",
        "<EMAIL>",
        ""
    ]
    
    for email in test_emails:
        is_valid = data_validator.validate_email(email)
        print(f"  Email '{email}': {'✅ Valid' if is_valid else '❌ Invalid'}")
    
    # Test phone validation
    test_phones = [
        "+7 (123) 456-78-90",
        "8 123 456 78 90",
        "1234567890",
        "invalid_phone"
    ]
    
    for phone in test_phones:
        is_valid, normalized = data_validator.validate_phone(phone)
        print(f"  Phone '{phone}': {'✅ Valid' if is_valid else '❌ Invalid'} - {normalized}")

def test_data_transformer():
    """Test data transformation functionality"""
    print("\n🔄 Testing Data Transformer...")
    
    # Test currency formatting
    test_amounts = [1234.56, "1234,56", "1 234.56", 0]
    
    for amount in test_amounts:
        formatted = data_transformer.format_currency(amount)
        print(f"  Amount {amount}: {formatted}")
    
    # Test date formatting
    test_date = datetime.now()
    formats = ['%d.%m.%Y', '%Y-%m-%d', '%d/%m/%Y']
    
    for fmt in formats:
        formatted = data_transformer.format_date(test_date, fmt)
        print(f"  Date format '{fmt}': {formatted}")
    
    # Test text normalization
    test_texts = [
        "  Multiple   spaces  ",
        "Text with\ttabs\nand\nnewlines",
        "Very long text that needs to be truncated because it exceeds the maximum length limit"
    ]
    
    for text in test_texts:
        normalized = data_transformer.normalize_text(text, 50)
        print(f"  Text: '{text}' -> '{normalized}'")

def test_data_mapper():
    """Test data mapping functionality"""
    print("\n🗺️ Testing Data Mapper...")
    
    # Test sales record mapping
    sales_record = {
        'дата': '25.12.2023',
        'сумма': '1 234,56',
        'способ_оплаты': 'Наличные',
        'отдел': 'Основной зал'
    }
    
    mapped_sales = data_mapper.map_record_fields(sales_record, 'sales')
    print(f"  Sales mapping: {sales_record} -> {mapped_sales}")
    
    # Validate mapped record
    validation_errors = data_mapper.validate_mapped_record(mapped_sales, 'sales')
    print(f"  Sales validation: {'✅ Valid' if not validation_errors else '❌ Errors: ' + str(validation_errors)}")
    
    # Test customer record mapping
    customer_record = {
        'имя': 'Иван Петров',
        'телефон': '8 123 456 78 90',
        'email': '<EMAIL>',
        'адрес': 'ул. Примерная, д. 123'
    }
    
    mapped_customer = data_mapper.map_record_fields(customer_record, 'customers')
    print(f"  Customer mapping: {customer_record} -> {mapped_customer}")
    
    # Validate mapped record
    validation_errors = data_mapper.validate_mapped_record(mapped_customer, 'customers')
    print(f"  Customer validation: {'✅ Valid' if not validation_errors else '❌ Errors: ' + str(validation_errors)}")

def test_export_data_structure():
    """Test export data structure creation"""
    print("\n📊 Testing Export Data Structure...")
    
    # Sample data for export
    sample_sales_data = [
        {
            'id': 1,
            'order_date': '25.12.2023',
            'total_amount': 1234.56,
            'payment_method': 'Наличные',
            'department': 'Основной зал'
        },
        {
            'id': 2,
            'order_date': '26.12.2023',
            'total_amount': 2345.67,
            'payment_method': 'Карта',
            'department': 'Летняя терраса'
        }
    ]
    
    # Create export structure
    export_data = {
        'export_info': {
            'timestamp': datetime.now().isoformat(),
            'system': 'Restaurant Management System',
            'version': '1.0',
            'tables_count': 1,
            'total_records': len(sample_sales_data)
        },
        'data': {
            'sales': {
                'table_info': {
                    'name': 'Данные о Продажах',
                    'records_count': len(sample_sales_data)
                },
                'records': sample_sales_data
            }
        }
    }
    
    print(f"  Export structure created: {json.dumps(export_data, ensure_ascii=False, indent=2, default=str)}")

def main():
    """Run all tests"""
    print("🚀 Starting Data Export/Import System Tests\n")
    
    try:
        test_data_validator()
        test_data_transformer()
        test_data_mapper()
        test_export_data_structure()
        
        print("\n✅ All tests completed successfully!")
        print("📊 Advanced Data Export/Import System is ready for use.")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
