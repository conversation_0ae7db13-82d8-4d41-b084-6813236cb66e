#!/usr/bin/env python3
"""
Comprehensive diagnostic test for Restaurant Management System
Tests all imports and potential error sources
"""

import sys
import os
import traceback

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """Test basic Python imports"""
    print("=" * 60)
    print("TESTING BASIC IMPORTS")
    print("=" * 60)
    
    basic_modules = [
        'tkinter',
        'sqlite3', 
        'datetime',
        'json',
        'os',
        'sys',
        'threading',
        'logging'
    ]
    
    for module in basic_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except Exception as e:
            print(f"❌ {module}: {e}")

def test_external_packages():
    """Test external package imports"""
    print("\n" + "=" * 60)
    print("TESTING EXTERNAL PACKAGES")
    print("=" * 60)
    
    external_packages = [
        'pandas',
        'matplotlib',
        'seaborn',
        'numpy',
        'schedule',
        'psutil',
        'xlsxwriter',
        'openpyxl',
        'reportlab',
        'pillow',
        'ttkthemes',
        'customtkinter'
    ]
    
    for package in external_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except Exception as e:
            print(f"❌ {package}: {e}")

def test_project_modules():
    """Test project module imports"""
    print("\n" + "=" * 60)
    print("TESTING PROJECT MODULES")
    print("=" * 60)
    
    project_modules = [
        'gui.main_window',
        'gui.simple_login',
        'database.db_manager',
        'modules.database_manager',
        'utils.logger',
        'utils.notification_system',
        'utils.backup_system',
        'modules.vendor_management'
    ]
    
    for module in project_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except Exception as e:
            print(f"❌ {module}: {e}")
            traceback.print_exc()

def test_phase3_modules():
    """Test Phase 3 module imports"""
    print("\n" + "=" * 60)
    print("TESTING PHASE 3 MODULES")
    print("=" * 60)
    
    phase3_modules = [
        'modules.customer_relationship_management',
        'modules.loyalty_rewards_system',
        'modules.advanced_business_intelligence',
        'modules.table_reservation_queue_management',
        'modules.supply_chain_vendor_management',
        'modules.financial_planning_budgeting'
    ]
    
    for module in phase3_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except Exception as e:
            print(f"❌ {module}: {e}")
            traceback.print_exc()

def test_main_window_creation():
    """Test MainWindow creation"""
    print("\n" + "=" * 60)
    print("TESTING MAIN WINDOW CREATION")
    print("=" * 60)
    
    try:
        import tkinter as tk
        from gui.main_window import MainWindow
        from database.db_manager import DatabaseManager
        
        # Create test environment
        root = tk.Tk()
        root.withdraw()
        
        db_manager = DatabaseManager('test_diagnostic.db')
        selected_db = {'name': 'Test DB', 'file_path': 'test_diagnostic.db'}
        
        main_window = MainWindow(selected_db)
        main_window.db_manager = db_manager
        
        print("✅ MainWindow created successfully")
        
        # Test navigation methods
        navigation_methods = [
            'show_customer_relationship_management',
            'show_loyalty_rewards_system',
            'show_advanced_business_intelligence',
            'show_table_reservation_queue_management',
            'show_supply_chain_vendor_management',
            'show_financial_planning_budgeting'
        ]
        
        for method_name in navigation_methods:
            if hasattr(main_window, method_name):
                print(f"✅ Method {method_name} exists")
            else:
                print(f"❌ Method {method_name} missing")
        
        root.destroy()
        
        # Clean up test database
        if os.path.exists('test_diagnostic.db'):
            os.remove('test_diagnostic.db')
            
    except Exception as e:
        print(f"❌ MainWindow creation failed: {e}")
        traceback.print_exc()

def main():
    """Run all diagnostic tests"""
    print("RESTAURANT MANAGEMENT SYSTEM - DIAGNOSTIC TEST")
    print("=" * 60)
    
    test_basic_imports()
    test_external_packages()
    test_project_modules()
    test_phase3_modules()
    test_main_window_creation()
    
    print("\n" + "=" * 60)
    print("DIAGNOSTIC TEST COMPLETED")
    print("=" * 60)

if __name__ == "__main__":
    main()
