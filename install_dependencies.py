#!/usr/bin/env python3
"""
Скрипт установки зависимостей для Системы Управления Рестораном
Автоматически устанавливает все необходимые пакеты
"""

import subprocess
import sys
import os

def install_package(package):
    """Установить пакет через pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} установлен успешно")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Ошибка установки {package}: {e}")
        return False

def check_package(package_name):
    """Проверить, установлен ли пакет"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    """Основная функция установки"""
    print("=" * 60)
    print("🏪 Система Управления Рестораном")
    print("📦 Установка зависимостей")
    print("=" * 60)
    
    # Список необходимых пакетов
    packages = [
        "pandas>=1.5.0",
        "openpyxl>=3.0.0", 
        "matplotlib>=3.5.0",
        "seaborn>=0.11.0",
        "pillow>=9.0.0",
        "numpy>=1.21.0",
        "schedule>=1.2.0",
        "psutil>=5.9.0",
        "xlsxwriter>=3.0.0",
        "python-dateutil>=2.8.2",
        "reportlab>=3.6.0"
    ]
    
    # Проверить, какие пакеты уже установлены
    print("\n🔍 Проверка установленных пакетов...")
    
    installed = []
    to_install = []
    
    for package in packages:
        package_name = package.split(">=")[0].split("==")[0]
        if check_package(package_name):
            print(f"✅ {package_name} уже установлен")
            installed.append(package_name)
        else:
            print(f"❌ {package_name} не установлен")
            to_install.append(package)
    
    if not to_install:
        print("\n🎉 Все пакеты уже установлены!")
        return
    
    print(f"\n📦 Необходимо установить {len(to_install)} пакетов:")
    for package in to_install:
        print(f"  • {package}")
    
    # Установить недостающие пакеты
    print("\n🚀 Начинаем установку...")
    
    success_count = 0
    failed_packages = []
    
    for package in to_install:
        print(f"\n📦 Устанавливаем {package}...")
        if install_package(package):
            success_count += 1
        else:
            failed_packages.append(package)
    
    # Результаты установки
    print("\n" + "=" * 60)
    print("📊 РЕЗУЛЬТАТЫ УСТАНОВКИ")
    print("=" * 60)
    
    print(f"✅ Успешно установлено: {success_count}")
    print(f"❌ Ошибки установки: {len(failed_packages)}")
    
    if failed_packages:
        print("\n❌ Пакеты с ошибками:")
        for package in failed_packages:
            print(f"  • {package}")
        print("\n💡 Попробуйте установить их вручную:")
        print(f"pip install {' '.join(failed_packages)}")
    else:
        print("\n🎉 Все зависимости установлены успешно!")
        print("\n🚀 Теперь вы можете запустить систему:")
        print("python main.py")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
