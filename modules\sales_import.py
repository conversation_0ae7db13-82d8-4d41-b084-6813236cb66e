"""
Sales Data Import Module for Restaurant Accounting System
"""

import pandas as pd
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from datetime import datetime
from typing import List, Dict, Any
from gui.styles import ModernStyles
from localization.russian import *
import json
import os

class SalesImportWindow:
    """Window for importing and managing sales data from CSV files"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.imported_data = []
        self.payment_codes = {}
        self.load_payment_codes()
        self.create_window()

    def load_payment_codes(self):
        """Загрузить коды способов оплаты из файла"""
        try:
            codes_file = 'data/payment_codes.json'
            if os.path.exists(codes_file):
                with open(codes_file, 'r', encoding='utf-8') as f:
                    self.payment_codes = json.load(f)
                print(f"✅ Загружено {len(self.payment_codes)} кодов способов оплаты")
            else:
                print("⚠️ Файл кодов способов оплаты не найден, используются значения по умолчанию")
                # Расширенный набор кодов по умолчанию
                self.payment_codes = {
                    # Числовые коды
                    '1': 'Наличные',
                    '2': 'Банковская карта',
                    '3': 'Безналичный расчёт',
                    '4': 'Электронные деньги',
                    '5': 'Мобильный платёж',
                    '6': 'Криптовалюта',
                    '7': 'Подарочная карта',
                    '8': 'Бонусные баллы',
                    '9': 'Кредит/Рассрочка',
                    '10': 'Банковский перевод',

                    # Английские коды
                    'CASH': 'Наличные',
                    'CARD': 'Банковская карта',
                    'ONLINE': 'Онлайн платёж',
                    'MOBILE': 'Мобильный платёж',
                    'CRYPTO': 'Криптовалюта',
                    'GIFT': 'Подарочная карта',
                    'BONUS': 'Бонусы',
                    'CREDIT': 'Кредит',
                    'TRANSFER': 'Перевод',

                    # Русские варианты (часто встречающиеся в CSV)
                    'наличный': 'Наличные',
                    'наличные': 'Наличные',
                    'наличка': 'Наличные',
                    'карта': 'Банковская карта',
                    'картой': 'Банковская карта',
                    'банковская карта': 'Банковская карта',
                    'безналичный': 'Безналичный расчёт',
                    'безнал': 'Безналичный расчёт',
                    'перевод': 'Банковский перевод',
                    'онлайн': 'Онлайн платёж',
                    'мобильный': 'Мобильный платёж',
                    'электронный': 'Электронные деньги',
                    'подарочная': 'Подарочная карта',
                    'бонус': 'Бонусные баллы',
                    'бонусы': 'Бонусные баллы',
                    'кредит': 'Кредит/Рассрочка',
                    'рассрочка': 'Кредит/Рассрочка'
                }
                # Сохранить коды по умолчанию в файл
                self.save_default_payment_codes()
        except Exception as e:
            print(f"❌ Ошибка загрузки кодов способов оплаты: {e}")
            self.payment_codes = {}

    def save_default_payment_codes(self):
        """Сохранить коды по умолчанию в файл"""
        try:
            codes_file = 'data/payment_codes.json'
            os.makedirs(os.path.dirname(codes_file), exist_ok=True)

            with open(codes_file, 'w', encoding='utf-8') as f:
                json.dump(self.payment_codes, f, ensure_ascii=False, indent=2)
            print(f"✅ Коды способов оплаты по умолчанию сохранены в {codes_file}")
        except Exception as e:
            print(f"❌ Ошибка сохранения кодов по умолчанию: {e}")

    def translate_payment_method(self, payment_code):
        """Перевести код способа оплаты в название"""
        if not payment_code:
            return 'Наличные'  # По умолчанию

        # Очистить код от лишних символов
        clean_code = str(payment_code).strip()

        # Поиск точного совпадения
        if clean_code in self.payment_codes:
            return self.payment_codes[clean_code]

        # Поиск без учёта регистра
        for code, name in self.payment_codes.items():
            if str(code).lower() == clean_code.lower():
                return name

        # Умный поиск по частичному совпадению и синонимам
        clean_lower = clean_code.lower()

        # Словарь синонимов для способов оплаты
        synonyms = {
            'наличный': 'Наличные',
            'наличные': 'Наличные',
            'наличка': 'Наличные',
            'cash': 'Наличные',
            'карта': 'Банковская карта',
            'картой': 'Банковская карта',
            'банковская': 'Банковская карта',
            'card': 'Банковская карта',
            'безналичный': 'Безналичный расчёт',
            'безнал': 'Безналичный расчёт',
            'перевод': 'Банковский перевод',
            'онлайн': 'Онлайн платёж',
            'online': 'Онлайн платёж',
            'мобильный': 'Мобильный платёж',
            'mobile': 'Мобильный платёж',
            'электронный': 'Электронные деньги',
            'crypto': 'Криптовалюта',
            'криптовалюта': 'Криптовалюта',
            'подарочная': 'Подарочная карта',
            'подарочный': 'Подарочная карта',
            'gift': 'Подарочная карта',
            'бонус': 'Бонусные баллы',
            'бонусы': 'Бонусные баллы',
            'bonus': 'Бонусные баллы',
            'кредит': 'Кредит/Рассрочка',
            'рассрочка': 'Кредит/Рассрочка',
            'credit': 'Кредит/Рассрочка'
        }

        # Поиск по синонимам
        for synonym, payment_name in synonyms.items():
            if synonym in clean_lower or clean_lower in synonym:
                return payment_name

        # Поиск по частичному совпадению в названиях способов оплаты
        for code, name in self.payment_codes.items():
            name_lower = name.lower()
            if clean_lower in name_lower or name_lower in clean_lower:
                return name

        # Если ничего не найдено, попробовать определить по ключевым словам
        if any(word in clean_lower for word in ['наличн', 'cash', 'деньги']):
            return 'Наличные'
        elif any(word in clean_lower for word in ['карт', 'card', 'банк']):
            return 'Банковская карта'
        elif any(word in clean_lower for word in ['безнал', 'перевод', 'онлайн']):
            return 'Безналичный расчёт'

        # Если код не найден, вернуть исходное значение с пометкой
        return f"{clean_code} (неизвестный код)"

    def open_payment_codes_manager(self):
        """Открыть менеджер кодов способов оплаты"""
        try:
            from modules.payment_codes_manager import PaymentCodesManager
            # Передаем и parent, и db_manager
            codes_manager = PaymentCodesManager(self.window, self.db_manager)

            # Показать окно менеджера кодов
            codes_manager.show()

            # После закрытия менеджера кодов, перезагрузить коды
            def on_codes_window_close():
                self.load_payment_codes()
                print("🔄 Коды способов оплаты перезагружены")

            # Привязать событие закрытия окна
            if hasattr(codes_manager, 'window') and codes_manager.window:
                codes_manager.window.protocol("WM_DELETE_WINDOW",
                    lambda: [codes_manager.window.destroy(), on_codes_window_close()])

        except ImportError as e:
            messagebox.showerror("Ошибка",
                f"Не удалось загрузить модуль управления кодами способов оплаты:\n{e}")
        except Exception as e:
            messagebox.showerror("Ошибка",
                f"Ошибка открытия менеджера кодов способов оплаты:\n{e}")

    def create_window(self):
        """Create the sales import window"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(IMPORT_TITLE)
        self.window.geometry("1400x800")  # Larger default size
        self.window.configure(bg=ModernStyles.COLORS['bg_main'])

        # Make window resizable and allow full screen
        self.window.resizable(True, True)
        self.window.minsize(800, 600)  # Minimum size

        # Make window more stable but not modal (remove grab_set for better UX)
        self.window.transient(self.parent)

        # Center the window
        self.window.update_idletasks()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - 1400) // 2
        y = (screen_height - 800) // 2
        self.window.geometry(f"1400x800+{x}+{y}")

        # Add maximize button functionality
        self.window.state('normal')  # Ensure window can be maximized

        # Prevent window from closing unexpectedly
        self.window.protocol("WM_DELETE_WINDOW", self.on_window_close)

        # Add keyboard shortcuts
        self.window.bind('<F11>', lambda e: self.toggle_maximize())
        self.window.bind('<Control-o>', lambda e: self.browse_file())
        self.window.bind('<Control-l>', lambda e: self.load_csv_data())
        self.window.bind('<Control-s>', lambda e: self.save_to_database())
        self.window.bind('<Escape>', lambda e: self.on_window_close())

        # Make window focusable for keyboard shortcuts
        self.window.focus_set()
        
        # Main container
        main_frame = tk.Frame(self.window, **ModernStyles.WIDGET_STYLES['frame_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Title and window controls
        title_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_main'])
        title_frame.pack(fill='x', pady=(0, 20))

        title_label = tk.Label(title_frame, text=IMPORT_SUBTITLE,
                              **ModernStyles.WIDGET_STYLES['label_title'])
        title_label.pack(side='left')

        # Window control buttons
        controls_frame = tk.Frame(title_frame, bg=ModernStyles.COLORS['bg_main'])
        controls_frame.pack(side='right')

        maximize_btn = tk.Button(controls_frame, text="⛶ Развернуть",
                                command=self.toggle_maximize,
                                bg=ModernStyles.COLORS['secondary'],
                                fg=ModernStyles.COLORS['text_white'],
                                font=ModernStyles.FONTS['small'],
                                relief='flat',
                                padx=10,
                                pady=5,
                                cursor='hand2')
        maximize_btn.pack(side='right', padx=(10, 0))

        # Кнопка для принудительного применения тёмно-синих заголовков
        style_btn = tk.Button(controls_frame, text="🎨 Тёмно-синий",
                             command=self.force_dark_blue_headers,
                             bg='#1e3a8a',
                             fg='#ffffff',
                             font=ModernStyles.FONTS['small'],
                             relief='flat',
                             padx=10,
                             pady=5,
                             cursor='hand2')
        style_btn.pack(side='right', padx=(10, 0))

        self.maximize_btn = maximize_btn
        self.is_maximized = False

        # File selection frame
        file_frame = ModernStyles.create_card_frame(main_frame)
        file_frame.pack(fill='x', pady=(0, 20))

        tk.Label(file_frame, text=SELECT_CSV_FILE,
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w')
        
        file_select_frame = tk.Frame(file_frame, bg=ModernStyles.COLORS['bg_card'])
        file_select_frame.pack(fill='x', pady=(10, 0))
        
        self.file_path_var = tk.StringVar()
        file_entry = tk.Entry(file_select_frame, textvariable=self.file_path_var,
                             **ModernStyles.WIDGET_STYLES['entry'])
        file_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))
        
        browse_btn = tk.Button(file_select_frame, text=BROWSE_BUTTON,
                              command=self.browse_file,
                              **ModernStyles.WIDGET_STYLES['button_secondary'])
        browse_btn.pack(side='right')

        load_btn = tk.Button(file_select_frame, text=LOAD_DATA_BUTTON,
                            command=self.load_csv_data,
                            **ModernStyles.WIDGET_STYLES['button_primary'])
        load_btn.pack(side='right', padx=(0, 10))

        # Кнопка управления кодами способов оплаты
        codes_btn = tk.Button(file_select_frame, text="💳 Коды Оплаты",
                             command=self.open_payment_codes_manager,
                             bg='#6366f1', fg='white',
                             font=('Cambria', 10, 'bold'),
                             relief='flat', padx=15, pady=5,
                             cursor='hand2')
        codes_btn.pack(side='right', padx=(0, 10))
        
        # Data preview frame
        preview_frame = ModernStyles.create_card_frame(main_frame)
        preview_frame.pack(fill='both', expand=True, pady=(0, 20))
        
        tk.Label(preview_frame, text=DATA_PREVIEW,
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w')
        
        # Treeview for data display
        tree_frame = tk.Frame(preview_frame, bg=ModernStyles.COLORS['bg_card'])
        tree_frame.pack(fill='both', expand=True, pady=(10, 0))
        
        # Create Treeview with scrollbars and force dark blue headers
        self.tree = ttk.Treeview(tree_frame, style="Modern.Treeview")

        # Force apply dark blue header style with Cambria font
        style = ttk.Style()
        style.configure("Modern.Treeview.Heading",
                       background='#1e3a8a',  # Принудительно тёмно-синий
                       foreground='#ffffff',  # Белый текст
                       font=('Cambria', 12, 'bold italic'),  # Cambria шрифт, жирный, курсив
                       relief='solid',
                       borderwidth=2,
                       fieldbackground='#1e3a8a')  # Дополнительный фон
        
        # Define columns based on CSV structure
        columns = (ORDER_DATE, ORDER_NUMBER, PAYMENT_METHOD, DEPARTMENT,
                  DISH_CODE, DISH_NAME, QUANTITY, PRICE_PER_DISH, TOTAL_AMOUNT)

        # Отладочная информация для проверки столбцов
        print(f"🔍 Определенные столбцы: {columns}")
        print(f"🔍 DISH_CODE константа: '{DISH_CODE}'")

        self.tree['columns'] = columns
        self.tree['show'] = 'headings'

        # Configure columns with better widths for Russian text
        column_widths = {
            ORDER_DATE: 100,
            ORDER_NUMBER: 100,
            PAYMENT_METHOD: 120,
            DEPARTMENT: 80,
            DISH_CODE: 100,
            DISH_NAME: 250,  # Wider for Russian dish names
            QUANTITY: 80,
            PRICE_PER_DISH: 100,
            TOTAL_AMOUNT: 100
        }

        for col in columns:
            self.tree.heading(col, text=col)
            width = column_widths.get(col, 120)
            self.tree.column(col, width=width, minwidth=60, stretch=True)

        # Принудительно применить тёмно-синий стиль к заголовкам с Cambria шрифтом
        self.tree.update()
        style.configure("Modern.Treeview.Heading",
                       background='#1e3a8a',  # Тёмно-синий
                       foreground='#ffffff',  # Белый текст
                       font=('Cambria', 12, 'bold italic'),  # Cambria шрифт, жирный, курсив
                       fieldbackground='#1e3a8a')  # Дополнительный фон

        # Обновить отображение
        self.tree.update_idletasks()

        # Альтернативный метод: прямое изменение цвета заголовков
        try:
            # Получить все дочерние элементы заголовков
            for child in self.tree.winfo_children():
                if hasattr(child, 'configure'):
                    try:
                        child.configure(bg='#1e3a8a', fg='#ffffff')
                    except:
                        pass
        except:
            pass
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient='vertical', command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient='horizontal', command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')
        
        # Action buttons frame
        action_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_main'])
        action_frame.pack(fill='x')
        
        # Edit/Delete buttons
        edit_btn = tk.Button(action_frame, text=EDIT_SELECTED,
                            command=self.edit_selected,
                            **ModernStyles.WIDGET_STYLES['button_warning'])
        edit_btn.pack(side='left', padx=(0, 10))

        delete_btn = tk.Button(action_frame, text=DELETE_SELECTED,
                              command=self.delete_selected,
                              **ModernStyles.WIDGET_STYLES['button_danger'])
        delete_btn.pack(side='left', padx=(0, 10))

        # Save buttons
        save_btn = tk.Button(action_frame, text=SAVE_TO_DATABASE,
                            command=self.save_to_database,
                            **ModernStyles.WIDGET_STYLES['button_success'])
        save_btn.pack(side='right')

        cancel_btn = tk.Button(action_frame, text=CANCEL_BUTTON,
                              command=self.window.destroy,
                              **ModernStyles.WIDGET_STYLES['button_secondary'])
        cancel_btn.pack(side='right', padx=(0, 10))

        # Status bar with keyboard shortcuts
        status_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['primary'], height=25)
        status_frame.pack(fill='x', pady=(10, 0))
        status_frame.pack_propagate(False)

        shortcuts_text = "Горячие клавиши: F11-Полный экран | Ctrl+O-Открыть | Ctrl+L-Загрузить | Ctrl+S-Сохранить | Esc-Закрыть"
        shortcuts_label = tk.Label(status_frame, text=shortcuts_text,
                                  font=ModernStyles.FONTS['small'],
                                  fg=ModernStyles.COLORS['text_white'],
                                  bg=ModernStyles.COLORS['primary'])
        shortcuts_label.pack(side='left', padx=10, pady=3)
    
    def browse_file(self):
        """Open file dialog to select CSV file"""
        try:
            # Ensure the window stays on top and focused
            self.window.lift()
            self.window.focus_force()

            file_path = filedialog.askopenfilename(
                parent=self.window,
                title="Select CSV File",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                initialdir="."
            )

            # Bring window back to front after dialog
            self.window.lift()
            self.window.focus_force()

            if file_path:
                self.file_path_var.set(file_path)
                print(f"Selected file: {file_path}")  # Debug output

                # Show success message
                messagebox.showinfo("Файл выбран", f"Выбранный файл:\n{file_path}")
            else:
                print("No file selected")  # Debug output

        except Exception as e:
            print(f"Error in browse_file: {e}")
            messagebox.showerror("Ошибка", f"Не удалось открыть диалог выбора файла: {str(e)}")
    
    def load_csv_data(self):
        """Load and parse CSV data according to specified column mapping"""
        file_path = self.file_path_var.get()
        if not file_path:
            messagebox.showerror("Ошибка", "Пожалуйста, сначала выберите CSV файл.")
            return

        try:
            print(f"Loading CSV file: {file_path}")  # Debug output

            # Check if file exists
            import os
            if not os.path.exists(file_path):
                messagebox.showerror("Ошибка", f"Файл не найден: {file_path}")
                return

            # Read CSV with multiple encoding and separator attempts
            df = None
            # Try Russian/Cyrillic encodings first for proper Russian text display
            encoding_attempts = ['cp1251', 'windows-1251', 'cp866', 'koi8-r', 'utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
            separator_attempts = [';', ',', '\t', '|']  # Try semicolon first

            best_result = None
            best_columns = 0
            best_russian_score = 0

            for encoding in encoding_attempts:
                for separator in separator_attempts:
                    try:
                        print(f"Trying encoding: {encoding}, separator: '{separator}'")
                        test_df = pd.read_csv(file_path, header=None, encoding=encoding,
                                            sep=separator, on_bad_lines='skip', engine='python')

                        # Check if we got meaningful data
                        if test_df.shape[1] > 1 and test_df.shape[0] > 0:
                            # Score this result based on columns and Russian text quality
                            russian_score = self.score_russian_text(test_df)

                            # Prefer results with more columns and better Russian text
                            is_better = (test_df.shape[1] > best_columns or
                                       (test_df.shape[1] == best_columns and russian_score > best_russian_score))

                            if is_better:
                                best_result = {
                                    'df': test_df,
                                    'encoding': encoding,
                                    'separator': separator,
                                    'shape': test_df.shape,
                                    'russian_score': russian_score
                                }
                                best_columns = test_df.shape[1]
                                best_russian_score = russian_score
                                print(f"New best: {encoding} + '{separator}' -> {test_df.shape}, Russian score: {russian_score}")

                    except UnicodeDecodeError as e:
                        print(f"Encoding {encoding} with separator '{separator}' failed: {e}")
                        continue
                    except Exception as e:
                        print(f"Other error with {encoding} and separator '{separator}': {e}")
                        continue

            if best_result:
                df = best_result['df']
                print(f"Using best result: {best_result['encoding']} + '{best_result['separator']}' -> {best_result['shape']}")
                print(f"Russian text quality score: {best_result['russian_score']}")
            else:
                df = None

            # If all encoding attempts failed, try manual reading
            if df is None:
                try:
                    print("Trying manual CSV reading...")
                    df = self.read_csv_with_encoding_detection(file_path)
                    print(f"CSV loaded manually. Shape: {df.shape}")
                except Exception as e:
                    messagebox.showerror("Ошибка",
                        f"Не удалось прочитать CSV файл ни одним из методов кодировки.\n"
                        f"Пожалуйста, проверьте формат файла и кодировку.\n"
                        f"Ошибка: {str(e)}")
                    return

            # Flexible column mapping - adapt to available columns
            available_columns = df.shape[1]
            print(f"Available columns: {available_columns}")  # Debug output

            # Показать образец данных для анализа структуры
            print("📊 Анализ структуры CSV файла:")
            for col_idx in range(min(available_columns, 20)):  # Показать первые 20 столбцов
                sample_values = df.iloc[:3, col_idx].dropna().astype(str).tolist()
                print(f"  Столбец {col_idx}: {sample_values}")

            # Define flexible column mapping based on available columns and detected format
            if available_columns >= 30:
                # Your specific CSV format (33 columns with semicolon separator)
                # Based on analysis: Date(0), Qty(3), Price(4), Total(5), Dept(7), Dish(8), Order(9), Payment(14), DishCode(16)
                column_mapping = {
                    'order_date': 0,      # Column 1: Date (YYYYMMDD format)
                    'quantity': 3,        # Column 4: Quantity (with comma decimal)
                    'price_per_dish': 4,  # Column 5: Price per dish (with comma decimal)
                    'total_amount': 5,    # Column 6: Total amount (with comma decimal)
                    'department': 7,      # Column 8: Department (Бар, etc.)
                    'dish_name': 8,       # Column 9: Dish name
                    'order_number': 9,    # Column 10: Order number
                    'payment_method': 14, # Column 15: Payment method codes (0-based index = 14)
                    'dish_code': 16       # Column 17: Dish code (0-based index = 16)
                }
                print("Using your specific 33-column CSV format mapping with payment method in column 15 and dish code in column 17")
            elif available_columns >= 17:
                # Full mapping for 17+ columns (original specification)
                column_mapping = {
                    'order_date': 0,      # Column 1
                    'quantity': 3,        # Column 4
                    'price_per_dish': 4,  # Column 5
                    'total_amount': 5,    # Column 6
                    'department': 7,      # Column 8
                    'dish_name': 8,       # Column 9
                    'order_number': 9,    # Column 10
                    'payment_method': 14, # Column 15
                    'dish_code': 16       # Column 17
                }
                print("Using full 17-column mapping")
            elif available_columns >= 6:
                # Simplified mapping for 6+ columns
                column_mapping = {
                    'order_date': 0,      # Column 1
                    'quantity': 1,        # Column 2
                    'price_per_dish': 2,  # Column 3
                    'total_amount': 3,    # Column 4
                    'dish_name': 4,       # Column 5
                    'payment_method': 5,  # Column 6
                    'department': None,   # Default value
                    'order_number': None, # Auto-generate
                    'dish_code': None     # Auto-generate
                }
                print("Using simplified 6-column mapping")
            elif available_columns >= 3:
                # Very basic mapping for 3+ columns (minimal data)
                column_mapping = {
                    'order_date': 0,      # Column 1 - try to parse as date
                    'quantity': None,     # Default to 1
                    'price_per_dish': 1,  # Column 2 - assume price
                    'total_amount': 2,    # Column 3 - assume total
                    'dish_name': None,    # Default name
                    'payment_method': None, # Default payment
                    'department': None,   # Default department
                    'order_number': None, # Auto-generate
                    'dish_code': None     # Auto-generate
                }
                print("Using minimal 3-column mapping")
            else:
                messagebox.showerror("Ошибка",
                    f"CSV файл должен содержать минимум 3 столбца.\n"
                    f"Найдено: {available_columns} столбцов\n"
                    f"Минимум требуется: 3 столбца")
                return
            
            # Extract required columns with better error handling
            self.imported_data = []
            skipped_rows = 0
            payment_codes_replaced = 0

            for index, row in df.iterrows():
                try:
                    # Check if row has enough columns for the selected mapping
                    min_required = 6 if available_columns >= 6 else available_columns
                    if len(row) < min_required:
                        print(f"Skipping row {index + 1}: insufficient columns ({len(row)})")
                        skipped_rows += 1
                        continue

                    # Extract data using flexible column mapping
                    def safe_get_value(col_index, default=''):
                        if col_index is None:
                            return default
                        if col_index >= len(row):
                            return default
                        value = row.iloc[col_index] if pd.notna(row.iloc[col_index]) else default
                        return str(value).strip() if value != default else default

                    # Debug: Print first few rows to understand structure
                    if index < 3:
                        print(f"Row {index + 1} data: {[str(x) for x in row.values]}")

                    # Extract date with more flexible parsing
                    date_val = safe_get_value(column_mapping['order_date'], '')
                    order_date = self.parse_date(date_val)
                    if not order_date:
                        print(f"Skipping row {index + 1}: invalid date '{date_val}'")
                        skipped_rows += 1
                        continue

                    # Extract and validate numeric fields with better error handling
                    try:
                        # Get raw values
                        quantity_val = safe_get_value(column_mapping['quantity'], '1')
                        price_val = safe_get_value(column_mapping['price_per_dish'], '0')
                        total_val = safe_get_value(column_mapping['total_amount'], '0')

                        # Clean and parse numeric values
                        quantity = self.parse_numeric(quantity_val, 1.0)
                        price_per_dish = self.parse_numeric(price_val, 0.0)
                        total_amount = self.parse_numeric(total_val, 0.0)

                        # Calculate missing values if needed
                        if total_amount == 0.0 and quantity > 0 and price_per_dish > 0:
                            total_amount = quantity * price_per_dish
                        elif price_per_dish == 0.0 and quantity > 0 and total_amount > 0:
                            price_per_dish = total_amount / quantity
                        elif quantity == 0.0 and price_per_dish > 0 and total_amount > 0:
                            quantity = total_amount / price_per_dish

                        # Validate that we have meaningful data
                        if quantity <= 0 or (price_per_dish <= 0 and total_amount <= 0):
                            print(f"Skipping row {index + 1}: insufficient numeric data (qty={quantity}, price={price_per_dish}, total={total_amount})")
                            skipped_rows += 1
                            continue

                    except (ValueError, TypeError) as e:
                        print(f"Skipping row {index + 1}: invalid numeric data - {e}")
                        skipped_rows += 1
                        continue

                    # Extract text fields with defaults and proper encoding handling
                    order_number = self.clean_text(safe_get_value(column_mapping['order_number'], f'ORD{index+1:04d}'))

                    # Получить код способа оплаты из CSV файла (столбец 15, индекс 14)
                    if column_mapping['payment_method'] is not None:
                        # Читаем код оплаты из столбца 15
                        payment_code = self.clean_text(safe_get_value(column_mapping['payment_method'], ''))
                        if not payment_code:  # Если столбец пустой, используем значение по умолчанию
                            payment_code = 'наличный'
                    else:
                        # Если столбец не найден, используем значение по умолчанию
                        payment_code = 'наличный'

                    # Автоматически заменить код на читаемое название
                    payment_method = self.translate_payment_method(payment_code)

                    # Подсчитать замены кодов (если код был успешно переведен)
                    if payment_code != payment_method and not "(неизвестный код)" in payment_method:
                        payment_codes_replaced += 1

                    # Извлечь остальные текстовые поля
                    department = self.clean_text(safe_get_value(column_mapping['department'], 'Кухня'))

                    # Получить код блюда из CSV файла (столбец 17, индекс 16)
                    if column_mapping['dish_code'] is not None:
                        dish_code = self.clean_text(safe_get_value(column_mapping['dish_code'], ''))
                        if not dish_code:  # Если столбец пустой, генерируем код
                            dish_code = f'DISH{index+1:04d}'
                    else:
                        dish_code = f'DISH{index+1:04d}'  # Автогенерация если столбец не найден

                    dish_name = self.clean_text(safe_get_value(column_mapping['dish_name'], f'Блюдо {index+1}'))

                    # Отладочная информация для первых нескольких строк
                    if index < 5:
                        print(f"Row {index + 1}: Payment='{payment_code}' -> '{payment_method}', DishCode='{dish_code}', DishName='{dish_name}'")

                    record = {
                        'order_date': order_date,
                        'order_number': order_number,
                        'payment_method': payment_method,
                        'department': department,
                        'dish_code': dish_code,
                        'dish_name': dish_name,
                        'quantity': quantity,
                        'price_per_dish': price_per_dish,
                        'total_amount': total_amount
                    }
                    self.imported_data.append(record)

                except Exception as e:
                    print(f"Error processing row {index + 1}: {e}")
                    skipped_rows += 1
                    continue
            
            self.display_data()

            # Show summary message
            total_rows = len(df)
            loaded_rows = len(self.imported_data)

            # Подготовить сообщение с информацией о замене кодов
            codes_info = f"\n• Кодов способов оплаты заменено: {payment_codes_replaced}" if payment_codes_replaced > 0 else ""

            if skipped_rows > 0:
                messagebox.showinfo("Сводка импорта",
                    f"Импорт завершён:\n"
                    f"• Всего строк в файле: {total_rows}\n"
                    f"• Успешно загружено: {loaded_rows}\n"
                    f"• Пропущено строк: {skipped_rows}{codes_info}\n\n"
                    f"Проверьте консоль для подробностей о пропущенных строках.")
            else:
                messagebox.showinfo("Успех",
                    f"✅ Успешно загружено {loaded_rows} записей из CSV файла.{codes_info}")

            # Принудительно применить тёмно-синие заголовки после загрузки данных
            self.force_dark_blue_headers()

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось загрузить CSV файл: {str(e)}")
    
    def parse_date(self, date_value):
        """Parse date from various formats and return in дд.мм.гггг format"""
        if pd.isna(date_value) or not date_value or str(date_value).strip() == '':
            return datetime.now().strftime('%d.%m.%Y')

        try:
            # Clean the date string
            date_str = str(date_value).strip()

            # Skip empty or invalid values
            if not date_str or date_str.lower() in ['nan', 'null', 'none', '']:
                return datetime.now().strftime('%d.%m.%Y')

            # Try different date formats (YYYYMMDD first for your CSV)
            date_formats = [
                '%Y%m%d',       # 20220508 (your format)
                '%Y-%m-%d',     # 2024-01-15
                '%d/%m/%Y',     # 15/01/2024
                '%m/%d/%Y',     # 01/15/2024
                '%d-%m-%Y',     # 15-01-2024
                '%d.%m.%Y',     # 15.01.2024 (already in target format)
                '%Y/%m/%d',     # 2024/01/15
                '%d %m %Y',     # 15 01 2024
            ]

            for fmt in date_formats:
                try:
                    parsed_date = datetime.strptime(date_str, fmt).date()
                    # Return in дд.мм.гггг format
                    return parsed_date.strftime('%d.%m.%Y')
                except ValueError:
                    continue

            # Try pandas date parsing as fallback
            try:
                parsed_date = pd.to_datetime(date_str).date()
                # Return in дд.мм.гггг format
                return parsed_date.strftime('%d.%m.%Y')
            except:
                pass

            # If no format works, return current date in дд.мм.гггг format
            print(f"Could not parse date '{date_str}', using current date")
            return datetime.now().strftime('%d.%m.%Y')
        except Exception as e:
            print(f"Date parsing error for '{date_value}': {e}")
            return datetime.now().strftime('%d.%m.%Y')

    def parse_numeric(self, value, default=0.0):
        """Parse numeric value from string with various formats including European format"""
        if pd.isna(value) or not value:
            return default

        try:
            # Convert to string and clean
            value_str = str(value).strip()

            # Skip empty or invalid values
            if not value_str or value_str.lower() in ['nan', 'null', 'none', '']:
                return default

            # Remove common currency symbols
            cleaned = value_str.replace('₽', '').replace('$', '').replace('€', '').strip()

            # Handle European format (comma as decimal separator)
            # Check if it looks like European format: "3,00" or "1,5000"
            if ',' in cleaned and '.' not in cleaned:
                # European format: comma is decimal separator
                cleaned = cleaned.replace(',', '.')
            elif ',' in cleaned and '.' in cleaned:
                # Mixed format: assume comma is thousands separator, dot is decimal
                # Example: "1,234.56" -> remove comma
                cleaned = cleaned.replace(',', '')

            # Remove any remaining spaces
            cleaned = cleaned.replace(' ', '')

            # Handle negative values in parentheses
            if cleaned.startswith('(') and cleaned.endswith(')'):
                cleaned = '-' + cleaned[1:-1]

            # Try to convert to float
            result = float(cleaned)
            return result

        except (ValueError, TypeError) as e:
            print(f"Could not parse numeric value '{value}': {e}")
            return default
    
    def display_data(self):
        """Display imported data in the treeview"""
        # Clear existing data
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Отладочная информация
        print(f"🔍 Отображение данных: {len(self.imported_data)} записей")
        if self.imported_data:
            first_record = self.imported_data[0]
            print(f"🔍 Первая запись: {first_record}")
            print(f"🔍 Ключи записи: {list(first_record.keys())}")
            print(f"🔍 Код блюда в первой записи: '{first_record.get('dish_code', 'НЕ НАЙДЕН')}'")

        # Проверить столбцы таблицы
        print(f"🔍 Столбцы таблицы: {self.tree['columns']}")

        # Insert new data
        for i, record in enumerate(self.imported_data):
            values = (
                record['order_date'],
                record['order_number'],
                record['payment_method'],
                record['department'],
                record['dish_code'],
                record['dish_name'],
                f"{record['quantity']:.1f}",
                format_currency(record['price_per_dish']),
                format_currency(record['total_amount'])
            )

            # Отладочная информация для первых записей
            if i < 3:
                print(f"🔍 Запись {i+1} values: {values}")

            self.tree.insert('', 'end', values=values)
    
    def edit_selected(self):
        """Edit selected record"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a record to edit.")
            return
        
        # Get selected item index
        item = selection[0]
        item_index = self.tree.index(item)
        
        # Open edit dialog
        self.open_edit_dialog(item_index)
    
    def delete_selected(self):
        """Delete selected record"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a record to delete.")
            return
        
        if messagebox.askyesno("Confirm", "Are you sure you want to delete the selected record?"):
            item = selection[0]
            item_index = self.tree.index(item)
            
            # Remove from data and treeview
            del self.imported_data[item_index]
            self.tree.delete(item)
    
    def open_edit_dialog(self, index):
        """Open dialog to edit record"""
        record = self.imported_data[index]
        
        # Create edit window with larger size to show all fields
        edit_window = tk.Toplevel(self.window)
        edit_window.title("Редактировать Запись")
        edit_window.geometry("600x700")  # Увеличен размер для показа всех полей
        edit_window.configure(bg=ModernStyles.COLORS['bg_main'])
        edit_window.resizable(True, True)  # Разрешить изменение размера

        # Центрировать окно
        edit_window.update_idletasks()
        x = (edit_window.winfo_screenwidth() // 2) - (600 // 2)
        y = (edit_window.winfo_screenheight() // 2) - (700 // 2)
        edit_window.geometry(f"600x700+{x}+{y}")
        
        # Create form fields
        fields = {}
        field_names = [
            ('order_date', 'Order Date'),
            ('order_number', 'Order Number'),
            ('payment_method', 'Payment Method'),
            ('department', 'Department'),
            ('dish_code', 'Dish Code'),
            ('dish_name', 'Dish Name'),
            ('quantity', 'Quantity'),
            ('price_per_dish', 'Price per Dish'),
            ('total_amount', 'Total Amount')
        ]
        
        for i, (field_key, field_label) in enumerate(field_names):
            tk.Label(edit_window, text=field_label + ":",
                    **ModernStyles.WIDGET_STYLES['label_body']).pack(anchor='w', padx=20, pady=(10, 0))
            
            var = tk.StringVar(value=str(record[field_key]))
            entry = tk.Entry(edit_window, textvariable=var,
                           **ModernStyles.WIDGET_STYLES['entry'])
            entry.pack(fill='x', padx=20, pady=(0, 5))
            fields[field_key] = var
        
        # Buttons
        button_frame = tk.Frame(edit_window, bg=ModernStyles.COLORS['bg_main'])
        button_frame.pack(fill='x', padx=20, pady=20)
        
        def save_changes():
            try:
                # Update record
                self.imported_data[index] = {
                    'order_date': fields['order_date'].get(),
                    'order_number': fields['order_number'].get(),
                    'payment_method': fields['payment_method'].get(),
                    'department': fields['department'].get(),
                    'dish_code': fields['dish_code'].get(),
                    'dish_name': fields['dish_name'].get(),
                    'quantity': float(fields['quantity'].get()),
                    'price_per_dish': float(fields['price_per_dish'].get()),
                    'total_amount': float(fields['total_amount'].get())
                }
                
                self.display_data()
                edit_window.destroy()
                messagebox.showinfo("Success", "Record updated successfully.")
            except ValueError as e:
                messagebox.showerror("Error", "Please enter valid numeric values for quantity, price, and total.")
        
        tk.Button(button_frame, text="Save", command=save_changes,
                 **ModernStyles.WIDGET_STYLES['button_success']).pack(side='right')
        tk.Button(button_frame, text="Cancel", command=edit_window.destroy,
                 **ModernStyles.WIDGET_STYLES['button_secondary']).pack(side='right', padx=(0, 10))
    
    def save_to_database(self):
        """Save imported data to database"""
        if not self.imported_data:
            messagebox.showwarning("Warning", "No data to save.")
            return
        
        if messagebox.askyesno("Confirm", f"Save {len(self.imported_data)} records to database?"):
            success = self.db_manager.insert_sales_data(self.imported_data)
            if success:
                messagebox.showinfo("Success", "Data saved to database successfully.")
                self.window.destroy()
            else:
                messagebox.showerror("Error", "Failed to save data to database.")

    def read_csv_with_encoding_detection(self, file_path):
        """Read CSV with automatic encoding detection"""
        import csv

        # Try common encodings in order of likelihood
        encodings_to_try = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1', 'utf-16', 'ascii']

        for encoding in encodings_to_try:
            if encoding is None:
                continue

            try:
                data = []
                with open(file_path, 'r', encoding=encoding, errors='ignore') as file:
                    csv_reader = csv.reader(file)
                    line_num = 0

                    for row in csv_reader:
                        line_num += 1
                        try:
                            # Ensure we have at least 17 columns
                            while len(row) < 17:
                                row.append('')

                            # Take only the first 17 columns if there are more
                            if len(row) > 17:
                                row = row[:17]

                            data.append(row)

                        except Exception as e:
                            print(f"Skipping problematic line {line_num}: {e}")
                            continue

                # Convert to DataFrame
                df = pd.DataFrame(data)
                print(f"Successfully read with encoding: {encoding}")
                return df

            except Exception as e:
                print(f"Failed with encoding {encoding}: {e}")
                continue

        raise Exception("Could not read file with any encoding method")

    def read_csv_manually(self, file_path):
        """Manually read CSV file to handle problematic lines"""
        import csv
        data = []

        # Try multiple encodings
        encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding, errors='ignore') as file:
                    csv_reader = csv.reader(file)
                    line_num = 0

                    for row in csv_reader:
                        line_num += 1
                        try:
                            # Ensure we have at least 17 columns (pad with empty strings if needed)
                            while len(row) < 17:
                                row.append('')

                            # Take only the first 17 columns if there are more
                            if len(row) > 17:
                                row = row[:17]

                            data.append(row)

                        except Exception as e:
                            print(f"Skipping problematic line {line_num}: {e}")
                            continue

                # Convert to DataFrame
                df = pd.DataFrame(data)
                print(f"Manual read successful with encoding: {encoding}")
                return df

            except Exception as e:
                print(f"Manual read failed with {encoding}: {e}")
                continue

        raise Exception("Manual CSV reading failed with all encodings")

    def score_russian_text(self, df):
        """Score the quality of Russian text decoding"""
        try:
            # Look for Russian characters in text columns
            russian_chars = set('абвгдеёжзийклмнопрстуфхцчшщъыьэюяАБВГДЕЁЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ')
            score = 0

            # Check first few rows and text-like columns
            for i in range(min(5, len(df))):
                for j in range(min(10, len(df.columns))):
                    cell_value = str(df.iloc[i, j])
                    if len(cell_value) > 3:  # Only check meaningful text
                        # Count Russian characters
                        russian_count = sum(1 for char in cell_value if char in russian_chars)
                        if russian_count > 0:
                            score += russian_count

                        # Penalize for garbled text indicators
                        if any(bad in cell_value for bad in ['Ã', 'Â', 'Ñ', 'ï', 'ð', 'à']):
                            score -= 10

            return score
        except:
            return 0

    def clean_text(self, text):
        """Clean and properly handle Russian text"""
        if not text or pd.isna(text):
            return ""

        try:
            text_str = str(text).strip()

            # Remove any null bytes or control characters
            text_str = ''.join(char for char in text_str if ord(char) >= 32 or char in '\n\r\t')

            # Handle common encoding issues
            replacements = {
                'Ã¡': 'а', 'Ã¢': 'б', 'Ã¢': 'в', 'Ã£': 'г', 'Ã¤': 'д', 'Ã¥': 'е',
                'Ã¦': 'ж', 'Ã§': 'з', 'Ã¨': 'и', 'Ã©': 'й', 'Ãª': 'к', 'Ã«': 'л',
                'Ã¬': 'м', 'Ã­': 'н', 'Ã®': 'о', 'Ã¯': 'п', 'Ã°': 'р', 'Ã±': 'с',
                'Ã²': 'т', 'Ã³': 'у', 'Ã´': 'ф', 'Ãµ': 'х', 'Ã¶': 'ц', 'Ã·': 'ч',
                'Ã¸': 'ш', 'Ã¹': 'щ', 'Ãº': 'ъ', 'Ã»': 'ы', 'Ã¼': 'ь', 'Ã½': 'э',
                'Ã¾': 'ю', 'Ã¿': 'я'
            }

            for bad, good in replacements.items():
                text_str = text_str.replace(bad, good)

            return text_str

        except Exception as e:
            print(f"Text cleaning error for '{text}': {e}")
            return str(text) if text else ""

    def force_dark_blue_headers(self):
        """Принудительно применить тёмно-синие заголовки"""
        try:
            # Метод 1: Через TTK Style с принудительным обновлением
            style = ttk.Style()

            # Сначала переключить тему для сброса
            current_theme = style.theme_use()
            style.theme_use('clam')

            # Применить тёмно-синий стиль
            style.configure("Modern.Treeview.Heading",
                           background='#1e3a8a',  # Тёмно-синий
                           foreground='#ffffff',  # Белый текст
                           font=('Cambria', 12, 'bold italic'),  # Cambria шрифт
                           relief='solid',
                           borderwidth=2,
                           fieldbackground='#1e3a8a')

            # Дополнительные стили для принудительного применения
            style.map("Modern.Treeview.Heading",
                     background=[('active', '#1e3a8a'), ('pressed', '#1e3a8a')],
                     foreground=[('active', '#ffffff'), ('pressed', '#ffffff')])

            # Обновить виджет
            if hasattr(self, 'tree'):
                self.tree.configure(style="Modern.Treeview")
                self.tree.update()
                self.tree.update_idletasks()

                # Принудительно обновить каждый заголовок
                for col in self.tree['columns']:
                    self.tree.heading(col, text=self.tree.heading(col, 'text'))

            print("✅ Принудительно применены тёмно-синие заголовки с Cambria шрифтом")

        except Exception as e:
            print(f"❌ Ошибка применения стилей: {e}")

    def toggle_maximize(self):
        """Toggle window between maximized and normal state"""
        try:
            if self.is_maximized:
                # Restore to normal size
                self.window.state('normal')
                self.window.geometry("1400x800")
                # Center the window
                screen_width = self.window.winfo_screenwidth()
                screen_height = self.window.winfo_screenheight()
                x = (screen_width - 1400) // 2
                y = (screen_height - 800) // 2
                self.window.geometry(f"1400x800+{x}+{y}")
                self.maximize_btn.config(text="⛶ Развернуть")
                self.is_maximized = False
            else:
                # Maximize window
                self.window.state('zoomed')  # Windows maximize
                self.maximize_btn.config(text="🗗 Восстановить")
                self.is_maximized = True
        except Exception as e:
            print(f"Error toggling maximize: {e}")

    def on_window_close(self):
        """Handle window close event"""
        if messagebox.askokcancel("Close", "Are you sure you want to close the import window?"):
            self.window.destroy()


def create_sales_import(parent, db_manager):
    """Create and show the sales import window"""
    importer = SalesImporter(parent, db_manager)
    importer.create_window()
    return importer
