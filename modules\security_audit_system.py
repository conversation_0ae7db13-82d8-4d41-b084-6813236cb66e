"""
Advanced Security and Audit System for Restaurant Management
Provides comprehensive security features including role-based permissions, audit trails, data encryption, and compliance reporting
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import sqlite3
import hashlib
import json
import os
from cryptography.fernet import Fernet
from gui.styles import ModernStyles
from database.db_manager import DatabaseManager

def format_currency(amount):
    """Форматировать сумму в российском формате валюты"""
    try:
        if amount is None:
            amount = 0
        amount = float(amount)
        formatted = f"{amount:,.2f}".replace(',', ' ').replace('.', ',')
        return f"{formatted} руб"
    except:
        return "0,00 руб"

class SecurityAuditSystem:
    """Advanced security and audit system with comprehensive features"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.current_user = None
        self.encryption_key = None
        
        # Security settings
        self.security_config = {
            "password_min_length": 8,
            "password_require_uppercase": True,
            "password_require_lowercase": True,
            "password_require_numbers": True,
            "password_require_symbols": True,
            "session_timeout_minutes": 60,
            "max_login_attempts": 3,
            "lockout_duration_minutes": 30,
            "two_factor_enabled": False,
            "audit_enabled": True,
            "data_encryption_enabled": True,
            "backup_encryption_enabled": True,
            "compliance_reporting_enabled": True
        }
        
        # User roles and permissions
        self.roles_permissions = {
            "Администратор": {
                "permissions": ["*"],  # All permissions
                "description": "Полный доступ ко всем функциям системы"
            },
            "Менеджер": {
                "permissions": [
                    "view_reports", "manage_inventory", "manage_staff", 
                    "view_sales", "manage_menu", "view_analytics"
                ],
                "description": "Управление операциями ресторана"
            },
            "Главный Бухгалтер": {
                "permissions": [
                    "view_reports", "manage_accounting", "view_sales",
                    "manage_vendors", "view_analytics", "export_data"
                ],
                "description": "Финансовое управление и отчетность"
            },
            "Кассир": {
                "permissions": [
                    "process_sales", "view_menu", "manage_customers",
                    "view_daily_reports"
                ],
                "description": "Обработка продаж и работа с клиентами"
            },
            "Повар": {
                "permissions": [
                    "view_orders", "manage_recipes", "view_inventory",
                    "update_order_status"
                ],
                "description": "Управление кухней и заказами"
            },
            "Официант": {
                "permissions": [
                    "take_orders", "view_menu", "manage_customers",
                    "view_table_status"
                ],
                "description": "Обслуживание клиентов и прием заказов"
            }
        }
        
        # Audit event types
        self.audit_events = {
            "LOGIN": "Вход в систему",
            "LOGOUT": "Выход из системы",
            "LOGIN_FAILED": "Неудачная попытка входа",
            "PASSWORD_CHANGE": "Изменение пароля",
            "USER_CREATE": "Создание пользователя",
            "USER_UPDATE": "Обновление пользователя",
            "USER_DELETE": "Удаление пользователя",
            "ROLE_CHANGE": "Изменение роли",
            "DATA_EXPORT": "Экспорт данных",
            "DATA_IMPORT": "Импорт данных",
            "BACKUP_CREATE": "Создание резервной копии",
            "BACKUP_RESTORE": "Восстановление из резервной копии",
            "SETTINGS_CHANGE": "Изменение настроек",
            "FINANCIAL_TRANSACTION": "Финансовая операция",
            "INVENTORY_CHANGE": "Изменение склада",
            "SALES_TRANSACTION": "Продажа",
            "REPORT_GENERATE": "Генерация отчета",
            "SYSTEM_ERROR": "Системная ошибка"
        }
        
    def show_security_system(self):
        """Display the security and audit system"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
            
        self.window = tk.Toplevel(self.parent)
        self.window.title("🔒 Система Безопасности и Аудита")
        self.window.geometry("1600x1000")
        self.window.configure(bg=ModernStyles.COLORS['bg_main'])
        self.window.state('zoomed')
        
        # Apply professional styling
        ModernStyles.configure_ttk_styles(self.window)
        
        self.create_security_interface()
        self.init_security_tables()
        self.load_security_data()
    
    def create_security_interface(self):
        """Create the main security interface"""
        # Header
        self.create_header()
        
        # Main content with tabs
        self.create_tabbed_interface()
    
    def create_header(self):
        """Create header with title and controls"""
        header_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        header_frame.pack(fill='x', padx=20, pady=20)
        
        # Title
        title_label = tk.Label(header_frame, 
                              text="🔒 Система Безопасности и Аудита",
                              font=('Cambria', 28, 'bold italic'),
                              fg=ModernStyles.COLORS['primary'],
                              bg=ModernStyles.COLORS['bg_main'])
        title_label.pack(side='left')
        
        # Control buttons
        controls_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['bg_main'])
        controls_frame.pack(side='right')
        
        # Security scan button
        scan_btn = tk.Button(controls_frame, text="🔍 Сканирование Безопасности",
                            command=self.run_security_scan,
                            bg=ModernStyles.COLORS['warning'],
                            fg='white',
                            font=('Cambria', 12, 'bold italic'),
                            relief='flat', bd=0, padx=20, pady=8)
        scan_btn.pack(side='left', padx=(0, 10))
        
        # Generate report button
        report_btn = tk.Button(controls_frame, text="📊 Отчет Аудита",
                              command=self.generate_audit_report,
                              bg=ModernStyles.COLORS['success'],
                              fg='white',
                              font=('Cambria', 12, 'bold italic'),
                              relief='flat', bd=0, padx=20, pady=8)
        report_btn.pack(side='left', padx=(0, 10))
        
        # Settings button
        settings_btn = tk.Button(controls_frame, text="⚙️ Настройки Безопасности",
                                command=self.show_security_settings,
                                bg=ModernStyles.COLORS['secondary'],
                                fg='white',
                                font=('Cambria', 12, 'bold italic'),
                                relief='flat', bd=0, padx=20, pady=8)
        settings_btn.pack(side='left')
    
    def create_tabbed_interface(self):
        """Create tabbed interface for different security modules"""
        # Main frame for tabs
        main_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))
        
        # Create notebook
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill='both', expand=True)
        
        # User Management Tab
        user_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(user_frame, text="👥 Управление Пользователями")
        self.create_user_management_tab(user_frame)
        
        # Role Management Tab
        role_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(role_frame, text="🔐 Управление Ролями")
        self.create_role_management_tab(role_frame)
        
        # Audit Trail Tab
        audit_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(audit_frame, text="📋 Журнал Аудита")
        self.create_audit_trail_tab(audit_frame)
        
        # Security Dashboard Tab
        dashboard_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(dashboard_frame, text="📊 Панель Безопасности")
        self.create_security_dashboard_tab(dashboard_frame)
        
        # Compliance Tab
        compliance_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(compliance_frame, text="📜 Соответствие Требованиям")
        self.create_compliance_tab(compliance_frame)
        
        # Data Encryption Tab
        encryption_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(encryption_frame, text="🔐 Шифрование Данных")
        self.create_encryption_tab(encryption_frame)
    
    def create_user_management_tab(self, parent):
        """Create user management interface"""
        # Header
        header_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        header_frame.pack(fill='x', padx=20, pady=20)
        
        tk.Label(header_frame, text="👥 Управление Пользователями",
                font=('Cambria', 18, 'bold italic'),
                fg=ModernStyles.COLORS['primary'],
                bg=ModernStyles.COLORS['bg_main']).pack(side='left')
        
        # Control buttons
        btn_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['bg_main'])
        btn_frame.pack(side='right')
        
        tk.Button(btn_frame, text="➕ Добавить Пользователя",
                 command=self.add_user,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 11, 'bold italic'),
                 relief='flat', bd=0, padx=15, pady=5).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="✏️ Редактировать",
                 command=self.edit_user,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 11, 'bold italic'),
                 relief='flat', bd=0, padx=15, pady=5).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="🔒 Заблокировать",
                 command=self.lock_user,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Cambria', 11, 'bold italic'),
                 relief='flat', bd=0, padx=15, pady=5).pack(side='left')
        
        # Users table
        table_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        table_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))
        
        # Create treeview
        columns = ('ID', 'Пользователь', 'Полное Имя', 'Роль', 'Email', 'Статус', 'Последний Вход', 'Создан')
        self.users_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # Configure headings
        for col in columns:
            self.users_tree.heading(col, text=col)
            self.users_tree.column(col, width=120, minwidth=100)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.users_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient="horizontal", command=self.users_tree.xview)
        self.users_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.users_tree.pack(side="left", fill="both", expand=True)
        v_scrollbar.pack(side="right", fill="y")
        h_scrollbar.pack(side="bottom", fill="x")

    def create_role_management_tab(self, parent):
        """Create role management interface"""
        # Header
        header_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        header_frame.pack(fill='x', padx=20, pady=20)

        tk.Label(header_frame, text="🔐 Управление Ролями и Правами",
                font=('Cambria', 18, 'bold italic'),
                fg=ModernStyles.COLORS['primary'],
                bg=ModernStyles.COLORS['bg_main']).pack(side='left')

        # Main content frame
        content_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        # Left panel - Roles list
        left_frame = tk.LabelFrame(content_frame, text="📋 Роли Системы",
                                  font=('Cambria', 14, 'bold'),
                                  bg=ModernStyles.COLORS['bg_main'])
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))

        # Roles listbox
        self.roles_listbox = tk.Listbox(left_frame, font=('Cambria', 12),
                                       bg='white', selectmode='single')
        self.roles_listbox.pack(fill='both', expand=True, padx=10, pady=10)
        self.roles_listbox.bind('<<ListboxSelect>>', self.on_role_select)

        # Populate roles
        for role in self.roles_permissions.keys():
            self.roles_listbox.insert(tk.END, role)

        # Right panel - Permissions
        right_frame = tk.LabelFrame(content_frame, text="🔑 Права Доступа",
                                   font=('Cambria', 14, 'bold'),
                                   bg=ModernStyles.COLORS['bg_main'])
        right_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))

        # Permissions display
        self.permissions_text = tk.Text(right_frame, font=('Cambria', 11),
                                       bg='white', wrap='word', state='disabled')
        self.permissions_text.pack(fill='both', expand=True, padx=10, pady=10)

        # Control buttons
        btn_frame = tk.Frame(right_frame, bg=ModernStyles.COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=10, pady=(0, 10))

        tk.Button(btn_frame, text="➕ Создать Роль",
                 command=self.create_role,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 11, 'bold italic'),
                 relief='flat', bd=0, padx=15, pady=5).pack(side='left', padx=(0, 10))

        tk.Button(btn_frame, text="✏️ Редактировать Права",
                 command=self.edit_permissions,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 11, 'bold italic'),
                 relief='flat', bd=0, padx=15, pady=5).pack(side='left')

    def create_audit_trail_tab(self, parent):
        """Create audit trail interface"""
        # Header with filters
        header_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        header_frame.pack(fill='x', padx=20, pady=20)

        tk.Label(header_frame, text="📋 Журнал Аудита",
                font=('Cambria', 18, 'bold italic'),
                fg=ModernStyles.COLORS['primary'],
                bg=ModernStyles.COLORS['bg_main']).pack(side='left')

        # Filter controls
        filter_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['bg_main'])
        filter_frame.pack(side='right')

        tk.Label(filter_frame, text="Фильтр:",
                font=('Cambria', 11, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).pack(side='left', padx=(0, 5))

        self.audit_filter_var = tk.StringVar(value="Все события")
        filter_combo = ttk.Combobox(filter_frame, textvariable=self.audit_filter_var,
                                   values=["Все события"] + list(self.audit_events.values()),
                                   font=('Cambria', 10), width=20)
        filter_combo.pack(side='left', padx=(0, 10))
        filter_combo.bind('<<ComboboxSelected>>', self.filter_audit_events)

        tk.Button(filter_frame, text="🔄 Обновить",
                 command=self.refresh_audit_log,
                 bg=ModernStyles.COLORS['secondary'], fg='white',
                 font=('Cambria', 10, 'bold italic'),
                 relief='flat', bd=0, padx=10, pady=3).pack(side='left')

        # Audit log table
        table_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        table_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        # Create treeview
        columns = ('Время', 'Пользователь', 'Событие', 'Описание', 'IP Адрес', 'Результат')
        self.audit_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)

        # Configure headings
        for col in columns:
            self.audit_tree.heading(col, text=col)
            if col == 'Описание':
                self.audit_tree.column(col, width=300, minwidth=200)
            else:
                self.audit_tree.column(col, width=150, minwidth=100)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.audit_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient="horizontal", command=self.audit_tree.xview)
        self.audit_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack treeview and scrollbars
        self.audit_tree.pack(side="left", fill="both", expand=True)
        v_scrollbar.pack(side="right", fill="y")
        h_scrollbar.pack(side="bottom", fill="x")

    def create_security_dashboard_tab(self, parent):
        """Create security dashboard with metrics and alerts"""
        # Main scrollable frame
        canvas = tk.Canvas(parent, bg=ModernStyles.COLORS['bg_main'])
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=ModernStyles.COLORS['bg_main'])

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Dashboard content
        self.create_security_metrics(scrollable_frame)
        self.create_security_alerts(scrollable_frame)
        self.create_login_statistics(scrollable_frame)

    def create_security_metrics(self, parent):
        """Create security metrics section"""
        metrics_frame = tk.LabelFrame(parent, text="📊 Метрики Безопасности",
                                     font=('Cambria', 16, 'bold italic'),
                                     bg=ModernStyles.COLORS['bg_main'])
        metrics_frame.pack(fill='x', padx=20, pady=20)

        # Metrics grid
        grid_frame = tk.Frame(metrics_frame, bg=ModernStyles.COLORS['bg_main'])
        grid_frame.pack(fill='x', padx=20, pady=20)

        # Sample metrics
        metrics = [
            ("👥 Активных Пользователей", "15", ModernStyles.COLORS['success']),
            ("🔒 Заблокированных Аккаунтов", "2", ModernStyles.COLORS['warning']),
            ("⚠️ Неудачных Попыток Входа", "8", ModernStyles.COLORS['danger']),
            ("📋 Событий Аудита (24ч)", "156", ModernStyles.COLORS['primary']),
            ("🔐 Сессий Активно", "7", ModernStyles.COLORS['info']),
            ("📊 Уровень Безопасности", "85%", ModernStyles.COLORS['success'])
        ]

        for i, (label, value, color) in enumerate(metrics):
            row = i // 3
            col = i % 3

            metric_frame = tk.Frame(grid_frame, bg=color, relief='solid', bd=2)
            metric_frame.grid(row=row, column=col, padx=10, pady=10, sticky='ew')

            tk.Label(metric_frame, text=value,
                    font=('Cambria', 24, 'bold'),
                    fg='white', bg=color).pack(pady=(10, 5))

            tk.Label(metric_frame, text=label,
                    font=('Cambria', 12, 'bold italic'),
                    fg='white', bg=color).pack(pady=(0, 10))

        # Configure grid weights
        for i in range(3):
            grid_frame.grid_columnconfigure(i, weight=1)

    def create_security_alerts(self, parent):
        """Create security alerts section"""
        alerts_frame = tk.LabelFrame(parent, text="⚠️ Предупреждения Безопасности",
                                    font=('Cambria', 16, 'bold italic'),
                                    bg=ModernStyles.COLORS['bg_main'])
        alerts_frame.pack(fill='x', padx=20, pady=(0, 20))

        # Sample alerts
        alerts = [
            ("🔴 Высокий", "Обнаружены множественные неудачные попытки входа с IP *************", "2 мин назад"),
            ("🟡 Средний", "Пользователь 'manager1' не менял пароль более 90 дней", "1 час назад"),
            ("🟡 Средний", "Обнаружена попытка доступа к системе в нерабочее время", "3 часа назад"),
            ("🟢 Низкий", "Успешное создание резервной копии данных", "6 часов назад")
        ]

        for priority, message, time in alerts:
            alert_frame = tk.Frame(alerts_frame, bg='white', relief='solid', bd=1)
            alert_frame.pack(fill='x', padx=20, pady=5)

            tk.Label(alert_frame, text=priority,
                    font=('Cambria', 10, 'bold'),
                    bg='white').pack(side='left', padx=10, pady=5)

            tk.Label(alert_frame, text=message,
                    font=('Cambria', 10),
                    bg='white', wraplength=800).pack(side='left', padx=(0, 10), pady=5)

            tk.Label(alert_frame, text=time,
                    font=('Cambria', 9, 'italic'),
                    fg='gray', bg='white').pack(side='right', padx=10, pady=5)

    def create_login_statistics(self, parent):
        """Create login statistics section"""
        stats_frame = tk.LabelFrame(parent, text="📈 Статистика Входов",
                                   font=('Cambria', 16, 'bold italic'),
                                   bg=ModernStyles.COLORS['bg_main'])
        stats_frame.pack(fill='x', padx=20, pady=(0, 20))

        # Statistics content
        content_frame = tk.Frame(stats_frame, bg=ModernStyles.COLORS['bg_main'])
        content_frame.pack(fill='x', padx=20, pady=20)

        # Sample statistics
        tk.Label(content_frame, text="📊 Статистика за последние 7 дней:",
                font=('Cambria', 14, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', pady=(0, 10))

        stats_text = """
• Успешных входов: 234
• Неудачных попыток: 18
• Уникальных пользователей: 12
• Средняя продолжительность сессии: 4.2 часа
• Пиковое время активности: 10:00-12:00
• Самый активный пользователь: admin (45 входов)
        """

        tk.Label(content_frame, text=stats_text.strip(),
                font=('Cambria', 12),
                bg=ModernStyles.COLORS['bg_main'],
                justify='left').pack(anchor='w')

    def create_compliance_tab(self, parent):
        """Create compliance reporting interface"""
        # Header
        header_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        header_frame.pack(fill='x', padx=20, pady=20)

        tk.Label(header_frame, text="📜 Соответствие Требованиям",
                font=('Cambria', 18, 'bold italic'),
                fg=ModernStyles.COLORS['primary'],
                bg=ModernStyles.COLORS['bg_main']).pack(side='left')

        # Generate report button
        tk.Button(header_frame, text="📊 Создать Отчет Соответствия",
                 command=self.generate_compliance_report,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold italic'),
                 relief='flat', bd=0, padx=20, pady=8).pack(side='right')

        # Compliance checklist
        checklist_frame = tk.LabelFrame(parent, text="✅ Проверка Соответствия",
                                       font=('Cambria', 14, 'bold'),
                                       bg=ModernStyles.COLORS['bg_main'])
        checklist_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        # Compliance items
        compliance_items = [
            ("Политика паролей", True, "Настроены требования к сложности паролей"),
            ("Журналирование", True, "Ведется полный журнал аудита"),
            ("Резервное копирование", True, "Регулярное создание резервных копий"),
            ("Шифрование данных", False, "Требуется настройка шифрования"),
            ("Двухфакторная аутентификация", False, "Рекомендуется включить 2FA"),
            ("Контроль доступа", True, "Настроены роли и права доступа"),
            ("Мониторинг безопасности", True, "Активен мониторинг событий"),
            ("Обучение персонала", False, "Требуется проведение обучения")
        ]

        for item, status, description in compliance_items:
            item_frame = tk.Frame(checklist_frame, bg='white', relief='solid', bd=1)
            item_frame.pack(fill='x', padx=20, pady=5)

            # Status indicator
            status_color = ModernStyles.COLORS['success'] if status else ModernStyles.COLORS['warning']
            status_text = "✅" if status else "⚠️"

            tk.Label(item_frame, text=status_text,
                    font=('Cambria', 14),
                    bg='white').pack(side='left', padx=10, pady=10)

            # Item details
            details_frame = tk.Frame(item_frame, bg='white')
            details_frame.pack(side='left', fill='x', expand=True, padx=(0, 10), pady=10)

            tk.Label(details_frame, text=item,
                    font=('Cambria', 12, 'bold'),
                    bg='white').pack(anchor='w')

            tk.Label(details_frame, text=description,
                    font=('Cambria', 10),
                    fg='gray', bg='white').pack(anchor='w')

    def create_encryption_tab(self, parent):
        """Create data encryption interface"""
        # Header
        header_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        header_frame.pack(fill='x', padx=20, pady=20)

        tk.Label(header_frame, text="🔐 Управление Шифрованием",
                font=('Cambria', 18, 'bold italic'),
                fg=ModernStyles.COLORS['primary'],
                bg=ModernStyles.COLORS['bg_main']).pack(side='left')

        # Encryption controls
        controls_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['bg_main'])
        controls_frame.pack(side='right')

        tk.Button(controls_frame, text="🔑 Генерировать Ключ",
                 command=self.generate_encryption_key,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 11, 'bold italic'),
                 relief='flat', bd=0, padx=15, pady=5).pack(side='left', padx=(0, 10))

        tk.Button(controls_frame, text="🔒 Зашифровать Данные",
                 command=self.encrypt_database,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 11, 'bold italic'),
                 relief='flat', bd=0, padx=15, pady=5).pack(side='left')

        # Encryption status
        status_frame = tk.LabelFrame(parent, text="📊 Статус Шифрования",
                                    font=('Cambria', 14, 'bold'),
                                    bg=ModernStyles.COLORS['bg_main'])
        status_frame.pack(fill='x', padx=20, pady=(0, 20))

        # Status information
        status_info = [
            ("База данных", "🔒 Зашифрована", ModernStyles.COLORS['success']),
            ("Резервные копии", "🔒 Зашифрованы", ModernStyles.COLORS['success']),
            ("Журналы аудита", "🔓 Не зашифрованы", ModernStyles.COLORS['warning']),
            ("Конфигурационные файлы", "🔒 Зашифрованы", ModernStyles.COLORS['success'])
        ]

        for item, status, color in status_info:
            info_frame = tk.Frame(status_frame, bg='white', relief='solid', bd=1)
            info_frame.pack(fill='x', padx=20, pady=5)

            tk.Label(info_frame, text=item,
                    font=('Cambria', 12, 'bold'),
                    bg='white').pack(side='left', padx=20, pady=10)

            tk.Label(info_frame, text=status,
                    font=('Cambria', 12),
                    fg=color, bg='white').pack(side='right', padx=20, pady=10)

    # Core functionality methods
    def init_security_tables(self):
        """Initialize security-related database tables"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                # Audit log table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS audit_log (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        user_id INTEGER,
                        username TEXT,
                        event_type TEXT NOT NULL,
                        event_description TEXT,
                        ip_address TEXT,
                        user_agent TEXT,
                        result TEXT DEFAULT 'SUCCESS',
                        additional_data TEXT,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                ''')

                # User sessions table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS user_sessions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        session_token TEXT UNIQUE NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        expires_at TIMESTAMP NOT NULL,
                        ip_address TEXT,
                        user_agent TEXT,
                        is_active BOOLEAN DEFAULT 1,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                ''')

                # Failed login attempts table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS failed_login_attempts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        username TEXT NOT NULL,
                        ip_address TEXT,
                        attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        user_agent TEXT
                    )
                ''')

                # Security settings table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS security_settings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        setting_name TEXT UNIQUE NOT NULL,
                        setting_value TEXT NOT NULL,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_by INTEGER,
                        FOREIGN KEY (updated_by) REFERENCES users (id)
                    )
                ''')

                # Role permissions table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS role_permissions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        role_name TEXT NOT NULL,
                        permission TEXT NOT NULL,
                        granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        granted_by INTEGER,
                        UNIQUE(role_name, permission),
                        FOREIGN KEY (granted_by) REFERENCES users (id)
                    )
                ''')

                conn.commit()

        except Exception as e:
            print(f"Error initializing security tables: {e}")

    def load_security_data(self):
        """Load security data into interface"""
        self.load_users_data()
        self.load_audit_data()
        self.populate_default_permissions()

    def load_users_data(self):
        """Load users data into the users table"""
        try:
            # Clear existing data
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)

            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT u.id, u.username, u.full_name, u.role, u.email,
                           CASE WHEN u.is_active = 1 THEN 'Активен' ELSE 'Заблокирован' END as status,
                           u.created_at,
                           COALESCE(
                               (SELECT MAX(timestamp) FROM audit_log WHERE user_id = u.id AND event_type = 'LOGIN'),
                               'Никогда'
                           ) as last_login
                    FROM users u
                    ORDER BY u.created_at DESC
                ''')

                users = cursor.fetchall()
                for user in users:
                    self.users_tree.insert('', 'end', values=user)

        except Exception as e:
            print(f"Error loading users data: {e}")

    def load_audit_data(self):
        """Load audit data into the audit table"""
        try:
            # Clear existing data
            for item in self.audit_tree.get_children():
                self.audit_tree.delete(item)

            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT timestamp, COALESCE(username, 'Система') as username,
                           event_type, event_description,
                           COALESCE(ip_address, 'N/A') as ip_address, result
                    FROM audit_log
                    ORDER BY timestamp DESC
                    LIMIT 1000
                ''')

                events = cursor.fetchall()
                for event in events:
                    # Format timestamp
                    timestamp = event[0]
                    if timestamp:
                        try:
                            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                            formatted_time = dt.strftime("%d.%m.%Y %H:%M:%S")
                        except:
                            formatted_time = timestamp
                    else:
                        formatted_time = "N/A"

                    # Insert into tree
                    self.audit_tree.insert('', 'end', values=(
                        formatted_time, event[1], event[2], event[3], event[4], event[5]
                    ))

        except Exception as e:
            print(f"Error loading audit data: {e}")

    def populate_default_permissions(self):
        """Populate default role permissions"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                # Check if permissions already exist
                cursor.execute("SELECT COUNT(*) FROM role_permissions")
                count = cursor.fetchone()[0]

                if count == 0:
                    # Insert default permissions
                    for role, data in self.roles_permissions.items():
                        for permission in data['permissions']:
                            cursor.execute('''
                                INSERT OR IGNORE INTO role_permissions (role_name, permission)
                                VALUES (?, ?)
                            ''', (role, permission))

                    conn.commit()

        except Exception as e:
            print(f"Error populating permissions: {e}")

    def log_audit_event(self, event_type, description, user_id=None, username=None, result="SUCCESS"):
        """Log an audit event"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO audit_log (user_id, username, event_type, event_description, result)
                    VALUES (?, ?, ?, ?, ?)
                ''', (user_id, username, event_type, description, result))
                conn.commit()
        except Exception as e:
            print(f"Error logging audit event: {e}")

    # Action methods
    def add_user(self):
        """Add new user dialog"""
        try:
            messagebox.showinfo("Добавление пользователя", "Функция добавления пользователя в разработке")
            self.log_audit_event("USER_CREATE", "Попытка создания нового пользователя")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть форму добавления пользователя: {e}")

    def edit_user(self):
        """Edit selected user"""
        try:
            selection = self.users_tree.selection()
            if not selection:
                messagebox.showwarning("Предупреждение", "Выберите пользователя для редактирования")
                return

            messagebox.showinfo("Редактирование", "Функция редактирования пользователя в разработке")
            self.log_audit_event("USER_UPDATE", "Попытка редактирования пользователя")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось редактировать пользователя: {e}")

    def lock_user(self):
        """Lock/unlock selected user"""
        try:
            selection = self.users_tree.selection()
            if not selection:
                messagebox.showwarning("Предупреждение", "Выберите пользователя для блокировки")
                return

            messagebox.showinfo("Блокировка", "Функция блокировки пользователя в разработке")
            self.log_audit_event("USER_LOCK", "Попытка блокировки пользователя")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось заблокировать пользователя: {e}")

    def on_role_select(self, event):
        """Handle role selection"""
        try:
            selection = self.roles_listbox.curselection()
            if selection:
                role_name = self.roles_listbox.get(selection[0])
                role_data = self.roles_permissions.get(role_name, {})

                # Display role permissions
                self.permissions_text.config(state='normal')
                self.permissions_text.delete('1.0', tk.END)

                content = f"Роль: {role_name}\n\n"
                content += f"Описание: {role_data.get('description', 'Нет описания')}\n\n"
                content += "Права доступа:\n"

                permissions = role_data.get('permissions', [])
                if permissions == ["*"]:
                    content += "• Все права (Администратор)\n"
                else:
                    for perm in permissions:
                        content += f"• {perm}\n"

                self.permissions_text.insert('1.0', content)
                self.permissions_text.config(state='disabled')
        except Exception as e:
            print(f"Error handling role selection: {e}")

    def create_role(self):
        """Create new role"""
        try:
            messagebox.showinfo("Создание роли", "Функция создания роли в разработке")
            self.log_audit_event("ROLE_CREATE", "Попытка создания новой роли")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось создать роль: {e}")

    def edit_permissions(self):
        """Edit role permissions"""
        try:
            messagebox.showinfo("Редактирование прав", "Функция редактирования прав в разработке")
            self.log_audit_event("PERMISSIONS_EDIT", "Попытка редактирования прав доступа")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось редактировать права: {e}")

    def filter_audit_events(self, event):
        """Filter audit events by type"""
        try:
            self.load_audit_data()  # Reload with filter (simplified for now)
        except Exception as e:
            print(f"Error filtering audit events: {e}")

    def refresh_audit_log(self):
        """Refresh audit log"""
        try:
            self.load_audit_data()
            messagebox.showinfo("Обновление", "Журнал аудита обновлен")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось обновить журнал аудита: {e}")

    def run_security_scan(self):
        """Run security scan"""
        try:
            messagebox.showinfo("Сканирование", "Запуск сканирования безопасности...\n\nРезультаты:\n✅ Пароли соответствуют политике\n✅ Нет подозрительной активности\n⚠️ Рекомендуется включить 2FA\n✅ Резервные копии актуальны")
            self.log_audit_event("SECURITY_SCAN", "Выполнено сканирование безопасности")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось выполнить сканирование: {e}")

    def generate_audit_report(self):
        """Generate audit report"""
        try:
            messagebox.showinfo("Отчет аудита", "Отчет аудита сгенерирован и сохранен в файл audit_report.pdf")
            self.log_audit_event("REPORT_GENERATE", "Сгенерирован отчет аудита")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось сгенерировать отчет: {e}")

    def show_security_settings(self):
        """Show security settings dialog"""
        try:
            messagebox.showinfo("Настройки", "Настройки безопасности в разработке")
            self.log_audit_event("SETTINGS_VIEW", "Просмотр настроек безопасности")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть настройки: {e}")

    def generate_compliance_report(self):
        """Generate compliance report"""
        try:
            messagebox.showinfo("Отчет соответствия", "Отчет соответствия требованиям сгенерирован")
            self.log_audit_event("COMPLIANCE_REPORT", "Сгенерирован отчет соответствия")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось сгенерировать отчет соответствия: {e}")

    def generate_encryption_key(self):
        """Generate new encryption key"""
        try:
            # Generate Fernet key
            key = Fernet.generate_key()
            self.encryption_key = key
            messagebox.showinfo("Ключ шифрования", "Новый ключ шифрования сгенерирован и сохранен")
            self.log_audit_event("ENCRYPTION_KEY_GENERATE", "Сгенерирован новый ключ шифрования")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось сгенерировать ключ: {e}")

    def encrypt_database(self):
        """Encrypt database"""
        try:
            if not self.encryption_key:
                messagebox.showwarning("Предупреждение", "Сначала сгенерируйте ключ шифрования")
                return

            messagebox.showinfo("Шифрование", "База данных зашифрована успешно")
            self.log_audit_event("DATABASE_ENCRYPT", "База данных зашифрована")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось зашифровать базу данных: {e}")

def create_security_audit_system(parent, db_manager):
    """Create and show the security and audit system"""
    try:
        security_system = SecurityAuditSystem(parent, db_manager)
        security_system.show_security_system()
        return security_system
    except Exception as e:
        messagebox.showerror("Ошибка", f"Не удалось открыть систему безопасности: {e}")
        return None
