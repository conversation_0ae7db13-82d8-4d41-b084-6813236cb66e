"""
Полностью функциональный менеджер склада
Управление товарами, остатками, поступлениями
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from datetime import datetime
from gui.styles import ModernStyles

class InventoryManagerWindow:
    """Окно управления складом"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        
        # Загрузить товары из базы данных
        self.inventory_items = []
        self.load_inventory_from_db()

        # Если база пуста, добавить образцы данных
        if not self.inventory_items:
            self.create_sample_data()
    
    def create_window(self):
        """Создать окно управления складом"""
        try:
            from utils.window_utils import create_centered_dialog
            self.window = create_centered_dialog(
                self.parent,
                "📦 Управление Складом",
                width=1500,
                height=900,
                resizable=True
            )
        except ImportError:
            # Fallback если utils не доступны
            self.window = tk.Toplevel(self.parent)
            self.window.title("📦 Управление Складом")
            self.window.geometry("1500x900")
            self.window.configure(bg=ModernStyles.COLORS['bg_main'])
            self.window.resizable(True, True)

            # Центрировать окно
            self.window.update_idletasks()
            x = (self.window.winfo_screenwidth() // 2) - (1500 // 2)
            y = (self.window.winfo_screenheight() // 2) - (900 // 2)
            self.window.geometry(f"1500x900+{x}+{y}")
        self.window.resizable(True, True)
        
        self.create_interface()
    
    def create_interface(self):
        """Создать интерфейс управления складом"""
        # Заголовок
        header_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['primary'], height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="📦 Управление Складом",
                font=('Cambria', 24, 'bold italic'), bg=ModernStyles.COLORS['primary'],
                fg='white').pack(side='left', padx=20, pady=15)
        
        # Кнопки действий
        btn_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['primary'])
        btn_frame.pack(side='right', padx=20, pady=10)
        
        tk.Button(btn_frame, text="➕ Добавить Товар", command=self.add_item,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 16, 'bold italic'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(btn_frame, text="📝 Изменить Остаток", command=self.adjust_stock,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Cambria', 16, 'bold italic'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(btn_frame, text="📊 Отчёт", command=self.generate_report,
                 bg=ModernStyles.COLORS['secondary'], fg='white',
                 font=('Cambria', 16, 'bold italic'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        # Основной контент
        main_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Статистика
        self.create_stats_section(main_frame)
        
        # Таблица товаров
        self.create_inventory_table(main_frame)

    def load_inventory_from_db(self):
        """Загрузить товары из базы данных"""
        try:
            raw_materials = self.db_manager.get_raw_materials()
            self.inventory_items = []

            for material in raw_materials:
                item = {
                    "id": material['id'],
                    "name": material['name'],
                    "category": material['category'] or "Без категории",
                    "current_stock": material['current_stock'],
                    "unit": material['unit_of_measure'],
                    "min_stock": material['minimum_stock'],
                    "max_stock": material['minimum_stock'] * 3,  # Примерное максимальное значение
                    "cost_per_unit": material['average_cost'],
                    "supplier": material['supplier'] or "Не указан",
                    "location": "Основной склад"  # Можно добавить в БД позже
                }
                self.inventory_items.append(item)

        except Exception as e:
            print(f"Ошибка загрузки данных из БД: {e}")
            self.inventory_items = []

    def create_sample_data(self):
        """Создать образцы данных в базе данных"""
        try:
            sample_items = [
                {
                    "name": "Мука пшеничная высший сорт",
                    "category": "Мука и крупы",
                    "unit_of_measure": "кг",
                    "current_stock": 45.5,
                    "minimum_stock": 20.0,
                    "average_cost": 65.0,
                    "supplier": "Мука-Сервис"
                },
                {
                    "name": "Молоко 3.2%",
                    "category": "Молочные продукты",
                    "unit_of_measure": "л",
                    "current_stock": 28.0,
                    "minimum_stock": 15.0,
                    "average_cost": 85.0,
                    "supplier": "Молочный Дом"
                },
                {
                    "name": "Говядина вырезка",
                    "category": "Мясо",
                    "unit_of_measure": "кг",
                    "current_stock": 12.5,
                    "minimum_stock": 10.0,
                    "average_cost": 850.0,
                    "supplier": "Мясокомбинат Премиум"
                },
                {
                    "name": "Картофель",
                    "category": "Овощи",
                    "unit_of_measure": "кг",
                    "current_stock": 85.0,
                    "minimum_stock": 50.0,
                    "average_cost": 45.0,
                    "supplier": "Овощи-Плюс"
                },
                {
                    "name": "Лук репчатый",
                    "category": "Овощи",
                    "unit_of_measure": "кг",
                    "current_stock": 8.0,
                    "minimum_stock": 15.0,
                    "average_cost": 35.0,
                    "supplier": "Овощи-Плюс"
                }
            ]

            for item_data in sample_items:
                self.db_manager.insert_raw_material(item_data)

            # Перезагрузить данные
            self.load_inventory_from_db()

        except Exception as e:
            print(f"Ошибка создания образцов данных: {e}")

    def save_item_to_db(self, item_data):
        """Сохранить товар в базу данных"""
        try:
            db_data = {
                "name": item_data['name'],
                "category": item_data['category'],
                "unit_of_measure": item_data['unit'],
                "current_stock": item_data['current_stock'],
                "minimum_stock": item_data['min_stock'],
                "average_cost": item_data['cost_per_unit'],
                "supplier": item_data['supplier']
            }
            return self.db_manager.insert_raw_material(db_data)
        except Exception as e:
            print(f"Ошибка сохранения товара: {e}")
            return None

    def update_item_in_db(self, item_id, item_data):
        """Обновить товар в базе данных"""
        try:
            # Здесь нужно добавить метод update_raw_material в db_manager
            # Пока используем простое обновление через SQL
            import sqlite3
            conn = sqlite3.connect(self.db_manager.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE raw_materials
                SET name=?, category=?, unit_of_measure=?, current_stock=?,
                    minimum_stock=?, average_cost=?, supplier=?, updated_at=CURRENT_TIMESTAMP
                WHERE id=?
            ''', (
                item_data['name'], item_data['category'], item_data['unit'],
                item_data['current_stock'], item_data['min_stock'],
                item_data['cost_per_unit'], item_data['supplier'], item_id
            ))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"Ошибка обновления товара: {e}")
            return False

    def delete_item_from_db(self, item_id):
        """Удалить товар из базы данных"""
        try:
            import sqlite3
            conn = sqlite3.connect(self.db_manager.db_path)
            cursor = conn.cursor()

            cursor.execute('DELETE FROM raw_materials WHERE id=?', (item_id,))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"Ошибка удаления товара: {e}")
            return False

    def format_currency(self, amount):
        """Форматировать сумму в российском формате: 25 952,59 руб"""
        try:
            if amount is None:
                amount = 0
            amount = float(amount)
            formatted = f"{amount:,.2f}".replace(',', ' ').replace('.', ',')
            return f"{formatted} руб"
        except:
            return "0,00 руб"

    def create_stats_section(self, parent):
        """Создать секцию статистики"""
        stats_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        stats_frame.pack(fill='x', pady=(0, 20))
        
        # Рассчитать статистику
        total_items = len(self.inventory_items)
        low_stock_items = len([item for item in self.inventory_items if item['current_stock'] <= item['min_stock']])
        total_value = sum(item['current_stock'] * item['cost_per_unit'] for item in self.inventory_items)
        
        # Карточки статистики
        self.create_stat_card(stats_frame, "Всего товаров", str(total_items), ModernStyles.COLORS['primary'])
        self.create_stat_card(stats_frame, "Требуют пополнения", str(low_stock_items), ModernStyles.COLORS['warning'])
        self.create_stat_card(stats_frame, "Общая стоимость", f"{total_value:,.0f}₽", ModernStyles.COLORS['success'])
        
        # Категории
        categories = set(item['category'] for item in self.inventory_items)
        self.create_stat_card(stats_frame, "Категорий", str(len(categories)), ModernStyles.COLORS['secondary'])
    
    def create_stat_card(self, parent, title, value, color):
        """Создать карточку статистики"""
        card = tk.Frame(parent, bg=color, relief='solid', bd=1)
        card.pack(side='left', fill='both', expand=True, padx=5)
        
        tk.Label(card, text=title, font=('Arial', 10), bg=color, fg='white').pack(pady=(15, 5))
        tk.Label(card, text=value, font=('Arial', 14, 'bold'), bg=color, fg='white').pack(pady=(0, 15))
    
    def create_inventory_table(self, parent):
        """Создать таблицу товаров"""
        # Заголовок таблицы
        tk.Label(parent, text="Товары на Складе",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=(0, 10))
        
        # Фрейм для таблицы
        table_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        table_frame.pack(fill='both', expand=True)
        
        # Создать таблицу
        columns = ('ID', 'Название', 'Категория', 'Остаток', 'Ед.изм.', 'Мин.остаток', 'Цена за ед.', 'Поставщик', 'Расположение')
        self.inventory_tree = ttk.Treeview(table_frame, columns=columns, show='headings', style="Modern.Treeview")
        
        # Настроить колонки
        column_widths = {
            'ID': 50, 'Название': 200, 'Категория': 120, 'Остаток': 80,
            'Ед.изм.': 60, 'Мин.остаток': 80, 'Цена за ед.': 100, 'Поставщик': 150, 'Расположение': 120
        }
        
        for col in columns:
            self.inventory_tree.heading(col, text=col)
            self.inventory_tree.column(col, width=column_widths.get(col, 100))
        
        # Заполнить данными
        self.refresh_inventory_table()
        
        # Скроллбары
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.inventory_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.inventory_tree.xview)
        self.inventory_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Упаковка
        self.inventory_tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')

        # Контекстное меню
        self.create_context_menu()

    def create_context_menu(self):
        """Создать контекстное меню для таблицы"""
        self.context_menu = tk.Menu(self.window, tearoff=0)
        self.context_menu.add_command(label="📝 Редактировать", command=self.edit_item)
        self.context_menu.add_command(label="📊 Изменить остаток", command=self.adjust_stock)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="❌ Удалить", command=self.delete_item)

        # Привязать к правой кнопке мыши
        self.inventory_tree.bind("<Button-3>", self.show_context_menu)

    def show_context_menu(self, event):
        """Показать контекстное меню"""
        # Выбрать элемент под курсором
        item = self.inventory_tree.identify_row(event.y)
        if item:
            self.inventory_tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)

    def refresh_inventory_table(self):
        """Обновить таблицу товаров"""
        # Очистить таблицу
        for item in self.inventory_tree.get_children():
            self.inventory_tree.delete(item)
        
        # Заполнить данными
        for item in self.inventory_items:
            # Определить цвет строки по уровню остатка
            if item['current_stock'] <= item['min_stock']:
                tags = ['low_stock']
            elif item['current_stock'] <= item['min_stock'] * 1.5:
                tags = ['medium_stock']
            else:
                tags = ['normal_stock']
            
            self.inventory_tree.insert('', 'end', values=(
                item['id'],
                item['name'],
                item['category'],
                f"{item['current_stock']:.1f}",
                item['unit'],
                f"{item['min_stock']:.1f}",
                self.format_currency(item['cost_per_unit']),
                item['supplier'],
                item['location']
            ), tags=tags)
        
        # Настроить цвета
        self.inventory_tree.tag_configure('low_stock', background='#ffebee', foreground='#c62828')
        self.inventory_tree.tag_configure('medium_stock', background='#fff3e0', foreground='#ef6c00')
        self.inventory_tree.tag_configure('normal_stock', background='#e8f5e8', foreground='#2e7d32')
    
    def add_item(self):
        """Добавить новый товар"""
        add_window = tk.Toplevel(self.window)
        add_window.title("Добавить Товар")
        add_window.state('zoomed')  # Full screen
        add_window.configure(bg=ModernStyles.COLORS['bg_main'])
        
        # Заголовок
        tk.Label(add_window, text="Добавление Нового Товара",
                font=('Cambria', 32, 'bold italic'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)
        
        # Форма
        form_frame = tk.Frame(add_window, bg=ModernStyles.COLORS['bg_main'])
        form_frame.pack(fill='both', expand=True, padx=20)
        
        # Поля формы
        fields = {}
        field_configs = [
            ('name', 'Название товара:', 'text'),
            ('category', 'Категория:', 'combo'),
            ('current_stock', 'Текущий остаток:', 'number'),
            ('unit', 'Единица измерения:', 'text'),
            ('min_stock', 'Минимальный остаток:', 'number'),
            ('max_stock', 'Максимальный остаток:', 'number'),
            ('cost_per_unit', 'Цена за единицу:', 'number'),
            ('supplier', 'Поставщик:', 'text'),
            ('location', 'Расположение:', 'text')
        ]
        
        for field_key, field_label, field_type in field_configs:
            field_frame = tk.Frame(form_frame, bg=ModernStyles.COLORS['bg_main'])
            field_frame.pack(fill='x', pady=5)
            
            tk.Label(field_frame, text=field_label, font=('Arial', 10, 'bold'),
                    bg=ModernStyles.COLORS['bg_main'], width=20, anchor='w').pack(side='left')
            
            if field_type == 'combo' and field_key == 'category':
                categories = list(set(item['category'] for item in self.inventory_items))
                var = tk.StringVar()
                widget = ttk.Combobox(field_frame, textvariable=var, values=categories)
            else:
                var = tk.StringVar()
                widget = tk.Entry(field_frame, textvariable=var, width=30)
            
            widget.pack(side='left', padx=10)
            fields[field_key] = var
        
        # Кнопки
        btn_frame = tk.Frame(add_window, bg=ModernStyles.COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=20, pady=20)
        
        def save_item():
            try:
                new_item = {
                    'name': fields['name'].get(),
                    'category': fields['category'].get(),
                    'current_stock': float(fields['current_stock'].get() or 0),
                    'unit': fields['unit'].get(),
                    'min_stock': float(fields['min_stock'].get() or 0),
                    'max_stock': float(fields['max_stock'].get() or 0),
                    'cost_per_unit': float(fields['cost_per_unit'].get() or 0),
                    'supplier': fields['supplier'].get(),
                    'location': fields['location'].get()
                }

                # Сохранить в базу данных
                item_id = self.save_item_to_db(new_item)
                if item_id:
                    # Перезагрузить данные из БД
                    self.load_inventory_from_db()
                    self.refresh_inventory_table()
                    add_window.destroy()
                    messagebox.showinfo("Успех", "Товар успешно добавлен!")
                else:
                    messagebox.showerror("Ошибка", "Не удалось сохранить товар в базу данных")

            except ValueError:
                messagebox.showerror("Ошибка", "Проверьте правильность введённых числовых значений")
            except Exception as e:
                messagebox.showerror("Ошибка", f"Произошла ошибка: {e}")
        
        tk.Button(btn_frame, text="💾 Сохранить", command=save_item,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(side='left')
        
        tk.Button(btn_frame, text="❌ Отмена", command=add_window.destroy,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(side='right')
    
    def adjust_stock(self):
        """Изменить остаток товара"""
        selection = self.inventory_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите товар для изменения остатка")
            return
        
        # Получить данные выбранного товара
        item_values = self.inventory_tree.item(selection[0])['values']
        item_id = int(item_values[0])
        item_name = item_values[1]
        current_stock = float(item_values[3])
        
        # Запросить новый остаток
        new_stock = simpledialog.askfloat(
            "Изменение Остатка",
            f"Товар: {item_name}\nТекущий остаток: {current_stock}\n\nВведите новый остаток:",
            initialvalue=current_stock
        )
        
        if new_stock is not None:
            # Найти товар и обновить остаток
            for item in self.inventory_items:
                if item['id'] == item_id:
                    item['current_stock'] = new_stock
                    break
            
            self.refresh_inventory_table()
            messagebox.showinfo("Успех", f"Остаток товара '{item_name}' изменён на {new_stock}")
    
    def generate_report(self):
        """Генерировать отчёт по складу"""
        report_window = tk.Toplevel(self.window)
        report_window.title("📊 Отчёт по Складу")
        report_window.geometry("600x500")
        report_window.configure(bg='white')
        
        # Заголовок
        tk.Label(report_window, text="📊 Отчёт по Складу",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)
        
        # Содержимое отчёта
        report_text = tk.Text(report_window, font=('Arial', 10), wrap='word')
        report_text.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Генерировать отчёт
        total_items = len(self.inventory_items)
        low_stock_items = [item for item in self.inventory_items if item['current_stock'] <= item['min_stock']]
        total_value = sum(item['current_stock'] * item['cost_per_unit'] for item in self.inventory_items)
        
        report_content = f"""
ОТЧЁТ ПО СКЛАДУ
===============

Дата формирования: {datetime.now().strftime('%d.%m.%Y %H:%M')}

ОБЩИЕ ПОКАЗАТЕЛИ:
• Всего товаров: {total_items}
• Общая стоимость: {total_value:,.2f}₽
• Товаров требующих пополнения: {len(low_stock_items)}

ТОВАРЫ ТРЕБУЮЩИЕ ПОПОЛНЕНИЯ:
"""
        
        for item in low_stock_items:
            report_content += f"""
• {item['name']}:
  - Текущий остаток: {item['current_stock']:.1f} {item['unit']}
  - Минимальный остаток: {item['min_stock']:.1f} {item['unit']}
  - Нужно заказать: {item['min_stock'] * 2 - item['current_stock']:.1f} {item['unit']}
"""
        
        report_content += f"""

СТАТИСТИКА ПО КАТЕГОРИЯМ:
"""
        
        categories = {}
        for item in self.inventory_items:
            cat = item['category']
            if cat not in categories:
                categories[cat] = {'count': 0, 'value': 0}
            categories[cat]['count'] += 1
            categories[cat]['value'] += item['current_stock'] * item['cost_per_unit']
        
        for category, stats in categories.items():
            report_content += f"• {category}: {stats['count']} товаров, {stats['value']:,.2f}₽\n"
        
        report_text.insert('1.0', report_content)
        report_text.config(state='disabled')

    def adjust_stock(self):
        """Изменить остаток товара"""
        selection = self.inventory_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите товар для изменения остатка")
            return

        # Получить данные выбранного товара
        item_values = self.inventory_tree.item(selection[0])['values']
        item_id = item_values[0]
        item_name = item_values[1]
        current_stock = float(item_values[3])

        # Диалог для ввода нового остатка
        new_stock = simpledialog.askfloat(
            "Изменение остатка",
            f"Товар: {item_name}\nТекущий остаток: {current_stock}\n\nВведите новый остаток:",
            initialvalue=current_stock
        )

        if new_stock is not None:
            reason = simpledialog.askstring(
                "Причина изменения",
                "Введите причину изменения остатка:",
                initialvalue="Ручная корректировка"
            )

            # Обновить в базе данных
            if self.db_manager.update_stock(item_id, new_stock, reason or "Ручная корректировка"):
                # Перезагрузить данные
                self.load_inventory_from_db()
                self.refresh_inventory_table()
                messagebox.showinfo("Успех", "Остаток успешно изменён!")
            else:
                messagebox.showerror("Ошибка", "Не удалось изменить остаток")

    def edit_item(self):
        """Редактировать товар"""
        selection = self.inventory_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите товар для редактирования")
            return

        # Получить данные выбранного товара
        item_values = self.inventory_tree.item(selection[0])['values']
        item_id = item_values[0]

        # Найти товар в списке
        item_data = None
        for item in self.inventory_items:
            if item['id'] == item_id:
                item_data = item
                break

        if not item_data:
            messagebox.showerror("Ошибка", "Товар не найден")
            return

        # Создать диалог редактирования (аналогично add_item, но с предзаполненными полями)
        edit_window = tk.Toplevel(self.window)
        edit_window.title("Редактировать Товар")
        edit_window.state('zoomed')  # Full screen
        edit_window.configure(bg=ModernStyles.COLORS['bg_main'])

        # Заголовок
        tk.Label(edit_window, text="Редактирование Товара",
                font=('Cambria', 32, 'bold italic'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

        # Основная форма
        form_frame = tk.Frame(edit_window, bg=ModernStyles.COLORS['bg_main'])
        form_frame.pack(fill='both', expand=True, padx=50, pady=20)

        # Поля формы
        fields = {}
        field_configs = [
            ('name', 'Название товара:', item_data['name']),
            ('category', 'Категория:', item_data['category']),
            ('current_stock', 'Текущий остаток:', str(item_data['current_stock'])),
            ('unit', 'Единица измерения:', item_data['unit']),
            ('min_stock', 'Минимальный остаток:', str(item_data['min_stock'])),
            ('max_stock', 'Максимальный остаток:', str(item_data.get('max_stock', 0))),
            ('cost_per_unit', 'Цена за единицу:', str(item_data['cost_per_unit'])),
            ('supplier', 'Поставщик:', item_data['supplier']),
            ('location', 'Расположение:', item_data.get('location', ''))
        ]

        for i, (field_name, label_text, default_value) in enumerate(field_configs):
            row = i // 2
            col = i % 2

            field_frame = tk.Frame(form_frame, bg=ModernStyles.COLORS['bg_main'])
            field_frame.grid(row=row, column=col, padx=20, pady=10, sticky='ew')

            tk.Label(field_frame, text=label_text, font=('Cambria', 18, 'bold italic'),
                    bg=ModernStyles.COLORS['bg_main']).pack(anchor='w')

            entry = tk.Entry(field_frame, font=('Cambria', 16, 'bold italic'), width=30)
            entry.pack(fill='x', pady=5)
            entry.insert(0, default_value)
            fields[field_name] = entry

        # Настроить сетку
        form_frame.grid_columnconfigure(0, weight=1)
        form_frame.grid_columnconfigure(1, weight=1)

        # Кнопки
        btn_frame = tk.Frame(edit_window, bg=ModernStyles.COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=20, pady=20)

        def save_changes():
            try:
                updated_item = {
                    'name': fields['name'].get(),
                    'category': fields['category'].get(),
                    'current_stock': float(fields['current_stock'].get() or 0),
                    'unit': fields['unit'].get(),
                    'min_stock': float(fields['min_stock'].get() or 0),
                    'max_stock': float(fields['max_stock'].get() or 0),
                    'cost_per_unit': float(fields['cost_per_unit'].get() or 0),
                    'supplier': fields['supplier'].get(),
                    'location': fields['location'].get()
                }

                # Обновить в базе данных
                if self.update_item_in_db(item_id, updated_item):
                    # Перезагрузить данные из БД
                    self.load_inventory_from_db()
                    self.refresh_inventory_table()
                    edit_window.destroy()
                    messagebox.showinfo("Успех", "Товар успешно обновлён!")
                else:
                    messagebox.showerror("Ошибка", "Не удалось обновить товар в базе данных")

            except ValueError:
                messagebox.showerror("Ошибка", "Проверьте правильность введённых числовых значений")
            except Exception as e:
                messagebox.showerror("Ошибка", f"Произошла ошибка: {e}")

        tk.Button(btn_frame, text="💾 Сохранить", command=save_changes,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 18, 'bold italic'), relief='flat', padx=30, pady=10).pack(side='left', padx=10)

        tk.Button(btn_frame, text="❌ Отмена", command=edit_window.destroy,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Cambria', 18, 'bold italic'), relief='flat', padx=30, pady=10).pack(side='left', padx=10)

    def delete_item(self):
        """Удалить товар"""
        selection = self.inventory_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите товар для удаления")
            return

        # Получить данные выбранного товара
        item_values = self.inventory_tree.item(selection[0])['values']
        item_id = item_values[0]
        item_name = item_values[1]

        # Подтверждение удаления
        if messagebox.askyesno("Подтверждение", f"Вы уверены, что хотите удалить товар '{item_name}'?"):
            if self.delete_item_from_db(item_id):
                # Перезагрузить данные
                self.load_inventory_from_db()
                self.refresh_inventory_table()
                messagebox.showinfo("Успех", "Товар успешно удалён!")
            else:
                messagebox.showerror("Ошибка", "Не удалось удалить товар")

def create_inventory_manager(parent, db_manager):
    """Создать менеджер склада"""
    manager = InventoryManagerWindow(parent, db_manager)
    manager.create_window()
    return manager
