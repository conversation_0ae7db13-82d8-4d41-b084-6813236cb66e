"""
Полностью функциональный менеджер склада
Управление товарами, остатками, поступлениями
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from datetime import datetime
from gui.styles import ModernStyles

class InventoryManagerWindow:
    """Окно управления складом"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        
        # Товары на складе
        self.inventory_items = [
            {
                "id": 1, "name": "Мука пшеничная высший сорт", "category": "Мука и крупы",
                "current_stock": 45.5, "unit": "кг", "min_stock": 20.0, "max_stock": 100.0,
                "cost_per_unit": 65.0, "supplier": "Мука-Сервис", "location": "Склад А-1"
            },
            {
                "id": 2, "name": "Молоко 3.2%", "category": "Молочные продукты",
                "current_stock": 28.0, "unit": "л", "min_stock": 15.0, "max_stock": 50.0,
                "cost_per_unit": 85.0, "supplier": "Молочный Дом", "location": "Холодильник 1"
            },
            {
                "id": 3, "name": "Говядина вырезка", "category": "Мясо",
                "current_stock": 12.5, "unit": "кг", "min_stock": 10.0, "max_stock": 30.0,
                "cost_per_unit": 850.0, "supplier": "Мясокомбинат Премиум", "location": "Морозильник 1"
            },
            {
                "id": 4, "name": "Картофель", "category": "Овощи",
                "current_stock": 85.0, "unit": "кг", "min_stock": 50.0, "max_stock": 200.0,
                "cost_per_unit": 45.0, "supplier": "Овощи-Плюс", "location": "Склад Б-2"
            },
            {
                "id": 5, "name": "Лук репчатый", "category": "Овощи",
                "current_stock": 8.0, "unit": "кг", "min_stock": 15.0, "max_stock": 50.0,
                "cost_per_unit": 35.0, "supplier": "Овощи-Плюс", "location": "Склад Б-2"
            }
        ]
    
    def create_window(self):
        """Создать окно управления складом"""
        try:
            from utils.window_utils import create_centered_dialog
            self.window = create_centered_dialog(
                self.parent,
                "📦 Управление Складом",
                width=1500,
                height=900,
                resizable=True
            )
        except ImportError:
            # Fallback если utils не доступны
            self.window = tk.Toplevel(self.parent)
            self.window.title("📦 Управление Складом")
            self.window.geometry("1500x900")
            self.window.configure(bg=ModernStyles.COLORS['bg_main'])
            self.window.resizable(True, True)

            # Центрировать окно
            self.window.update_idletasks()
            x = (self.window.winfo_screenwidth() // 2) - (1500 // 2)
            y = (self.window.winfo_screenheight() // 2) - (900 // 2)
            self.window.geometry(f"1500x900+{x}+{y}")
        self.window.resizable(True, True)
        
        self.create_interface()
    
    def create_interface(self):
        """Создать интерфейс управления складом"""
        # Заголовок
        header_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['primary'], height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="📦 Управление Складом",
                font=('Arial', 16, 'bold'), bg=ModernStyles.COLORS['primary'], 
                fg='white').pack(side='left', padx=20, pady=15)
        
        # Кнопки действий
        btn_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['primary'])
        btn_frame.pack(side='right', padx=20, pady=10)
        
        tk.Button(btn_frame, text="➕ Добавить Товар", command=self.add_item,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="📝 Изменить Остаток", command=self.adjust_stock,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="📊 Отчёт", command=self.generate_report,
                 bg=ModernStyles.COLORS['secondary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        # Основной контент
        main_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Статистика
        self.create_stats_section(main_frame)
        
        # Таблица товаров
        self.create_inventory_table(main_frame)
    
    def create_stats_section(self, parent):
        """Создать секцию статистики"""
        stats_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        stats_frame.pack(fill='x', pady=(0, 20))
        
        # Рассчитать статистику
        total_items = len(self.inventory_items)
        low_stock_items = len([item for item in self.inventory_items if item['current_stock'] <= item['min_stock']])
        total_value = sum(item['current_stock'] * item['cost_per_unit'] for item in self.inventory_items)
        
        # Карточки статистики
        self.create_stat_card(stats_frame, "Всего товаров", str(total_items), ModernStyles.COLORS['primary'])
        self.create_stat_card(stats_frame, "Требуют пополнения", str(low_stock_items), ModernStyles.COLORS['warning'])
        self.create_stat_card(stats_frame, "Общая стоимость", f"{total_value:,.0f}₽", ModernStyles.COLORS['success'])
        
        # Категории
        categories = set(item['category'] for item in self.inventory_items)
        self.create_stat_card(stats_frame, "Категорий", str(len(categories)), ModernStyles.COLORS['secondary'])
    
    def create_stat_card(self, parent, title, value, color):
        """Создать карточку статистики"""
        card = tk.Frame(parent, bg=color, relief='solid', bd=1)
        card.pack(side='left', fill='both', expand=True, padx=5)
        
        tk.Label(card, text=title, font=('Arial', 10), bg=color, fg='white').pack(pady=(15, 5))
        tk.Label(card, text=value, font=('Arial', 14, 'bold'), bg=color, fg='white').pack(pady=(0, 15))
    
    def create_inventory_table(self, parent):
        """Создать таблицу товаров"""
        # Заголовок таблицы
        tk.Label(parent, text="Товары на Складе",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=(0, 10))
        
        # Фрейм для таблицы
        table_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        table_frame.pack(fill='both', expand=True)
        
        # Создать таблицу
        columns = ('ID', 'Название', 'Категория', 'Остаток', 'Ед.изм.', 'Мин.остаток', 'Цена за ед.', 'Поставщик', 'Расположение')
        self.inventory_tree = ttk.Treeview(table_frame, columns=columns, show='headings', style="Modern.Treeview")
        
        # Настроить колонки
        column_widths = {
            'ID': 50, 'Название': 200, 'Категория': 120, 'Остаток': 80,
            'Ед.изм.': 60, 'Мин.остаток': 80, 'Цена за ед.': 100, 'Поставщик': 150, 'Расположение': 120
        }
        
        for col in columns:
            self.inventory_tree.heading(col, text=col)
            self.inventory_tree.column(col, width=column_widths.get(col, 100))
        
        # Заполнить данными
        self.refresh_inventory_table()
        
        # Скроллбары
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.inventory_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.inventory_tree.xview)
        self.inventory_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Упаковка
        self.inventory_tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')
    
    def refresh_inventory_table(self):
        """Обновить таблицу товаров"""
        # Очистить таблицу
        for item in self.inventory_tree.get_children():
            self.inventory_tree.delete(item)
        
        # Заполнить данными
        for item in self.inventory_items:
            # Определить цвет строки по уровню остатка
            if item['current_stock'] <= item['min_stock']:
                tags = ['low_stock']
            elif item['current_stock'] <= item['min_stock'] * 1.5:
                tags = ['medium_stock']
            else:
                tags = ['normal_stock']
            
            self.inventory_tree.insert('', 'end', values=(
                item['id'],
                item['name'],
                item['category'],
                f"{item['current_stock']:.1f}",
                item['unit'],
                f"{item['min_stock']:.1f}",
                f"{item['cost_per_unit']:.2f}₽",
                item['supplier'],
                item['location']
            ), tags=tags)
        
        # Настроить цвета
        self.inventory_tree.tag_configure('low_stock', background='#ffebee', foreground='#c62828')
        self.inventory_tree.tag_configure('medium_stock', background='#fff3e0', foreground='#ef6c00')
        self.inventory_tree.tag_configure('normal_stock', background='#e8f5e8', foreground='#2e7d32')
    
    def add_item(self):
        """Добавить новый товар"""
        add_window = tk.Toplevel(self.window)
        add_window.title("Добавить Товар")
        add_window.geometry("500x600")
        add_window.configure(bg=ModernStyles.COLORS['bg_main'])
        
        # Заголовок
        tk.Label(add_window, text="Добавление Нового Товара",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)
        
        # Форма
        form_frame = tk.Frame(add_window, bg=ModernStyles.COLORS['bg_main'])
        form_frame.pack(fill='both', expand=True, padx=20)
        
        # Поля формы
        fields = {}
        field_configs = [
            ('name', 'Название товара:', 'text'),
            ('category', 'Категория:', 'combo'),
            ('current_stock', 'Текущий остаток:', 'number'),
            ('unit', 'Единица измерения:', 'text'),
            ('min_stock', 'Минимальный остаток:', 'number'),
            ('max_stock', 'Максимальный остаток:', 'number'),
            ('cost_per_unit', 'Цена за единицу:', 'number'),
            ('supplier', 'Поставщик:', 'text'),
            ('location', 'Расположение:', 'text')
        ]
        
        for field_key, field_label, field_type in field_configs:
            field_frame = tk.Frame(form_frame, bg=ModernStyles.COLORS['bg_main'])
            field_frame.pack(fill='x', pady=5)
            
            tk.Label(field_frame, text=field_label, font=('Arial', 10, 'bold'),
                    bg=ModernStyles.COLORS['bg_main'], width=20, anchor='w').pack(side='left')
            
            if field_type == 'combo' and field_key == 'category':
                categories = list(set(item['category'] for item in self.inventory_items))
                var = tk.StringVar()
                widget = ttk.Combobox(field_frame, textvariable=var, values=categories)
            else:
                var = tk.StringVar()
                widget = tk.Entry(field_frame, textvariable=var, width=30)
            
            widget.pack(side='left', padx=10)
            fields[field_key] = var
        
        # Кнопки
        btn_frame = tk.Frame(add_window, bg=ModernStyles.COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=20, pady=20)
        
        def save_item():
            try:
                new_item = {
                    'id': max(item['id'] for item in self.inventory_items) + 1,
                    'name': fields['name'].get(),
                    'category': fields['category'].get(),
                    'current_stock': float(fields['current_stock'].get() or 0),
                    'unit': fields['unit'].get(),
                    'min_stock': float(fields['min_stock'].get() or 0),
                    'max_stock': float(fields['max_stock'].get() or 0),
                    'cost_per_unit': float(fields['cost_per_unit'].get() or 0),
                    'supplier': fields['supplier'].get(),
                    'location': fields['location'].get()
                }
                
                self.inventory_items.append(new_item)
                self.refresh_inventory_table()
                add_window.destroy()
                messagebox.showinfo("Успех", "Товар успешно добавлен!")
                
            except ValueError:
                messagebox.showerror("Ошибка", "Проверьте правильность введённых числовых значений")
        
        tk.Button(btn_frame, text="💾 Сохранить", command=save_item,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(side='left')
        
        tk.Button(btn_frame, text="❌ Отмена", command=add_window.destroy,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(side='right')
    
    def adjust_stock(self):
        """Изменить остаток товара"""
        selection = self.inventory_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите товар для изменения остатка")
            return
        
        # Получить данные выбранного товара
        item_values = self.inventory_tree.item(selection[0])['values']
        item_id = int(item_values[0])
        item_name = item_values[1]
        current_stock = float(item_values[3])
        
        # Запросить новый остаток
        new_stock = simpledialog.askfloat(
            "Изменение Остатка",
            f"Товар: {item_name}\nТекущий остаток: {current_stock}\n\nВведите новый остаток:",
            initialvalue=current_stock
        )
        
        if new_stock is not None:
            # Найти товар и обновить остаток
            for item in self.inventory_items:
                if item['id'] == item_id:
                    item['current_stock'] = new_stock
                    break
            
            self.refresh_inventory_table()
            messagebox.showinfo("Успех", f"Остаток товара '{item_name}' изменён на {new_stock}")
    
    def generate_report(self):
        """Генерировать отчёт по складу"""
        report_window = tk.Toplevel(self.window)
        report_window.title("📊 Отчёт по Складу")
        report_window.geometry("600x500")
        report_window.configure(bg='white')
        
        # Заголовок
        tk.Label(report_window, text="📊 Отчёт по Складу",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)
        
        # Содержимое отчёта
        report_text = tk.Text(report_window, font=('Arial', 10), wrap='word')
        report_text.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Генерировать отчёт
        total_items = len(self.inventory_items)
        low_stock_items = [item for item in self.inventory_items if item['current_stock'] <= item['min_stock']]
        total_value = sum(item['current_stock'] * item['cost_per_unit'] for item in self.inventory_items)
        
        report_content = f"""
ОТЧЁТ ПО СКЛАДУ
===============

Дата формирования: {datetime.now().strftime('%d.%m.%Y %H:%M')}

ОБЩИЕ ПОКАЗАТЕЛИ:
• Всего товаров: {total_items}
• Общая стоимость: {total_value:,.2f}₽
• Товаров требующих пополнения: {len(low_stock_items)}

ТОВАРЫ ТРЕБУЮЩИЕ ПОПОЛНЕНИЯ:
"""
        
        for item in low_stock_items:
            report_content += f"""
• {item['name']}:
  - Текущий остаток: {item['current_stock']:.1f} {item['unit']}
  - Минимальный остаток: {item['min_stock']:.1f} {item['unit']}
  - Нужно заказать: {item['min_stock'] * 2 - item['current_stock']:.1f} {item['unit']}
"""
        
        report_content += f"""

СТАТИСТИКА ПО КАТЕГОРИЯМ:
"""
        
        categories = {}
        for item in self.inventory_items:
            cat = item['category']
            if cat not in categories:
                categories[cat] = {'count': 0, 'value': 0}
            categories[cat]['count'] += 1
            categories[cat]['value'] += item['current_stock'] * item['cost_per_unit']
        
        for category, stats in categories.items():
            report_content += f"• {category}: {stats['count']} товаров, {stats['value']:,.2f}₽\n"
        
        report_text.insert('1.0', report_content)
        report_text.config(state='disabled')

def create_inventory_manager(parent, db_manager):
    """Создать менеджер склада"""
    manager = InventoryManagerWindow(parent, db_manager)
    manager.create_window()
    return manager
