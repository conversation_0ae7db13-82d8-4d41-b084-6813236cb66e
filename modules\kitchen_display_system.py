"""
Real-Time Kitchen Display System for Restaurant Management
Provides order management, cooking times tracking, and kitchen workflow optimization
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import sqlite3
import threading
import time
from gui.styles import ModernStyles, EnhancedStyles
from database.db_manager import DatabaseManager

def format_currency(amount):
    """Форматировать сумму в российском формате валюты"""
    try:
        if amount is None:
            amount = 0
        amount = float(amount)
        formatted = f"{amount:,.2f}".replace(',', ' ').replace('.', ',')
        return f"{formatted} руб"
    except:
        return "0,00 руб"

def format_time(seconds):
    """Format time in MM:SS format"""
    try:
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"
    except:
        return "00:00"

class KitchenDisplaySystem:
    """Real-time kitchen display system for order management and workflow optimization"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.orders = []
        self.active_orders = {}
        self.cooking_times = {}
        self.stations = {}
        self.refresh_timer = None
        self.auto_refresh = True
        
        # Initialize cooking times for different dish types
        self.default_cooking_times = {
            "Салаты": 5,
            "Супы": 15,
            "Горячие блюда": 25,
            "Пицца": 12,
            "Паста": 8,
            "Гриль": 20,
            "Десерты": 10,
            "Напитки": 3
        }
        
        # Kitchen stations
        self.kitchen_stations = {
            "Холодный цех": {"color": "#3b82f6", "orders": []},
            "Горячий цех": {"color": "#ef4444", "orders": []},
            "Гриль": {"color": "#f59e0b", "orders": []},
            "Пицца": {"color": "#8b5cf6", "orders": []},
            "Десерты": {"color": "#10b981", "orders": []},
            "Бар": {"color": "#06b6d4", "orders": []}
        }
        
    def show_kitchen_display(self):
        """Display the kitchen display system"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
            
        self.window = tk.Toplevel(self.parent)
        self.window.title("🍳 Кухонный Дисплей - Управление Заказами")
        self.window.geometry("1800x1000")
        self.window.configure(bg='#1a1a1a')  # Dark background for kitchen
        self.window.state('zoomed')
        
        # Apply professional styling
        EnhancedStyles.apply_professional_style(self.window)
        
        self.create_kitchen_interface()
        self.load_orders()
        self.start_auto_refresh()
    
    def create_kitchen_interface(self):
        """Create the main kitchen display interface"""
        # Header with status and controls
        self.create_header()
        
        # Main content area
        main_frame = tk.Frame(self.window, bg='#1a1a1a')
        main_frame.pack(fill='both', expand=True, padx=10, pady=(0, 10))
        
        # Create kitchen stations layout
        self.create_stations_layout(main_frame)
        
        # Order details panel
        self.create_order_details_panel(main_frame)
    
    def create_header(self):
        """Create header with status and controls"""
        header_frame = tk.Frame(self.window, bg='#2d3748', height=80)
        header_frame.pack(fill='x', padx=10, pady=10)
        header_frame.pack_propagate(False)
        
        # Left side - Title and status
        left_frame = tk.Frame(header_frame, bg='#2d3748')
        left_frame.pack(side='left', fill='y', padx=20)
        
        title_label = tk.Label(left_frame, 
                              text="🍳 КУХОННЫЙ ДИСПЛЕЙ",
                              font=('Cambria', 24, 'bold italic'),
                              fg='white',
                              bg='#2d3748')
        title_label.pack(anchor='w')
        
        # Status info
        self.status_frame = tk.Frame(left_frame, bg='#2d3748')
        self.status_frame.pack(anchor='w', pady=(5, 0))
        
        self.active_orders_label = tk.Label(self.status_frame,
                                           text="Активных заказов: 0",
                                           font=('Cambria', 12, 'italic'),
                                           fg='#a0aec0',
                                           bg='#2d3748')
        self.active_orders_label.pack(side='left', padx=(0, 20))
        
        self.current_time_label = tk.Label(self.status_frame,
                                          text="",
                                          font=('Cambria', 12, 'italic'),
                                          fg='#a0aec0',
                                          bg='#2d3748')
        self.current_time_label.pack(side='left')
        
        # Right side - Controls
        controls_frame = tk.Frame(header_frame, bg='#2d3748')
        controls_frame.pack(side='right', fill='y', padx=20)
        
        # Control buttons
        btn_frame = tk.Frame(controls_frame, bg='#2d3748')
        btn_frame.pack(expand=True)
        
        # New order button
        new_order_btn = tk.Button(btn_frame, text="➕ Новый Заказ",
                                 command=self.create_new_order,
                                 bg='#48bb78', fg='white',
                                 font=('Cambria', 11, 'bold italic'),
                                 relief='flat', bd=0, padx=15, pady=8)
        new_order_btn.pack(side='left', padx=(0, 10))
        
        # Refresh button
        refresh_btn = tk.Button(btn_frame, text="🔄 Обновить",
                               command=self.refresh_orders,
                               bg='#4299e1', fg='white',
                               font=('Cambria', 11, 'bold italic'),
                               relief='flat', bd=0, padx=15, pady=8)
        refresh_btn.pack(side='left', padx=(0, 10))
        
        # Settings button
        settings_btn = tk.Button(btn_frame, text="⚙️ Настройки",
                                command=self.show_kitchen_settings,
                                bg='#805ad5', fg='white',
                                font=('Cambria', 11, 'bold italic'),
                                relief='flat', bd=0, padx=15, pady=8)
        settings_btn.pack(side='left')
    
    def create_stations_layout(self, parent):
        """Create kitchen stations layout"""
        # Stations container
        stations_frame = tk.Frame(parent, bg='#1a1a1a')
        stations_frame.pack(fill='both', expand=True)
        
        # Create stations in a 2x3 grid
        self.station_frames = {}
        row = 0
        col = 0
        
        for station_name, station_info in self.kitchen_stations.items():
            # Station frame
            station_frame = tk.Frame(stations_frame, bg=station_info['color'], relief='solid', bd=2)
            station_frame.grid(row=row, column=col, padx=5, pady=5, sticky='nsew')
            
            # Configure grid weights
            stations_frame.grid_rowconfigure(row, weight=1)
            stations_frame.grid_columnconfigure(col, weight=1)
            
            # Station header
            header_frame = tk.Frame(station_frame, bg=station_info['color'])
            header_frame.pack(fill='x', padx=5, pady=5)
            
            tk.Label(header_frame, text=station_name,
                    font=('Cambria', 14, 'bold italic'),
                    fg='white', bg=station_info['color']).pack()
            
            # Orders container for this station
            orders_container = tk.Frame(station_frame, bg='#2d3748')
            orders_container.pack(fill='both', expand=True, padx=5, pady=(0, 5))
            
            # Scrollable orders list
            canvas = tk.Canvas(orders_container, bg='#2d3748', highlightthickness=0)
            scrollbar = ttk.Scrollbar(orders_container, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg='#2d3748')
            
            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )
            
            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)
            
            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")
            
            # Store references
            self.station_frames[station_name] = {
                'frame': station_frame,
                'orders_frame': scrollable_frame,
                'canvas': canvas
            }
            
            # Move to next position
            col += 1
            if col >= 3:
                col = 0
                row += 1
    
    def create_order_details_panel(self, parent):
        """Create order details panel"""
        # Details panel (right side)
        details_frame = tk.Frame(parent, bg='#2d3748', width=400)
        details_frame.pack(side='right', fill='y', padx=(10, 0))
        details_frame.pack_propagate(False)
        
        # Panel header
        tk.Label(details_frame, text="📋 Детали Заказа",
                font=('Cambria', 16, 'bold italic'),
                fg='white', bg='#2d3748').pack(pady=15)
        
        # Order info frame
        self.order_info_frame = tk.Frame(details_frame, bg='#2d3748')
        self.order_info_frame.pack(fill='both', expand=True, padx=15, pady=(0, 15))
        
        # Initially show "No order selected" message
        tk.Label(self.order_info_frame, 
                text="Выберите заказ для просмотра деталей",
                font=('Cambria', 12, 'italic'),
                fg='#a0aec0', bg='#2d3748').pack(expand=True)
    
    def load_orders(self):
        """Load orders from database"""
        try:
            # Initialize database tables if needed
            self.init_kitchen_tables()
            
            # Load active orders
            self.orders = self.get_active_orders()
            
            # Distribute orders to stations
            self.distribute_orders_to_stations()
            
            # Update display
            self.update_stations_display()
            self.update_status()
            
        except Exception as e:
            print(f"Error loading orders: {e}")
            # Use demo data for testing
            self.create_demo_orders()
    
    def init_kitchen_tables(self):
        """Initialize kitchen-related database tables"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Kitchen orders table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS kitchen_orders (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        order_number TEXT NOT NULL,
                        table_number TEXT,
                        customer_name TEXT,
                        order_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        status TEXT DEFAULT 'pending',
                        priority INTEGER DEFAULT 1,
                        estimated_time INTEGER DEFAULT 15,
                        actual_time INTEGER,
                        station TEXT,
                        special_instructions TEXT,
                        total_amount REAL DEFAULT 0
                    )
                ''')
                
                # Kitchen order items table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS kitchen_order_items (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        kitchen_order_id INTEGER,
                        dish_name TEXT NOT NULL,
                        quantity INTEGER DEFAULT 1,
                        cooking_time INTEGER DEFAULT 10,
                        status TEXT DEFAULT 'pending',
                        started_at TIMESTAMP,
                        completed_at TIMESTAMP,
                        station TEXT,
                        special_notes TEXT,
                        FOREIGN KEY (kitchen_order_id) REFERENCES kitchen_orders (id)
                    )
                ''')
                
                conn.commit()
                print("✅ Kitchen tables initialized")
                
        except Exception as e:
            print(f"Error initializing kitchen tables: {e}")

    def get_active_orders(self):
        """Get active orders from database"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT ko.*,
                           GROUP_CONCAT(koi.dish_name || ' x' || koi.quantity) as items
                    FROM kitchen_orders ko
                    LEFT JOIN kitchen_order_items koi ON ko.id = koi.kitchen_order_id
                    WHERE ko.status IN ('pending', 'cooking', 'ready')
                    GROUP BY ko.id
                    ORDER BY ko.priority DESC, ko.order_time ASC
                ''')
                return cursor.fetchall()
        except Exception as e:
            print(f"Error getting active orders: {e}")
            return []

    def create_demo_orders(self):
        """Create demo orders for testing"""
        demo_orders = [
            {
                'id': 1, 'order_number': 'ORD-001', 'table_number': 'Стол 5',
                'customer_name': 'Иванов А.', 'status': 'cooking', 'priority': 2,
                'estimated_time': 15, 'station': 'Горячий цех', 'total_amount': 1250.00,
                'items': 'Борщ x1, Котлета по-киевски x1', 'order_time': datetime.now() - timedelta(minutes=8)
            },
            {
                'id': 2, 'order_number': 'ORD-002', 'table_number': 'Стол 12',
                'customer_name': 'Петрова М.', 'status': 'pending', 'priority': 1,
                'estimated_time': 12, 'station': 'Пицца', 'total_amount': 890.00,
                'items': 'Пицца Маргарита x2', 'order_time': datetime.now() - timedelta(minutes=3)
            },
            {
                'id': 3, 'order_number': 'ORD-003', 'table_number': 'Стол 8',
                'customer_name': 'Сидоров В.', 'status': 'cooking', 'priority': 3,
                'estimated_time': 20, 'station': 'Гриль', 'total_amount': 1850.00,
                'items': 'Стейк Рибай x1, Салат Цезарь x1', 'order_time': datetime.now() - timedelta(minutes=12)
            },
            {
                'id': 4, 'order_number': 'ORD-004', 'table_number': 'Стол 3',
                'customer_name': 'Козлова Е.', 'status': 'ready', 'priority': 1,
                'estimated_time': 5, 'station': 'Холодный цех', 'total_amount': 650.00,
                'items': 'Салат Греческий x2, Брускетта x1', 'order_time': datetime.now() - timedelta(minutes=18)
            },
            {
                'id': 5, 'order_number': 'ORD-005', 'table_number': 'Стол 15',
                'customer_name': 'Морозов Д.', 'status': 'pending', 'priority': 1,
                'estimated_time': 8, 'station': 'Бар', 'total_amount': 420.00,
                'items': 'Мохито x2, Кофе Американо x1', 'order_time': datetime.now() - timedelta(minutes=1)
            }
        ]

        self.orders = demo_orders

    def distribute_orders_to_stations(self):
        """Distribute orders to appropriate kitchen stations"""
        # Clear existing orders from stations
        for station in self.kitchen_stations:
            self.kitchen_stations[station]['orders'] = []

        # Distribute orders based on station assignment
        for order in self.orders:
            station = order.get('station', 'Горячий цех')
            if station in self.kitchen_stations:
                self.kitchen_stations[station]['orders'].append(order)

    def update_stations_display(self):
        """Update the display of all kitchen stations"""
        for station_name, station_data in self.kitchen_stations.items():
            orders_frame = self.station_frames[station_name]['orders_frame']

            # Clear existing order widgets
            for widget in orders_frame.winfo_children():
                widget.destroy()

            # Add orders to station
            for order in station_data['orders']:
                self.create_order_card(orders_frame, order, station_name)

    def create_order_card(self, parent, order, station_name):
        """Create an order card widget"""
        # Determine card color based on status and priority
        status_colors = {
            'pending': '#fbbf24',    # Yellow
            'cooking': '#f59e0b',    # Orange
            'ready': '#10b981',      # Green
            'completed': '#6b7280'   # Gray
        }

        priority_colors = {
            1: '#374151',  # Normal
            2: '#dc2626',  # High priority - Red
            3: '#7c2d12'   # Urgent - Dark red
        }

        status = order.get('status', 'pending')
        priority = order.get('priority', 1)

        # Main card frame
        card_frame = tk.Frame(parent, bg=status_colors.get(status, '#374151'),
                             relief='solid', bd=2)
        card_frame.pack(fill='x', padx=5, pady=5)

        # Priority indicator
        if priority > 1:
            priority_frame = tk.Frame(card_frame, bg=priority_colors.get(priority, '#374151'), height=5)
            priority_frame.pack(fill='x')

        # Order header
        header_frame = tk.Frame(card_frame, bg=status_colors.get(status, '#374151'))
        header_frame.pack(fill='x', padx=10, pady=(10, 5))

        # Order number and table
        order_info = f"{order.get('order_number', 'N/A')} - {order.get('table_number', 'N/A')}"
        tk.Label(header_frame, text=order_info,
                font=('Cambria', 12, 'bold italic'),
                fg='white', bg=status_colors.get(status, '#374151')).pack(anchor='w')

        # Customer name
        if order.get('customer_name'):
            tk.Label(header_frame, text=order['customer_name'],
                    font=('Cambria', 10, 'italic'),
                    fg='white', bg=status_colors.get(status, '#374151')).pack(anchor='w')

        # Order items
        items_frame = tk.Frame(card_frame, bg='#2d3748')
        items_frame.pack(fill='x', padx=10, pady=5)

        items_text = order.get('items', 'Нет данных о блюдах')
        tk.Label(items_frame, text=items_text,
                font=('Cambria', 10),
                fg='white', bg='#2d3748',
                wraplength=200, justify='left').pack(anchor='w')

        # Time and status info
        info_frame = tk.Frame(card_frame, bg=status_colors.get(status, '#374151'))
        info_frame.pack(fill='x', padx=10, pady=(5, 10))

        # Calculate elapsed time
        order_time = order.get('order_time', datetime.now())
        if isinstance(order_time, str):
            try:
                order_time = datetime.fromisoformat(order_time)
            except:
                order_time = datetime.now()

        elapsed = datetime.now() - order_time
        elapsed_minutes = int(elapsed.total_seconds() / 60)

        time_text = f"⏱️ {elapsed_minutes} мин"
        estimated_time = order.get('estimated_time', 15)

        if elapsed_minutes > estimated_time:
            time_color = '#dc2626'  # Red for overdue
        elif elapsed_minutes > estimated_time * 0.8:
            time_color = '#f59e0b'  # Orange for approaching deadline
        else:
            time_color = 'white'

        tk.Label(info_frame, text=time_text,
                font=('Cambria', 10, 'bold'),
                fg=time_color, bg=status_colors.get(status, '#374151')).pack(side='left')

        # Status buttons
        btn_frame = tk.Frame(info_frame, bg=status_colors.get(status, '#374151'))
        btn_frame.pack(side='right')

        if status == 'pending':
            start_btn = tk.Button(btn_frame, text="▶️ Начать",
                                 command=lambda: self.start_order(order['id']),
                                 bg='#10b981', fg='white',
                                 font=('Cambria', 8, 'bold'),
                                 relief='flat', bd=0, padx=8, pady=2)
            start_btn.pack(side='right')
        elif status == 'cooking':
            ready_btn = tk.Button(btn_frame, text="✅ Готово",
                                 command=lambda: self.complete_order(order['id']),
                                 bg='#10b981', fg='white',
                                 font=('Cambria', 8, 'bold'),
                                 relief='flat', bd=0, padx=8, pady=2)
            ready_btn.pack(side='right')
        elif status == 'ready':
            serve_btn = tk.Button(btn_frame, text="🍽️ Подано",
                                 command=lambda: self.serve_order(order['id']),
                                 bg='#6b7280', fg='white',
                                 font=('Cambria', 8, 'bold'),
                                 relief='flat', bd=0, padx=8, pady=2)
            serve_btn.pack(side='right')

        # Make card clickable for details
        def show_order_details(event):
            self.show_order_details(order)

        card_frame.bind("<Button-1>", show_order_details)
        for child in card_frame.winfo_children():
            child.bind("<Button-1>", show_order_details)

    def update_status(self):
        """Update header status information"""
        active_count = len([o for o in self.orders if o.get('status') in ['pending', 'cooking']])
        self.active_orders_label.config(text=f"Активных заказов: {active_count}")

        current_time = datetime.now().strftime("%H:%M:%S")
        self.current_time_label.config(text=f"Время: {current_time}")

    def show_order_details(self, order):
        """Show detailed information about an order"""
        # Clear existing details
        for widget in self.order_info_frame.winfo_children():
            widget.destroy()

        # Order header
        header_frame = tk.Frame(self.order_info_frame, bg='#4a5568')
        header_frame.pack(fill='x', pady=(0, 10))

        tk.Label(header_frame, text=f"Заказ {order.get('order_number', 'N/A')}",
                font=('Cambria', 14, 'bold italic'),
                fg='white', bg='#4a5568').pack(pady=10)

        # Order details
        details = [
            ("Стол:", order.get('table_number', 'N/A')),
            ("Клиент:", order.get('customer_name', 'N/A')),
            ("Статус:", order.get('status', 'N/A').title()),
            ("Цех:", order.get('station', 'N/A')),
            ("Сумма:", format_currency(order.get('total_amount', 0))),
            ("Время заказа:", order.get('order_time', 'N/A')),
            ("Ожидаемое время:", f"{order.get('estimated_time', 0)} мин")
        ]

        for label, value in details:
            detail_frame = tk.Frame(self.order_info_frame, bg='#2d3748')
            detail_frame.pack(fill='x', pady=2)

            tk.Label(detail_frame, text=label,
                    font=('Cambria', 10, 'bold'),
                    fg='#a0aec0', bg='#2d3748').pack(side='left')

            tk.Label(detail_frame, text=str(value),
                    font=('Cambria', 10),
                    fg='white', bg='#2d3748').pack(side='right')

        # Items section
        items_frame = tk.Frame(self.order_info_frame, bg='#2d3748')
        items_frame.pack(fill='x', pady=(10, 0))

        tk.Label(items_frame, text="Блюда:",
                font=('Cambria', 12, 'bold italic'),
                fg='white', bg='#2d3748').pack(anchor='w')

        items_text = order.get('items', 'Нет данных о блюдах')
        tk.Label(items_frame, text=items_text,
                font=('Cambria', 10),
                fg='#a0aec0', bg='#2d3748',
                wraplength=350, justify='left').pack(anchor='w', pady=(5, 0))

    # Order management methods
    def start_order(self, order_id):
        """Start cooking an order"""
        try:
            # Update order status in database
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE kitchen_orders
                    SET status = 'cooking', started_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (order_id,))
                conn.commit()

            # Update local data
            for order in self.orders:
                if order.get('id') == order_id:
                    order['status'] = 'cooking'
                    break

            # Refresh display
            self.refresh_orders()

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось начать приготовление заказа: {e}")

    def complete_order(self, order_id):
        """Mark order as ready"""
        try:
            # Update order status in database
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE kitchen_orders
                    SET status = 'ready', completed_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (order_id,))
                conn.commit()

            # Update local data
            for order in self.orders:
                if order.get('id') == order_id:
                    order['status'] = 'ready'
                    break

            # Refresh display
            self.refresh_orders()

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось завершить заказ: {e}")

    def serve_order(self, order_id):
        """Mark order as served"""
        try:
            # Update order status in database
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE kitchen_orders
                    SET status = 'completed', served_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (order_id,))
                conn.commit()

            # Remove from local orders list
            self.orders = [order for order in self.orders if order.get('id') != order_id]

            # Refresh display
            self.refresh_orders()

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось отметить заказ как поданный: {e}")

    def create_new_order(self):
        """Create a new kitchen order"""
        try:
            # Create new order dialog
            order_dialog = tk.Toplevel(self.window)
            order_dialog.title("➕ Новый Заказ")
            order_dialog.geometry("600x500")
            order_dialog.configure(bg=ModernStyles.COLORS['bg_main'])
            order_dialog.transient(self.window)
            order_dialog.grab_set()

            # Center the dialog
            order_dialog.geometry("+{}+{}".format(
                int(order_dialog.winfo_screenwidth()/2 - 300),
                int(order_dialog.winfo_screenheight()/2 - 250)
            ))

            # Dialog content
            tk.Label(order_dialog, text="Создание Нового Заказа",
                    font=('Cambria', 16, 'bold italic'),
                    fg=ModernStyles.COLORS['primary'],
                    bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

            # Order form
            form_frame = tk.Frame(order_dialog, bg=ModernStyles.COLORS['bg_main'])
            form_frame.pack(fill='both', expand=True, padx=30, pady=20)

            # Order number
            tk.Label(form_frame, text="Номер заказа:",
                    font=('Cambria', 12, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).pack(anchor='w')
            order_number_var = tk.StringVar(value=f"ORD-{len(self.orders) + 1:03d}")
            tk.Entry(form_frame, textvariable=order_number_var,
                    font=('Cambria', 12)).pack(fill='x', pady=(5, 15))

            # Table number
            tk.Label(form_frame, text="Номер стола:",
                    font=('Cambria', 12, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).pack(anchor='w')
            table_var = tk.StringVar()
            tk.Entry(form_frame, textvariable=table_var,
                    font=('Cambria', 12)).pack(fill='x', pady=(5, 15))

            # Customer name
            tk.Label(form_frame, text="Имя клиента:",
                    font=('Cambria', 12, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).pack(anchor='w')
            customer_var = tk.StringVar()
            tk.Entry(form_frame, textvariable=customer_var,
                    font=('Cambria', 12)).pack(fill='x', pady=(5, 15))

            # Station
            tk.Label(form_frame, text="Кухонный цех:",
                    font=('Cambria', 12, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).pack(anchor='w')
            station_var = tk.StringVar(value="Горячий цех")
            station_combo = ttk.Combobox(form_frame, textvariable=station_var,
                                        values=list(self.kitchen_stations.keys()),
                                        font=('Cambria', 12))
            station_combo.pack(fill='x', pady=(5, 15))

            # Items
            tk.Label(form_frame, text="Блюда:",
                    font=('Cambria', 12, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).pack(anchor='w')
            items_var = tk.StringVar()
            items_entry = tk.Text(form_frame, height=4, font=('Cambria', 12))
            items_entry.pack(fill='x', pady=(5, 15))

            # Buttons
            btn_frame = tk.Frame(form_frame, bg=ModernStyles.COLORS['bg_main'])
            btn_frame.pack(fill='x', pady=20)

            def save_order():
                try:
                    # Get form data
                    order_data = {
                        'order_number': order_number_var.get(),
                        'table_number': table_var.get(),
                        'customer_name': customer_var.get(),
                        'station': station_var.get(),
                        'items': items_entry.get('1.0', 'end-1c'),
                        'status': 'pending',
                        'priority': 1,
                        'estimated_time': 15,
                        'total_amount': 0,
                        'order_time': datetime.now()
                    }

                    # Add to orders list (in real implementation, save to database)
                    new_id = max([o.get('id', 0) for o in self.orders], default=0) + 1
                    order_data['id'] = new_id
                    self.orders.append(order_data)

                    # Close dialog and refresh
                    order_dialog.destroy()
                    self.refresh_orders()

                    messagebox.showinfo("Успех", f"Заказ {order_data['order_number']} создан!")

                except Exception as e:
                    messagebox.showerror("Ошибка", f"Не удалось создать заказ: {e}")

            tk.Button(btn_frame, text="💾 Создать Заказ",
                     command=save_order,
                     bg=ModernStyles.COLORS['success'], fg='white',
                     font=('Cambria', 12, 'bold italic'),
                     relief='flat', bd=0, padx=20, pady=8).pack(side='left')

            tk.Button(btn_frame, text="❌ Отмена",
                     command=order_dialog.destroy,
                     bg=ModernStyles.COLORS['danger'], fg='white',
                     font=('Cambria', 12, 'bold italic'),
                     relief='flat', bd=0, padx=20, pady=8).pack(side='right')

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть форму создания заказа: {e}")

    def refresh_orders(self):
        """Refresh orders display"""
        try:
            # Reload orders from database or use existing demo data
            if hasattr(self, 'orders') and self.orders:
                self.distribute_orders_to_stations()
                self.update_stations_display()
                self.update_status()
            else:
                self.load_orders()
        except Exception as e:
            print(f"Error refreshing orders: {e}")

    def start_auto_refresh(self):
        """Start automatic refresh timer"""
        if self.auto_refresh and self.window and self.window.winfo_exists():
            self.refresh_orders()
            # Schedule next refresh in 30 seconds
            self.refresh_timer = self.window.after(30000, self.start_auto_refresh)

    def show_kitchen_settings(self):
        """Show kitchen settings dialog"""
        try:
            messagebox.showinfo("Настройки", "Настройки кухонного дисплея в разработке")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть настройки: {e}")

def create_kitchen_display_system(parent, db_manager):
    """Create and show the kitchen display system"""
    try:
        kitchen_system = KitchenDisplaySystem(parent, db_manager)
        kitchen_system.show_kitchen_display()
        return kitchen_system
    except Exception as e:
        messagebox.showerror("Ошибка", f"Не удалось открыть кухонный дисплей: {e}")
        return None
