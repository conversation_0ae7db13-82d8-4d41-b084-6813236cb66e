"""
Professional Reporting System
Comprehensive reporting system with custom report builder, scheduled reports, and executive dashboards
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
import json
import sys
import os
from typing import Dict, List, Optional, Tuple
import threading
import time

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gui.styles import ModernStyles
from utils.error_handler import handle_module_error, log_info

class ProfessionalReportingSystem:
    """Professional reporting system with advanced features"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.current_report = None
        self.scheduled_reports = []
        self.report_templates = []
        self.custom_reports = []
        
        # Initialize data
        self.load_data()
        
        # Create main window
        self.create_main_window()
    
    def load_data(self):
        """Load reporting data"""
        self.load_report_templates()
        self.load_scheduled_reports()
        self.load_custom_reports()
    
    def load_report_templates(self):
        """Load report templates"""
        self.report_templates = [
            {
                "id": "executive_dashboard",
                "name": "Исполнительная Панель",
                "description": "Ключевые показатели для руководства",
                "category": "Управленческие",
                "frequency": "Ежедневно",
                "format": "PDF",
                "sections": ["Финансы", "Продажи", "Персонал", "Склад"]
            },
            {
                "id": "financial_summary",
                "name": "Финансовая Сводка",
                "description": "Полный финансовый отчет",
                "category": "Финансовые",
                "frequency": "Ежемесячно",
                "format": "Excel",
                "sections": ["Доходы", "Расходы", "Прибыль", "Баланс"]
            },
            {
                "id": "sales_analytics",
                "name": "Аналитика Продаж",
                "description": "Детальный анализ продаж",
                "category": "Продажи",
                "frequency": "Еженедельно",
                "format": "PDF",
                "sections": ["Динамика", "Топ блюда", "Клиенты", "Прогноз"]
            },
            {
                "id": "inventory_report",
                "name": "Отчет по Складу",
                "description": "Состояние складских запасов",
                "category": "Склад",
                "frequency": "Еженедельно",
                "format": "Excel",
                "sections": ["Остатки", "Движение", "Поставщики", "Потери"]
            },
            {
                "id": "staff_performance",
                "name": "Эффективность Персонала",
                "description": "Анализ работы сотрудников",
                "category": "HR",
                "frequency": "Ежемесячно",
                "format": "PDF",
                "sections": ["Производительность", "Смены", "Затраты", "KPI"]
            }
        ]
    
    def load_scheduled_reports(self):
        """Load scheduled reports"""
        self.scheduled_reports = [
            {
                "id": "daily_exec",
                "template_id": "executive_dashboard",
                "name": "Ежедневная Сводка",
                "schedule": "Ежедневно в 09:00",
                "recipients": ["<EMAIL>", "<EMAIL>"],
                "status": "Активен",
                "last_run": "2024-01-20 09:00",
                "next_run": "2024-01-21 09:00"
            },
            {
                "id": "weekly_sales",
                "template_id": "sales_analytics",
                "name": "Еженедельные Продажи",
                "schedule": "Понедельник в 10:00",
                "recipients": ["<EMAIL>"],
                "status": "Активен",
                "last_run": "2024-01-15 10:00",
                "next_run": "2024-01-22 10:00"
            },
            {
                "id": "monthly_financial",
                "template_id": "financial_summary",
                "name": "Месячный Финансовый",
                "schedule": "1 число в 08:00",
                "recipients": ["<EMAIL>", "<EMAIL>"],
                "status": "Активен",
                "last_run": "2024-01-01 08:00",
                "next_run": "2024-02-01 08:00"
            }
        ]
    
    def load_custom_reports(self):
        """Load custom reports"""
        self.custom_reports = [
            {
                "id": "custom_1",
                "name": "Анализ Прибыльности Блюд",
                "description": "Пользовательский отчет по рентабельности",
                "created_by": "Менеджер",
                "created_date": "2024-01-15",
                "data_sources": ["Продажи", "Себестоимость", "Склад"],
                "filters": {"period": "Месяц", "category": "Все"},
                "charts": ["Столбчатая", "Круговая"],
                "status": "Готов"
            }
        ]
    
    def create_main_window(self):
        """Create main reporting window"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("Профессиональная Система Отчетности")
        self.window.geometry("1400x900")
        self.window.configure(bg=ModernStyles.COLORS['bg_primary'])
        
        # Make window resizable and center it
        self.window.resizable(True, True)
        self.center_window()
        
        # Create header
        self.create_header()
        
        # Create main content
        self.create_main_content()
        
        # Create status bar
        self.create_status_bar()
        
        # Load initial data
        self.refresh_all_displays()
    
    def center_window(self):
        """Center window on screen"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (1400 // 2)
        y = (self.window.winfo_screenheight() // 2) - (900 // 2)
        self.window.geometry(f"1400x900+{x}+{y}")
    
    def create_header(self):
        """Create header section"""
        header_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['primary'], height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        # Title
        title_label = tk.Label(
            header_frame,
            text="📊 Профессиональная Система Отчетности",
            font=ModernStyles.FONTS['title'],
            bg=ModernStyles.COLORS['primary'],
            fg='white'
        )
        title_label.pack(side='left', padx=20, pady=20)
        
        # Action buttons
        buttons_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['primary'])
        buttons_frame.pack(side='right', padx=20, pady=20)
        
        action_buttons = [
            ("🔄 Обновить", self.refresh_all_data),
            ("⚙️ Настройки", self.open_settings),
            ("❓ Справка", self.show_help)
        ]
        
        for text, command in action_buttons:
            btn = tk.Button(
                buttons_frame,
                text=text,
                command=command,
                font=ModernStyles.FONTS['button'],
                bg='white',
                fg=ModernStyles.COLORS['primary'],
                relief='flat',
                padx=15,
                pady=5
            )
            btn.pack(side='left', padx=5)
    
    def create_main_content(self):
        """Create main content area"""
        # Create notebook for different sections
        self.notebook = ttk.Notebook(self.window)
        self.notebook.pack(fill='both', expand=True, padx=20, pady=(0, 20))
        
        # Create tabs
        self.create_dashboard_tab()
        self.create_templates_tab()
        self.create_custom_builder_tab()
        self.create_scheduled_reports_tab()
        self.create_report_history_tab()
        self.create_analytics_tab()
    
    def create_status_bar(self):
        """Create status bar"""
        self.status_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_secondary'], height=30)
        self.status_frame.pack(fill='x', side='bottom')
        self.status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(
            self.status_frame,
            text="Готов к работе",
            font=ModernStyles.FONTS['small'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        self.status_label.pack(side='left', padx=10, pady=5)
        
        # Current time
        self.time_label = tk.Label(
            self.status_frame,
            text="",
            font=ModernStyles.FONTS['small'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        self.time_label.pack(side='right', padx=10, pady=5)
        
        # Update time
        self.update_time()
    
    def update_time(self):
        """Update current time display"""
        current_time = datetime.now().strftime("%d.%m.%Y %H:%M:%S")
        self.time_label.config(text=current_time)
        self.window.after(1000, self.update_time)

    def create_dashboard_tab(self):
        """Create executive dashboard tab"""
        dashboard_frame = tk.Frame(self.notebook, bg=ModernStyles.COLORS['bg_primary'])
        self.notebook.add(dashboard_frame, text="📊 Исполнительная Панель")

        # Scrollable frame
        canvas = tk.Canvas(dashboard_frame, bg=ModernStyles.COLORS['bg_primary'])
        scrollbar = ttk.Scrollbar(dashboard_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=ModernStyles.COLORS['bg_primary'])

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Dashboard content
        self.create_dashboard_content(scrollable_frame)

    def create_dashboard_content(self, parent):
        """Create dashboard content"""
        # Title
        title_label = tk.Label(
            parent,
            text="📊 Исполнительная Панель Управления",
            font=ModernStyles.FONTS['title'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        title_label.pack(pady=20)

        # Key metrics cards
        metrics_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_primary'])
        metrics_frame.pack(fill='x', padx=20, pady=10)

        metrics = [
            ("💰 Выручка за месяц", "1,568,400 руб", "+8.2%", ModernStyles.COLORS['success']),
            ("📊 Количество заказов", "4,127", "+12.5%", ModernStyles.COLORS['info']),
            ("💳 Средний чек", "380 руб", "+3.1%", ModernStyles.COLORS['warning']),
            ("📈 Чистая прибыль", "167,360 руб", "+15%", ModernStyles.COLORS['primary'])
        ]

        for i, (title, value, change, color) in enumerate(metrics):
            self.create_metric_card(metrics_frame, title, value, change, color, i)

        # Charts section
        charts_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_primary'])
        charts_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Sales trend chart placeholder
        sales_chart_frame = tk.LabelFrame(
            charts_frame,
            text="📈 Динамика Продаж",
            font=ModernStyles.COLORS['body'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        sales_chart_frame.pack(fill='x', pady=(0, 10))

        # Chart placeholder
        chart_placeholder = tk.Label(
            sales_chart_frame,
            text="📊 График динамики продаж за последние 30 дней\n(Интеграция с библиотекой графиков)",
            font=ModernStyles.FONTS['body'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_secondary'],
            height=8
        )
        chart_placeholder.pack(fill='x', padx=10, pady=10)

        # Top performers table
        performers_frame = tk.LabelFrame(
            charts_frame,
            text="🏆 Топ Исполнители",
            font=ModernStyles.FONTS['body'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        performers_frame.pack(fill='x', pady=(0, 10))

        # Create performers table
        performers_columns = ('Категория', 'Название', 'Показатель', 'Изменение')
        performers_tree = ttk.Treeview(performers_frame, columns=performers_columns, show='headings', height=6)

        for col in performers_columns:
            performers_tree.heading(col, text=col)
            performers_tree.column(col, width=150, anchor='center')

        # Sample data
        performers_data = [
            ("Блюдо", "Стейк Рибай", "156 продаж", "+23%"),
            ("Сотрудник", "Иванов А.П.", "98% KPI", "+5%"),
            ("Поставщик", "Мясной Двор", "4.8 рейтинг", "+0.2"),
            ("Категория", "Горячие блюда", "45% продаж", "+8%"),
            ("Смена", "Вечерняя", "380 руб чек", "+12%")
        ]

        for data in performers_data:
            performers_tree.insert('', 'end', values=data)

        performers_tree.pack(fill='x', padx=10, pady=10)

        # Action buttons
        actions_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_primary'])
        actions_frame.pack(fill='x', padx=20, pady=20)

        dashboard_buttons = [
            ("📊 Создать Отчет", self.create_new_report),
            ("📧 Отправить Сводку", self.send_executive_summary),
            ("📈 Детальная Аналитика", self.show_detailed_analytics),
            ("⚙️ Настроить Панель", self.configure_dashboard)
        ]

        for text, command in dashboard_buttons:
            btn = tk.Button(
                actions_frame,
                text=text,
                command=command,
                font=ModernStyles.FONTS['button'],
                bg=ModernStyles.COLORS['accent'],
                fg='white',
                relief='flat',
                padx=20,
                pady=8
            )
            btn.pack(side='left', padx=10)

    def create_metric_card(self, parent, title, value, change, color, index):
        """Create metric card"""
        card_frame = tk.Frame(parent, bg=color, relief='raised', bd=2)
        card_frame.grid(row=0, column=index, padx=10, pady=10, sticky='ew')

        parent.grid_columnconfigure(index, weight=1)

        # Title
        title_label = tk.Label(
            card_frame,
            text=title,
            font=ModernStyles.FONTS['small'],
            bg=color,
            fg='white'
        )
        title_label.pack(pady=(10, 5))

        # Value
        value_label = tk.Label(
            card_frame,
            text=value,
            font=ModernStyles.FONTS['heading'],
            bg=color,
            fg='white'
        )
        value_label.pack()

        # Change
        change_label = tk.Label(
            card_frame,
            text=change,
            font=ModernStyles.FONTS['small'],
            bg=color,
            fg='white'
        )
        change_label.pack(pady=(5, 10))

    def create_templates_tab(self):
        """Create report templates tab"""
        templates_frame = tk.Frame(self.notebook, bg=ModernStyles.COLORS['bg_primary'])
        self.notebook.add(templates_frame, text="📄 Шаблоны Отчетов")

        # Header
        header_frame = tk.Frame(templates_frame, bg=ModernStyles.COLORS['bg_primary'])
        header_frame.pack(fill='x', padx=20, pady=20)

        title_label = tk.Label(
            header_frame,
            text="📄 Шаблоны Отчетов",
            font=ModernStyles.FONTS['title'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        title_label.pack(side='left')

        # Template actions
        actions_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['bg_primary'])
        actions_frame.pack(side='right')

        template_buttons = [
            ("➕ Новый Шаблон", self.create_new_template),
            ("📥 Импорт", self.import_template),
            ("📤 Экспорт", self.export_template)
        ]

        for text, command in template_buttons:
            btn = tk.Button(
                actions_frame,
                text=text,
                command=command,
                font=ModernStyles.FONTS['button'],
                bg=ModernStyles.COLORS['accent'],
                fg='white',
                relief='flat',
                padx=15,
                pady=5
            )
            btn.pack(side='left', padx=5)

        # Templates list
        templates_list_frame = tk.Frame(templates_frame, bg=ModernStyles.COLORS['bg_primary'])
        templates_list_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        # Create templates table
        columns = ('Название', 'Категория', 'Описание', 'Частота', 'Формат', 'Разделы')
        self.templates_tree = ttk.Treeview(templates_list_frame, columns=columns, show='headings')

        for col in columns:
            self.templates_tree.heading(col, text=col)
            self.templates_tree.column(col, width=150, anchor='center')

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(templates_list_frame, orient="vertical", command=self.templates_tree.yview)
        h_scrollbar = ttk.Scrollbar(templates_list_frame, orient="horizontal", command=self.templates_tree.xview)
        self.templates_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack table and scrollbars
        self.templates_tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')

        # Context menu for templates
        self.templates_tree.bind("<Button-3>", self.show_template_context_menu)
        self.templates_tree.bind("<Double-1>", self.edit_template)

    def create_custom_builder_tab(self):
        """Create custom report builder tab"""
        builder_frame = tk.Frame(self.notebook, bg=ModernStyles.COLORS['bg_primary'])
        self.notebook.add(builder_frame, text="🔧 Конструктор Отчетов")

        # Create paned window for builder
        paned = ttk.PanedWindow(builder_frame, orient='horizontal')
        paned.pack(fill='both', expand=True, padx=20, pady=20)

        # Left panel - Configuration
        config_frame = tk.Frame(paned, bg=ModernStyles.COLORS['bg_secondary'], width=400)
        paned.add(config_frame, weight=1)

        # Right panel - Preview
        preview_frame = tk.Frame(paned, bg=ModernStyles.COLORS['bg_primary'])
        paned.add(preview_frame, weight=2)

        # Configuration panel
        self.create_builder_config_panel(config_frame)

        # Preview panel
        self.create_builder_preview_panel(preview_frame)

    def create_builder_config_panel(self, parent):
        """Create report builder configuration panel"""
        # Title
        title_label = tk.Label(
            parent,
            text="🔧 Конфигурация Отчета",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        title_label.pack(pady=20)

        # Report basic info
        basic_frame = tk.LabelFrame(
            parent,
            text="Основная Информация",
            font=ModernStyles.FONTS['body'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        basic_frame.pack(fill='x', padx=20, pady=10)

        # Report name
        tk.Label(basic_frame, text="Название отчета:", bg=ModernStyles.COLORS['bg_secondary']).pack(anchor='w', padx=10, pady=5)
        self.report_name_entry = tk.Entry(basic_frame, font=ModernStyles.FONTS['body'])
        self.report_name_entry.pack(fill='x', padx=10, pady=5)

        # Report description
        tk.Label(basic_frame, text="Описание:", bg=ModernStyles.COLORS['bg_secondary']).pack(anchor='w', padx=10, pady=5)
        self.report_desc_text = tk.Text(basic_frame, height=3, font=ModernStyles.FONTS['body'])
        self.report_desc_text.pack(fill='x', padx=10, pady=5)

        # Data sources
        sources_frame = tk.LabelFrame(
            parent,
            text="Источники Данных",
            font=ModernStyles.FONTS['body'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        sources_frame.pack(fill='x', padx=20, pady=10)

        # Data source checkboxes
        self.data_sources = {}
        sources = ["Продажи", "Склад", "Персонал", "Финансы", "Клиенты", "Поставщики"]

        for source in sources:
            var = tk.BooleanVar()
            cb = tk.Checkbutton(
                sources_frame,
                text=source,
                variable=var,
                bg=ModernStyles.COLORS['bg_secondary'],
                font=ModernStyles.FONTS['body']
            )
            cb.pack(anchor='w', padx=10, pady=2)
            self.data_sources[source] = var

        # Filters
        filters_frame = tk.LabelFrame(
            parent,
            text="Фильтры",
            font=ModernStyles.FONTS['body'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        filters_frame.pack(fill='x', padx=20, pady=10)

        # Date range
        tk.Label(filters_frame, text="Период:", bg=ModernStyles.COLORS['bg_secondary']).pack(anchor='w', padx=10, pady=5)
        self.period_var = tk.StringVar(value="Последний месяц")
        period_combo = ttk.Combobox(
            filters_frame,
            textvariable=self.period_var,
            values=["Сегодня", "Вчера", "Последняя неделя", "Последний месяц", "Последний квартал", "Последний год", "Произвольный период"]
        )
        period_combo.pack(fill='x', padx=10, pady=5)

        # Charts
        charts_frame = tk.LabelFrame(
            parent,
            text="Графики и Диаграммы",
            font=ModernStyles.FONTS['body'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        charts_frame.pack(fill='x', padx=20, pady=10)

        # Chart types
        self.chart_types = {}
        charts = ["Столбчатая", "Линейная", "Круговая", "Область", "Точечная"]

        for chart in charts:
            var = tk.BooleanVar()
            cb = tk.Checkbutton(
                charts_frame,
                text=chart,
                variable=var,
                bg=ModernStyles.COLORS['bg_secondary'],
                font=ModernStyles.FONTS['body']
            )
            cb.pack(anchor='w', padx=10, pady=2)
            self.chart_types[chart] = var

        # Action buttons
        actions_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_secondary'])
        actions_frame.pack(fill='x', padx=20, pady=20)

        builder_buttons = [
            ("👁️ Предпросмотр", self.preview_custom_report),
            ("💾 Сохранить", self.save_custom_report),
            ("🔄 Сброс", self.reset_builder)
        ]

        for text, command in builder_buttons:
            btn = tk.Button(
                actions_frame,
                text=text,
                command=command,
                font=ModernStyles.FONTS['button'],
                bg=ModernStyles.COLORS['accent'],
                fg='white',
                relief='flat',
                padx=15,
                pady=5
            )
            btn.pack(fill='x', pady=5)

    def create_builder_preview_panel(self, parent):
        """Create report builder preview panel"""
        # Title
        title_label = tk.Label(
            parent,
            text="👁️ Предпросмотр Отчета",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        title_label.pack(pady=20)

        # Preview area
        self.preview_frame = tk.Frame(parent, bg='white', relief='sunken', bd=2)
        self.preview_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Initial preview message
        preview_label = tk.Label(
            self.preview_frame,
            text="📊 Предпросмотр отчета появится здесь\nпосле настройки параметров",
            font=ModernStyles.FONTS['body'],
            bg='white',
            fg=ModernStyles.COLORS['text_secondary']
        )
        preview_label.pack(expand=True)

    def create_scheduled_reports_tab(self):
        """Create scheduled reports tab"""
        scheduled_frame = tk.Frame(self.notebook, bg=ModernStyles.COLORS['bg_primary'])
        self.notebook.add(scheduled_frame, text="⏰ Расписание Отчетов")

        # Header
        header_frame = tk.Frame(scheduled_frame, bg=ModernStyles.COLORS['bg_primary'])
        header_frame.pack(fill='x', padx=20, pady=20)

        title_label = tk.Label(
            header_frame,
            text="⏰ Автоматические Отчеты",
            font=ModernStyles.FONTS['title'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        title_label.pack(side='left')

        # Schedule actions
        actions_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['bg_primary'])
        actions_frame.pack(side='right')

        schedule_buttons = [
            ("➕ Новое Расписание", self.create_new_schedule),
            ("▶️ Запустить Сейчас", self.run_scheduled_report),
            ("⏸️ Приостановить", self.pause_schedule)
        ]

        for text, command in schedule_buttons:
            btn = tk.Button(
                actions_frame,
                text=text,
                command=command,
                font=ModernStyles.FONTS['button'],
                bg=ModernStyles.COLORS['accent'],
                fg='white',
                relief='flat',
                padx=15,
                pady=5
            )
            btn.pack(side='left', padx=5)

        # Scheduled reports list
        scheduled_list_frame = tk.Frame(scheduled_frame, bg=ModernStyles.COLORS['bg_primary'])
        scheduled_list_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        # Create scheduled reports table
        columns = ('Название', 'Шаблон', 'Расписание', 'Получатели', 'Статус', 'Последний запуск', 'Следующий запуск')
        self.scheduled_tree = ttk.Treeview(scheduled_list_frame, columns=columns, show='headings')

        for col in columns:
            self.scheduled_tree.heading(col, text=col)
            self.scheduled_tree.column(col, width=120, anchor='center')

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(scheduled_list_frame, orient="vertical", command=self.scheduled_tree.yview)
        h_scrollbar = ttk.Scrollbar(scheduled_list_frame, orient="horizontal", command=self.scheduled_tree.xview)
        self.scheduled_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack table and scrollbars
        self.scheduled_tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')

        # Context menu for scheduled reports
        self.scheduled_tree.bind("<Button-3>", self.show_schedule_context_menu)
        self.scheduled_tree.bind("<Double-1>", self.edit_schedule)

    def create_report_history_tab(self):
        """Create report history tab"""
        history_frame = tk.Frame(self.notebook, bg=ModernStyles.COLORS['bg_primary'])
        self.notebook.add(history_frame, text="📚 История Отчетов")

        # Header with search
        header_frame = tk.Frame(history_frame, bg=ModernStyles.COLORS['bg_primary'])
        header_frame.pack(fill='x', padx=20, pady=20)

        title_label = tk.Label(
            header_frame,
            text="📚 История Отчетов",
            font=ModernStyles.FONTS['title'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        title_label.pack(side='left')

        # Search frame
        search_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['bg_primary'])
        search_frame.pack(side='right')

        tk.Label(search_frame, text="Поиск:", bg=ModernStyles.COLORS['bg_primary']).pack(side='left', padx=5)
        self.search_entry = tk.Entry(search_frame, font=ModernStyles.FONTS['body'])
        self.search_entry.pack(side='left', padx=5)

        search_btn = tk.Button(
            search_frame,
            text="🔍",
            command=self.search_reports,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['accent'],
            fg='white',
            relief='flat',
            padx=10
        )
        search_btn.pack(side='left', padx=5)

        # History table
        history_list_frame = tk.Frame(history_frame, bg=ModernStyles.COLORS['bg_primary'])
        history_list_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        columns = ('Дата', 'Название', 'Тип', 'Создал', 'Размер', 'Формат', 'Статус')
        self.history_tree = ttk.Treeview(history_list_frame, columns=columns, show='headings')

        for col in columns:
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=120, anchor='center')

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(history_list_frame, orient="vertical", command=self.history_tree.yview)
        h_scrollbar = ttk.Scrollbar(history_list_frame, orient="horizontal", command=self.history_tree.xview)
        self.history_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack table and scrollbars
        self.history_tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')

        # Context menu
        self.history_tree.bind("<Button-3>", self.show_history_context_menu)
        self.history_tree.bind("<Double-1>", self.open_report)

    def create_analytics_tab(self):
        """Create analytics tab"""
        analytics_frame = tk.Frame(self.notebook, bg=ModernStyles.COLORS['bg_primary'])
        self.notebook.add(analytics_frame, text="📈 Аналитика Отчетов")

        # Analytics content
        title_label = tk.Label(
            analytics_frame,
            text="📈 Аналитика Использования Отчетов",
            font=ModernStyles.FONTS['title'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        title_label.pack(pady=20)

        # Statistics cards
        stats_frame = tk.Frame(analytics_frame, bg=ModernStyles.COLORS['bg_primary'])
        stats_frame.pack(fill='x', padx=20, pady=10)

        analytics_stats = [
            ("📊 Всего отчетов", "1,247", "За все время"),
            ("📅 За месяц", "89", "+15% к прошлому"),
            ("👥 Активных пользователей", "23", "Создают отчеты"),
            ("⏰ Автоматических", "12", "По расписанию")
        ]

        for i, (title, value, subtitle) in enumerate(analytics_stats):
            self.create_analytics_card(stats_frame, title, value, subtitle, i)

        # Most popular reports
        popular_frame = tk.LabelFrame(
            analytics_frame,
            text="🏆 Популярные Отчеты",
            font=ModernStyles.FONTS['body'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        popular_frame.pack(fill='x', padx=20, pady=20)

        popular_data = [
            ("Ежедневная Сводка", "156 запусков", "Каждый день"),
            ("Финансовый Отчет", "89 запусков", "Еженедельно"),
            ("Анализ Продаж", "67 запусков", "По требованию"),
            ("Отчет по Складу", "45 запусков", "Еженедельно")
        ]

        for name, usage, frequency in popular_data:
            item_frame = tk.Frame(popular_frame, bg=ModernStyles.COLORS['bg_secondary'])
            item_frame.pack(fill='x', padx=10, pady=5)

            tk.Label(item_frame, text=name, font=ModernStyles.FONTS['body'],
                    bg=ModernStyles.COLORS['bg_secondary']).pack(side='left')
            tk.Label(item_frame, text=f"{usage} | {frequency}", font=ModernStyles.FONTS['small'],
                    bg=ModernStyles.COLORS['bg_secondary'], fg=ModernStyles.COLORS['text_secondary']).pack(side='right')

    def create_analytics_card(self, parent, title, value, subtitle, index):
        """Create analytics card"""
        card_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_secondary'], relief='raised', bd=2)
        card_frame.grid(row=0, column=index, padx=10, pady=10, sticky='ew')

        parent.grid_columnconfigure(index, weight=1)

        # Title
        title_label = tk.Label(
            card_frame,
            text=title,
            font=ModernStyles.FONTS['small'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        title_label.pack(pady=(10, 5))

        # Value
        value_label = tk.Label(
            card_frame,
            text=value,
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['primary']
        )
        value_label.pack()

        # Subtitle
        subtitle_label = tk.Label(
            card_frame,
            text=subtitle,
            font=ModernStyles.FONTS['small'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_secondary']
        )
        subtitle_label.pack(pady=(5, 10))

    def refresh_all_displays(self):
        """Refresh all data displays"""
        self.refresh_templates_display()
        self.refresh_scheduled_display()
        self.refresh_history_display()

    def refresh_templates_display(self):
        """Refresh templates display"""
        # Clear existing items
        for item in self.templates_tree.get_children():
            self.templates_tree.delete(item)

        # Add templates
        for template in self.report_templates:
            sections_text = ", ".join(template['sections'])
            self.templates_tree.insert('', 'end', values=(
                template['name'],
                template['category'],
                template['description'],
                template['frequency'],
                template['format'],
                sections_text
            ))

    def refresh_scheduled_display(self):
        """Refresh scheduled reports display"""
        # Clear existing items
        for item in self.scheduled_tree.get_children():
            self.scheduled_tree.delete(item)

        # Add scheduled reports
        for report in self.scheduled_reports:
            recipients_text = ", ".join(report['recipients'][:2])  # Show first 2 recipients
            if len(report['recipients']) > 2:
                recipients_text += f" (+{len(report['recipients']) - 2})"

            self.scheduled_tree.insert('', 'end', values=(
                report['name'],
                report['template_id'],
                report['schedule'],
                recipients_text,
                report['status'],
                report['last_run'],
                report['next_run']
            ))

    def refresh_history_display(self):
        """Refresh history display"""
        # Clear existing items
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)

        # Sample history data
        history_data = [
            ("20.01.2024 09:00", "Ежедневная Сводка", "Исполнительная", "Система", "2.1 MB", "PDF", "Готов"),
            ("19.01.2024 18:30", "Анализ Продаж", "Продажи", "Менеджер", "1.8 MB", "Excel", "Готов"),
            ("19.01.2024 10:00", "Финансовый Отчет", "Финансы", "Бухгалтер", "3.2 MB", "PDF", "Готов"),
            ("18.01.2024 16:45", "Отчет по Складу", "Склад", "Кладовщик", "1.5 MB", "Excel", "Готов")
        ]

        for data in history_data:
            self.history_tree.insert('', 'end', values=data)

    # Action methods
    def refresh_all_data(self):
        """Refresh all data"""
        self.status_label.config(text="Обновление данных...")
        self.load_data()
        self.refresh_all_displays()
        self.status_label.config(text="Данные обновлены")

    def open_settings(self):
        """Open settings dialog"""
        messagebox.showinfo("Настройки", "Настройки системы отчетности будут доступны в следующей версии")

    def show_help(self):
        """Show help dialog"""
        help_text = """
📊 Профессиональная Система Отчетности

Основные возможности:
• Исполнительная панель с ключевыми показателями
• Готовые шаблоны отчетов для всех областей бизнеса
• Конструктор пользовательских отчетов
• Автоматическое создание отчетов по расписанию
• История всех созданных отчетов
• Аналитика использования системы отчетности

Для создания нового отчета:
1. Выберите готовый шаблон или используйте конструктор
2. Настройте параметры и фильтры
3. Выберите формат экспорта
4. Создайте отчет или настройте автоматическое создание

Поддерживаемые форматы: PDF, Excel, Word, PowerPoint
        """
        messagebox.showinfo("Справка", help_text)

    def create_new_report(self):
        """Create new report"""
        messagebox.showinfo("Создание Отчета", "Переход к конструктору отчетов...")
        self.notebook.select(2)  # Switch to custom builder tab

    def send_executive_summary(self):
        """Send executive summary"""
        messagebox.showinfo("Отправка Сводки", "📧 Исполнительная сводка отправлена руководству")

    def show_detailed_analytics(self):
        """Show detailed analytics"""
        messagebox.showinfo("Детальная Аналитика", "Переход к расширенной аналитике...")
        self.notebook.select(5)  # Switch to analytics tab

    def configure_dashboard(self):
        """Configure dashboard"""
        messagebox.showinfo("Настройка Панели", "⚙️ Настройка исполнительной панели будет доступна в следующей версии")

    def create_new_template(self):
        """Create new template"""
        messagebox.showinfo("Новый Шаблон", "📄 Создание нового шаблона отчета будет доступно в следующей версии")

    def import_template(self):
        """Import template"""
        file_path = filedialog.askopenfilename(
            title="Импорт Шаблона",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if file_path:
            messagebox.showinfo("Импорт", f"📥 Шаблон импортирован: {file_path}")

    def export_template(self):
        """Export template"""
        selection = self.templates_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите шаблон для экспорта")
            return

        file_path = filedialog.asksaveasfilename(
            title="Экспорт Шаблона",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if file_path:
            messagebox.showinfo("Экспорт", f"📤 Шаблон экспортирован: {file_path}")

    def show_template_context_menu(self, event):
        """Show template context menu"""
        item = self.templates_tree.identify_row(event.y)
        if item:
            self.templates_tree.selection_set(item)
            context_menu = tk.Menu(self.window, tearoff=0)
            context_menu.add_command(label="✏️ Редактировать", command=self.edit_template)
            context_menu.add_command(label="📋 Копировать", command=self.copy_template)
            context_menu.add_command(label="🗑️ Удалить", command=self.delete_template)
            context_menu.add_separator()
            context_menu.add_command(label="▶️ Создать Отчет", command=self.create_report_from_template)
            context_menu.post(event.x_root, event.y_root)

    def edit_template(self, event=None):
        """Edit selected template"""
        selection = self.templates_tree.selection()
        if selection:
            messagebox.showinfo("Редактирование", "✏️ Редактирование шаблона будет доступно в следующей версии")

    def copy_template(self):
        """Copy selected template"""
        messagebox.showinfo("Копирование", "📋 Шаблон скопирован")

    def delete_template(self):
        """Delete selected template"""
        if messagebox.askyesno("Подтверждение", "Удалить выбранный шаблон?"):
            messagebox.showinfo("Удаление", "🗑️ Шаблон удален")

    def create_report_from_template(self):
        """Create report from selected template"""
        messagebox.showinfo("Создание Отчета", "▶️ Создание отчета по шаблону...")

    def preview_custom_report(self):
        """Preview custom report"""
        # Clear preview frame
        for widget in self.preview_frame.winfo_children():
            widget.destroy()

        # Create preview content
        preview_title = tk.Label(
            self.preview_frame,
            text=f"📊 {self.report_name_entry.get() or 'Новый Отчет'}",
            font=ModernStyles.FONTS['heading'],
            bg='white'
        )
        preview_title.pack(pady=20)

        # Show selected data sources
        selected_sources = [source for source, var in self.data_sources.items() if var.get()]
        if selected_sources:
            sources_label = tk.Label(
                self.preview_frame,
                text=f"Источники данных: {', '.join(selected_sources)}",
                font=ModernStyles.FONTS['body'],
                bg='white'
            )
            sources_label.pack(pady=10)

        # Show period
        period_label = tk.Label(
            self.preview_frame,
            text=f"Период: {self.period_var.get()}",
            font=ModernStyles.FONTS['body'],
            bg='white'
        )
        period_label.pack(pady=5)

        # Show selected charts
        selected_charts = [chart for chart, var in self.chart_types.items() if var.get()]
        if selected_charts:
            charts_label = tk.Label(
                self.preview_frame,
                text=f"Графики: {', '.join(selected_charts)}",
                font=ModernStyles.FONTS['body'],
                bg='white'
            )
            charts_label.pack(pady=10)

        # Sample data table
        sample_frame = tk.Frame(self.preview_frame, bg='white')
        sample_frame.pack(fill='both', expand=True, padx=20, pady=20)

        tk.Label(sample_frame, text="📊 Пример данных отчета:",
                font=ModernStyles.FONTS['body'], bg='white').pack(anchor='w')

        # Create sample table
        columns = ('Показатель', 'Значение', 'Изменение')
        sample_tree = ttk.Treeview(sample_frame, columns=columns, show='headings', height=6)

        for col in columns:
            sample_tree.heading(col, text=col)
            sample_tree.column(col, width=150, anchor='center')

        sample_data = [
            ("Выручка", "1,568,400 руб", "+8.2%"),
            ("Заказы", "4,127", "+12.5%"),
            ("Средний чек", "380 руб", "+3.1%"),
            ("Прибыль", "167,360 руб", "+15%")
        ]

        for data in sample_data:
            sample_tree.insert('', 'end', values=data)

        sample_tree.pack(fill='x', pady=10)

    def save_custom_report(self):
        """Save custom report"""
        report_name = self.report_name_entry.get()
        if not report_name:
            messagebox.showwarning("Предупреждение", "Введите название отчета")
            return

        # Create new custom report
        new_report = {
            "id": f"custom_{len(self.custom_reports) + 1}",
            "name": report_name,
            "description": self.report_desc_text.get("1.0", tk.END).strip(),
            "created_by": "Пользователь",
            "created_date": datetime.now().strftime("%Y-%m-%d"),
            "data_sources": [source for source, var in self.data_sources.items() if var.get()],
            "filters": {"period": self.period_var.get()},
            "charts": [chart for chart, var in self.chart_types.items() if var.get()],
            "status": "Готов"
        }

        self.custom_reports.append(new_report)
        messagebox.showinfo("Сохранение", f"💾 Пользовательский отчет '{report_name}' сохранен")

    def reset_builder(self):
        """Reset report builder"""
        self.report_name_entry.delete(0, tk.END)
        self.report_desc_text.delete("1.0", tk.END)

        for var in self.data_sources.values():
            var.set(False)

        for var in self.chart_types.values():
            var.set(False)

        self.period_var.set("Последний месяц")

        # Clear preview
        for widget in self.preview_frame.winfo_children():
            widget.destroy()

        preview_label = tk.Label(
            self.preview_frame,
            text="📊 Предпросмотр отчета появится здесь\nпосле настройки параметров",
            font=ModernStyles.FONTS['body'],
            bg='white',
            fg=ModernStyles.COLORS['text_secondary']
        )
        preview_label.pack(expand=True)

    def create_new_schedule(self):
        """Create new schedule"""
        messagebox.showinfo("Новое Расписание", "⏰ Создание нового расписания будет доступно в следующей версии")

    def run_scheduled_report(self):
        """Run scheduled report now"""
        selection = self.scheduled_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите отчет для запуска")
            return

        messagebox.showinfo("Запуск Отчета", "▶️ Отчет запущен вне расписания")

    def pause_schedule(self):
        """Pause schedule"""
        selection = self.scheduled_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите расписание для приостановки")
            return

        messagebox.showinfo("Приостановка", "⏸️ Расписание приостановлено")

    def show_schedule_context_menu(self, event):
        """Show schedule context menu"""
        item = self.scheduled_tree.identify_row(event.y)
        if item:
            self.scheduled_tree.selection_set(item)
            context_menu = tk.Menu(self.window, tearoff=0)
            context_menu.add_command(label="✏️ Редактировать", command=self.edit_schedule)
            context_menu.add_command(label="▶️ Запустить", command=self.run_scheduled_report)
            context_menu.add_command(label="⏸️ Приостановить", command=self.pause_schedule)
            context_menu.add_separator()
            context_menu.add_command(label="🗑️ Удалить", command=self.delete_schedule)
            context_menu.post(event.x_root, event.y_root)

    def edit_schedule(self, event=None):
        """Edit schedule"""
        messagebox.showinfo("Редактирование", "✏️ Редактирование расписания будет доступно в следующей версии")

    def delete_schedule(self):
        """Delete schedule"""
        if messagebox.askyesno("Подтверждение", "Удалить выбранное расписание?"):
            messagebox.showinfo("Удаление", "🗑️ Расписание удалено")

    def search_reports(self):
        """Search reports"""
        search_term = self.search_entry.get()
        if search_term:
            messagebox.showinfo("Поиск", f"🔍 Поиск отчетов по запросу: '{search_term}'")
        else:
            messagebox.showwarning("Предупреждение", "Введите поисковый запрос")

    def show_history_context_menu(self, event):
        """Show history context menu"""
        item = self.history_tree.identify_row(event.y)
        if item:
            self.history_tree.selection_set(item)
            context_menu = tk.Menu(self.window, tearoff=0)
            context_menu.add_command(label="👁️ Открыть", command=self.open_report)
            context_menu.add_command(label="📤 Экспорт", command=self.export_report)
            context_menu.add_command(label="📧 Отправить", command=self.email_report)
            context_menu.add_separator()
            context_menu.add_command(label="🗑️ Удалить", command=self.delete_report)
            context_menu.post(event.x_root, event.y_root)

    def open_report(self, event=None):
        """Open selected report"""
        selection = self.history_tree.selection()
        if selection:
            messagebox.showinfo("Открытие Отчета", "👁️ Отчет открыт для просмотра")

    def export_report(self):
        """Export selected report"""
        selection = self.history_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите отчет для экспорта")
            return

        file_path = filedialog.asksaveasfilename(
            title="Экспорт Отчета",
            defaultextension=".pdf",
            filetypes=[("PDF files", "*.pdf"), ("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if file_path:
            messagebox.showinfo("Экспорт", f"📤 Отчет экспортирован: {file_path}")

    def email_report(self):
        """Email selected report"""
        messagebox.showinfo("Отправка Email", "📧 Отчет отправлен по электронной почте")

    def delete_report(self):
        """Delete selected report"""
        if messagebox.askyesno("Подтверждение", "Удалить выбранный отчет из истории?"):
            messagebox.showinfo("Удаление", "🗑️ Отчет удален из истории")


def show_professional_reporting(parent, db_manager):
    """Show professional reporting system"""
    try:
        reporting_system = ProfessionalReportingSystem(parent, db_manager)
        log_info("Professional reporting system opened successfully")
        return reporting_system
    except Exception as e:
        handle_module_error(e, "Профессиональная система отчетности", "открытие системы")
        return None


if __name__ == "__main__":
    # Test the module
    root = tk.Tk()
    root.withdraw()  # Hide main window

    # Mock database manager
    class MockDBManager:
        pass

    db_manager = MockDBManager()
    show_professional_reporting(root, db_manager)
    root.mainloop()
