"""
Financial Planning and Budgeting Module
Build comprehensive financial planning tools with budget creation, variance analysis,
cash flow forecasting, profitability analysis, and financial goal tracking.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from datetime import datetime, timedelta
import json
import uuid
import random
from gui.styles import ModernStyles

class FinancialPlanningBudgeting:
    """Financial Planning and Budgeting System"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.notebook = None
        
        # Budget categories
        self.budget_categories = {
            "revenue": {"name": "Доходы", "icon": "💰", "color": ModernStyles.COLORS['success']},
            "cogs": {"name": "Себестоимость", "icon": "🏭", "color": ModernStyles.COLORS['warning']},
            "labor": {"name": "Трудозатраты", "icon": "👥", "color": ModernStyles.COLORS['info']},
            "overhead": {"name": "Накладные расходы", "icon": "🏢", "color": ModernStyles.COLORS['primary']},
            "marketing": {"name": "Маркетинг", "icon": "📢", "color": ModernStyles.COLORS['secondary']},
            "utilities": {"name": "Коммунальные услуги", "icon": "⚡", "color": ModernStyles.COLORS['danger']},
            "maintenance": {"name": "Обслуживание", "icon": "🔧", "color": ModernStyles.COLORS['info']},
            "other": {"name": "Прочие расходы", "icon": "📋", "color": ModernStyles.COLORS['secondary']}
        }
        
        # Budget periods
        self.budget_periods = {
            "monthly": {"name": "Месячный", "months": 1},
            "quarterly": {"name": "Квартальный", "months": 3},
            "annual": {"name": "Годовой", "months": 12}
        }
        
        # Variance analysis thresholds
        self.variance_thresholds = {
            "excellent": {"min": -5, "max": 5, "color": ModernStyles.COLORS['success']},
            "good": {"min": -10, "max": 10, "color": ModernStyles.COLORS['info']},
            "warning": {"min": -20, "max": 20, "color": ModernStyles.COLORS['warning']},
            "critical": {"min": -100, "max": 100, "color": ModernStyles.COLORS['danger']}
        }
        
        # Financial goals types
        self.goal_types = {
            "revenue": {"name": "Увеличение выручки", "icon": "📈"},
            "profit": {"name": "Увеличение прибыли", "icon": "💎"},
            "cost_reduction": {"name": "Снижение затрат", "icon": "📉"},
            "efficiency": {"name": "Повышение эффективности", "icon": "⚡"},
            "expansion": {"name": "Расширение бизнеса", "icon": "🚀"},
            "sustainability": {"name": "Устойчивое развитие", "icon": "🌱"}
        }
        
        # Cash flow categories
        self.cash_flow_categories = {
            "operating": {"name": "Операционная деятельность", "color": ModernStyles.COLORS['primary']},
            "investing": {"name": "Инвестиционная деятельность", "color": ModernStyles.COLORS['info']},
            "financing": {"name": "Финансовая деятельность", "color": ModernStyles.COLORS['warning']}
        }
        
        # Initialize financial planning database tables
        self._init_financial_planning_tables()
    
    def _init_financial_planning_tables(self):
        """Initialize financial planning database tables"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Budget Plans
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS budget_plans (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        budget_name TEXT NOT NULL,
                        budget_period TEXT NOT NULL,
                        start_date DATE NOT NULL,
                        end_date DATE NOT NULL,
                        total_revenue DECIMAL(15,2) DEFAULT 0,
                        total_expenses DECIMAL(15,2) DEFAULT 0,
                        net_profit DECIMAL(15,2) DEFAULT 0,
                        status TEXT DEFAULT 'draft',
                        created_by INTEGER,
                        approved_by INTEGER,
                        approved_at TIMESTAMP,
                        notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (created_by) REFERENCES users (id),
                        FOREIGN KEY (approved_by) REFERENCES users (id)
                    )
                ''')
                
                # Budget Line Items
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS budget_line_items (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        budget_id INTEGER NOT NULL,
                        category TEXT NOT NULL,
                        subcategory TEXT,
                        line_item_name TEXT NOT NULL,
                        budgeted_amount DECIMAL(15,2) NOT NULL,
                        actual_amount DECIMAL(15,2) DEFAULT 0,
                        variance_amount DECIMAL(15,2) DEFAULT 0,
                        variance_percentage DECIMAL(5,2) DEFAULT 0,
                        is_revenue BOOLEAN DEFAULT 0,
                        notes TEXT,
                        FOREIGN KEY (budget_id) REFERENCES budget_plans (id)
                    )
                ''')
                
                # Cash Flow Forecasts
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS cash_flow_forecasts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        forecast_name TEXT NOT NULL,
                        forecast_period_start DATE NOT NULL,
                        forecast_period_end DATE NOT NULL,
                        opening_balance DECIMAL(15,2) DEFAULT 0,
                        closing_balance DECIMAL(15,2) DEFAULT 0,
                        total_inflows DECIMAL(15,2) DEFAULT 0,
                        total_outflows DECIMAL(15,2) DEFAULT 0,
                        net_cash_flow DECIMAL(15,2) DEFAULT 0,
                        created_by INTEGER,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (created_by) REFERENCES users (id)
                    )
                ''')
                
                # Cash Flow Items
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS cash_flow_items (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        forecast_id INTEGER NOT NULL,
                        item_date DATE NOT NULL,
                        category TEXT NOT NULL,
                        description TEXT NOT NULL,
                        amount DECIMAL(15,2) NOT NULL,
                        is_inflow BOOLEAN DEFAULT 1,
                        probability DECIMAL(3,2) DEFAULT 1.0,
                        notes TEXT,
                        FOREIGN KEY (forecast_id) REFERENCES cash_flow_forecasts (id)
                    )
                ''')
                
                # Financial Goals
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS financial_goals (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        goal_name TEXT NOT NULL,
                        goal_type TEXT NOT NULL,
                        target_value DECIMAL(15,2) NOT NULL,
                        current_value DECIMAL(15,2) DEFAULT 0,
                        target_date DATE NOT NULL,
                        status TEXT DEFAULT 'active',
                        progress_percentage DECIMAL(5,2) DEFAULT 0,
                        description TEXT,
                        success_criteria TEXT,
                        assigned_to INTEGER,
                        created_by INTEGER,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (assigned_to) REFERENCES users (id),
                        FOREIGN KEY (created_by) REFERENCES users (id)
                    )
                ''')
                
                # Profitability Analysis
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS profitability_analysis (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        analysis_name TEXT NOT NULL,
                        analysis_period_start DATE NOT NULL,
                        analysis_period_end DATE NOT NULL,
                        total_revenue DECIMAL(15,2) DEFAULT 0,
                        gross_profit DECIMAL(15,2) DEFAULT 0,
                        operating_profit DECIMAL(15,2) DEFAULT 0,
                        net_profit DECIMAL(15,2) DEFAULT 0,
                        gross_margin DECIMAL(5,2) DEFAULT 0,
                        operating_margin DECIMAL(5,2) DEFAULT 0,
                        net_margin DECIMAL(5,2) DEFAULT 0,
                        roi DECIMAL(5,2) DEFAULT 0,
                        created_by INTEGER,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (created_by) REFERENCES users (id)
                    )
                ''')
                
                conn.commit()
                print("Financial planning database tables initialized successfully")
                
        except Exception as e:
            print(f"Error initializing financial planning tables: {e}")
    
    def show_financial_planning_system(self):
        """Show financial planning and budgeting window"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
            
        self.create_window()
        self.create_interface()
        self.load_financial_data()
    
    def create_window(self):
        """Create the financial planning system window"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("💰 Система Финансового Планирования и Бюджетирования")
        self.window.geometry("1600x1000")
        self.window.configure(bg=ModernStyles.COLORS['bg_main'])
        self.window.resizable(True, True)
        self.window.state('zoomed')
        
        # Center window
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # Configure styles
        ModernStyles.configure_ttk_styles(self.window)
    
    def create_interface(self):
        """Create the financial planning system interface"""
        # Main container
        main_container = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        main_container.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Header with financial metrics
        header_frame = tk.Frame(main_container, bg=ModernStyles.COLORS['bg_main'])
        header_frame.pack(fill='x', pady=(0, 20))
        
        title_label = tk.Label(header_frame, text="💰 Система Финансового Планирования и Бюджетирования",
                              font=('Cambria', 24, 'bold italic'),
                              fg=ModernStyles.COLORS['text_primary'],
                              bg=ModernStyles.COLORS['bg_main'])
        title_label.pack(side='left')
        
        # Financial metrics indicators
        self.metrics_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['bg_main'])
        self.metrics_frame.pack(side='right')
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_container)
        self.notebook.pack(fill='both', expand=True)
        
        # Create tabs
        self.create_budget_planning_tab()
        self.create_variance_analysis_tab()
        self.create_cash_flow_forecasting_tab()
        self.create_profitability_analysis_tab()
        self.create_financial_goals_tab()
        self.create_reports_dashboard_tab()

    def create_budget_planning_tab(self):
        """Create budget planning tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="📊 Планирование Бюджета")

        # Budget planning controls
        budget_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        budget_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(budget_controls_frame, text="➕ Создать Бюджет",
                 command=self.create_budget,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(budget_controls_frame, text="✏️ Редактировать",
                 command=self.edit_budget,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(budget_controls_frame, text="✅ Утвердить",
                 command=self.approve_budget,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(budget_controls_frame, text="📋 Копировать",
                 command=self.copy_budget,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Budget period filter
        tk.Label(budget_controls_frame, text="Период:",
                font=('Cambria', 12, 'bold'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_secondary']).pack(side='left', padx=(20, 5))

        self.budget_period_filter = ttk.Combobox(budget_controls_frame, font=('Cambria', 11), width=12, state='readonly')
        self.budget_period_filter['values'] = ['Все', 'Месячный', 'Квартальный', 'Годовой']
        self.budget_period_filter.set('Все')
        self.budget_period_filter.pack(side='left', padx=5)

        # Budget display
        budget_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        budget_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Budget treeview
        budget_columns = ('budget_name', 'period', 'start_date', 'end_date', 'total_revenue', 'total_expenses', 'net_profit', 'status')
        self.budget_tree = ttk.Treeview(budget_frame, columns=budget_columns, show='headings', height=12)

        # Configure budget columns
        self.budget_tree.heading('budget_name', text='Название Бюджета')
        self.budget_tree.heading('period', text='Период')
        self.budget_tree.heading('start_date', text='Начало')
        self.budget_tree.heading('end_date', text='Конец')
        self.budget_tree.heading('total_revenue', text='Доходы')
        self.budget_tree.heading('total_expenses', text='Расходы')
        self.budget_tree.heading('net_profit', text='Чистая Прибыль')
        self.budget_tree.heading('status', text='Статус')

        self.budget_tree.column('budget_name', width=200)
        self.budget_tree.column('period', width=120)
        self.budget_tree.column('start_date', width=100)
        self.budget_tree.column('end_date', width=100)
        self.budget_tree.column('total_revenue', width=120)
        self.budget_tree.column('total_expenses', width=120)
        self.budget_tree.column('net_profit', width=120)
        self.budget_tree.column('status', width=100)

        # Budget scrollbar
        budget_scrollbar = ttk.Scrollbar(budget_frame, orient='vertical', command=self.budget_tree.yview)
        self.budget_tree.configure(yscrollcommand=budget_scrollbar.set)

        self.budget_tree.pack(side='left', fill='both', expand=True)
        budget_scrollbar.pack(side='right', fill='y')

    def create_variance_analysis_tab(self):
        """Create variance analysis tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="📈 Анализ Отклонений")

        # Variance analysis controls
        variance_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        variance_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(variance_controls_frame, text="🔍 Анализировать",
                 command=self.analyze_variance,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(variance_controls_frame, text="📊 Детальный Отчет",
                 command=self.detailed_variance_report,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(variance_controls_frame, text="⚠️ Критические Отклонения",
                 command=self.show_critical_variances,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Variance analysis display
        variance_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        variance_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Variance analysis text display
        variance_text = tk.Text(variance_frame, font=('Cambria', 11),
                              bg=ModernStyles.COLORS['bg_main'],
                              fg=ModernStyles.COLORS['text_primary'],
                              wrap='word', state='disabled')
        variance_text.pack(fill='both', expand=True, padx=15, pady=15)

        # Sample variance analysis data
        variance_data = """📈 АНАЛИЗ ОТКЛОНЕНИЙ БЮДЖЕТА (ИЮНЬ 2024)

💰 ДОХОДЫ:
• Продажи блюд: План 2 500 000 руб | Факт 2 680 000 руб | Отклонение +180 000 руб (+7.2%) ✅
• Продажи напитков: План 800 000 руб | Факт 750 000 руб | Отклонение -50 000 руб (-6.3%) ⚠️
• Доставка: План 300 000 руб | Факт 420 000 руб | Отклонение +120 000 руб (+40.0%) ✅
• Банкеты: План 500 000 руб | Факт 380 000 руб | Отклонение -120 000 руб (-24.0%) ❌

ИТОГО ДОХОДЫ: План 4 100 000 руб | Факт 4 230 000 руб | Отклонение +130 000 руб (+3.2%) ✅

💸 РАСХОДЫ:

🥘 СЕБЕСТОИМОСТЬ:
• Продукты питания: План 1 200 000 руб | Факт 1 280 000 руб | Отклонение +80 000 руб (+6.7%) ⚠️
• Напитки: План 240 000 руб | Факт 225 000 руб | Отклонение -15 000 руб (-6.3%) ✅
• Упаковка доставки: План 45 000 руб | Факт 63 000 руб | Отклонение +18 000 руб (+40.0%) ❌

👥 ТРУДОЗАТРАТЫ:
• Зарплата поваров: План 450 000 руб | Факт 465 000 руб | Отклонение +15 000 руб (+3.3%) ✅
• Зарплата официантов: План 320 000 руб | Факт 340 000 руб | Отклонение +20 000 руб (+6.3%) ⚠️
• Социальные взносы: План 231 000 руб | Факт 241 500 руб | Отклонение +10 500 руб (+4.5%) ✅

🏢 НАКЛАДНЫЕ РАСХОДЫ:
• Аренда помещения: План 180 000 руб | Факт 180 000 руб | Отклонение 0 руб (0.0%) ✅
• Коммунальные услуги: План 85 000 руб | Факт 92 000 руб | Отклонение +7 000 руб (+8.2%) ⚠️
• Реклама и маркетинг: План 120 000 руб | Факт 95 000 руб | Отклонение -25 000 руб (-20.8%) ✅

ИТОГО РАСХОДЫ: План 2 871 000 руб | Факт 2 981 500 руб | Отклонение +110 500 руб (+3.8%) ⚠️

💎 ЧИСТАЯ ПРИБЫЛЬ:
План: 1 229 000 руб | Факт: 1 248 500 руб | Отклонение +19 500 руб (+1.6%) ✅

📊 КЛЮЧЕВЫЕ ВЫВОДЫ:
✅ Превышение плана по общим доходам на 3.2%
✅ Значительный рост доходов от доставки (+40%)
⚠️ Снижение продаж напитков требует внимания
❌ Критическое снижение доходов от банкетов (-24%)
⚠️ Превышение расходов на продукты питания (+6.7%)
✅ Экономия на рекламе без потери эффективности

🎯 РЕКОМЕНДАЦИИ:
• Усилить маркетинг банкетных услуг
• Пересмотреть меню напитков и ценообразование
• Оптимизировать закупки продуктов питания
• Продолжить развитие службы доставки
• Контролировать рост коммунальных расходов
"""

        variance_text.config(state='normal')
        variance_text.insert('1.0', variance_data)
        variance_text.config(state='disabled')

    def create_cash_flow_forecasting_tab(self):
        """Create cash flow forecasting tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="💸 Прогноз Денежных Потоков")

        # Cash flow controls
        cf_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        cf_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(cf_controls_frame, text="➕ Создать Прогноз",
                 command=self.create_cash_flow_forecast,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(cf_controls_frame, text="🔄 Обновить Прогноз",
                 command=self.update_cash_flow_forecast,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(cf_controls_frame, text="📊 Сценарный Анализ",
                 command=self.scenario_analysis,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Cash flow display
        cf_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        cf_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Cash flow text display
        cf_text = tk.Text(cf_frame, font=('Cambria', 11),
                         bg=ModernStyles.COLORS['bg_main'],
                         fg=ModernStyles.COLORS['text_primary'],
                         wrap='word', state='disabled')
        cf_text.pack(fill='both', expand=True, padx=15, pady=15)

        # Sample cash flow data
        cf_data = """💸 ПРОГНОЗ ДЕНЕЖНЫХ ПОТОКОВ НА 3 МЕСЯЦА

📅 ИЮЛЬ 2024:
💰 ПОСТУПЛЕНИЯ:
• Продажи наличными: 1 800 000 руб
• Продажи по картам: 2 200 000 руб
• Банкетные услуги: 450 000 руб
• Доставка: 380 000 руб
ИТОГО ПОСТУПЛЕНИЙ: 4 830 000 руб

💸 ВЫПЛАТЫ:
• Закупка продуктов: 1 450 000 руб
• Зарплата персонала: 980 000 руб
• Аренда помещения: 180 000 руб
• Коммунальные услуги: 95 000 руб
• Налоги и сборы: 420 000 руб
• Прочие расходы: 285 000 руб
ИТОГО ВЫПЛАТ: 3 410 000 руб

ЧИСТЫЙ ДЕНЕЖНЫЙ ПОТОК: +1 420 000 руб

📅 АВГУСТ 2024:
💰 ПОСТУПЛЕНИЯ:
• Продажи наличными: 1 650 000 руб (сезонное снижение)
• Продажи по картам: 2 050 000 руб
• Банкетные услуги: 380 000 руб
• Доставка: 420 000 руб
ИТОГО ПОСТУПЛЕНИЙ: 4 500 000 руб

💸 ВЫПЛАТЫ:
• Закупка продуктов: 1 350 000 руб
• Зарплата персонала: 980 000 руб
• Аренда помещения: 180 000 руб
• Коммунальные услуги: 85 000 руб
• Налоги и сборы: 390 000 руб
• Прочие расходы: 265 000 руб
ИТОГО ВЫПЛАТ: 3 250 000 руб

ЧИСТЫЙ ДЕНЕЖНЫЙ ПОТОК: +1 250 000 руб

📅 СЕНТЯБРЬ 2024:
💰 ПОСТУПЛЕНИЯ:
• Продажи наличными: 1 900 000 руб (восстановление)
• Продажи по картам: 2 300 000 руб
• Банкетные услуги: 520 000 руб (сезон корпоративов)
• Доставка: 400 000 руб
ИТОГО ПОСТУПЛЕНИЙ: 5 120 000 руб

💸 ВЫПЛАТЫ:
• Закупка продуктов: 1 540 000 руб
• Зарплата персонала: 1 050 000 руб (премии)
• Аренда помещения: 180 000 руб
• Коммунальные услуги: 90 000 руб
• Налоги и сборы: 445 000 руб
• Прочие расходы: 295 000 руб
ИТОГО ВЫПЛАТ: 3 600 000 руб

ЧИСТЫЙ ДЕНЕЖНЫЙ ПОТОК: +1 520 000 руб

📊 СВОДКА ПО КВАРТАЛУ:
• Общие поступления: 14 450 000 руб
• Общие выплаты: 10 260 000 руб
• Чистый денежный поток: +4 190 000 руб
• Средний месячный поток: +1 396 667 руб

💡 КЛЮЧЕВЫЕ НАБЛЮДЕНИЯ:
✅ Стабильный положительный денежный поток
⚠️ Сезонное снижение в августе
✅ Рост корпоративных заказов в сентябре
💰 Возможность инвестиций или расширения

🎯 РЕКОМЕНДАЦИИ:
• Создать резерв на сезонные колебания
• Рассмотреть возможность расширения
• Оптимизировать закупки в августе
• Развивать корпоративное направление
"""

        cf_text.config(state='normal')
        cf_text.insert('1.0', cf_data)
        cf_text.config(state='disabled')

    def create_profitability_analysis_tab(self):
        """Create profitability analysis tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="💎 Анализ Прибыльности")

        # Profitability controls
        profit_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        profit_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(profit_controls_frame, text="📊 Анализ по Блюдам",
                 command=self.analyze_dish_profitability,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(profit_controls_frame, text="📈 Анализ по Периодам",
                 command=self.analyze_period_profitability,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(profit_controls_frame, text="🎯 ROI Анализ",
                 command=self.roi_analysis,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Profitability display
        profit_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        profit_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Profitability treeview
        profit_columns = ('category', 'revenue', 'costs', 'gross_profit', 'margin', 'roi')
        self.profit_tree = ttk.Treeview(profit_frame, columns=profit_columns, show='headings', height=15)

        # Configure profitability columns
        self.profit_tree.heading('category', text='Категория')
        self.profit_tree.heading('revenue', text='Выручка')
        self.profit_tree.heading('costs', text='Затраты')
        self.profit_tree.heading('gross_profit', text='Валовая Прибыль')
        self.profit_tree.heading('margin', text='Маржа %')
        self.profit_tree.heading('roi', text='ROI %')

        self.profit_tree.column('category', width=200)
        self.profit_tree.column('revenue', width=150)
        self.profit_tree.column('costs', width=150)
        self.profit_tree.column('gross_profit', width=150)
        self.profit_tree.column('margin', width=100)
        self.profit_tree.column('roi', width=100)

        # Profitability scrollbar
        profit_scrollbar = ttk.Scrollbar(profit_frame, orient='vertical', command=self.profit_tree.yview)
        self.profit_tree.configure(yscrollcommand=profit_scrollbar.set)

        self.profit_tree.pack(side='left', fill='both', expand=True)
        profit_scrollbar.pack(side='right', fill='y')

    def create_financial_goals_tab(self):
        """Create financial goals tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="🎯 Финансовые Цели")

        # Goals controls
        goals_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        goals_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(goals_controls_frame, text="➕ Новая Цель",
                 command=self.create_financial_goal,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(goals_controls_frame, text="📊 Отслеживать Прогресс",
                 command=self.track_goal_progress,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(goals_controls_frame, text="✅ Завершить Цель",
                 command=self.complete_goal,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Goals display
        goals_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        goals_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Goals treeview
        goals_columns = ('goal_name', 'goal_type', 'target_value', 'current_value', 'progress', 'target_date', 'status')
        self.goals_tree = ttk.Treeview(goals_frame, columns=goals_columns, show='headings', height=12)

        # Configure goals columns
        self.goals_tree.heading('goal_name', text='Название Цели')
        self.goals_tree.heading('goal_type', text='Тип')
        self.goals_tree.heading('target_value', text='Целевое Значение')
        self.goals_tree.heading('current_value', text='Текущее Значение')
        self.goals_tree.heading('progress', text='Прогресс %')
        self.goals_tree.heading('target_date', text='Срок')
        self.goals_tree.heading('status', text='Статус')

        self.goals_tree.column('goal_name', width=200)
        self.goals_tree.column('goal_type', width=150)
        self.goals_tree.column('target_value', width=120)
        self.goals_tree.column('current_value', width=120)
        self.goals_tree.column('progress', width=80)
        self.goals_tree.column('target_date', width=100)
        self.goals_tree.column('status', width=100)

        # Goals scrollbar
        goals_scrollbar = ttk.Scrollbar(goals_frame, orient='vertical', command=self.goals_tree.yview)
        self.goals_tree.configure(yscrollcommand=goals_scrollbar.set)

        self.goals_tree.pack(side='left', fill='both', expand=True)
        goals_scrollbar.pack(side='right', fill='y')

    def create_reports_dashboard_tab(self):
        """Create reports and dashboard tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="📋 Отчеты и Дашборд")

        # Reports controls
        reports_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        reports_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(reports_controls_frame, text="📊 Финансовый Дашборд",
                 command=self.show_financial_dashboard,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(reports_controls_frame, text="📈 Отчет о Прибылях",
                 command=self.generate_profit_report,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(reports_controls_frame, text="💸 Отчет о Движении Средств",
                 command=self.generate_cash_flow_report,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(reports_controls_frame, text="📋 Бюджетный Отчет",
                 command=self.generate_budget_report,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Dashboard display
        dashboard_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        dashboard_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Financial metrics cards
        metrics_container = tk.Frame(dashboard_frame, bg=ModernStyles.COLORS['bg_main'])
        metrics_container.pack(fill='x', pady=(0, 20))

        # Revenue card
        revenue_card = tk.Frame(metrics_container, bg=ModernStyles.COLORS['success'], relief='raised', bd=2)
        revenue_card.pack(side='left', fill='both', expand=True, padx=5)

        tk.Label(revenue_card, text="💰 ВЫРУЧКА", font=('Cambria', 14, 'bold'),
                fg='white', bg=ModernStyles.COLORS['success']).pack(pady=5)
        tk.Label(revenue_card, text="4 230 000 руб", font=('Cambria', 18, 'bold'),
                fg='white', bg=ModernStyles.COLORS['success']).pack()
        tk.Label(revenue_card, text="↗️ +3.2% к плану", font=('Cambria', 12),
                fg='white', bg=ModernStyles.COLORS['success']).pack(pady=5)

        # Profit card
        profit_card = tk.Frame(metrics_container, bg=ModernStyles.COLORS['primary'], relief='raised', bd=2)
        profit_card.pack(side='left', fill='both', expand=True, padx=5)

        tk.Label(profit_card, text="💎 ПРИБЫЛЬ", font=('Cambria', 14, 'bold'),
                fg='white', bg=ModernStyles.COLORS['primary']).pack(pady=5)
        tk.Label(profit_card, text="1 248 500 руб", font=('Cambria', 18, 'bold'),
                fg='white', bg=ModernStyles.COLORS['primary']).pack()
        tk.Label(profit_card, text="↗️ +1.6% к плану", font=('Cambria', 12),
                fg='white', bg=ModernStyles.COLORS['primary']).pack(pady=5)

        # Margin card
        margin_card = tk.Frame(metrics_container, bg=ModernStyles.COLORS['info'], relief='raised', bd=2)
        margin_card.pack(side='left', fill='both', expand=True, padx=5)

        tk.Label(margin_card, text="📊 МАРЖА", font=('Cambria', 14, 'bold'),
                fg='white', bg=ModernStyles.COLORS['info']).pack(pady=5)
        tk.Label(margin_card, text="29.5%", font=('Cambria', 18, 'bold'),
                fg='white', bg=ModernStyles.COLORS['info']).pack()
        tk.Label(margin_card, text="↘️ -0.5% к плану", font=('Cambria', 12),
                fg='white', bg=ModernStyles.COLORS['info']).pack(pady=5)

        # Cash flow card
        cf_card = tk.Frame(metrics_container, bg=ModernStyles.COLORS['warning'], relief='raised', bd=2)
        cf_card.pack(side='left', fill='both', expand=True, padx=5)

        tk.Label(cf_card, text="💸 ДЕНЕЖНЫЙ ПОТОК", font=('Cambria', 14, 'bold'),
                fg='white', bg=ModernStyles.COLORS['warning']).pack(pady=5)
        tk.Label(cf_card, text="1 420 000 руб", font=('Cambria', 18, 'bold'),
                fg='white', bg=ModernStyles.COLORS['warning']).pack()
        tk.Label(cf_card, text="↗️ +8.2% к прогнозу", font=('Cambria', 12),
                fg='white', bg=ModernStyles.COLORS['warning']).pack(pady=5)

    def load_financial_data(self):
        """Load financial data into all tabs"""
        self.load_budget_data()
        self.load_profitability_data()
        self.load_goals_data()
        self.update_financial_metrics()

    def load_budget_data(self):
        """Load budget data"""
        # Sample budget data
        sample_budgets = [
            ('Бюджет Q3 2024', 'Квартальный', '01.07.2024', '30.09.2024', '14 450 000', '10 260 000', '4 190 000', 'Утвержден'),
            ('Бюджет Июль 2024', 'Месячный', '01.07.2024', '31.07.2024', '4 830 000', '3 410 000', '1 420 000', 'Выполнен'),
            ('Бюджет 2024', 'Годовой', '01.01.2024', '31.12.2024', '52 000 000', '38 500 000', '13 500 000', 'Активный'),
            ('Бюджет Август 2024', 'Месячный', '01.08.2024', '31.08.2024', '4 500 000', '3 250 000', '1 250 000', 'Черновик')
        ]

        for budget in sample_budgets:
            self.budget_tree.insert('', 'end', values=budget)

    def load_profitability_data(self):
        """Load profitability data"""
        # Sample profitability data
        sample_profit = [
            ('Основные блюда', '2 680 000', '1 340 000', '1 340 000', '50.0%', '125.4%'),
            ('Напитки', '750 000', '225 000', '525 000', '70.0%', '233.3%'),
            ('Десерты', '420 000', '168 000', '252 000', '60.0%', '150.0%'),
            ('Доставка', '420 000', '63 000', '357 000', '85.0%', '566.7%'),
            ('Банкеты', '380 000', '190 000', '190 000', '50.0%', '100.0%'),
            ('Бар', '580 000', '174 000', '406 000', '70.0%', '233.3%')
        ]

        for profit in sample_profit:
            self.profit_tree.insert('', 'end', values=profit)

    def load_goals_data(self):
        """Load financial goals data"""
        # Sample goals data
        sample_goals = [
            ('Увеличение выручки на 15%', 'Увеличение выручки', '6 000 000', '4 230 000', '70.5%', '31.12.2024', 'Активная'),
            ('Снижение затрат на 5%', 'Снижение затрат', '2 730 000', '2 981 500', '0.0%', '30.09.2024', 'Требует внимания'),
            ('ROI 25%', 'Повышение эффективности', '25.0%', '29.5%', '118.0%', '31.12.2024', 'Превышена'),
            ('Открытие филиала', 'Расширение бизнеса', '5 000 000', '2 100 000', '42.0%', '01.03.2025', 'Активная')
        ]

        for goal in sample_goals:
            self.goals_tree.insert('', 'end', values=goal)

    def update_financial_metrics(self):
        """Update financial metrics indicators"""
        # This would update the metrics in the header
        pass

    # Action methods (placeholder implementations)
    def create_budget(self):
        messagebox.showinfo("Создать Бюджет", "Функция создания бюджета будет реализована")

    def edit_budget(self):
        messagebox.showinfo("Редактировать Бюджет", "Функция редактирования бюджета будет реализована")

    def approve_budget(self):
        messagebox.showinfo("Утвердить Бюджет", "Функция утверждения бюджета будет реализована")

    def copy_budget(self):
        messagebox.showinfo("Копировать Бюджет", "Функция копирования бюджета будет реализована")

    def analyze_variance(self):
        messagebox.showinfo("Анализ Отклонений", "Функция анализа отклонений будет реализована")

    def detailed_variance_report(self):
        messagebox.showinfo("Детальный Отчет", "Функция детального отчета по отклонениям будет реализована")

    def show_critical_variances(self):
        messagebox.showinfo("Критические Отклонения", "Функция показа критических отклонений будет реализована")

    def create_cash_flow_forecast(self):
        messagebox.showinfo("Создать Прогноз", "Функция создания прогноза денежных потоков будет реализована")

    def update_cash_flow_forecast(self):
        messagebox.showinfo("Обновить Прогноз", "Функция обновления прогноза будет реализована")

    def scenario_analysis(self):
        messagebox.showinfo("Сценарный Анализ", "Функция сценарного анализа будет реализована")

    def analyze_dish_profitability(self):
        messagebox.showinfo("Анализ по Блюдам", "Функция анализа прибыльности блюд будет реализована")

    def analyze_period_profitability(self):
        messagebox.showinfo("Анализ по Периодам", "Функция анализа прибыльности по периодам будет реализована")

    def roi_analysis(self):
        messagebox.showinfo("ROI Анализ", "Функция ROI анализа будет реализована")

    def create_financial_goal(self):
        messagebox.showinfo("Новая Цель", "Функция создания финансовой цели будет реализована")

    def track_goal_progress(self):
        messagebox.showinfo("Отслеживать Прогресс", "Функция отслеживания прогресса цели будет реализована")

    def complete_goal(self):
        messagebox.showinfo("Завершить Цель", "Функция завершения цели будет реализована")

    def show_financial_dashboard(self):
        messagebox.showinfo("Финансовый Дашборд", "Функция показа финансового дашборда будет реализована")

    def generate_profit_report(self):
        messagebox.showinfo("Отчет о Прибылях", "Функция генерации отчета о прибылях будет реализована")

    def generate_cash_flow_report(self):
        messagebox.showinfo("Отчет о Движении Средств", "Функция генерации отчета о движении средств будет реализована")

    def generate_budget_report(self):
        messagebox.showinfo("Бюджетный Отчет", "Функция генерации бюджетного отчета будет реализована")

def create_financial_planning_budgeting(parent, db_manager):
    """Create and show the financial planning and budgeting system"""
    try:
        financial_system = FinancialPlanningBudgeting(parent, db_manager)
        financial_system.show_financial_planning_system()
        return financial_system
    except Exception as e:
        messagebox.showerror("Ошибка", f"Не удалось открыть систему финансового планирования: {e}")
        return None
