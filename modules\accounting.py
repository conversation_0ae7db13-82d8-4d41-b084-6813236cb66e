"""
Comprehensive Accounting Module for Restaurant Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from datetime import datetime, date
from typing import List, Dict, Any
from gui.styles import ModernStyles
from utils.helpers import <PERSON><PERSON><PERSON><PERSON>, DateHelper

class AccountingManager:
    """Complete accounting functionality for restaurant operations"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
    
    def create_accounting_window(self):
        """Create main accounting window with tabs"""
        window = tk.Toplevel(self.parent)
        window.title("Accounting & Financial Management")
        window.geometry("1400x900")
        window.configure(bg=ModernStyles.COLORS['bg_main'])
        
        # Main container
        main_frame = tk.Frame(window, **ModernStyles.WIDGET_STYLES['frame_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Title
        title_label = tk.Label(main_frame, text="Accounting & Financial Management",
                              **ModernStyles.WIDGET_STYLES['label_title'])
        title_label.pack(anchor='w', pady=(0, 20))
        
        # Create notebook for different accounting modules
        notebook = ttk.Notebook(main_frame, style="Modern.TNotebook")
        notebook.pack(fill='both', expand=True)
        
        # Chart of Accounts Tab
        accounts_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(accounts_frame, text="Chart of Accounts")
        self.create_chart_of_accounts_tab(accounts_frame)
        
        # Journal Entries Tab
        journal_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(journal_frame, text="Journal Entries")
        self.create_journal_entries_tab(journal_frame)
        
        # General Ledger Tab
        ledger_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(ledger_frame, text="General Ledger")
        self.create_general_ledger_tab(ledger_frame)
        
        # Accounts Payable Tab
        payable_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(payable_frame, text="Accounts Payable")
        self.create_accounts_payable_tab(payable_frame)
        
        # Accounts Receivable Tab
        receivable_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(receivable_frame, text="Accounts Receivable")
        self.create_accounts_receivable_tab(receivable_frame)
        
        # Financial Reports Tab
        reports_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(reports_frame, text="Financial Reports")
        self.create_financial_reports_tab(reports_frame)
        
        # Cash Flow Tab
        cashflow_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(cashflow_frame, text="Cash Flow")
        self.create_cash_flow_tab(cashflow_frame)
        
        # Budget Planning Tab
        budget_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(budget_frame, text="Budget Planning")
        self.create_budget_planning_tab(budget_frame)
        
        return window
    
    def create_chart_of_accounts_tab(self, parent):
        """Create chart of accounts management tab"""
        # Controls frame
        controls_frame = ModernStyles.create_card_frame(parent)
        controls_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(controls_frame, text="Chart of Accounts",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Buttons
        btn_frame = tk.Frame(controls_frame, bg=ModernStyles.COLORS['bg_card'])
        btn_frame.pack(fill='x')
        
        tk.Button(btn_frame, text="Add Account", command=self.add_account,
                 **ModernStyles.WIDGET_STYLES['button_success']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Edit Account", command=self.edit_account,
                 **ModernStyles.WIDGET_STYLES['button_warning']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Refresh", command=self.refresh_accounts,
                 **ModernStyles.WIDGET_STYLES['button_primary']).pack(side='left')
        
        # Accounts tree
        tree_frame = ModernStyles.create_card_frame(parent)
        tree_frame.pack(fill='both', expand=True)
        
        columns = ('Code', 'Account Name', 'Type', 'Balance', 'Status')
        self.accounts_tree = ttk.Treeview(tree_frame, columns=columns, show='headings',
                                         style="Modern.Treeview")
        
        for col in columns:
            self.accounts_tree.heading(col, text=col)
            self.accounts_tree.column(col, width=120)
        
        # Scrollbars
        v_scroll = ttk.Scrollbar(tree_frame, orient='vertical', command=self.accounts_tree.yview)
        h_scroll = ttk.Scrollbar(tree_frame, orient='horizontal', command=self.accounts_tree.xview)
        self.accounts_tree.configure(yscrollcommand=v_scroll.set, xscrollcommand=h_scroll.set)
        
        self.accounts_tree.pack(side='left', fill='both', expand=True)
        v_scroll.pack(side='right', fill='y')
        h_scroll.pack(side='bottom', fill='x')
        
        self.refresh_accounts()
    
    def create_journal_entries_tab(self, parent):
        """Create journal entries tab"""
        # Controls frame
        controls_frame = ModernStyles.create_card_frame(parent)
        controls_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(controls_frame, text="Journal Entries",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Buttons
        btn_frame = tk.Frame(controls_frame, bg=ModernStyles.COLORS['bg_card'])
        btn_frame.pack(fill='x')
        
        tk.Button(btn_frame, text="New Entry", command=self.create_journal_entry,
                 **ModernStyles.WIDGET_STYLES['button_success']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Post Entry", command=self.post_journal_entry,
                 **ModernStyles.WIDGET_STYLES['button_primary']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Reverse Entry", command=self.reverse_journal_entry,
                 **ModernStyles.WIDGET_STYLES['button_danger']).pack(side='left')
        
        # Journal entries list
        entries_frame = ModernStyles.create_card_frame(parent)
        entries_frame.pack(fill='both', expand=True)
        
        columns = ('Entry #', 'Date', 'Description', 'Debit', 'Credit', 'Status')
        self.journal_tree = ttk.Treeview(entries_frame, columns=columns, show='headings',
                                        style="Modern.Treeview")
        
        for col in columns:
            self.journal_tree.heading(col, text=col)
            self.journal_tree.column(col, width=120)
        
        # Scrollbars
        j_v_scroll = ttk.Scrollbar(entries_frame, orient='vertical', command=self.journal_tree.yview)
        j_h_scroll = ttk.Scrollbar(entries_frame, orient='horizontal', command=self.journal_tree.xview)
        self.journal_tree.configure(yscrollcommand=j_v_scroll.set, xscrollcommand=j_h_scroll.set)
        
        self.journal_tree.pack(side='left', fill='both', expand=True)
        j_v_scroll.pack(side='right', fill='y')
        j_h_scroll.pack(side='bottom', fill='x')
    
    def create_general_ledger_tab(self, parent):
        """Create general ledger tab"""
        # Controls frame
        controls_frame = ModernStyles.create_card_frame(parent)
        controls_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(controls_frame, text="General Ledger",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Account selection
        select_frame = tk.Frame(controls_frame, bg=ModernStyles.COLORS['bg_card'])
        select_frame.pack(fill='x', pady=(0, 10))
        
        tk.Label(select_frame, text="Select Account:",
                **ModernStyles.WIDGET_STYLES['label_body']).pack(side='left')
        
        self.ledger_account_var = tk.StringVar()
        account_combo = ttk.Combobox(select_frame, textvariable=self.ledger_account_var,
                                    style="Modern.TCombobox", width=30)
        account_combo.pack(side='left', padx=(10, 0))
        
        tk.Button(select_frame, text="View Ledger", command=self.view_account_ledger,
                 **ModernStyles.WIDGET_STYLES['button_primary']).pack(side='left', padx=(10, 0))
        
        # Ledger display
        ledger_frame = ModernStyles.create_card_frame(parent)
        ledger_frame.pack(fill='both', expand=True)
        
        columns = ('Date', 'Description', 'Reference', 'Debit', 'Credit', 'Balance')
        self.ledger_tree = ttk.Treeview(ledger_frame, columns=columns, show='headings',
                                       style="Modern.Treeview")
        
        for col in columns:
            self.ledger_tree.heading(col, text=col)
            self.ledger_tree.column(col, width=120)
        
        # Scrollbars
        l_v_scroll = ttk.Scrollbar(ledger_frame, orient='vertical', command=self.ledger_tree.yview)
        l_h_scroll = ttk.Scrollbar(ledger_frame, orient='horizontal', command=self.ledger_tree.xview)
        self.ledger_tree.configure(yscrollcommand=l_v_scroll.set, xscrollcommand=l_h_scroll.set)
        
        self.ledger_tree.pack(side='left', fill='both', expand=True)
        l_v_scroll.pack(side='right', fill='y')
        l_h_scroll.pack(side='bottom', fill='x')
    
    def create_accounts_payable_tab(self, parent):
        """Create accounts payable tab"""
        # Controls frame
        controls_frame = ModernStyles.create_card_frame(parent)
        controls_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(controls_frame, text="Accounts Payable",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Buttons
        btn_frame = tk.Frame(controls_frame, bg=ModernStyles.COLORS['bg_card'])
        btn_frame.pack(fill='x')
        
        tk.Button(btn_frame, text="New Bill", command=self.create_bill,
                 **ModernStyles.WIDGET_STYLES['button_success']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Pay Bill", command=self.pay_bill,
                 **ModernStyles.WIDGET_STYLES['button_primary']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Aging Report", command=self.ap_aging_report,
                 **ModernStyles.WIDGET_STYLES['button_secondary']).pack(side='left')
        
        # Bills list
        bills_frame = ModernStyles.create_card_frame(parent)
        bills_frame.pack(fill='both', expand=True)
        
        columns = ('Vendor', 'Invoice #', 'Date', 'Due Date', 'Amount', 'Paid', 'Balance', 'Status')
        self.bills_tree = ttk.Treeview(bills_frame, columns=columns, show='headings',
                                      style="Modern.Treeview")
        
        for col in columns:
            self.bills_tree.heading(col, text=col)
            self.bills_tree.column(col, width=100)
        
        # Scrollbars
        b_v_scroll = ttk.Scrollbar(bills_frame, orient='vertical', command=self.bills_tree.yview)
        b_h_scroll = ttk.Scrollbar(bills_frame, orient='horizontal', command=self.bills_tree.xview)
        self.bills_tree.configure(yscrollcommand=b_v_scroll.set, xscrollcommand=b_h_scroll.set)
        
        self.bills_tree.pack(side='left', fill='both', expand=True)
        b_v_scroll.pack(side='right', fill='y')
        b_h_scroll.pack(side='bottom', fill='x')
    
    def create_accounts_receivable_tab(self, parent):
        """Create accounts receivable tab"""
        # Controls frame
        controls_frame = ModernStyles.create_card_frame(parent)
        controls_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(controls_frame, text="Accounts Receivable",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Buttons
        btn_frame = tk.Frame(controls_frame, bg=ModernStyles.COLORS['bg_card'])
        btn_frame.pack(fill='x')
        
        tk.Button(btn_frame, text="New Invoice", command=self.create_invoice,
                 **ModernStyles.WIDGET_STYLES['button_success']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Record Payment", command=self.record_payment,
                 **ModernStyles.WIDGET_STYLES['button_primary']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Aging Report", command=self.ar_aging_report,
                 **ModernStyles.WIDGET_STYLES['button_secondary']).pack(side='left')
        
        # Invoices list
        invoices_frame = ModernStyles.create_card_frame(parent)
        invoices_frame.pack(fill='both', expand=True)
        
        columns = ('Customer', 'Invoice #', 'Date', 'Due Date', 'Amount', 'Paid', 'Balance', 'Status')
        self.invoices_tree = ttk.Treeview(invoices_frame, columns=columns, show='headings',
                                         style="Modern.Treeview")
        
        for col in columns:
            self.invoices_tree.heading(col, text=col)
            self.invoices_tree.column(col, width=100)
        
        # Scrollbars
        i_v_scroll = ttk.Scrollbar(invoices_frame, orient='vertical', command=self.invoices_tree.yview)
        i_h_scroll = ttk.Scrollbar(invoices_frame, orient='horizontal', command=self.invoices_tree.xview)
        self.invoices_tree.configure(yscrollcommand=i_v_scroll.set, xscrollcommand=i_h_scroll.set)
        
        self.invoices_tree.pack(side='left', fill='both', expand=True)
        i_v_scroll.pack(side='right', fill='y')
        i_h_scroll.pack(side='bottom', fill='x')
    
    def create_financial_reports_tab(self, parent):
        """Create financial reports tab"""
        # Controls frame
        controls_frame = ModernStyles.create_card_frame(parent)
        controls_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(controls_frame, text="Financial Reports",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Report buttons
        btn_frame = tk.Frame(controls_frame, bg=ModernStyles.COLORS['bg_card'])
        btn_frame.pack(fill='x')
        
        tk.Button(btn_frame, text="Balance Sheet", command=self.generate_balance_sheet,
                 **ModernStyles.WIDGET_STYLES['button_primary']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Income Statement", command=self.generate_income_statement,
                 **ModernStyles.WIDGET_STYLES['button_secondary']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Trial Balance", command=self.generate_trial_balance,
                 **ModernStyles.WIDGET_STYLES['button_success']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Cash Flow Statement", command=self.generate_cash_flow_statement,
                 **ModernStyles.WIDGET_STYLES['button_warning']).pack(side='left')
        
        # Reports display
        self.reports_display_frame = ModernStyles.create_card_frame(parent)
        self.reports_display_frame.pack(fill='both', expand=True)
        
        # Initial placeholder
        tk.Label(self.reports_display_frame, text="Select a report to generate financial statements",
                font=ModernStyles.FONTS['body'],
                fg=ModernStyles.COLORS['text_secondary'],
                bg=ModernStyles.COLORS['bg_card']).pack(expand=True)
    
    def create_cash_flow_tab(self, parent):
        """Create cash flow management tab"""
        messagebox.showinfo("Feature", "Cash flow management will be implemented in the next update.")
    
    def create_budget_planning_tab(self, parent):
        """Create budget planning tab"""
        messagebox.showinfo("Feature", "Budget planning will be implemented in the next update.")
    
    # Account Management Methods
    def add_account(self):
        """Add new account to chart of accounts"""
        messagebox.showinfo("Feature", "Add account functionality will be implemented.")
    
    def edit_account(self):
        """Edit existing account"""
        messagebox.showinfo("Feature", "Edit account functionality will be implemented.")
    
    def refresh_accounts(self):
        """Refresh chart of accounts display"""
        try:
            # Clear existing data
            for item in self.accounts_tree.get_children():
                self.accounts_tree.delete(item)
            
            # Get accounts from database
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT account_code, account_name, account_type, is_active
                    FROM chart_of_accounts
                    ORDER BY account_code
                ''')
                
                accounts = cursor.fetchall()
                
                for account in accounts:
                    # Calculate balance (placeholder for now)
                    balance = 0.00
                    status = "Active" if account[3] else "Inactive"
                    
                    self.accounts_tree.insert('', 'end', values=(
                        account[0],  # Code
                        account[1],  # Name
                        account[2],  # Type
                        NumberHelper.format_currency(balance),
                        status
                    ))
        
        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh accounts: {str(e)}")
    
    # Journal Entry Methods
    def create_journal_entry(self):
        """Create new journal entry"""
        messagebox.showinfo("Feature", "Journal entry creation will be implemented.")
    
    def post_journal_entry(self):
        """Post selected journal entry"""
        messagebox.showinfo("Feature", "Journal entry posting will be implemented.")
    
    def reverse_journal_entry(self):
        """Reverse selected journal entry"""
        messagebox.showinfo("Feature", "Journal entry reversal will be implemented.")
    
    # General Ledger Methods
    def view_account_ledger(self):
        """View ledger for selected account"""
        messagebox.showinfo("Feature", "Account ledger view will be implemented.")
    
    # Accounts Payable Methods
    def create_bill(self):
        """Create new bill"""
        messagebox.showinfo("Feature", "Bill creation will be implemented.")
    
    def pay_bill(self):
        """Pay selected bill"""
        messagebox.showinfo("Feature", "Bill payment will be implemented.")
    
    def ap_aging_report(self):
        """Generate accounts payable aging report"""
        messagebox.showinfo("Feature", "AP aging report will be implemented.")
    
    # Accounts Receivable Methods
    def create_invoice(self):
        """Create new invoice"""
        messagebox.showinfo("Feature", "Invoice creation will be implemented.")
    
    def record_payment(self):
        """Record payment for invoice"""
        messagebox.showinfo("Feature", "Payment recording will be implemented.")
    
    def ar_aging_report(self):
        """Generate accounts receivable aging report"""
        messagebox.showinfo("Feature", "AR aging report will be implemented.")
    
    # Financial Reports Methods
    def generate_balance_sheet(self):
        """Generate balance sheet"""
        messagebox.showinfo("Feature", "Balance sheet generation will be implemented.")
    
    def generate_income_statement(self):
        """Generate income statement"""
        messagebox.showinfo("Feature", "Income statement generation will be implemented.")
    
    def generate_trial_balance(self):
        """Generate trial balance"""
        messagebox.showinfo("Feature", "Trial balance generation will be implemented.")
    
    def generate_cash_flow_statement(self):
        """Generate cash flow statement"""
        messagebox.showinfo("Feature", "Cash flow statement generation will be implemented.")
