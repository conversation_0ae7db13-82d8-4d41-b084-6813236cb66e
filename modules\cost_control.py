"""
Advanced Cost Control and Analysis Module for Restaurant Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
from gui.styles import ModernStyles

# Проверяем наличие matplotlib
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    import pandas as pd
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

# Простые хелперы если utils недоступны
class NumberHelper:
    @staticmethod
    def format_currency(amount):
        return f"{amount:,.2f}₽"

class DateHelper:
    @staticmethod
    def format_date(date):
        return date.strftime("%d.%m.%Y")

class CostControlManager:
    """Advanced cost control and analysis functionality"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
    
    def create_cost_control_window(self):
        """Create cost control and analysis window"""
        window = tk.Toplevel(self.parent)
        window.title("Cost Control & Analysis")
        window.geometry("1600x1000")
        window.configure(bg=ModernStyles.COLORS['bg_main'])
        
        # Main container
        main_frame = tk.Frame(window, **ModernStyles.WIDGET_STYLES['frame_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Title
        title_label = tk.Label(main_frame, text="Cost Control & Analysis",
                              **ModernStyles.WIDGET_STYLES['label_title'])
        title_label.pack(anchor='w', pady=(0, 20))
        
        # Create notebook for cost control modules
        notebook = ttk.Notebook(main_frame, style="Modern.TNotebook")
        notebook.pack(fill='both', expand=True)
        
        # Food Cost Analysis Tab
        food_cost_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(food_cost_frame, text="Food Cost Analysis")
        self.create_food_cost_tab(food_cost_frame)
        
        # Labor Cost Analysis Tab
        labor_cost_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(labor_cost_frame, text="Labor Cost Analysis")
        self.create_labor_cost_tab(labor_cost_frame)
        
        # Recipe Costing Tab
        recipe_cost_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(recipe_cost_frame, text="Recipe Costing")
        self.create_recipe_costing_tab(recipe_cost_frame)
        
        # Variance Analysis Tab
        variance_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(variance_frame, text="Variance Analysis")
        self.create_variance_analysis_tab(variance_frame)
        
        # Cost Trends Tab
        trends_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(trends_frame, text="Cost Trends")
        self.create_cost_trends_tab(trends_frame)
        
        # Budget vs Actual Tab
        budget_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(budget_frame, text="Budget vs Actual")
        self.create_budget_analysis_tab(budget_frame)
        
        return window
    
    def create_food_cost_tab(self, parent):
        """Create food cost analysis tab"""
        # Summary cards
        summary_frame = ModernStyles.create_card_frame(parent)
        summary_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(summary_frame, text="Food Cost Summary",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Cost metrics
        metrics_frame = tk.Frame(summary_frame, bg=ModernStyles.COLORS['bg_card'])
        metrics_frame.pack(fill='x')
        
        # Calculate food cost metrics
        food_cost_percentage = self.calculate_food_cost_percentage()
        theoretical_cost = self.calculate_theoretical_food_cost()
        actual_cost = self.calculate_actual_food_cost()
        variance = actual_cost - theoretical_cost
        
        self.create_cost_metric(metrics_frame, "Food Cost %", f"{food_cost_percentage:.1f}%", 
                               ModernStyles.COLORS['primary'])
        self.create_cost_metric(metrics_frame, "Theoretical Cost", NumberHelper.format_currency(theoretical_cost), 
                               ModernStyles.COLORS['success'])
        self.create_cost_metric(metrics_frame, "Actual Cost", NumberHelper.format_currency(actual_cost), 
                               ModernStyles.COLORS['warning'])
        self.create_cost_metric(metrics_frame, "Variance", NumberHelper.format_currency(variance), 
                               ModernStyles.COLORS['danger'] if variance > 0 else ModernStyles.COLORS['success'])
        
        # Food cost breakdown chart
        chart_frame = ModernStyles.create_card_frame(parent)
        chart_frame.pack(fill='both', expand=True)
        
        tk.Label(chart_frame, text="Food Cost Breakdown",
                **ModernStyles.WIDGET_STYLES['label_subheading']).pack(anchor='w', pady=(0, 10))
        
        self.create_food_cost_chart(chart_frame)
    
    def create_labor_cost_tab(self, parent):
        """Create labor cost analysis tab"""
        # Labor cost summary
        summary_frame = ModernStyles.create_card_frame(parent)
        summary_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(summary_frame, text="Labor Cost Analysis",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Labor metrics
        metrics_frame = tk.Frame(summary_frame, bg=ModernStyles.COLORS['bg_card'])
        metrics_frame.pack(fill='x')
        
        # Calculate labor metrics
        labor_cost_percentage = self.calculate_labor_cost_percentage()
        total_labor_cost = self.calculate_total_labor_cost()
        labor_hours = self.calculate_total_labor_hours()
        avg_hourly_rate = total_labor_cost / labor_hours if labor_hours > 0 else 0
        
        self.create_cost_metric(metrics_frame, "Labor Cost %", f"{labor_cost_percentage:.1f}%", 
                               ModernStyles.COLORS['primary'])
        self.create_cost_metric(metrics_frame, "Total Labor Cost", NumberHelper.format_currency(total_labor_cost), 
                               ModernStyles.COLORS['secondary'])
        self.create_cost_metric(metrics_frame, "Total Hours", f"{labor_hours:.0f}", 
                               ModernStyles.COLORS['success'])
        self.create_cost_metric(metrics_frame, "Avg Hourly Rate", NumberHelper.format_currency(avg_hourly_rate), 
                               ModernStyles.COLORS['warning'])
        
        # Labor cost breakdown
        breakdown_frame = ModernStyles.create_card_frame(parent)
        breakdown_frame.pack(fill='both', expand=True)
        
        tk.Label(breakdown_frame, text="Labor Cost by Department",
                **ModernStyles.WIDGET_STYLES['label_subheading']).pack(anchor='w', pady=(0, 10))
        
        self.create_labor_cost_chart(breakdown_frame)
    
    def create_recipe_costing_tab(self, parent):
        """Create recipe costing tab"""
        # Controls frame
        controls_frame = ModernStyles.create_card_frame(parent)
        controls_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(controls_frame, text="Recipe Cost Analysis",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Recipe selection
        select_frame = tk.Frame(controls_frame, bg=ModernStyles.COLORS['bg_card'])
        select_frame.pack(fill='x', pady=(0, 10))
        
        tk.Label(select_frame, text="Select Recipe:",
                **ModernStyles.WIDGET_STYLES['label_body']).pack(side='left')
        
        self.recipe_var = tk.StringVar()
        recipe_combo = ttk.Combobox(select_frame, textvariable=self.recipe_var,
                                   style="Modern.TCombobox", width=30)
        recipe_combo.pack(side='left', padx=(10, 0))
        
        tk.Button(select_frame, text="Calculate Cost", command=self.calculate_recipe_cost,
                 **ModernStyles.WIDGET_STYLES['button_primary']).pack(side='left', padx=(10, 0))
        
        # Recipe cost details
        details_frame = ModernStyles.create_card_frame(parent)
        details_frame.pack(fill='both', expand=True)
        
        # Recipe cost breakdown table
        columns = ('Ingredient', 'Quantity', 'Unit', 'Unit Cost', 'Total Cost', '% of Recipe')
        self.recipe_cost_tree = ttk.Treeview(details_frame, columns=columns, show='headings',
                                           style="Modern.Treeview")
        
        for col in columns:
            self.recipe_cost_tree.heading(col, text=col)
            self.recipe_cost_tree.column(col, width=120)
        
        # Scrollbars
        rc_v_scroll = ttk.Scrollbar(details_frame, orient='vertical', command=self.recipe_cost_tree.yview)
        rc_h_scroll = ttk.Scrollbar(details_frame, orient='horizontal', command=self.recipe_cost_tree.xview)
        self.recipe_cost_tree.configure(yscrollcommand=rc_v_scroll.set, xscrollcommand=rc_h_scroll.set)
        
        self.recipe_cost_tree.pack(side='left', fill='both', expand=True)
        rc_v_scroll.pack(side='right', fill='y')
        rc_h_scroll.pack(side='bottom', fill='x')
    
    def create_variance_analysis_tab(self, parent):
        """Create variance analysis tab"""
        # Variance summary
        summary_frame = ModernStyles.create_card_frame(parent)
        summary_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(summary_frame, text="Cost Variance Analysis",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Variance metrics
        metrics_frame = tk.Frame(summary_frame, bg=ModernStyles.COLORS['bg_card'])
        metrics_frame.pack(fill='x')
        
        # Calculate variances
        food_variance = self.calculate_food_cost_variance()
        labor_variance = self.calculate_labor_cost_variance()
        overhead_variance = self.calculate_overhead_variance()
        total_variance = food_variance + labor_variance + overhead_variance
        
        variances = [
            ("Food Cost Variance", food_variance),
            ("Labor Cost Variance", labor_variance),
            ("Overhead Variance", overhead_variance),
            ("Total Variance", total_variance)
        ]
        
        for name, variance in variances:
            color = ModernStyles.COLORS['danger'] if variance > 0 else ModernStyles.COLORS['success']
            self.create_cost_metric(metrics_frame, name, NumberHelper.format_currency(abs(variance)), color)
        
        # Variance details
        details_frame = ModernStyles.create_card_frame(parent)
        details_frame.pack(fill='both', expand=True)
        
        tk.Label(details_frame, text="Variance Details",
                **ModernStyles.WIDGET_STYLES['label_subheading']).pack(anchor='w', pady=(0, 10))
        
        self.create_variance_chart(details_frame)
    
    def create_cost_trends_tab(self, parent):
        """Create cost trends tab"""
        # Trends summary
        summary_frame = ModernStyles.create_card_frame(parent)
        summary_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(summary_frame, text="Cost Trends Analysis",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Time period selection
        period_frame = tk.Frame(summary_frame, bg=ModernStyles.COLORS['bg_card'])
        period_frame.pack(fill='x')
        
        tk.Label(period_frame, text="Period:",
                **ModernStyles.WIDGET_STYLES['label_body']).pack(side='left')
        
        self.period_var = tk.StringVar(value="Last 12 Months")
        period_combo = ttk.Combobox(period_frame, textvariable=self.period_var,
                                   values=["Last 6 Months", "Last 12 Months", "Last 2 Years"],
                                   style="Modern.TCombobox", width=20)
        period_combo.pack(side='left', padx=(10, 0))
        
        tk.Button(period_frame, text="Update Trends", command=self.update_cost_trends,
                 **ModernStyles.WIDGET_STYLES['button_primary']).pack(side='left', padx=(10, 0))
        
        # Trends chart
        trends_frame = ModernStyles.create_card_frame(parent)
        trends_frame.pack(fill='both', expand=True)
        
        self.create_cost_trends_chart(trends_frame)
    
    def create_budget_analysis_tab(self, parent):
        """Create budget vs actual analysis tab"""
        # Budget summary
        summary_frame = ModernStyles.create_card_frame(parent)
        summary_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(summary_frame, text="Budget vs Actual Analysis",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Budget metrics
        metrics_frame = tk.Frame(summary_frame, bg=ModernStyles.COLORS['bg_card'])
        metrics_frame.pack(fill='x')
        
        # Calculate budget metrics
        budgeted_food_cost = 25000
        actual_food_cost = 26500
        budgeted_labor_cost = 18000
        actual_labor_cost = 17200
        
        food_variance_pct = ((actual_food_cost - budgeted_food_cost) / budgeted_food_cost) * 100
        labor_variance_pct = ((actual_labor_cost - budgeted_labor_cost) / budgeted_labor_cost) * 100
        
        budget_data = [
            ("Food Cost Budget", NumberHelper.format_currency(budgeted_food_cost)),
            ("Food Cost Actual", NumberHelper.format_currency(actual_food_cost)),
            ("Labor Cost Budget", NumberHelper.format_currency(budgeted_labor_cost)),
            ("Labor Cost Actual", NumberHelper.format_currency(actual_labor_cost))
        ]
        
        for name, value in budget_data:
            color = ModernStyles.COLORS['primary'] if 'Budget' in name else ModernStyles.COLORS['secondary']
            self.create_cost_metric(metrics_frame, name, value, color)
        
        # Budget vs actual chart
        chart_frame = ModernStyles.create_card_frame(parent)
        chart_frame.pack(fill='both', expand=True)
        
        self.create_budget_vs_actual_chart(chart_frame)
    
    def create_cost_metric(self, parent, label, value, color):
        """Create cost metric display"""
        metric_frame = tk.Frame(parent, bg=color, relief='flat', bd=0)
        metric_frame.pack(side='left', fill='both', expand=True, padx=5)
        
        inner_frame = tk.Frame(metric_frame, bg=color)
        inner_frame.pack(fill='both', expand=True, padx=15, pady=15)
        
        tk.Label(inner_frame, text=str(value), font=ModernStyles.FONTS['heading'],
                fg=ModernStyles.COLORS['text_white'], bg=color).pack()
        
        tk.Label(inner_frame, text=label, font=ModernStyles.FONTS['body'],
                fg=ModernStyles.COLORS['text_white'], bg=color).pack()
    
    def create_food_cost_chart(self, parent):
        """Create food cost breakdown chart"""
        if not MATPLOTLIB_AVAILABLE:
            # Показать текстовые данные
            chart_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_card'])
            chart_frame.pack(fill='both', expand=True)

            tk.Label(chart_frame, text="📊 Структура Затрат на Продукты",
                    font=('Arial', 12, 'bold'),
                    bg=ModernStyles.COLORS['bg_card']).pack(pady=10)

            # Данные по категориям
            categories_data = [
                ("Белки (мясо, рыба)", "42,500₽", "41.5%"),
                ("Овощи и фрукты", "21,000₽", "20.5%"),
                ("Молочные продукты", "19,000₽", "18.5%"),
                ("Крупы и мука", "10,500₽", "10.3%"),
                ("Прочее", "9,500₽", "9.2%")
            ]

            for category, cost, percent in categories_data:
                cat_frame = tk.Frame(chart_frame, bg=ModernStyles.COLORS['bg_card'])
                cat_frame.pack(fill='x', padx=20, pady=3)

                tk.Label(cat_frame, text=category, font=('Arial', 10),
                        bg=ModernStyles.COLORS['bg_card']).pack(side='left')
                tk.Label(cat_frame, text=f"{cost} ({percent})", font=('Arial', 10, 'bold'),
                        fg=ModernStyles.COLORS['primary'],
                        bg=ModernStyles.COLORS['bg_card']).pack(side='right')

            # Разделитель
            tk.Frame(chart_frame, bg=ModernStyles.COLORS['gray'], height=1).pack(fill='x', padx=20, pady=10)

            tk.Label(chart_frame, text="📈 Затраты по Месяцам",
                    font=('Arial', 12, 'bold'),
                    bg=ModernStyles.COLORS['bg_card']).pack(pady=10)

            monthly_data = [
                ("Январь", "102,500₽"),
                ("Февраль", "106,000₽"),
                ("Март", "104,000₽"),
                ("Апрель", "110,500₽"),
                ("Май", "107,500₽"),
                ("Июнь", "104,500₽")
            ]

            for month, cost in monthly_data:
                month_frame = tk.Frame(chart_frame, bg=ModernStyles.COLORS['bg_card'])
                month_frame.pack(fill='x', padx=20, pady=2)

                tk.Label(month_frame, text=month, font=('Arial', 10),
                        bg=ModernStyles.COLORS['bg_card']).pack(side='left')
                tk.Label(month_frame, text=cost, font=('Arial', 10, 'bold'),
                        fg=ModernStyles.COLORS['success'],
                        bg=ModernStyles.COLORS['bg_card']).pack(side='right')
            return

        try:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

            # Pie chart for food cost categories
            categories = ['Proteins', 'Vegetables', 'Dairy', 'Grains', 'Other']
            costs = [8500, 4200, 3800, 2100, 1900]
            colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']

            ax1.pie(costs, labels=categories, autopct='%1.1f%%', colors=colors, startangle=90)
            ax1.set_title('Food Cost by Category')

            # Bar chart for monthly food costs
            months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
            monthly_costs = [20500, 21200, 20800, 22100, 21500, 20900]

            ax2.bar(months, monthly_costs, color=ModernStyles.COLORS['primary'], alpha=0.8)
            ax2.set_title('Monthly Food Costs')
            ax2.set_ylabel('Cost ($)')
            ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))

            plt.tight_layout()

            # Embed chart
            canvas = FigureCanvasTkAgg(fig, parent)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)

        except Exception as e:
            tk.Label(parent, text=f"График недоступен: {str(e)}",
                    font=ModernStyles.FONTS['body'],
                    fg=ModernStyles.COLORS['text_secondary'],
                    bg=ModernStyles.COLORS['bg_card']).pack(expand=True)
    
    def create_labor_cost_chart(self, parent):
        """Create labor cost breakdown chart"""
        try:
            fig, ax = plt.subplots(figsize=(10, 6))
            
            departments = ['Kitchen', 'Service', 'Management', 'Cleaning']
            costs = [12000, 8500, 4200, 2100]
            hours = [480, 360, 160, 120]
            
            x = range(len(departments))
            width = 0.35
            
            ax2 = ax.twinx()
            
            bars1 = ax.bar([i - width/2 for i in x], costs, width, label='Cost ($)',
                          color=ModernStyles.COLORS['primary'], alpha=0.8)
            bars2 = ax2.bar([i + width/2 for i in x], hours, width, label='Hours',
                           color=ModernStyles.COLORS['secondary'], alpha=0.8)
            
            ax.set_xlabel('Department')
            ax.set_ylabel('Cost ($)', color=ModernStyles.COLORS['primary'])
            ax2.set_ylabel('Hours', color=ModernStyles.COLORS['secondary'])
            ax.set_title('Labor Cost and Hours by Department')
            ax.set_xticks(x)
            ax.set_xticklabels(departments)
            
            # Format y-axis
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
            
            # Add legends
            ax.legend(loc='upper left')
            ax2.legend(loc='upper right')
            
            plt.tight_layout()
            
            # Embed chart
            canvas = FigureCanvasTkAgg(fig, parent)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)
            
        except Exception as e:
            tk.Label(parent, text=f"Chart unavailable: {str(e)}",
                    font=ModernStyles.FONTS['body'],
                    fg=ModernStyles.COLORS['text_secondary'],
                    bg=ModernStyles.COLORS['bg_card']).pack(expand=True)
    
    def create_variance_chart(self, parent):
        """Create variance analysis chart"""
        try:
            fig, ax = plt.subplots(figsize=(10, 6))
            
            categories = ['Food Cost', 'Labor Cost', 'Overhead', 'Utilities', 'Marketing']
            budget = [25000, 18000, 8000, 3500, 2000]
            actual = [26500, 17200, 8400, 3200, 2100]
            
            x = range(len(categories))
            width = 0.35
            
            bars1 = ax.bar([i - width/2 for i in x], budget, width, label='Budget',
                          color=ModernStyles.COLORS['success'], alpha=0.8)
            bars2 = ax.bar([i + width/2 for i in x], actual, width, label='Actual',
                          color=ModernStyles.COLORS['warning'], alpha=0.8)
            
            ax.set_xlabel('Cost Category')
            ax.set_ylabel('Amount ($)')
            ax.set_title('Budget vs Actual Cost Analysis')
            ax.set_xticks(x)
            ax.set_xticklabels(categories, rotation=45)
            ax.legend()
            
            # Format y-axis
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
            
            plt.tight_layout()
            
            # Embed chart
            canvas = FigureCanvasTkAgg(fig, parent)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)
            
        except Exception as e:
            tk.Label(parent, text=f"Chart unavailable: {str(e)}",
                    font=ModernStyles.FONTS['body'],
                    fg=ModernStyles.COLORS['text_secondary'],
                    bg=ModernStyles.COLORS['bg_card']).pack(expand=True)
    
    def create_cost_trends_chart(self, parent):
        """Create cost trends chart"""
        try:
            fig, ax = plt.subplots(figsize=(12, 6))
            
            months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
            food_costs = [20500, 21200, 20800, 22100, 21500, 20900, 21800, 22200, 21600, 22000, 21400, 21900]
            labor_costs = [17800, 18200, 17600, 18500, 18100, 17900, 18300, 18600, 18000, 18400, 17700, 18200]
            
            ax.plot(months, food_costs, marker='o', linewidth=2, label='Food Costs',
                   color=ModernStyles.COLORS['primary'])
            ax.plot(months, labor_costs, marker='s', linewidth=2, label='Labor Costs',
                   color=ModernStyles.COLORS['secondary'])
            
            ax.set_title('Cost Trends Over Time')
            ax.set_xlabel('Month')
            ax.set_ylabel('Cost ($)')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            # Format y-axis
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
            
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            # Embed chart
            canvas = FigureCanvasTkAgg(fig, parent)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)
            
        except Exception as e:
            tk.Label(parent, text=f"Chart unavailable: {str(e)}",
                    font=ModernStyles.FONTS['body'],
                    fg=ModernStyles.COLORS['text_secondary'],
                    bg=ModernStyles.COLORS['bg_card']).pack(expand=True)
    
    def create_budget_vs_actual_chart(self, parent):
        """Create budget vs actual comparison chart"""
        try:
            fig, ax = plt.subplots(figsize=(10, 6))
            
            categories = ['Food Cost', 'Labor Cost', 'Overhead', 'Utilities', 'Marketing', 'Total']
            budget = [25000, 18000, 8000, 3500, 2000, 56500]
            actual = [26500, 17200, 8400, 3200, 2100, 57400]
            variance = [actual[i] - budget[i] for i in range(len(budget))]
            
            x = range(len(categories))
            
            # Create variance bars
            colors = [ModernStyles.COLORS['danger'] if v > 0 else ModernStyles.COLORS['success'] for v in variance]
            bars = ax.bar(x, variance, color=colors, alpha=0.8)
            
            ax.set_xlabel('Cost Category')
            ax.set_ylabel('Variance ($)')
            ax.set_title('Budget Variance Analysis')
            ax.set_xticks(x)
            ax.set_xticklabels(categories, rotation=45)
            ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)
            
            # Add value labels on bars
            for bar, variance_val in zip(bars, variance):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'${variance_val:,.0f}',
                       ha='center', va='bottom' if height > 0 else 'top')
            
            # Format y-axis
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
            
            plt.tight_layout()
            
            # Embed chart
            canvas = FigureCanvasTkAgg(fig, parent)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)
            
        except Exception as e:
            tk.Label(parent, text=f"Chart unavailable: {str(e)}",
                    font=ModernStyles.FONTS['body'],
                    fg=ModernStyles.COLORS['text_secondary'],
                    bg=ModernStyles.COLORS['bg_card']).pack(expand=True)
    
    # Calculation methods (using sample data for demonstration)
    def calculate_food_cost_percentage(self):
        """Calculate food cost as percentage of sales"""
        return 28.5  # Sample data
    
    def calculate_theoretical_food_cost(self):
        """Calculate theoretical food cost"""
        return 20500.00  # Sample data
    
    def calculate_actual_food_cost(self):
        """Calculate actual food cost"""
        return 21800.00  # Sample data
    
    def calculate_labor_cost_percentage(self):
        """Calculate labor cost as percentage of sales"""
        return 24.2  # Sample data
    
    def calculate_total_labor_cost(self):
        """Calculate total labor cost"""
        return 18200.00  # Sample data
    
    def calculate_total_labor_hours(self):
        """Calculate total labor hours"""
        return 1120.0  # Sample data
    
    def calculate_food_cost_variance(self):
        """Calculate food cost variance"""
        return 1300.00  # Sample data
    
    def calculate_labor_cost_variance(self):
        """Calculate labor cost variance"""
        return -800.00  # Sample data (negative = under budget)
    
    def calculate_overhead_variance(self):
        """Calculate overhead variance"""
        return 400.00  # Sample data
    
    # Action methods
    def calculate_recipe_cost(self):
        """Calculate cost for selected recipe"""
        recipe = self.recipe_var.get()
        if recipe:
            messagebox.showinfo("Recipe Cost", f"Calculating cost for: {recipe}")
        else:
            messagebox.showwarning("Warning", "Please select a recipe first.")
    
    def update_cost_trends(self):
        """Update cost trends for selected period"""
        period = self.period_var.get()
        messagebox.showinfo("Trends Updated", f"Cost trends updated for: {period}")

def create_cost_control_system(parent, db_manager):
    """Создать систему контроля затрат"""
    system = CostControlSystem(parent, db_manager)
    return system.create_cost_control_window()
