"""
Enhanced styles with bigger fonts and automatic text color selection
"""

from tkinter import ttk

class EnhancedStyles:
    """Enhanced styling with automatic text color selection"""
    
    # Enhanced font sizes with Cambria Bold Italic (increased by 5-6 points)
    FONTS = {
        'small': ('Cambria', 16, 'bold italic'),      # Was 12, now 16 bold italic
        'normal': ('Cambria', 18, 'bold italic'),     # Was 14, now 18 bold italic
        'medium': ('Cambria', 20, 'bold italic'),     # Was 16, now 20 bold italic
        'large': ('Cambria', 24, 'bold italic'),      # Was 18, now 24 bold italic
        'xlarge': ('Cambria', 28, 'bold italic'),     # Was 20, now 28 bold italic
        'title': ('Cambria', 32, 'bold italic'),      # Was 24, now 32 bold italic
        'header': ('Cambria', 36, 'bold italic'),     # Was 28, now 36 bold italic
        'button': ('Cambria', 18, 'bold italic'),     # Was 13, now 18 bold italic
        'menu': ('Cambria', 18, 'bold italic'),       # Was 13, now 18 bold italic
        'table': ('Cambria', 16, 'bold italic'),      # New for tables
        'table_header': ('Cambria', 18, 'bold italic'), # New for table headers
    }
    
    # Color palette
    COLORS = {
        'primary': '#2c3e50',      # Dark blue-gray
        'secondary': '#34495e',    # Lighter blue-gray
        'success': '#27ae60',      # Green
        'warning': '#f39c12',      # Orange
        'danger': '#e74c3c',       # Red
        'info': '#3498db',         # Blue
        'light': '#f8f9fa',        # Light gray
        'white': '#ffffff',        # White
        'dark': '#2c3e50',         # Dark
        'maroon': '#800000',       # Maroon for light backgrounds
        'purple': '#8e44ad',       # Purple
        'teal': '#16a085',         # Teal
    }
    
    # Dark backgrounds that need white text
    DARK_BACKGROUNDS = [
        '#2c3e50', '#34495e', '#27ae60', '#e74c3c', '#3498db', 
        '#8e44ad', '#16a085', '#f39c12', '#95a5a6', '#800000'
    ]
    
    # Light backgrounds that need maroon text
    LIGHT_BACKGROUNDS = [
        '#ffffff', '#f8f9fa', '#ecf0f1', '#e9ecef', '#dee2e6',
        '#ced4da', '#adb5bd', '#fdf2e9', '#eaf2f8', '#e8f5e8'
    ]
    
    @staticmethod
    def get_text_color(background_color):
        """
        Automatically determine text color based on background
        Returns 'white' for dark backgrounds, maroon for light backgrounds
        """
        bg_lower = background_color.lower()
        
        if bg_lower in [color.lower() for color in EnhancedStyles.DARK_BACKGROUNDS]:
            return 'white'
        elif bg_lower in [color.lower() for color in EnhancedStyles.LIGHT_BACKGROUNDS]:
            return EnhancedStyles.COLORS['maroon']
        else:
            # For unknown colors, use luminance calculation
            bg = background_color.lstrip('#')
            if len(bg) == 6:
                try:
                    r, g, b = tuple(int(bg[i:i+2], 16) for i in (0, 2, 4))
                    luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255
                    return 'white' if luminance < 0.5 else EnhancedStyles.COLORS['maroon']
                except ValueError:
                    return EnhancedStyles.COLORS['maroon']
            else:
                return EnhancedStyles.COLORS['maroon']  # Default to maroon
    
    @staticmethod
    def create_label(parent, text, bg_color, font_size='normal', bold=False, **kwargs):
        """Create label with automatic text color and enhanced font"""
        import tkinter as tk
        
        font_tuple = list(EnhancedStyles.FONTS[font_size])
        if bold and len(font_tuple) == 2:
            font_tuple.append('bold')
        elif bold and len(font_tuple) == 3 and font_tuple[2] != 'bold':
            font_tuple[2] = 'bold'
        
        text_color = EnhancedStyles.get_text_color(bg_color)
        
        return tk.Label(parent, text=text, bg=bg_color, fg=text_color, 
                       font=tuple(font_tuple), **kwargs)
    
    @staticmethod
    def create_button(parent, text, bg_color, command=None, font_size='button', **kwargs):
        """Create button with automatic text color and enhanced font"""
        import tkinter as tk
        
        text_color = EnhancedStyles.get_text_color(bg_color)
        
        return tk.Button(parent, text=text, bg=bg_color, fg=text_color,
                        font=EnhancedStyles.FONTS[font_size], command=command,
                        relief='flat', cursor='hand2', **kwargs)
    
    @staticmethod
    def style_label(label, bg_color, font_size='normal', bold=False):
        """Apply enhanced styling to existing label"""
        font_tuple = list(EnhancedStyles.FONTS[font_size])
        if bold and len(font_tuple) == 2:
            font_tuple.append('bold')
        elif bold and len(font_tuple) == 3 and font_tuple[2] != 'bold':
            font_tuple[2] = 'bold'
        
        text_color = EnhancedStyles.get_text_color(bg_color)
        
        label.configure(
            bg=bg_color,
            fg=text_color,
            font=tuple(font_tuple)
        )
    
    @staticmethod
    def style_button(button, bg_color, font_size='button'):
        """Apply enhanced styling to existing button"""
        text_color = EnhancedStyles.get_text_color(bg_color)
        
        button.configure(
            bg=bg_color,
            fg=text_color,
            font=EnhancedStyles.FONTS[font_size],
            relief='flat',
            cursor='hand2'
        )
    
    @staticmethod
    def style_entry(entry, font_size='normal'):
        """Apply enhanced font to entry widget"""
        entry.configure(font=EnhancedStyles.FONTS[font_size])
    
    @staticmethod
    def style_listbox(listbox, font_size='normal'):
        """Apply enhanced font to listbox widget"""
        listbox.configure(font=EnhancedStyles.FONTS[font_size])
    
    @staticmethod
    def style_text(text_widget, font_size='normal'):
        """Apply enhanced font to text widget"""
        text_widget.configure(font=EnhancedStyles.FONTS[font_size])

    @staticmethod
    def style_treeview(treeview, font_size='table'):
        """Apply enhanced font to treeview widget"""
        style = ttk.Style()
        style.configure("Enhanced.Treeview", font=EnhancedStyles.FONTS[font_size])
        style.configure("Enhanced.Treeview.Heading", font=EnhancedStyles.FONTS['table_header'])
        treeview.configure(style="Enhanced.Treeview")

    @staticmethod
    def configure_treeview_style():
        """Configure global treeview style with Cambria bold italic"""
        style = ttk.Style()
        style.configure("Treeview", font=EnhancedStyles.FONTS['table'])
        style.configure("Treeview.Heading", font=EnhancedStyles.FONTS['table_header'])
        style.configure("Treeview.Item", font=EnhancedStyles.FONTS['table'])

    @staticmethod
    def configure_combobox_style():
        """Configure global combobox style with Cambria bold italic"""
        style = ttk.Style()
        style.configure("TCombobox", font=EnhancedStyles.FONTS['normal'])
        style.map("TCombobox", font=[('readonly', EnhancedStyles.FONTS['normal'])])

    @staticmethod
    def configure_notebook_style():
        """Configure global notebook style with Cambria bold italic"""
        style = ttk.Style()
        style.configure("TNotebook.Tab", font=EnhancedStyles.FONTS['medium'])
        style.configure("TNotebook", font=EnhancedStyles.FONTS['medium'])
    
    # Quick access methods for common combinations
    @staticmethod
    def header_label(parent, text, **kwargs):
        """Create header label with automatic styling"""
        return EnhancedStyles.create_label(parent, text, EnhancedStyles.COLORS['primary'], 'header', **kwargs)
    
    @staticmethod
    def title_label(parent, text, bg_color='#ffffff', **kwargs):
        """Create title label with automatic styling"""
        return EnhancedStyles.create_label(parent, text, bg_color, 'title', **kwargs)
    
    @staticmethod
    def success_button(parent, text, command=None, **kwargs):
        """Create success button with automatic styling"""
        return EnhancedStyles.create_button(parent, text, EnhancedStyles.COLORS['success'], command, **kwargs)
    
    @staticmethod
    def danger_button(parent, text, command=None, **kwargs):
        """Create danger button with automatic styling"""
        return EnhancedStyles.create_button(parent, text, EnhancedStyles.COLORS['danger'], command, **kwargs)
    
    @staticmethod
    def primary_button(parent, text, command=None, **kwargs):
        """Create primary button with automatic styling"""
        return EnhancedStyles.create_button(parent, text, EnhancedStyles.COLORS['primary'], command, **kwargs)
    
    @staticmethod
    def secondary_button(parent, text, command=None, **kwargs):
        """Create secondary button with automatic styling"""
        return EnhancedStyles.create_button(parent, text, EnhancedStyles.COLORS['secondary'], command, **kwargs)
    
    # Common style combinations
    HEADER_STYLE = {
        'bg': COLORS['primary'],
        'fg': 'white',
        'font': FONTS['header']
    }
    
    CARD_STYLE = {
        'bg': COLORS['white'],
        'fg': COLORS['maroon'],
        'font': FONTS['normal']
    }
    
    BUTTON_STYLE = {
        'relief': 'flat',
        'cursor': 'hand2',
        'font': FONTS['button']
    }
