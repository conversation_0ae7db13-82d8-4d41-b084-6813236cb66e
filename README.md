# Restaurant Accounting & Inventory Management System

A comprehensive Python-based accounting and inventory management system designed specifically for restaurants. This system provides modern, user-friendly interfaces for managing sales data, inventory, purchasing, recipes, and generating detailed reports.

## 🌟 Features

### Core Functionality
- **Sales Data Management**: Import and manage daily sales data from CSV files
- **Inventory Tracking**: Real-time inventory management with stock level monitoring
- **Purchase Order Management**: Handle purchase orders with VAT and discount calculations
- **Recipe Management**: Manage recipes (technological cards) and semi-finished products
- **Unit Conversion**: Support for different measurement units
- **Continuous Inventory**: Ongoing inventory tracking with adjustment capabilities
- **Average Cost Tracking**: Track average prices for accurate cost management

### Advanced Features
- **Modern GUI**: Beautiful, responsive interface with modern color scheme
- **User Access Control**: Multi-level user permissions and authentication
- **Reporting & Analytics**: Comprehensive reports and data visualization
- **Data Import/Export**: CSV import/export functionality
- **Database Management**: SQLite database with proper schema design
- **Real-time Updates**: Live data updates and synchronization

## 🚀 Installation

### Prerequisites
- Python 3.8 or higher
- Windows, macOS, or Linux operating system

### Step 1: Clone or Download
```bash
git clone <repository-url>
cd Restaurant
```

### Step 2: Install Dependencies
```bash
pip install -r requirements.txt
```

### Step 3: Run the Application
```bash
python main.py
```

## 📊 CSV Import Format

The system expects CSV files with the following column structure (no headers):

| Column | Data | Description |
|--------|------|-------------|
| 1 | Order Date | Date of the order (YYYY-MM-DD or DD/MM/YYYY) |
| 4 | Quantity | Number of dishes ordered |
| 5 | Price per Dish | Unit price of the dish |
| 6 | Total | Total amount (Quantity × Price) |
| 8 | Department | Department (e.g., bar, kitchen) |
| 9 | Dish Name | Name of the dish |
| 10 | Order Number | Unique order identifier |
| 15 | Payment Method | Payment method (Cash, Credit Card, etc.) |
| 17 | Dish Code | Unique dish code |

### Sample CSV Data
```csv
2024-01-15,Table 1,Cash,2,15.50,31.00,Appetizer,Kitchen,Bruschetta,ORD001,Lunch,Main Course,Pasta,Carbonara,Credit Card,APP001,Starter
2024-01-15,Table 2,Credit Card,1,25.00,25.00,Main Course,Kitchen,Grilled Salmon,ORD002,Dinner,Seafood,Fish,Salmon,Cash,MAIN002,Entree
```

## 🎨 User Interface

### Modern Design Features
- **Color Scheme**: Professional restaurant-themed colors
- **Responsive Layout**: Adaptive interface that works on different screen sizes
- **Intuitive Navigation**: Easy-to-use sidebar navigation
- **Data Visualization**: Charts and graphs for better data understanding
- **Interactive Elements**: Hover effects and modern button styles

### Main Sections
1. **Dashboard**: Overview of key metrics and recent activity
2. **Sales Data**: View and manage imported sales records
3. **Import Sales**: Import new sales data from CSV files
4. **Inventory**: Manage raw materials and stock levels
5. **Purchasing**: Create and manage purchase orders
6. **Recipes**: Manage recipes and semi-finished products
7. **Reports**: Generate comprehensive reports and analytics
8. **Settings**: System configuration and user management

## 💾 Database Structure

The system uses SQLite database with the following main tables:

- **users**: User accounts and permissions
- **sales**: Sales transaction records
- **raw_materials**: Inventory items and stock levels
- **purchase_orders**: Purchase order headers
- **purchase_order_items**: Purchase order line items
- **recipes**: Recipe definitions
- **recipe_ingredients**: Recipe ingredient lists
- **semi_finished_products**: Semi-finished product inventory
- **inventory_adjustments**: Stock adjustment history
- **unit_conversions**: Unit conversion factors

## 🔧 Configuration

### Default Login
- **Username**: admin
- **Password**: admin123

### Database Location
The SQLite database file (`restaurant_system.db`) is created in the application directory.

### Configuration File
Settings are stored in `config.json` and include:
- Database settings
- UI preferences
- Business settings (currency, tax rates)
- Report preferences

## 📈 Usage Guide

### Importing Sales Data
1. Navigate to "Import Sales" section
2. Click "Browse" to select your CSV file
3. Click "Load Data" to preview the imported data
4. Review, edit, or delete records as needed
5. Click "Save to Database" to store the data

### Managing Inventory
1. Go to "Inventory" section
2. View current stock levels and low-stock alerts
3. Use "Add Item" to add new inventory items
4. Use "Adjust Stock" to update quantities
5. Monitor stock levels with color-coded indicators

### Creating Purchase Orders
1. Navigate to "Purchasing" section
2. Create new purchase orders
3. Add items with quantities and prices
4. Apply VAT and discounts as needed
5. Track order status and delivery

### Generating Reports
1. Access "Reports" section
2. Select report type and date range
3. Generate PDF or Excel reports
4. View charts and analytics
5. Export data for external use

## 🛠️ Development

### Project Structure
```
Restaurant/
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
├── README.md              # This file
├── sample_sales_data.csv  # Sample CSV data
├── database/
│   ├── db_manager.py      # Database operations
│   └── models.py          # Database schema
├── gui/
│   ├── main_window.py     # Main GUI window
│   └── styles.py          # UI styling and themes
├── modules/
│   ├── sales_import.py    # Sales data import
│   ├── inventory.py       # Inventory management
│   └── purchasing.py      # Purchase order management
└── utils/
    └── helpers.py         # Utility functions
```

### Adding New Features
1. Create new modules in the `modules/` directory
2. Add database tables in `database/models.py`
3. Update the main window navigation in `gui/main_window.py`
4. Follow the existing code style and patterns

## 🔒 Security Features

- Password-protected user accounts
- Role-based access control
- Data validation and sanitization
- Secure database operations
- Audit trail for inventory changes

## 📞 Support

For support, feature requests, or bug reports:
1. Check the documentation
2. Review the code comments
3. Create an issue in the repository
4. Contact the development team

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built with Python and Tkinter
- Uses SQLite for data storage
- Inspired by modern restaurant management needs
- Designed for ease of use and reliability

---

**Version**: 1.0.0  
**Last Updated**: January 2024  
**Compatibility**: Python 3.8+, Windows/macOS/Linux
