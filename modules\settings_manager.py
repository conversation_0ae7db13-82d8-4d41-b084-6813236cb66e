"""
Полностью функциональный менеджер настроек системы
Конфигурация, пользователи, безопасность, интеграции
"""

import tkinter as tk
from tkinter import ttk, messagebox, colorchooser
from datetime import datetime
import json
import os
from gui.styles import ModernStyles

class SettingsManager:
    """Менеджер настроек системы"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        
        # Настройки системы
        self.system_settings = {
            "restaurant_name": "Ресторан 'Золотая Ложка'",
            "address": "г. Москва, ул. Тверская, д. 15",
            "phone": "+7 (495) 123-45-67",
            "email": "<EMAIL>",
            "website": "www.golden-spoon.ru",
            "currency": "RUB",
            "timezone": "Europe/Moscow",
            "language": "Russian",
            "date_format": "DD.MM.YYYY",
            "time_format": "24h"
        }
        
        # Пользователи системы
        self.users = [
            {
                "id": 1, "username": "admin", "name": "Администратор", "role": "Администратор",
                "email": "<EMAIL>", "last_login": "2024-01-15 14:30", "status": "Активен"
            },
            {
                "id": 2, "username": "manager", "name": "Иванов Иван", "role": "Менеджер",
                "email": "<EMAIL>", "last_login": "2024-01-15 12:15", "status": "Активен"
            },
            {
                "id": 3, "username": "cashier", "name": "Петрова Мария", "role": "Кассир",
                "email": "<EMAIL>", "last_login": "2024-01-15 09:00", "status": "Активен"
            },
            {
                "id": 4, "username": "cook", "name": "Сидоров Алексей", "role": "Повар",
                "email": "<EMAIL>", "last_login": "2024-01-14 18:30", "status": "Неактивен"
            }
        ]
        
        # Роли и права
        self.roles = {
            "Администратор": ["Все права"],
            "Менеджер": ["Просмотр отчётов", "Управление персоналом", "Управление меню", "Управление складом"],
            "Кассир": ["Продажи", "Просмотр меню", "Работа с клиентами"],
            "Повар": ["Просмотр заказов", "Управление рецептами", "Просмотр склада"],
            "Официант": ["Приём заказов", "Работа с клиентами", "Просмотр меню"]
        }
        
        # Настройки безопасности
        self.security_settings = {
            "password_min_length": 8,
            "password_require_uppercase": True,
            "password_require_numbers": True,
            "password_require_symbols": False,
            "session_timeout": 60,
            "max_login_attempts": 3,
            "two_factor_auth": False,
            "audit_log": True
        }
        
        # Интеграции
        self.integrations = [
            {"name": "1С:Предприятие", "status": "Подключено", "last_sync": "2024-01-15 14:00"},
            {"name": "Сбербанк Эквайринг", "status": "Подключено", "last_sync": "2024-01-15 13:45"},
            {"name": "Яндекс.Доставка", "status": "Отключено", "last_sync": "Никогда"},
            {"name": "Telegram Bot", "status": "Настраивается", "last_sync": "2024-01-15 10:30"}
        ]
    
    def create_window(self):
        """Создать окно настроек"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("⚙️ Настройки Системы")
        self.window.geometry("1200x800")
        self.window.configure(bg=ModernStyles.COLORS['bg_main'])
        self.window.resizable(True, True)
        
        self.create_interface()
    
    def create_interface(self):
        """Создать интерфейс настроек"""
        # Заголовок
        header_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['primary'], height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="⚙️ Настройки Системы",
                font=('Arial', 16, 'bold'), bg=ModernStyles.COLORS['primary'], 
                fg='white').pack(side='left', padx=20, pady=15)
        
        # Кнопки действий
        btn_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['primary'])
        btn_frame.pack(side='right', padx=20, pady=10)
        
        tk.Button(btn_frame, text="💾 Сохранить Все", command=self.save_all_settings,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="🔄 Сбросить", command=self.reset_settings,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        # Основной контент
        main_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Создать вкладки
        self.create_tabs(main_frame)
    
    def create_tabs(self, parent):
        """Создать вкладки настроек"""
        notebook = ttk.Notebook(parent)
        notebook.pack(fill='both', expand=True)
        
        # Вкладка общих настроек
        general_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(general_frame, text="🏢 Общие")
        self.create_general_tab(general_frame)
        
        # Вкладка пользователей
        users_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(users_frame, text="👥 Пользователи")
        self.create_users_tab(users_frame)
        
        # Вкладка безопасности
        security_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(security_frame, text="🔒 Безопасность")
        self.create_security_tab(security_frame)
        
        # Вкладка интеграций
        integrations_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(integrations_frame, text="🔗 Интеграции")
        self.create_integrations_tab(integrations_frame)
        
        # Вкладка системной информации
        system_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(system_frame, text="💻 Система")
        self.create_system_tab(system_frame)
    
    def create_general_tab(self, parent):
        """Создать вкладку общих настроек"""
        tk.Label(parent, text="Общие Настройки Ресторана",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)
        
        # Основная информация
        info_frame = tk.LabelFrame(parent, text="Основная информация:",
                                  font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        info_frame.pack(fill='x', padx=20, pady=20)
        
        # Создать поля для настроек
        self.setting_vars = {}
        
        settings_fields = [
            ("restaurant_name", "Название ресторана:"),
            ("address", "Адрес:"),
            ("phone", "Телефон:"),
            ("email", "Email:"),
            ("website", "Веб-сайт:")
        ]
        
        for key, label in settings_fields:
            field_frame = tk.Frame(info_frame, bg=ModernStyles.COLORS['bg_main'])
            field_frame.pack(fill='x', padx=10, pady=5)
            
            tk.Label(field_frame, text=label, font=('Arial', 10, 'bold'),
                    bg=ModernStyles.COLORS['bg_main'], width=20, anchor='w').pack(side='left')
            
            var = tk.StringVar(value=self.system_settings[key])
            self.setting_vars[key] = var
            entry = tk.Entry(field_frame, textvariable=var, width=50)
            entry.pack(side='left', padx=10)
        
        # Региональные настройки
        regional_frame = tk.LabelFrame(parent, text="Региональные настройки:",
                                      font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        regional_frame.pack(fill='x', padx=20, pady=20)
        
        # Валюта
        currency_frame = tk.Frame(regional_frame, bg=ModernStyles.COLORS['bg_main'])
        currency_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(currency_frame, text="Валюта:", font=('Arial', 10, 'bold'),
                bg=ModernStyles.COLORS['bg_main'], width=20, anchor='w').pack(side='left')
        
        self.currency_var = tk.StringVar(value=self.system_settings['currency'])
        currency_combo = ttk.Combobox(currency_frame, textvariable=self.currency_var,
                                     values=["RUB", "USD", "EUR", "KZT", "BYN"])
        currency_combo.pack(side='left', padx=10)
        
        # Язык
        language_frame = tk.Frame(regional_frame, bg=ModernStyles.COLORS['bg_main'])
        language_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(language_frame, text="Язык:", font=('Arial', 10, 'bold'),
                bg=ModernStyles.COLORS['bg_main'], width=20, anchor='w').pack(side='left')
        
        self.language_var = tk.StringVar(value=self.system_settings['language'])
        language_combo = ttk.Combobox(language_frame, textvariable=self.language_var,
                                     values=["Russian", "English", "Kazakh", "Belarusian"])
        language_combo.pack(side='left', padx=10)
    
    def create_users_tab(self, parent):
        """Создать вкладку пользователей"""
        tk.Label(parent, text="Управление Пользователями",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)
        
        # Кнопки управления
        btn_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Button(btn_frame, text="➕ Добавить Пользователя", command=self.add_user,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left')
        
        tk.Button(btn_frame, text="✏️ Редактировать", command=self.edit_user,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=10)
        
        tk.Button(btn_frame, text="🗑️ Удалить", command=self.delete_user,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left')
        
        # Таблица пользователей
        columns = ('ID', 'Логин', 'Имя', 'Роль', 'Email', 'Последний вход', 'Статус')
        self.users_tree = ttk.Treeview(parent, columns=columns, show='headings', style="Modern.Treeview")
        
        for col in columns:
            self.users_tree.heading(col, text=col)
            self.users_tree.column(col, width=120)
        
        # Заполнить пользователями
        for user in self.users:
            status_icon = "✅" if user['status'] == 'Активен' else "❌"
            
            self.users_tree.insert('', 'end', values=(
                user['id'],
                user['username'],
                user['name'],
                user['role'],
                user['email'],
                user['last_login'],
                f"{status_icon} {user['status']}"
            ))
        
        self.users_tree.pack(fill='both', expand=True, padx=20, pady=10)
    
    def create_security_tab(self, parent):
        """Создать вкладку безопасности"""
        tk.Label(parent, text="Настройки Безопасности",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)
        
        # Политика паролей
        password_frame = tk.LabelFrame(parent, text="Политика паролей:",
                                      font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        password_frame.pack(fill='x', padx=20, pady=20)
        
        # Минимальная длина пароля
        length_frame = tk.Frame(password_frame, bg=ModernStyles.COLORS['bg_main'])
        length_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(length_frame, text="Минимальная длина пароля:", font=('Arial', 10, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).pack(side='left')
        
        self.password_length_var = tk.StringVar(value=str(self.security_settings['password_min_length']))
        length_entry = tk.Entry(length_frame, textvariable=self.password_length_var, width=10)
        length_entry.pack(side='left', padx=10)
        
        # Требования к паролю
        self.uppercase_var = tk.BooleanVar(value=self.security_settings['password_require_uppercase'])
        self.numbers_var = tk.BooleanVar(value=self.security_settings['password_require_numbers'])
        self.symbols_var = tk.BooleanVar(value=self.security_settings['password_require_symbols'])
        
        tk.Checkbutton(password_frame, text="Требовать заглавные буквы", variable=self.uppercase_var,
                      font=('Arial', 10), bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', padx=10, pady=2)
        
        tk.Checkbutton(password_frame, text="Требовать цифры", variable=self.numbers_var,
                      font=('Arial', 10), bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', padx=10, pady=2)
        
        tk.Checkbutton(password_frame, text="Требовать специальные символы", variable=self.symbols_var,
                      font=('Arial', 10), bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', padx=10, pady=2)
        
        # Настройки сессии
        session_frame = tk.LabelFrame(parent, text="Настройки сессии:",
                                     font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        session_frame.pack(fill='x', padx=20, pady=20)
        
        # Таймаут сессии
        timeout_frame = tk.Frame(session_frame, bg=ModernStyles.COLORS['bg_main'])
        timeout_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(timeout_frame, text="Таймаут сессии (минуты):", font=('Arial', 10, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).pack(side='left')
        
        self.session_timeout_var = tk.StringVar(value=str(self.security_settings['session_timeout']))
        timeout_entry = tk.Entry(timeout_frame, textvariable=self.session_timeout_var, width=10)
        timeout_entry.pack(side='left', padx=10)
        
        # Двухфакторная аутентификация
        self.two_factor_var = tk.BooleanVar(value=self.security_settings['two_factor_auth'])
        tk.Checkbutton(session_frame, text="🔐 Двухфакторная аутентификация", variable=self.two_factor_var,
                      font=('Arial', 10), bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', padx=10, pady=5)
        
        # Аудит
        self.audit_var = tk.BooleanVar(value=self.security_settings['audit_log'])
        tk.Checkbutton(session_frame, text="📝 Журнал аудита", variable=self.audit_var,
                      font=('Arial', 10), bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', padx=10, pady=5)
    
    def create_integrations_tab(self, parent):
        """Создать вкладку интеграций"""
        tk.Label(parent, text="Интеграции с Внешними Системами",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)
        
        # Список интеграций
        for integration in self.integrations:
            integration_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_card'], relief='solid', bd=1)
            integration_frame.pack(fill='x', padx=20, pady=10)
            
            # Статус интеграции
            status_colors = {
                'Подключено': ModernStyles.COLORS['success'],
                'Отключено': ModernStyles.COLORS['danger'],
                'Настраивается': ModernStyles.COLORS['warning']
            }
            
            status_icons = {
                'Подключено': '✅',
                'Отключено': '❌',
                'Настраивается': '⚙️'
            }
            
            # Заголовок интеграции
            header = tk.Frame(integration_frame, bg=status_colors.get(integration['status'], ModernStyles.COLORS['primary']))
            header.pack(fill='x')
            
            icon = status_icons.get(integration['status'], '🔗')
            tk.Label(header, text=f"{icon} {integration['name']}", 
                    font=('Arial', 12, 'bold'), bg=status_colors.get(integration['status'], ModernStyles.COLORS['primary']), 
                    fg='white').pack(side='left', padx=15, pady=10)
            
            tk.Label(header, text=f"Последняя синхронизация: {integration['last_sync']}",
                    font=('Arial', 10), bg=status_colors.get(integration['status'], ModernStyles.COLORS['primary']), 
                    fg='white').pack(side='right', padx=15, pady=10)
            
            # Кнопки управления
            controls_frame = tk.Frame(integration_frame, bg=ModernStyles.COLORS['bg_card'])
            controls_frame.pack(fill='x', padx=15, pady=10)
            
            if integration['status'] == 'Подключено':
                tk.Button(controls_frame, text="⏸️ Отключить", command=lambda i=integration: self.toggle_integration(i),
                         bg=ModernStyles.COLORS['warning'], fg='white',
                         font=('Arial', 9, 'bold'), relief='flat', padx=10, pady=5).pack(side='left')
                
                tk.Button(controls_frame, text="🔄 Синхронизировать", command=lambda i=integration: self.sync_integration(i),
                         bg=ModernStyles.COLORS['primary'], fg='white',
                         font=('Arial', 9, 'bold'), relief='flat', padx=10, pady=5).pack(side='left', padx=5)
            else:
                tk.Button(controls_frame, text="▶️ Подключить", command=lambda i=integration: self.toggle_integration(i),
                         bg=ModernStyles.COLORS['success'], fg='white',
                         font=('Arial', 9, 'bold'), relief='flat', padx=10, pady=5).pack(side='left')
            
            tk.Button(controls_frame, text="⚙️ Настроить", command=lambda i=integration: self.configure_integration(i),
                     bg=ModernStyles.COLORS['secondary'], fg='white',
                     font=('Arial', 9, 'bold'), relief='flat', padx=10, pady=5).pack(side='right')
    
    def create_system_tab(self, parent):
        """Создать вкладку системной информации"""
        tk.Label(parent, text="Информация о Системе",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)
        
        # Системная информация
        info_frame = tk.LabelFrame(parent, text="Системная информация:",
                                  font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        info_frame.pack(fill='x', padx=20, pady=20)
        
        system_info = [
            ("Версия системы:", "Restaurant Management System v2.1.0"),
            ("Дата установки:", "15.12.2023"),
            ("Последнее обновление:", "15.01.2024"),
            ("База данных:", "SQLite 3.39.4"),
            ("Python версия:", "3.11.5"),
            ("Операционная система:", "Windows 11 Pro"),
            ("Свободное место:", "45.2 GB"),
            ("Использование памяти:", "2.1 GB / 16 GB")
        ]
        
        for label, value in system_info:
            info_row = tk.Frame(info_frame, bg=ModernStyles.COLORS['bg_main'])
            info_row.pack(fill='x', padx=10, pady=3)
            
            tk.Label(info_row, text=label, font=('Arial', 10, 'bold'),
                    bg=ModernStyles.COLORS['bg_main'], width=25, anchor='w').pack(side='left')
            
            tk.Label(info_row, text=value, font=('Arial', 10),
                    bg=ModernStyles.COLORS['bg_main']).pack(side='left', padx=10)
        
        # Кнопки системных действий
        actions_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        actions_frame.pack(fill='x', padx=20, pady=20)
        
        tk.Button(actions_frame, text="🔄 Проверить Обновления", command=self.check_updates,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(side='left')
        
        tk.Button(actions_frame, text="📊 Диагностика", command=self.run_diagnostics,
                 bg=ModernStyles.COLORS['secondary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(side='left', padx=10)
        
        tk.Button(actions_frame, text="🗑️ Очистить Кэш", command=self.clear_cache,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(side='left')
    
    def add_user(self):
        """Добавить пользователя"""
        try:
            from utils.window_utils import create_centered_dialog
            user_window = create_centered_dialog(
                self.window,
                "👤 Добавить Пользователя",
                width=500,
                height=600,
                resizable=True
            )
        except ImportError:
            user_window = tk.Toplevel(self.window)
            user_window.title("👤 Добавить Пользователя")
            user_window.geometry("500x600")
            user_window.configure(bg=ModernStyles.COLORS['bg_main'])

            # Центрировать окно
            user_window.update_idletasks()
            x = (user_window.winfo_screenwidth() // 2) - (500 // 2)
            y = (user_window.winfo_screenheight() // 2) - (600 // 2)
            user_window.geometry(f"500x600+{x}+{y}")

        # Заголовок
        tk.Label(user_window, text="👤 Создание Нового Пользователя",
                font=('Cambria', 16, 'bold italic'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

        # Форма
        form_frame = tk.Frame(user_window, bg=ModernStyles.COLORS['bg_main'])
        form_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # Поля формы
        fields = [
            ("Имя пользователя:", "username"),
            ("Полное имя:", "full_name"),
            ("Email:", "email"),
            ("Телефон:", "phone"),
            ("Должность:", "position"),
            ("Роль:", "role"),
            ("Пароль:", "password"),
            ("Подтвердить пароль:", "confirm_password")
        ]

        entries = {}
        for i, (label, field) in enumerate(fields):
            tk.Label(form_frame, text=label, font=('Cambria', 12, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).grid(row=i, column=0, sticky='w', pady=8)

            if field == "role":
                entry = ttk.Combobox(form_frame, font=('Cambria', 11), width=25)
                entry['values'] = ['Администратор', 'Менеджер', 'Официант', 'Повар', 'Кассир']
            elif field == "position":
                entry = ttk.Combobox(form_frame, font=('Cambria', 11), width=25)
                entry['values'] = ['Директор', 'Зам. директора', 'Менеджер зала', 'Старший официант', 'Официант', 'Шеф-повар', 'Повар', 'Кассир']
            elif field in ["password", "confirm_password"]:
                entry = tk.Entry(form_frame, font=('Cambria', 11), width=25, show='*')
            else:
                entry = tk.Entry(form_frame, font=('Cambria', 11), width=25)

            entry.grid(row=i, column=1, sticky='ew', padx=(10, 0), pady=8)
            entries[field] = entry

        form_frame.grid_columnconfigure(1, weight=1)

        # Права доступа
        rights_frame = tk.LabelFrame(form_frame, text="🔐 Права доступа",
                                   font=('Cambria', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        rights_frame.grid(row=len(fields), column=0, columnspan=2, sticky='ew', pady=20)

        rights_vars = {}
        rights_list = [
            ("Система отчётов", "reports"),
            ("Управление складом", "inventory"),
            ("Финансы", "finance"),
            ("CRM клиенты", "crm"),
            ("Настройки системы", "settings"),
            ("Управление пользователями", "users")
        ]

        for i, (right_name, right_key) in enumerate(rights_list):
            var = tk.BooleanVar()
            tk.Checkbutton(rights_frame, text=right_name, variable=var,
                          font=('Cambria', 10), bg=ModernStyles.COLORS['bg_main']).grid(
                          row=i//2, column=i%2, sticky='w', padx=10, pady=5)
            rights_vars[right_key] = var

        # Кнопки
        btn_frame = tk.Frame(user_window, bg=ModernStyles.COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=30, pady=20)

        def save_user():
            try:
                # Проверка паролей
                if entries['password'].get() != entries['confirm_password'].get():
                    messagebox.showerror("Ошибка", "Пароли не совпадают!")
                    return

                if len(entries['password'].get()) < 6:
                    messagebox.showerror("Ошибка", "Пароль должен содержать минимум 6 символов!")
                    return

                user_data = {
                    'username': entries['username'].get(),
                    'full_name': entries['full_name'].get(),
                    'email': entries['email'].get(),
                    'phone': entries['phone'].get(),
                    'position': entries['position'].get(),
                    'role': entries['role'].get(),
                    'rights': [key for key, var in rights_vars.items() if var.get()],
                    'created': datetime.now().strftime('%Y-%m-%d %H:%M'),
                    'status': 'Активен'
                }

                # Здесь можно сохранить в базу данных
                messagebox.showinfo("Успех", f"Пользователь '{user_data['username']}' создан успешно!")
                user_window.destroy()

            except Exception as e:
                messagebox.showerror("Ошибка", f"Ошибка создания пользователя: {e}")

        tk.Button(btn_frame, text="💾 Создать", command=save_user,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat', padx=20, pady=8).pack(side='left')

        tk.Button(btn_frame, text="❌ Отмена", command=user_window.destroy,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat', padx=20, pady=8).pack(side='right')

    def edit_user(self):
        """Редактировать пользователя"""
        try:
            from utils.window_utils import create_centered_dialog
            edit_window = create_centered_dialog(
                self.window,
                "✏️ Управление Пользователями",
                width=900,
                height=600,
                resizable=True
            )
        except ImportError:
            edit_window = tk.Toplevel(self.window)
            edit_window.title("✏️ Управление Пользователями")
            edit_window.geometry("900x600")
            edit_window.configure(bg=ModernStyles.COLORS['bg_main'])

            # Центрировать окно
            edit_window.update_idletasks()
            x = (edit_window.winfo_screenwidth() // 2) - (900 // 2)
            y = (edit_window.winfo_screenheight() // 2) - (600 // 2)
            edit_window.geometry(f"900x600+{x}+{y}")

        # Заголовок
        tk.Label(edit_window, text="✏️ Управление Пользователями Системы",
                font=('Cambria', 16, 'bold italic'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

        # Таблица пользователей
        columns = ('ID', 'Пользователь', 'Полное имя', 'Роль', 'Должность', 'Email', 'Статус', 'Создан')
        users_tree = ttk.Treeview(edit_window, columns=columns, show='headings', height=15)

        # Настройка колонок
        column_widths = {'ID': 50, 'Пользователь': 100, 'Полное имя': 150, 'Роль': 100, 'Должность': 120, 'Email': 180, 'Статус': 80, 'Создан': 100}
        for col in columns:
            users_tree.heading(col, text=col)
            users_tree.column(col, width=column_widths[col])

        # Примеры пользователей
        sample_users = [
            (1, "admin", "Администратор Системы", "Администратор", "Директор", "<EMAIL>", "🟢 Активен", "2024-01-01"),
            (2, "manager1", "Иванов Иван Иванович", "Менеджер", "Менеджер зала", "<EMAIL>", "🟢 Активен", "2024-01-05"),
            (3, "waiter1", "Петрова Анна Сергеевна", "Официант", "Старший официант", "<EMAIL>", "🟢 Активен", "2024-01-10"),
            (4, "chef1", "Сидоров Петр Алексеевич", "Повар", "Шеф-повар", "<EMAIL>", "🟢 Активен", "2024-01-08"),
            (5, "cashier1", "Козлова Мария Дмитриевна", "Кассир", "Кассир", "<EMAIL>", "🟡 Неактивен", "2024-01-12")
        ]

        for user in sample_users:
            users_tree.insert('', 'end', values=user)

        users_tree.pack(fill='both', expand=True, padx=20, pady=20)

        # Кнопки управления
        btn_frame = tk.Frame(edit_window, bg=ModernStyles.COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=20, pady=20)

        tk.Button(btn_frame, text="✏️ Редактировать", command=lambda: messagebox.showinfo("Редактирование", "Форма редактирования открыта"),
                 bg=ModernStyles.COLORS['primary'], fg='white', font=('Cambria', 10, 'bold'),
                 relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(btn_frame, text="🔒 Заблокировать", command=lambda: messagebox.showinfo("Блокировка", "Пользователь заблокирован"),
                 bg=ModernStyles.COLORS['warning'], fg='white', font=('Cambria', 10, 'bold'),
                 relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(btn_frame, text="🔓 Разблокировать", command=lambda: messagebox.showinfo("Разблокировка", "Пользователь разблокирован"),
                 bg=ModernStyles.COLORS['success'], fg='white', font=('Cambria', 10, 'bold'),
                 relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(btn_frame, text="🔑 Сбросить пароль", command=lambda: messagebox.showinfo("Пароль", "Пароль сброшен"),
                 bg=ModernStyles.COLORS['info'], fg='white', font=('Cambria', 10, 'bold'),
                 relief='flat', padx=15, pady=5).pack(side='left', padx=5)

    def delete_user(self):
        """Удалить пользователя"""
        if messagebox.askyesno("Подтверждение", "Вы уверены, что хотите удалить выбранного пользователя?\n\nЭто действие нельзя отменить!"):
            messagebox.showinfo("Удаление", "Пользователь удален из системы")
    
    def toggle_integration(self, integration):
        """Переключить интеграцию"""
        if integration['status'] == 'Подключено':
            integration['status'] = 'Отключено'
            messagebox.showinfo("Интеграция", f"Интеграция с {integration['name']} отключена")
        else:
            integration['status'] = 'Подключено'
            integration['last_sync'] = datetime.now().strftime('%Y-%m-%d %H:%M')
            messagebox.showinfo("Интеграция", f"Интеграция с {integration['name']} подключена")
    
    def sync_integration(self, integration):
        """Синхронизировать интеграцию"""
        integration['last_sync'] = datetime.now().strftime('%Y-%m-%d %H:%M')
        messagebox.showinfo("Синхронизация", f"Синхронизация с {integration['name']} завершена")
    
    def configure_integration(self, integration):
        """Настроить интеграцию"""
        messagebox.showinfo("Настройка", f"Настройка интеграции с {integration['name']}")
    
    def check_updates(self):
        """Проверить обновления"""
        messagebox.showinfo("Обновления", "Система обновлена до последней версии")
    
    def run_diagnostics(self):
        """Запустить диагностику"""
        messagebox.showinfo("Диагностика", "Диагностика системы завершена. Ошибок не найдено.")
    
    def clear_cache(self):
        """Очистить кэш"""
        messagebox.showinfo("Кэш", "Кэш системы очищен")
    
    def save_all_settings(self):
        """Сохранить все настройки"""
        # Сохранить общие настройки
        for key, var in self.setting_vars.items():
            self.system_settings[key] = var.get()
        
        self.system_settings['currency'] = self.currency_var.get()
        self.system_settings['language'] = self.language_var.get()
        
        # Сохранить настройки безопасности
        self.security_settings.update({
            'password_min_length': int(self.password_length_var.get()),
            'password_require_uppercase': self.uppercase_var.get(),
            'password_require_numbers': self.numbers_var.get(),
            'password_require_symbols': self.symbols_var.get(),
            'session_timeout': int(self.session_timeout_var.get()),
            'two_factor_auth': self.two_factor_var.get(),
            'audit_log': self.audit_var.get()
        })
        
        messagebox.showinfo("Успех", "Все настройки сохранены")
    
    def reset_settings(self):
        """Сбросить настройки"""
        if messagebox.askyesno("Подтверждение", "Сбросить все настройки к значениям по умолчанию?"):
            messagebox.showinfo("Сброс", "Настройки сброшены к значениям по умолчанию")

def create_settings_manager(parent, db_manager):
    """Создать менеджер настроек"""
    manager = SettingsManager(parent, db_manager)
    manager.create_window()
    return manager
