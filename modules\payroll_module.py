"""
Полностью функциональный модуль расчёта зарплаты
Управление персоналом, расчёт зарплат, табель учёта рабочего времени
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import json
import os
from gui.styles import ModernStyles

class PayrollModule:
    """Функциональный модуль зарплаты"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        
        # База данных сотрудников
        self.employees = [
            {
                "id": 1, "name": "Иванов Иван Иванович", "position": "Повар",
                "hourly_rate": 350, "hours_worked": 160, "bonus": 5000,
                "deductions": 2000, "hire_date": "2023-01-15"
            },
            {
                "id": 2, "name": "Петрова Мария Сергеевна", "position": "Официант",
                "hourly_rate": 280, "hours_worked": 168, "bonus": 3000,
                "deductions": 1500, "hire_date": "2023-03-20"
            },
            {
                "id": 3, "name": "Сидоров Алексей Петрович", "position": "Администратор",
                "hourly_rate": 400, "hours_worked": 176, "bonus": 8000,
                "deductions": 3000, "hire_date": "2022-11-10"
            },
            {
                "id": 4, "name": "Козлова Елена Викторовна", "position": "Кассир",
                "hourly_rate": 300, "hours_worked": 160, "bonus": 2000,
                "deductions": 1200, "hire_date": "2023-05-05"
            },
            {
                "id": 5, "name": "Морозов Дмитрий Андреевич", "position": "Помощник повара",
                "hourly_rate": 250, "hours_worked": 152, "bonus": 1500,
                "deductions": 800, "hire_date": "2023-08-12"
            }
        ]
        
        # Табель учёта рабочего времени
        self.timesheet = {}
        self.generate_sample_timesheet()
    
    def generate_sample_timesheet(self):
        """Генерировать примерный табель"""
        for emp in self.employees:
            self.timesheet[emp['id']] = {
                "days_worked": 22,
                "overtime_hours": 8,
                "sick_days": 1,
                "vacation_days": 0
            }
    
    def create_window(self):
        """Создать окно модуля зарплаты"""
        try:
            from utils.window_utils import create_centered_dialog
            self.window = create_centered_dialog(
                self.parent,
                "💵 Модуль Расчёта Зарплаты",
                width=1400,
                height=950,
                resizable=True
            )
        except ImportError:
            # Fallback если utils не доступны
            self.window = tk.Toplevel(self.parent)
            self.window.title("💵 Модуль Расчёта Зарплаты")
            self.window.geometry("1400x950")
            self.window.configure(bg=ModernStyles.COLORS['bg_main'])
            self.window.resizable(True, True)

            # Центрировать окно
            self.window.update_idletasks()
            x = (self.window.winfo_screenwidth() // 2) - (1400 // 2)
            y = (self.window.winfo_screenheight() // 2) - (950 // 2)
            self.window.geometry(f"1400x950+{x}+{y}")
        
        self.create_interface()
    
    def create_interface(self):
        """Создать интерфейс модуля зарплаты"""
        # Заголовок
        header_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['primary'], height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="💵 Модуль Расчёта Зарплаты",
                font=('Arial', 16, 'bold'), bg=ModernStyles.COLORS['primary'], 
                fg='white').pack(side='left', padx=20, pady=15)
        
        # Кнопки действий
        btn_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['primary'])
        btn_frame.pack(side='right', padx=20, pady=10)
        
        tk.Button(btn_frame, text="👤 Добавить Сотрудника", command=self.add_employee,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="💰 Рассчитать Зарплаты", command=self.calculate_payroll,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="📊 Отчёт", command=self.generate_payroll_report,
                 bg=ModernStyles.COLORS['secondary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        # Основной контент
        main_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Создать вкладки
        self.create_tabs(main_frame)
    
    def create_tabs(self, parent):
        """Создать вкладки модуля"""
        notebook = ttk.Notebook(parent)
        notebook.pack(fill='both', expand=True)
        
        # Вкладка сотрудников
        employees_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(employees_frame, text="👥 Сотрудники")
        self.create_employees_tab(employees_frame)
        
        # Вкладка табеля
        timesheet_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(timesheet_frame, text="📅 Табель")
        self.create_timesheet_tab(timesheet_frame)
        
        # Вкладка расчёта зарплаты
        payroll_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(payroll_frame, text="💰 Расчёт Зарплаты")
        self.create_payroll_tab(payroll_frame)
    
    def create_employees_tab(self, parent):
        """Создать вкладку сотрудников"""
        # Заголовок
        tk.Label(parent, text="База Данных Сотрудников",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)
        
        # Таблица сотрудников
        columns = ('ID', 'ФИО', 'Должность', 'Ставка/час', 'Дата найма')
        self.employees_tree = ttk.Treeview(parent, columns=columns, show='headings', style="Modern.Treeview")
        
        # Настройка столбцов
        column_widths = {'ID': 50, 'ФИО': 250, 'Должность': 150, 'Ставка/час': 100, 'Дата найма': 120}
        for col in columns:
            self.employees_tree.heading(col, text=col)
            self.employees_tree.column(col, width=column_widths.get(col, 100))
        
        # Заполнить данными
        self.refresh_employees()
        
        # Скроллбары
        emp_v_scroll = ttk.Scrollbar(parent, orient='vertical', command=self.employees_tree.yview)
        emp_h_scroll = ttk.Scrollbar(parent, orient='horizontal', command=self.employees_tree.xview)
        self.employees_tree.configure(yscrollcommand=emp_v_scroll.set, xscrollcommand=emp_h_scroll.set)
        
        self.employees_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        emp_v_scroll.pack(side='right', fill='y')
        emp_h_scroll.pack(side='bottom', fill='x')
        
        # Кнопки управления
        emp_btn_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        emp_btn_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Button(emp_btn_frame, text="✏️ Редактировать", command=self.edit_employee,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        tk.Button(emp_btn_frame, text="🗑️ Удалить", command=self.delete_employee,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
    
    def create_timesheet_tab(self, parent):
        """Создать вкладку табеля"""
        # Заголовок
        tk.Label(parent, text="Табель Учёта Рабочего Времени",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)
        
        # Период
        period_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        period_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Label(period_frame, text="Период:", font=('Arial', 12, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).pack(side='left')
        tk.Label(period_frame, text=f"Январь 2024", font=('Arial', 12),
                bg=ModernStyles.COLORS['bg_main']).pack(side='left', padx=10)
        
        # Таблица табеля
        columns = ('ФИО', 'Должность', 'Дни', 'Переработка', 'Больничные', 'Отпуск')
        timesheet_tree = ttk.Treeview(parent, columns=columns, show='headings', style="Modern.Treeview")
        
        for col in columns:
            timesheet_tree.heading(col, text=col)
            timesheet_tree.column(col, width=150)
        
        # Заполнить табель
        for emp in self.employees:
            timesheet_data = self.timesheet.get(emp['id'], {})
            timesheet_tree.insert('', 'end', values=(
                emp['name'],
                emp['position'],
                timesheet_data.get('days_worked', 0),
                timesheet_data.get('overtime_hours', 0),
                timesheet_data.get('sick_days', 0),
                timesheet_data.get('vacation_days', 0)
            ))
        
        timesheet_tree.pack(fill='both', expand=True, padx=10, pady=10)
    
    def create_payroll_tab(self, parent):
        """Создать вкладку расчёта зарплаты"""
        # Заголовок
        tk.Label(parent, text="Расчёт Заработной Платы",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)
        
        # Таблица расчёта
        columns = ('ФИО', 'Оклад', 'Премия', 'Удержания', 'К выплате')
        self.payroll_tree = ttk.Treeview(parent, columns=columns, show='headings', style="Modern.Treeview")
        
        for col in columns:
            self.payroll_tree.heading(col, text=col)
            self.payroll_tree.column(col, width=150)
        
        # Рассчитать и заполнить
        self.refresh_payroll()
        
        self.payroll_tree.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Итоги
        totals_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_card'], relief='solid', bd=1)
        totals_frame.pack(fill='x', padx=10, pady=10)
        
        total_salary = sum(emp['hourly_rate'] * emp['hours_worked'] for emp in self.employees)
        total_bonus = sum(emp['bonus'] for emp in self.employees)
        total_deductions = sum(emp['deductions'] for emp in self.employees)
        total_payout = total_salary + total_bonus - total_deductions
        
        tk.Label(totals_frame, text=f"Общий фонд оплаты труда: {total_payout:,}₽",
                font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_card']).pack(pady=10)
    
    def refresh_employees(self):
        """Обновить список сотрудников"""
        for item in self.employees_tree.get_children():
            self.employees_tree.delete(item)
        
        for emp in self.employees:
            self.employees_tree.insert('', 'end', values=(
                emp['id'],
                emp['name'],
                emp['position'],
                f"{emp['hourly_rate']}₽",
                emp['hire_date']
            ))
    
    def refresh_payroll(self):
        """Обновить расчёт зарплаты"""
        for item in self.payroll_tree.get_children():
            self.payroll_tree.delete(item)
        
        for emp in self.employees:
            base_salary = emp['hourly_rate'] * emp['hours_worked']
            total_payout = base_salary + emp['bonus'] - emp['deductions']
            
            self.payroll_tree.insert('', 'end', values=(
                emp['name'],
                f"{base_salary:,}₽",
                f"{emp['bonus']:,}₽",
                f"{emp['deductions']:,}₽",
                f"{total_payout:,}₽"
            ))
    
    def add_employee(self):
        """Добавить нового сотрудника"""
        try:
            from utils.window_utils import create_centered_dialog
            emp_window = create_centered_dialog(
                self.window,
                "👤 Новый Сотрудник",
                width=700,
                height=550,
                resizable=True
            )
        except ImportError:
            # Fallback если utils не доступны
            emp_window = tk.Toplevel(self.window)
            emp_window.title("👤 Новый Сотрудник")
            emp_window.geometry("700x550")
            emp_window.configure(bg=ModernStyles.COLORS['bg_main'])
            emp_window.resizable(True, True)

            # Центрировать окно
            emp_window.update_idletasks()
            x = (emp_window.winfo_screenwidth() // 2) - (700 // 2)
            y = (emp_window.winfo_screenheight() // 2) - (550 // 2)
            emp_window.geometry(f"700x550+{x}+{y}")
        
        # Заголовок
        tk.Label(emp_window, text="Добавить Сотрудника",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)
        
        # Форма
        form_frame = tk.Frame(emp_window, bg=ModernStyles.COLORS['bg_main'])
        form_frame.pack(fill='both', expand=True, padx=20)
        
        # Поля формы
        fields = [
            ("ФИО:", "name"),
            ("Должность:", "position"),
            ("Ставка в час:", "hourly_rate"),
            ("Дата найма:", "hire_date")
        ]
        
        entries = {}
        for i, (label, field) in enumerate(fields):
            tk.Label(form_frame, text=label, font=('Arial', 10, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).grid(row=i, column=0, sticky='w', pady=5)
            entry = tk.Entry(form_frame, font=('Arial', 10))
            entry.grid(row=i, column=1, sticky='ew', padx=(10, 0), pady=5)
            entries[field] = entry
        
        # Предзаполнить дату
        entries['hire_date'].insert(0, datetime.now().strftime("%Y-%m-%d"))
        
        form_frame.grid_columnconfigure(1, weight=1)
        
        # Кнопки
        btn_frame = tk.Frame(emp_window, bg=ModernStyles.COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=20, pady=20)
        
        def save_employee():
            try:
                new_emp = {
                    "id": max(emp['id'] for emp in self.employees) + 1,
                    "name": entries['name'].get(),
                    "position": entries['position'].get(),
                    "hourly_rate": float(entries['hourly_rate'].get()),
                    "hours_worked": 0,
                    "bonus": 0,
                    "deductions": 0,
                    "hire_date": entries['hire_date'].get()
                }
                
                self.employees.append(new_emp)
                self.refresh_employees()
                emp_window.destroy()
                messagebox.showinfo("Успех", "Сотрудник добавлен")
                
            except Exception as e:
                messagebox.showerror("Ошибка", f"Ошибка добавления сотрудника: {e}")
        
        tk.Button(btn_frame, text="💾 Сохранить", command=save_employee,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(side='left')
        
        tk.Button(btn_frame, text="❌ Отмена", command=emp_window.destroy,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(side='right')
    
    def edit_employee(self):
        """Редактировать сотрудника"""
        selection = self.employees_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите сотрудника для редактирования")
            return
        
        messagebox.showinfo("В разработке", "Функция редактирования будет добавлена в следующей версии")
    
    def delete_employee(self):
        """Удалить сотрудника"""
        selection = self.employees_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите сотрудника для удаления")
            return
        
        if messagebox.askyesno("Подтверждение", "Удалить выбранного сотрудника?"):
            messagebox.showinfo("В разработке", "Функция удаления будет добавлена в следующей версии")
    
    def calculate_payroll(self):
        """Рассчитать зарплаты"""
        self.refresh_payroll()
        messagebox.showinfo("Расчёт", "Зарплаты рассчитаны успешно!")
    
    def generate_payroll_report(self):
        """Генерировать отчёт по зарплате"""
        report_window = tk.Toplevel(self.window)
        report_window.title("📊 Отчёт по Зарплате")
        report_window.geometry("600x500")
        report_window.configure(bg='white')
        
        # Заголовок
        tk.Label(report_window, text="📊 Отчёт по Заработной Плате",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)
        
        # Содержимое отчёта
        report_text = tk.Text(report_window, font=('Arial', 10), wrap='word')
        report_text.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Генерировать отчёт
        total_salary = sum(emp['hourly_rate'] * emp['hours_worked'] for emp in self.employees)
        total_bonus = sum(emp['bonus'] for emp in self.employees)
        total_deductions = sum(emp['deductions'] for emp in self.employees)
        total_payout = total_salary + total_bonus - total_deductions
        
        report_content = f"""
ОТЧЁТ ПО ЗАРАБОТНОЙ ПЛАТЕ
========================

Период: Январь 2024
Дата формирования: {datetime.now().strftime('%d.%m.%Y %H:%M')}

СВОДКА:
• Количество сотрудников: {len(self.employees)}
• Общий фонд оплаты труда: {total_payout:,}₽
• Базовая зарплата: {total_salary:,}₽
• Премии: {total_bonus:,}₽
• Удержания: {total_deductions:,}₽

ДЕТАЛИЗАЦИЯ ПО СОТРУДНИКАМ:
"""
        
        for emp in self.employees:
            base_salary = emp['hourly_rate'] * emp['hours_worked']
            payout = base_salary + emp['bonus'] - emp['deductions']
            report_content += f"""
{emp['name']} ({emp['position']}):
  • Базовая зарплата: {base_salary:,}₽
  • Премия: {emp['bonus']:,}₽
  • Удержания: {emp['deductions']:,}₽
  • К выплате: {payout:,}₽
"""
        
        report_text.insert('1.0', report_content)
        report_text.config(state='disabled')

def create_payroll_module(parent, db_manager):
    """Создать модуль зарплаты"""
    module = PayrollModule(parent, db_manager)
    module.create_window()
    return module
