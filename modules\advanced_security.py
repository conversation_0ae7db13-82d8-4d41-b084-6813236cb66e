"""
Advanced Security and Compliance Features Module
Implements two-factor authentication, role-based permissions, audit trails, 
data encryption, and compliance reporting for food safety regulations.
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import hashlib
import secrets
import json
import qrcode
import pyotp
from datetime import datetime, timedelta
import threading
from PIL import Image, ImageTk
import io
import base64
import os
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

# Import utility functions
try:
    from utils.error_handler import handle_module_error
    from utils.logger import log_info, log_error
    from utils.database_manager import DatabaseManager
except ImportError:
    # Fallback error handling
    def handle_module_error(error, module_name, operation):
        print(f"Error in {module_name} during {operation}: {str(error)}")
    
    def log_info(message, module_name):
        print(f"INFO [{module_name}]: {message}")
    
    def log_error(message, module_name):
        print(f"ERROR [{module_name}]: {message}")


class AdvancedSecurityManager:
    """Manager for Advanced Security and Compliance Features"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.current_frame = None
        
        # Security configurations
        self.security_config = {
            'password_policy': {
                'min_length': 8,
                'require_uppercase': True,
                'require_lowercase': True,
                'require_numbers': True,
                'require_special': True,
                'max_age_days': 90
            },
            'session_config': {
                'timeout_minutes': 30,
                'max_concurrent_sessions': 3,
                'require_2fa': False
            },
            'audit_config': {
                'log_all_actions': True,
                'retention_days': 365,
                'alert_on_suspicious': True
            }
        }
        
        # Initialize encryption
        self.encryption_key = None
        self.init_encryption()
        
        # Initialize database tables
        self.init_security_tables()
    
    def init_encryption(self):
        """Initialize encryption system"""
        try:
            # Generate or load encryption key
            key_file = "security/encryption.key"
            if os.path.exists(key_file):
                with open(key_file, 'rb') as f:
                    self.encryption_key = f.read()
            else:
                # Create security directory if it doesn't exist
                os.makedirs("security", exist_ok=True)
                
                # Generate new key
                password = b"restaurant_management_system_2024"
                salt = os.urandom(16)
                kdf = PBKDF2HMAC(
                    algorithm=hashes.SHA256(),
                    length=32,
                    salt=salt,
                    iterations=100000,
                )
                self.encryption_key = base64.urlsafe_b64encode(kdf.derive(password))
                
                # Save key
                with open(key_file, 'wb') as f:
                    f.write(self.encryption_key)
                
                # Save salt
                with open("security/salt.key", 'wb') as f:
                    f.write(salt)
            
            log_info("Система шифрования инициализирована", "AdvancedSecurity")
            
        except Exception as e:
            handle_module_error(e, "Безопасность", "инициализация шифрования")
    
    def init_security_tables(self):
        """Initialize security-related database tables"""
        try:
            cursor = self.db_manager.connection.cursor()
            
            # Users table with security features
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS security_users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    salt TEXT NOT NULL,
                    role_id INTEGER,
                    is_active BOOLEAN DEFAULT 1,
                    last_login DATETIME,
                    failed_login_attempts INTEGER DEFAULT 0,
                    account_locked_until DATETIME,
                    password_changed_date DATETIME,
                    two_factor_secret TEXT,
                    two_factor_enabled BOOLEAN DEFAULT 0,
                    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                    created_by TEXT,
                    FOREIGN KEY (role_id) REFERENCES security_roles (id)
                )
            ''')
            
            # Roles table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS security_roles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    role_name TEXT UNIQUE NOT NULL,
                    description TEXT,
                    permissions TEXT,  -- JSON string of permissions
                    is_active BOOLEAN DEFAULT 1,
                    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                    created_by TEXT
                )
            ''')
            
            # Permissions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS security_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    permission_name TEXT UNIQUE NOT NULL,
                    description TEXT,
                    module_name TEXT,
                    action_type TEXT,
                    is_active BOOLEAN DEFAULT 1
                )
            ''')
            
            # Audit log table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS security_audit_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    username TEXT,
                    action_type TEXT NOT NULL,
                    module_name TEXT,
                    action_description TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    session_id TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    success BOOLEAN,
                    error_message TEXT,
                    additional_data TEXT,  -- JSON string
                    FOREIGN KEY (user_id) REFERENCES security_users (id)
                )
            ''')
            
            # Sessions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS security_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT UNIQUE NOT NULL,
                    user_id INTEGER,
                    username TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                    last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
                    expires_at DATETIME,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (user_id) REFERENCES security_users (id)
                )
            ''')
            
            # Compliance records table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS compliance_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    record_type TEXT NOT NULL,  -- food_safety, haccp, etc.
                    title TEXT NOT NULL,
                    description TEXT,
                    compliance_date DATE,
                    expiry_date DATE,
                    status TEXT DEFAULT 'active',  -- active, expired, pending
                    responsible_person TEXT,
                    document_path TEXT,
                    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                    created_by TEXT,
                    last_updated DATETIME,
                    updated_by TEXT
                )
            ''')
            
            self.db_manager.connection.commit()
            
            # Insert default roles and permissions
            self.create_default_roles_and_permissions()
            
            log_info("Таблицы безопасности инициализированы", "AdvancedSecurity")
            
        except Exception as e:
            handle_module_error(e, "Безопасность", "инициализация таблиц")
    
    def create_default_roles_and_permissions(self):
        """Create default roles and permissions"""
        try:
            cursor = self.db_manager.connection.cursor()
            
            # Default permissions
            default_permissions = [
                ('view_dashboard', 'Просмотр панели управления', 'dashboard', 'view'),
                ('manage_users', 'Управление пользователями', 'users', 'manage'),
                ('view_reports', 'Просмотр отчетов', 'reports', 'view'),
                ('manage_inventory', 'Управление складом', 'inventory', 'manage'),
                ('manage_sales', 'Управление продажами', 'sales', 'manage'),
                ('manage_recipes', 'Управление рецептами', 'recipes', 'manage'),
                ('view_analytics', 'Просмотр аналитики', 'analytics', 'view'),
                ('manage_settings', 'Управление настройками', 'settings', 'manage'),
                ('audit_access', 'Доступ к аудиту', 'audit', 'view'),
                ('compliance_manage', 'Управление соответствием', 'compliance', 'manage')
            ]
            
            for perm in default_permissions:
                cursor.execute('''
                    INSERT OR IGNORE INTO security_permissions 
                    (permission_name, description, module_name, action_type)
                    VALUES (?, ?, ?, ?)
                ''', perm)
            
            # Default roles
            admin_permissions = json.dumps([p[0] for p in default_permissions])
            manager_permissions = json.dumps([
                'view_dashboard', 'view_reports', 'manage_inventory', 
                'manage_sales', 'manage_recipes', 'view_analytics'
            ])
            staff_permissions = json.dumps([
                'view_dashboard', 'manage_sales', 'manage_recipes'
            ])
            
            default_roles = [
                ('Администратор', 'Полный доступ ко всем функциям системы', admin_permissions),
                ('Менеджер', 'Доступ к управлению операциями ресторана', manager_permissions),
                ('Персонал', 'Базовый доступ к рабочим функциям', staff_permissions)
            ]
            
            for role in default_roles:
                cursor.execute('''
                    INSERT OR IGNORE INTO security_roles 
                    (role_name, description, permissions, created_by)
                    VALUES (?, ?, ?, ?)
                ''', (*role, 'system'))
            
            self.db_manager.connection.commit()
            log_info("Роли и разрешения по умолчанию созданы", "AdvancedSecurity")
            
        except Exception as e:
            handle_module_error(e, "Безопасность", "создание ролей по умолчанию")
    
    def show_security_manager(self):
        """Show the main security management window"""
        try:
            if self.window and self.window.winfo_exists():
                self.window.lift()
                return
            
            self.window = tk.Toplevel(self.parent)
            self.window.title("🔒 Расширенная Безопасность и Соответствие")
            self.window.geometry("1400x900")
            self.window.configure(bg='white')
            
            # Center window
            x = (self.window.winfo_screenwidth() // 2) - (1400 // 2)
            y = (self.window.winfo_screenheight() // 2) - (900 // 2)
            self.window.geometry(f"1400x900+{x}+{y}")
            
            # Header
            header_frame = tk.Frame(self.window, bg='#2c3e50', height=80)
            header_frame.pack(fill='x')
            header_frame.pack_propagate(False)
            
            header_label = tk.Label(header_frame, 
                                   text="🔒 Расширенная Безопасность и Соответствие",
                                   font=('Cambria', 24, 'bold'), 
                                   fg='white', bg='#2c3e50')
            header_label.pack(expand=True)
            
            # Status bar
            status_frame = tk.Frame(self.window, bg='#34495e', height=30)
            status_frame.pack(fill='x', side='bottom')
            status_frame.pack_propagate(False)
            
            self.status_label = tk.Label(status_frame, 
                                        text="Система безопасности готова к работе",
                                        font=('Cambria', 10), 
                                        fg='white', bg='#34495e')
            self.status_label.pack(side='left', padx=10, pady=5)
            
            # Main content with notebook
            self.notebook = ttk.Notebook(self.window)
            self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
            
            # Create tabs
            self.create_user_management_tab()
            self.create_role_permissions_tab()
            self.create_two_factor_auth_tab()
            self.create_audit_trail_tab()
            self.create_compliance_tab()
            self.create_security_settings_tab()
            
            log_info("Окно расширенной безопасности открыто", "AdvancedSecurity")
            
        except Exception as e:
            handle_module_error(e, "Безопасность", "открытие главного окна")

    def create_user_management_tab(self):
        """Create User Management tab"""
        user_frame = ttk.Frame(self.notebook)
        self.notebook.add(user_frame, text="👥 Пользователи")

        # Header
        header = tk.Label(user_frame, text="Управление Пользователями",
                         font=('Cambria', 20, 'bold'), fg='maroon')
        header.pack(pady=20)

        # Users list frame
        users_list_frame = tk.Frame(user_frame, bg='white', relief='solid', bd=1)
        users_list_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Create users treeview
        users_columns = ('ID', 'Пользователь', 'Роль', 'Статус', 'Последний вход', '2FA', 'Создан')
        self.users_tree = ttk.Treeview(users_list_frame, columns=users_columns, show='headings', height=15)

        for col in users_columns:
            self.users_tree.heading(col, text=col)
            self.users_tree.column(col, width=150)

        # Add scrollbar
        users_scrollbar = ttk.Scrollbar(users_list_frame, orient='vertical', command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=users_scrollbar.set)

        self.users_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        users_scrollbar.pack(side='right', fill='y', pady=10)

        # Load users data
        self.load_users_data()

        # Control buttons
        users_buttons_frame = tk.Frame(user_frame)
        users_buttons_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(users_buttons_frame, text="➕ Добавить Пользователя",
                 command=self.add_user,
                 font=('Cambria', 12, 'bold'), bg='#2ecc71', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(users_buttons_frame, text="✏️ Редактировать",
                 command=self.edit_user,
                 font=('Cambria', 12, 'bold'), bg='#3498db', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(users_buttons_frame, text="🔒 Заблокировать",
                 command=self.lock_user,
                 font=('Cambria', 12, 'bold'), bg='#e74c3c', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(users_buttons_frame, text="🔓 Разблокировать",
                 command=self.unlock_user,
                 font=('Cambria', 12, 'bold'), bg='#f39c12', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(users_buttons_frame, text="🔄 Сбросить Пароль",
                 command=self.reset_password,
                 font=('Cambria', 12, 'bold'), bg='#9b59b6', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

    def create_role_permissions_tab(self):
        """Create Role and Permissions Management tab"""
        role_frame = ttk.Frame(self.notebook)
        self.notebook.add(role_frame, text="🛡️ Роли и Права")

        # Header
        header = tk.Label(role_frame, text="Управление Ролями и Правами",
                         font=('Cambria', 20, 'bold'), fg='maroon')
        header.pack(pady=20)

        # Split frame for roles and permissions
        split_frame = tk.Frame(role_frame)
        split_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Roles frame (left side)
        roles_frame = tk.LabelFrame(split_frame, text="Роли",
                                   font=('Cambria', 14, 'bold'),
                                   fg='maroon', bg='white')
        roles_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))

        # Roles treeview
        roles_columns = ('ID', 'Роль', 'Описание', 'Активна')
        self.roles_tree = ttk.Treeview(roles_frame, columns=roles_columns, show='headings', height=12)

        for col in roles_columns:
            self.roles_tree.heading(col, text=col)
            self.roles_tree.column(col, width=120)

        self.roles_tree.pack(fill='both', expand=True, padx=10, pady=10)
        self.roles_tree.bind('<<TreeviewSelect>>', self.on_role_select)

        # Role buttons
        role_buttons_frame = tk.Frame(roles_frame)
        role_buttons_frame.pack(fill='x', padx=10, pady=5)

        tk.Button(role_buttons_frame, text="➕ Добавить Роль",
                 command=self.add_role,
                 font=('Cambria', 10, 'bold'), bg='#2ecc71', fg='white').pack(side='left', padx=2)

        tk.Button(role_buttons_frame, text="✏️ Изменить",
                 command=self.edit_role,
                 font=('Cambria', 10, 'bold'), bg='#3498db', fg='white').pack(side='left', padx=2)

        tk.Button(role_buttons_frame, text="🗑️ Удалить",
                 command=self.delete_role,
                 font=('Cambria', 10, 'bold'), bg='#e74c3c', fg='white').pack(side='left', padx=2)

        # Permissions frame (right side)
        permissions_frame = tk.LabelFrame(split_frame, text="Права Доступа",
                                         font=('Cambria', 14, 'bold'),
                                         fg='maroon', bg='white')
        permissions_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))

        # Permissions checkboxes
        self.permissions_vars = {}
        self.permissions_frame = tk.Frame(permissions_frame)
        self.permissions_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Load roles and permissions data
        self.load_roles_data()
        self.load_permissions_checkboxes()

    def create_two_factor_auth_tab(self):
        """Create Two-Factor Authentication tab"""
        tfa_frame = ttk.Frame(self.notebook)
        self.notebook.add(tfa_frame, text="🔐 2FA")

        # Header
        header = tk.Label(tfa_frame, text="Двухфакторная Аутентификация",
                         font=('Cambria', 20, 'bold'), fg='maroon')
        header.pack(pady=20)

        # 2FA Settings frame
        settings_frame = tk.LabelFrame(tfa_frame, text="Настройки 2FA",
                                      font=('Cambria', 14, 'bold'),
                                      fg='maroon', bg='white')
        settings_frame.pack(fill='x', padx=20, pady=10)

        # Global 2FA settings
        self.require_2fa_var = tk.BooleanVar()
        tk.Checkbutton(settings_frame, text="Требовать 2FA для всех пользователей",
                      variable=self.require_2fa_var,
                      font=('Cambria', 12, 'bold')).pack(anchor='w', padx=10, pady=10)

        # QR Code generation frame
        qr_frame = tk.LabelFrame(tfa_frame, text="Настройка 2FA для Пользователя",
                                font=('Cambria', 14, 'bold'),
                                fg='maroon', bg='white')
        qr_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # User selection
        user_select_frame = tk.Frame(qr_frame)
        user_select_frame.pack(fill='x', padx=10, pady=10)

        tk.Label(user_select_frame, text="Выберите пользователя:",
                font=('Cambria', 12, 'bold')).pack(side='left')

        self.tfa_user_combo = ttk.Combobox(user_select_frame, font=('Cambria', 12), width=30)
        self.tfa_user_combo.pack(side='left', padx=10)

        tk.Button(user_select_frame, text="🔄 Обновить Список",
                 command=self.refresh_user_list,
                 font=('Cambria', 10, 'bold'), bg='#3498db', fg='white').pack(side='left', padx=5)

        # QR Code display
        qr_display_frame = tk.Frame(qr_frame)
        qr_display_frame.pack(fill='both', expand=True, padx=10, pady=10)

        self.qr_label = tk.Label(qr_display_frame, text="Выберите пользователя для генерации QR кода",
                                font=('Cambria', 12), bg='white')
        self.qr_label.pack(expand=True)

        # 2FA control buttons
        tfa_buttons_frame = tk.Frame(tfa_frame)
        tfa_buttons_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(tfa_buttons_frame, text="📱 Генерировать QR Код",
                 command=self.generate_qr_code,
                 font=('Cambria', 12, 'bold'), bg='#2ecc71', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(tfa_buttons_frame, text="✅ Включить 2FA",
                 command=self.enable_2fa,
                 font=('Cambria', 12, 'bold'), bg='#3498db', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(tfa_buttons_frame, text="❌ Отключить 2FA",
                 command=self.disable_2fa,
                 font=('Cambria', 12, 'bold'), bg='#e74c3c', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Load user list for 2FA
        self.refresh_user_list()

    def create_audit_trail_tab(self):
        """Create Audit Trail tab"""
        audit_frame = ttk.Frame(self.notebook)
        self.notebook.add(audit_frame, text="📋 Аудит")

        # Header
        header = tk.Label(audit_frame, text="Журнал Аудита",
                         font=('Cambria', 20, 'bold'), fg='maroon')
        header.pack(pady=20)

        # Filters frame
        filters_frame = tk.LabelFrame(audit_frame, text="Фильтры",
                                     font=('Cambria', 12, 'bold'),
                                     fg='maroon', bg='white')
        filters_frame.pack(fill='x', padx=20, pady=10)

        # Filter controls
        filter_controls_frame = tk.Frame(filters_frame)
        filter_controls_frame.pack(fill='x', padx=10, pady=10)

        # Date range
        tk.Label(filter_controls_frame, text="От:", font=('Cambria', 11, 'bold')).grid(row=0, column=0, padx=5, pady=5)
        self.audit_date_from = tk.Entry(filter_controls_frame, font=('Cambria', 11), width=12)
        self.audit_date_from.grid(row=0, column=1, padx=5, pady=5)
        self.audit_date_from.insert(0, (datetime.now() - timedelta(days=7)).strftime("%d.%m.%Y"))

        tk.Label(filter_controls_frame, text="До:", font=('Cambria', 11, 'bold')).grid(row=0, column=2, padx=5, pady=5)
        self.audit_date_to = tk.Entry(filter_controls_frame, font=('Cambria', 11), width=12)
        self.audit_date_to.grid(row=0, column=3, padx=5, pady=5)
        self.audit_date_to.insert(0, datetime.now().strftime("%d.%m.%Y"))

        # User filter
        tk.Label(filter_controls_frame, text="Пользователь:", font=('Cambria', 11, 'bold')).grid(row=0, column=4, padx=5, pady=5)
        self.audit_user_combo = ttk.Combobox(filter_controls_frame, font=('Cambria', 11), width=15)
        self.audit_user_combo.grid(row=0, column=5, padx=5, pady=5)

        # Action type filter
        tk.Label(filter_controls_frame, text="Действие:", font=('Cambria', 11, 'bold')).grid(row=1, column=0, padx=5, pady=5)
        self.audit_action_combo = ttk.Combobox(filter_controls_frame, font=('Cambria', 11), width=15)
        self.audit_action_combo.grid(row=1, column=1, padx=5, pady=5)
        self.audit_action_combo['values'] = ('Все', 'Вход', 'Выход', 'Создание', 'Изменение', 'Удаление', 'Просмотр')
        self.audit_action_combo.set('Все')

        # Filter button
        tk.Button(filter_controls_frame, text="🔍 Применить Фильтр",
                 command=self.apply_audit_filter,
                 font=('Cambria', 11, 'bold'), bg='#3498db', fg='white').grid(row=1, column=2, padx=10, pady=5)

        # Audit log display
        audit_log_frame = tk.Frame(audit_frame, bg='white', relief='solid', bd=1)
        audit_log_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Audit log treeview
        audit_columns = ('Время', 'Пользователь', 'Действие', 'Модуль', 'Описание', 'IP Адрес', 'Результат')
        self.audit_tree = ttk.Treeview(audit_log_frame, columns=audit_columns, show='headings', height=15)

        for col in audit_columns:
            self.audit_tree.heading(col, text=col)
            self.audit_tree.column(col, width=120)

        # Add scrollbars
        audit_v_scrollbar = ttk.Scrollbar(audit_log_frame, orient='vertical', command=self.audit_tree.yview)
        audit_h_scrollbar = ttk.Scrollbar(audit_log_frame, orient='horizontal', command=self.audit_tree.xview)
        self.audit_tree.configure(yscrollcommand=audit_v_scrollbar.set, xscrollcommand=audit_h_scrollbar.set)

        self.audit_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        audit_v_scrollbar.pack(side='right', fill='y', pady=10)
        audit_h_scrollbar.pack(side='bottom', fill='x', padx=10)

        # Load audit data
        self.load_audit_data()

        # Audit control buttons
        audit_buttons_frame = tk.Frame(audit_frame)
        audit_buttons_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(audit_buttons_frame, text="🔄 Обновить",
                 command=self.load_audit_data,
                 font=('Cambria', 12, 'bold'), bg='#2ecc71', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(audit_buttons_frame, text="📊 Экспорт в Excel",
                 command=self.export_audit_log,
                 font=('Cambria', 12, 'bold'), bg='#f39c12', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(audit_buttons_frame, text="🗑️ Очистить Старые Записи",
                 command=self.cleanup_audit_log,
                 font=('Cambria', 12, 'bold'), bg='#e74c3c', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

    def create_compliance_tab(self):
        """Create Compliance Management tab"""
        compliance_frame = ttk.Frame(self.notebook)
        self.notebook.add(compliance_frame, text="📜 Соответствие")

        # Header
        header = tk.Label(compliance_frame, text="Управление Соответствием",
                         font=('Cambria', 20, 'bold'), fg='maroon')
        header.pack(pady=20)

        # Compliance records frame
        records_frame = tk.Frame(compliance_frame, bg='white', relief='solid', bd=1)
        records_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Compliance records treeview
        compliance_columns = ('ID', 'Тип', 'Название', 'Дата', 'Срок действия', 'Статус', 'Ответственный')
        self.compliance_tree = ttk.Treeview(records_frame, columns=compliance_columns, show='headings', height=15)

        for col in compliance_columns:
            self.compliance_tree.heading(col, text=col)
            self.compliance_tree.column(col, width=140)

        # Add scrollbar
        compliance_scrollbar = ttk.Scrollbar(records_frame, orient='vertical', command=self.compliance_tree.yview)
        self.compliance_tree.configure(yscrollcommand=compliance_scrollbar.set)

        self.compliance_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        compliance_scrollbar.pack(side='right', fill='y', pady=10)

        # Load compliance data
        self.load_compliance_data()

        # Compliance control buttons
        compliance_buttons_frame = tk.Frame(compliance_frame)
        compliance_buttons_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(compliance_buttons_frame, text="➕ Добавить Запись",
                 command=self.add_compliance_record,
                 font=('Cambria', 12, 'bold'), bg='#2ecc71', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(compliance_buttons_frame, text="✏️ Редактировать",
                 command=self.edit_compliance_record,
                 font=('Cambria', 12, 'bold'), bg='#3498db', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(compliance_buttons_frame, text="📄 Просмотр Документа",
                 command=self.view_compliance_document,
                 font=('Cambria', 12, 'bold'), bg='#9b59b6', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(compliance_buttons_frame, text="⚠️ Проверить Сроки",
                 command=self.check_compliance_expiry,
                 font=('Cambria', 12, 'bold'), bg='#f39c12', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(compliance_buttons_frame, text="📊 Отчет о Соответствии",
                 command=self.generate_compliance_report,
                 font=('Cambria', 12, 'bold'), bg='#1abc9c', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

    def create_security_settings_tab(self):
        """Create Security Settings tab"""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="⚙️ Настройки")

        # Header
        header = tk.Label(settings_frame, text="Настройки Безопасности",
                         font=('Cambria', 20, 'bold'), fg='maroon')
        header.pack(pady=20)

        # Password policy frame
        password_frame = tk.LabelFrame(settings_frame, text="Политика Паролей",
                                      font=('Cambria', 14, 'bold'),
                                      fg='maroon', bg='white')
        password_frame.pack(fill='x', padx=20, pady=10)

        # Password policy settings
        policy_frame = tk.Frame(password_frame)
        policy_frame.pack(fill='x', padx=10, pady=10)

        tk.Label(policy_frame, text="Минимальная длина пароля:",
                font=('Cambria', 12, 'bold')).grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.min_length_entry = tk.Entry(policy_frame, font=('Cambria', 12), width=10)
        self.min_length_entry.grid(row=0, column=1, padx=5, pady=5)
        self.min_length_entry.insert(0, str(self.security_config['password_policy']['min_length']))

        tk.Label(policy_frame, text="Срок действия пароля (дни):",
                font=('Cambria', 12, 'bold')).grid(row=0, column=2, sticky='w', padx=5, pady=5)
        self.password_age_entry = tk.Entry(policy_frame, font=('Cambria', 12), width=10)
        self.password_age_entry.grid(row=0, column=3, padx=5, pady=5)
        self.password_age_entry.insert(0, str(self.security_config['password_policy']['max_age_days']))

        # Password requirements checkboxes
        self.require_uppercase_var = tk.BooleanVar(value=self.security_config['password_policy']['require_uppercase'])
        self.require_lowercase_var = tk.BooleanVar(value=self.security_config['password_policy']['require_lowercase'])
        self.require_numbers_var = tk.BooleanVar(value=self.security_config['password_policy']['require_numbers'])
        self.require_special_var = tk.BooleanVar(value=self.security_config['password_policy']['require_special'])

        tk.Checkbutton(policy_frame, text="Требовать заглавные буквы",
                      variable=self.require_uppercase_var,
                      font=('Cambria', 11)).grid(row=1, column=0, sticky='w', padx=5, pady=2)

        tk.Checkbutton(policy_frame, text="Требовать строчные буквы",
                      variable=self.require_lowercase_var,
                      font=('Cambria', 11)).grid(row=1, column=1, sticky='w', padx=5, pady=2)

        tk.Checkbutton(policy_frame, text="Требовать цифры",
                      variable=self.require_numbers_var,
                      font=('Cambria', 11)).grid(row=2, column=0, sticky='w', padx=5, pady=2)

        tk.Checkbutton(policy_frame, text="Требовать спецсимволы",
                      variable=self.require_special_var,
                      font=('Cambria', 11)).grid(row=2, column=1, sticky='w', padx=5, pady=2)

        # Session settings frame
        session_frame = tk.LabelFrame(settings_frame, text="Настройки Сессий",
                                     font=('Cambria', 14, 'bold'),
                                     fg='maroon', bg='white')
        session_frame.pack(fill='x', padx=20, pady=10)

        session_controls_frame = tk.Frame(session_frame)
        session_controls_frame.pack(fill='x', padx=10, pady=10)

        tk.Label(session_controls_frame, text="Таймаут сессии (мин):",
                font=('Cambria', 12, 'bold')).grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.session_timeout_entry = tk.Entry(session_controls_frame, font=('Cambria', 12), width=10)
        self.session_timeout_entry.grid(row=0, column=1, padx=5, pady=5)
        self.session_timeout_entry.insert(0, str(self.security_config['session_config']['timeout_minutes']))

        tk.Label(session_controls_frame, text="Макс. одновременных сессий:",
                font=('Cambria', 12, 'bold')).grid(row=0, column=2, sticky='w', padx=5, pady=5)
        self.max_sessions_entry = tk.Entry(session_controls_frame, font=('Cambria', 12), width=10)
        self.max_sessions_entry.grid(row=0, column=3, padx=5, pady=5)
        self.max_sessions_entry.insert(0, str(self.security_config['session_config']['max_concurrent_sessions']))

        # Audit settings frame
        audit_settings_frame = tk.LabelFrame(settings_frame, text="Настройки Аудита",
                                            font=('Cambria', 14, 'bold'),
                                            fg='maroon', bg='white')
        audit_settings_frame.pack(fill='x', padx=20, pady=10)

        audit_controls_frame = tk.Frame(audit_settings_frame)
        audit_controls_frame.pack(fill='x', padx=10, pady=10)

        self.log_all_actions_var = tk.BooleanVar(value=self.security_config['audit_config']['log_all_actions'])
        self.alert_suspicious_var = tk.BooleanVar(value=self.security_config['audit_config']['alert_on_suspicious'])

        tk.Checkbutton(audit_controls_frame, text="Логировать все действия",
                      variable=self.log_all_actions_var,
                      font=('Cambria', 12, 'bold')).grid(row=0, column=0, sticky='w', padx=5, pady=5)

        tk.Checkbutton(audit_controls_frame, text="Уведомления о подозрительной активности",
                      variable=self.alert_suspicious_var,
                      font=('Cambria', 12, 'bold')).grid(row=0, column=1, sticky='w', padx=5, pady=5)

        tk.Label(audit_controls_frame, text="Хранить логи (дни):",
                font=('Cambria', 12, 'bold')).grid(row=1, column=0, sticky='w', padx=5, pady=5)
        self.audit_retention_entry = tk.Entry(audit_controls_frame, font=('Cambria', 12), width=10)
        self.audit_retention_entry.grid(row=1, column=1, padx=5, pady=5)
        self.audit_retention_entry.insert(0, str(self.security_config['audit_config']['retention_days']))

        # Settings control buttons
        settings_buttons_frame = tk.Frame(settings_frame)
        settings_buttons_frame.pack(fill='x', padx=20, pady=20)

        tk.Button(settings_buttons_frame, text="💾 Сохранить Настройки",
                 command=self.save_security_settings,
                 font=('Cambria', 12, 'bold'), bg='#2ecc71', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(settings_buttons_frame, text="🔄 Сбросить к Умолчанию",
                 command=self.reset_security_settings,
                 font=('Cambria', 12, 'bold'), bg='#e74c3c', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(settings_buttons_frame, text="🧪 Тест Настроек",
                 command=self.test_security_settings,
                 font=('Cambria', 12, 'bold'), bg='#f39c12', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

    # Implementation methods
    def load_users_data(self):
        """Load users data into the tree"""
        try:
            # Clear existing data
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)

            cursor = self.db_manager.connection.cursor()
            cursor.execute('''
                SELECT u.id, u.username, r.role_name,
                       CASE WHEN u.is_active THEN 'Активен' ELSE 'Заблокирован' END,
                       u.last_login,
                       CASE WHEN u.two_factor_enabled THEN 'Включен' ELSE 'Отключен' END,
                       u.created_date
                FROM security_users u
                LEFT JOIN security_roles r ON u.role_id = r.id
                ORDER BY u.username
            ''')

            users = cursor.fetchall()
            for user in users:
                # Format dates
                last_login = user[4] if user[4] else 'Никогда'
                if last_login != 'Никогда':
                    last_login = datetime.fromisoformat(last_login).strftime("%d.%m.%Y %H:%M")

                created_date = datetime.fromisoformat(user[6]).strftime("%d.%m.%Y") if user[6] else ''

                self.users_tree.insert('', 'end', values=(
                    user[0], user[1], user[2] or 'Не назначена', user[3],
                    last_login, user[5], created_date
                ))

            log_info("Данные пользователей загружены", "AdvancedSecurity")

        except Exception as e:
            handle_module_error(e, "Безопасность", "загрузка пользователей")

    def load_roles_data(self):
        """Load roles data into the tree"""
        try:
            # Clear existing data
            for item in self.roles_tree.get_children():
                self.roles_tree.delete(item)

            cursor = self.db_manager.connection.cursor()
            cursor.execute('''
                SELECT id, role_name, description,
                       CASE WHEN is_active THEN 'Да' ELSE 'Нет' END
                FROM security_roles
                ORDER BY role_name
            ''')

            roles = cursor.fetchall()
            for role in roles:
                self.roles_tree.insert('', 'end', values=role)

            log_info("Данные ролей загружены", "AdvancedSecurity")

        except Exception as e:
            handle_module_error(e, "Безопасность", "загрузка ролей")

    def load_permissions_checkboxes(self):
        """Load permissions checkboxes"""
        try:
            # Clear existing checkboxes
            for widget in self.permissions_frame.winfo_children():
                widget.destroy()

            cursor = self.db_manager.connection.cursor()
            cursor.execute('''
                SELECT permission_name, description, module_name
                FROM security_permissions
                WHERE is_active = 1
                ORDER BY module_name, permission_name
            ''')

            permissions = cursor.fetchall()
            self.permissions_vars = {}

            current_module = None
            row = 0

            for perm in permissions:
                perm_name, description, module_name = perm

                # Add module header if changed
                if module_name != current_module:
                    if current_module is not None:
                        row += 1

                    tk.Label(self.permissions_frame, text=f"📁 {module_name.upper()}",
                            font=('Cambria', 12, 'bold'), fg='maroon').grid(
                                row=row, column=0, columnspan=2, sticky='w', pady=(10, 5))
                    row += 1
                    current_module = module_name

                # Add permission checkbox
                var = tk.BooleanVar()
                self.permissions_vars[perm_name] = var

                cb = tk.Checkbutton(self.permissions_frame, text=description,
                                   variable=var, font=('Cambria', 11))
                cb.grid(row=row, column=0, sticky='w', padx=20, pady=2)
                row += 1

            log_info("Права доступа загружены", "AdvancedSecurity")

        except Exception as e:
            handle_module_error(e, "Безопасность", "загрузка прав доступа")

    def on_role_select(self, event):
        """Handle role selection"""
        try:
            selection = self.roles_tree.selection()
            if not selection:
                return

            item = self.roles_tree.item(selection[0])
            role_id = item['values'][0]

            # Load role permissions
            cursor = self.db_manager.connection.cursor()
            cursor.execute('SELECT permissions FROM security_roles WHERE id = ?', (role_id,))
            result = cursor.fetchone()

            if result and result[0]:
                role_permissions = json.loads(result[0])

                # Update checkboxes
                for perm_name, var in self.permissions_vars.items():
                    var.set(perm_name in role_permissions)
            else:
                # Clear all checkboxes
                for var in self.permissions_vars.values():
                    var.set(False)

        except Exception as e:
            handle_module_error(e, "Безопасность", "выбор роли")

    def load_audit_data(self):
        """Load audit data"""
        try:
            # Clear existing data
            for item in self.audit_tree.get_children():
                self.audit_tree.delete(item)

            cursor = self.db_manager.connection.cursor()
            cursor.execute('''
                SELECT timestamp, username, action_type, module_name,
                       action_description, ip_address,
                       CASE WHEN success THEN 'Успех' ELSE 'Ошибка' END
                FROM security_audit_log
                ORDER BY timestamp DESC
                LIMIT 1000
            ''')

            audit_records = cursor.fetchall()
            for record in audit_records:
                # Format timestamp
                timestamp = datetime.fromisoformat(record[0]).strftime("%d.%m.%Y %H:%M:%S")

                self.audit_tree.insert('', 'end', values=(
                    timestamp, record[1], record[2], record[3],
                    record[4], record[5] or 'N/A', record[6]
                ))

            log_info("Данные аудита загружены", "AdvancedSecurity")

        except Exception as e:
            handle_module_error(e, "Безопасность", "загрузка данных аудита")

    def load_compliance_data(self):
        """Load compliance data"""
        try:
            # Clear existing data
            for item in self.compliance_tree.get_children():
                self.compliance_tree.delete(item)

            cursor = self.db_manager.connection.cursor()
            cursor.execute('''
                SELECT id, record_type, title, compliance_date,
                       expiry_date, status, responsible_person
                FROM compliance_records
                ORDER BY expiry_date ASC
            ''')

            compliance_records = cursor.fetchall()
            for record in compliance_records:
                # Format dates
                compliance_date = record[3] if record[3] else 'N/A'
                if compliance_date != 'N/A':
                    compliance_date = datetime.fromisoformat(compliance_date).strftime("%d.%m.%Y")

                expiry_date = record[4] if record[4] else 'N/A'
                if expiry_date != 'N/A':
                    expiry_date = datetime.fromisoformat(expiry_date).strftime("%d.%m.%Y")

                # Color code based on expiry
                status = record[5]
                if expiry_date != 'N/A':
                    expiry_dt = datetime.strptime(expiry_date, "%d.%m.%Y")
                    days_until_expiry = (expiry_dt - datetime.now()).days
                    if days_until_expiry < 0:
                        status = "Просрочено"
                    elif days_until_expiry < 30:
                        status = "Истекает"

                self.compliance_tree.insert('', 'end', values=(
                    record[0], record[1], record[2], compliance_date,
                    expiry_date, status, record[6] or 'Не назначен'
                ))

            log_info("Данные соответствия загружены", "AdvancedSecurity")

        except Exception as e:
            handle_module_error(e, "Безопасность", "загрузка данных соответствия")

    def generate_qr_code(self):
        """Generate QR code for 2FA setup"""
        try:
            selected_user = self.tfa_user_combo.get()
            if not selected_user:
                messagebox.showwarning("Предупреждение", "Выберите пользователя для настройки 2FA")
                return

            # Generate secret key
            secret = pyotp.random_base32()

            # Create TOTP URI
            totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
                name=selected_user,
                issuer_name="Restaurant Management System"
            )

            # Generate QR code
            qr = qrcode.QRCode(version=1, box_size=10, border=5)
            qr.add_data(totp_uri)
            qr.make(fit=True)

            qr_image = qr.make_image(fill_color="black", back_color="white")

            # Convert to PhotoImage
            qr_photo = ImageTk.PhotoImage(qr_image)

            # Display QR code
            self.qr_label.configure(image=qr_photo, text="")
            self.qr_label.image = qr_photo  # Keep a reference

            # Store secret temporarily
            self.temp_2fa_secret = secret

            messagebox.showinfo("QR Код Сгенерирован",
                               f"QR код для пользователя '{selected_user}' сгенерирован.\n"
                               f"Отсканируйте его в приложении аутентификатора (Google Authenticator, Authy и т.д.)")

            log_info(f"QR код для 2FA сгенерирован для пользователя {selected_user}", "AdvancedSecurity")

        except Exception as e:
            handle_module_error(e, "Безопасность", "генерация QR кода")

    def enable_2fa(self):
        """Enable 2FA for selected user"""
        try:
            selected_user = self.tfa_user_combo.get()
            if not selected_user or not hasattr(self, 'temp_2fa_secret'):
                messagebox.showwarning("Предупреждение", "Сначала сгенерируйте QR код")
                return

            # Verify TOTP code
            code = tk.simpledialog.askstring("Подтверждение",
                                            "Введите 6-значный код из приложения аутентификатора:")
            if not code:
                return

            totp = pyotp.TOTP(self.temp_2fa_secret)
            if not totp.verify(code):
                messagebox.showerror("Ошибка", "Неверный код аутентификации")
                return

            # Save 2FA secret to database
            cursor = self.db_manager.connection.cursor()
            cursor.execute('''
                UPDATE security_users
                SET two_factor_secret = ?, two_factor_enabled = 1
                WHERE username = ?
            ''', (self.temp_2fa_secret, selected_user))

            self.db_manager.connection.commit()

            messagebox.showinfo("Успех", f"2FA включен для пользователя '{selected_user}'")
            self.load_users_data()

            # Clear temporary secret
            delattr(self, 'temp_2fa_secret')

            log_info(f"2FA включен для пользователя {selected_user}", "AdvancedSecurity")

        except Exception as e:
            handle_module_error(e, "Безопасность", "включение 2FA")

    def disable_2fa(self):
        """Disable 2FA for selected user"""
        try:
            selected_user = self.tfa_user_combo.get()
            if not selected_user:
                messagebox.showwarning("Предупреждение", "Выберите пользователя")
                return

            if messagebox.askyesno("Подтверждение",
                                  f"Отключить 2FA для пользователя '{selected_user}'?"):
                cursor = self.db_manager.connection.cursor()
                cursor.execute('''
                    UPDATE security_users
                    SET two_factor_secret = NULL, two_factor_enabled = 0
                    WHERE username = ?
                ''', (selected_user,))

                self.db_manager.connection.commit()

                messagebox.showinfo("Успех", f"2FA отключен для пользователя '{selected_user}'")
                self.load_users_data()

                log_info(f"2FA отключен для пользователя {selected_user}", "AdvancedSecurity")

        except Exception as e:
            handle_module_error(e, "Безопасность", "отключение 2FA")

    def refresh_user_list(self):
        """Refresh user list for 2FA combo"""
        try:
            cursor = self.db_manager.connection.cursor()
            cursor.execute('SELECT username FROM security_users WHERE is_active = 1 ORDER BY username')
            users = [row[0] for row in cursor.fetchall()]

            self.tfa_user_combo['values'] = users
            if hasattr(self, 'audit_user_combo'):
                self.audit_user_combo['values'] = ['Все'] + users
                self.audit_user_combo.set('Все')

        except Exception as e:
            handle_module_error(e, "Безопасность", "обновление списка пользователей")

    def log_audit_event(self, user_id, username, action_type, module_name, description,
                       success=True, error_message=None, additional_data=None):
        """Log audit event"""
        try:
            cursor = self.db_manager.connection.cursor()
            cursor.execute('''
                INSERT INTO security_audit_log
                (user_id, username, action_type, module_name, action_description,
                 success, error_message, additional_data)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (user_id, username, action_type, module_name, description,
                  success, error_message, json.dumps(additional_data) if additional_data else None))

            self.db_manager.connection.commit()

        except Exception as e:
            log_error(f"Ошибка записи в журнал аудита: {str(e)}", "AdvancedSecurity")

    # Placeholder methods for UI actions
    def add_user(self):
        messagebox.showinfo("В разработке", "Функция добавления пользователя будет реализована")

    def edit_user(self):
        messagebox.showinfo("В разработке", "Функция редактирования пользователя будет реализована")

    def lock_user(self):
        messagebox.showinfo("В разработке", "Функция блокировки пользователя будет реализована")

    def unlock_user(self):
        messagebox.showinfo("В разработке", "Функция разблокировки пользователя будет реализована")

    def reset_password(self):
        messagebox.showinfo("В разработке", "Функция сброса пароля будет реализована")

    def add_role(self):
        messagebox.showinfo("В разработке", "Функция добавления роли будет реализована")

    def edit_role(self):
        messagebox.showinfo("В разработке", "Функция редактирования роли будет реализована")

    def delete_role(self):
        messagebox.showinfo("В разработке", "Функция удаления роли будет реализована")

    def apply_audit_filter(self):
        messagebox.showinfo("В разработке", "Функция фильтрации аудита будет реализована")

    def export_audit_log(self):
        messagebox.showinfo("В разработке", "Функция экспорта журнала аудита будет реализована")

    def cleanup_audit_log(self):
        messagebox.showinfo("В разработке", "Функция очистки журнала аудита будет реализована")

    def add_compliance_record(self):
        messagebox.showinfo("В разработке", "Функция добавления записи соответствия будет реализована")

    def edit_compliance_record(self):
        messagebox.showinfo("В разработке", "Функция редактирования записи соответствия будет реализована")

    def view_compliance_document(self):
        messagebox.showinfo("В разработке", "Функция просмотра документа соответствия будет реализована")

    def check_compliance_expiry(self):
        messagebox.showinfo("В разработке", "Функция проверки сроков соответствия будет реализована")

    def generate_compliance_report(self):
        messagebox.showinfo("В разработке", "Функция генерации отчета соответствия будет реализована")

    def save_security_settings(self):
        messagebox.showinfo("В разработке", "Функция сохранения настроек безопасности будет реализована")

    def reset_security_settings(self):
        messagebox.showinfo("В разработке", "Функция сброса настроек безопасности будет реализована")

    def test_security_settings(self):
        messagebox.showinfo("В разработке", "Функция тестирования настроек безопасности будет реализована")


def create_advanced_security_manager(parent, db_manager):
    """Create and return Advanced Security Manager instance"""
    try:
        manager = AdvancedSecurityManager(parent, db_manager)
        manager.show_security_manager()
        return manager
    except Exception as e:
        handle_module_error(e, "Безопасность", "создание менеджера безопасности")
        return None
