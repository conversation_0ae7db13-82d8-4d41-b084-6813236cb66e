"""
Standard Module Template for Restaurant Management System
This template demonstrates the standardized patterns for creating new modules

Key Features:
- Consistent window creation using utils.window_utils
- Comprehensive error handling using utils.error_handling
- Russian localization
- Professional styling with ModernStyles
- Proper database integration
- Standard navigation entry point
"""

import tkinter as tk
from tkinter import ttk, messagebox
from gui.styles import ModernStyles
from utils.window_utils import create_standard_window, apply_standard_styling
from utils.error_handling import handle_module_error, handle_database_error, log_info, log_warning


class StandardModuleTemplate:
    """Template class for creating standardized restaurant management modules"""
    
    def __init__(self, parent, db_manager):
        """
        Initialize the module
        
        Args:
            parent: Parent window (usually main window)
            db_manager: Database manager instance
        """
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        
        # Module-specific data
        self.data = []
        self.selected_item = None
        
        log_info("Инициализация модуля", "StandardModule")
    
    def show_module(self):
        """Show module window (main entry point)"""
        try:
            # Check if window already exists
            if self.window and self.window.winfo_exists():
                self.window.lift()
                self.window.focus_set()
                return
            
            log_info("Открытие окна модуля", "StandardModule")
            
            # Create and show window
            self.create_window()
            self.create_interface()
            self.load_data()
            
        except Exception as e:
            handle_module_error(e, "Стандартный модуль", "открытие окна")
    
    def create_window(self):
        """Create the module window using standardized approach"""
        try:
            self.window = create_standard_window(
                parent=self.parent,
                title="Стандартный Модуль",
                width=1400,
                height=900,
                resizable=True,
                maximized=False,
                modal=True,
                icon_emoji="📋"
            )
            
            log_info("Окно модуля создано", "StandardModule")
            
        except Exception as e:
            handle_module_error(e, "Стандартный модуль", "создание окна")
            raise
    
    def create_interface(self):
        """Create the module interface using standardized styling"""
        try:
            # Apply standard styling and get main container
            main_container = apply_standard_styling(
                self.window, 
                "Стандартный Модуль Управления", 
                "📋"
            )
            
            # Create notebook for tabs
            self.notebook = ttk.Notebook(main_container)
            self.notebook.pack(fill='both', expand=True, pady=(0, 20))
            
            # Create tabs
            self.create_main_tab()
            self.create_settings_tab()
            self.create_reports_tab()
            
            # Create bottom buttons
            self.create_bottom_buttons(main_container)
            
            log_info("Интерфейс модуля создан", "StandardModule")
            
        except Exception as e:
            handle_module_error(e, "Стандартный модуль", "создание интерфейса")
    
    def create_main_tab(self):
        """Create main functionality tab"""
        try:
            tab_frame = ttk.Frame(self.notebook)
            self.notebook.add(tab_frame, text="📋 Основные Функции")
            
            # Control panel
            control_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
            control_frame.pack(fill='x', padx=20, pady=10)
            
            # Add buttons
            tk.Button(control_frame, text="➕ Добавить",
                     command=self.add_item,
                     bg=ModernStyles.COLORS['success'], fg='white',
                     font=('Cambria', 12, 'bold'), relief='flat',
                     padx=20, pady=8).pack(side='left', padx=5)
            
            tk.Button(control_frame, text="✏️ Редактировать",
                     command=self.edit_item,
                     bg=ModernStyles.COLORS['primary'], fg='white',
                     font=('Cambria', 12, 'bold'), relief='flat',
                     padx=20, pady=8).pack(side='left', padx=5)
            
            tk.Button(control_frame, text="🗑️ Удалить",
                     command=self.delete_item,
                     bg=ModernStyles.COLORS['danger'], fg='white',
                     font=('Cambria', 12, 'bold'), relief='flat',
                     padx=20, pady=8).pack(side='left', padx=5)
            
            tk.Button(control_frame, text="🔄 Обновить",
                     command=self.refresh_data,
                     bg=ModernStyles.COLORS['info'], fg='white',
                     font=('Cambria', 12, 'bold'), relief='flat',
                     padx=20, pady=8).pack(side='right', padx=5)
            
            # Data table
            self.create_data_table(tab_frame)
            
        except Exception as e:
            handle_module_error(e, "Стандартный модуль", "создание основной вкладки")
    
    def create_data_table(self, parent):
        """Create data table with standardized styling"""
        try:
            # Table frame
            table_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
            table_frame.pack(fill='both', expand=True, padx=20, pady=10)
            
            # Create treeview
            columns = ('ID', 'Название', 'Описание', 'Дата создания', 'Статус')
            self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
            
            # Configure columns
            for col in columns:
                self.tree.heading(col, text=col)
                self.tree.column(col, width=150, anchor='center')
            
            # Scrollbars
            v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.tree.yview)
            h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.tree.xview)
            self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
            
            # Pack table and scrollbars
            self.tree.pack(side='left', fill='both', expand=True)
            v_scrollbar.pack(side='right', fill='y')
            h_scrollbar.pack(side='bottom', fill='x')
            
            # Bind selection event
            self.tree.bind('<<TreeviewSelect>>', self.on_item_select)
            
        except Exception as e:
            handle_module_error(e, "Стандартный модуль", "создание таблицы данных")
    
    def create_settings_tab(self):
        """Create settings tab"""
        try:
            tab_frame = ttk.Frame(self.notebook)
            self.notebook.add(tab_frame, text="⚙️ Настройки")
            
            # Settings content
            settings_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
            settings_frame.pack(fill='both', expand=True, padx=20, pady=20)
            
            tk.Label(settings_frame, text="⚙️ Настройки модуля",
                    font=('Cambria', 18, 'bold'),
                    fg=ModernStyles.COLORS['text_primary'],
                    bg=ModernStyles.COLORS['bg_main']).pack(pady=20)
            
            # Add settings controls here
            
        except Exception as e:
            handle_module_error(e, "Стандартный модуль", "создание вкладки настроек")
    
    def create_reports_tab(self):
        """Create reports tab"""
        try:
            tab_frame = ttk.Frame(self.notebook)
            self.notebook.add(tab_frame, text="📊 Отчёты")
            
            # Reports content
            reports_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
            reports_frame.pack(fill='both', expand=True, padx=20, pady=20)
            
            tk.Label(reports_frame, text="📊 Отчёты модуля",
                    font=('Cambria', 18, 'bold'),
                    fg=ModernStyles.COLORS['text_primary'],
                    bg=ModernStyles.COLORS['bg_main']).pack(pady=20)
            
            # Add report controls here
            
        except Exception as e:
            handle_module_error(e, "Стандартный модуль", "создание вкладки отчётов")
    
    def create_bottom_buttons(self, parent):
        """Create bottom action buttons"""
        try:
            button_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
            button_frame.pack(fill='x', pady=(10, 0))
            
            tk.Button(button_frame, text="💾 Сохранить",
                     command=self.save_data,
                     bg=ModernStyles.COLORS['success'], fg='white',
                     font=('Cambria', 12, 'bold'), relief='flat',
                     padx=30, pady=10).pack(side='right', padx=5)
            
            tk.Button(button_frame, text="❌ Закрыть",
                     command=self.close_window,
                     bg=ModernStyles.COLORS['secondary'], fg='white',
                     font=('Cambria', 12, 'bold'), relief='flat',
                     padx=30, pady=10).pack(side='right', padx=5)
            
        except Exception as e:
            handle_module_error(e, "Стандартный модуль", "создание кнопок")
    
    def load_data(self):
        """Load data from database"""
        try:
            log_info("Загрузка данных", "StandardModule")
            
            # Example database query
            # self.data = self.db_manager.fetch_all("SELECT * FROM example_table")
            
            # For template, use sample data
            self.data = [
                (1, "Пример 1", "Описание примера 1", "2024-01-01", "Активен"),
                (2, "Пример 2", "Описание примера 2", "2024-01-02", "Неактивен"),
            ]
            
            self.refresh_table()
            
        except Exception as e:
            handle_database_error(e, "загрузка данных модуля")
    
    def refresh_table(self):
        """Refresh table with current data"""
        try:
            # Clear existing items
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # Insert data
            for row in self.data:
                self.tree.insert('', 'end', values=row)
                
        except Exception as e:
            handle_module_error(e, "Стандартный модуль", "обновление таблицы")
    
    def on_item_select(self, event):
        """Handle item selection"""
        try:
            selection = self.tree.selection()
            if selection:
                item = self.tree.item(selection[0])
                self.selected_item = item['values']
                log_info(f"Выбран элемент: {self.selected_item[1]}", "StandardModule")
                
        except Exception as e:
            log_warning(f"Ошибка выбора элемента: {e}", "StandardModule")
    
    def add_item(self):
        """Add new item"""
        try:
            log_info("Добавление нового элемента", "StandardModule")
            messagebox.showinfo("Добавление", "Функция добавления элемента")
            
        except Exception as e:
            handle_module_error(e, "Стандартный модуль", "добавление элемента")
    
    def edit_item(self):
        """Edit selected item"""
        try:
            if not self.selected_item:
                messagebox.showwarning("Предупреждение", "Выберите элемент для редактирования")
                return
            
            log_info(f"Редактирование элемента: {self.selected_item[1]}", "StandardModule")
            messagebox.showinfo("Редактирование", f"Редактирование элемента: {self.selected_item[1]}")
            
        except Exception as e:
            handle_module_error(e, "Стандартный модуль", "редактирование элемента")
    
    def delete_item(self):
        """Delete selected item"""
        try:
            if not self.selected_item:
                messagebox.showwarning("Предупреждение", "Выберите элемент для удаления")
                return
            
            if messagebox.askyesno("Подтверждение", f"Удалить элемент '{self.selected_item[1]}'?"):
                log_info(f"Удаление элемента: {self.selected_item[1]}", "StandardModule")
                messagebox.showinfo("Удаление", f"Элемент '{self.selected_item[1]}' удалён")
                
        except Exception as e:
            handle_module_error(e, "Стандартный модуль", "удаление элемента")
    
    def refresh_data(self):
        """Refresh data from database"""
        try:
            log_info("Обновление данных", "StandardModule")
            self.load_data()
            messagebox.showinfo("Обновление", "Данные обновлены")
            
        except Exception as e:
            handle_module_error(e, "Стандартный модуль", "обновление данных")
    
    def save_data(self):
        """Save data to database"""
        try:
            log_info("Сохранение данных", "StandardModule")
            messagebox.showinfo("Сохранение", "Данные сохранены")
            
        except Exception as e:
            handle_database_error(e, "сохранение данных модуля")
    
    def close_window(self):
        """Close module window"""
        try:
            log_info("Закрытие окна модуля", "StandardModule")
            if self.window:
                self.window.destroy()
                
        except Exception as e:
            log_warning(f"Ошибка закрытия окна: {e}", "StandardModule")


def create_standard_module(parent, db_manager):
    """
    Standard entry point function for navigation menu
    This function should be present in every module for consistent navigation
    """
    try:
        log_info("Создание стандартного модуля", "StandardModule")
        module = StandardModuleTemplate(parent, db_manager)
        module.show_module()
        return module
        
    except Exception as e:
        handle_module_error(e, "Стандартный модуль")
        return None


# Alternative entry point names for backward compatibility
def show_standard_module(parent, db_manager):
    """Alternative entry point for backward compatibility"""
    return create_standard_module(parent, db_manager)


def open_standard_module(parent, db_manager):
    """Alternative entry point for backward compatibility"""
    return create_standard_module(parent, db_manager)
