#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Финальный тест исправлений прокрутки главного окна
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_scrolling_fixes():
    """Тест исправлений прокрутки"""
    
    print("🔧 ТЕСТ ИСПРАВЛЕНИЙ ПРОКРУТКИ ГЛАВНОГО ОКНА")
    print("=" * 60)
    
    try:
        # Импортировать главное окно
        from gui.main_window import MainWindow
        from database.db_manager import DatabaseManager
        import tkinter as tk
        
        print("✅ Модули импортированы успешно")
        
        # Создать главное окно с правильными параметрами
        selected_db = {'name': 'Тестовый Ресторан', 'file_path': 'test_restaurant.db'}
        main_window = MainWindow(selected_db)
        print("✅ Главное окно создано")
        
        # Проверить наличие элементов прокрутки
        checks = []
        
        # Проверка 1: Наличие canvas
        if hasattr(main_window, 'nav_canvas'):
            checks.append("✅ Canvas найден")
        else:
            checks.append("❌ Canvas не найден")
        
        # Проверка 2: Наличие scrollbar
        if hasattr(main_window, 'nav_scrollbar'):
            checks.append("✅ Scrollbar найден")
        else:
            checks.append("❌ Scrollbar не найден")
        
        # Проверка 3: Наличие scrollable_frame
        if hasattr(main_window, 'nav_scrollable_frame'):
            checks.append("✅ Scrollable frame найден")
        else:
            checks.append("❌ Scrollable frame не найден")
        
        # Проверка 4: Наличие метода update_scroll_region
        if hasattr(main_window, 'update_scroll_region'):
            checks.append("✅ Метод update_scroll_region найден")
        else:
            checks.append("❌ Метод update_scroll_region не найден")
        
        # Проверка 5: Проверить scroll region
        if hasattr(main_window, 'nav_canvas'):
            try:
                main_window.nav_canvas.update_idletasks()
                bbox = main_window.nav_canvas.bbox("all")
                if bbox:
                    checks.append(f"✅ Scroll region: {bbox}")
                else:
                    checks.append("❌ Scroll region пустой")
            except Exception as e:
                checks.append(f"❌ Ошибка scroll region: {e}")
        
        # Проверка 6: Количество дочерних элементов в scrollable_frame
        if hasattr(main_window, 'nav_scrollable_frame'):
            children = main_window.nav_scrollable_frame.winfo_children()
            checks.append(f"✅ Дочерних элементов в scrollable_frame: {len(children)}")
        
        # Вывести результаты проверок
        print("\n📊 РЕЗУЛЬТАТЫ ПРОВЕРОК:")
        for check in checks:
            print(f"   {check}")
        
        # Показать окно для визуального тестирования
        main_window.root.title("🔧 Тест Исправлений Прокрутки - Система Управления Рестораном")

        print("\n🖼️ ВИЗУАЛЬНЫЙ ТЕСТ:")
        print("   1. Окно должно быть видимо")
        print("   2. Боковая панель должна содержать кнопки")
        print("   3. Полоса прокрутки должна быть видна справа")
        print("   4. Прокрутка колесом мыши должна работать")
        print("   5. Все кнопки должны быть доступны при прокрутке")

        print("\n🎯 Закройте окно, когда закончите тестирование")

        # Запустить главный цикл
        main_window.root.mainloop()
        
        print("✅ Тест завершен")
        
    except Exception as e:
        print(f"❌ Ошибка в тесте: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_scrolling_fixes()
