"""
Полностью функциональная система планирования меню
Сезонное планирование, анализ популярности, оптимизация меню
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import json
import os
from gui.styles import ModernStyles

class MenuPlanningSystem:
    """Система планирования меню"""

    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None

        # Текущее меню
        self.current_menu = [
            {
                "id": 1, "name": "Борщ украинский", "category": "Супы", "price": 280,
                "cost": 125, "margin": 155, "popularity": 85, "season": "Зима",
                "prep_time": 45, "ingredients": ["Свекла", "Капуста", "Морковь", "Мясо"],
                "allergens": [], "calories": 320, "status": "Активное"
            },
            {
                "id": 2, "name": "Салат Цезарь", "category": "Салаты", "price": 320,
                "cost": 120, "margin": 200, "popularity": 92, "season": "Круглый год",
                "prep_time": 15, "ingredients": ["Салат", "Курица", "Сыр", "Соус"],
                "allergens": ["Глютен", "Молоко"], "calories": 280, "status": "Активное"
            },
            {
                "id": 3, "name": "Стейк рибай", "category": "Горячие блюда", "price": 850,
                "cost": 345, "margin": 505, "popularity": 78, "season": "Круглый год",
                "prep_time": 25, "ingredients": ["Говядина", "Специи", "Масло"],
                "allergens": [], "calories": 650, "status": "Активное"
            },
            {
                "id": 4, "name": "Суп-пюре тыквенный", "category": "Супы", "price": 240,
                "cost": 95, "margin": 145, "popularity": 65, "season": "Осень",
                "prep_time": 35, "ingredients": ["Тыква", "Сливки", "Лук"],
                "allergens": ["Молоко"], "calories": 180, "status": "Сезонное"
            },
            {
                "id": 5, "name": "Мороженое ванильное", "category": "Десерты", "price": 180,
                "cost": 45, "margin": 135, "popularity": 88, "season": "Лето",
                "prep_time": 5, "ingredients": ["Мороженое", "Топпинг"],
                "allergens": ["Молоко"], "calories": 220, "status": "Сезонное"
            },
            {
                "id": 6, "name": "Глинтвейн", "category": "Напитки", "price": 200,
                "cost": 65, "margin": 135, "popularity": 45, "season": "Зима",
                "prep_time": 20, "ingredients": ["Вино", "Специи", "Фрукты"],
                "allergens": ["Алкоголь"], "calories": 150, "status": "Сезонное"
            }
        ]

        # Планы меню по сезонам
        self.seasonal_plans = {
            "Зима": ["Борщ украинский", "Глинтвейн", "Жаркое", "Горячий шоколад"],
            "Весна": ["Зеленый борщ", "Салат весенний", "Рыба на пару"],
            "Лето": ["Окрошка", "Мороженое", "Салаты", "Холодные супы"],
            "Осень": ["Суп-пюре тыквенный", "Грибные блюда", "Компоты"]
        }

        # Аналитика продаж
        self.sales_analytics = [
            {"dish_id": 1, "week": "2024-W02", "orders": 45, "revenue": 12600},
            {"dish_id": 2, "week": "2024-W02", "orders": 52, "revenue": 16640},
            {"dish_id": 3, "week": "2024-W02", "orders": 28, "revenue": 23800},
            {"dish_id": 4, "week": "2024-W02", "orders": 18, "revenue": 4320},
            {"dish_id": 5, "week": "2024-W02", "orders": 35, "revenue": 6300},
            {"dish_id": 6, "week": "2024-W02", "orders": 12, "revenue": 2400},
        ]

    def create_window(self):
        """Создать окно системы планирования меню"""
        try:
            from utils.window_utils import create_centered_dialog
            self.window = create_centered_dialog(
                self.parent,
                "📋 Планирование Меню",
                width=1500,
                height=950,
                resizable=True
            )
        except ImportError:
            # Fallback если utils не доступны
            self.window = tk.Toplevel(self.parent)
            self.window.title("📋 Планирование Меню")
            self.window.geometry("1500x950")
            self.window.configure(bg=ModernStyles.COLORS['bg_main'])
            self.window.resizable(True, True)

            # Центрировать окно
            self.window.update_idletasks()
            x = (self.window.winfo_screenwidth() // 2) - (1500 // 2)
            y = (self.window.winfo_screenheight() // 2) - (950 // 2)
            self.window.geometry(f"1500x950+{x}+{y}")
        self.window.resizable(True, True)

        self.create_interface()

    def create_interface(self):
        """Создать интерфейс системы планирования меню"""
        # Заголовок
        header_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['primary'], height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="📋 Планирование Меню",
                font=('Arial', 16, 'bold'), bg=ModernStyles.COLORS['primary'],
                fg='white').pack(side='left', padx=20, pady=15)

        # Кнопки действий
        btn_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['primary'])
        btn_frame.pack(side='right', padx=20, pady=10)

        tk.Button(btn_frame, text="➕ Новое Блюдо", command=self.add_dish,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(btn_frame, text="🔄 Обновить Меню", command=self.update_seasonal_menu,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(btn_frame, text="📊 Анализ", command=self.analyze_menu_performance,
                 bg=ModernStyles.COLORS['secondary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        # Основной контент
        main_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Создать вкладки
        self.create_tabs(main_frame)

    def create_tabs(self, parent):
        """Создать вкладки модуля"""
        notebook = ttk.Notebook(parent)
        notebook.pack(fill='both', expand=True)

        # Вкладка текущего меню
        current_menu_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(current_menu_frame, text="📋 Текущее Меню")
        self.create_current_menu_tab(current_menu_frame)

        # Вкладка сезонного планирования
        seasonal_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(seasonal_frame, text="🌱 Сезонное Планирование")
        self.create_seasonal_tab(seasonal_frame)

        # Вкладка анализа популярности
        popularity_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(popularity_frame, text="📈 Анализ Популярности")
        self.create_popularity_tab(popularity_frame)

        # Вкладка оптимизации
        optimization_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(optimization_frame, text="⚡ Оптимизация")
        self.create_optimization_tab(optimization_frame)

    def create_current_menu_tab(self, parent):
        """Создать вкладку текущего меню"""
        # Заголовок
        tk.Label(parent, text="Текущее Меню Ресторана",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)

        # Статистика меню
        stats_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        stats_frame.pack(fill='x', padx=20, pady=10)

        total_dishes = len(self.current_menu)
        avg_margin = sum(dish['margin'] for dish in self.current_menu) / total_dishes if total_dishes > 0 else 0
        avg_popularity = sum(dish['popularity'] for dish in self.current_menu) / total_dishes if total_dishes > 0 else 0

        self.create_stat_card(stats_frame, "Всего блюд", str(total_dishes), ModernStyles.COLORS['primary'])
        self.create_stat_card(stats_frame, "Средняя маржа", f"{avg_margin:.0f}₽", ModernStyles.COLORS['success'])
        self.create_stat_card(stats_frame, "Популярность", f"{avg_popularity:.0f}%", ModernStyles.COLORS['warning'])

        # Фильтры
        filter_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        filter_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(filter_frame, text="Категория:", font=('Arial', 10, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).pack(side='left')

        categories = ["Все"] + list(set(dish['category'] for dish in self.current_menu))
        category_combo = ttk.Combobox(filter_frame, values=categories)
        category_combo.set("Все")
        category_combo.pack(side='left', padx=10)

        tk.Button(filter_frame, text="🔍 Фильтр", command=lambda: self.filter_menu(category_combo.get()),
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Arial', 9, 'bold'), relief='flat', padx=10, pady=3).pack(side='left', padx=5)

        # Таблица меню
        columns = ('Название', 'Категория', 'Цена', 'Себестоимость', 'Маржа', 'Популярность', 'Статус')
        self.menu_tree = ttk.Treeview(parent, columns=columns, show='headings', style="Modern.Treeview")

        for col in columns:
            self.menu_tree.heading(col, text=col)
            self.menu_tree.column(col, width=120)

        # Заполнить данными
        self.refresh_menu()

        # Скроллбары
        menu_v_scroll = ttk.Scrollbar(parent, orient='vertical', command=self.menu_tree.yview)
        menu_h_scroll = ttk.Scrollbar(parent, orient='horizontal', command=self.menu_tree.xview)
        self.menu_tree.configure(yscrollcommand=menu_v_scroll.set, xscrollcommand=menu_h_scroll.set)

        self.menu_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        menu_v_scroll.pack(side='right', fill='y')
        menu_h_scroll.pack(side='bottom', fill='x')

        # Кнопки управления
        menu_btn_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        menu_btn_frame.pack(fill='x', padx=10, pady=10)

        tk.Button(menu_btn_frame, text="👁️ Детали", command=self.view_dish_details,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(menu_btn_frame, text="✏️ Редактировать", command=self.edit_dish,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(menu_btn_frame, text="🗑️ Удалить", command=self.remove_dish,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

    def create_seasonal_tab(self, parent):
        """Создать вкладку сезонного планирования"""
        # Заголовок
        tk.Label(parent, text="Сезонное Планирование Меню",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)

        # Текущий сезон
        current_season = self.get_current_season()
        season_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_card'], relief='solid', bd=1)
        season_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(season_frame, text=f"🌟 Текущий сезон: {current_season}",
                font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_card']).pack(pady=10)

        # Планы по сезонам
        seasons_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        seasons_frame.pack(fill='both', expand=True, padx=20, pady=20)

        for season, dishes in self.seasonal_plans.items():
            season_card = tk.LabelFrame(seasons_frame, text=f"{self.get_season_icon(season)} {season}",
                                       font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
            season_card.pack(side='left', fill='both', expand=True, padx=10)

            for dish in dishes:
                tk.Label(season_card, text=f"• {dish}", font=('Arial', 10),
                        bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', padx=10, pady=2)

    def create_popularity_tab(self, parent):
        """Создать вкладку анализа популярности"""
        # Заголовок
        tk.Label(parent, text="Анализ Популярности Блюд",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)

        # Топ блюда
        top_frame = tk.LabelFrame(parent, text="Топ Популярных Блюд",
                                 font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        top_frame.pack(fill='x', padx=20, pady=10)

        # Сортировать по популярности
        sorted_dishes = sorted(self.current_menu, key=lambda x: x['popularity'], reverse=True)

        for i, dish in enumerate(sorted_dishes[:5]):
            row = tk.Frame(top_frame, bg=ModernStyles.COLORS['bg_main'])
            row.pack(fill='x', padx=10, pady=5)

            # Позиция и медаль
            medal = "🥇" if i == 0 else "🥈" if i == 1 else "🥉" if i == 2 else f"{i+1}."
            tk.Label(row, text=medal, font=('Arial', 12, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).pack(side='left')

            # Название и категория
            tk.Label(row, text=f"{dish['name']} ({dish['category']})", font=('Arial', 11),
                    bg=ModernStyles.COLORS['bg_main']).pack(side='left', padx=10)

            # Популярность
            tk.Label(row, text=f"📊 {dish['popularity']}%", font=('Arial', 11, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).pack(side='right')

        # Анализ продаж
        sales_frame = tk.LabelFrame(parent, text="Анализ Продаж за Неделю",
                                   font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        sales_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Таблица продаж
        columns = ('Блюдо', 'Заказов', 'Выручка', 'Средний чек')
        sales_tree = ttk.Treeview(sales_frame, columns=columns, show='headings', style="Modern.Treeview")

        for col in columns:
            sales_tree.heading(col, text=col)
            sales_tree.column(col, width=150)

        # Заполнить данными продаж
        for sale in self.sales_analytics:
            dish = next((d for d in self.current_menu if d['id'] == sale['dish_id']), None)
            if dish:
                avg_check = sale['revenue'] / sale['orders'] if sale['orders'] > 0 else 0
                sales_tree.insert('', 'end', values=(
                    dish['name'],
                    sale['orders'],
                    f"{sale['revenue']:,}₽",
                    f"{avg_check:.0f}₽"
                ))

        sales_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def create_optimization_tab(self, parent):
        """Создать вкладку оптимизации меню"""
        # Заголовок
        tk.Label(parent, text="Оптимизация Меню",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)

        # Рекомендации по оптимизации
        recommendations_frame = tk.LabelFrame(parent, text="Рекомендации по Оптимизации",
                                            font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        recommendations_frame.pack(fill='x', padx=20, pady=10)

        # Анализ меню и генерация рекомендаций
        recommendations = self.generate_menu_recommendations()

        for i, recommendation in enumerate(recommendations):
            rec_frame = tk.Frame(recommendations_frame, bg=ModernStyles.COLORS['bg_card'], relief='solid', bd=1)
            rec_frame.pack(fill='x', padx=10, pady=5)

            # Иконка рекомендации
            icon = "🔥" if i == 0 else "💡" if i == 1 else "⚡"
            tk.Label(rec_frame, text=icon, font=('Arial', 14),
                    bg=ModernStyles.COLORS['bg_card']).pack(side='left', padx=10, pady=10)

            # Текст рекомендации
            tk.Label(rec_frame, text=recommendation, font=('Arial', 11),
                    bg=ModernStyles.COLORS['bg_card'], wraplength=600).pack(side='left', padx=10, pady=10)

        # Анализ прибыльности
        profitability_frame = tk.LabelFrame(parent, text="Анализ Прибыльности",
                                          font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        profitability_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Таблица прибыльности
        columns = ('Блюдо', 'Маржа', '% Маржи', 'Популярность', 'Рекомендация')
        profit_tree = ttk.Treeview(profitability_frame, columns=columns, show='headings', style="Modern.Treeview")

        for col in columns:
            profit_tree.heading(col, text=col)
            profit_tree.column(col, width=120)

        # Заполнить анализом прибыльности
        for dish in self.current_menu:
            margin_pct = (dish['margin'] / dish['price'] * 100) if dish['price'] > 0 else 0

            # Определить рекомендацию
            if margin_pct > 60 and dish['popularity'] > 80:
                recommendation = "🌟 Звезда меню"
            elif margin_pct > 60 and dish['popularity'] < 50:
                recommendation = "💎 Скрытая жемчужина"
            elif margin_pct < 40 and dish['popularity'] > 80:
                recommendation = "⚠️ Пересмотреть цену"
            elif margin_pct < 40 and dish['popularity'] < 50:
                recommendation = "❌ Убрать из меню"
            else:
                recommendation = "✅ Оставить как есть"

            profit_tree.insert('', 'end', values=(
                dish['name'],
                f"{dish['margin']}₽",
                f"{margin_pct:.1f}%",
                f"{dish['popularity']}%",
                recommendation
            ))

        profit_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def create_stat_card(self, parent, title, value, color):
        """Создать карточку статистики"""
        card = tk.Frame(parent, bg=color, relief='solid', bd=1)
        card.pack(side='left', fill='both', expand=True, padx=5)

        tk.Label(card, text=title, font=('Arial', 10), bg=color, fg='white').pack(pady=(15, 5))
        tk.Label(card, text=value, font=('Arial', 14, 'bold'), bg=color, fg='white').pack(pady=(0, 15))

    def refresh_menu(self):
        """Обновить таблицу меню"""
        for item in self.menu_tree.get_children():
            self.menu_tree.delete(item)

        for dish in self.current_menu:
            status_icon = "🟢" if dish['status'] == 'Активное' else "🟡"

            self.menu_tree.insert('', 'end', values=(
                dish['name'],
                dish['category'],
                f"{dish['price']}₽",
                f"{dish['cost']}₽",
                f"{dish['margin']}₽",
                f"{dish['popularity']}%",
                f"{status_icon} {dish['status']}"
            ))

    def filter_menu(self, category):
        """Фильтровать меню по категории"""
        for item in self.menu_tree.get_children():
            self.menu_tree.delete(item)

        filtered_dishes = self.current_menu if category == "Все" else [
            dish for dish in self.current_menu if dish['category'] == category
        ]

        for dish in filtered_dishes:
            status_icon = "🟢" if dish['status'] == 'Активное' else "🟡"

            self.menu_tree.insert('', 'end', values=(
                dish['name'],
                dish['category'],
                f"{dish['price']}₽",
                f"{dish['cost']}₽",
                f"{dish['margin']}₽",
                f"{dish['popularity']}%",
                f"{status_icon} {dish['status']}"
            ))

    def get_current_season(self):
        """Определить текущий сезон"""
        month = datetime.now().month
        if month in [12, 1, 2]:
            return "Зима"
        elif month in [3, 4, 5]:
            return "Весна"
        elif month in [6, 7, 8]:
            return "Лето"
        else:
            return "Осень"

    def get_season_icon(self, season):
        """Получить иконку сезона"""
        icons = {
            "Зима": "❄️",
            "Весна": "🌸",
            "Лето": "☀️",
            "Осень": "🍂"
        }
        return icons.get(season, "🌟")

    def generate_menu_recommendations(self):
        """Генерировать рекомендации по меню"""
        recommendations = []

        # Анализ популярности
        low_popularity = [dish for dish in self.current_menu if dish['popularity'] < 50]
        if low_popularity:
            recommendations.append(f"Рассмотрите удаление {len(low_popularity)} блюд с низкой популярностью: {', '.join([d['name'] for d in low_popularity[:2]])}")

        # Анализ маржинальности
        low_margin = [dish for dish in self.current_menu if dish['margin']/dish['price'] < 0.4]
        if low_margin:
            recommendations.append(f"Пересмотрите цены на {len(low_margin)} блюд с низкой маржой: {', '.join([d['name'] for d in low_margin[:2]])}")

        # Сезонные рекомендации
        current_season = self.get_current_season()
        seasonal_dishes = [dish for dish in self.current_menu if dish['season'] == current_season]
        if len(seasonal_dishes) < 2:
            recommendations.append(f"Добавьте больше блюд для сезона '{current_season}' - сейчас только {len(seasonal_dishes)} блюд")

        # Общие рекомендации
        if not recommendations:
            recommendations.append("Меню хорошо сбалансировано. Продолжайте мониторить популярность и маржинальность блюд")

        return recommendations

    def add_dish(self):
        """Добавить новое блюдо"""
        try:
            from utils.window_utils import create_centered_dialog
            add_window = create_centered_dialog(
                self.window,
                "➕ Добавить Новое Блюдо",
                width=800,
                height=700,
                resizable=True
            )
        except ImportError:
            add_window = tk.Toplevel(self.window)
            add_window.title("➕ Добавить Новое Блюдо")
            add_window.geometry("800x700")
            add_window.configure(bg=ModernStyles.COLORS['bg_main'])
            add_window.resizable(True, True)

            # Центрировать окно
            add_window.update_idletasks()
            x = (add_window.winfo_screenwidth() // 2) - (800 // 2)
            y = (add_window.winfo_screenheight() // 2) - (700 // 2)
            add_window.geometry(f"800x700+{x}+{y}")

        # Заголовок
        tk.Label(add_window, text="Добавить Новое Блюдо в Меню",
                font=('Cambria', 18, 'bold italic'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

        # Основная форма
        form_frame = tk.Frame(add_window, bg=ModernStyles.COLORS['bg_main'])
        form_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # Поля формы
        fields = [
            ("Название блюда:", "name", "entry"),
            ("Категория:", "category", "combo"),
            ("Цена (₽):", "price", "entry"),
            ("Себестоимость (₽):", "cost", "entry"),
            ("Время приготовления (мин):", "prep_time", "entry"),
            ("Сезон:", "season", "combo"),
            ("Калории:", "calories", "entry"),
            ("Ингредиенты:", "ingredients", "text"),
            ("Аллергены:", "allergens", "text"),
            ("Описание:", "description", "text")
        ]

        entries = {}
        row = 0

        for label_text, field_name, field_type in fields:
            tk.Label(form_frame, text=label_text, font=('Cambria', 12, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).grid(row=row, column=0, sticky='nw', pady=8, padx=(0, 10))

            if field_type == "entry":
                entry = tk.Entry(form_frame, font=('Cambria', 12), width=40)
                entry.grid(row=row, column=1, sticky='ew', pady=8)
                entries[field_name] = entry

            elif field_type == "combo":
                if field_name == "category":
                    values = ["Супы", "Салаты", "Горячие блюда", "Десерты", "Напитки", "Закуски"]
                elif field_name == "season":
                    values = ["Круглый год", "Зима", "Весна", "Лето", "Осень"]

                combo = ttk.Combobox(form_frame, font=('Cambria', 12), width=37, values=values, state="readonly")
                combo.grid(row=row, column=1, sticky='ew', pady=8)
                combo.set(values[0])
                entries[field_name] = combo

            elif field_type == "text":
                text_widget = tk.Text(form_frame, font=('Cambria', 11), width=40, height=3)
                text_widget.grid(row=row, column=1, sticky='ew', pady=8)
                entries[field_name] = text_widget

            row += 1

        # Настройка сетки
        form_frame.grid_columnconfigure(1, weight=1)

        # Кнопки
        btn_frame = tk.Frame(add_window, bg=ModernStyles.COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=30, pady=20)

        def save_dish():
            try:
                # Получить данные из формы
                name = entries['name'].get().strip()
                category = entries['category'].get()
                price = float(entries['price'].get() or 0)
                cost = float(entries['cost'].get() or 0)
                prep_time = int(entries['prep_time'].get() or 0)
                season = entries['season'].get()
                calories = int(entries['calories'].get() or 0)

                # Получить текст из Text виджетов
                ingredients_text = entries['ingredients'].get('1.0', 'end-1c').strip()
                allergens_text = entries['allergens'].get('1.0', 'end-1c').strip()
                description_text = entries['description'].get('1.0', 'end-1c').strip()

                # Валидация
                if not name:
                    messagebox.showerror("Ошибка", "Введите название блюда")
                    return
                if price <= 0:
                    messagebox.showerror("Ошибка", "Цена должна быть больше 0")
                    return
                if cost < 0:
                    messagebox.showerror("Ошибка", "Себестоимость не может быть отрицательной")
                    return

                # Создать новое блюдо
                new_dish = {
                    "id": max([dish['id'] for dish in self.current_menu], default=0) + 1,
                    "name": name,
                    "category": category,
                    "price": price,
                    "cost": cost,
                    "margin": price - cost,
                    "popularity": 50,  # Начальная популярность
                    "season": season,
                    "prep_time": prep_time,
                    "ingredients": [ing.strip() for ing in ingredients_text.split(',') if ing.strip()],
                    "allergens": [all.strip() for all in allergens_text.split(',') if all.strip()],
                    "calories": calories,
                    "status": "Активное",
                    "description": description_text
                }

                # Добавить в меню
                self.current_menu.append(new_dish)

                # Обновить отображение
                self.refresh_menu()

                # Закрыть окно
                add_window.destroy()

                messagebox.showinfo("Успех", f"Блюдо '{name}' добавлено в меню!")

            except ValueError as e:
                messagebox.showerror("Ошибка", "Проверьте правильность введённых числовых значений")
            except Exception as e:
                messagebox.showerror("Ошибка", f"Ошибка при добавлении блюда: {e}")

        tk.Button(btn_frame, text="💾 Сохранить", command=save_dish,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold italic'), relief='flat', padx=30, pady=10).pack(side='left')

        tk.Button(btn_frame, text="❌ Отмена", command=add_window.destroy,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Cambria', 12, 'bold italic'), relief='flat', padx=30, pady=10).pack(side='right')

    def view_dish_details(self):
        """Просмотр деталей блюда"""
        selection = self.menu_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите блюдо для просмотра деталей")
            return

        # Получить данные выбранного блюда
        item = self.menu_tree.item(selection[0])
        dish_data = item['values']

        try:
            from utils.window_utils import create_centered_dialog
            details_window = create_centered_dialog(
                self.window,
                f"🍽️ Детали блюда: {dish_data[0]}",
                width=700,
                height=600,
                resizable=True
            )
        except ImportError:
            details_window = tk.Toplevel(self.window)
            details_window.title(f"🍽️ Детали блюда: {dish_data[0]}")
            details_window.geometry("700x600")
            details_window.configure(bg='white')

            # Центрировать окно
            details_window.update_idletasks()
            x = (details_window.winfo_screenwidth() // 2) - (700 // 2)
            y = (details_window.winfo_screenheight() // 2) - (600 // 2)
            details_window.geometry(f"700x600+{x}+{y}")

        # Заголовок
        tk.Label(details_window, text=f"🍽️ Подробная информация о блюде",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Основная информация
        info_frame = tk.LabelFrame(details_window, text="📋 Основная информация",
                                  font=('Cambria', 14, 'bold'), bg='white')
        info_frame.pack(fill='x', padx=20, pady=10)

        info_data = [
            ("Название блюда:", dish_data[0]),
            ("Категория:", dish_data[1]),
            ("Цена:", f"{dish_data[2]} ₽"),
            ("Себестоимость:", f"{dish_data[3]} ₽"),
            ("Маржа:", f"{dish_data[4]}%"),
            ("Время приготовления:", f"{dish_data[5]} мин"),
            ("Популярность:", dish_data[6])
        ]

        for i, (label, value) in enumerate(info_data):
            row_frame = tk.Frame(info_frame, bg='white')
            row_frame.pack(fill='x', padx=10, pady=5)

            tk.Label(row_frame, text=label, font=('Cambria', 12, 'bold'),
                    bg='white', width=20, anchor='w').pack(side='left')
            tk.Label(row_frame, text=value, font=('Cambria', 12),
                    bg='white', anchor='w').pack(side='left', padx=(10, 0))

        # Ингредиенты
        ingredients_frame = tk.LabelFrame(details_window, text="🥕 Состав ингредиентов",
                                        font=('Cambria', 14, 'bold'), bg='white')
        ingredients_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Таблица ингредиентов
        ing_columns = ('Ингредиент', 'Количество', 'Единица', 'Стоимость')
        ing_tree = ttk.Treeview(ingredients_frame, columns=ing_columns, show='headings', height=8)

        for col in ing_columns:
            ing_tree.heading(col, text=col)
            ing_tree.column(col, width=150)

        # Примеры ингредиентов в зависимости от блюда
        if "Стейк" in dish_data[0]:
            ingredients = [
                ("Говядина (стейк)", "250", "г", "450₽"),
                ("Соль морская", "5", "г", "2₽"),
                ("Перец чёрный", "2", "г", "3₽"),
                ("Масло оливковое", "15", "мл", "8₽"),
                ("Розмарин", "2", "веточки", "5₽"),
                ("Чеснок", "2", "зубчика", "3₽")
            ]
        elif "Паста" in dish_data[0]:
            ingredients = [
                ("Паста пенне", "120", "г", "25₽"),
                ("Сливки 33%", "100", "мл", "35₽"),
                ("Сыр пармезан", "30", "г", "45₽"),
                ("Бекон", "50", "г", "65₽"),
                ("Яйцо", "1", "шт", "8₽"),
                ("Чеснок", "1", "зубчик", "2₽")
            ]
        elif "Салат" in dish_data[0]:
            ingredients = [
                ("Листья салата", "80", "г", "25₽"),
                ("Помидоры черри", "100", "г", "45₽"),
                ("Огурцы", "50", "г", "15₽"),
                ("Сыр фета", "40", "г", "55₽"),
                ("Оливки", "20", "г", "25₽"),
                ("Масло оливковое", "10", "мл", "5₽")
            ]
        else:
            ingredients = [
                ("Основной ингредиент", "200", "г", "100₽"),
                ("Специи", "5", "г", "10₽"),
                ("Масло", "10", "мл", "5₽"),
                ("Соль", "2", "г", "1₽")
            ]

        for ingredient in ingredients:
            ing_tree.insert('', 'end', values=ingredient)

        ing_tree.pack(fill='both', expand=True, padx=10, pady=10)

        # Кнопки действий
        btn_frame = tk.Frame(details_window, bg='white')
        btn_frame.pack(fill='x', padx=20, pady=20)

        tk.Button(btn_frame, text="✏️ Редактировать", command=lambda: messagebox.showinfo("Редактирование", "Функция редактирования открыта"),
                 bg='#3498db', fg='white', font=('Cambria', 10, 'bold'),
                 relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(btn_frame, text="📊 Аналитика", command=lambda: messagebox.showinfo("Аналитика", "Аналитика блюда открыта"),
                 bg='#9b59b6', fg='white', font=('Cambria', 10, 'bold'),
                 relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(btn_frame, text="🖨️ Печать", command=lambda: messagebox.showinfo("Печать", "Карточка блюда отправлена на печать"),
                 bg='#27ae60', fg='white', font=('Cambria', 10, 'bold'),
                 relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(btn_frame, text="❌ Закрыть", command=details_window.destroy,
                 bg='#e74c3c', fg='white', font=('Cambria', 10, 'bold'),
                 relief='flat', padx=15, pady=5).pack(side='right', padx=5)

    def edit_dish(self):
        """Редактировать блюдо"""
        selection = self.menu_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите блюдо для редактирования")
            return

        # Получить выбранное блюдо
        item = self.menu_tree.item(selection[0])
        dish_name = item['values'][0]

        # Найти блюдо в списке
        selected_dish = None
        for dish in self.current_menu:
            if dish['name'] == dish_name:
                selected_dish = dish
                break

        if not selected_dish:
            messagebox.showerror("Ошибка", "Блюдо не найдено")
            return

        try:
            from utils.window_utils import create_centered_dialog
            edit_window = create_centered_dialog(
                self.window,
                f"✏️ Редактировать: {dish_name}",
                width=800,
                height=700,
                resizable=True
            )
        except ImportError:
            edit_window = tk.Toplevel(self.window)
            edit_window.title(f"✏️ Редактировать: {dish_name}")
            edit_window.geometry("800x700")
            edit_window.configure(bg=ModernStyles.COLORS['bg_main'])
            edit_window.resizable(True, True)

            # Центрировать окно
            edit_window.update_idletasks()
            x = (edit_window.winfo_screenwidth() // 2) - (800 // 2)
            y = (edit_window.winfo_screenheight() // 2) - (700 // 2)
            edit_window.geometry(f"800x700+{x}+{y}")

        # Заголовок
        tk.Label(edit_window, text=f"Редактировать блюдо: {dish_name}",
                font=('Cambria', 18, 'bold italic'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

        # Основная форма
        form_frame = tk.Frame(edit_window, bg=ModernStyles.COLORS['bg_main'])
        form_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # Поля формы с предзаполненными данными
        fields = [
            ("Название блюда:", "name", "entry", selected_dish['name']),
            ("Категория:", "category", "combo", selected_dish['category']),
            ("Цена (₽):", "price", "entry", str(selected_dish['price'])),
            ("Себестоимость (₽):", "cost", "entry", str(selected_dish['cost'])),
            ("Время приготовления (мин):", "prep_time", "entry", str(selected_dish['prep_time'])),
            ("Сезон:", "season", "combo", selected_dish['season']),
            ("Калории:", "calories", "entry", str(selected_dish['calories'])),
            ("Ингредиенты:", "ingredients", "text", ', '.join(selected_dish['ingredients'])),
            ("Аллергены:", "allergens", "text", ', '.join(selected_dish['allergens'])),
            ("Популярность (%):", "popularity", "entry", str(selected_dish['popularity']))
        ]

        entries = {}
        row = 0

        for label_text, field_name, field_type, default_value in fields:
            tk.Label(form_frame, text=label_text, font=('Cambria', 12, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).grid(row=row, column=0, sticky='nw', pady=8, padx=(0, 10))

            if field_type == "entry":
                entry = tk.Entry(form_frame, font=('Cambria', 12), width=40)
                entry.grid(row=row, column=1, sticky='ew', pady=8)
                entry.insert(0, default_value)
                entries[field_name] = entry

            elif field_type == "combo":
                if field_name == "category":
                    values = ["Супы", "Салаты", "Горячие блюда", "Десерты", "Напитки", "Закуски"]
                elif field_name == "season":
                    values = ["Круглый год", "Зима", "Весна", "Лето", "Осень"]

                combo = ttk.Combobox(form_frame, font=('Cambria', 12), width=37, values=values, state="readonly")
                combo.grid(row=row, column=1, sticky='ew', pady=8)
                combo.set(default_value)
                entries[field_name] = combo

            elif field_type == "text":
                text_widget = tk.Text(form_frame, font=('Cambria', 11), width=40, height=3)
                text_widget.grid(row=row, column=1, sticky='ew', pady=8)
                text_widget.insert('1.0', default_value)
                entries[field_name] = text_widget

            row += 1

        # Настройка сетки
        form_frame.grid_columnconfigure(1, weight=1)

        # Кнопки
        btn_frame = tk.Frame(edit_window, bg=ModernStyles.COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=30, pady=20)

        def save_changes():
            try:
                # Получить данные из формы
                name = entries['name'].get().strip()
                category = entries['category'].get()
                price = float(entries['price'].get() or 0)
                cost = float(entries['cost'].get() or 0)
                prep_time = int(entries['prep_time'].get() or 0)
                season = entries['season'].get()
                calories = int(entries['calories'].get() or 0)
                popularity = int(entries['popularity'].get() or 50)

                # Получить текст из Text виджетов
                ingredients_text = entries['ingredients'].get('1.0', 'end-1c').strip()
                allergens_text = entries['allergens'].get('1.0', 'end-1c').strip()

                # Валидация
                if not name:
                    messagebox.showerror("Ошибка", "Введите название блюда")
                    return
                if price <= 0:
                    messagebox.showerror("Ошибка", "Цена должна быть больше 0")
                    return
                if cost < 0:
                    messagebox.showerror("Ошибка", "Себестоимость не может быть отрицательной")
                    return

                # Обновить данные блюда
                selected_dish['name'] = name
                selected_dish['category'] = category
                selected_dish['price'] = price
                selected_dish['cost'] = cost
                selected_dish['margin'] = price - cost
                selected_dish['prep_time'] = prep_time
                selected_dish['season'] = season
                selected_dish['calories'] = calories
                selected_dish['popularity'] = max(0, min(100, popularity))
                selected_dish['ingredients'] = [ing.strip() for ing in ingredients_text.split(',') if ing.strip()]
                selected_dish['allergens'] = [all.strip() for all in allergens_text.split(',') if all.strip()]

                # Обновить отображение
                self.refresh_menu()

                # Закрыть окно
                edit_window.destroy()

                messagebox.showinfo("Успех", f"Блюдо '{name}' обновлено!")

            except ValueError as e:
                messagebox.showerror("Ошибка", "Проверьте правильность введённых числовых значений")
            except Exception as e:
                messagebox.showerror("Ошибка", f"Ошибка при обновлении блюда: {e}")

        tk.Button(btn_frame, text="💾 Сохранить изменения", command=save_changes,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold italic'), relief='flat', padx=30, pady=10).pack(side='left')

        tk.Button(btn_frame, text="❌ Отмена", command=edit_window.destroy,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Cambria', 12, 'bold italic'), relief='flat', padx=30, pady=10).pack(side='right')

    def remove_dish(self):
        """Удалить блюдо"""
        selection = self.menu_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите блюдо для удаления")
            return

        # Получить выбранное блюдо
        item = self.menu_tree.item(selection[0])
        dish_name = item['values'][0]

        if messagebox.askyesno("Подтверждение", f"Удалить блюдо '{dish_name}' из меню?\n\nЭто действие нельзя отменить."):
            try:
                # Найти и удалить блюдо из списка
                for i, dish in enumerate(self.current_menu):
                    if dish['name'] == dish_name:
                        removed_dish = self.current_menu.pop(i)
                        break
                else:
                    messagebox.showerror("Ошибка", "Блюдо не найдено в меню")
                    return

                # Обновить отображение
                self.refresh_menu()

                messagebox.showinfo("Успех", f"Блюдо '{dish_name}' удалено из меню")

            except Exception as e:
                messagebox.showerror("Ошибка", f"Ошибка при удалении блюда: {e}")

    def refresh_menu(self):
        """Обновить отображение меню"""
        try:
            # Очистить таблицу
            for item in self.menu_tree.get_children():
                self.menu_tree.delete(item)

            # Заполнить актуальными данными
            for dish in self.current_menu:
                margin_pct = (dish['margin'] / dish['price'] * 100) if dish['price'] > 0 else 0

                self.menu_tree.insert('', 'end', values=(
                    dish['name'],
                    dish['category'],
                    f"{dish['price']:.0f}₽",
                    f"{dish['cost']:.0f}₽",
                    f"{dish['margin']:.0f}₽",
                    f"{margin_pct:.1f}%",
                    f"{dish['popularity']}%",
                    dish['season'],
                    dish['status']
                ))
        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка обновления меню: {e}")

    def update_seasonal_menu(self):
        """Обновить сезонное меню"""
        current_season = self.get_current_season()

        # Показать диалог с рекомендациями
        try:
            from utils.window_utils import create_centered_dialog
            season_window = create_centered_dialog(
                self.window,
                f"🌟 Сезонное обновление: {current_season}",
                width=700,
                height=500,
                resizable=True
            )
        except ImportError:
            season_window = tk.Toplevel(self.window)
            season_window.title(f"🌟 Сезонное обновление: {current_season}")
            season_window.geometry("700x500")
            season_window.configure(bg=ModernStyles.COLORS['bg_main'])
            season_window.resizable(True, True)

            # Центрировать окно
            season_window.update_idletasks()
            x = (season_window.winfo_screenwidth() // 2) - (700 // 2)
            y = (season_window.winfo_screenheight() // 2) - (500 // 2)
            season_window.geometry(f"700x500+{x}+{y}")

        # Заголовок
        season_icon = self.get_season_icon(current_season)
        tk.Label(season_window, text=f"{season_icon} Сезонное обновление меню",
                font=('Cambria', 18, 'bold italic'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

        # Информация о сезоне
        info_frame = tk.Frame(season_window, bg=ModernStyles.COLORS['bg_main'])
        info_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # Текущий сезон
        tk.Label(info_frame, text=f"Текущий сезон: {current_season}",
                font=('Cambria', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', pady=10)

        # Рекомендуемые блюда для сезона
        seasonal_dishes = self.seasonal_plans.get(current_season, [])
        if seasonal_dishes:
            tk.Label(info_frame, text="Рекомендуемые сезонные блюда:",
                    font=('Cambria', 12, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', pady=(20, 5))

            for dish in seasonal_dishes[:5]:
                tk.Label(info_frame, text=f"• {dish}",
                        font=('Cambria', 11), bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', padx=20)

        # Текущие сезонные блюда в меню
        current_seasonal = [dish for dish in self.current_menu if dish['season'] == current_season]
        tk.Label(info_frame, text=f"Текущие блюда для сезона '{current_season}': {len(current_seasonal)}",
                font=('Cambria', 12, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', pady=(20, 5))

        if current_seasonal:
            for dish in current_seasonal:
                tk.Label(info_frame, text=f"• {dish['name']} - {dish['price']}₽",
                        font=('Cambria', 11), bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', padx=20)

        # Кнопки
        btn_frame = tk.Frame(season_window, bg=ModernStyles.COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=30, pady=20)

        def apply_seasonal_update():
            # Обновить популярность сезонных блюд
            updated_count = 0
            for dish in self.current_menu:
                if dish['season'] == current_season:
                    dish['popularity'] = min(100, dish['popularity'] + 10)
                    updated_count += 1
                elif dish['season'] != "Круглый год":
                    dish['popularity'] = max(20, dish['popularity'] - 5)

            self.refresh_menu()
            season_window.destroy()
            messagebox.showinfo("Обновление завершено",
                               f"Сезонное меню обновлено!\n"
                               f"Популярность {updated_count} сезонных блюд увеличена.")

        tk.Button(btn_frame, text="✅ Применить обновление", command=apply_seasonal_update,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold italic'), relief='flat', padx=30, pady=10).pack(side='left')

        tk.Button(btn_frame, text="❌ Отмена", command=season_window.destroy,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Cambria', 12, 'bold italic'), relief='flat', padx=30, pady=10).pack(side='right')

    def analyze_menu_performance(self):
        """Анализ эффективности меню"""
        analysis_window = tk.Toplevel(self.window)
        analysis_window.title("📊 Анализ Эффективности Меню")
        analysis_window.geometry("600x500")
        analysis_window.configure(bg='white')

        # Заголовок
        tk.Label(analysis_window, text="📊 Анализ Эффективности Меню",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        # Содержимое анализа
        analysis_text = tk.Text(analysis_window, font=('Arial', 10), wrap='word')
        analysis_text.pack(fill='both', expand=True, padx=20, pady=20)

        # Генерировать анализ
        total_dishes = len(self.current_menu)
        avg_margin = sum(dish['margin'] for dish in self.current_menu) / total_dishes if total_dishes > 0 else 0
        avg_popularity = sum(dish['popularity'] for dish in self.current_menu) / total_dishes if total_dishes > 0 else 0
        total_revenue = sum(sale['revenue'] for sale in self.sales_analytics)

        analysis_content = f"""
АНАЛИЗ ЭФФЕКТИВНОСТИ МЕНЮ
========================

Дата анализа: {datetime.now().strftime('%d.%m.%Y %H:%M')}

ОБЩИЕ ПОКАЗАТЕЛИ:
• Всего блюд в меню: {total_dishes}
• Средняя маржа: {avg_margin:.0f}₽
• Средняя популярность: {avg_popularity:.1f}%
• Выручка за неделю: {total_revenue:,}₽

АНАЛИЗ ПО КАТЕГОРИЯМ:
"""

        # Анализ по категориям
        categories = {}
        for dish in self.current_menu:
            cat = dish['category']
            if cat not in categories:
                categories[cat] = {'count': 0, 'avg_margin': 0, 'avg_popularity': 0}
            categories[cat]['count'] += 1
            categories[cat]['avg_margin'] += dish['margin']
            categories[cat]['avg_popularity'] += dish['popularity']

        for cat, data in categories.items():
            if data['count'] > 0:
                data['avg_margin'] /= data['count']
                data['avg_popularity'] /= data['count']
                analysis_content += f"• {cat}: {data['count']} блюд, маржа {data['avg_margin']:.0f}₽, популярность {data['avg_popularity']:.1f}%\n"

        analysis_content += f"""

РЕКОМЕНДАЦИИ:
"""

        recommendations = self.generate_menu_recommendations()
        for rec in recommendations:
            analysis_content += f"• {rec}\n"

        analysis_content += f"""

СЕЗОННЫЙ АНАЛИЗ:
• Текущий сезон: {self.get_current_season()}
• Сезонных блюд в меню: {len([d for d in self.current_menu if d['season'] == self.get_current_season()])}
• Рекомендуется добавить: {', '.join(self.seasonal_plans.get(self.get_current_season(), [])[:3])}
        """

        analysis_text.insert('1.0', analysis_content)
        analysis_text.config(state='disabled')

def create_menu_planning_system(parent, db_manager):
    """Создать систему планирования меню"""
    system = MenuPlanningSystem(parent, db_manager)
    system.create_window()
    return system