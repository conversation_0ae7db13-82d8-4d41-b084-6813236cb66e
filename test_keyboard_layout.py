"""
Тестовый скрипт для проверки автоматического переключения раскладки клавиатуры
на английский язык при открытии окна входа в систему
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager
from gui.login_window import LoginWindow
from gui.simple_login import SimpleLoginWindow

def test_keyboard_layout_switching():
    """Тестирование автоматического переключения раскладки клавиатуры"""
    print("🧪 Тестирование автоматического переключения раскладки клавиатуры")
    print("=" * 70)
    print("Новая функциональность:")
    print("✅ Автоматическое переключение на английскую раскладку при открытии окна входа")
    print("✅ Работает для основного и простого окна входа")
    print("✅ Использует Windows API для переключения раскладки")
    print("=" * 70)
    
    try:
        # Инициализация базы данных
        db_manager = DatabaseManager()
        print("✅ База данных инициализирована")
        
        choice = input("\nВыберите тест:\n1 - Основное окно входа\n2 - Простое окно входа\n3 - Оба окна\nВаш выбор (1-3): ")
        
        if choice == "1" or choice == "3":
            print("\n🚀 Тестирование основного окна входа...")
            print("Обратите внимание:")
            print("- Раскладка клавиатуры должна автоматически переключиться на английский")
            print("- Фокус установлен на поле 'Имя пользователя'")
            print("- Можно вводить логин и пароль на английском языке")
            
            # Показать основное окно входа
            login_window = LoginWindow(db_manager)
            authenticated_user = login_window.run()
            
            if authenticated_user:
                print(f"✅ Успешный вход: {authenticated_user['username']}")
            else:
                print("❌ Вход отменен или неуспешен")
        
        if choice == "2" or choice == "3":
            print("\n🚀 Тестирование простого окна входа...")
            print("Обратите внимание:")
            print("- Раскладка клавиатуры должна автоматически переключиться на английский")
            print("- Фокус установлен на поле 'Имя пользователя'")
            
            # Показать простое окно входа
            simple_login = SimpleLoginWindow(db_manager)
            authenticated_user = simple_login.run()
            
            if authenticated_user:
                print(f"✅ Успешный вход: {authenticated_user['username']}")
            else:
                print("❌ Вход отменен или неуспешен")
            
    except Exception as e:
        print(f"❌ Ошибка тестирования: {e}")
        import traceback
        traceback.print_exc()

def test_keyboard_api():
    """Тестирование Windows API для переключения раскладки"""
    print("\n🔧 Тестирование Windows API для переключения раскладки")
    print("=" * 60)
    
    try:
        import ctypes
        import ctypes.wintypes
        
        # Windows API constants
        HWND_BROADCAST = 0xFFFF
        WM_INPUTLANGCHANGEREQUEST = 0x0050
        ENGLISH_LAYOUT = 0x04090409
        
        # Load user32.dll
        user32 = ctypes.windll.user32
        
        print("✅ Windows API загружен успешно")
        print(f"✅ HWND_BROADCAST: {HWND_BROADCAST}")
        print(f"✅ WM_INPUTLANGCHANGEREQUEST: {WM_INPUTLANGCHANGEREQUEST}")
        print(f"✅ ENGLISH_LAYOUT: {ENGLISH_LAYOUT}")
        
        # Попытка переключения раскладки
        result = user32.PostMessageW(HWND_BROADCAST, WM_INPUTLANGCHANGEREQUEST, 0, ENGLISH_LAYOUT)
        
        if result:
            print("✅ Команда переключения раскладки отправлена успешно")
        else:
            print("⚠️ Команда переключения раскладки не была отправлена")
            
    except Exception as e:
        print(f"❌ Ошибка тестирования API: {e}")

def main():
    """Главная функция тестирования"""
    print("🏪 Тестирование автоматического переключения раскладки клавиатуры")
    print("=" * 80)
    print("Описание функциональности:")
    print("- При открытии окна входа в систему раскладка автоматически переключается на английский")
    print("- Это упрощает ввод логина и пароля, которые обычно на английском языке")
    print("- Функция работает на Windows через Windows API")
    print("=" * 80)
    
    choice = input("\nВыберите тест:\n1 - Тестирование окон входа\n2 - Тестирование API\n3 - Полное тестирование\nВаш выбор (1-3): ")
    
    if choice == "1":
        test_keyboard_layout_switching()
    elif choice == "2":
        test_keyboard_api()
    elif choice == "3":
        test_keyboard_api()
        test_keyboard_layout_switching()
    else:
        print("Неверный выбор. Запуск полного тестирования...")
        test_keyboard_api()
        test_keyboard_layout_switching()
    
    print("\n✅ Тестирование завершено!")
    print("Новая функциональность:")
    print("- Автоматическое переключение раскладки на английский ✅")
    print("- Работает в основном окне входа ✅")
    print("- Работает в простом окне входа ✅")
    print("- Использует Windows API для надежного переключения ✅")

if __name__ == "__main__":
    main()
