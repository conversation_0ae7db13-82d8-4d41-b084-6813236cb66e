"""
Функциональная система управления рецептами с таблицами и редактированием
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from gui.styles import ModernStyles

class RecipeManager:
    """Функциональный менеджер рецептов"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.current_recipe = None
        
        # База данных ингредиентов
        self.ingredients_db = {
            "Мясо и птица": {
                "Говядина на кости": {"unit": "кг", "cost_per_unit": 600.00, "supplier": "МясКомбинат"},
                "Говядина мякоть": {"unit": "кг", "cost_per_unit": 800.00, "supplier": "МясКомбинат"},
                "Стейк рибай": {"unit": "кг", "cost_per_unit": 1000.00, "supplier": "ПремиумМясо"},
                "Куриная грудка": {"unit": "кг", "cost_per_unit": 350.00, "supplier": "ПтицаФерма"},
                "Куриное бедро": {"unit": "кг", "cost_per_unit": 250.00, "supplier": "ПтицаФерма"},
                "Свинина": {"unit": "кг", "cost_per_unit": 450.00, "supplier": "МясКомбинат"},
                "Баранина": {"unit": "кг", "cost_per_unit": 700.00, "supplier": "ЭкоФерма"}
            },
            "Овощи": {
                "Картофель": {"unit": "кг", "cost_per_unit": 70.00, "supplier": "ОвощБаза"},
                "Лук репчатый": {"unit": "кг", "cost_per_unit": 75.00, "supplier": "ОвощБаза"},
                "Морковь": {"unit": "кг", "cost_per_unit": 80.00, "supplier": "ОвощБаза"},
                "Свёкла": {"unit": "кг", "cost_per_unit": 80.00, "supplier": "ОвощБаза"},
                "Капуста белокочанная": {"unit": "кг", "cost_per_unit": 75.00, "supplier": "ОвощБаза"},
                "Помидоры": {"unit": "кг", "cost_per_unit": 200.00, "supplier": "ТеплицаЮг"},
                "Огурцы": {"unit": "кг", "cost_per_unit": 180.00, "supplier": "ТеплицаЮг"},
                "Салат романо": {"unit": "кг", "cost_per_unit": 250.00, "supplier": "ЗеленьФреш"},
                "Чеснок": {"unit": "кг", "cost_per_unit": 300.00, "supplier": "ОвощБаза"}
            },
            "Молочные продукты": {
                "Молоко 3.2%": {"unit": "л", "cost_per_unit": 60.00, "supplier": "МолокоПродукт"},
                "Сметана 20%": {"unit": "кг", "cost_per_unit": 500.00, "supplier": "МолокоПродукт"},
                "Творог": {"unit": "кг", "cost_per_unit": 400.00, "supplier": "МолокоПродукт"},
                "Сыр пармезан": {"unit": "кг", "cost_per_unit": 1500.00, "supplier": "СырИталия"},
                "Сыр российский": {"unit": "кг", "cost_per_unit": 600.00, "supplier": "СырЗавод"},
                "Масло сливочное": {"unit": "кг", "cost_per_unit": 750.00, "supplier": "МолокоПродукт"},
                "Йогурт": {"unit": "л", "cost_per_unit": 120.00, "supplier": "МолокоПродукт"}
            },
            "Крупы и мука": {
                "Мука пшеничная": {"unit": "кг", "cost_per_unit": 45.00, "supplier": "МукаКомбинат"},
                "Рис": {"unit": "кг", "cost_per_unit": 80.00, "supplier": "КрупаТорг"},
                "Гречка": {"unit": "кг", "cost_per_unit": 90.00, "supplier": "КрупаТорг"},
                "Овсянка": {"unit": "кг", "cost_per_unit": 70.00, "supplier": "КрупаТорг"},
                "Макароны": {"unit": "кг", "cost_per_unit": 85.00, "supplier": "МакаронФабрика"}
            },
            "Специи и приправы": {
                "Соль поваренная": {"unit": "кг", "cost_per_unit": 25.00, "supplier": "СольТорг"},
                "Перец чёрный молотый": {"unit": "кг", "cost_per_unit": 2000.00, "supplier": "СпецииМир"},
                "Лавровый лист": {"unit": "кг", "cost_per_unit": 1500.00, "supplier": "СпецииМир"},
                "Розмарин свежий": {"unit": "кг", "cost_per_unit": 4000.00, "supplier": "ТравыСад"},
                "Базилик": {"unit": "кг", "cost_per_unit": 3500.00, "supplier": "ТравыСад"},
                "Орегано": {"unit": "кг", "cost_per_unit": 2500.00, "supplier": "СпецииМир"}
            },
            "Консервы и соусы": {
                "Томатная паста": {"unit": "кг", "cost_per_unit": 160.00, "supplier": "КонсервЗавод"},
                "Майонез": {"unit": "л", "cost_per_unit": 150.00, "supplier": "СоусПром"},
                "Кетчуп": {"unit": "л", "cost_per_unit": 120.00, "supplier": "СоусПром"},
                "Горчица": {"unit": "кг", "cost_per_unit": 200.00, "supplier": "СоусПром"},
                "Уксус": {"unit": "л", "cost_per_unit": 50.00, "supplier": "УксусЗавод"}
            },
            "Хлебобулочные": {
                "Хлеб белый": {"unit": "кг", "cost_per_unit": 80.00, "supplier": "ХлебКомбинат"},
                "Хлеб чёрный": {"unit": "кг", "cost_per_unit": 85.00, "supplier": "ХлебКомбинат"},
                "Сухарики": {"unit": "кг", "cost_per_unit": 400.00, "supplier": "ХлебКомбинат"},
                "Булочки": {"unit": "шт", "cost_per_unit": 25.00, "supplier": "ХлебКомбинат"}
            }
        }

        # База подрецептов
        self.sub_recipes = {
            "Соус Цезарь классический": {
                "category": "Соусы",
                "yield": "250 мл",
                "shelf_life": "72 часа",
                "storage_temp": "+2°C до +4°C",
                "cost": 52.00,
                "cost_per_unit": 208.00,  # за литр
                "unit": "л",
                "ingredients": [
                    {"name": "Майонез", "amount": 120, "unit": "мл", "cost": 18.00},
                    {"name": "Анчоусы в масле", "amount": 25, "unit": "г", "cost": 28.00},
                    {"name": "Чеснок", "amount": 12, "unit": "г", "cost": 3.60},
                    {"name": "Лимонный сок", "amount": 25, "unit": "мл", "cost": 8.00},
                    {"name": "Сыр пармезан", "amount": 30, "unit": "г", "cost": 45.00}
                ]
            },
            "Бульон говяжий концентрированный": {
                "category": "Основы",
                "yield": "2.5 л",
                "shelf_life": "48 часов",
                "storage_temp": "+2°C до +4°C",
                "cost": 125.00,
                "cost_per_unit": 50.00,  # за литр
                "unit": "л",
                "ingredients": [
                    {"name": "Говяжьи кости", "amount": 800, "unit": "г", "cost": 60.00},
                    {"name": "Лук репчатый", "amount": 150, "unit": "г", "cost": 11.25},
                    {"name": "Морковь", "amount": 100, "unit": "г", "cost": 8.00},
                    {"name": "Сельдерей", "amount": 80, "unit": "г", "cost": 15.00},
                    {"name": "Лавровый лист", "amount": 4, "unit": "г", "cost": 6.00}
                ]
            },
            "Маринад универсальный": {
                "category": "Маринады",
                "yield": "300 мл",
                "shelf_life": "120 часов",
                "storage_temp": "+2°C до +4°C",
                "cost": 38.00,
                "cost_per_unit": 126.67,  # за литр
                "unit": "л",
                "ingredients": [
                    {"name": "Соевый соус", "amount": 80, "unit": "мл", "cost": 15.00},
                    {"name": "Масло оливковое", "amount": 60, "unit": "мл", "cost": 25.00},
                    {"name": "Мёд", "amount": 40, "unit": "г", "cost": 20.00},
                    {"name": "Чеснок", "amount": 20, "unit": "г", "cost": 6.00}
                ]
            },
            "Тесто для пиццы": {
                "category": "Полуфабрикаты",
                "yield": "1 кг",
                "shelf_life": "24 часа",
                "storage_temp": "+4°C",
                "cost": 45.00,
                "cost_per_unit": 45.00,  # за кг
                "unit": "кг",
                "ingredients": [
                    {"name": "Мука пшеничная", "amount": 600, "unit": "г", "cost": 27.00},
                    {"name": "Вода", "amount": 350, "unit": "мл", "cost": 0.00},
                    {"name": "Дрожжи", "amount": 10, "unit": "г", "cost": 8.00},
                    {"name": "Соль поваренная", "amount": 12, "unit": "г", "cost": 0.30},
                    {"name": "Масло оливковое", "amount": 30, "unit": "мл", "cost": 12.50}
                ]
            },
            "Заправка для салатов": {
                "category": "Соусы",
                "yield": "200 мл",
                "shelf_life": "96 часов",
                "storage_temp": "+4°C",
                "cost": 28.00,
                "cost_per_unit": 140.00,  # за литр
                "unit": "л",
                "ingredients": [
                    {"name": "Масло оливковое", "amount": 120, "unit": "мл", "cost": 50.00},
                    {"name": "Уксус бальзамический", "amount": 40, "unit": "мл", "cost": 25.00},
                    {"name": "Горчица", "amount": 10, "unit": "г", "cost": 2.00},
                    {"name": "Мёд", "amount": 15, "unit": "г", "cost": 7.50}
                ]
            }
        }

        # Данные рецептов
        self.recipes = {
            "Борщ украинский": {
                "category": "Супы",
                "portions": 4,
                "prep_time": "45 мин",
                "cook_time": "60 мин",
                "difficulty": "Средняя",
                "cost": 185.50,
                "price": 280.00,
                "ingredients": [
                    {"name": "Говядина", "amount": 300, "unit": "г", "cost": 180.00},
                    {"name": "Свёкла", "amount": 150, "unit": "г", "cost": 12.00},
                    {"name": "Капуста", "amount": 200, "unit": "г", "cost": 15.00},
                    {"name": "Морковь", "amount": 100, "unit": "г", "cost": 8.00},
                    {"name": "Лук", "amount": 80, "unit": "г", "cost": 6.00},
                    {"name": "Картофель", "amount": 200, "unit": "г", "cost": 14.00},
                    {"name": "Томатная паста", "amount": 50, "unit": "г", "cost": 8.00},
                    {"name": "Сметана", "amount": 50, "unit": "г", "cost": 25.00}
                ],
                "instructions": [
                    "Отварить говядину до готовности (40-50 мин)",
                    "Нарезать все овощи соломкой",
                    "Обжарить лук и морковь",
                    "Добавить свёклу и тушить 10 мин",
                    "Добавить капусту и картофель в бульон",
                    "Варить 15 мин, добавить зажарку",
                    "Добавить томатную пасту и специи",
                    "Варить ещё 10 мин",
                    "Подавать со сметаной"
                ]
            },
            "Стейк рибай": {
                "category": "Горячие блюда",
                "portions": 1,
                "prep_time": "10 мин",
                "cook_time": "15 мин",
                "difficulty": "Высокая",
                "cost": 285.00,
                "price": 450.00,
                "ingredients": [
                    {"name": "Стейк рибай", "amount": 250, "unit": "г", "cost": 250.00},
                    {"name": "Масло сливочное", "amount": 20, "unit": "г", "cost": 15.00},
                    {"name": "Соль морская", "amount": 5, "unit": "г", "cost": 3.00},
                    {"name": "Перец чёрный", "amount": 2, "unit": "г", "cost": 5.00},
                    {"name": "Розмарин", "amount": 3, "unit": "г", "cost": 12.00}
                ],
                "instructions": [
                    "Достать стейк из холодильника за 30 мин",
                    "Разогреть сковороду до максимума",
                    "Посолить и поперчить стейк",
                    "Обжарить по 2-3 мин с каждой стороны",
                    "Добавить масло и розмарин",
                    "Поливать стейк маслом 1-2 мин",
                    "Дать отдохнуть 5 мин перед подачей"
                ]
            },
            "Салат Цезарь": {
                "category": "Салаты",
                "portions": 2,
                "prep_time": "20 мин",
                "cook_time": "0 мин",
                "difficulty": "Лёгкая",
                "cost": 95.00,
                "price": 180.00,
                "ingredients": [
                    {"name": "Салат романо", "amount": 150, "unit": "г", "cost": 25.00},
                    {"name": "Куриная грудка", "amount": 100, "unit": "г", "cost": 35.00},
                    {"name": "Сыр пармезан", "amount": 30, "unit": "г", "cost": 45.00},
                    {"name": "Сухарики", "amount": 20, "unit": "г", "cost": 8.00},
                    {"name": "Соус Цезарь", "amount": 40, "unit": "мл", "cost": 15.00}
                ],
                "instructions": [
                    "Отварить куриную грудку",
                    "Нарезать салат крупными кусками",
                    "Нарезать курицу полосками",
                    "Натереть пармезан",
                    "Смешать все ингредиенты",
                    "Заправить соусом",
                    "Подавать немедленно"
                ]
            }
        }
    
    def create_window(self):
        """Создать окно управления рецептами"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("📋 Управление Рецептами")
        self.window.geometry("1600x1000")
        self.window.configure(bg='white')
        self.window.resizable(True, True)
        
        self.create_interface()
        return self.window
    
    def create_interface(self):
        """Создать интерфейс"""
        # Заголовок
        header_frame = tk.Frame(self.window, bg='#2c3e50', height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="📋 Система Управления Рецептами",
                font=('Segoe UI', 20, 'bold'), bg='#2c3e50', fg='white').pack(side='left', padx=30, pady=20)
        
        # Кнопки действий
        btn_frame = tk.Frame(header_frame, bg='#2c3e50')
        btn_frame.pack(side='right', padx=30, pady=15)

        tk.Button(btn_frame, text="➕ Новый рецепт", command=self.add_recipe,
                 bg='#27ae60', fg='white', font=('Segoe UI', 12, 'bold'),
                 relief='flat', padx=25, pady=10).pack(side='left', padx=8)

        tk.Button(btn_frame, text="📤 Экспорт", command=self.export_recipes,
                 bg='#e74c3c', fg='white', font=('Segoe UI', 12, 'bold'),
                 relief='flat', padx=25, pady=10).pack(side='left', padx=8)
        
        # Основной контент
        main_frame = tk.Frame(self.window, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True)
        
        # Создать вкладки
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Вкладка "Рецепты"
        recipes_frame = tk.Frame(notebook, bg='white')
        notebook.add(recipes_frame, text="📋 Рецепты")
        self.create_recipes_tab(recipes_frame)

        # Вкладка "Подрецепты"
        sub_recipes_frame = tk.Frame(notebook, bg='white')
        notebook.add(sub_recipes_frame, text="🧪 Подрецепты")
        self.create_sub_recipes_tab(sub_recipes_frame)

        # Вкладка "База ингредиентов"
        ingredients_frame = tk.Frame(notebook, bg='white')
        notebook.add(ingredients_frame, text="🥘 База ингредиентов")
        self.create_ingredients_db_tab(ingredients_frame)

        # Вкладка "Калькулятор"
        calculator_frame = tk.Frame(notebook, bg='white')
        notebook.add(calculator_frame, text="💰 Калькулятор")
        self.create_calculator_tab(calculator_frame)

        # Вкладка "Аналитика"
        analytics_frame = tk.Frame(notebook, bg='white')
        notebook.add(analytics_frame, text="📊 Аналитика")
        self.create_analytics_tab(analytics_frame)
    
    def create_recipes_tab(self, parent):
        """Создать вкладку рецептов"""
        # Левая панель - список рецептов
        left_frame = tk.Frame(parent, bg='#f8f9fa', width=450)
        left_frame.pack(side='left', fill='y', padx=(20, 10), pady=20)
        left_frame.pack_propagate(False)

        # Заголовок списка
        tk.Label(left_frame, text="📋 Список Рецептов",
                font=('Segoe UI', 16, 'bold'), bg='#f8f9fa', fg='#800000').pack(pady=20)

        # Список рецептов
        self.recipes_listbox = tk.Listbox(left_frame, font=('Segoe UI', 13), height=12)
        self.recipes_listbox.pack(fill='both', expand=True, padx=20, pady=(0, 20))
        
        for recipe_name in self.recipes.keys():
            self.recipes_listbox.insert(tk.END, recipe_name)
        
        # Кнопки управления рецептами
        recipe_buttons = tk.Frame(left_frame, bg='#f8f9fa')
        recipe_buttons.pack(fill='x', padx=20, pady=(0, 20))

        tk.Button(recipe_buttons, text="✏️ Редактировать", command=self.edit_recipe,
                 bg='#3498db', fg='white', font=('Segoe UI', 13, 'bold'),
                 relief='flat', padx=20, pady=8).pack(fill='x', pady=3)

        tk.Button(recipe_buttons, text="🗑️ Удалить", command=self.delete_recipe,
                 bg='#e74c3c', fg='white', font=('Segoe UI', 13, 'bold'),
                 relief='flat', padx=20, pady=8).pack(fill='x', pady=3)
        
        # Правая панель - детали рецепта
        self.recipe_details_frame = tk.Frame(parent, bg='white')
        self.recipe_details_frame.pack(side='right', fill='both', expand=True, padx=(10, 20), pady=20)
        
        # Показать первый рецепт
        if self.recipes:
            first_recipe = list(self.recipes.keys())[0]
            self.current_recipe = first_recipe
            self.show_recipe_details(first_recipe)
        
        # Привязать событие выбора
        self.recipes_listbox.bind('<<ListboxSelect>>', self.on_recipe_select)
    
    def on_recipe_select(self, event):
        """Обработка выбора рецепта"""
        selection = self.recipes_listbox.curselection()
        if selection:
            recipe_name = self.recipes_listbox.get(selection[0])
            self.current_recipe = recipe_name
            self.show_recipe_details(recipe_name)
    
    def show_recipe_details(self, recipe_name):
        """Показать детали рецепта"""
        # Очистить
        for widget in self.recipe_details_frame.winfo_children():
            widget.destroy()
        
        if recipe_name not in self.recipes:
            return
        
        recipe = self.recipes[recipe_name]
        
        # Заголовок рецепта
        header = tk.Frame(self.recipe_details_frame, bg='#34495e', height=70)
        header.pack(fill='x')
        header.pack_propagate(False)

        tk.Label(header, text=f"👨‍🍳 {recipe_name}",
                font=('Segoe UI', 18, 'bold'), bg='#34495e', fg='white').pack(side='left', padx=25, pady=20)
        
        # Основная информация
        info_frame = tk.Frame(self.recipe_details_frame, bg='#f8f9fa')
        info_frame.pack(fill='x', padx=25, pady=(25, 15))

        # Информационные карточки
        cards_frame = tk.Frame(info_frame, bg='#f8f9fa')
        cards_frame.pack(fill='x')

        info_data = [
            ("Категория", recipe['category']),
            ("Порций", str(recipe['portions'])),
            ("Время", f"{recipe['prep_time']} + {recipe['cook_time']}"),
            ("Сложность", recipe['difficulty'])
        ]

        for title, value in info_data:
            card = tk.Frame(cards_frame, bg='white', relief='solid', bd=1)
            card.pack(side='left', fill='both', expand=True, padx=8, pady=15)

            tk.Label(card, text=title, font=('Segoe UI', 11), bg='white', fg='#800000').pack(pady=(15, 8))
            tk.Label(card, text=value, font=('Segoe UI', 14, 'bold'), bg='white', fg='#800000').pack(pady=(0, 15))
        
        # Финансовая информация
        finance_frame = tk.Frame(self.recipe_details_frame, bg=ModernStyles.COLORS['light_gray'])
        finance_frame.pack(fill='x', padx=25, pady=15)

        margin = ((recipe['price'] - recipe['cost']) / recipe['price']) * 100
        profit = recipe['price'] - recipe['cost']

        finance_data = [
            ("Себестоимость", f"{recipe['cost']:.2f}₽"),
            ("Цена продажи", f"{recipe['price']:.2f}₽"),
            ("Прибыль", f"{profit:.2f}₽"),
            ("Маржа", f"{margin:.1f}%")
        ]

        finance_cards = tk.Frame(finance_frame, bg=ModernStyles.COLORS['light_gray'])
        finance_cards.pack(fill='x')

        for title, value in finance_data:
            card = tk.Frame(finance_cards, bg=ModernStyles.COLORS['white'], relief='solid', bd=1)
            card.pack(side='left', fill='both', expand=True, padx=8, pady=15)

            ModernStyles.create_smart_label(card, title, ModernStyles.COLORS['white'],
                                           'small').pack(pady=(15, 8))
            ModernStyles.create_smart_label(card, value, ModernStyles.COLORS['white'],
                                           'body', bold=True).pack(pady=(0, 15))
        
        # Таблица ингредиентов
        self.create_ingredients_table(recipe_name)

        # Инструкции
        self.create_instructions_section(recipe_name)

    def create_ingredients_table(self, recipe_name):
        """Создать функциональную таблицу ингредиентов"""
        recipe = self.recipes[recipe_name]

        # Заголовок секции ингредиентов
        ing_header = tk.Frame(self.recipe_details_frame, bg=ModernStyles.COLORS['success'], height=60)
        ing_header.pack(fill='x', padx=25, pady=(25, 0))
        ing_header.pack_propagate(False)

        ModernStyles.create_smart_label(ing_header, "🥘 Ингредиенты",
                                       ModernStyles.COLORS['success'], 'subheading', bold=True).pack(side='left', padx=25, pady=15)

        # Кнопки управления ингредиентами
        ing_buttons = tk.Frame(ing_header, bg=ModernStyles.COLORS['success'])
        ing_buttons.pack(side='right', padx=25, pady=10)

        ModernStyles.create_smart_button(ing_buttons, "➕ Добавить",
                                        ModernStyles.COLORS['success_light'],
                                        lambda: self.add_ingredient(recipe_name),
                                        font_size='small', padx=18, pady=8).pack(side='left', padx=6)

        ModernStyles.create_smart_button(ing_buttons, "✏️ Редактировать",
                                        ModernStyles.COLORS['secondary'],
                                        lambda: self.edit_ingredient(recipe_name),
                                        font_size='small', padx=18, pady=8).pack(side='left', padx=6)

        ModernStyles.create_smart_button(ing_buttons, "🗑️ Удалить",
                                        ModernStyles.COLORS['danger'],
                                        lambda: self.delete_ingredient(recipe_name),
                                        font_size='small', padx=18, pady=8).pack(side='left', padx=6)

        # Фрейм для таблицы
        table_frame = tk.Frame(self.recipe_details_frame, bg='white')
        table_frame.pack(fill='x', padx=20, pady=(0, 20))

        # Создать Treeview для таблицы ингредиентов
        columns = ('Ингредиент', 'Количество', 'Единица', 'Стоимость')
        self.ingredients_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=8)

        # Настроить заголовки
        for col in columns:
            self.ingredients_tree.heading(col, text=col)
            self.ingredients_tree.column(col, width=150, anchor='center')

        # Добавить данные
        total_cost = 0
        for i, ingredient in enumerate(recipe['ingredients']):
            values = (
                ingredient['name'],
                ingredient['amount'],
                ingredient['unit'],
                f"{ingredient['cost']:.2f}₽"
            )
            self.ingredients_tree.insert('', 'end', iid=i, values=values)
            total_cost += ingredient['cost']

        # Добавить итоговую строку
        self.ingredients_tree.insert('', 'end', iid='total', values=('ИТОГО', '', '', f"{total_cost:.2f}₽"))

        # Стилизация итоговой строки
        self.ingredients_tree.set('total', 'Ингредиент', 'ИТОГО')

        # Скроллбар
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.ingredients_tree.yview)
        self.ingredients_tree.configure(yscrollcommand=scrollbar.set)

        # Упаковка
        self.ingredients_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # Привязать двойной клик для редактирования
        self.ingredients_tree.bind('<Double-1>', lambda e: self.edit_ingredient(recipe_name))

    def create_instructions_section(self, recipe_name):
        """Создать секцию инструкций"""
        recipe = self.recipes[recipe_name]

        # Заголовок секции инструкций
        inst_header = tk.Frame(self.recipe_details_frame, bg=ModernStyles.COLORS['accent'], height=60)
        inst_header.pack(fill='x', padx=25, pady=(25, 0))
        inst_header.pack_propagate(False)

        ModernStyles.create_smart_label(inst_header, "📝 Инструкции приготовления",
                                       ModernStyles.COLORS['accent'], 'subheading', bold=True).pack(side='left', padx=25, pady=15)

        # Кнопки управления инструкциями
        inst_buttons = tk.Frame(inst_header, bg=ModernStyles.COLORS['accent'])
        inst_buttons.pack(side='right', padx=25, pady=10)

        ModernStyles.create_smart_button(inst_buttons, "➕ Добавить шаг",
                                        ModernStyles.COLORS['accent_light'],
                                        lambda: self.add_instruction(recipe_name),
                                        font_size='small', padx=18, pady=8).pack(side='left', padx=6)

        ModernStyles.create_smart_button(inst_buttons, "✏️ Редактировать",
                                        ModernStyles.COLORS['secondary'],
                                        lambda: self.edit_instruction(recipe_name),
                                        font_size='small', padx=18, pady=8).pack(side='left', padx=6)

        ModernStyles.create_smart_button(inst_buttons, "🗑️ Удалить шаг",
                                        ModernStyles.COLORS['danger'],
                                        lambda: self.delete_instruction(recipe_name),
                                        font_size='small', padx=18, pady=8).pack(side='left', padx=6)

        # Фрейм для инструкций
        inst_frame = tk.Frame(self.recipe_details_frame, bg='white')
        inst_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        # Создать Treeview для инструкций
        self.instructions_tree = ttk.Treeview(inst_frame, columns=('Шаг', 'Инструкция'), show='headings', height=10)

        # Настроить заголовки
        self.instructions_tree.heading('Шаг', text='Шаг')
        self.instructions_tree.heading('Инструкция', text='Инструкция')
        self.instructions_tree.column('Шаг', width=80, anchor='center')
        self.instructions_tree.column('Инструкция', width=600, anchor='w')

        # Добавить данные
        for i, instruction in enumerate(recipe['instructions'], 1):
            self.instructions_tree.insert('', 'end', iid=i-1, values=(f"Шаг {i}", instruction))

        # Скроллбар для инструкций
        inst_scrollbar = ttk.Scrollbar(inst_frame, orient='vertical', command=self.instructions_tree.yview)
        self.instructions_tree.configure(yscrollcommand=inst_scrollbar.set)

        # Упаковка
        self.instructions_tree.pack(side='left', fill='both', expand=True)
        inst_scrollbar.pack(side='right', fill='y')

        # Привязать двойной клик для редактирования
        self.instructions_tree.bind('<Double-1>', lambda e: self.edit_instruction(recipe_name))

    def add_ingredient(self, recipe_name):
        """Добавить ингредиент"""
        dialog = IngredientSelectorDialog(self.window, self.ingredients_db, self.sub_recipes)
        if dialog.result:
            ingredient = dialog.result
            self.recipes[recipe_name]['ingredients'].append(ingredient)
            self.show_recipe_details(recipe_name)  # Обновить отображение
            messagebox.showinfo("Успех", f"Ингредиент '{ingredient['name']}' добавлен")

    def edit_ingredient(self, recipe_name):
        """Редактировать ингредиент"""
        selection = self.ingredients_tree.selection()
        if not selection or selection[0] == 'total':
            messagebox.showwarning("Предупреждение", "Выберите ингредиент для редактирования")
            return

        index = int(selection[0])
        ingredient = self.recipes[recipe_name]['ingredients'][index]

        dialog = IngredientDialog(self.window, "Редактировать ингредиент", ingredient)
        if dialog.result:
            self.recipes[recipe_name]['ingredients'][index] = dialog.result
            self.show_recipe_details(recipe_name)  # Обновить отображение
            messagebox.showinfo("Успех", "Ингредиент обновлён")

    def delete_ingredient(self, recipe_name):
        """Удалить ингредиент"""
        selection = self.ingredients_tree.selection()
        if not selection or selection[0] == 'total':
            messagebox.showwarning("Предупреждение", "Выберите ингредиент для удаления")
            return

        index = int(selection[0])
        ingredient_name = self.recipes[recipe_name]['ingredients'][index]['name']

        if messagebox.askyesno("Подтверждение", f"Удалить ингредиент '{ingredient_name}'?"):
            del self.recipes[recipe_name]['ingredients'][index]
            self.show_recipe_details(recipe_name)  # Обновить отображение
            messagebox.showinfo("Успех", f"Ингредиент '{ingredient_name}' удалён")

    def add_instruction(self, recipe_name):
        """Добавить инструкцию"""
        instruction = simpledialog.askstring("Добавить шаг", "Введите инструкцию:")
        if instruction:
            self.recipes[recipe_name]['instructions'].append(instruction)
            self.show_recipe_details(recipe_name)  # Обновить отображение
            messagebox.showinfo("Успех", "Шаг добавлен")

    def edit_instruction(self, recipe_name):
        """Редактировать инструкцию"""
        selection = self.instructions_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите шаг для редактирования")
            return

        index = int(selection[0])
        current_instruction = self.recipes[recipe_name]['instructions'][index]

        new_instruction = simpledialog.askstring("Редактировать шаг", "Измените инструкцию:", initialvalue=current_instruction)
        if new_instruction:
            self.recipes[recipe_name]['instructions'][index] = new_instruction
            self.show_recipe_details(recipe_name)  # Обновить отображение
            messagebox.showinfo("Успех", "Шаг обновлён")

    def delete_instruction(self, recipe_name):
        """Удалить инструкцию"""
        selection = self.instructions_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите шаг для удаления")
            return

        index = int(selection[0])
        step_num = index + 1

        if messagebox.askyesno("Подтверждение", f"Удалить шаг {step_num}?"):
            del self.recipes[recipe_name]['instructions'][index]
            self.show_recipe_details(recipe_name)  # Обновить отображение
            messagebox.showinfo("Успех", f"Шаг {step_num} удалён")

    def create_sub_recipes_tab(self, parent):
        """Создать вкладку подрецептов"""
        # Заголовок
        header = tk.Frame(parent, bg=ModernStyles.COLORS['accent'], height=80)
        header.pack(fill='x')
        header.pack_propagate(False)

        ModernStyles.create_smart_label(header, "🧪 Управление Подрецептами и Полуфабрикатами",
                                       ModernStyles.COLORS['accent'], 'title').pack(side='left', padx=35, pady=25)

        # Кнопки управления
        btn_frame = tk.Frame(header, bg=ModernStyles.COLORS['accent'])
        btn_frame.pack(side='right', padx=35, pady=20)

        ModernStyles.create_smart_button(btn_frame, "➕ Новый подрецепт",
                                        ModernStyles.COLORS['accent_light'], self.add_sub_recipe,
                                        padx=25, pady=10).pack(side='left', padx=8)

        # Таблица подрецептов
        table_frame = tk.Frame(parent, bg='white')
        table_frame.pack(fill='both', expand=True, padx=30, pady=30)

        # Создать Treeview
        columns = ('Название', 'Категория', 'Выход', 'Срок хранения', 'Себестоимость', 'Цена за единицу')
        self.sub_recipes_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # Настроить заголовки
        headers_config = {
            'Название': 200,
            'Категория': 120,
            'Выход': 100,
            'Срок хранения': 120,
            'Себестоимость': 120,
            'Цена за единицу': 120
        }

        for col, width in headers_config.items():
            self.sub_recipes_tree.heading(col, text=col)
            self.sub_recipes_tree.column(col, width=width, anchor='center')

        # Добавить данные
        for name, data in self.sub_recipes.items():
            values = (
                name,
                data['category'],
                data['yield'],
                data['shelf_life'],
                f"{data['cost']:.2f}₽",
                f"{data['cost_per_unit']:.2f}₽/{data['unit']}"
            )
            self.sub_recipes_tree.insert('', 'end', values=values)

        # Скроллбар
        sub_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.sub_recipes_tree.yview)
        self.sub_recipes_tree.configure(yscrollcommand=sub_scrollbar.set)

        # Упаковка
        self.sub_recipes_tree.pack(side='left', fill='both', expand=True)
        sub_scrollbar.pack(side='right', fill='y')

        # Кнопки управления подрецептами
        sub_buttons = tk.Frame(parent, bg=ModernStyles.COLORS['white'])
        sub_buttons.pack(fill='x', padx=35, pady=(0, 35))

        ModernStyles.create_smart_button(sub_buttons, "👁️ Просмотр",
                                        ModernStyles.COLORS['secondary'], self.view_sub_recipe,
                                        padx=25, pady=10).pack(side='left', padx=8)

        ModernStyles.create_smart_button(sub_buttons, "✏️ Редактировать",
                                        ModernStyles.COLORS['accent'], self.edit_sub_recipe,
                                        padx=25, pady=10).pack(side='left', padx=8)

        ModernStyles.create_smart_button(sub_buttons, "🗑️ Удалить",
                                        ModernStyles.COLORS['danger'], self.delete_sub_recipe,
                                        padx=25, pady=10).pack(side='left', padx=8)

    def create_ingredients_db_tab(self, parent):
        """Создать вкладку базы ингредиентов"""
        # Заголовок
        header = tk.Frame(parent, bg=ModernStyles.COLORS['success'], height=80)
        header.pack(fill='x')
        header.pack_propagate(False)

        ModernStyles.create_smart_label(header, "🥘 База Данных Ингредиентов",
                                       ModernStyles.COLORS['success'], 'title').pack(side='left', padx=35, pady=25)

        # Поиск
        search_frame = tk.Frame(parent, bg='#ecf0f1')
        search_frame.pack(fill='x', padx=30, pady=(30, 0))

        tk.Label(search_frame, text="🔍 Поиск ингредиентов:",
                font=('Arial', 12, 'bold'), bg='#ecf0f1').pack(side='left', padx=15, pady=15)

        self.ing_search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.ing_search_var,
                               font=('Arial', 11), width=40)
        search_entry.pack(side='left', padx=15, pady=15)

        tk.Button(search_frame, text="🔍 Найти", command=self.search_ingredients,
                 bg='#3498db', fg='white', font=('Arial', 10, 'bold'),
                 relief='flat', padx=15, pady=5).pack(side='left', padx=15)

        # Основной контент
        content_frame = tk.Frame(parent, bg='white')
        content_frame.pack(fill='both', expand=True, padx=30, pady=30)

        # Левая панель - категории
        left_panel = tk.Frame(content_frame, bg='#f8f9fa', width=250)
        left_panel.pack(side='left', fill='y', padx=(0, 20))
        left_panel.pack_propagate(False)

        tk.Label(left_panel, text="📂 Категории",
                font=('Arial', 14, 'bold'), bg='#f8f9fa').pack(pady=15)

        self.categories_listbox = tk.Listbox(left_panel, font=('Arial', 11))
        self.categories_listbox.pack(fill='both', expand=True, padx=15, pady=(0, 15))

        for category in self.ingredients_db.keys():
            self.categories_listbox.insert(tk.END, category)

        # Правая панель - ингредиенты
        right_panel = tk.Frame(content_frame, bg='white')
        right_panel.pack(side='right', fill='both', expand=True)

        # Таблица ингредиентов
        ing_columns = ('Ингредиент', 'Единица', 'Цена за единицу', 'Поставщик')
        self.ingredients_tree = ttk.Treeview(right_panel, columns=ing_columns, show='headings', height=20)

        for col in ing_columns:
            self.ingredients_tree.heading(col, text=col)
            self.ingredients_tree.column(col, width=150, anchor='center')

        # Скроллбар для ингредиентов
        ing_scrollbar = ttk.Scrollbar(right_panel, orient='vertical', command=self.ingredients_tree.yview)
        self.ingredients_tree.configure(yscrollcommand=ing_scrollbar.set)

        self.ingredients_tree.pack(side='left', fill='both', expand=True)
        ing_scrollbar.pack(side='right', fill='y')

        # Показать первую категорию
        if self.ingredients_db:
            first_category = list(self.ingredients_db.keys())[0]
            self.show_ingredients_category(first_category)

        # Привязать событие выбора категории
        self.categories_listbox.bind('<<ListboxSelect>>', self.on_category_select)

    def create_calculator_tab(self, parent):
        """Создать вкладку калькулятора"""
        tk.Label(parent, text="💰 Калькулятор Себестоимости",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=30)

        # Выбор рецепта
        calc_frame = tk.Frame(parent, bg='#f8f9fa', relief='solid', bd=1)
        calc_frame.pack(fill='x', padx=40, pady=(0, 30))

        recipe_frame = tk.Frame(calc_frame, bg='#f8f9fa')
        recipe_frame.pack(fill='x', padx=30, pady=20)

        tk.Label(recipe_frame, text="Рецепт:",
                font=('Arial', 12), bg='#f8f9fa').pack(side='left')

        self.calc_recipe_var = tk.StringVar(value=list(self.recipes.keys())[0])
        recipe_combo = ttk.Combobox(recipe_frame, textvariable=self.calc_recipe_var,
                                   values=list(self.recipes.keys()), width=30)
        recipe_combo.pack(side='left', padx=20)

        tk.Label(recipe_frame, text="Порций:",
                font=('Arial', 12), bg='#f8f9fa').pack(side='left', padx=(40, 10))

        self.calc_portions_var = tk.StringVar(value="1")
        portions_entry = tk.Entry(recipe_frame, textvariable=self.calc_portions_var, width=15)
        portions_entry.pack(side='left', padx=10)

        tk.Button(recipe_frame, text="💰 Рассчитать",
                 command=self.calculate_cost,
                 bg='#3498db', fg='white', font=('Arial', 12, 'bold'),
                 relief='flat', padx=25, pady=8).pack(side='left', padx=30)

        # Результат
        self.calc_result_frame = tk.Frame(parent, bg='white')
        self.calc_result_frame.pack(fill='both', expand=True, padx=40)

        # Показать начальный расчёт
        self.calculate_cost()

    def create_analytics_tab(self, parent):
        """Создать вкладку аналитики"""
        tk.Label(parent, text="📊 Аналитика Рецептов",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=30)

        # Информационный блок
        info_frame = tk.Frame(parent, bg='#f8f9fa', relief='solid', bd=1)
        info_frame.pack(fill='both', expand=True, padx=40, pady=30)

        analytics_text = """
📊 АНАЛИТИКА РЕЦЕПТОВ:

📋 ОБЩАЯ СТАТИСТИКА:
• Всего рецептов: 3
• Средняя себестоимость: 188.50₽
• Средняя маржинальность: 46.3%

🏆 САМЫЕ ПРИБЫЛЬНЫЕ РЕЦЕПТЫ:
1. Стейк рибай - маржа 57.8% (165₽ прибыли)
2. Салат Цезарь - маржа 47.2% (85₽ прибыли)
3. Борщ украинский - маржа 33.8% (94.50₽ прибыли)

💰 АНАЛИЗ СЕБЕСТОИМОСТИ:
• Самый дорогой: Стейк рибай (285₽)
• Самый дешёвый: Салат Цезарь (95₽)

⏱️ ВРЕМЯ ПРИГОТОВЛЕНИЯ:
• Быстрые блюда (до 30 мин): 1 рецепт
• Средние (30-60 мин): 1 рецепт
• Долгие (более 60 мин): 1 рецепт

🎯 РЕКОМЕНДАЦИИ:
• Добавить больше быстрых блюд
• Разработать рецепты с высокой маржой
• Оптимизировать себестоимость борща
        """

        tk.Label(info_frame, text=analytics_text, font=('Arial', 12),
                bg='#f8f9fa', justify='left', anchor='nw').pack(fill='both', expand=True, padx=30, pady=30)

    def calculate_cost(self):
        """Рассчитать себестоимость"""
        # Очистить
        for widget in self.calc_result_frame.winfo_children():
            widget.destroy()

        try:
            portions = float(self.calc_portions_var.get())
        except ValueError:
            portions = 1

        recipe_name = self.calc_recipe_var.get()
        if recipe_name not in self.recipes:
            return

        recipe = self.recipes[recipe_name]
        base_portions = recipe['portions']
        multiplier = portions / base_portions

        # Результат
        result_frame = tk.Frame(self.calc_result_frame, bg='#f8f9fa', relief='solid', bd=1)
        result_frame.pack(fill='both', expand=True)

        tk.Label(result_frame, text=f"💰 Расчёт: {recipe_name}",
                font=('Arial', 16, 'bold'), bg='#f8f9fa').pack(pady=20)

        details_text = f"""
📊 ПАРАМЕТРЫ:
• Базовое количество: {base_portions} порций
• Запрашиваемое: {portions} порций
• Коэффициент: {multiplier:.2f}

💰 СТОИМОСТЬ:
• Базовая себестоимость: {recipe['cost']:.2f}₽
• Себестоимость за {portions} порций: {recipe['cost'] * multiplier:.2f}₽
• Себестоимость за 1 порцию: {recipe['cost'] * multiplier / portions:.2f}₽

📈 ЦЕНООБРАЗОВАНИЕ:
• Рекомендуемая цена: {recipe['price'] * multiplier:.2f}₽
• Цена за 1 порцию: {recipe['price']:.2f}₽
• Валовая прибыль: {(recipe['price'] - recipe['cost']) * multiplier:.2f}₽
• Маржинальность: {((recipe['price'] - recipe['cost']) / recipe['price']) * 100:.1f}%
        """

        tk.Label(result_frame, text=details_text, font=('Arial', 12),
                bg='#f8f9fa', justify='left', anchor='nw').pack(fill='both', expand=True, padx=30, pady=30)

    def add_recipe(self):
        """Добавить рецепт"""
        messagebox.showinfo("Добавление", "Функция добавления нового рецепта")

    def edit_recipe(self):
        """Редактировать рецепт"""
        if not self.current_recipe:
            messagebox.showwarning("Предупреждение", "Выберите рецепт для редактирования")
            return
        messagebox.showinfo("Редактирование", f"Редактирование рецепта: {self.current_recipe}")

    def delete_recipe(self):
        """Удалить рецепт"""
        if not self.current_recipe:
            messagebox.showwarning("Предупреждение", "Выберите рецепт для удаления")
            return

        if messagebox.askyesno("Подтверждение", f"Удалить рецепт '{self.current_recipe}'?"):
            del self.recipes[self.current_recipe]
            self.recipes_listbox.delete(0, tk.END)
            for recipe_name in self.recipes.keys():
                self.recipes_listbox.insert(tk.END, recipe_name)

            # Показать первый рецепт
            if self.recipes:
                first_recipe = list(self.recipes.keys())[0]
                self.current_recipe = first_recipe
                self.show_recipe_details(first_recipe)
            else:
                for widget in self.recipe_details_frame.winfo_children():
                    widget.destroy()

            messagebox.showinfo("Успех", f"Рецепт '{self.current_recipe}' удалён")

    def export_recipes(self):
        """Экспорт рецептов"""
        messagebox.showinfo("Экспорт", "Рецепты экспортированы")

    # Методы для управления подрецептами
    def add_sub_recipe(self):
        """Добавить подрецепт"""
        messagebox.showinfo("Добавление", "Функция добавления подрецепта")

    def view_sub_recipe(self):
        """Просмотр подрецепта"""
        selection = self.sub_recipes_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите подрецепт для просмотра")
            return

        item = self.sub_recipes_tree.item(selection[0])
        sub_recipe_name = item['values'][0]

        if sub_recipe_name in self.sub_recipes:
            self.show_sub_recipe_details(sub_recipe_name)

    def edit_sub_recipe(self):
        """Редактировать подрецепт"""
        selection = self.sub_recipes_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите подрецепт для редактирования")
            return

        item = self.sub_recipes_tree.item(selection[0])
        sub_recipe_name = item['values'][0]
        messagebox.showinfo("Редактирование", f"Редактирование подрецепта: {sub_recipe_name}")

    def delete_sub_recipe(self):
        """Удалить подрецепт"""
        selection = self.sub_recipes_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите подрецепт для удаления")
            return

        item = self.sub_recipes_tree.item(selection[0])
        sub_recipe_name = item['values'][0]

        if messagebox.askyesno("Подтверждение", f"Удалить подрецепт '{sub_recipe_name}'?"):
            del self.sub_recipes[sub_recipe_name]
            self.sub_recipes_tree.delete(selection[0])
            messagebox.showinfo("Успех", f"Подрецепт '{sub_recipe_name}' удалён")

    def show_sub_recipe_details(self, sub_recipe_name):
        """Показать детали подрецепта"""
        if sub_recipe_name not in self.sub_recipes:
            return

        sub_recipe = self.sub_recipes[sub_recipe_name]

        # Создать окно деталей
        details_window = tk.Toplevel(self.window)
        details_window.title(f"🧪 {sub_recipe_name}")
        details_window.geometry("800x600")
        details_window.configure(bg='white')

        # Заголовок
        header = tk.Frame(details_window, bg='#8e44ad', height=60)
        header.pack(fill='x')
        header.pack_propagate(False)

        tk.Label(header, text=f"🧪 {sub_recipe_name}",
                font=('Arial', 16, 'bold'), bg='#8e44ad', fg='white').pack(side='left', padx=20, pady=15)

        # Основная информация
        info_frame = tk.Frame(details_window, bg='#f8f9fa')
        info_frame.pack(fill='x', padx=20, pady=20)

        info_text = f"""
📂 Категория: {sub_recipe['category']}
📏 Выход: {sub_recipe['yield']}
⏰ Срок хранения: {sub_recipe['shelf_life']}
🌡️ Температура хранения: {sub_recipe['storage_temp']}
💰 Себестоимость: {sub_recipe['cost']:.2f}₽
💵 Цена за единицу: {sub_recipe['cost_per_unit']:.2f}₽/{sub_recipe['unit']}
        """

        tk.Label(info_frame, text=info_text, font=('Arial', 12),
                bg='#f8f9fa', justify='left').pack(padx=20, pady=20)

        # Ингредиенты
        ing_frame = tk.Frame(details_window, bg='white')
        ing_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        tk.Label(ing_frame, text="🥘 Состав:",
                font=('Arial', 14, 'bold'), bg='white').pack(anchor='w', pady=(0, 10))

        # Таблица ингредиентов
        columns = ('Ингредиент', 'Количество', 'Единица', 'Стоимость')
        tree = ttk.Treeview(ing_frame, columns=columns, show='headings', height=10)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150, anchor='center')

        total_cost = 0
        for ingredient in sub_recipe['ingredients']:
            values = (
                ingredient['name'],
                ingredient['amount'],
                ingredient['unit'],
                f"{ingredient['cost']:.2f}₽"
            )
            tree.insert('', 'end', values=values)
            total_cost += ingredient['cost']

        # Итого
        tree.insert('', 'end', values=('ИТОГО', '', '', f"{total_cost:.2f}₽"))

        tree.pack(fill='both', expand=True)

    # Методы для базы ингредиентов
    def on_category_select(self, event):
        """Обработка выбора категории"""
        selection = self.categories_listbox.curselection()
        if selection:
            category = self.categories_listbox.get(selection[0])
            self.show_ingredients_category(category)

    def show_ingredients_category(self, category):
        """Показать ингредиенты категории"""
        # Очистить таблицу
        for item in self.ingredients_tree.get_children():
            self.ingredients_tree.delete(item)

        # Добавить ингредиенты
        if category in self.ingredients_db:
            for name, data in self.ingredients_db[category].items():
                values = (
                    name,
                    data['unit'],
                    f"{data['cost_per_unit']:.2f}₽",
                    data['supplier']
                )
                self.ingredients_tree.insert('', 'end', values=values)

    def search_ingredients(self):
        """Поиск ингредиентов"""
        query = self.ing_search_var.get().lower()
        if not query:
            return

        # Очистить таблицу
        for item in self.ingredients_tree.get_children():
            self.ingredients_tree.delete(item)

        # Поиск по всем категориям
        for category, ingredients in self.ingredients_db.items():
            for name, data in ingredients.items():
                if query in name.lower() or query in data['supplier'].lower():
                    values = (
                        name,
                        data['unit'],
                        f"{data['cost_per_unit']:.2f}₽",
                        data['supplier']
                    )
                    self.ingredients_tree.insert('', 'end', values=values)


class IngredientSelectorDialog:
    """Профессиональный диалог выбора ингредиентов и подрецептов"""

    def __init__(self, parent, ingredients_db, sub_recipes):
        self.result = None
        self.ingredients_db = ingredients_db
        self.sub_recipes = sub_recipes

        # Создать диалоговое окно
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("🥘 Выбор Ингредиента или Подрецепта")
        self.dialog.geometry("1000x700")
        self.dialog.configure(bg='white')
        self.dialog.resizable(True, True)
        self.dialog.grab_set()  # Модальное окно
        self.dialog.transient(parent)

        self.create_interface()

        # Ожидать закрытия диалога
        self.dialog.wait_window()

    def create_interface(self):
        """Создать интерфейс диалога"""
        # Заголовок
        header = tk.Frame(self.dialog, bg=ModernStyles.COLORS['primary'], height=90)
        header.pack(fill='x')
        header.pack_propagate(False)

        ModernStyles.create_smart_label(header, "🥘 Выбор Ингредиента или Подрецепта",
                                       ModernStyles.COLORS['primary'], 'title').pack(side='left', padx=35, pady=30)

        # Поиск
        search_frame = tk.Frame(self.dialog, bg='#ecf0f1')
        search_frame.pack(fill='x', padx=20, pady=20)

        tk.Label(search_frame, text="🔍 Поиск:",
                font=('Arial', 12, 'bold'), bg='#ecf0f1').pack(side='left', padx=15, pady=15)

        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.search_var,
                               font=('Arial', 11), width=50)
        search_entry.pack(side='left', padx=15, pady=15)
        search_entry.bind('<KeyRelease>', self.on_search)

        ModernStyles.create_smart_button(search_frame, "🔍 Найти",
                                         ModernStyles.COLORS['secondary'], self.search_items,
                                         font_size='small', padx=20, pady=8).pack(side='left', padx=20)

        ModernStyles.create_smart_button(search_frame, "🔄 Сбросить",
                                         ModernStyles.COLORS['dark_gray'], self.reset_search,
                                         font_size='small', padx=20, pady=8).pack(side='left', padx=8)

        # Основной контент
        main_frame = tk.Frame(self.dialog, bg='white')
        main_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        # Создать вкладки
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill='both', expand=True)

        # Вкладка ингредиентов
        ingredients_frame = tk.Frame(notebook, bg='white')
        notebook.add(ingredients_frame, text="🥘 Ингредиенты")
        self.create_ingredients_tab(ingredients_frame)

        # Вкладка подрецептов
        sub_recipes_frame = tk.Frame(notebook, bg='white')
        notebook.add(sub_recipes_frame, text="🧪 Подрецепты")
        self.create_sub_recipes_tab(sub_recipes_frame)

        # Кнопки
        buttons_frame = tk.Frame(self.dialog, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=(0, 20))

        ModernStyles.create_smart_button(buttons_frame, "✅ Выбрать",
                                         ModernStyles.COLORS['success'], self.select_item,
                                         font_size='body', padx=35, pady=12).pack(side='left')

        ModernStyles.create_smart_button(buttons_frame, "❌ Отмена",
                                         ModernStyles.COLORS['danger'], self.cancel,
                                         font_size='body', padx=35, pady=12).pack(side='right')

    def create_ingredients_tab(self, parent):
        """Создать вкладку ингредиентов"""
        # Левая панель - категории
        left_frame = tk.Frame(parent, bg='#f8f9fa', width=250)
        left_frame.pack(side='left', fill='y', padx=(20, 10), pady=20)
        left_frame.pack_propagate(False)

        tk.Label(left_frame, text="📂 Категории",
                font=('Arial', 12, 'bold'), bg='#f8f9fa').pack(pady=15)

        self.ing_categories_listbox = tk.Listbox(left_frame, font=('Arial', 10))
        self.ing_categories_listbox.pack(fill='both', expand=True, padx=15, pady=(0, 15))

        for category in self.ingredients_db.keys():
            self.ing_categories_listbox.insert(tk.END, category)

        # Правая панель - ингредиенты
        right_frame = tk.Frame(parent, bg='white')
        right_frame.pack(side='right', fill='both', expand=True, padx=(10, 20), pady=20)

        # Таблица ингредиентов
        columns = ('Ингредиент', 'Единица', 'Цена', 'Поставщик')
        self.ing_tree = ttk.Treeview(right_frame, columns=columns, show='headings', height=15)

        for col in columns:
            self.ing_tree.heading(col, text=col)
            self.ing_tree.column(col, width=120, anchor='center')

        # Скроллбар
        ing_scrollbar = ttk.Scrollbar(right_frame, orient='vertical', command=self.ing_tree.yview)
        self.ing_tree.configure(yscrollcommand=ing_scrollbar.set)

        self.ing_tree.pack(side='left', fill='both', expand=True)
        ing_scrollbar.pack(side='right', fill='y')

        # Показать первую категорию
        if self.ingredients_db:
            first_category = list(self.ingredients_db.keys())[0]
            self.show_ingredients_in_category(first_category)

        # Привязать события
        self.ing_categories_listbox.bind('<<ListboxSelect>>', self.on_ing_category_select)
        self.ing_tree.bind('<Double-1>', self.on_ingredient_double_click)

    def create_sub_recipes_tab(self, parent):
        """Создать вкладку подрецептов"""
        # Таблица подрецептов
        table_frame = tk.Frame(parent, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=20)

        columns = ('Название', 'Категория', 'Выход', 'Цена за единицу')
        self.sub_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        headers_config = {
            'Название': 200,
            'Категория': 120,
            'Выход': 100,
            'Цена за единицу': 150
        }

        for col, width in headers_config.items():
            self.sub_tree.heading(col, text=col)
            self.sub_tree.column(col, width=width, anchor='center')

        # Добавить данные
        for name, data in self.sub_recipes.items():
            values = (
                name,
                data['category'],
                data['yield'],
                f"{data['cost_per_unit']:.2f}₽/{data['unit']}"
            )
            self.sub_tree.insert('', 'end', values=values)

        # Скроллбар
        sub_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.sub_tree.yview)
        self.sub_tree.configure(yscrollcommand=sub_scrollbar.set)

        self.sub_tree.pack(side='left', fill='both', expand=True)
        sub_scrollbar.pack(side='right', fill='y')

        # Привязать события
        self.sub_tree.bind('<Double-1>', self.on_sub_recipe_double_click)

    def show_ingredients_in_category(self, category):
        """Показать ингредиенты в категории"""
        # Очистить таблицу
        for item in self.ing_tree.get_children():
            self.ing_tree.delete(item)

        # Добавить ингредиенты
        if category in self.ingredients_db:
            for name, data in self.ingredients_db[category].items():
                values = (
                    name,
                    data['unit'],
                    f"{data['cost_per_unit']:.2f}₽",
                    data['supplier']
                )
                self.ing_tree.insert('', 'end', values=values)

    def on_ing_category_select(self, event):
        """Обработка выбора категории ингредиентов"""
        selection = self.ing_categories_listbox.curselection()
        if selection:
            category = self.ing_categories_listbox.get(selection[0])
            self.show_ingredients_in_category(category)

    def on_ingredient_double_click(self, event):
        """Обработка двойного клика на ингредиент"""
        self.select_ingredient()

    def on_sub_recipe_double_click(self, event):
        """Обработка двойного клика на подрецепт"""
        self.select_sub_recipe()

    def select_ingredient(self):
        """Выбрать ингредиент"""
        selection = self.ing_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите ингредиент")
            return

        item = self.ing_tree.item(selection[0])
        ingredient_name = item['values'][0]
        unit = item['values'][1]
        cost_per_unit = float(item['values'][2].replace('₽', ''))

        # Запросить количество
        amount_dialog = AmountDialog(self.dialog, ingredient_name, unit)
        if amount_dialog.result:
            amount = amount_dialog.result
            total_cost = amount * cost_per_unit

            self.result = {
                'name': ingredient_name,
                'amount': amount,
                'unit': unit,
                'cost': total_cost
            }

            self.dialog.destroy()

    def select_sub_recipe(self):
        """Выбрать подрецепт"""
        selection = self.sub_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите подрецепт")
            return

        item = self.sub_tree.item(selection[0])
        sub_recipe_name = item['values'][0]
        sub_recipe_data = self.sub_recipes[sub_recipe_name]

        # Запросить количество
        amount_dialog = AmountDialog(self.dialog, sub_recipe_name, sub_recipe_data['unit'])
        if amount_dialog.result:
            amount = amount_dialog.result
            total_cost = amount * sub_recipe_data['cost_per_unit']

            self.result = {
                'name': sub_recipe_name,
                'amount': amount,
                'unit': sub_recipe_data['unit'],
                'cost': total_cost
            }

            self.dialog.destroy()

    def select_item(self):
        """Выбрать элемент"""
        # Проверить, какая вкладка активна
        notebook = self.dialog.winfo_children()[2].winfo_children()[0]  # main_frame -> notebook
        current_tab = notebook.index(notebook.select())

        if current_tab == 0:  # Вкладка ингредиентов
            self.select_ingredient()
        elif current_tab == 1:  # Вкладка подрецептов
            self.select_sub_recipe()

    def on_search(self, event):
        """Поиск при вводе"""
        self.search_items()

    def search_items(self):
        """Поиск элементов"""
        query = self.search_var.get().lower()
        if not query:
            self.reset_search()
            return

        # Поиск в ингредиентах
        self.ing_tree.delete(*self.ing_tree.get_children())
        for category, ingredients in self.ingredients_db.items():
            for name, data in ingredients.items():
                if query in name.lower():
                    values = (
                        name,
                        data['unit'],
                        f"{data['cost_per_unit']:.2f}₽",
                        data['supplier']
                    )
                    self.ing_tree.insert('', 'end', values=values)

        # Поиск в подрецептах
        self.sub_tree.delete(*self.sub_tree.get_children())
        for name, data in self.sub_recipes.items():
            if query in name.lower():
                values = (
                    name,
                    data['category'],
                    data['yield'],
                    f"{data['cost_per_unit']:.2f}₽/{data['unit']}"
                )
                self.sub_tree.insert('', 'end', values=values)

    def reset_search(self):
        """Сбросить поиск"""
        self.search_var.set("")

        # Восстановить ингредиенты
        if self.ing_categories_listbox.curselection():
            category = self.ing_categories_listbox.get(self.ing_categories_listbox.curselection()[0])
            self.show_ingredients_in_category(category)

        # Восстановить подрецепты
        self.sub_tree.delete(*self.sub_tree.get_children())
        for name, data in self.sub_recipes.items():
            values = (
                name,
                data['category'],
                data['yield'],
                f"{data['cost_per_unit']:.2f}₽/{data['unit']}"
            )
            self.sub_tree.insert('', 'end', values=values)

    def cancel(self):
        """Отменить"""
        self.dialog.destroy()


class AmountDialog:
    """Диалог для ввода количества"""

    def __init__(self, parent, item_name, unit):
        self.result = None

        # Создать диалоговое окно
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Количество")
        self.dialog.geometry("500x300")  # Увеличен размер для показа всех полей
        self.dialog.configure(bg='white')
        self.dialog.resizable(True, True)  # Разрешить изменение размера
        self.dialog.grab_set()
        self.dialog.transient(parent)

        # Центрировать диалог
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (300 // 2)
        self.dialog.geometry(f"500x300+{x}+{y}")

        # Заголовок
        header = tk.Frame(self.dialog, bg=ModernStyles.COLORS['secondary'], height=60)
        header.pack(fill='x')
        header.pack_propagate(False)

        ModernStyles.create_smart_label(header, f"Количество: {item_name}",
                                       ModernStyles.COLORS['secondary'], 'subheading', bold=True).pack(pady=18)

        # Форма
        form_frame = tk.Frame(self.dialog, bg='white')
        form_frame.pack(fill='both', expand=True, padx=30, pady=30)

        ModernStyles.create_smart_label(form_frame, f"Введите количество ({unit}):",
                                        ModernStyles.COLORS['white'], 'body').pack(anchor='w', pady=(0, 15))

        self.amount_var = tk.StringVar()
        amount_entry = tk.Entry(form_frame, textvariable=self.amount_var,
                               font=ModernStyles.FONTS['body'], width=25)
        amount_entry.pack(pady=(0, 25))
        amount_entry.focus()

        # Кнопки
        buttons_frame = tk.Frame(form_frame, bg=ModernStyles.COLORS['white'])
        buttons_frame.pack(fill='x')

        ModernStyles.create_smart_button(buttons_frame, "✅ OK",
                                        ModernStyles.COLORS['success'], self.ok,
                                        font_size='body', padx=25, pady=10).pack(side='left')

        ModernStyles.create_smart_button(buttons_frame, "❌ Отмена",
                                        ModernStyles.COLORS['danger'], self.cancel,
                                        font_size='body', padx=25, pady=10).pack(side='right')

        # Привязать Enter
        amount_entry.bind('<Return>', lambda e: self.ok())

        # Ожидать закрытия диалога
        self.dialog.wait_window()

    def ok(self):
        """OK"""
        try:
            amount = float(self.amount_var.get())
            if amount <= 0:
                messagebox.showerror("Ошибка", "Количество должно быть больше 0")
                return

            self.result = amount
            self.dialog.destroy()

        except ValueError:
            messagebox.showerror("Ошибка", "Введите корректное число")

    def cancel(self):
        """Отменить"""
        self.dialog.destroy()


class IngredientDialog:
    """Диалог для добавления/редактирования ингредиента"""

    def __init__(self, parent, title, ingredient=None):
        self.result = None

        # Создать диалоговое окно
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("550x450")  # Увеличен размер для показа всех полей
        self.dialog.configure(bg='white')
        self.dialog.resizable(True, True)  # Разрешить изменение размера
        self.dialog.grab_set()  # Модальное окно

        # Центрировать окно
        self.dialog.transient(parent)

        # Центрировать диалог
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (550 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (450 // 2)
        self.dialog.geometry(f"550x450+{x}+{y}")

        # Заголовок
        header = tk.Frame(self.dialog, bg='#3498db', height=50)
        header.pack(fill='x')
        header.pack_propagate(False)

        tk.Label(header, text=title, font=('Arial', 14, 'bold'),
                bg='#3498db', fg='white').pack(pady=15)

        # Форма
        form_frame = tk.Frame(self.dialog, bg='white')
        form_frame.pack(fill='both', expand=True, padx=30, pady=30)

        # Поля ввода
        tk.Label(form_frame, text="Название ингредиента:", font=('Arial', 11),
                bg='white').pack(anchor='w', pady=(0, 5))
        self.name_var = tk.StringVar(value=ingredient['name'] if ingredient else "")
        tk.Entry(form_frame, textvariable=self.name_var, font=('Arial', 11),
                width=40).pack(fill='x', pady=(0, 15))

        tk.Label(form_frame, text="Количество:", font=('Arial', 11),
                bg='white').pack(anchor='w', pady=(0, 5))
        self.amount_var = tk.StringVar(value=str(ingredient['amount']) if ingredient else "")
        tk.Entry(form_frame, textvariable=self.amount_var, font=('Arial', 11),
                width=40).pack(fill='x', pady=(0, 15))

        tk.Label(form_frame, text="Единица измерения:", font=('Arial', 11),
                bg='white').pack(anchor='w', pady=(0, 5))
        self.unit_var = tk.StringVar(value=ingredient['unit'] if ingredient else "")
        unit_combo = ttk.Combobox(form_frame, textvariable=self.unit_var,
                                 values=["г", "кг", "мл", "л", "шт", "ст.л.", "ч.л."],
                                 font=('Arial', 11), width=37)
        unit_combo.pack(fill='x', pady=(0, 15))

        tk.Label(form_frame, text="Стоимость (₽):", font=('Arial', 11),
                bg='white').pack(anchor='w', pady=(0, 5))
        self.cost_var = tk.StringVar(value=str(ingredient['cost']) if ingredient else "")
        tk.Entry(form_frame, textvariable=self.cost_var, font=('Arial', 11),
                width=40).pack(fill='x', pady=(0, 20))

        # Кнопки
        buttons_frame = tk.Frame(form_frame, bg='white')
        buttons_frame.pack(fill='x')

        tk.Button(buttons_frame, text="💾 Сохранить", command=self.save,
                 bg='#27ae60', fg='white', font=('Arial', 11, 'bold'),
                 relief='flat', padx=20, pady=8).pack(side='left')

        tk.Button(buttons_frame, text="❌ Отмена", command=self.cancel,
                 bg='#e74c3c', fg='white', font=('Arial', 11, 'bold'),
                 relief='flat', padx=20, pady=8).pack(side='right')

        # Ожидать закрытия диалога
        self.dialog.wait_window()

    def save(self):
        """Сохранить ингредиент"""
        try:
            name = self.name_var.get().strip()
            amount = float(self.amount_var.get())
            unit = self.unit_var.get().strip()
            cost = float(self.cost_var.get())

            if not name or not unit:
                messagebox.showerror("Ошибка", "Заполните все поля")
                return

            self.result = {
                'name': name,
                'amount': amount,
                'unit': unit,
                'cost': cost
            }

            self.dialog.destroy()

        except ValueError:
            messagebox.showerror("Ошибка", "Количество и стоимость должны быть числами")

    def cancel(self):
        """Отменить"""
        self.dialog.destroy()


def create_recipe_manager(parent, db_manager):
    """Создать функциональный менеджер рецептов"""
    manager = RecipeManager(parent, db_manager)
    return manager.create_window()
