#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Тест для проверки отсутствующих методов в главном окне
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gui.main_window import <PERSON><PERSON><PERSON><PERSON>

def test_missing_methods():
    """Проверить отсутствующие методы в главном окне"""
    
    # Список всех методов, которые должны быть в главном окне согласно кнопкам
    expected_methods = [
        # ОСНОВНЫЕ МОДУЛИ
        "show_dashboard",
        "show_realtime_dashboard", 
        "show_enhanced_reporting",
        "show_enhanced_mobile_web",
        "show_integration_apis",
        "show_advanced_security",
        "show_automated_backup",
        "show_multi_location_manager",
        "show_advanced_analytics",
        "show_security_management",
        "show_advanced_inventory",
        "show_kitchen_display",
        "show_sales",
        "show_import_sales",
        
        # СКЛАД И ЗАКУПКИ
        "show_inventory",
        "show_purchasing",
        "show_vendor_management",
        
        # КУХНЯ И МЕНЮ
        "show_recipes",
        "show_menu_planning",
        "show_cost_control",
        "show_quality_control",
        
        # КЛИЕНТЫ И ПЕРСОНАЛ
        "show_customer_crm",
        "show_table_booking",
        "show_staff_scheduling",
        "show_payroll",
        
        # ФИНАНСЫ И УЧЁТ
        "show_financial_dashboard",
        "show_accounting",
        
        # АНАЛИТИКА И ОТЧЁТЫ
        "show_professional_reporting",
        "show_reports_system",
        "show_advanced_reports",
        "show_ai_insights",
        
        # ИНСТРУМЕНТЫ
        "show_performance_optimization",
        "show_sync_monitor",
        "show_style_configurator",
        "show_language_settings",
        "show_payment_codes",
        "show_security_audit",
        "show_mobile_integration",
        "show_customer_relationship_management",
        "show_loyalty_rewards_system",
        "show_advanced_business_intelligence",
        "show_table_reservation_queue_management",
        "show_supply_chain_vendor_management",
        "show_financial_planning_budgeting",
        "show_performance_dashboard",
        "show_data_export_import",
        "show_notification_center",
        "show_backup_manager",
        "show_settings_manager",
    ]
    
    print("🔍 ПРОВЕРКА ОТСУТСТВУЮЩИХ МЕТОДОВ В ГЛАВНОМ ОКНЕ")
    print("=" * 60)
    
    # Создать экземпляр главного окна (без инициализации GUI)
    try:
        # Получить все методы класса MainWindow
        main_window_methods = [method for method in dir(MainWindow) if method.startswith('show_')]
        
        print(f"📊 Найдено методов show_* в MainWindow: {len(main_window_methods)}")
        print(f"📊 Ожидается методов: {len(expected_methods)}")
        print()
        
        # Найти отсутствующие методы
        missing_methods = []
        for method in expected_methods:
            if method not in main_window_methods:
                missing_methods.append(method)
        
        # Найти лишние методы
        extra_methods = []
        for method in main_window_methods:
            if method not in expected_methods:
                extra_methods.append(method)
        
        print("❌ ОТСУТСТВУЮЩИЕ МЕТОДЫ:")
        if missing_methods:
            for i, method in enumerate(missing_methods, 1):
                print(f"   {i}. {method}")
        else:
            print("   Все методы присутствуют!")
        
        print()
        print("➕ ДОПОЛНИТЕЛЬНЫЕ МЕТОДЫ:")
        if extra_methods:
            for i, method in enumerate(extra_methods, 1):
                print(f"   {i}. {method}")
        else:
            print("   Дополнительных методов нет")
        
        print()
        print("📊 РЕЗУЛЬТАТЫ:")
        print(f"   ✅ Присутствующих методов: {len(expected_methods) - len(missing_methods)}")
        print(f"   ❌ Отсутствующих методов: {len(missing_methods)}")
        print(f"   ➕ Дополнительных методов: {len(extra_methods)}")
        
        return missing_methods, extra_methods
        
    except Exception as e:
        print(f"❌ Ошибка при проверке методов: {e}")
        import traceback
        traceback.print_exc()
        return [], []

if __name__ == "__main__":
    test_missing_methods()
