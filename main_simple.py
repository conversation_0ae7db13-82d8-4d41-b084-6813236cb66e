#!/usr/bin/env python3
"""
Упрощенная версия Системы Управления Рестораном
Без сложных зависимостей - только базовый функционал
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# Добавить текущую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class SimpleRestaurantApp:
    """Упрощенная версия приложения"""
    
    def __init__(self):
        self.root = None
        self.app = None
    
    def initialize_app(self):
        """Инициализация упрощенного приложения"""
        try:
            # Импорт только базовых модулей
            from gui.main_window import MainWindow
            from gui.login_window import LoginWindow
            from database.db_manager import DatabaseManager
            
            print("=" * 60)
            print("🏪 Система Управления Рестораном (Упрощенная версия)")
            print("=" * 60)
            print("Инициализация базовых систем...")
            
            # Инициализация базы данных
            db_manager = DatabaseManager()
            print("✅ База данных инициализирована")
            
            # Показать окно входа
            print("Запуск системы входа...")
            login_window = LoginWindow(db_manager)
            authenticated_user = login_window.run()
            
            if authenticated_user:
                print(f"✅ Пользователь аутентифицирован: {authenticated_user['username']}")
                
                # Создать главное окно
                self.app = MainWindow()
                self.app.current_user = authenticated_user
                self.app.db_manager = db_manager
                
                # Обновить информацию о пользователе
                self.app.update_user_info()
                
                # Обновить статусную строку
                if hasattr(self.app, 'status_label'):
                    self.app.status_label.config(text=f"Вошёл: {authenticated_user['full_name']} | Система готова")
                
                print("✅ Приложение успешно инициализировано!")
                print("=" * 60)
                
                # Показать приветственное сообщение
                messagebox.showinfo("Добро пожаловать!", 
                                  f"Добро пожаловать, {authenticated_user['full_name']}!\n\n"
                                  "Система управления рестораном готова к работе.\n"
                                  "Все основные модули доступны.")
                
            else:
                print("❌ Аутентификация отменена или не удалась")
                sys.exit(0)
                
        except ImportError as e:
            print(f"❌ Ошибка импорта: {e}")
            messagebox.showerror("Ошибка", 
                               f"Не удалось загрузить модули:\n{e}\n\n"
                               "Убедитесь, что все файлы находятся в правильных папках.")
            sys.exit(1)
        except Exception as e:
            print(f"❌ Ошибка инициализации: {e}")
            messagebox.showerror("Ошибка", f"Ошибка запуска приложения:\n{e}")
            sys.exit(1)
    
    def run(self):
        """Запустить приложение"""
        try:
            self.initialize_app()
            if self.app:
                print("🚀 Запуск главного окна...")
                self.app.run()
        except KeyboardInterrupt:
            print("\n👋 Приложение остановлено пользователем")
        except Exception as e:
            print(f"❌ Критическая ошибка: {e}")
            messagebox.showerror("Критическая ошибка", 
                               f"Произошла критическая ошибка:\n{e}")
        finally:
            print("🔚 Завершение работы приложения")

def main():
    """Главная функция"""
    print("Запуск Системы Управления Рестораном...")
    
    # Проверить версию Python
    if sys.version_info < (3, 7):
        print("❌ Требуется Python 3.7 или выше")
        print(f"Текущая версия: {sys.version}")
        sys.exit(1)
    
    # Проверить наличие tkinter
    try:
        import tkinter
        print("✅ Tkinter доступен")
    except ImportError:
        print("❌ Tkinter не установлен")
        print("Установите tkinter: sudo apt-get install python3-tk (Linux)")
        sys.exit(1)
    
    # Создать и запустить приложение
    app = SimpleRestaurantApp()
    app.run()

if __name__ == "__main__":
    main()
