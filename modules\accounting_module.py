"""
Полностью функциональный модуль бухгалтерии
Ведение учёта, проводки, отчёты
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
import json
import os
from gui.styles import ModernStyles

class AccountingModule:
    """Функциональный модуль бухгалтерии"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        
        # Счета бухгалтерского учёта
        self.accounts = {
            "50": "Касса",
            "51": "Расчётные счета",
            "60": "Расчёты с поставщиками",
            "62": "Расчёты с покупателями",
            "70": "Расчёты с персоналом",
            "90": "Продажи",
            "20": "Основное производство",
            "10": "Материалы",
            "41": "Товары"
        }
        
        # Проводки за день
        self.journal_entries = [
            {"date": "2024-01-15", "debit": "50", "credit": "90", "amount": 45600, "description": "Выручка за день"},
            {"date": "2024-01-15", "debit": "20", "credit": "10", "amount": 15200, "description": "Списание материалов"},
            {"date": "2024-01-15", "debit": "70", "credit": "50", "amount": 28000, "description": "Выплата зарплаты"},
            {"date": "2024-01-15", "debit": "60", "credit": "51", "amount": 12500, "description": "Оплата поставщику"},
            {"date": "2024-01-14", "debit": "10", "credit": "60", "amount": 18000, "description": "Поступление товаров"},
        ]
    
    def create_window(self):
        """Создать окно модуля бухгалтерии"""
        try:
            from utils.window_utils import create_centered_dialog
            self.window = create_centered_dialog(
                self.parent,
                "📊 Модуль Бухгалтерии",
                width=1400,
                height=950,
                resizable=True
            )
        except ImportError:
            # Fallback если utils не доступны
            self.window = tk.Toplevel(self.parent)
            self.window.title("📊 Модуль Бухгалтерии")
            self.window.geometry("1400x950")
            self.window.configure(bg=ModernStyles.COLORS['bg_main'])
            self.window.resizable(True, True)

            # Центрировать окно
            self.window.update_idletasks()
            x = (self.window.winfo_screenwidth() // 2) - (1400 // 2)
            y = (self.window.winfo_screenheight() // 2) - (950 // 2)
            self.window.geometry(f"1400x950+{x}+{y}")
        self.window.resizable(True, True)
        
        self.create_interface()
    
    def create_interface(self):
        """Создать интерфейс модуля бухгалтерии"""
        # Заголовок
        header_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['primary'], height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="📊 Модуль Бухгалтерии",
                font=('Arial', 16, 'bold'), bg=ModernStyles.COLORS['primary'], 
                fg='white').pack(side='left', padx=20, pady=15)
        
        # Кнопки действий
        btn_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['primary'])
        btn_frame.pack(side='right', padx=20, pady=10)
        
        tk.Button(btn_frame, text="➕ Новая Проводка", command=self.add_journal_entry,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="📋 Оборотка", command=self.show_trial_balance,
                 bg=ModernStyles.COLORS['secondary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="💾 Экспорт", command=self.export_accounting_data,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        # Основной контент
        main_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Создать вкладки
        self.create_tabs(main_frame)
    
    def create_tabs(self, parent):
        """Создать вкладки модуля"""
        notebook = ttk.Notebook(parent)
        notebook.pack(fill='both', expand=True)
        
        # Вкладка журнала проводок
        journal_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(journal_frame, text="📝 Журнал Проводок")
        self.create_journal_tab(journal_frame)
        
        # Вкладка плана счетов
        accounts_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(accounts_frame, text="📋 План Счетов")
        self.create_accounts_tab(accounts_frame)
        
        # Вкладка отчётов
        reports_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(reports_frame, text="📊 Отчёты")
        self.create_reports_tab(reports_frame)
    
    def create_journal_tab(self, parent):
        """Создать вкладку журнала проводок"""
        # Заголовок
        tk.Label(parent, text="Журнал Хозяйственных Операций",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)
        
        # Таблица проводок
        columns = ('Дата', 'Дебет', 'Кредит', 'Сумма', 'Описание')
        self.journal_tree = ttk.Treeview(parent, columns=columns, show='headings', style="Modern.Treeview")
        
        # Настройка столбцов
        column_widths = {'Дата': 100, 'Дебет': 80, 'Кредит': 80, 'Сумма': 120, 'Описание': 300}
        for col in columns:
            self.journal_tree.heading(col, text=col)
            self.journal_tree.column(col, width=column_widths.get(col, 100))
        
        # Заполнить данными
        self.refresh_journal()
        
        # Скроллбары
        j_v_scroll = ttk.Scrollbar(parent, orient='vertical', command=self.journal_tree.yview)
        j_h_scroll = ttk.Scrollbar(parent, orient='horizontal', command=self.journal_tree.xview)
        self.journal_tree.configure(yscrollcommand=j_v_scroll.set, xscrollcommand=j_h_scroll.set)
        
        self.journal_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        j_v_scroll.pack(side='right', fill='y')
        j_h_scroll.pack(side='bottom', fill='x')
    
    def create_accounts_tab(self, parent):
        """Создать вкладку плана счетов"""
        # Заголовок
        tk.Label(parent, text="План Счетов Бухгалтерского Учёта",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)
        
        # Таблица счетов
        columns = ('Счёт', 'Наименование', 'Дебет', 'Кредит', 'Сальдо')
        accounts_tree = ttk.Treeview(parent, columns=columns, show='headings', style="Modern.Treeview")
        
        for col in columns:
            accounts_tree.heading(col, text=col)
            accounts_tree.column(col, width=150)
        
        # Заполнить план счетов с оборотами
        account_balances = self.calculate_account_balances()
        for account_code, account_name in self.accounts.items():
            balance = account_balances.get(account_code, {'debit': 0, 'credit': 0, 'balance': 0})
            accounts_tree.insert('', 'end', values=(
                account_code,
                account_name,
                f"{balance['debit']:,}₽",
                f"{balance['credit']:,}₽",
                f"{balance['balance']:,}₽"
            ))
        
        accounts_tree.pack(fill='both', expand=True, padx=10, pady=10)
    
    def create_reports_tab(self, parent):
        """Создать вкладку отчётов"""
        # Заголовок
        tk.Label(parent, text="Бухгалтерские Отчёты",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)
        
        # Кнопки отчётов
        reports_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        reports_frame.pack(fill='x', padx=20, pady=20)
        
        reports = [
            ("📊 Оборотно-сальдовая ведомость", self.generate_trial_balance),
            ("💰 Отчёт о прибылях и убытках", self.generate_income_statement),
            ("📋 Баланс", self.generate_balance_sheet),
            ("📈 Анализ движения денежных средств", self.generate_cash_flow)
        ]
        
        for i, (report_name, command) in enumerate(reports):
            row = i // 2
            col = i % 2
            
            btn = tk.Button(reports_frame, text=report_name, command=command,
                           bg=ModernStyles.COLORS['primary'], fg='white',
                           font=('Arial', 11, 'bold'), relief='flat',
                           padx=20, pady=15, width=35)
            btn.grid(row=row, column=col, padx=10, pady=10, sticky='ew')
        
        reports_frame.grid_columnconfigure(0, weight=1)
        reports_frame.grid_columnconfigure(1, weight=1)
    
    def refresh_journal(self):
        """Обновить журнал проводок"""
        # Очистить таблицу
        for item in self.journal_tree.get_children():
            self.journal_tree.delete(item)
        
        # Заполнить данными
        for entry in self.journal_entries:
            debit_account = f"{entry['debit']} ({self.accounts.get(entry['debit'], 'Неизвестно')})"
            credit_account = f"{entry['credit']} ({self.accounts.get(entry['credit'], 'Неизвестно')})"
            
            self.journal_tree.insert('', 'end', values=(
                entry['date'],
                debit_account,
                credit_account,
                f"{entry['amount']:,}₽",
                entry['description']
            ))
    
    def calculate_account_balances(self):
        """Рассчитать обороты и сальдо по счетам"""
        balances = {}
        
        for entry in self.journal_entries:
            # Дебетовый оборот
            if entry['debit'] not in balances:
                balances[entry['debit']] = {'debit': 0, 'credit': 0, 'balance': 0}
            balances[entry['debit']]['debit'] += entry['amount']
            
            # Кредитовый оборот
            if entry['credit'] not in balances:
                balances[entry['credit']] = {'debit': 0, 'credit': 0, 'balance': 0}
            balances[entry['credit']]['credit'] += entry['amount']
        
        # Рассчитать сальдо
        for account in balances:
            balances[account]['balance'] = balances[account]['debit'] - balances[account]['credit']
        
        return balances
    
    def add_journal_entry(self):
        """Добавить новую проводку"""
        entry_window = tk.Toplevel(self.window)
        entry_window.title("➕ Новая Проводка")
        entry_window.geometry("500x400")
        entry_window.configure(bg=ModernStyles.COLORS['bg_main'])
        
        # Заголовок
        tk.Label(entry_window, text="Добавить Проводку",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)
        
        # Форма
        form_frame = tk.Frame(entry_window, bg=ModernStyles.COLORS['bg_main'])
        form_frame.pack(fill='both', expand=True, padx=20)
        
        # Дата
        tk.Label(form_frame, text="Дата:", font=('Arial', 10, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).grid(row=0, column=0, sticky='w', pady=5)
        date_entry = tk.Entry(form_frame, font=('Arial', 10))
        date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
        date_entry.grid(row=0, column=1, sticky='ew', padx=(10, 0), pady=5)
        
        # Дебет
        tk.Label(form_frame, text="Дебет:", font=('Arial', 10, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).grid(row=1, column=0, sticky='w', pady=5)
        debit_combo = ttk.Combobox(form_frame, values=[f"{k} - {v}" for k, v in self.accounts.items()])
        debit_combo.grid(row=1, column=1, sticky='ew', padx=(10, 0), pady=5)
        
        # Кредит
        tk.Label(form_frame, text="Кредит:", font=('Arial', 10, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).grid(row=2, column=0, sticky='w', pady=5)
        credit_combo = ttk.Combobox(form_frame, values=[f"{k} - {v}" for k, v in self.accounts.items()])
        credit_combo.grid(row=2, column=1, sticky='ew', padx=(10, 0), pady=5)
        
        # Сумма
        tk.Label(form_frame, text="Сумма:", font=('Arial', 10, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).grid(row=3, column=0, sticky='w', pady=5)
        amount_entry = tk.Entry(form_frame, font=('Arial', 10))
        amount_entry.grid(row=3, column=1, sticky='ew', padx=(10, 0), pady=5)
        
        # Описание
        tk.Label(form_frame, text="Описание:", font=('Arial', 10, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).grid(row=4, column=0, sticky='w', pady=5)
        desc_entry = tk.Entry(form_frame, font=('Arial', 10))
        desc_entry.grid(row=4, column=1, sticky='ew', padx=(10, 0), pady=5)
        
        form_frame.grid_columnconfigure(1, weight=1)
        
        # Кнопки
        btn_frame = tk.Frame(entry_window, bg=ModernStyles.COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=20, pady=20)
        
        def save_entry():
            try:
                debit_code = debit_combo.get().split(' - ')[0]
                credit_code = credit_combo.get().split(' - ')[0]
                amount = float(amount_entry.get())
                
                new_entry = {
                    "date": date_entry.get(),
                    "debit": debit_code,
                    "credit": credit_code,
                    "amount": amount,
                    "description": desc_entry.get()
                }
                
                self.journal_entries.append(new_entry)
                self.refresh_journal()
                entry_window.destroy()
                messagebox.showinfo("Успех", "Проводка добавлена")
                
            except Exception as e:
                messagebox.showerror("Ошибка", f"Ошибка добавления проводки: {e}")
        
        tk.Button(btn_frame, text="💾 Сохранить", command=save_entry,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(side='left')
        
        tk.Button(btn_frame, text="❌ Отмена", command=entry_window.destroy,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(side='right')
    
    def show_trial_balance(self):
        """Показать оборотно-сальдовую ведомость"""
        self.generate_trial_balance()
    
    def generate_trial_balance(self):
        """Генерировать оборотно-сальдовую ведомость"""
        report_window = tk.Toplevel(self.window)
        report_window.title("📊 Оборотно-сальдовая ведомость")
        report_window.geometry("800x600")
        report_window.configure(bg='white')
        
        # Заголовок
        tk.Label(report_window, text="📊 Оборотно-сальдовая ведомость",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)
        
        # Таблица
        columns = ('Счёт', 'Наименование', 'Дебет', 'Кредит', 'Сальдо')
        tree = ttk.Treeview(report_window, columns=columns, show='headings')
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120)
        
        # Заполнить данными
        balances = self.calculate_account_balances()
        total_debit = total_credit = 0
        
        for account_code, account_name in self.accounts.items():
            balance = balances.get(account_code, {'debit': 0, 'credit': 0, 'balance': 0})
            tree.insert('', 'end', values=(
                account_code,
                account_name,
                f"{balance['debit']:,}₽",
                f"{balance['credit']:,}₽",
                f"{balance['balance']:,}₽"
            ))
            total_debit += balance['debit']
            total_credit += balance['credit']
        
        # Итого
        tree.insert('', 'end', values=(
            "ИТОГО", "", f"{total_debit:,}₽", f"{total_credit:,}₽", f"{total_debit-total_credit:,}₽"
        ))
        
        tree.pack(fill='both', expand=True, padx=20, pady=10)
    
    def generate_income_statement(self):
        """Генерировать отчёт о прибылях и убытках"""
        messagebox.showinfo("Отчёт", "Отчёт о прибылях и убытках будет добавлен в следующей версии")
    
    def generate_balance_sheet(self):
        """Генерировать баланс"""
        messagebox.showinfo("Отчёт", "Бухгалтерский баланс будет добавлен в следующей версии")
    
    def generate_cash_flow(self):
        """Генерировать отчёт о движении денежных средств"""
        messagebox.showinfo("Отчёт", "Отчёт о движении денежных средств будет добавлен в следующей версии")
    
    def export_accounting_data(self):
        """Экспортировать данные бухгалтерии"""
        try:
            export_dir = "exports"
            if not os.path.exists(export_dir):
                os.makedirs(export_dir)
            
            filename = f"accounting_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            filepath = os.path.join(export_dir, filename)
            
            export_data = {
                "export_date": datetime.now().isoformat(),
                "accounts": self.accounts,
                "journal_entries": self.journal_entries,
                "account_balances": self.calculate_account_balances()
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            messagebox.showinfo("Успех", f"Данные экспортированы в:\n{filepath}")
            
        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка экспорта: {e}")

def create_accounting_module(parent, db_manager):
    """Создать модуль бухгалтерии"""
    module = AccountingModule(parent, db_manager)
    module.create_window()
    return module
