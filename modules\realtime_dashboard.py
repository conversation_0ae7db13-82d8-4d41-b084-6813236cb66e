"""
Real-time Dashboard and Live Updates System
Provides live sales tracking, inventory alerts, kitchen order status, and automatic data refresh
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from datetime import datetime, timedelta
import json
from gui.styles import ModernStyles
from utils.window_utils import create_standard_window, apply_standard_styling
from utils.error_handling import handle_module_error, log_info
from utils.notification_system import init_notifications

class RealTimeDashboard:
    """Real-time dashboard with live updates"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.running = False
        self.update_thread = None
        self.refresh_interval = 5  # seconds
        
        # Data containers
        self.live_data = {
            'sales': [],
            'inventory_alerts': [],
            'kitchen_orders': [],
            'notifications': []
        }
        
        # UI components
        self.sales_widgets = {}
        self.inventory_widgets = {}
        self.kitchen_widgets = {}
        self.notification_widgets = {}
        
        self.create_dashboard()
    
    def create_dashboard(self):
        """Create the real-time dashboard window"""
        try:
            self.window = create_standard_window(
                self.parent,
                "📊 Панель Управления в Реальном Времени",
                "1600x1000"
            )
            
            # Apply standard styling
            main_container = apply_standard_styling(
                self.window, 
                "Панель Управления в Реальном Времени", 
                "📊"
            )
            
            # Create header with status indicators
            self.create_header(main_container)
            
            # Create main dashboard grid
            self.create_dashboard_grid(main_container)
            
            # Create control panel
            self.create_control_panel(main_container)
            
            # Start real-time updates
            self.start_live_updates()
            
            # Initialize notification system
            self.notification_system = init_notifications(self.window)
            
            log_info("Панель управления в реальном времени создана", "RealTimeDashboard")
            
        except Exception as e:
            handle_module_error(e, "Панель управления в реальном времени", "создание панели")
    
    def create_header(self, parent):
        """Create header with live status indicators"""
        try:
            header_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
            header_frame.pack(fill='x', pady=(0, 20))
            
            # Current time display
            self.time_label = tk.Label(header_frame,
                                     text=datetime.now().strftime("%d.%m.%Y %H:%M:%S"),
                                     font=('Cambria', 16, 'bold'),
                                     fg=ModernStyles.COLORS['primary'],
                                     bg=ModernStyles.COLORS['bg_main'])
            self.time_label.pack(side='left')
            
            # Status indicators
            self.status_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['bg_main'])
            self.status_frame.pack(side='right')
            
            # Connection status
            self.connection_status = tk.Label(self.status_frame,
                                            text="🟢 Подключено",
                                            font=('Cambria', 12, 'bold'),
                                            fg=ModernStyles.COLORS['success'],
                                            bg=ModernStyles.COLORS['bg_main'])
            self.connection_status.pack(side='right', padx=10)
            
            # Auto-refresh status
            self.refresh_status = tk.Label(self.status_frame,
                                         text="🔄 Авто-обновление",
                                         font=('Cambria', 12, 'bold'),
                                         fg=ModernStyles.COLORS['info'],
                                         bg=ModernStyles.COLORS['bg_main'])
            self.refresh_status.pack(side='right', padx=10)
            
        except Exception as e:
            handle_module_error(e, "Панель управления в реальном времени", "создание заголовка")
    
    def create_dashboard_grid(self, parent):
        """Create main dashboard grid with live widgets"""
        try:
            # Create grid container
            grid_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
            grid_frame.pack(fill='both', expand=True, pady=(0, 20))
            
            # Configure grid weights
            grid_frame.grid_columnconfigure(0, weight=1)
            grid_frame.grid_columnconfigure(1, weight=1)
            grid_frame.grid_rowconfigure(0, weight=1)
            grid_frame.grid_rowconfigure(1, weight=1)
            
            # Create dashboard sections
            self.create_sales_section(grid_frame, 0, 0)
            self.create_inventory_section(grid_frame, 0, 1)
            self.create_kitchen_section(grid_frame, 1, 0)
            self.create_notifications_section(grid_frame, 1, 1)
            
        except Exception as e:
            handle_module_error(e, "Панель управления в реальном времени", "создание сетки панели")
    
    def create_sales_section(self, parent, row, col):
        """Create live sales tracking section"""
        try:
            # Sales frame
            sales_frame = tk.LabelFrame(parent, text="📈 Продажи в Реальном Времени",
                                      font=('Cambria', 14, 'bold'),
                                      fg=ModernStyles.COLORS['primary'],
                                      bg=ModernStyles.COLORS['bg_secondary'])
            sales_frame.grid(row=row, column=col, sticky='nsew', padx=10, pady=10)
            
            # Sales metrics
            metrics_frame = tk.Frame(sales_frame, bg=ModernStyles.COLORS['bg_secondary'])
            metrics_frame.pack(fill='x', padx=10, pady=10)
            
            # Today's sales
            self.sales_widgets['today_total'] = tk.Label(metrics_frame,
                                                       text="Сегодня: 0,00 руб",
                                                       font=('Cambria', 16, 'bold'),
                                                       fg=ModernStyles.COLORS['success'],
                                                       bg=ModernStyles.COLORS['bg_secondary'])
            self.sales_widgets['today_total'].pack(anchor='w')
            
            # Orders count
            self.sales_widgets['orders_count'] = tk.Label(metrics_frame,
                                                        text="Заказов: 0",
                                                        font=('Cambria', 14),
                                                        fg=ModernStyles.COLORS['text_primary'],
                                                        bg=ModernStyles.COLORS['bg_secondary'])
            self.sales_widgets['orders_count'].pack(anchor='w')
            
            # Average check
            self.sales_widgets['avg_check'] = tk.Label(metrics_frame,
                                                     text="Средний чек: 0,00 руб",
                                                     font=('Cambria', 14),
                                                     fg=ModernStyles.COLORS['text_primary'],
                                                     bg=ModernStyles.COLORS['bg_secondary'])
            self.sales_widgets['avg_check'].pack(anchor='w')
            
            # Recent orders list
            orders_label = tk.Label(sales_frame, text="Последние заказы:",
                                  font=('Cambria', 12, 'bold'),
                                  fg=ModernStyles.COLORS['text_primary'],
                                  bg=ModernStyles.COLORS['bg_secondary'])
            orders_label.pack(anchor='w', padx=10, pady=(10, 5))
            
            # Orders listbox
            orders_frame = tk.Frame(sales_frame, bg=ModernStyles.COLORS['bg_secondary'])
            orders_frame.pack(fill='both', expand=True, padx=10, pady=(0, 10))
            
            self.sales_widgets['orders_list'] = tk.Listbox(orders_frame,
                                                         font=('Cambria', 10),
                                                         bg='white',
                                                         fg=ModernStyles.COLORS['text_primary'],
                                                         height=8)
            self.sales_widgets['orders_list'].pack(fill='both', expand=True)
            
        except Exception as e:
            handle_module_error(e, "Панель управления в реальном времени", "создание секции продаж")
    
    def create_inventory_section(self, parent, row, col):
        """Create inventory alerts section"""
        try:
            # Inventory frame
            inventory_frame = tk.LabelFrame(parent, text="📦 Уведомления о Складе",
                                          font=('Cambria', 14, 'bold'),
                                          fg=ModernStyles.COLORS['warning'],
                                          bg=ModernStyles.COLORS['bg_secondary'])
            inventory_frame.grid(row=row, column=col, sticky='nsew', padx=10, pady=10)
            
            # Alert summary
            summary_frame = tk.Frame(inventory_frame, bg=ModernStyles.COLORS['bg_secondary'])
            summary_frame.pack(fill='x', padx=10, pady=10)
            
            # Low stock alerts
            self.inventory_widgets['low_stock'] = tk.Label(summary_frame,
                                                         text="🔴 Низкие запасы: 0",
                                                         font=('Cambria', 14, 'bold'),
                                                         fg=ModernStyles.COLORS['danger'],
                                                         bg=ModernStyles.COLORS['bg_secondary'])
            self.inventory_widgets['low_stock'].pack(anchor='w')
            
            # Expiring items
            self.inventory_widgets['expiring'] = tk.Label(summary_frame,
                                                        text="🟡 Истекает срок: 0",
                                                        font=('Cambria', 14, 'bold'),
                                                        fg=ModernStyles.COLORS['warning'],
                                                        bg=ModernStyles.COLORS['bg_secondary'])
            self.inventory_widgets['expiring'].pack(anchor='w')
            
            # Out of stock
            self.inventory_widgets['out_of_stock'] = tk.Label(summary_frame,
                                                            text="⚫ Нет в наличии: 0",
                                                            font=('Cambria', 14, 'bold'),
                                                            fg=ModernStyles.COLORS['text_secondary'],
                                                            bg=ModernStyles.COLORS['bg_secondary'])
            self.inventory_widgets['out_of_stock'].pack(anchor='w')
            
            # Alerts list
            alerts_label = tk.Label(inventory_frame, text="Активные уведомления:",
                                  font=('Cambria', 12, 'bold'),
                                  fg=ModernStyles.COLORS['text_primary'],
                                  bg=ModernStyles.COLORS['bg_secondary'])
            alerts_label.pack(anchor='w', padx=10, pady=(10, 5))
            
            # Alerts listbox
            alerts_frame = tk.Frame(inventory_frame, bg=ModernStyles.COLORS['bg_secondary'])
            alerts_frame.pack(fill='both', expand=True, padx=10, pady=(0, 10))
            
            self.inventory_widgets['alerts_list'] = tk.Listbox(alerts_frame,
                                                             font=('Cambria', 10),
                                                             bg='white',
                                                             fg=ModernStyles.COLORS['text_primary'],
                                                             height=8)
            self.inventory_widgets['alerts_list'].pack(fill='both', expand=True)
            
        except Exception as e:
            handle_module_error(e, "Панель управления в реальном времени", "создание секции склада")

    def create_kitchen_section(self, parent, row, col):
        """Create kitchen order status section"""
        try:
            # Kitchen frame
            kitchen_frame = tk.LabelFrame(parent, text="🍳 Статус Кухни",
                                        font=('Cambria', 14, 'bold'),
                                        fg=ModernStyles.COLORS['info'],
                                        bg=ModernStyles.COLORS['bg_secondary'])
            kitchen_frame.grid(row=row, column=col, sticky='nsew', padx=10, pady=10)

            # Kitchen metrics
            metrics_frame = tk.Frame(kitchen_frame, bg=ModernStyles.COLORS['bg_secondary'])
            metrics_frame.pack(fill='x', padx=10, pady=10)

            # Active orders
            self.kitchen_widgets['active_orders'] = tk.Label(metrics_frame,
                                                           text="🔥 Активных заказов: 0",
                                                           font=('Cambria', 14, 'bold'),
                                                           fg=ModernStyles.COLORS['danger'],
                                                           bg=ModernStyles.COLORS['bg_secondary'])
            self.kitchen_widgets['active_orders'].pack(anchor='w')

            # Pending orders
            self.kitchen_widgets['pending_orders'] = tk.Label(metrics_frame,
                                                            text="⏳ В ожидании: 0",
                                                            font=('Cambria', 14, 'bold'),
                                                            fg=ModernStyles.COLORS['warning'],
                                                            bg=ModernStyles.COLORS['bg_secondary'])
            self.kitchen_widgets['pending_orders'].pack(anchor='w')

            # Completed orders
            self.kitchen_widgets['completed_orders'] = tk.Label(metrics_frame,
                                                              text="✅ Готово: 0",
                                                              font=('Cambria', 14, 'bold'),
                                                              fg=ModernStyles.COLORS['success'],
                                                              bg=ModernStyles.COLORS['bg_secondary'])
            self.kitchen_widgets['completed_orders'].pack(anchor='w')

            # Average preparation time
            self.kitchen_widgets['avg_prep_time'] = tk.Label(metrics_frame,
                                                           text="⏱️ Среднее время: 0 мин",
                                                           font=('Cambria', 12),
                                                           fg=ModernStyles.COLORS['text_primary'],
                                                           bg=ModernStyles.COLORS['bg_secondary'])
            self.kitchen_widgets['avg_prep_time'].pack(anchor='w')

            # Orders queue
            queue_label = tk.Label(kitchen_frame, text="Очередь заказов:",
                                 font=('Cambria', 12, 'bold'),
                                 fg=ModernStyles.COLORS['text_primary'],
                                 bg=ModernStyles.COLORS['bg_secondary'])
            queue_label.pack(anchor='w', padx=10, pady=(10, 5))

            # Queue listbox
            queue_frame = tk.Frame(kitchen_frame, bg=ModernStyles.COLORS['bg_secondary'])
            queue_frame.pack(fill='both', expand=True, padx=10, pady=(0, 10))

            self.kitchen_widgets['queue_list'] = tk.Listbox(queue_frame,
                                                          font=('Cambria', 10),
                                                          bg='white',
                                                          fg=ModernStyles.COLORS['text_primary'],
                                                          height=6)
            self.kitchen_widgets['queue_list'].pack(fill='both', expand=True)

        except Exception as e:
            handle_module_error(e, "Панель управления в реальном времени", "создание секции кухни")

    def create_notifications_section(self, parent, row, col):
        """Create notifications section"""
        try:
            # Notifications frame
            notifications_frame = tk.LabelFrame(parent, text="🔔 Уведомления",
                                               font=('Cambria', 14, 'bold'),
                                               fg=ModernStyles.COLORS['secondary'],
                                               bg=ModernStyles.COLORS['bg_secondary'])
            notifications_frame.grid(row=row, column=col, sticky='nsew', padx=10, pady=10)

            # Notification summary
            summary_frame = tk.Frame(notifications_frame, bg=ModernStyles.COLORS['bg_secondary'])
            summary_frame.pack(fill='x', padx=10, pady=10)

            # Critical alerts
            self.notification_widgets['critical'] = tk.Label(summary_frame,
                                                            text="🚨 Критические: 0",
                                                            font=('Cambria', 14, 'bold'),
                                                            fg=ModernStyles.COLORS['danger'],
                                                            bg=ModernStyles.COLORS['bg_secondary'])
            self.notification_widgets['critical'].pack(anchor='w')

            # Warnings
            self.notification_widgets['warnings'] = tk.Label(summary_frame,
                                                           text="⚠️ Предупреждения: 0",
                                                           font=('Cambria', 14, 'bold'),
                                                           fg=ModernStyles.COLORS['warning'],
                                                           bg=ModernStyles.COLORS['bg_secondary'])
            self.notification_widgets['warnings'].pack(anchor='w')

            # Info notifications
            self.notification_widgets['info'] = tk.Label(summary_frame,
                                                       text="ℹ️ Информационные: 0",
                                                       font=('Cambria', 14, 'bold'),
                                                       fg=ModernStyles.COLORS['info'],
                                                       bg=ModernStyles.COLORS['bg_secondary'])
            self.notification_widgets['info'].pack(anchor='w')

            # Notifications list
            notifications_label = tk.Label(notifications_frame, text="Последние уведомления:",
                                          font=('Cambria', 12, 'bold'),
                                          fg=ModernStyles.COLORS['text_primary'],
                                          bg=ModernStyles.COLORS['bg_secondary'])
            notifications_label.pack(anchor='w', padx=10, pady=(10, 5))

            # Notifications listbox
            notifications_list_frame = tk.Frame(notifications_frame, bg=ModernStyles.COLORS['bg_secondary'])
            notifications_list_frame.pack(fill='both', expand=True, padx=10, pady=(0, 10))

            self.notification_widgets['notifications_list'] = tk.Listbox(notifications_list_frame,
                                                                       font=('Cambria', 10),
                                                                       bg='white',
                                                                       fg=ModernStyles.COLORS['text_primary'],
                                                                       height=6)
            self.notification_widgets['notifications_list'].pack(fill='both', expand=True)

        except Exception as e:
            handle_module_error(e, "Панель управления в реальном времени", "создание секции уведомлений")

    def create_control_panel(self, parent):
        """Create control panel with refresh controls"""
        try:
            control_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
            control_frame.pack(fill='x', pady=10)

            # Left side - refresh controls
            left_frame = tk.Frame(control_frame, bg=ModernStyles.COLORS['bg_main'])
            left_frame.pack(side='left')

            # Auto-refresh toggle
            self.auto_refresh_var = tk.BooleanVar(value=True)
            auto_refresh_cb = tk.Checkbutton(left_frame,
                                           text="Автоматическое обновление",
                                           variable=self.auto_refresh_var,
                                           command=self.toggle_auto_refresh,
                                           font=('Cambria', 12, 'bold'),
                                           fg=ModernStyles.COLORS['text_primary'],
                                           bg=ModernStyles.COLORS['bg_main'],
                                           selectcolor=ModernStyles.COLORS['bg_secondary'])
            auto_refresh_cb.pack(side='left', padx=10)

            # Refresh interval
            tk.Label(left_frame, text="Интервал (сек):",
                    font=('Cambria', 12),
                    fg=ModernStyles.COLORS['text_primary'],
                    bg=ModernStyles.COLORS['bg_main']).pack(side='left', padx=(20, 5))

            self.interval_var = tk.StringVar(value=str(self.refresh_interval))
            interval_entry = tk.Entry(left_frame, textvariable=self.interval_var,
                                    width=5, font=('Cambria', 12),
                                    bg='white', fg=ModernStyles.COLORS['text_primary'])
            interval_entry.pack(side='left', padx=5)
            interval_entry.bind('<Return>', self.update_refresh_interval)

            # Manual refresh button
            refresh_btn = tk.Button(left_frame, text="🔄 Обновить",
                                  command=self.manual_refresh,
                                  bg=ModernStyles.COLORS['primary'], fg='white',
                                  font=('Cambria', 12, 'bold'), relief='flat',
                                  padx=20, pady=5)
            refresh_btn.pack(side='left', padx=20)

            # Right side - action buttons
            right_frame = tk.Frame(control_frame, bg=ModernStyles.COLORS['bg_main'])
            right_frame.pack(side='right')

            # Export data button
            export_btn = tk.Button(right_frame, text="📊 Экспорт данных",
                                 command=self.export_live_data,
                                 bg=ModernStyles.COLORS['info'], fg='white',
                                 font=('Cambria', 12, 'bold'), relief='flat',
                                 padx=20, pady=5)
            export_btn.pack(side='right', padx=10)

            # Settings button
            settings_btn = tk.Button(right_frame, text="⚙️ Настройки",
                                   command=self.show_settings,
                                   bg=ModernStyles.COLORS['secondary'], fg='white',
                                   font=('Cambria', 12, 'bold'), relief='flat',
                                   padx=20, pady=5)
            settings_btn.pack(side='right', padx=10)

            # Close button
            close_btn = tk.Button(right_frame, text="❌ Закрыть",
                                command=self.close_dashboard,
                                bg=ModernStyles.COLORS['danger'], fg='white',
                                font=('Cambria', 12, 'bold'), relief='flat',
                                padx=20, pady=5)
            close_btn.pack(side='right', padx=10)

        except Exception as e:
            handle_module_error(e, "Панель управления в реальном времени", "создание панели управления")

    def start_live_updates(self):
        """Start the live update thread"""
        try:
            self.running = True
            self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
            self.update_thread.start()
            log_info("Система живых обновлений запущена", "RealTimeDashboard")

        except Exception as e:
            handle_module_error(e, "Панель управления в реальном времени", "запуск живых обновлений")

    def _update_loop(self):
        """Background update loop"""
        while self.running:
            try:
                if self.window and self.window.winfo_exists() and self.auto_refresh_var.get():
                    # Update time display
                    self.window.after(0, self.update_time_display)

                    # Fetch and update live data
                    self.fetch_live_data()
                    self.window.after(0, self.update_dashboard_data)

                    time.sleep(self.refresh_interval)
                else:
                    time.sleep(1)  # Check every second if updates should resume

            except Exception as e:
                log_info(f"Ошибка в цикле обновления: {e}", "RealTimeDashboard")
                time.sleep(5)  # Wait before retrying

    def fetch_live_data(self):
        """Fetch live data from database and external sources"""
        try:
            # Fetch sales data
            self.fetch_sales_data()

            # Fetch inventory alerts
            self.fetch_inventory_alerts()

            # Fetch kitchen orders
            self.fetch_kitchen_orders()

            # Fetch notifications
            self.fetch_notifications()

        except Exception as e:
            log_info(f"Ошибка получения живых данных: {e}", "RealTimeDashboard")

    def fetch_sales_data(self):
        """Fetch real-time sales data"""
        try:
            # Get today's sales
            today = datetime.now().strftime('%Y-%m-%d')

            # Simulate real sales data (in production, fetch from database)
            import random

            # Today's total
            today_total = random.uniform(15000, 45000)
            orders_count = random.randint(25, 85)
            avg_check = today_total / orders_count if orders_count > 0 else 0

            # Recent orders
            recent_orders = []
            for i in range(10):
                order_time = datetime.now() - timedelta(minutes=random.randint(1, 120))
                order_amount = random.uniform(500, 2500)
                recent_orders.append({
                    'time': order_time.strftime('%H:%M'),
                    'amount': order_amount,
                    'items': random.randint(1, 5)
                })

            self.live_data['sales'] = {
                'today_total': today_total,
                'orders_count': orders_count,
                'avg_check': avg_check,
                'recent_orders': recent_orders
            }

        except Exception as e:
            log_info(f"Ошибка получения данных о продажах: {e}", "RealTimeDashboard")

    def fetch_inventory_alerts(self):
        """Fetch inventory alerts"""
        try:
            import random

            # Simulate inventory alerts
            low_stock_count = random.randint(0, 8)
            expiring_count = random.randint(0, 5)
            out_of_stock_count = random.randint(0, 3)

            alerts = []

            # Generate sample alerts
            items = ['Мука', 'Молоко', 'Яйца', 'Сыр', 'Мясо', 'Овощи', 'Специи', 'Масло']

            for i in range(low_stock_count):
                item = random.choice(items)
                alerts.append(f"🔴 {item}: осталось {random.randint(1, 10)} кг")

            for i in range(expiring_count):
                item = random.choice(items)
                days = random.randint(1, 3)
                alerts.append(f"🟡 {item}: истекает через {days} дн.")

            for i in range(out_of_stock_count):
                item = random.choice(items)
                alerts.append(f"⚫ {item}: нет в наличии")

            self.live_data['inventory_alerts'] = {
                'low_stock_count': low_stock_count,
                'expiring_count': expiring_count,
                'out_of_stock_count': out_of_stock_count,
                'alerts': alerts
            }

        except Exception as e:
            log_info(f"Ошибка получения уведомлений о складе: {e}", "RealTimeDashboard")

    def fetch_kitchen_orders(self):
        """Fetch kitchen order status"""
        try:
            import random

            # Simulate kitchen orders
            active_orders = random.randint(0, 12)
            pending_orders = random.randint(0, 8)
            completed_orders = random.randint(15, 45)
            avg_prep_time = random.randint(12, 25)

            # Generate order queue
            queue = []
            dishes = ['Борщ', 'Салат Цезарь', 'Стейк', 'Паста', 'Суп', 'Пицца', 'Рыба', 'Курица']

            for i in range(min(active_orders + pending_orders, 10)):
                dish = random.choice(dishes)
                order_time = datetime.now() - timedelta(minutes=random.randint(1, 30))
                status = random.choice(['🔥 Готовится', '⏳ В ожидании', '✅ Готово'])
                queue.append(f"{order_time.strftime('%H:%M')} - {dish} ({status})")

            self.live_data['kitchen_orders'] = {
                'active_orders': active_orders,
                'pending_orders': pending_orders,
                'completed_orders': completed_orders,
                'avg_prep_time': avg_prep_time,
                'queue': queue
            }

        except Exception as e:
            log_info(f"Ошибка получения данных кухни: {e}", "RealTimeDashboard")

    def fetch_notifications(self):
        """Fetch system notifications"""
        try:
            import random

            # Simulate notifications
            critical_count = random.randint(0, 2)
            warning_count = random.randint(0, 5)
            info_count = random.randint(2, 8)

            notifications = []

            # Generate sample notifications
            critical_msgs = [
                "🚨 Система оплаты недоступна",
                "🚨 Критически низкий запас основных продуктов",
                "🚨 Превышено время ожидания заказов"
            ]

            warning_msgs = [
                "⚠️ Высокая нагрузка на кухню",
                "⚠️ Задержка поставки продуктов",
                "⚠️ Необходимо обновление меню",
                "⚠️ Низкий уровень персонала"
            ]

            info_msgs = [
                "ℹ️ Новый заказ принят",
                "ℹ️ Смена персонала завершена",
                "ℹ️ Отчет за день готов",
                "ℹ️ Обновление системы доступно",
                "ℹ️ Резервирование стола подтверждено"
            ]

            # Add notifications with timestamps
            for i in range(critical_count):
                msg = random.choice(critical_msgs)
                time_ago = random.randint(1, 60)
                notifications.append(f"{msg} ({time_ago} мин назад)")

            for i in range(warning_count):
                msg = random.choice(warning_msgs)
                time_ago = random.randint(1, 120)
                notifications.append(f"{msg} ({time_ago} мин назад)")

            for i in range(info_count):
                msg = random.choice(info_msgs)
                time_ago = random.randint(1, 30)
                notifications.append(f"{msg} ({time_ago} мин назад)")

            self.live_data['notifications'] = {
                'critical_count': critical_count,
                'warning_count': warning_count,
                'info_count': info_count,
                'notifications': notifications[:15]  # Show only last 15
            }

        except Exception as e:
            log_info(f"Ошибка получения уведомлений: {e}", "RealTimeDashboard")

    def update_time_display(self):
        """Update current time display"""
        try:
            current_time = datetime.now().strftime("%d.%m.%Y %H:%M:%S")
            self.time_label.config(text=current_time)
        except Exception as e:
            log_info(f"Ошибка обновления времени: {e}", "RealTimeDashboard")

    def update_dashboard_data(self):
        """Update all dashboard widgets with live data"""
        try:
            # Update sales section
            if 'sales' in self.live_data:
                sales_data = self.live_data['sales']

                # Format currency
                today_total = f"{sales_data['today_total']:,.2f}".replace(',', ' ').replace('.', ',') + " руб"
                avg_check = f"{sales_data['avg_check']:,.2f}".replace(',', ' ').replace('.', ',') + " руб"

                self.sales_widgets['today_total'].config(text=f"Сегодня: {today_total}")
                self.sales_widgets['orders_count'].config(text=f"Заказов: {sales_data['orders_count']}")
                self.sales_widgets['avg_check'].config(text=f"Средний чек: {avg_check}")

                # Update orders list
                self.sales_widgets['orders_list'].delete(0, tk.END)
                for order in sales_data['recent_orders']:
                    order_amount = f"{order['amount']:,.2f}".replace(',', ' ').replace('.', ',')
                    order_text = f"{order['time']} - {order_amount} руб ({order['items']} поз.)"
                    self.sales_widgets['orders_list'].insert(tk.END, order_text)

            # Update inventory section
            if 'inventory_alerts' in self.live_data:
                inv_data = self.live_data['inventory_alerts']

                self.inventory_widgets['low_stock'].config(text=f"🔴 Низкие запасы: {inv_data['low_stock_count']}")
                self.inventory_widgets['expiring'].config(text=f"🟡 Истекает срок: {inv_data['expiring_count']}")
                self.inventory_widgets['out_of_stock'].config(text=f"⚫ Нет в наличии: {inv_data['out_of_stock_count']}")

                # Update alerts list
                self.inventory_widgets['alerts_list'].delete(0, tk.END)
                for alert in inv_data['alerts']:
                    self.inventory_widgets['alerts_list'].insert(tk.END, alert)

            # Update kitchen section
            if 'kitchen_orders' in self.live_data:
                kitchen_data = self.live_data['kitchen_orders']

                self.kitchen_widgets['active_orders'].config(text=f"🔥 Активных заказов: {kitchen_data['active_orders']}")
                self.kitchen_widgets['pending_orders'].config(text=f"⏳ В ожидании: {kitchen_data['pending_orders']}")
                self.kitchen_widgets['completed_orders'].config(text=f"✅ Готово: {kitchen_data['completed_orders']}")
                self.kitchen_widgets['avg_prep_time'].config(text=f"⏱️ Среднее время: {kitchen_data['avg_prep_time']} мин")

                # Update queue list
                self.kitchen_widgets['queue_list'].delete(0, tk.END)
                for order in kitchen_data['queue']:
                    self.kitchen_widgets['queue_list'].insert(tk.END, order)

            # Update notifications section
            if 'notifications' in self.live_data:
                notif_data = self.live_data['notifications']

                self.notification_widgets['critical'].config(text=f"🚨 Критические: {notif_data['critical_count']}")
                self.notification_widgets['warnings'].config(text=f"⚠️ Предупреждения: {notif_data['warning_count']}")
                self.notification_widgets['info'].config(text=f"ℹ️ Информационные: {notif_data['info_count']}")

                # Update notifications list
                self.notification_widgets['notifications_list'].delete(0, tk.END)
                for notification in notif_data['notifications']:
                    self.notification_widgets['notifications_list'].insert(tk.END, notification)

        except Exception as e:
            log_info(f"Ошибка обновления данных панели: {e}", "RealTimeDashboard")

    def toggle_auto_refresh(self):
        """Toggle auto-refresh on/off"""
        try:
            if self.auto_refresh_var.get():
                self.refresh_status.config(text="🔄 Авто-обновление", fg=ModernStyles.COLORS['info'])
                log_info("Автоматическое обновление включено", "RealTimeDashboard")
            else:
                self.refresh_status.config(text="⏸️ Приостановлено", fg=ModernStyles.COLORS['warning'])
                log_info("Автоматическое обновление отключено", "RealTimeDashboard")
        except Exception as e:
            handle_module_error(e, "Панель управления в реальном времени", "переключение авто-обновления")

    def update_refresh_interval(self, event=None):
        """Update refresh interval"""
        try:
            new_interval = int(self.interval_var.get())
            if new_interval >= 1:
                self.refresh_interval = new_interval
                log_info(f"Интервал обновления изменен на {new_interval} секунд", "RealTimeDashboard")
            else:
                messagebox.showwarning("Предупреждение", "Интервал должен быть не менее 1 секунды")
                self.interval_var.set(str(self.refresh_interval))
        except ValueError:
            messagebox.showerror("Ошибка", "Введите корректное число секунд")
            self.interval_var.set(str(self.refresh_interval))
        except Exception as e:
            handle_module_error(e, "Панель управления в реальном времени", "обновление интервала")

    def manual_refresh(self):
        """Manually refresh all data"""
        try:
            self.fetch_live_data()
            self.update_dashboard_data()
            self.update_time_display()
            log_info("Ручное обновление данных выполнено", "RealTimeDashboard")
        except Exception as e:
            handle_module_error(e, "Панель управления в реальном времени", "ручное обновление")

    def export_live_data(self):
        """Export current live data"""
        try:
            from tkinter import filedialog
            import json

            filename = filedialog.asksaveasfilename(
                title="Сохранить данные панели",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if filename:
                export_data = {
                    'timestamp': datetime.now().isoformat(),
                    'data': self.live_data
                }

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)

                messagebox.showinfo("Успех", f"Данные сохранены в файл:\n{filename}")
                log_info(f"Данные панели экспортированы в {filename}", "RealTimeDashboard")

        except Exception as e:
            handle_module_error(e, "Панель управления в реальном времени", "экспорт данных")

    def show_settings(self):
        """Show dashboard settings"""
        try:
            settings_window = tk.Toplevel(self.window)
            settings_window.title("⚙️ Настройки Панели")
            settings_window.geometry("500x400")
            settings_window.configure(bg=ModernStyles.COLORS['bg_main'])
            settings_window.transient(self.window)
            settings_window.grab_set()

            # Center the window
            settings_window.geometry("+%d+%d" % (
                self.window.winfo_rootx() + 50,
                self.window.winfo_rooty() + 50
            ))

            tk.Label(settings_window, text="⚙️ Настройки Панели Управления",
                    font=('Cambria', 16, 'bold'),
                    fg=ModernStyles.COLORS['primary'],
                    bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

            # Settings content
            settings_frame = tk.Frame(settings_window, bg=ModernStyles.COLORS['bg_main'])
            settings_frame.pack(fill='both', expand=True, padx=20, pady=10)

            tk.Label(settings_frame, text="Настройки будут добавлены в следующих версиях",
                    font=('Cambria', 12),
                    fg=ModernStyles.COLORS['text_primary'],
                    bg=ModernStyles.COLORS['bg_main']).pack(pady=50)

            # Close button
            tk.Button(settings_window, text="Закрыть",
                     command=settings_window.destroy,
                     bg=ModernStyles.COLORS['secondary'], fg='white',
                     font=('Cambria', 12, 'bold'), relief='flat',
                     padx=30, pady=10).pack(pady=20)

        except Exception as e:
            handle_module_error(e, "Панель управления в реальном времени", "показ настроек")

    def close_dashboard(self):
        """Close the dashboard"""
        try:
            self.running = False
            if self.window:
                self.window.destroy()
            log_info("Панель управления в реальном времени закрыта", "RealTimeDashboard")
        except Exception as e:
            handle_module_error(e, "Панель управления в реальном времени", "закрытие панели")

def create_realtime_dashboard(parent, db_manager):
    """Create and return real-time dashboard instance"""
    try:
        dashboard = RealTimeDashboard(parent, db_manager)
        return dashboard
    except Exception as e:
        handle_module_error(e, "Панель управления в реальном времени", "создание экземпляра")
        return None
