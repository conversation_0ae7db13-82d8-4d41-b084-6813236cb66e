"""
Простое окно входа с видимой кнопкой ОК
"""

import tkinter as tk
from tkinter import messagebox
import hashlib

class SimpleLoginWindow:
    """Простое окно входа"""
    
    def __init__(self, db_manager, restaurant_info=None):
        self.db_manager = db_manager
        self.restaurant_info = restaurant_info
        self.authenticated_user = None
        self.root = None
        self.create_simple_login()
    
    def create_simple_login(self):
        """Создать простое окно входа"""
        self.root = tk.Tk()
        self.root.title("Вход в систему")
        self.root.geometry("600x500")
        self.root.configure(bg='#f0f0f0')
        self.root.resizable(True, True)
        
        # Центрировать окно
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.root.winfo_screenheight() // 2) - (500 // 2)
        self.root.geometry(f"600x500+{x}+{y}")
        
        # Заголовок
        title_label = tk.Label(self.root, text="🏪 Вход в Систему",
                              font=('Cambria', 24, 'bold'),
                              bg='#f0f0f0', fg='maroon')  # Изменили цвет на maroon
        title_label.pack(pady=30)
        
        # Информация о ресторане
        if self.restaurant_info:
            restaurant_label = tk.Label(self.root, 
                                       text=f"Ресторан: {self.restaurant_info['name']}",
                                       font=('Cambria', 16, 'italic'),
                                       bg='#f0f0f0', fg='#34495e')
            restaurant_label.pack(pady=10)
        
        # Контейнер для формы
        form_frame = tk.Frame(self.root, bg='white', relief='solid', bd=2)
        form_frame.pack(padx=50, pady=30, fill='x')
        
        # Поле имени пользователя
        tk.Label(form_frame, text="Имя пользователя:",
                font=('Cambria', 14, 'bold'), bg='white', fg='maroon').pack(pady=(20, 5))  # Изменили цвет на maroon

        self.username_var = tk.StringVar()
        self.username_entry = tk.Entry(form_frame, textvariable=self.username_var,
                                      font=('Cambria', 14), width=30, fg='maroon')  # Изменили цвет текста на maroon
        self.username_entry.pack(pady=(0, 15))
        # Фокус будет установлен в конце создания окна
        
        # Поле пароля
        tk.Label(form_frame, text="Пароль:",
                font=('Cambria', 14, 'bold'), bg='white', fg='maroon').pack(pady=(0, 5))  # Изменили цвет на maroon

        self.password_var = tk.StringVar()
        self.password_entry = tk.Entry(form_frame, textvariable=self.password_var,
                                      font=('Cambria', 14), width=30, show="*", fg='maroon')  # Изменили цвет текста на maroon
        self.password_entry.pack(pady=(0, 20))
        
        # БОЛЬШАЯ КНОПКА ОК
        ok_button = tk.Button(form_frame, text="ОК",
                             command=self.authenticate,
                             font=('Cambria', 20, 'bold'),
                             bg='#27ae60', fg='white',
                             width=15, height=2,
                             relief='raised', bd=3,
                             cursor='hand2')
        ok_button.pack(pady=20)
        
        print("✅ Большая кнопка ОК создана и размещена!")
        
        # Сообщение об ошибке
        self.error_label = tk.Label(form_frame, text="",
                                   font=('Cambria', 12, 'italic'),
                                   bg='white', fg='red')
        self.error_label.pack(pady=10)
        
        # Привязка клавиш
        self.username_entry.bind('<Return>', lambda e: self.password_entry.focus())
        self.password_entry.bind('<Return>', lambda e: self.authenticate())
        self.root.bind('<Return>', lambda e: self.authenticate())
        
        # Обновить окно
        self.root.update_idletasks()
        self.root.update()

        # Установить фокус на поле имени пользователя
        self.set_username_focus()
        print("✅ Простое окно входа создано!")

    def set_username_focus(self):
        """Установить фокус на поле имени пользователя"""
        try:
            # Немедленная установка фокуса
            self.username_entry.focus_set()
            self.username_entry.focus_force()

            # Дополнительные попытки с задержкой
            self.root.after(50, lambda: self.username_entry.focus_set())
            self.root.after(100, lambda: self.username_entry.focus_force())

            # Убедиться, что окно активно
            self.root.lift()
            self.root.attributes('-topmost', True)
            self.root.after(200, lambda: self.root.attributes('-topmost', False))

            print("Фокус установлен на поле имени пользователя")
        except Exception as e:
            print(f"Ошибка установки фокуса: {e}")

    def authenticate(self):
        """Аутентификация пользователя"""
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()
        
        print(f"Попытка входа: {username}")
        
        # Очистить предыдущие ошибки
        self.error_label.config(text="")
        
        if not username or not password:
            self.error_label.config(text="Введите имя пользователя и пароль")
            return
        
        try:
            # Проверка учетных данных
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, username, password_hash, role, full_name, is_active
                    FROM users 
                    WHERE username = ? AND is_active = 1
                ''', (username,))
                
                user = cursor.fetchone()
                
                if user:
                    stored_password = user[2]
                    # Проверка пароля
                    if (username == "admin" and password == "admin") or \
                       (stored_password == self.hash_password(password)):
                        
                        self.authenticated_user = {
                            'id': user[0],
                            'username': user[1],
                            'role': user[3],
                            'full_name': user[4] or username,
                            'is_active': user[5]
                        }
                        
                        print(f"✅ Успешный вход: {username}")
                        self.root.destroy()
                        return True
                    else:
                        self.error_label.config(text="Неверный пароль")
                else:
                    self.error_label.config(text="Неверное имя пользователя")
        
        except Exception as e:
            self.error_label.config(text="Ошибка подключения к базе данных")
            print(f"Ошибка аутентификации: {e}")
        
        # Очистить пароль при ошибке
        self.password_var.set("")
        self.set_username_focus()  # Используем наш метод для установки фокуса
        return False
    
    def hash_password(self, password):
        """Хеширование пароля"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def run(self):
        """Запустить окно входа"""
        self.root.mainloop()
        return self.authenticated_user
