# -*- coding: utf-8 -*-
"""
Russian Language Pack for Restaurant Management System
Русский языковой пакет для системы управления рестораном
"""

# Main Application
APP_TITLE = "Система Управления Рестораном"
APP_SUBTITLE = "Учёт и Управление Запасами"

# Login Window
LOGIN_TITLE = "Вход в Систему"
LOGIN_SUBTITLE = "Пожалуйста, войдите в систему"
USERNAME_LABEL = "Имя пользователя:"
PASSWORD_LABEL = "Пароль:"
SIGN_IN_BUTTON = "Войти"
FORGOT_PASSWORD = "Забыли пароль? Обратитесь к администратору"
DEFAULT_CREDENTIALS = "Учётные данные по умолчанию:"
CHANGE_PASSWORD_WARNING = "⚠️ Измените пароль по умолчанию после первого входа"

# Main Dashboard
DASHBOARD_TITLE = "Панель Управления"
WELCOME_MESSAGE = "Добро пожаловать в систему управления рестораном"
USER_INFO = "Информация о пользователе"
CHANGE_PASSWORD = "Изменить пароль"
LOGOUT = "Выйти"

# Navigation Menu
NAV_DASHBOARD = "🏠 Панель Управления"
NAV_FINANCIAL = "💰 Финансовая Панель"
NAV_SALES_DATA = "📊 Данные о Продажах"
NAV_IMPORT_SALES = "📥 Импорт Продаж"
NAV_INVENTORY = "📦 Склад"
NAV_PURCHASING = "🛒 Закупки"
NAV_VENDORS = "🏪 Поставщики"
NAV_RECIPES = "📋 Рецепты"
NAV_COST_CONTROL = "💲 Контроль Затрат"
NAV_ACCOUNTING = "🧮 Бухгалтерия"
NAV_PAYROLL = "👥 Зарплата"

# KPI Cards
KPI_TODAY_SALES = "Продажи Сегодня"
KPI_MONTHLY_REVENUE = "Месячная Выручка"
KPI_FOOD_COST = "Стоимость Продуктов %"
KPI_LABOR_COST = "Стоимость Труда %"
KPI_ORDERS_TODAY = "Заказов Сегодня"
KPI_LOW_STOCK = "Мало на Складе"
KPI_PENDING_POS = "Ожидающие Заказы"
KPI_PROFIT_MARGIN = "Маржа Прибыли"

# Recent Activity
RECENT_ACTIVITY = "Последняя Активность"
ACTIVITY_SALE_RECORDED = "Продажа записана"
ACTIVITY_INVENTORY_UPDATED = "Склад обновлён"
ACTIVITY_PURCHASE_ORDER = "Заказ на покупку создан"
ACTIVITY_PAYMENT_RECEIVED = "Платёж получен"
ACTIVITY_RECIPE_ADDED = "Рецепт добавлен"

# Sales Import
IMPORT_TITLE = "Импорт Данных о Продажах"
IMPORT_SUBTITLE = "Импорт данных о продажах из CSV"
SELECT_CSV_FILE = "Выберите CSV файл:"
LOAD_DATA_BUTTON = "Загрузить Данные"
BROWSE_BUTTON = "Обзор"
DATA_PREVIEW = "Предварительный Просмотр Данных:"
EDIT_SELECTED = "Редактировать Выбранное"
DELETE_SELECTED = "Удалить Выбранное"
CANCEL_BUTTON = "Отмена"
SAVE_TO_DATABASE = "Сохранить в Базу Данных"

# Sales Data
SALES_DATA_TITLE = "Данные о Продажах"
ORDER_DATE = "Дата Заказа"
ORDER_NUMBER = "Номер Заказа"
DISH_NAME = "Название Блюда"
QUANTITY = "Количество"
PRICE_PER_DISH = "Цена за Блюдо"
TOTAL_AMOUNT = "Общая Сумма"
PAYMENT_METHOD = "Способ Оплаты"
DEPARTMENT = "Отдел"
DISH_CODE = "Код Блюда"

# Inventory
INVENTORY_TITLE = "Управление Складом"
ITEM_NAME = "Название Товара"
CURRENT_STOCK = "Текущий Запас"
UNIT_PRICE = "Цена за Единицу"
SUPPLIER = "Поставщик"
REORDER_LEVEL = "Уровень Перезаказа"
LAST_UPDATED = "Последнее Обновление"
ADD_ITEM = "Добавить Товар"
UPDATE_STOCK = "Обновить Запас"
LOW_STOCK_ALERT = "Предупреждение о Малом Запасе"

# Financial Dashboard
FINANCIAL_TITLE = "Финансовая Панель"
REVENUE_CHART = "График Выручки"
EXPENSE_BREAKDOWN = "Разбивка Расходов"
PROFIT_LOSS = "Прибыли и Убытки"
CASH_FLOW = "Денежный Поток"

# Purchasing
PURCHASING_TITLE = "Управление Закупками"
PURCHASE_ORDER = "Заказ на Покупку"
VENDOR_NAME = "Название Поставщика"
ORDER_STATUS = "Статус Заказа"
ORDER_TOTAL = "Общая Сумма Заказа"
DELIVERY_DATE = "Дата Доставки"
CREATE_ORDER = "Создать Заказ"

# Vendors
VENDORS_TITLE = "Управление Поставщиками"
VENDOR_CODE = "Код Поставщика"
CONTACT_PERSON = "Контактное Лицо"
PHONE_NUMBER = "Номер Телефона"
EMAIL_ADDRESS = "Адрес Электронной Почты"
ADDRESS = "Адрес"
ADD_VENDOR = "Добавить Поставщика"

# Recipes
RECIPES_TITLE = "Управление Рецептами"
RECIPE_NAME = "Название Рецепта"
INGREDIENTS = "Ингредиенты"
PREPARATION_TIME = "Время Приготовления"
COOKING_TIME = "Время Готовки"
SERVINGS = "Порций"
COST_PER_SERVING = "Стоимость за Порцию"
ADD_RECIPE = "Добавить Рецепт"

# Cost Control
COST_CONTROL_TITLE = "Контроль Затрат"
FOOD_COST_ANALYSIS = "Анализ Стоимости Продуктов"
LABOR_COST_ANALYSIS = "Анализ Стоимости Труда"
OVERHEAD_COSTS = "Накладные Расходы"
VARIANCE_ANALYSIS = "Анализ Отклонений"

# Accounting
ACCOUNTING_TITLE = "Бухгалтерский Учёт"
CHART_OF_ACCOUNTS = "План Счетов"
JOURNAL_ENTRIES = "Журнальные Записи"
GENERAL_LEDGER = "Главная Книга"
TRIAL_BALANCE = "Пробный Баланс"
FINANCIAL_STATEMENTS = "Финансовые Отчёты"

# Payroll
PAYROLL_TITLE = "Управление Зарплатой"
EMPLOYEE_NAME = "Имя Сотрудника"
POSITION = "Должность"
HOURLY_RATE = "Почасовая Ставка"
HOURS_WORKED = "Отработанные Часы"
GROSS_PAY = "Валовая Зарплата"
DEDUCTIONS = "Удержания"
NET_PAY = "Чистая Зарплата"
ADD_EMPLOYEE = "Добавить Сотрудника"

# Common Buttons and Actions
ADD_BUTTON = "Добавить"
EDIT_BUTTON = "Редактировать"
DELETE_BUTTON = "Удалить"
SAVE_BUTTON = "Сохранить"
CANCEL_BUTTON = "Отмена"
SEARCH_BUTTON = "Поиск"
FILTER_BUTTON = "Фильтр"
EXPORT_BUTTON = "Экспорт"
PRINT_BUTTON = "Печать"
REFRESH_BUTTON = "Обновить"

# Messages
SUCCESS_MESSAGE = "Операция выполнена успешно"
ERROR_MESSAGE = "Произошла ошибка"
CONFIRM_DELETE = "Вы уверены, что хотите удалить этот элемент?"
CONFIRM_SAVE = "Вы уверены, что хотите сохранить изменения?"
DATA_SAVED = "Данные сохранены успешно"
DATA_LOADED = "Данные загружены успешно"
INVALID_INPUT = "Неверный ввод данных"
REQUIRED_FIELD = "Обязательное поле"

# File Operations
FILE_NOT_FOUND = "Файл не найден"
FILE_LOADED = "Файл загружен"
FILE_SAVED = "Файл сохранён"
INVALID_FILE_FORMAT = "Неверный формат файла"
SELECT_FILE = "Выберите файл"

# Date and Time
TODAY = "Сегодня"
YESTERDAY = "Вчера"
THIS_WEEK = "На этой неделе"
THIS_MONTH = "В этом месяце"
THIS_YEAR = "В этом году"
LAST_WEEK = "На прошлой неделе"
LAST_MONTH = "В прошлом месяце"
LAST_YEAR = "В прошлом году"

# Currency and Numbers
CURRENCY_SYMBOL = "руб"
THOUSAND_SEPARATOR = " "
DECIMAL_SEPARATOR = ","

# Currency formatting function
def format_currency(amount):
    """Форматировать сумму в российском формате: 25 952,59 руб"""
    try:
        # Преобразовать в float если это строка
        if isinstance(amount, str):
            amount = float(amount.replace(',', '.').replace(' ', ''))

        # Округлить до 2 знаков после запятой
        amount = round(float(amount), 2)

        # Разделить на целую и дробную части
        integer_part = int(amount)
        decimal_part = amount - integer_part

        # Форматировать целую часть с пробелами как разделителями тысяч
        integer_str = f"{integer_part:,}".replace(',', ' ')

        # Форматировать дробную часть
        decimal_str = f"{decimal_part:.2f}"[2:]  # Убрать "0."

        # Собрать итоговую строку
        if decimal_part > 0:
            return f"{integer_str},{decimal_str} руб"
        else:
            return f"{integer_str},00 руб"

    except (ValueError, TypeError):
        return "0,00 руб"

# Status
STATUS_ACTIVE = "Активный"
STATUS_INACTIVE = "Неактивный"
STATUS_PENDING = "Ожидает"
STATUS_COMPLETED = "Завершён"
STATUS_CANCELLED = "Отменён"
STATUS_IN_PROGRESS = "В процессе"

# Units
UNIT_PIECES = "шт"
UNIT_KILOGRAMS = "кг"
UNIT_GRAMS = "г"
UNIT_LITERS = "л"
UNIT_MILLILITERS = "мл"
UNIT_HOURS = "ч"
UNIT_MINUTES = "мин"

# Payment Methods
PAYMENT_CASH = "Наличные"
PAYMENT_CARD = "Карта"
PAYMENT_TRANSFER = "Перевод"
PAYMENT_CHECK = "Чек"

# Departments
DEPT_KITCHEN = "Кухня"
DEPT_BAR = "Бар"
DEPT_SERVICE = "Обслуживание"
DEPT_MANAGEMENT = "Управление"
