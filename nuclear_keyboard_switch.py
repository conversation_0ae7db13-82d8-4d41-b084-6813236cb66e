#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import ctypes
import subprocess
import winreg
import time
import os
import sys
import threading

def safe_print(text):
    """Безопасный вывод с обработкой Unicode ошибок"""
    try:
        print(text)
    except UnicodeEncodeError:
        # Удаляем все Unicode символы и заменяем на ASCII
        ascii_text = text.encode('ascii', 'ignore').decode('ascii')
        print(ascii_text)

def is_admin():
    """Проверить права администратора"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """Запустить с правами администратора"""
    if is_admin():
        return True
    else:
        safe_print("🔐 Требуются права администратора...")
        try:
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, " ".join(sys.argv), None, 1
            )
            return False
        except:
            return False

def nuclear_keyboard_switch():
    """ЯДЕРНЫЙ метод переключения раскладки - самый радикальный подход"""
    safe_print("☢️ ЯДЕРНЫЙ МЕТОД ПЕРЕКЛЮЧЕНИЯ РАСКЛАДКИ КЛАВИАТУРЫ")
    print("=" * 80)
    
    if not is_admin():
        safe_print("❌ Этот метод требует прав администратора!")
        if not run_as_admin():
            safe_print("💡 Запустите PowerShell от имени администратора и выполните:")
            print("   python nuclear_keyboard_switch.py")
            return False
        return False

    safe_print("✅ Запущено с правами администратора")
    
    success_methods = []
    
    # МЕТОД 1: Полное изменение системного реестра
    try:
        safe_print("🔧 Метод 1: Полное изменение системного реестра...")
        
        # 1.1 HKEY_CURRENT_USER
        try:
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Keyboard Layout\Preload", 0, winreg.KEY_ALL_ACCESS)
            winreg.SetValueEx(key, "1", 0, winreg.REG_SZ, "00000419")  # Русская первая
            winreg.SetValueEx(key, "2", 0, winreg.REG_SZ, "00000409")  # Английская вторая
            winreg.CloseKey(key)
            safe_print("   ✅ HKEY_CURRENT_USER обновлен")
        except Exception as e:
            safe_print("   ❌ HKEY_CURRENT_USER ошибка: {e}")
        
        # 1.2 HKEY_USERS\.DEFAULT
        try:
            key = winreg.OpenKey(winreg.HKEY_USERS, r".DEFAULT\Keyboard Layout\Preload", 0, winreg.KEY_ALL_ACCESS)
            winreg.SetValueEx(key, "1", 0, winreg.REG_SZ, "00000419")
            winreg.SetValueEx(key, "2", 0, winreg.REG_SZ, "00000409")
            winreg.CloseKey(key)
            safe_print("   ✅ HKEY_USERS\\.DEFAULT обновлен")
        except Exception as e:
            safe_print("   ❌ HKEY_USERS\\.DEFAULT ошибка: {e}")
        
        # 1.3 HKEY_LOCAL_MACHINE (системные настройки)
        try:
            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Control\Keyboard Layout\DosKeybCodes", 0, winreg.KEY_ALL_ACCESS)
            winreg.SetValueEx(key, "00000419", 0, winreg.REG_SZ, "ru")
            winreg.CloseKey(key)
            safe_print("   ✅ HKEY_LOCAL_MACHINE обновлен")
        except Exception as e:
            safe_print("   ❌ HKEY_LOCAL_MACHINE ошибка: {e}")
        
        success_methods.append("Полное изменение реестра")
        
    except Exception as e:
        safe_print("   ❌ Полное изменение реестра общая ошибка: {e}")
    
    # МЕТОД 2: Принудительная остановка и перезапуск explorer.exe
    try:
        safe_print("🔧 Метод 2: Перезапуск explorer.exe...")
        
        # Останавливаем explorer.exe
        subprocess.run("taskkill /f /im explorer.exe", shell=True, capture_output=True)
        safe_print("   ✅ explorer.exe остановлен")
        
        time.sleep(2)
        
        # Запускаем explorer.exe заново
        subprocess.Popen("explorer.exe", shell=True)
        safe_print("   ✅ explorer.exe перезапущен")
        
        success_methods.append("Перезапуск explorer.exe")
        
    except Exception as e:
        safe_print("   ❌ Перезапуск explorer.exe ошибка: {e}")
    
    # МЕТОД 3: Принудительная перезагрузка службы ввода
    try:
        safe_print("🔧 Метод 3: Перезагрузка службы ввода...")
        
        services = [
            "TabletInputService",
            "WSearch",
            "Themes"
        ]
        
        for service in services:
            try:
                subprocess.run(f"net stop {service}", shell=True, capture_output=True, timeout=10)
                time.sleep(1)
                subprocess.run(f"net start {service}", shell=True, capture_output=True, timeout=10)
                safe_print("   ✅ Служба {service} перезапущена")
            except:
                safe_print("   ⚠️ Служба {service} не перезапущена")
        
        success_methods.append("Перезагрузка служб")
        
    except Exception as e:
        safe_print("   ❌ Перезагрузка служб ошибка: {e}")
    
    # МЕТОД 4: Использование rundll32 с системными правами
    try:
        safe_print("🔧 Метод 4: rundll32 с системными правами...")
        
        commands = [
            "rundll32.exe user32.dll,LoadKeyboardLayoutW 00000419 1",
            "rundll32.exe user32.dll,ActivateKeyboardLayout 67699721 8",
            "rundll32.exe shell32.dll,Control_RunDLL input.dll,,{C07337D3-DB2C-4D0B-9A93-B722A6C106E2}",
            "rundll32.exe user32.dll,SystemParametersInfoW 90 0 00000419 2"
        ]
        
        for cmd in commands:
            try:
                result = subprocess.run(cmd, shell=True, capture_output=True, timeout=10)
                safe_print("   ✅ Команда выполнена: {cmd[:50]}...")
            except:
                safe_print("   ❌ Команда не удалась: {cmd[:50]}...")
        
        success_methods.append("rundll32 с системными правами")
        
    except Exception as e:
        safe_print("   ❌ rundll32 с системными правами ошибка: {e}")
    
    # МЕТОД 5: Изменение через WMI (Windows Management Instrumentation)
    try:
        safe_print("🔧 Метод 5: WMI изменение...")
        
        import wmi
        c = wmi.WMI()
        
        # Получаем информацию о системе
        for system in c.Win32_ComputerSystem():
            safe_print("   📋 Система: {system.Name}")
        
        # Пытаемся изменить региональные настройки через WMI
        wmi_script = '''
        $computer = Get-WmiObject -Class Win32_ComputerSystem
        $computer.SetPowerState(0)
        '''
        
        subprocess.run(["powershell", "-Command", wmi_script], capture_output=True)
        
        success_methods.append("WMI изменение")
        
    except Exception as e:
        safe_print("   ❌ WMI изменение ошибка: {e}")
    
    # МЕТОД 6: Прямое изменение памяти процесса winlogon.exe
    try:
        safe_print("🔧 Метод 6: Изменение памяти winlogon.exe...")
        
        # Получаем PID процесса winlogon
        result = subprocess.run("tasklist /fi \"imagename eq winlogon.exe\"", 
                              shell=True, capture_output=True, text=True)
        
        if "winlogon.exe" in result.stdout:
            safe_print("   ✅ Процесс winlogon.exe найден")
            
            # Пытаемся получить доступ к процессу
            kernel32 = ctypes.windll.kernel32
            PROCESS_ALL_ACCESS = 0x1F0FFF
            
            # Это очень опасная операция, поэтому делаем только проверку
            safe_print("   ⚠️ Доступ к winlogon.exe ограничен системой безопасности")
            
        success_methods.append("Анализ winlogon.exe")
        
    except Exception as e:
        safe_print("   ❌ Изменение памяти winlogon.exe ошибка: {e}")
    
    # МЕТОД 7: Создание задачи планировщика для переключения
    try:
        safe_print("🔧 Метод 7: Задача планировщика...")
        
        task_xml = '''<?xml version="1.0" encoding="UTF-16"?>
<Task version="1.2">
  <Triggers>
    <LogonTrigger>
      <Enabled>true</Enabled>
    </LogonTrigger>
  </Triggers>
  <Actions>
    <Exec>
      <Command>rundll32.exe</Command>
      <Arguments>user32.dll,ActivateKeyboardLayout 67699721 8</Arguments>
    </Exec>
  </Actions>
</Task>'''
        
        # Сохраняем XML задачи
        with open("keyboard_switch_task.xml", "w", encoding="utf-16") as f:
            f.write(task_xml)
        
        # Создаем задачу
        result = subprocess.run([
            "schtasks", "/create", "/tn", "KeyboardSwitchTask", 
            "/xml", "keyboard_switch_task.xml", "/f"
        ], capture_output=True)
        
        if result.returncode == 0:
            safe_print("   ✅ Задача планировщика создана")
            
            # Запускаем задачу немедленно
            subprocess.run(["schtasks", "/run", "/tn", "KeyboardSwitchTask"], capture_output=True)
            safe_print("   ✅ Задача запущена")
        else:
            safe_print("   ❌ Не удалось создать задачу")
        
        # Удаляем временный файл
        try:
            os.remove("keyboard_switch_task.xml")
        except:
            pass
        
        success_methods.append("Задача планировщика")
        
    except Exception as e:
        safe_print("   ❌ Задача планировщика ошибка: {e}")
    
    # Ждем применения изменений
    safe_print("\n⏳ Ожидание применения изменений...")
    time.sleep(5)
    
    # Проверяем результат
    try:
        user32 = ctypes.windll.user32
        hwnd = user32.GetForegroundWindow()
        thread_id = user32.GetWindowThreadProcessId(hwnd, None)
        current_hkl = user32.GetKeyboardLayout(thread_id)
        current_id = current_hkl & 0xFFFF
        
        print("\n" + "=" * 80)
        safe_print("📋 РЕЗУЛЬТАТ: Текущая раскладка {hex(current_hkl)} (ID: {hex(current_id)})")
        
        if current_id == 0x0419:
            safe_print("🎉 УСПЕХ! ЯДЕРНЫЙ МЕТОД СРАБОТАЛ!")
            safe_print("✅ Раскладка переключена на русскую!")
            safe_print("🔧 Успешные методы: {', '.join(success_methods)}")
            return True
        else:
            safe_print("💥 ДАЖЕ ЯДЕРНЫЙ МЕТОД НЕ СРАБОТАЛ!")
            print("Это означает, что Windows имеет АБСОЛЮТНУЮ защиту от программного переключения раскладки")
            safe_print("🔧 Попробованные методы: {', '.join(success_methods)}")
            return False
            
    except Exception as e:
        safe_print("❌ Ошибка при проверке результата: {e}")
        return False

if __name__ == "__main__":
    safe_print("☢️ ЯДЕРНЫЙ МЕТОД ПЕРЕКЛЮЧЕНИЯ РАСКЛАДКИ КЛАВИАТУРЫ")
    print("ВНИМАНИЕ: Этот метод использует самые радикальные подходы!")
    print("Включает изменение системного реестра, перезапуск explorer.exe,")
    print("остановку системных служб и другие критические операции.")
    print()
    
    if not is_admin():
        safe_print("❌ Требуются права администратора!")
        print("Запустите PowerShell от имени администратора и выполните:")
        print("   python nuclear_keyboard_switch.py")
        input("\nНажмите Enter для выхода...")
        sys.exit(1)
    
    choice = input("Вы уверены, что хотите запустить ЯДЕРНЫЙ метод? (yes/no): ").lower().strip()
    
    if choice in ['yes', 'да']:
        print("\n🚨 ЗАПУСК ЯДЕРНОГО МЕТОДА...")
        success = nuclear_keyboard_switch()
        
        if success:
            safe_print("\n🎉 ЯДЕРНЫЙ МЕТОД УСПЕШЕН!")
            print("Раскладка клавиатуры переключена на русскую!")
        else:
            safe_print("\n💀 ЯДЕРНЫЙ МЕТОД НЕ СРАБОТАЛ")
            print("Windows имеет абсолютную защиту от программного переключения раскладки.")
            print("Единственное решение - ручное переключение Alt+Shift.")
    else:
        print("Операция отменена.")
    
    input("\nНажмите Enter для выхода...")
