"""
Enhanced Reporting and Business Intelligence System
Advanced analytics, trend analysis, predictive insights, custom report builder, and automated scheduling
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import json
import csv
from datetime import datetime, timedelta
from gui.styles import ModernStyles
from utils.window_utils import create_standard_window, apply_standard_styling
from utils.error_handling import handle_module_error, log_info

class EnhancedReportingSystem:
    """Enhanced reporting system with advanced analytics and automation"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        
        # Report categories and types
        self.report_categories = {
            "📈 Продажи и Выручка": {
                "sales_dynamics": "Динамика продаж",
                "top_products": "Топ продукты",
                "sales_forecast": "Прогноз продаж",
                "seasonal_analysis": "Сезонный анализ",
                "customer_segments": "Сегменты клиентов"
            },
            "💰 Финансовая Аналитика": {
                "profitability": "Анализ прибыльности",
                "cost_analysis": "Анализ затрат",
                "roi_analysis": "ROI анализ",
                "budget_variance": "Отклонения от бюджета",
                "cash_flow": "Денежные потоки"
            },
            "📦 Складская Аналитика": {
                "inventory_turnover": "Оборачиваемость запасов",
                "abc_analysis": "ABC анализ",
                "demand_forecast": "Прогноз спроса",
                "supplier_performance": "Эффективность поставщиков",
                "waste_analysis": "Анализ потерь"
            },
            "👥 HR Аналитика": {
                "staff_performance": "Производительность персонала",
                "labor_costs": "Затраты на персонал",
                "schedule_optimization": "Оптимизация расписания",
                "training_effectiveness": "Эффективность обучения",
                "turnover_analysis": "Анализ текучести"
            },
            "🎯 Операционная Аналитика": {
                "kitchen_efficiency": "Эффективность кухни",
                "service_quality": "Качество обслуживания",
                "table_turnover": "Оборачиваемость столов",
                "wait_time_analysis": "Анализ времени ожидания",
                "peak_hours": "Часы пик"
            }
        }
        
        # Automated schedules
        self.automated_schedules = []
        
        # Custom report templates
        self.custom_templates = []
        
        # Predictive models
        self.predictive_models = {
            "sales_forecast": "Прогноз продаж",
            "demand_prediction": "Прогноз спроса",
            "customer_churn": "Отток клиентов",
            "inventory_optimization": "Оптимизация запасов",
            "revenue_forecast": "Прогноз выручки"
        }
        
        self.create_reporting_system()
    
    def create_reporting_system(self):
        """Create the enhanced reporting system window"""
        try:
            self.window = create_standard_window(
                self.parent,
                "📊 Расширенная Система Отчетности и Аналитики",
                "1600x1000"
            )
            
            # Apply standard styling
            main_container = apply_standard_styling(
                self.window, 
                "Расширенная Система Отчетности и Аналитики", 
                "📊"
            )
            
            # Create header with quick stats
            self.create_header(main_container)
            
            # Create main notebook
            self.create_main_notebook(main_container)
            
            # Create control panel
            self.create_control_panel(main_container)
            
            log_info("Расширенная система отчетности создана", "EnhancedReportingSystem")
            
        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "создание системы")
    
    def create_header(self, parent):
        """Create header with quick statistics"""
        try:
            header_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
            header_frame.pack(fill='x', pady=(0, 20))
            
            # Quick stats
            stats_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['bg_main'])
            stats_frame.pack(fill='x')
            
            # Stats cards
            stats = [
                ("📊 Всего отчетов", "247", ModernStyles.COLORS['primary']),
                ("🤖 Автоматических", "18", ModernStyles.COLORS['info']),
                ("📈 За сегодня", "12", ModernStyles.COLORS['success']),
                ("⏰ Запланировано", "5", ModernStyles.COLORS['warning'])
            ]
            
            for title, value, color in stats:
                self.create_stat_card(stats_frame, title, value, color)
            
        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "создание заголовка")
    
    def create_stat_card(self, parent, title, value, color):
        """Create a statistics card"""
        try:
            card_frame = tk.Frame(parent, bg=color, relief='flat', bd=1)
            card_frame.pack(side='left', fill='both', expand=True, padx=5, pady=5)
            
            # Value
            value_label = tk.Label(card_frame, text=value,
                                 font=('Cambria', 24, 'bold'),
                                 fg='white', bg=color)
            value_label.pack(pady=(15, 5))
            
            # Title
            title_label = tk.Label(card_frame, text=title,
                                 font=('Cambria', 12, 'bold'),
                                 fg='white', bg=color)
            title_label.pack(pady=(0, 15))
            
        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "создание карточки статистики")
    
    def create_main_notebook(self, parent):
        """Create main notebook with tabs"""
        try:
            self.notebook = ttk.Notebook(parent)
            self.notebook.pack(fill='both', expand=True, pady=(0, 20))
            
            # Create tabs
            self.create_reports_tab()
            self.create_analytics_tab()
            self.create_predictive_tab()
            self.create_custom_builder_tab()
            self.create_automation_tab()
            self.create_templates_tab()
            
        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "создание основного блокнота")
    
    def create_reports_tab(self):
        """Create reports tab"""
        try:
            reports_frame = tk.Frame(self.notebook, bg=ModernStyles.COLORS['bg_main'])
            self.notebook.add(reports_frame, text="📊 Отчеты")
            
            # Create scrollable frame
            canvas = tk.Canvas(reports_frame, bg=ModernStyles.COLORS['bg_main'])
            scrollbar = ttk.Scrollbar(reports_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg=ModernStyles.COLORS['bg_main'])
            
            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )
            
            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)
            
            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")
            
            # Create report categories
            for category, reports in self.report_categories.items():
                self.create_report_category(scrollable_frame, category, reports)
            
            # Enable mouse wheel scrolling
            def _on_mousewheel(event):
                canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            canvas.bind_all("<MouseWheel>", _on_mousewheel)
            
        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "создание вкладки отчетов")
    
    def create_report_category(self, parent, category_name, reports):
        """Create a report category section"""
        try:
            # Category header
            category_frame = tk.LabelFrame(parent, text=category_name,
                                         font=('Cambria', 14, 'bold'),
                                         fg=ModernStyles.COLORS['primary'],
                                         bg=ModernStyles.COLORS['bg_secondary'])
            category_frame.pack(fill='x', padx=10, pady=10)
            
            # Reports grid
            reports_grid = tk.Frame(category_frame, bg=ModernStyles.COLORS['bg_secondary'])
            reports_grid.pack(fill='x', padx=10, pady=10)
            
            # Create report buttons in grid
            row = 0
            col = 0
            for report_id, report_name in reports.items():
                btn = tk.Button(reports_grid, text=report_name,
                              command=lambda r_id=report_id, r_name=report_name: self.generate_report(r_id, r_name),
                              bg=ModernStyles.COLORS['primary'], fg='white',
                              font=('Cambria', 11, 'bold'), relief='flat',
                              padx=20, pady=10, width=25)
                btn.grid(row=row, column=col, padx=5, pady=5, sticky='ew')
                
                col += 1
                if col >= 3:  # 3 columns per row
                    col = 0
                    row += 1
            
            # Configure grid weights
            for i in range(3):
                reports_grid.grid_columnconfigure(i, weight=1)
            
        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "создание категории отчетов")
    
    def create_analytics_tab(self):
        """Create advanced analytics tab"""
        try:
            analytics_frame = tk.Frame(self.notebook, bg=ModernStyles.COLORS['bg_main'])
            self.notebook.add(analytics_frame, text="📈 Аналитика")
            
            # Analytics header
            header_label = tk.Label(analytics_frame, text="📈 Расширенная Аналитика",
                                  font=('Cambria', 20, 'bold'),
                                  fg=ModernStyles.COLORS['primary'],
                                  bg=ModernStyles.COLORS['bg_main'])
            header_label.pack(pady=20)
            
            # Analytics options
            options_frame = tk.Frame(analytics_frame, bg=ModernStyles.COLORS['bg_main'])
            options_frame.pack(fill='both', expand=True, padx=20, pady=10)
            
            # Trend analysis section
            trend_frame = tk.LabelFrame(options_frame, text="📊 Анализ Трендов",
                                      font=('Cambria', 14, 'bold'),
                                      fg=ModernStyles.COLORS['info'],
                                      bg=ModernStyles.COLORS['bg_secondary'])
            trend_frame.pack(fill='x', pady=10)
            
            trend_buttons = [
                ("📈 Тренды продаж", self.show_sales_trends),
                ("💰 Финансовые тренды", self.show_financial_trends),
                ("📦 Тренды запасов", self.show_inventory_trends),
                ("👥 HR тренды", self.show_hr_trends)
            ]
            
            for text, command in trend_buttons:
                btn = tk.Button(trend_frame, text=text, command=command,
                              bg=ModernStyles.COLORS['info'], fg='white',
                              font=('Cambria', 12, 'bold'), relief='flat',
                              padx=30, pady=10)
                btn.pack(side='left', padx=10, pady=10)
            
            # Comparative analysis section
            compare_frame = tk.LabelFrame(options_frame, text="🔄 Сравнительный Анализ",
                                        font=('Cambria', 14, 'bold'),
                                        fg=ModernStyles.COLORS['secondary'],
                                        bg=ModernStyles.COLORS['bg_secondary'])
            compare_frame.pack(fill='x', pady=10)
            
            compare_buttons = [
                ("📅 Сравнение периодов", self.show_period_comparison),
                ("🏢 Сравнение локаций", self.show_location_comparison),
                ("📊 Сравнение продуктов", self.show_product_comparison),
                ("👥 Сравнение персонала", self.show_staff_comparison)
            ]
            
            for text, command in compare_buttons:
                btn = tk.Button(compare_frame, text=text, command=command,
                              bg=ModernStyles.COLORS['secondary'], fg='white',
                              font=('Cambria', 12, 'bold'), relief='flat',
                              padx=30, pady=10)
                btn.pack(side='left', padx=10, pady=10)
            
        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "создание вкладки аналитики")

    def create_predictive_tab(self):
        """Create predictive analytics tab"""
        try:
            predictive_frame = tk.Frame(self.notebook, bg=ModernStyles.COLORS['bg_main'])
            self.notebook.add(predictive_frame, text="🔮 Прогнозы")

            # Predictive header
            header_label = tk.Label(predictive_frame, text="🔮 Прогнозная Аналитика",
                                  font=('Cambria', 20, 'bold'),
                                  fg=ModernStyles.COLORS['primary'],
                                  bg=ModernStyles.COLORS['bg_main'])
            header_label.pack(pady=20)

            # Models grid
            models_frame = tk.Frame(predictive_frame, bg=ModernStyles.COLORS['bg_main'])
            models_frame.pack(fill='both', expand=True, padx=20, pady=10)

            # Create model cards
            row = 0
            col = 0
            for model_id, model_name in self.predictive_models.items():
                model_card = self.create_predictive_model_card(models_frame, model_id, model_name)
                model_card.grid(row=row, column=col, padx=10, pady=10, sticky='nsew')

                col += 1
                if col >= 2:  # 2 columns
                    col = 0
                    row += 1

            # Configure grid weights
            for i in range(2):
                models_frame.grid_columnconfigure(i, weight=1)
            for i in range(row + 1):
                models_frame.grid_rowconfigure(i, weight=1)

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "создание вкладки прогнозов")

    def create_predictive_model_card(self, parent, model_id, model_name):
        """Create a predictive model card"""
        try:
            card_frame = tk.LabelFrame(parent, text=model_name,
                                     font=('Cambria', 14, 'bold'),
                                     fg=ModernStyles.COLORS['primary'],
                                     bg=ModernStyles.COLORS['bg_secondary'])

            # Model info
            info_label = tk.Label(card_frame, text=f"Модель: {model_name}",
                                font=('Cambria', 12),
                                fg=ModernStyles.COLORS['text_primary'],
                                bg=ModernStyles.COLORS['bg_secondary'])
            info_label.pack(pady=10)

            # Accuracy indicator
            accuracy = 85 + (hash(model_id) % 15)  # Simulated accuracy
            accuracy_label = tk.Label(card_frame, text=f"Точность: {accuracy}%",
                                    font=('Cambria', 11),
                                    fg=ModernStyles.COLORS['success'],
                                    bg=ModernStyles.COLORS['bg_secondary'])
            accuracy_label.pack(pady=5)

            # Action buttons
            btn_frame = tk.Frame(card_frame, bg=ModernStyles.COLORS['bg_secondary'])
            btn_frame.pack(pady=10)

            run_btn = tk.Button(btn_frame, text="▶️ Запустить",
                              command=lambda: self.run_predictive_model(model_id, model_name),
                              bg=ModernStyles.COLORS['success'], fg='white',
                              font=('Cambria', 10, 'bold'), relief='flat',
                              padx=15, pady=5)
            run_btn.pack(side='left', padx=5)

            config_btn = tk.Button(btn_frame, text="⚙️ Настроить",
                                 command=lambda: self.configure_model(model_id, model_name),
                                 bg=ModernStyles.COLORS['secondary'], fg='white',
                                 font=('Cambria', 10, 'bold'), relief='flat',
                                 padx=15, pady=5)
            config_btn.pack(side='left', padx=5)

            return card_frame

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "создание карточки модели")
            return tk.Frame(parent)

    def create_custom_builder_tab(self):
        """Create custom report builder tab"""
        try:
            builder_frame = tk.Frame(self.notebook, bg=ModernStyles.COLORS['bg_main'])
            self.notebook.add(builder_frame, text="🔧 Конструктор")

            # Builder header
            header_label = tk.Label(builder_frame, text="🔧 Конструктор Отчетов",
                                  font=('Cambria', 20, 'bold'),
                                  fg=ModernStyles.COLORS['primary'],
                                  bg=ModernStyles.COLORS['bg_main'])
            header_label.pack(pady=20)

            # Main builder area
            builder_container = tk.Frame(builder_frame, bg=ModernStyles.COLORS['bg_main'])
            builder_container.pack(fill='both', expand=True, padx=20, pady=10)

            # Left panel - Data sources
            left_panel = tk.LabelFrame(builder_container, text="📊 Источники Данных",
                                     font=('Cambria', 14, 'bold'),
                                     fg=ModernStyles.COLORS['info'],
                                     bg=ModernStyles.COLORS['bg_secondary'])
            left_panel.pack(side='left', fill='y', padx=(0, 10))

            # Data sources list
            data_sources = [
                "💰 Продажи",
                "📦 Склад",
                "👥 Персонал",
                "🍽️ Меню",
                "🏢 Локации",
                "💳 Платежи",
                "📞 Заказы",
                "⭐ Отзывы"
            ]

            for source in data_sources:
                source_btn = tk.Button(left_panel, text=source,
                                     command=lambda s=source: self.add_data_source(s),
                                     bg=ModernStyles.COLORS['info'], fg='white',
                                     font=('Cambria', 11, 'bold'), relief='flat',
                                     padx=20, pady=8, width=15)
                source_btn.pack(pady=5, padx=10)

            # Center panel - Report builder
            center_panel = tk.LabelFrame(builder_container, text="🔧 Конструктор",
                                       font=('Cambria', 14, 'bold'),
                                       fg=ModernStyles.COLORS['primary'],
                                       bg=ModernStyles.COLORS['bg_secondary'])
            center_panel.pack(side='left', fill='both', expand=True, padx=10)

            # Report name
            name_frame = tk.Frame(center_panel, bg=ModernStyles.COLORS['bg_secondary'])
            name_frame.pack(fill='x', padx=10, pady=10)

            tk.Label(name_frame, text="Название отчета:",
                   font=('Cambria', 12, 'bold'),
                   fg=ModernStyles.COLORS['text_primary'],
                   bg=ModernStyles.COLORS['bg_secondary']).pack(anchor='w')

            self.report_name_var = tk.StringVar()
            name_entry = tk.Entry(name_frame, textvariable=self.report_name_var,
                                font=('Cambria', 12), width=40)
            name_entry.pack(fill='x', pady=5)

            # Selected data sources
            sources_frame = tk.Frame(center_panel, bg=ModernStyles.COLORS['bg_secondary'])
            sources_frame.pack(fill='x', padx=10, pady=10)

            tk.Label(sources_frame, text="Выбранные источники:",
                   font=('Cambria', 12, 'bold'),
                   fg=ModernStyles.COLORS['text_primary'],
                   bg=ModernStyles.COLORS['bg_secondary']).pack(anchor='w')

            self.selected_sources_listbox = tk.Listbox(sources_frame, height=6,
                                                     font=('Cambria', 11))
            self.selected_sources_listbox.pack(fill='x', pady=5)

            # Filters
            filters_frame = tk.Frame(center_panel, bg=ModernStyles.COLORS['bg_secondary'])
            filters_frame.pack(fill='x', padx=10, pady=10)

            tk.Label(filters_frame, text="Фильтры:",
                   font=('Cambria', 12, 'bold'),
                   fg=ModernStyles.COLORS['text_primary'],
                   bg=ModernStyles.COLORS['bg_secondary']).pack(anchor='w')

            filter_btn_frame = tk.Frame(filters_frame, bg=ModernStyles.COLORS['bg_secondary'])
            filter_btn_frame.pack(fill='x', pady=5)

            filter_buttons = [
                ("📅 Период", self.add_date_filter),
                ("🏢 Локация", self.add_location_filter),
                ("👤 Пользователь", self.add_user_filter),
                ("💰 Сумма", self.add_amount_filter)
            ]

            for text, command in filter_buttons:
                btn = tk.Button(filter_btn_frame, text=text, command=command,
                              bg=ModernStyles.COLORS['warning'], fg='white',
                              font=('Cambria', 10, 'bold'), relief='flat',
                              padx=15, pady=5)
                btn.pack(side='left', padx=5)

            # Right panel - Actions
            right_panel = tk.LabelFrame(builder_container, text="⚡ Действия",
                                      font=('Cambria', 14, 'bold'),
                                      fg=ModernStyles.COLORS['success'],
                                      bg=ModernStyles.COLORS['bg_secondary'])
            right_panel.pack(side='right', fill='y', padx=(10, 0))

            # Action buttons
            actions = [
                ("👁️ Предпросмотр", self.preview_custom_report),
                ("💾 Сохранить", self.save_custom_report),
                ("📊 Создать", self.generate_custom_report),
                ("🗑️ Очистить", self.clear_builder),
                ("📤 Экспорт", self.export_custom_report)
            ]

            for text, command in actions:
                action_btn = tk.Button(right_panel, text=text, command=command,
                                     bg=ModernStyles.COLORS['success'], fg='white',
                                     font=('Cambria', 11, 'bold'), relief='flat',
                                     padx=20, pady=10, width=15)
                action_btn.pack(pady=10, padx=10)

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "создание вкладки конструктора")

    def create_automation_tab(self):
        """Create automation and scheduling tab"""
        try:
            automation_frame = tk.Frame(self.notebook, bg=ModernStyles.COLORS['bg_main'])
            self.notebook.add(automation_frame, text="🤖 Автоматизация")

            # Automation header
            header_label = tk.Label(automation_frame, text="🤖 Автоматизация Отчетов",
                                  font=('Cambria', 20, 'bold'),
                                  fg=ModernStyles.COLORS['primary'],
                                  bg=ModernStyles.COLORS['bg_main'])
            header_label.pack(pady=20)

            # Main automation container
            automation_container = tk.Frame(automation_frame, bg=ModernStyles.COLORS['bg_main'])
            automation_container.pack(fill='both', expand=True, padx=20, pady=10)

            # Left panel - Schedule creation
            left_panel = tk.LabelFrame(automation_container, text="📅 Создание Расписания",
                                     font=('Cambria', 14, 'bold'),
                                     fg=ModernStyles.COLORS['info'],
                                     bg=ModernStyles.COLORS['bg_secondary'])
            left_panel.pack(side='left', fill='both', expand=True, padx=(0, 10))

            # Schedule form
            form_frame = tk.Frame(left_panel, bg=ModernStyles.COLORS['bg_secondary'])
            form_frame.pack(fill='both', expand=True, padx=10, pady=10)

            # Report selection
            tk.Label(form_frame, text="Тип отчета:",
                   font=('Cambria', 12, 'bold'),
                   fg=ModernStyles.COLORS['text_primary'],
                   bg=ModernStyles.COLORS['bg_secondary']).pack(anchor='w', pady=(0, 5))

            self.schedule_report_var = tk.StringVar()
            report_combo = ttk.Combobox(form_frame, textvariable=self.schedule_report_var,
                                      values=list(self.get_all_reports()), state='readonly',
                                      font=('Cambria', 11))
            report_combo.pack(fill='x', pady=(0, 10))

            # Frequency selection
            tk.Label(form_frame, text="Частота:",
                   font=('Cambria', 12, 'bold'),
                   fg=ModernStyles.COLORS['text_primary'],
                   bg=ModernStyles.COLORS['bg_secondary']).pack(anchor='w', pady=(0, 5))

            self.frequency_var = tk.StringVar(value="daily")
            frequency_frame = tk.Frame(form_frame, bg=ModernStyles.COLORS['bg_secondary'])
            frequency_frame.pack(fill='x', pady=(0, 10))

            frequencies = [
                ("Ежедневно", "daily"),
                ("Еженедельно", "weekly"),
                ("Ежемесячно", "monthly"),
                ("Ежеквартально", "quarterly")
            ]

            for text, value in frequencies:
                rb = tk.Radiobutton(frequency_frame, text=text, variable=self.frequency_var,
                                  value=value, font=('Cambria', 11),
                                  fg=ModernStyles.COLORS['text_primary'],
                                  bg=ModernStyles.COLORS['bg_secondary'])
                rb.pack(anchor='w')

            # Time selection
            tk.Label(form_frame, text="Время отправки:",
                   font=('Cambria', 12, 'bold'),
                   fg=ModernStyles.COLORS['text_primary'],
                   bg=ModernStyles.COLORS['bg_secondary']).pack(anchor='w', pady=(10, 5))

            time_frame = tk.Frame(form_frame, bg=ModernStyles.COLORS['bg_secondary'])
            time_frame.pack(fill='x', pady=(0, 10))

            self.hour_var = tk.StringVar(value="09")
            self.minute_var = tk.StringVar(value="00")

            hour_spin = tk.Spinbox(time_frame, from_=0, to=23, textvariable=self.hour_var,
                                 width=5, font=('Cambria', 11), format="%02.0f")
            hour_spin.pack(side='left')

            tk.Label(time_frame, text=":", font=('Cambria', 12, 'bold'),
                   fg=ModernStyles.COLORS['text_primary'],
                   bg=ModernStyles.COLORS['bg_secondary']).pack(side='left', padx=5)

            minute_spin = tk.Spinbox(time_frame, from_=0, to=59, textvariable=self.minute_var,
                                   width=5, font=('Cambria', 11), format="%02.0f")
            minute_spin.pack(side='left')

            # Recipients
            tk.Label(form_frame, text="Получатели (email):",
                   font=('Cambria', 12, 'bold'),
                   fg=ModernStyles.COLORS['text_primary'],
                   bg=ModernStyles.COLORS['bg_secondary']).pack(anchor='w', pady=(10, 5))

            self.recipients_text = tk.Text(form_frame, height=4, font=('Cambria', 11))
            self.recipients_text.pack(fill='x', pady=(0, 10))

            # Create schedule button
            create_btn = tk.Button(form_frame, text="📅 Создать Расписание",
                                 command=self.create_schedule,
                                 bg=ModernStyles.COLORS['success'], fg='white',
                                 font=('Cambria', 12, 'bold'), relief='flat',
                                 padx=30, pady=10)
            create_btn.pack(pady=10)

            # Right panel - Existing schedules
            right_panel = tk.LabelFrame(automation_container, text="📋 Активные Расписания",
                                      font=('Cambria', 14, 'bold'),
                                      fg=ModernStyles.COLORS['primary'],
                                      bg=ModernStyles.COLORS['bg_secondary'])
            right_panel.pack(side='right', fill='both', expand=True, padx=(10, 0))

            # Schedules list
            schedules_frame = tk.Frame(right_panel, bg=ModernStyles.COLORS['bg_secondary'])
            schedules_frame.pack(fill='both', expand=True, padx=10, pady=10)

            # Schedules treeview
            columns = ('Отчет', 'Частота', 'Время', 'Статус')
            self.schedules_tree = ttk.Treeview(schedules_frame, columns=columns, show='headings')

            for col in columns:
                self.schedules_tree.heading(col, text=col)
                self.schedules_tree.column(col, width=120)

            self.schedules_tree.pack(fill='both', expand=True)

            # Schedule control buttons
            schedule_btn_frame = tk.Frame(right_panel, bg=ModernStyles.COLORS['bg_secondary'])
            schedule_btn_frame.pack(fill='x', padx=10, pady=10)

            schedule_buttons = [
                ("▶️ Запустить", self.run_schedule),
                ("⏸️ Приостановить", self.pause_schedule),
                ("🗑️ Удалить", self.delete_schedule),
                ("✏️ Редактировать", self.edit_schedule)
            ]

            for text, command in schedule_buttons:
                btn = tk.Button(schedule_btn_frame, text=text, command=command,
                              bg=ModernStyles.COLORS['secondary'], fg='white',
                              font=('Cambria', 10, 'bold'), relief='flat',
                              padx=15, pady=5)
                btn.pack(side='left', padx=5)

            # Load existing schedules
            self.load_schedules()

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "создание вкладки автоматизации")

    def create_templates_tab(self):
        """Create templates management tab"""
        try:
            templates_frame = tk.Frame(self.notebook, bg=ModernStyles.COLORS['bg_main'])
            self.notebook.add(templates_frame, text="📄 Шаблоны")

            # Templates header
            header_label = tk.Label(templates_frame, text="📄 Управление Шаблонами",
                                  font=('Cambria', 20, 'bold'),
                                  fg=ModernStyles.COLORS['primary'],
                                  bg=ModernStyles.COLORS['bg_main'])
            header_label.pack(pady=20)

            # Templates container
            templates_container = tk.Frame(templates_frame, bg=ModernStyles.COLORS['bg_main'])
            templates_container.pack(fill='both', expand=True, padx=20, pady=10)

            # Templates grid
            templates_grid = tk.Frame(templates_container, bg=ModernStyles.COLORS['bg_main'])
            templates_grid.pack(fill='both', expand=True)

            # Default templates
            default_templates = [
                ("📊 Стандартный Отчет", "Базовый шаблон для большинства отчетов"),
                ("📈 Аналитический", "Шаблон с графиками и диаграммами"),
                ("💰 Финансовый", "Специализированный финансовый шаблон"),
                ("📦 Складской", "Шаблон для складских отчетов"),
                ("👥 HR Отчет", "Шаблон для отчетов по персоналу"),
                ("🎯 Управленческий", "Краткий шаблон для руководства")
            ]

            row = 0
            col = 0
            for template_name, template_desc in default_templates:
                template_card = self.create_template_card(templates_grid, template_name, template_desc)
                template_card.grid(row=row, column=col, padx=10, pady=10, sticky='nsew')

                col += 1
                if col >= 2:  # 2 columns
                    col = 0
                    row += 1

            # Configure grid weights
            for i in range(2):
                templates_grid.grid_columnconfigure(i, weight=1)
            for i in range(row + 1):
                templates_grid.grid_rowconfigure(i, weight=1)

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "создание вкладки шаблонов")

    def create_template_card(self, parent, template_name, template_desc):
        """Create a template card"""
        try:
            card_frame = tk.LabelFrame(parent, text=template_name,
                                     font=('Cambria', 14, 'bold'),
                                     fg=ModernStyles.COLORS['primary'],
                                     bg=ModernStyles.COLORS['bg_secondary'])

            # Description
            desc_label = tk.Label(card_frame, text=template_desc,
                                font=('Cambria', 11),
                                fg=ModernStyles.COLORS['text_primary'],
                                bg=ModernStyles.COLORS['bg_secondary'],
                                wraplength=250)
            desc_label.pack(pady=10, padx=10)

            # Action buttons
            btn_frame = tk.Frame(card_frame, bg=ModernStyles.COLORS['bg_secondary'])
            btn_frame.pack(pady=10)

            use_btn = tk.Button(btn_frame, text="📊 Использовать",
                              command=lambda: self.use_template(template_name),
                              bg=ModernStyles.COLORS['success'], fg='white',
                              font=('Cambria', 10, 'bold'), relief='flat',
                              padx=15, pady=5)
            use_btn.pack(side='left', padx=5)

            edit_btn = tk.Button(btn_frame, text="✏️ Редактировать",
                               command=lambda: self.edit_template(template_name),
                               bg=ModernStyles.COLORS['warning'], fg='white',
                               font=('Cambria', 10, 'bold'), relief='flat',
                               padx=15, pady=5)
            edit_btn.pack(side='left', padx=5)

            return card_frame

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "создание карточки шаблона")
            return tk.Frame(parent)

    def create_control_panel(self, parent):
        """Create control panel with global actions"""
        try:
            control_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
            control_frame.pack(fill='x', pady=10)

            # Control buttons
            buttons = [
                ("🔄 Обновить Данные", self.refresh_all_data, ModernStyles.COLORS['info']),
                ("📤 Экспорт Всех", self.export_all_reports, ModernStyles.COLORS['primary']),
                ("📧 Отправить Отчеты", self.send_reports, ModernStyles.COLORS['success']),
                ("⚙️ Настройки", self.show_settings, ModernStyles.COLORS['secondary']),
                ("❌ Закрыть", self.close_system, ModernStyles.COLORS['danger'])
            ]

            for text, command, color in buttons:
                btn = tk.Button(control_frame, text=text, command=command,
                              bg=color, fg='white',
                              font=('Cambria', 12, 'bold'), relief='flat',
                              padx=25, pady=10)
                btn.pack(side='left', padx=10)

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "создание панели управления")

    # Report generation methods
    def generate_report(self, report_id, report_name):
        """Generate a specific report"""
        try:
            # Create report window
            report_window = tk.Toplevel(self.window)
            report_window.title(f"📊 {report_name}")
            report_window.geometry("1200x800")
            report_window.configure(bg=ModernStyles.COLORS['bg_main'])

            # Report header
            header_frame = tk.Frame(report_window, bg=ModernStyles.COLORS['bg_main'])
            header_frame.pack(fill='x', pady=20)

            tk.Label(header_frame, text=f"📊 {report_name}",
                   font=('Cambria', 20, 'bold'),
                   fg=ModernStyles.COLORS['primary'],
                   bg=ModernStyles.COLORS['bg_main']).pack()

            # Report content
            content_frame = tk.Frame(report_window, bg=ModernStyles.COLORS['bg_main'])
            content_frame.pack(fill='both', expand=True, padx=20, pady=10)

            # Generate sample data based on report type
            self.generate_sample_report_data(content_frame, report_id, report_name)

            # Export buttons
            export_frame = tk.Frame(report_window, bg=ModernStyles.COLORS['bg_main'])
            export_frame.pack(fill='x', pady=20)

            export_buttons = [
                ("📄 PDF", lambda: self.export_report_pdf(report_name)),
                ("📊 Excel", lambda: self.export_report_excel(report_name)),
                ("📧 Email", lambda: self.email_report(report_name)),
                ("💾 Сохранить", lambda: self.save_report(report_name))
            ]

            for text, command in export_buttons:
                btn = tk.Button(export_frame, text=text, command=command,
                              bg=ModernStyles.COLORS['primary'], fg='white',
                              font=('Cambria', 11, 'bold'), relief='flat',
                              padx=20, pady=8)
                btn.pack(side='left', padx=10)

            log_info(f"Отчет '{report_name}' сгенерирован", "EnhancedReportingSystem")

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", f"генерация отчета {report_name}")

    def generate_sample_report_data(self, parent, report_id, report_name):
        """Generate sample data for report"""
        try:
            # Create scrollable text widget for report content
            text_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
            text_frame.pack(fill='both', expand=True)

            text_widget = tk.Text(text_frame, font=('Cambria', 11),
                                bg='white', fg=ModernStyles.COLORS['text_primary'],
                                wrap='word')
            scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            text_widget.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            # Generate sample content based on report type
            sample_content = self.get_sample_report_content(report_id, report_name)
            text_widget.insert('1.0', sample_content)
            text_widget.config(state='disabled')

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "генерация примера данных отчета")

    def get_sample_report_content(self, report_id, report_name):
        """Get sample content for report"""
        try:
            current_date = datetime.now().strftime("%d.%m.%Y %H:%M")

            content = f"""
{report_name}
Дата создания: {current_date}
{'='*60}

КРАТКОЕ РЕЗЮМЕ:
• Период анализа: {datetime.now().strftime('%B %Y')}
• Общий объем данных: 15,247 записей
• Статус обработки: Завершено
• Точность данных: 98.5%

КЛЮЧЕВЫЕ ПОКАЗАТЕЛИ:
"""

            if "продаж" in report_id.lower() or "sales" in report_id.lower():
                content += """
• Общая выручка: 2,847,592.50 руб
• Количество заказов: 1,247
• Средний чек: 2,284.15 руб
• Рост к предыдущему периоду: +12.3%

ТОП-5 ПРОДУКТОВ:
1. Пицца Маргарита - 247,850.00 руб (8.7%)
2. Борщ украинский - 198,420.00 руб (7.0%)
3. Стейк из говядины - 187,650.00 руб (6.6%)
4. Салат Цезарь - 156,780.00 руб (5.5%)
5. Паста Карбонара - 142,390.00 руб (5.0%)
"""
            elif "финанс" in report_id.lower() or "financial" in report_id.lower():
                content += """
• Валовая прибыль: 1,847,592.50 руб
• Операционные расходы: 1,247,850.00 руб
• Чистая прибыль: 599,742.50 руб
• Рентабельность: 21.1%

СТРУКТУРА РАСХОДОВ:
• Продукты и сырье: 45.2%
• Заработная плата: 28.7%
• Аренда и коммунальные: 12.4%
• Маркетинг: 6.8%
• Прочие расходы: 6.9%
"""
            elif "склад" in report_id.lower() or "inventory" in report_id.lower():
                content += """
• Общая стоимость запасов: 847,592.50 руб
• Количество позиций: 347
• Оборачиваемость: 12.4 раза в год
• Уровень потерь: 2.1%

КРИТИЧЕСКИЕ ЗАПАСЫ:
• Мука пшеничная: 12 кг (мин. 50 кг)
• Сыр моцарелла: 8 кг (мин. 25 кг)
• Томаты свежие: 15 кг (мин. 30 кг)
• Масло оливковое: 3 л (мин. 10 л)
"""
            else:
                content += """
• Общее количество записей: 15,247
• Обработано успешно: 14,928 (97.9%)
• Ошибки обработки: 319 (2.1%)
• Время обработки: 2.3 секунды

ДЕТАЛЬНАЯ ИНФОРМАЦИЯ:
Данный отчет содержит подробную аналитику по выбранным параметрам.
Все данные актуальны на момент генерации отчета.
"""

            content += f"""

РЕКОМЕНДАЦИИ:
• Продолжить мониторинг ключевых показателей
• Обратить внимание на выявленные тренды
• Рассмотреть возможности оптимизации
• Запланировать следующий анализ

ТЕХНИЧЕСКАЯ ИНФОРМАЦИЯ:
• Источник данных: Основная база данных
• Метод анализа: Статистический анализ
• Версия системы: 3.0
• ID отчета: {report_id}

{'='*60}
Отчет сгенерирован автоматически системой Enhanced Reporting System
© 2024 Restaurant Management System
"""

            return content

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "получение содержимого отчета")
            return f"Ошибка генерации содержимого отчета: {str(e)}"

    # Analytics methods
    def show_sales_trends(self):
        """Show sales trends analysis"""
        messagebox.showinfo("Анализ Трендов", "📈 Анализ трендов продаж:\n\n• Рост продаж на 15.2% за месяц\n• Пиковые часы: 12:00-14:00, 19:00-21:00\n• Лучший день недели: Суббота\n• Сезонность: Рост в выходные дни")

    def show_financial_trends(self):
        """Show financial trends analysis"""
        messagebox.showinfo("Финансовые Тренды", "💰 Анализ финансовых трендов:\n\n• Прибыльность растет на 8.7% ежемесячно\n• Снижение затрат на 3.2%\n• ROI улучшился на 12.5%\n• Денежный поток стабилен")

    def show_inventory_trends(self):
        """Show inventory trends analysis"""
        messagebox.showinfo("Тренды Запасов", "📦 Анализ трендов запасов:\n\n• Оборачиваемость увеличилась на 18%\n• Потери снизились на 2.1%\n• Оптимизация закупок на 15%\n• ABC-анализ показывает эффективность")

    def show_hr_trends(self):
        """Show HR trends analysis"""
        messagebox.showinfo("HR Тренды", "👥 Анализ HR трендов:\n\n• Производительность выросла на 11%\n• Текучесть снизилась на 5.3%\n• Удовлетворенность персонала: 87%\n• Эффективность обучения: 92%")

    def show_period_comparison(self):
        """Show period comparison"""
        messagebox.showinfo("Сравнение Периодов", "📅 Сравнительный анализ:\n\nТекущий месяц vs Предыдущий:\n• Выручка: +12.3%\n• Заказы: +8.7%\n• Средний чек: +3.4%\n• Прибыль: +15.2%")

    def show_location_comparison(self):
        """Show location comparison"""
        messagebox.showinfo("Сравнение Локаций", "🏢 Сравнение по локациям:\n\n• Центральная: 45% выручки\n• Северная: 28% выручки\n• Южная: 27% выручки\n\nЛидер по эффективности: Центральная")

    def show_product_comparison(self):
        """Show product comparison"""
        messagebox.showinfo("Сравнение Продуктов", "📊 Анализ продуктов:\n\n• Топ категория: Пицца (32%)\n• Растущая категория: Салаты (+25%)\n• Падающая: Десерты (-8%)\n• Новинки показывают +45%")

    def show_staff_comparison(self):
        """Show staff comparison"""
        messagebox.showinfo("Сравнение Персонала", "👥 Анализ персонала:\n\n• Лучший официант: Иванов И. (98%)\n• Лучший повар: Петров П. (95%)\n• Средняя оценка: 87%\n• Рост производительности: +11%")

    # Predictive analytics methods
    def run_predictive_model(self, model_id, model_name):
        """Run predictive model"""
        try:
            # Simulate model execution
            progress_window = tk.Toplevel(self.window)
            progress_window.title(f"🔮 {model_name}")
            progress_window.geometry("400x200")
            progress_window.configure(bg=ModernStyles.COLORS['bg_main'])

            tk.Label(progress_window, text=f"Выполнение модели: {model_name}",
                   font=('Cambria', 14, 'bold'),
                   fg=ModernStyles.COLORS['primary'],
                   bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

            progress = ttk.Progressbar(progress_window, length=300, mode='determinate')
            progress.pack(pady=20)

            status_label = tk.Label(progress_window, text="Инициализация...",
                                  font=('Cambria', 11),
                                  fg=ModernStyles.COLORS['text_primary'],
                                  bg=ModernStyles.COLORS['bg_main'])
            status_label.pack(pady=10)

            def update_progress():
                for i in range(101):
                    progress['value'] = i
                    if i < 30:
                        status_label.config(text="Загрузка данных...")
                    elif i < 60:
                        status_label.config(text="Обучение модели...")
                    elif i < 90:
                        status_label.config(text="Генерация прогнозов...")
                    else:
                        status_label.config(text="Завершение...")

                    progress_window.update()
                    time.sleep(0.05)

                progress_window.destroy()
                self.show_prediction_results(model_id, model_name)

            threading.Thread(target=update_progress, daemon=True).start()

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", f"запуск модели {model_name}")

    def show_prediction_results(self, model_id, model_name):
        """Show prediction results"""
        try:
            results = {
                "sales_forecast": "📈 Прогноз продаж на следующий месяц:\n\n• Ожидаемая выручка: 3,125,000 руб (+9.7%)\n• Количество заказов: 1,380 (+10.7%)\n• Пиковые дни: 15, 22, 29 число\n• Рекомендация: увеличить запасы на 12%",
                "demand_prediction": "🔮 Прогноз спроса:\n\n• Пицца: +15% (высокий спрос)\n• Салаты: +8% (растущий тренд)\n• Супы: -3% (сезонное снижение)\n• Десерты: +5% (стабильный рост)",
                "customer_churn": "👥 Анализ оттока клиентов:\n\n• Риск оттока: 12.3% клиентов\n• Основные причины: цены, сервис\n• Рекомендации: программа лояльности\n• Потенциальная экономия: 450,000 руб",
                "inventory_optimization": "📦 Оптимизация запасов:\n\n• Переизбыток: 15 позиций\n• Недостаток: 8 позиций\n• Оптимальный уровень: 92% позиций\n• Экономия: 125,000 руб в месяц",
                "revenue_forecast": "💰 Прогноз выручки:\n\n• Следующий квартал: 9,450,000 руб\n• Годовой прогноз: 38,200,000 руб\n• Рост: +11.5% к текущему году\n• Доверительный интервал: 95%"
            }

            result_text = results.get(model_id, f"Результаты модели {model_name} готовы к анализу")
            messagebox.showinfo(f"🔮 {model_name}", result_text)

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "показ результатов прогноза")

    def configure_model(self, model_id, model_name):
        """Configure predictive model"""
        messagebox.showinfo("Настройка Модели", f"⚙️ Настройка модели: {model_name}\n\nПараметры модели будут настроены в следующей версии системы.")

    # Custom report builder methods
    def add_data_source(self, source):
        """Add data source to custom report"""
        try:
            current_sources = list(self.selected_sources_listbox.get(0, tk.END))
            if source not in current_sources:
                self.selected_sources_listbox.insert(tk.END, source)
                log_info(f"Источник данных добавлен: {source}", "EnhancedReportingSystem")
        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "добавление источника данных")

    def add_date_filter(self):
        """Add date filter"""
        messagebox.showinfo("Фильтр Даты", "📅 Фильтр по дате добавлен:\n\nПериод: с 01.01.2024 по 31.12.2024")

    def add_location_filter(self):
        """Add location filter"""
        messagebox.showinfo("Фильтр Локации", "🏢 Фильтр по локации добавлен:\n\nВыбранные локации: Все активные")

    def add_user_filter(self):
        """Add user filter"""
        messagebox.showinfo("Фильтр Пользователя", "👤 Фильтр по пользователю добавлен:\n\nПользователи: Все активные")

    def add_amount_filter(self):
        """Add amount filter"""
        messagebox.showinfo("Фильтр Суммы", "💰 Фильтр по сумме добавлен:\n\nДиапазон: от 0 до 999,999 руб")

    def preview_custom_report(self):
        """Preview custom report"""
        try:
            report_name = self.report_name_var.get() or "Пользовательский отчет"
            sources = list(self.selected_sources_listbox.get(0, tk.END))

            preview_text = f"📊 Предпросмотр отчета: {report_name}\n\n"
            preview_text += f"Источники данных: {', '.join(sources) if sources else 'Не выбраны'}\n"
            preview_text += f"Фильтры: Применены стандартные фильтры\n"
            preview_text += f"Формат: Табличный с графиками\n"
            preview_text += f"Дата создания: {datetime.now().strftime('%d.%m.%Y %H:%M')}"

            messagebox.showinfo("Предпросмотр", preview_text)

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "предпросмотр отчета")

    def save_custom_report(self):
        """Save custom report template"""
        try:
            report_name = self.report_name_var.get()
            if not report_name:
                messagebox.showwarning("Предупреждение", "Введите название отчета")
                return

            sources = list(self.selected_sources_listbox.get(0, tk.END))
            if not sources:
                messagebox.showwarning("Предупреждение", "Выберите источники данных")
                return

            # Save template (simulate)
            template = {
                "name": report_name,
                "sources": sources,
                "created": datetime.now().isoformat(),
                "filters": []
            }

            self.custom_templates.append(template)
            messagebox.showinfo("Успех", f"Шаблон '{report_name}' сохранен")
            log_info(f"Пользовательский шаблон сохранен: {report_name}", "EnhancedReportingSystem")

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "сохранение пользовательского отчета")

    def generate_custom_report(self):
        """Generate custom report"""
        try:
            report_name = self.report_name_var.get() or "Пользовательский отчет"
            sources = list(self.selected_sources_listbox.get(0, tk.END))

            if not sources:
                messagebox.showwarning("Предупреждение", "Выберите источники данных")
                return

            # Generate custom report
            self.generate_report("custom_report", report_name)

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "генерация пользовательского отчета")

    def clear_builder(self):
        """Clear report builder"""
        try:
            self.report_name_var.set("")
            self.selected_sources_listbox.delete(0, tk.END)
            messagebox.showinfo("Очистка", "Конструктор отчетов очищен")

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "очистка конструктора")

    def export_custom_report(self):
        """Export custom report"""
        messagebox.showinfo("Экспорт", "📤 Пользовательский отчет экспортирован в формате Excel")

    # Automation methods
    def get_all_reports(self):
        """Get list of all available reports"""
        all_reports = []
        for category, reports in self.report_categories.items():
            for report_id, report_name in reports.items():
                all_reports.append(f"{category}: {report_name}")
        return all_reports

    def create_schedule(self):
        """Create new report schedule"""
        try:
            report = self.schedule_report_var.get()
            frequency = self.frequency_var.get()
            hour = self.hour_var.get()
            minute = self.minute_var.get()
            recipients = self.recipients_text.get("1.0", tk.END).strip()

            if not report:
                messagebox.showwarning("Предупреждение", "Выберите тип отчета")
                return

            if not recipients:
                messagebox.showwarning("Предупреждение", "Укажите получателей")
                return

            # Create schedule entry
            schedule_id = f"schedule_{len(self.schedules) + 1}"
            schedule = {
                "id": schedule_id,
                "report": report,
                "frequency": frequency,
                "time": f"{hour}:{minute}",
                "recipients": recipients,
                "status": "Активно",
                "created": datetime.now().isoformat()
            }

            self.schedules.append(schedule)
            self.load_schedules()

            messagebox.showinfo("Успех", f"Расписание создано:\n\nОтчет: {report}\nЧастота: {frequency}\nВремя: {hour}:{minute}")
            log_info(f"Создано расписание: {report}", "EnhancedReportingSystem")

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "создание расписания")

    def load_schedules(self):
        """Load schedules into treeview"""
        try:
            # Clear existing items
            for item in self.schedules_tree.get_children():
                self.schedules_tree.delete(item)

            # Add schedules
            for schedule in self.schedules:
                self.schedules_tree.insert('', 'end', values=(
                    schedule['report'][:30] + '...' if len(schedule['report']) > 30 else schedule['report'],
                    schedule['frequency'],
                    schedule['time'],
                    schedule['status']
                ))

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "загрузка расписаний")

    def run_schedule(self):
        """Run selected schedule"""
        try:
            selection = self.schedules_tree.selection()
            if not selection:
                messagebox.showwarning("Предупреждение", "Выберите расписание")
                return

            messagebox.showinfo("Запуск", "▶️ Расписание запущено\n\nОтчет будет сгенерирован и отправлен получателям")

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "запуск расписания")

    def pause_schedule(self):
        """Pause selected schedule"""
        messagebox.showinfo("Пауза", "⏸️ Расписание приостановлено")

    def delete_schedule(self):
        """Delete selected schedule"""
        try:
            selection = self.schedules_tree.selection()
            if not selection:
                messagebox.showwarning("Предупреждение", "Выберите расписание")
                return

            if messagebox.askyesno("Подтверждение", "Удалить выбранное расписание?"):
                # Remove from list and reload
                index = self.schedules_tree.index(selection[0])
                if 0 <= index < len(self.schedules):
                    del self.schedules[index]
                    self.load_schedules()
                    messagebox.showinfo("Успех", "Расписание удалено")

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "удаление расписания")

    def edit_schedule(self):
        """Edit selected schedule"""
        messagebox.showinfo("Редактирование", "✏️ Редактирование расписания будет доступно в следующей версии")

    # Template methods
    def use_template(self, template_name):
        """Use selected template"""
        messagebox.showinfo("Использование Шаблона", f"📊 Шаблон '{template_name}' применен к новому отчету")

    def edit_template(self, template_name):
        """Edit selected template"""
        messagebox.showinfo("Редактирование Шаблона", f"✏️ Редактирование шаблона '{template_name}' будет доступно в следующей версии")

    # Export methods
    def export_report_pdf(self, report_name):
        """Export report to PDF"""
        messagebox.showinfo("Экспорт PDF", f"📄 Отчет '{report_name}' экспортирован в PDF")

    def export_report_excel(self, report_name):
        """Export report to Excel"""
        messagebox.showinfo("Экспорт Excel", f"📊 Отчет '{report_name}' экспортирован в Excel")

    def email_report(self, report_name):
        """Email report"""
        messagebox.showinfo("Отправка Email", f"📧 Отчет '{report_name}' отправлен по электронной почте")

    def save_report(self, report_name):
        """Save report"""
        messagebox.showinfo("Сохранение", f"💾 Отчет '{report_name}' сохранен в системе")

    # Control panel methods
    def refresh_all_data(self):
        """Refresh all data"""
        try:
            # Simulate data refresh
            progress_window = tk.Toplevel(self.window)
            progress_window.title("🔄 Обновление Данных")
            progress_window.geometry("300x150")
            progress_window.configure(bg=ModernStyles.COLORS['bg_main'])

            tk.Label(progress_window, text="Обновление данных...",
                   font=('Cambria', 12, 'bold'),
                   fg=ModernStyles.COLORS['primary'],
                   bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

            progress = ttk.Progressbar(progress_window, length=250, mode='indeterminate')
            progress.pack(pady=20)
            progress.start()

            def complete_refresh():
                time.sleep(2)
                progress.stop()
                progress_window.destroy()
                messagebox.showinfo("Успех", "🔄 Все данные обновлены")

            threading.Thread(target=complete_refresh, daemon=True).start()

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "обновление данных")

    def export_all_reports(self):
        """Export all reports"""
        messagebox.showinfo("Экспорт Всех", "📤 Все отчеты экспортированы в папку 'Отчеты'")

    def send_reports(self):
        """Send reports via email"""
        messagebox.showinfo("Отправка Отчетов", "📧 Все отчеты отправлены получателям")

    def show_settings(self):
        """Show system settings"""
        try:
            settings_window = tk.Toplevel(self.window)
            settings_window.title("⚙️ Настройки Системы Отчетности")
            settings_window.geometry("500x400")
            settings_window.configure(bg=ModernStyles.COLORS['bg_main'])

            # Settings header
            tk.Label(settings_window, text="⚙️ Настройки Системы",
                   font=('Cambria', 16, 'bold'),
                   fg=ModernStyles.COLORS['primary'],
                   bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

            # Settings notebook
            settings_notebook = ttk.Notebook(settings_window)
            settings_notebook.pack(fill='both', expand=True, padx=20, pady=10)

            # General settings
            general_frame = tk.Frame(settings_notebook, bg=ModernStyles.COLORS['bg_secondary'])
            settings_notebook.add(general_frame, text="Общие")

            tk.Label(general_frame, text="Автообновление данных:",
                   font=('Cambria', 12),
                   fg=ModernStyles.COLORS['text_primary'],
                   bg=ModernStyles.COLORS['bg_secondary']).pack(anchor='w', padx=20, pady=10)

            auto_refresh_var = tk.BooleanVar(value=True)
            tk.Checkbutton(general_frame, text="Включить автообновление",
                         variable=auto_refresh_var,
                         font=('Cambria', 11),
                         fg=ModernStyles.COLORS['text_primary'],
                         bg=ModernStyles.COLORS['bg_secondary']).pack(anchor='w', padx=40)

            # Email settings
            email_frame = tk.Frame(settings_notebook, bg=ModernStyles.COLORS['bg_secondary'])
            settings_notebook.add(email_frame, text="Email")

            tk.Label(email_frame, text="SMTP сервер:",
                   font=('Cambria', 12),
                   fg=ModernStyles.COLORS['text_primary'],
                   bg=ModernStyles.COLORS['bg_secondary']).pack(anchor='w', padx=20, pady=10)

            smtp_entry = tk.Entry(email_frame, font=('Cambria', 11), width=40)
            smtp_entry.pack(padx=20, pady=5)
            smtp_entry.insert(0, "smtp.gmail.com")

            # Save button
            save_btn = tk.Button(settings_window, text="💾 Сохранить Настройки",
                               command=lambda: self.save_settings(settings_window),
                               bg=ModernStyles.COLORS['success'], fg='white',
                               font=('Cambria', 12, 'bold'), relief='flat',
                               padx=30, pady=10)
            save_btn.pack(pady=20)

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "показ настроек")

    def save_settings(self, settings_window):
        """Save system settings"""
        try:
            messagebox.showinfo("Настройки", "💾 Настройки сохранены")
            settings_window.destroy()

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "сохранение настроек")

    def close_system(self):
        """Close reporting system"""
        try:
            if messagebox.askyesno("Закрытие", "Закрыть систему отчетности?"):
                log_info("Система отчетности закрыта", "EnhancedReportingSystem")
                self.window.destroy()

        except Exception as e:
            handle_module_error(e, "Расширенная система отчетности", "закрытие системы")


def create_enhanced_reporting_system(parent, db_manager):
    """Create and return enhanced reporting system window"""
    try:
        system = EnhancedReportingSystem(parent, db_manager)
        log_info("Расширенная система отчетности создана", "EnhancedReportingSystem")
        return system.window

    except Exception as e:
        handle_module_error(e, "Расширенная система отчетности", "создание системы")
        return None


# Initialize module
if __name__ == "__main__":
    try:
        import tkinter as tk
        from tkinter import ttk, messagebox

        # Test the module
        root = tk.Tk()
        root.withdraw()  # Hide root window

        # Create test database manager
        class TestDBManager:
            pass

        db_manager = TestDBManager()

        # Create enhanced reporting system
        system_window = create_enhanced_reporting_system(root, db_manager)

        if system_window:
            root.mainloop()
        else:
            print("Ошибка создания системы отчетности")

    except Exception as e:
        print(f"Ошибка инициализации модуля: {e}")
        import traceback
        traceback.print_exc()
