"""
Полностью функциональная система контроля качества
Проверки качества, стандарты, аудиты, сертификация
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import json
import os
from gui.styles import ModernStyles

class QualityControlSystem:
    """Система контроля качества"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        
        # Стандарты качества
        self.quality_standards = [
            {
                "id": 1, "category": "Продукты", "standard": "Температура хранения",
                "requirement": "0-4°C для молочных продуктов", "frequency": "Ежедневно",
                "responsible": "Кладовщик", "status": "Соблюдается"
            },
            {
                "id": 2, "category": "Кухня", "standard": "Санитарная обработка",
                "requirement": "Дезинфекция поверхностей каждые 2 часа", "frequency": "Каждые 2 часа",
                "responsible": "Повар", "status": "Соблюдается"
            },
            {
                "id": 3, "category": "Сервис", "standard": "Время подачи",
                "requirement": "Горячие блюда не более 15 минут", "frequency": "Постоянно",
                "responsible": "Официант", "status": "Требует внимания"
            },
            {
                "id": 4, "category": "Продукты", "standard": "Срок годности",
                "requirement": "Проверка сроков годности при поступлении", "frequency": "При поступлении",
                "responsible": "Кладовщик", "status": "Соблюдается"
            },
            {
                "id": 5, "category": "Кухня", "standard": "Температура приготовления",
                "requirement": "Мясо не менее 75°C внутри", "frequency": "При приготовлении",
                "responsible": "Повар", "status": "Соблюдается"
            }
        ]
        
        # Проверки качества
        self.quality_checks = [
            {
                "date": "2024-01-15", "inspector": "Иванов И.И.", "category": "Кухня",
                "item": "Санитарное состояние", "score": 95, "notes": "Отличное состояние, все требования соблюдены",
                "violations": [], "status": "Пройдена"
            },
            {
                "date": "2024-01-14", "inspector": "Петрова М.С.", "category": "Продукты",
                "item": "Качество мяса", "score": 88, "notes": "Хорошее качество, небольшие замечания по упаковке",
                "violations": ["Неплотная упаковка"], "status": "Пройдена"
            },
            {
                "date": "2024-01-13", "inspector": "Сидоров А.П.", "category": "Сервис",
                "item": "Время подачи блюд", "score": 75, "notes": "Превышение времени подачи на 5 минут",
                "violations": ["Превышение времени подачи"], "status": "Требует улучшения"
            },
            {
                "date": "2024-01-12", "inspector": "Козлова Е.В.", "category": "Продукты",
                "item": "Температурный режим", "score": 92, "notes": "Температура соблюдается, холодильники работают исправно",
                "violations": [], "status": "Пройдена"
            }
        ]
        
        # Сертификаты и документы
        self.certificates = [
            {
                "name": "Сертификат соответствия ГОСТ", "type": "Безопасность пищевых продуктов",
                "issue_date": "2023-06-15", "expiry_date": "2024-06-15", "status": "Действующий",
                "authority": "Росстандарт", "number": "РОСС RU.АБ12.Н00123"
            },
            {
                "name": "Санитарно-эпидемиологическое заключение", "type": "Санитарные требования",
                "issue_date": "2023-08-20", "expiry_date": "2024-08-20", "status": "Действующий",
                "authority": "Роспотребнадзор", "number": "77.01.16.000.М.000456.08.23"
            },
            {
                "name": "Лицензия на алкоголь", "type": "Розничная продажа алкоголя",
                "issue_date": "2023-01-10", "expiry_date": "2028-01-10", "status": "Действующий",
                "authority": "Росалкогольрегулирование", "number": "77РПА0012345"
            }
        ]
    
    def create_window(self):
        """Создать окно системы контроля качества"""
        try:
            from utils.window_utils import create_centered_dialog
            self.window = create_centered_dialog(
                self.parent,
                "🔍 Контроль Качества",
                width=1450,
                height=950,
                resizable=True
            )
        except ImportError:
            # Fallback если utils не доступны
            self.window = tk.Toplevel(self.parent)
            self.window.title("🔍 Контроль Качества")
            self.window.geometry("1450x950")
            self.window.configure(bg=ModernStyles.COLORS['bg_main'])
            self.window.resizable(True, True)

            # Центрировать окно
            self.window.update_idletasks()
            x = (self.window.winfo_screenwidth() // 2) - (1450 // 2)
            y = (self.window.winfo_screenheight() // 2) - (950 // 2)
            self.window.geometry(f"1450x950+{x}+{y}")
        self.window.resizable(True, True)
        
        self.create_interface()
    
    def create_interface(self):
        """Создать интерфейс системы контроля качества"""
        # Заголовок
        header_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['primary'], height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="🔍 Система Контроля Качества",
                font=('Arial', 16, 'bold'), bg=ModernStyles.COLORS['primary'], 
                fg='white').pack(side='left', padx=20, pady=15)
        
        # Кнопки действий
        btn_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['primary'])
        btn_frame.pack(side='right', padx=20, pady=10)
        
        tk.Button(btn_frame, text="➕ Новая Проверка", command=self.add_quality_check,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="📋 Аудит", command=self.conduct_audit,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="📊 Отчёт", command=self.generate_quality_report,
                 bg=ModernStyles.COLORS['secondary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        # Основной контент
        main_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Создать вкладки
        self.create_tabs(main_frame)
    
    def create_tabs(self, parent):
        """Создать вкладки модуля"""
        notebook = ttk.Notebook(parent)
        notebook.pack(fill='both', expand=True)
        
        # Вкладка стандартов
        standards_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(standards_frame, text="📏 Стандарты Качества")
        self.create_standards_tab(standards_frame)
        
        # Вкладка проверок
        checks_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(checks_frame, text="✅ Проверки Качества")
        self.create_checks_tab(checks_frame)
        
        # Вкладка сертификатов
        certificates_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(certificates_frame, text="📜 Сертификаты")
        self.create_certificates_tab(certificates_frame)
        
        # Вкладка аналитики
        analytics_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(analytics_frame, text="📈 Аналитика Качества")
        self.create_analytics_tab(analytics_frame)
    
    def create_standards_tab(self, parent):
        """Создать вкладку стандартов качества"""
        # Заголовок
        tk.Label(parent, text="Стандарты Качества",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)
        
        # Статистика стандартов
        stats_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        stats_frame.pack(fill='x', padx=20, pady=10)
        
        total_standards = len(self.quality_standards)
        compliant = len([s for s in self.quality_standards if s['status'] == 'Соблюдается'])
        needs_attention = len([s for s in self.quality_standards if s['status'] == 'Требует внимания'])
        
        self.create_stat_card(stats_frame, "Всего стандартов", str(total_standards), ModernStyles.COLORS['primary'])
        self.create_stat_card(stats_frame, "Соблюдается", str(compliant), ModernStyles.COLORS['success'])
        self.create_stat_card(stats_frame, "Требует внимания", str(needs_attention), ModernStyles.COLORS['warning'])
        
        # Таблица стандартов
        columns = ('Категория', 'Стандарт', 'Требование', 'Частота', 'Ответственный', 'Статус')
        standards_tree = ttk.Treeview(parent, columns=columns, show='headings', style="Modern.Treeview")
        
        for col in columns:
            standards_tree.heading(col, text=col)
            standards_tree.column(col, width=150)
        
        # Заполнить стандартами
        for standard in self.quality_standards:
            status_icon = "✅" if standard['status'] == 'Соблюдается' else "⚠️"
            
            standards_tree.insert('', 'end', values=(
                standard['category'],
                standard['standard'],
                standard['requirement'],
                standard['frequency'],
                standard['responsible'],
                f"{status_icon} {standard['status']}"
            ))
        
        standards_tree.pack(fill='both', expand=True, padx=10, pady=10)
    
    def create_checks_tab(self, parent):
        """Создать вкладку проверок качества"""
        # Заголовок
        tk.Label(parent, text="История Проверок Качества",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)
        
        # Таблица проверок
        columns = ('Дата', 'Инспектор', 'Категория', 'Объект', 'Оценка', 'Статус')
        checks_tree = ttk.Treeview(parent, columns=columns, show='headings', style="Modern.Treeview")
        
        for col in columns:
            checks_tree.heading(col, text=col)
            checks_tree.column(col, width=120)
        
        # Заполнить проверками
        for check in self.quality_checks:
            status_icon = "✅" if check['status'] == 'Пройдена' else "⚠️"
            
            checks_tree.insert('', 'end', values=(
                check['date'],
                check['inspector'],
                check['category'],
                check['item'],
                f"{check['score']}/100",
                f"{status_icon} {check['status']}"
            ))
        
        checks_tree.pack(fill='both', expand=True, padx=10, pady=10)
    
    def create_certificates_tab(self, parent):
        """Создать вкладку сертификатов"""
        # Заголовок
        tk.Label(parent, text="Сертификаты и Лицензии",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)
        
        # Таблица сертификатов
        columns = ('Название', 'Тип', 'Дата выдачи', 'Срок действия', 'Орган', 'Статус')
        cert_tree = ttk.Treeview(parent, columns=columns, show='headings', style="Modern.Treeview")
        
        for col in columns:
            cert_tree.heading(col, text=col)
            cert_tree.column(col, width=150)
        
        # Заполнить сертификатами
        for cert in self.certificates:
            # Проверить срок действия
            expiry = datetime.strptime(cert['expiry_date'], "%Y-%m-%d")
            days_left = (expiry - datetime.now()).days
            
            if days_left > 30:
                status_icon = "✅"
            elif days_left > 0:
                status_icon = "⚠️"
            else:
                status_icon = "❌"
            
            cert_tree.insert('', 'end', values=(
                cert['name'],
                cert['type'],
                cert['issue_date'],
                cert['expiry_date'],
                cert['authority'],
                f"{status_icon} {cert['status']}"
            ))
        
        cert_tree.pack(fill='both', expand=True, padx=10, pady=10)
    
    def create_analytics_tab(self, parent):
        """Создать вкладку аналитики качества"""
        # Заголовок
        tk.Label(parent, text="Аналитика Качества",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)
        
        # Общая статистика
        stats_frame = tk.LabelFrame(parent, text="Общая Статистика",
                                   font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        stats_frame.pack(fill='x', padx=20, pady=10)
        
        # Рассчитать статистику
        total_checks = len(self.quality_checks)
        avg_score = sum(check['score'] for check in self.quality_checks) / total_checks if total_checks > 0 else 0
        passed_checks = len([c for c in self.quality_checks if c['status'] == 'Пройдена'])
        
        stats_text = f"""
Статистика проверок качества:
• Всего проверок: {total_checks}
• Средняя оценка: {avg_score:.1f}/100
• Успешных проверок: {passed_checks} ({passed_checks/total_checks*100:.1f}%)
• Нарушений выявлено: {sum(len(c['violations']) for c in self.quality_checks)}
        """
        
        tk.Label(stats_frame, text=stats_text, font=('Arial', 11),
                bg=ModernStyles.COLORS['bg_main'], justify='left').pack(padx=20, pady=20)
        
        # Анализ по категориям
        category_frame = tk.LabelFrame(parent, text="Анализ по Категориям",
                                      font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        category_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Группировка по категориям
        categories = {}
        for check in self.quality_checks:
            cat = check['category']
            if cat not in categories:
                categories[cat] = {'count': 0, 'avg_score': 0, 'violations': 0}
            categories[cat]['count'] += 1
            categories[cat]['avg_score'] += check['score']
            categories[cat]['violations'] += len(check['violations'])
        
        # Рассчитать средние значения
        for cat in categories:
            if categories[cat]['count'] > 0:
                categories[cat]['avg_score'] /= categories[cat]['count']
        
        # Отобразить статистику по категориям
        for category, stats in categories.items():
            row = tk.Frame(category_frame, bg=ModernStyles.COLORS['bg_main'])
            row.pack(fill='x', padx=10, pady=5)
            
            category_icon = "🍽️" if category == "Кухня" else "📦" if category == "Продукты" else "👥"
            
            tk.Label(row, text=f"{category_icon} {category}", font=('Arial', 12, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).pack(side='left')
            
            info_text = f"{stats['count']} проверок • {stats['avg_score']:.1f}/100 • {stats['violations']} нарушений"
            tk.Label(row, text=info_text, font=('Arial', 11),
                    bg=ModernStyles.COLORS['bg_main']).pack(side='right')
    
    def create_stat_card(self, parent, title, value, color):
        """Создать карточку статистики"""
        card = tk.Frame(parent, bg=color, relief='solid', bd=1)
        card.pack(side='left', fill='both', expand=True, padx=5)
        
        tk.Label(card, text=title, font=('Arial', 10), bg=color, fg='white').pack(pady=(15, 5))
        tk.Label(card, text=value, font=('Arial', 14, 'bold'), bg=color, fg='white').pack(pady=(0, 15))
    
    def add_quality_check(self):
        """Добавить новую проверку качества"""
        try:
            from utils.window_utils import create_centered_dialog
            check_window = create_centered_dialog(
                self.window,
                "🔍 Новая Проверка Качества",
                width=800,
                height=700,
                resizable=True
            )
        except ImportError:
            check_window = tk.Toplevel(self.window)
            check_window.title("🔍 Новая Проверка Качества")
            check_window.geometry("800x700")
            check_window.configure(bg=ModernStyles.COLORS['bg_main'])
            check_window.resizable(True, True)

            # Центрировать окно
            check_window.update_idletasks()
            x = (check_window.winfo_screenwidth() // 2) - (800 // 2)
            y = (check_window.winfo_screenheight() // 2) - (700 // 2)
            check_window.geometry(f"800x700+{x}+{y}")

        # Заголовок
        tk.Label(check_window, text="Создать новую проверку качества",
                font=('Cambria', 18, 'bold italic'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

        # Основная форма
        form_frame = tk.Frame(check_window, bg=ModernStyles.COLORS['bg_main'])
        form_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # Поля формы
        fields = [
            ("Тип проверки:", "type", "combo"),
            ("Объект проверки:", "object", "entry"),
            ("Инспектор:", "inspector", "combo"),
            ("Дата проверки:", "date", "entry"),
            ("Время проверки:", "time", "entry"),
            ("Критерии оценки:", "criteria", "text"),
            ("Комментарии:", "comments", "text")
        ]

        entries = {}
        row = 0

        for label_text, field_name, field_type in fields:
            tk.Label(form_frame, text=label_text, font=('Cambria', 12, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).grid(row=row, column=0, sticky='nw', pady=8, padx=(0, 10))

            if field_type == "entry":
                entry = tk.Entry(form_frame, font=('Cambria', 12), width=40)
                entry.grid(row=row, column=1, sticky='ew', pady=8)

                # Предзаполнить дату и время
                if field_name == "date":
                    from datetime import datetime
                    entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
                elif field_name == "time":
                    from datetime import datetime
                    entry.insert(0, datetime.now().strftime("%H:%M"))

                entries[field_name] = entry

            elif field_type == "combo":
                if field_name == "type":
                    values = ["Проверка продуктов", "Проверка кухни", "Проверка зала",
                             "Проверка персонала", "Проверка оборудования", "Санитарная проверка"]
                elif field_name == "inspector":
                    values = ["Иванов И.И.", "Петрова А.С.", "Сидоров П.П.", "Козлова М.В."]

                combo = ttk.Combobox(form_frame, font=('Cambria', 12), width=37, values=values, state="readonly")
                combo.grid(row=row, column=1, sticky='ew', pady=8)
                combo.set(values[0])
                entries[field_name] = combo

            elif field_type == "text":
                text_widget = tk.Text(form_frame, font=('Cambria', 11), width=40, height=4)
                text_widget.grid(row=row, column=1, sticky='ew', pady=8)

                # Предзаполнить критерии
                if field_name == "criteria":
                    default_criteria = "• Соответствие стандартам качества\n• Соблюдение санитарных норм\n• Правильность хранения\n• Внешний вид и свежесть"
                    text_widget.insert('1.0', default_criteria)

                entries[field_name] = text_widget

            row += 1

        # Настройка сетки
        form_frame.grid_columnconfigure(1, weight=1)

        # Кнопки
        btn_frame = tk.Frame(check_window, bg=ModernStyles.COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=30, pady=20)

        def save_check():
            try:
                # Получить данные из формы
                check_type = entries['type'].get()
                object_name = entries['object'].get().strip()
                inspector = entries['inspector'].get()
                date = entries['date'].get().strip()
                time = entries['time'].get().strip()

                # Получить текст из Text виджетов
                criteria_text = entries['criteria'].get('1.0', 'end-1c').strip()
                comments_text = entries['comments'].get('1.0', 'end-1c').strip()

                # Валидация
                if not object_name:
                    messagebox.showerror("Ошибка", "Введите объект проверки")
                    return
                if not date:
                    messagebox.showerror("Ошибка", "Введите дату проверки")
                    return

                # Создать новую проверку
                from datetime import datetime
                new_check = {
                    "id": len(self.quality_checks) + 1,
                    "type": check_type,
                    "object": object_name,
                    "inspector": inspector,
                    "date": date,
                    "time": time,
                    "status": "Запланирована",
                    "score": 0,
                    "criteria": criteria_text,
                    "comments": comments_text,
                    "created_at": datetime.now().strftime("%Y-%m-%d %H:%M")
                }

                # Добавить в список
                self.quality_checks.append(new_check)

                # Закрыть окно
                check_window.destroy()

                messagebox.showinfo("Успех", f"Проверка '{check_type}' для '{object_name}' создана!")

            except Exception as e:
                messagebox.showerror("Ошибка", f"Ошибка при создании проверки: {e}")

        tk.Button(btn_frame, text="💾 Создать проверку", command=save_check,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold italic'), relief='flat', padx=30, pady=10).pack(side='left')

        tk.Button(btn_frame, text="❌ Отмена", command=check_window.destroy,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Cambria', 12, 'bold italic'), relief='flat', padx=30, pady=10).pack(side='right')
    
    def conduct_audit(self):
        """Провести аудит"""
        try:
            from utils.window_utils import create_centered_dialog
            audit_window = create_centered_dialog(
                self.window,
                "🔍 Проведение Аудита Качества",
                width=900,
                height=800,
                resizable=True
            )
        except ImportError:
            audit_window = tk.Toplevel(self.window)
            audit_window.title("🔍 Проведение Аудита Качества")
            audit_window.geometry("900x800")
            audit_window.configure(bg=ModernStyles.COLORS['bg_main'])
            audit_window.resizable(True, True)

            # Центрировать окно
            audit_window.update_idletasks()
            x = (audit_window.winfo_screenwidth() // 2) - (900 // 2)
            y = (audit_window.winfo_screenheight() // 2) - (800 // 2)
            audit_window.geometry(f"900x800+{x}+{y}")

        # Заголовок
        tk.Label(audit_window, text="Комплексный Аудит Качества",
                font=('Cambria', 18, 'bold italic'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

        # Основная форма
        main_frame = tk.Frame(audit_window, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # Информация об аудите
        info_frame = tk.LabelFrame(main_frame, text="Информация об аудите",
                                  font=('Cambria', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        info_frame.pack(fill='x', pady=(0, 20))

        # Поля информации
        info_fields = [
            ("Тип аудита:", "audit_type", ["Плановый", "Внеплановый", "Контрольный"]),
            ("Аудитор:", "auditor", ["Главный технолог", "Санитарный врач", "Внешний аудитор"]),
            ("Дата проведения:", "audit_date", None),
            ("Время начала:", "start_time", None)
        ]

        audit_entries = {}

        for i, (label_text, field_name, values) in enumerate(info_fields):
            tk.Label(info_frame, text=label_text, font=('Cambria', 11, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).grid(row=i, column=0, sticky='w', pady=5, padx=10)

            if values:
                combo = ttk.Combobox(info_frame, font=('Cambria', 11), values=values, state="readonly", width=25)
                combo.grid(row=i, column=1, sticky='w', pady=5, padx=10)
                combo.set(values[0])
                audit_entries[field_name] = combo
            else:
                entry = tk.Entry(info_frame, font=('Cambria', 11), width=25)
                entry.grid(row=i, column=1, sticky='w', pady=5, padx=10)

                # Предзаполнить дату и время
                if field_name == "audit_date":
                    from datetime import datetime
                    entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
                elif field_name == "start_time":
                    from datetime import datetime
                    entry.insert(0, datetime.now().strftime("%H:%M"))

                audit_entries[field_name] = entry

        # Чек-лист аудита
        checklist_frame = tk.LabelFrame(main_frame, text="Чек-лист проверки",
                                       font=('Cambria', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        checklist_frame.pack(fill='both', expand=True, pady=(0, 20))

        # Создать скроллируемый фрейм для чек-листа
        canvas = tk.Canvas(checklist_frame, bg=ModernStyles.COLORS['bg_main'])
        scrollbar = ttk.Scrollbar(checklist_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=ModernStyles.COLORS['bg_main'])

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Элементы чек-листа
        checklist_items = [
            "Соблюдение температурного режима хранения продуктов",
            "Правильность маркировки и сроков годности",
            "Чистота и санитарное состояние кухни",
            "Соблюдение технологии приготовления блюд",
            "Внешний вид и качество готовых блюд",
            "Соблюдение правил личной гигиены персоналом",
            "Наличие и актуальность документации",
            "Состояние оборудования и инвентаря",
            "Соблюдение правил хранения химических средств",
            "Организация рабочих мест",
            "Соблюдение норм безопасности труда",
            "Качество обслуживания клиентов"
        ]

        check_vars = {}
        score_vars = {}

        # Заголовки
        tk.Label(scrollable_frame, text="Критерий", font=('Cambria', 11, 'bold'),
                bg=ModernStyles.COLORS['bg_main'], width=50).grid(row=0, column=0, padx=5, pady=5)
        tk.Label(scrollable_frame, text="Проверено", font=('Cambria', 11, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).grid(row=0, column=1, padx=5, pady=5)
        tk.Label(scrollable_frame, text="Оценка (1-10)", font=('Cambria', 11, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).grid(row=0, column=2, padx=5, pady=5)

        for i, item in enumerate(checklist_items, 1):
            # Текст критерия
            tk.Label(scrollable_frame, text=item, font=('Cambria', 10),
                    bg=ModernStyles.COLORS['bg_main'], anchor='w', width=50).grid(row=i, column=0, sticky='w', padx=5, pady=2)

            # Чекбокс
            var = tk.BooleanVar()
            check = tk.Checkbutton(scrollable_frame, variable=var, bg=ModernStyles.COLORS['bg_main'])
            check.grid(row=i, column=1, padx=5, pady=2)
            check_vars[item] = var

            # Поле оценки
            score_var = tk.StringVar(value="10")
            score_entry = tk.Entry(scrollable_frame, textvariable=score_var, width=5, font=('Cambria', 10))
            score_entry.grid(row=i, column=2, padx=5, pady=2)
            score_vars[item] = score_var

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Кнопки
        btn_frame = tk.Frame(audit_window, bg=ModernStyles.COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=30, pady=20)

        def complete_audit():
            try:
                # Получить данные аудита
                audit_type = audit_entries['audit_type'].get()
                auditor = audit_entries['auditor'].get()
                audit_date = audit_entries['audit_date'].get()
                start_time = audit_entries['start_time'].get()

                # Подсчитать результаты
                checked_items = []
                total_score = 0
                checked_count = 0

                for item, var in check_vars.items():
                    if var.get():
                        checked_items.append(item)
                        try:
                            score = int(score_vars[item].get())
                            total_score += score
                            checked_count += 1
                        except ValueError:
                            pass

                if checked_count == 0:
                    messagebox.showwarning("Предупреждение", "Отметьте хотя бы один критерий для проверки")
                    return

                avg_score = total_score / checked_count

                # Определить статус
                if avg_score >= 9:
                    status = "Отлично"
                    status_color = "green"
                elif avg_score >= 7:
                    status = "Хорошо"
                    status_color = "blue"
                elif avg_score >= 5:
                    status = "Удовлетворительно"
                    status_color = "orange"
                else:
                    status = "Неудовлетворительно"
                    status_color = "red"

                # Создать отчёт об аудите
                from datetime import datetime
                audit_report = {
                    "id": len(self.quality_checks) + 1,
                    "type": f"Аудит - {audit_type}",
                    "auditor": auditor,
                    "date": audit_date,
                    "time": start_time,
                    "status": status,
                    "score": round(avg_score, 1),
                    "checked_items": len(checked_items),
                    "total_items": len(checklist_items),
                    "details": checked_items,
                    "created_at": datetime.now().strftime("%Y-%m-%d %H:%M")
                }

                # Добавить в список проверок
                self.quality_checks.append(audit_report)

                # Закрыть окно
                audit_window.destroy()

                # Показать результаты
                result_message = f"""
Аудит качества завершён!

Тип аудита: {audit_type}
Аудитор: {auditor}
Дата: {audit_date} {start_time}

Проверено критериев: {checked_count} из {len(checklist_items)}
Средняя оценка: {avg_score:.1f}/10
Общий статус: {status}

Аудит сохранён в системе контроля качества.
                """

                messagebox.showinfo("Аудит завершён", result_message)

            except Exception as e:
                messagebox.showerror("Ошибка", f"Ошибка при завершении аудита: {e}")

        tk.Button(btn_frame, text="✅ Завершить аудит", command=complete_audit,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold italic'), relief='flat', padx=30, pady=10).pack(side='left')

        tk.Button(btn_frame, text="❌ Отмена", command=audit_window.destroy,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Cambria', 12, 'bold italic'), relief='flat', padx=30, pady=10).pack(side='right')
    
    def generate_quality_report(self):
        """Генерировать отчёт по качеству"""
        report_window = tk.Toplevel(self.window)
        report_window.title("📊 Отчёт по Качеству")
        report_window.geometry("600x500")
        report_window.configure(bg='white')
        
        # Заголовок
        tk.Label(report_window, text="📊 Отчёт по Контролю Качества",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)
        
        # Содержимое отчёта
        report_text = tk.Text(report_window, font=('Arial', 10), wrap='word')
        report_text.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Генерировать отчёт
        total_checks = len(self.quality_checks)
        avg_score = sum(check['score'] for check in self.quality_checks) / total_checks if total_checks > 0 else 0
        total_violations = sum(len(check['violations']) for check in self.quality_checks)
        
        report_content = f"""
ОТЧЁТ ПО КОНТРОЛЮ КАЧЕСТВА
==========================

Дата формирования: {datetime.now().strftime('%d.%m.%Y %H:%M')}

ОБЩИЕ ПОКАЗАТЕЛИ:
• Всего проверок: {total_checks}
• Средняя оценка: {avg_score:.1f}/100
• Общее количество нарушений: {total_violations}
• Соблюдение стандартов: {len([s for s in self.quality_standards if s['status'] == 'Соблюдается'])}/{len(self.quality_standards)}

ПОСЛЕДНИЕ ПРОВЕРКИ:
"""
        
        for check in self.quality_checks[-3:]:
            report_content += f"""
{check['date']} - {check['item']}:
  • Инспектор: {check['inspector']}
  • Оценка: {check['score']}/100
  • Статус: {check['status']}
  • Замечания: {check['notes']}
"""
        
        report_content += f"""

СЕРТИФИКАТЫ И ЛИЦЕНЗИИ:
• Всего документов: {len(self.certificates)}
• Действующих: {len([c for c in self.certificates if c['status'] == 'Действующий'])}
• Требуют продления в ближайшие 30 дней: 0

РЕКОМЕНДАЦИИ:
• Продолжать регулярные проверки качества
• Обратить внимание на стандарты, требующие улучшения
• Подготовиться к продлению сертификатов
• Провести дополнительное обучение персонала
        """
        
        report_text.insert('1.0', report_content)
        report_text.config(state='disabled')

def create_quality_control_system(parent, db_manager):
    """Создать систему контроля качества"""
    system = QualityControlSystem(parent, db_manager)
    system.create_window()
    return system
