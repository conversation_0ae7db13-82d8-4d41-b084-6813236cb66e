#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
УЛЬТИМАТИВНОЕ РЕШЕНИЕ ПРОБЛЕМЫ ПЕРЕКЛЮЧЕНИЯ РАСКЛАДКИ
Самый агрессивный подход с множественными методами
"""

import ctypes
import time
import subprocess
import sys
import os
from ctypes import wintypes

def safe_print(text):
    """Безопасный вывод с обработкой Unicode ошибок"""
    try:
        print(text)
    except UnicodeEncodeError:
        ascii_text = text.encode('ascii', 'ignore').decode('ascii')
        print(ascii_text)

def get_current_layout():
    """Получить текущую раскладку клавиатуры"""
    try:
        user32 = ctypes.windll.user32
        hwnd = user32.GetForegroundWindow()
        thread_id = user32.GetWindowThreadProcessId(hwnd, None)
        hkl = user32.GetKeyboardLayout(thread_id)
        layout_id = hkl & 0xFFFF
        return layout_id
    except:
        return None

def method_1_basic_switch():
    """Метод 1: Базовое переключение"""
    try:
        safe_print("🔄 Метод 1: Базовое переключение...")
        user32 = ctypes.windll.user32
        hkl_russian = user32.LoadKeyboardLayoutW("00000419", 0x00000001)
        if hkl_russian:
            result = user32.ActivateKeyboardLayout(hkl_russian, 0x00000008)
            if result:
                safe_print("✅ Метод 1: Успех")
                return True
        safe_print("❌ Метод 1: Неудача")
        return False
    except Exception as e:
        safe_print(f"❌ Метод 1: Ошибка {e}")
        return False

def method_2_with_flags():
    """Метод 2: С различными флагами"""
    try:
        safe_print("🔄 Метод 2: С флагами...")
        user32 = ctypes.windll.user32
        
        # Пробуем разные флаги
        flags = [0x00000008, 0x00000001, 0x00000000, 0x00000100]
        
        for flag in flags:
            hkl_russian = user32.LoadKeyboardLayoutW("00000419", 0x00000001)
            if hkl_russian:
                result = user32.ActivateKeyboardLayout(hkl_russian, flag)
                if result:
                    time.sleep(0.1)
                    current = get_current_layout()
                    if current == 0x0419:
                        safe_print(f"✅ Метод 2: Успех с флагом {hex(flag)}")
                        return True
        
        safe_print("❌ Метод 2: Неудача")
        return False
    except Exception as e:
        safe_print(f"❌ Метод 2: Ошибка {e}")
        return False

def method_3_send_keys():
    """Метод 3: Эмуляция нажатия Alt+Shift"""
    try:
        safe_print("🔄 Метод 3: Эмуляция Alt+Shift...")
        user32 = ctypes.windll.user32
        
        # Нажимаем Alt
        user32.keybd_event(0x12, 0, 0, 0)  # Alt down
        time.sleep(0.05)
        # Нажимаем Shift
        user32.keybd_event(0x10, 0, 0, 0)  # Shift down
        time.sleep(0.05)
        # Отпускаем Shift
        user32.keybd_event(0x10, 0, 2, 0)  # Shift up
        time.sleep(0.05)
        # Отпускаем Alt
        user32.keybd_event(0x12, 0, 2, 0)  # Alt up
        
        time.sleep(0.5)
        current = get_current_layout()
        if current == 0x0419:
            safe_print("✅ Метод 3: Успех")
            return True
        
        safe_print("❌ Метод 3: Неудача")
        return False
    except Exception as e:
        safe_print(f"❌ Метод 3: Ошибка {e}")
        return False

def method_4_window_message():
    """Метод 4: Отправка сообщения окну"""
    try:
        safe_print("🔄 Метод 4: Сообщение окну...")
        user32 = ctypes.windll.user32
        
        hwnd = user32.GetForegroundWindow()
        if hwnd:
            # WM_INPUTLANGCHANGEREQUEST
            user32.SendMessageW(hwnd, 0x0050, 0, 0x4190419)
            time.sleep(0.2)
            
            current = get_current_layout()
            if current == 0x0419:
                safe_print("✅ Метод 4: Успех")
                return True
        
        safe_print("❌ Метод 4: Неудача")
        return False
    except Exception as e:
        safe_print(f"❌ Метод 4: Ошибка {e}")
        return False

def method_5_registry_approach():
    """Метод 5: Через реестр"""
    try:
        safe_print("🔄 Метод 5: Через реестр...")
        import winreg
        
        # Открываем ключ реестра
        key_path = r"SYSTEM\CurrentControlSet\Control\Keyboard Layout\DosKeybCodes"
        try:
            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path, 0, winreg.KEY_READ)
            winreg.CloseKey(key)
            safe_print("✅ Метод 5: Реестр доступен")
        except:
            safe_print("❌ Метод 5: Реестр недоступен")
            
        return False
    except Exception as e:
        safe_print(f"❌ Метод 5: Ошибка {e}")
        return False

def method_6_powershell_extreme():
    """Метод 6: Экстремальный PowerShell"""
    try:
        safe_print("🔄 Метод 6: Экстремальный PowerShell...")
        
        ps_script = '''
        Add-Type -AssemblyName System.Windows.Forms
        [System.Windows.Forms.InputLanguage]::CurrentInputLanguage = [System.Windows.Forms.InputLanguage]::FromCulture([System.Globalization.CultureInfo]::GetCultureInfo("ru-RU"))
        '''
        
        result = subprocess.run([
            "powershell", "-Command", ps_script
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            time.sleep(0.5)
            current = get_current_layout()
            if current == 0x0419:
                safe_print("✅ Метод 6: Успех")
                return True
        
        safe_print("❌ Метод 6: Неудача")
        return False
    except Exception as e:
        safe_print(f"❌ Метод 6: Ошибка {e}")
        return False

def ultimate_keyboard_switch():
    """Ультимативное переключение раскладки - все методы подряд"""
    safe_print("🚀 УЛЬТИМАТИВНОЕ ПЕРЕКЛЮЧЕНИЕ РАСКЛАДКИ КЛАВИАТУРЫ")
    safe_print("=" * 60)
    
    initial_layout = get_current_layout()
    safe_print(f"Начальная раскладка: {hex(initial_layout) if initial_layout else 'ОШИБКА'}")
    
    if initial_layout == 0x0419:
        safe_print("✅ Раскладка уже русская!")
        return True
    
    # Пробуем все методы по очереди
    methods = [
        method_1_basic_switch,
        method_2_with_flags,
        method_3_send_keys,
        method_4_window_message,
        method_5_registry_approach,
        method_6_powershell_extreme
    ]
    
    for i, method in enumerate(methods, 1):
        safe_print(f"\n📋 Попытка {i}/6...")
        try:
            if method():
                final_layout = get_current_layout()
                if final_layout == 0x0419:
                    safe_print(f"🎉 УСПЕХ! Метод {i} сработал!")
                    safe_print(f"Финальная раскладка: {hex(final_layout)}")
                    return True
        except Exception as e:
            safe_print(f"❌ Метод {i}: Критическая ошибка {e}")
        
        time.sleep(0.5)  # Пауза между методами
    
    # Если ничего не сработало
    final_layout = get_current_layout()
    safe_print("\n" + "=" * 60)
    safe_print("💥 ВСЕ МЕТОДЫ НЕ СРАБОТАЛИ!")
    safe_print(f"Начальная раскладка: {hex(initial_layout) if initial_layout else 'ОШИБКА'}")
    safe_print(f"Финальная раскладка: {hex(final_layout) if final_layout else 'ОШИБКА'}")
    safe_print("\n🎯 ПОЛЬЗОВАТЕЛЮ НУЖНО НАЖАТЬ Alt+Shift ВРУЧНУЮ")
    safe_print("=" * 60)
    
    return False

def main():
    """Главная функция"""
    ultimate_keyboard_switch()

if __name__ == "__main__":
    main()
