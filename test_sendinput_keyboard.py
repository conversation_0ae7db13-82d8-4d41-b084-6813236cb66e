#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import ctypes
import time

def test_sendinput_keyboard_switching():
    """Тестирование переключения раскладки через SendInput API"""
    
    print("🔧 Тестирование переключения раскладки через SendInput API...")
    print("=" * 70)
    
    user32 = ctypes.windll.user32
    
    # Метод SendInput для имитации Ctrl+Shift
    print("\n1️⃣ Тестирование метода SendInput (Ctrl+Shift)...")
    try:
        # Структура INPUT для SendInput
        class INPUT(ctypes.Structure):
            _fields_ = [("type", ctypes.c_ulong),
                       ("ki", ctypes.c_ulong * 6)]
        
        # Константы
        INPUT_KEYBOARD = 1
        KEYEVENTF_KEYUP = 2
        VK_CONTROL = 0x11
        VK_SHIFT = 0x10
        
        # Создаем события клавиатуры
        inputs = (INPUT * 4)()
        
        # Ctrl down
        inputs[0].type = INPUT_KEYBOARD
        inputs[0].ki[0] = VK_CONTROL  # virtual key code
        inputs[0].ki[1] = 0  # scan code
        inputs[0].ki[2] = 0  # flags
        
        # Shift down
        inputs[1].type = INPUT_KEYBOARD
        inputs[1].ki[0] = VK_SHIFT
        inputs[1].ki[1] = 0
        inputs[1].ki[2] = 0
        
        # Shift up
        inputs[2].type = INPUT_KEYBOARD
        inputs[2].ki[0] = VK_SHIFT
        inputs[2].ki[1] = 0
        inputs[2].ki[2] = KEYEVENTF_KEYUP
        
        # Ctrl up
        inputs[3].type = INPUT_KEYBOARD
        inputs[3].ki[0] = VK_CONTROL
        inputs[3].ki[1] = 0
        inputs[3].ki[2] = KEYEVENTF_KEYUP
        
        # Отправляем события
        result = user32.SendInput(4, inputs, ctypes.sizeof(INPUT))
        print(f"   ✅ SendInput Ctrl+Shift: Result={result} (ожидается 4)")
        
        if result == 4:
            print("   ✅ Команда отправлена успешно")
            time.sleep(3)
            print("   📝 Попробуйте печатать - раскладка должна была переключиться")
        else:
            print("   ❌ Команда не была отправлена полностью")
            
    except Exception as e:
        print(f"   ❌ Ошибка в методе SendInput: {e}")
    
    time.sleep(2)
    
    # Получение текущей раскладки
    print("\n2️⃣ Получение информации о текущей раскладке...")
    try:
        # Получаем текущую раскладку
        hwnd = user32.GetForegroundWindow()
        thread_id = user32.GetWindowThreadProcessId(hwnd, None)
        hkl = user32.GetKeyboardLayout(thread_id)
        
        print(f"   📋 Текущая раскладка: {hex(hkl)} ({hkl})")
        
        # Определяем язык
        lang_id = hkl & 0xFFFF
        if lang_id == 0x0409:
            print("   🇺🇸 Текущая раскладка: Английская")
        elif lang_id == 0x0419:
            print("   🇷🇺 Текущая раскладка: Русская")
        else:
            print(f"   🌐 Текущая раскладка: Неизвестная (ID: {hex(lang_id)})")
            
    except Exception as e:
        print(f"   ❌ Ошибка при получении раскладки: {e}")
    
    # Тестирование LoadKeyboardLayout
    print("\n3️⃣ Тестирование LoadKeyboardLayout...")
    try:
        # Загружаем русскую раскладку
        hkl = user32.LoadKeyboardLayoutW("00000419", 0x00000001)  # KLF_ACTIVATE
        if hkl:
            print(f"   ✅ Русская раскладка загружена: HKL={hkl}")
            # Активируем раскладку для текущего потока
            result = user32.ActivateKeyboardLayout(hkl, 0)
            if result:
                print(f"   ✅ Раскладка активирована: Result={result}")
                time.sleep(3)
                print("   📝 Попробуйте печатать - должна быть русская раскладка")
            else:
                print("   ❌ Не удалось активировать раскладку")
        else:
            print("   ❌ Не удалось загрузить русскую раскладку")
            
    except Exception as e:
        print(f"   ❌ Ошибка в LoadKeyboardLayout: {e}")
    
    print("\n" + "=" * 70)
    print("🏁 Тестирование завершено!")
    print("\n💡 Инструкции:")
    print("   - Проверьте, изменилась ли раскладка клавиатуры во время тестирования")
    print("   - Попробуйте печатать русские буквы для проверки")
    print("   - Если раскладка не переключилась, возможно нужны права администратора")

if __name__ == "__main__":
    test_sendinput_keyboard_switching()
    input("\nНажмите Enter для выхода...")
