"""
Расширенная система отчётов без зависимости от matplotlib
Профессиональные отчёты для ресторанного бизнеса
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
import json
import os
from gui.styles import ModernStyles

class AdvancedReportsManager:
    """Менеджер расширенных отчётов"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.report_panel = None
        
        # Цвета для отчётов
        self.colors = {
            'primary': '#3b82f6',
            'success': '#10b981', 
            'warning': '#f59e0b',
            'danger': '#ef4444',
            'info': '#06b6d4',
            'secondary': '#6b7280'
        }
    
    def create_window(self):
        """Создать окно расширенных отчётов"""
        try:
            from utils.window_utils import create_centered_dialog
            self.window = create_centered_dialog(
                self.parent,
                "📊 Расширенная Аналитика",
                width=1500,
                height=950,
                resizable=True
            )
            self.window.configure(bg='white')
        except ImportError:
            self.window = tk.Toplevel(self.parent)
            self.window.title("📊 Расширенная Аналитика")
            self.window.geometry("1500x950")
            self.window.configure(bg='white')
            self.window.resizable(True, True)

            # Центрировать окно
            self.window.update_idletasks()
            x = (self.window.winfo_screenwidth() // 2) - (1500 // 2)
            y = (self.window.winfo_screenheight() // 2) - (950 // 2)
            self.window.geometry(f"1500x950+{x}+{y}")

        self.create_interface()
    
    def create_interface(self):
        """Создать интерфейс"""
        # Заголовок
        header_frame = tk.Frame(self.window, bg='#1a202c', height=70)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="📊 Расширенная Бизнес-Аналитика",
                font=('Cambria', 20, 'bold italic'), bg='#1a202c',
                fg='white').pack(side='left', padx=30, pady=20)

        # Кнопки экспорта
        btn_frame = tk.Frame(header_frame, bg='#1a202c')
        btn_frame.pack(side='right', padx=30, pady=15)

        tk.Button(btn_frame, text="📤 Экспорт PDF", command=self.export_pdf,
                 bg='#e53e3e', fg='white', font=('Cambria', 12, 'bold italic'),
                 relief='flat', padx=20, pady=8).pack(side='left', padx=10)

        tk.Button(btn_frame, text="📊 Экспорт Excel", command=self.export_excel,
                 bg='#38a169', fg='white', font=('Cambria', 12, 'bold italic'),
                 relief='flat', padx=20, pady=8).pack(side='left', padx=10)

        tk.Button(btn_frame, text="🔄 Обновить", command=self.refresh_data,
                 bg='#3182ce', fg='white', font=('Cambria', 12, 'bold italic'),
                 relief='flat', padx=20, pady=8).pack(side='left', padx=10)

        # Основной контент
        main_frame = tk.Frame(self.window, bg='white')
        main_frame.pack(fill='both', expand=True)

        # Боковая панель с отчётами
        sidebar = tk.Frame(main_frame, bg='#2d3748', width=280)
        sidebar.pack(side='left', fill='y')
        sidebar.pack_propagate(False)

        # Панель отчётов
        self.report_panel = tk.Frame(main_frame, bg='white')
        self.report_panel.pack(side='right', fill='both', expand=True)

        self.create_sidebar(sidebar)
        self.show_dashboard()
    
    def create_sidebar(self, parent):
        """Создать боковую панель"""
        # Заголовок панели
        title_frame = tk.Frame(parent, bg='#1a202c', height=50)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)

        tk.Label(title_frame, text="📊 Аналитические Отчёты",
                font=('Cambria', 16, 'bold italic'), bg='#1a202c', fg='white').pack(pady=15)

        # Создать скроллируемую область
        canvas = tk.Canvas(parent, bg='#2d3748', highlightthickness=0)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='#2d3748')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Категории отчётов
        categories = {
            "📈 ПРОДАЖИ И ВЫРУЧКА": [
                ("📊 Динамика продаж", self.show_sales_dynamics_table),
                ("🏆 Топ блюда", self.show_top_dishes_table),
                ("⏰ Анализ по времени", self.show_time_analysis_table),
                ("📅 Сравнение периодов", self.show_period_comparison_table)
            ],
            "💰 ФИНАНСОВАЯ АНАЛИТИКА": [
                ("💹 Анализ прибыльности", self.show_profitability_table),
                ("📊 Структура доходов", self.show_revenue_structure_table),
                ("📉 Анализ затрат", self.show_cost_analysis_table),
                ("🎯 ROI по блюдам", self.show_dish_roi_table)
            ],
            "📦 СКЛАДСКАЯ АНАЛИТИКА": [
                ("🔄 Оборачиваемость", self.show_inventory_turnover_table),
                ("📊 ABC анализ", self.show_abc_analysis_table),
                ("🔮 Прогноз потребности", self.show_demand_forecast_table),
                ("🏢 Анализ поставщиков", self.show_supplier_analysis_table)
            ],
            "👥 HR АНАЛИТИКА": [
                ("⚡ Производительность", self.show_staff_performance_table),
                ("🕐 Анализ смен", self.show_shift_analysis_table),
                ("💰 Затраты на персонал", self.show_staff_costs_table)
            ]
        }

        for category, reports in categories.items():
            # Заголовок категории
            cat_frame = tk.Frame(scrollable_frame, bg='#4a5568', relief='flat', bd=1)
            cat_frame.pack(fill='x', padx=15, pady=(15, 5))

            tk.Label(cat_frame, text=category, font=('Cambria', 12, 'bold italic'),
                    bg='#4a5568', fg='white').pack(pady=10)

            # Отчёты в категории
            for report_name, report_func in reports:
                btn = tk.Button(scrollable_frame, text=report_name, command=report_func,
                               bg='#2d3748', fg='white', font=('Cambria', 11, 'bold'),
                               relief='flat', anchor='w', padx=25, pady=10)
                btn.pack(fill='x', padx=15, pady=2)

                # Эффект наведения
                def on_enter(e, button=btn):
                    button.config(bg='#4a5568')
                def on_leave(e, button=btn):
                    button.config(bg='#2d3748')

                btn.bind("<Enter>", on_enter)
                btn.bind("<Leave>", on_leave)

        # Упаковать canvas и scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def clear_report_panel(self):
        """Очистить панель отчётов"""
        for widget in self.report_panel.winfo_children():
            widget.destroy()
    
    def create_table_report(self, title, columns, data, description=""):
        """Создать отчёт с таблицей"""
        self.clear_report_panel()

        # Заголовок
        header_frame = tk.Frame(self.report_panel, bg='#f7fafc')
        header_frame.pack(fill='x', padx=0, pady=0)

        tk.Label(header_frame, text=title,
                font=('Cambria', 18, 'bold italic'), bg='#f7fafc').pack(pady=20)

        if description:
            tk.Label(header_frame, text=description,
                    font=('Cambria', 12), bg='#f7fafc', wraplength=800).pack(pady=(0, 15))

        # Контейнер для таблицы
        table_frame = tk.Frame(self.report_panel, bg='white')
        table_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # Создать таблицу
        tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # Настройка колонок
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120, anchor='center')

        # Заполнить данными
        for row in data:
            tree.insert('', 'end', values=row)

        # Скроллбары
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=tree.xview)
        tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Упаковать элементы
        tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')

        return tree
    
    def show_dashboard(self):
        """Показать профессиональный дашборд"""
        self.clear_report_panel()

        # Заголовок
        header_frame = tk.Frame(self.report_panel, bg='#f7fafc')
        header_frame.pack(fill='x')

        tk.Label(header_frame, text="📊 Исполнительная Панель Управления",
                font=('Cambria', 20, 'bold italic'), bg='#f7fafc').pack(pady=25)

        # Основные метрики
        metrics_frame = tk.Frame(self.report_panel, bg='white')
        metrics_frame.pack(fill='x', padx=40, pady=20)

        # Карточки метрик
        metrics = [
            ("💰 Выручка за месяц", "1,568,400₽", "+8.2%", "#27ae60"),
            ("📊 Количество заказов", "4,127", "+12.5%", "#3498db"),
            ("💳 Средний чек", "380₽", "+3.1%", "#9b59b6"),
            ("📈 Чистая прибыль", "167,360₽", "+115%", "#e67e22")
        ]

        for i, (title, value, change, color) in enumerate(metrics):
            card = tk.Frame(metrics_frame, bg=color, relief='flat', bd=0)
            card.grid(row=0, column=i, padx=15, pady=10, sticky='ew')

            tk.Label(card, text=title, font=('Cambria', 12, 'bold italic'),
                    bg=color, fg='white').pack(pady=(15, 5))

            tk.Label(card, text=value, font=('Cambria', 22, 'bold'),
                    bg=color, fg='white').pack(pady=(0, 5))

            tk.Label(card, text=change, font=('Cambria', 11, 'bold'),
                    bg=color, fg='white').pack(pady=(0, 15))

        # Настройка сетки
        for i in range(4):
            metrics_frame.grid_columnconfigure(i, weight=1)

        # Создать notebook для дашборда
        dashboard_notebook = ttk.Notebook(self.report_panel)
        dashboard_notebook.pack(fill='both', expand=True, padx=40, pady=30)

        # Вкладка "Сегодня"
        today_frame = tk.Frame(dashboard_notebook, bg='white')
        dashboard_notebook.add(today_frame, text="📅 Сегодня")
        self.create_today_dashboard(today_frame)

        # Вкладка "Тренды"
        trends_frame = tk.Frame(dashboard_notebook, bg='white')
        dashboard_notebook.add(trends_frame, text="📈 Тренды")
        self.create_trends_dashboard(trends_frame)

        # Вкладка "Алерты"
        alerts_frame = tk.Frame(dashboard_notebook, bg='white')
        dashboard_notebook.add(alerts_frame, text="🚨 Алерты")
        self.create_alerts_dashboard(alerts_frame)

    def create_today_dashboard(self, parent):
        """Создать дашборд сегодняшнего дня"""
        # Заголовок
        tk.Label(parent, text="📅 Операционная сводка на сегодня",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Таблица сегодняшних показателей
        today_frame = tk.Frame(parent, bg='white')
        today_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Создать таблицу
        today_columns = ('Показатель', 'Текущее значение', 'План на день', 'Выполнение %', 'Статус', 'Прогноз')
        today_tree = ttk.Treeview(today_frame, columns=today_columns, show='headings', height=12)

        # Настройка колонок
        column_widths = {'Показатель': 180, 'Текущее значение': 120, 'План на день': 100, 'Выполнение %': 100, 'Статус': 100, 'Прогноз': 120}
        for col in today_columns:
            today_tree.heading(col, text=col)
            today_tree.column(col, width=column_widths[col], anchor='center')

        # Данные сегодняшнего дня
        today_data = [
            ('💰 Выручка', '18,400₽', '22,000₽', '83.6%', '🟡 В процессе', '21,500₽'),
            ('📊 Заказы', '48', '60', '80.0%', '🟡 В процессе', '58'),
            ('👥 Посетители', '156', '180', '86.7%', '🟢 Хорошо', '175'),
            ('🍽️ Блюд приготовлено', '124', '150', '82.7%', '🟡 В процессе', '145'),
            ('⏰ Среднее время обслуживания', '42 мин', '≤45 мин', '107%', '🟢 Отлично', '43 мин'),
            ('🔄 Оборачиваемость столов', '2.8', '3.0', '93.3%', '🟡 Близко к цели', '2.9'),
            ('📋 Жалобы', '0', '≤2', '100%', '🟢 Отлично', '1'),
            ('⭐ Рейтинг сервиса', '4.8/5', '≥4.5', '107%', '🟢 Отлично', '4.7/5'),
            ('👨‍🍳 Персонал на смене', '12', '12', '100%', '🟢 Полная смена', '12'),
            ('🔧 Работающее оборудование', '12/12', '12/12', '100%', '🟢 Все исправно', '12/12'),
            ('📦 Критические остатки', '2', '≤3', '133%', '🟢 Норма', '2'),
            ('💳 Средний чек', '383₽', '367₽', '104%', '🟢 Выше плана', '371₽')
        ]

        for data in today_data:
            today_tree.insert('', 'end', values=data)

        # Скроллбар
        today_scrollbar = ttk.Scrollbar(today_frame, orient='vertical', command=today_tree.yview)
        today_tree.configure(yscrollcommand=today_scrollbar.set)

        today_tree.pack(side='left', fill='both', expand=True)
        today_scrollbar.pack(side='right', fill='y')

    def create_trends_dashboard(self, parent):
        """Создать дашборд трендов"""
        # Заголовок
        tk.Label(parent, text="📈 Анализ трендов за последние 30 дней",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Таблица трендов
        trends_frame = tk.Frame(parent, bg='white')
        trends_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Создать таблицу
        trends_columns = ('Показатель', 'Текущий период', 'Предыдущий период', 'Изменение', 'Тренд', 'Прогноз')
        trends_tree = ttk.Treeview(trends_frame, columns=trends_columns, show='headings', height=12)

        # Настройка колонок
        column_widths = {'Показатель': 180, 'Текущий период': 120, 'Предыдущий период': 130, 'Изменение': 100, 'Тренд': 80, 'Прогноз': 120}
        for col in trends_columns:
            trends_tree.heading(col, text=col)
            trends_tree.column(col, width=column_widths[col], anchor='center')

        # Данные трендов
        trends_data = [
            ('💰 Выручка', '628,600₽', '583,600₽', '+7.7%', '📈', 'Рост'),
            ('📊 Заказы', '1,632', '1,506', '+8.4%', '📈', 'Рост'),
            ('👥 Посетители', '4,896', '4,518', '+8.4%', '📈', 'Рост'),
            ('💳 Средний чек', '385₽', '387₽', '-0.5%', '📉', 'Стабильно'),
            ('🍽️ Топ блюдо (Стейк)', '156 заказов', '142 заказов', '+9.9%', '📈', 'Рост'),
            ('⏰ Время обслуживания', '45 мин', '48 мин', '-6.3%', '📈', 'Улучшение'),
            ('📋 Жалобы', '8', '12', '-33.3%', '📈', 'Улучшение'),
            ('⭐ Рейтинг сервиса', '4.6/5', '4.4/5', '+4.5%', '📈', 'Рост'),
            ('👨‍🍳 Производительность', '48 блюд/смена', '46 блюд/смена', '+4.3%', '📈', 'Рост'),
            ('🔧 Простои оборудования', '8 ч', '15 ч', '-46.7%', '📈', 'Улучшение'),
            ('📦 Оборачиваемость склада', '2.3 раза', '2.1 раза', '+9.5%', '📈', 'Рост'),
            ('💰 Чистая прибыль', '67,360₽', '31,280₽', '+115%', '🚀', 'Взрывной рост')
        ]

        for data in trends_data:
            trends_tree.insert('', 'end', values=data)

        # Скроллбар
        trends_scrollbar = ttk.Scrollbar(trends_frame, orient='vertical', command=trends_tree.yview)
        trends_tree.configure(yscrollcommand=trends_scrollbar.set)

        trends_tree.pack(side='left', fill='both', expand=True)
        trends_scrollbar.pack(side='right', fill='y')

    def create_alerts_dashboard(self, parent):
        """Создать дашборд алертов"""
        # Заголовок
        tk.Label(parent, text="🚨 Системные уведомления и алерты",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Таблица алертов
        alerts_frame = tk.Frame(parent, bg='white')
        alerts_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Создать таблицу
        alerts_columns = ('Приоритет', 'Тип', 'Сообщение', 'Время', 'Статус', 'Действие')
        alerts_tree = ttk.Treeview(alerts_frame, columns=alerts_columns, show='headings', height=12)

        # Настройка колонок
        column_widths = {'Приоритет': 80, 'Тип': 120, 'Сообщение': 300, 'Время': 120, 'Статус': 100, 'Действие': 150}
        for col in alerts_columns:
            alerts_tree.heading(col, text=col)
            alerts_tree.column(col, width=column_widths[col], anchor='center')

        # Данные алертов
        alerts_data = [
            ('🔴', 'Склад', 'Критический остаток: Перец чёрный молотый (0.3 кг)', '10:30', 'Активно', 'Заказать у поставщика'),
            ('🟡', 'Склад', 'Низкий остаток: Сыр твёрдый (4.8 кг)', '09:15', 'Активно', 'Планировать закупку'),
            ('🟢', 'Продажи', 'Превышен план продаж на 8.2%', '08:00', 'Информация', 'Отметить успех'),
            ('🟡', 'Оборудование', 'Плановое ТО: Духовой шкаф №1 через 3 дня', '07:45', 'Запланировано', 'Связаться с сервисом'),
            ('🟢', 'Персонал', 'Отличная работа: Петрова С.В. (рейтинг 5.0)', '16:20', 'Информация', 'Поощрить сотрудника'),
            ('🟡', 'Финансы', 'Превышение бюджета на маркетинг на 18%', '14:30', 'Контроль', 'Пересмотреть расходы'),
            ('🟢', 'Качество', 'Аудит качества пройден на 95%', '12:00', 'Завершено', 'Архивировать'),
            ('🔴', 'Безопасность', 'Неудачная попытка входа в систему', '11:45', 'Расследование', 'Проверить логи'),
            ('🟡', 'Клиенты', 'Жалоба на обслуживание: стол №12', '15:30', 'В работе', 'Связаться с клиентом'),
            ('🟢', 'Система', 'Резервное копирование выполнено успешно', '02:00', 'Завершено', 'Архивировать')
        ]

        for data in alerts_data:
            alerts_tree.insert('', 'end', values=data)

        # Скроллбар
        alerts_scrollbar = ttk.Scrollbar(alerts_frame, orient='vertical', command=alerts_tree.yview)
        alerts_tree.configure(yscrollcommand=alerts_scrollbar.set)

        alerts_tree.pack(side='left', fill='both', expand=True)
        alerts_scrollbar.pack(side='right', fill='y')
    
    # Новые методы отчётов с таблицами
    def show_sales_dynamics_table(self):
        """Показать динамику продаж в виде таблицы"""
        columns = ('Дата', 'День недели', 'Выручка', 'Заказов', 'Средний чек', 'Изменение', 'Тренд')
        data = [
            ('20.05.24', 'Понедельник', '45,000₽', '117', '385₽', '+5.2%', '📈'),
            ('21.05.24', 'Вторник', '52,000₽', '135', '385₽', '+15.6%', '📈'),
            ('22.05.24', 'Среда', '48,000₽', '125', '384₽', '-7.7%', '📉'),
            ('23.05.24', 'Четверг', '61,000₽', '158', '386₽', '+27.1%', '🚀'),
            ('24.05.24', 'Пятница', '58,000₽', '150', '387₽', '-4.9%', '📉'),
            ('25.05.24', 'Суббота', '67,000₽', '173', '387₽', '+15.5%', '📈'),
            ('26.05.24', 'Воскресенье', '72,000₽', '186', '387₽', '+7.5%', '📈'),
            ('27.05.24', 'Понедельник', '49,000₽', '127', '386₽', '-31.9%', '📉'),
            ('28.05.24', 'Вторник', '55,000₽', '142', '387₽', '+12.2%', '📈'),
            ('29.05.24', 'Среда', '53,000₽', '137', '387₽', '-3.6%', '📊'),
            ('30.05.24', 'Четверг', '64,000₽', '165', '388₽', '+20.8%', '📈'),
            ('31.05.24', 'Пятница', '69,000₽', '178', '388₽', '+7.8%', '📈')
        ]

        description = "Детальный анализ динамики продаж за последние 12 дней. Средний рост: +8.2% к предыдущему периоду."
        self.create_table_report("📈 Динамика продаж", columns, data, description)

    def show_top_dishes_table(self):
        """Показать топ блюд в виде таблицы"""
        columns = ('Место', 'Блюдо', 'Категория', 'Заказов', 'Выручка', 'Доля %', 'Маржа', 'Рейтинг')
        data = [
            ('🥇', 'Стейк Рибай', 'Горячие блюда', '156', '93,600₽', '6.0%', '65%', '⭐⭐⭐⭐⭐'),
            ('🥈', 'Борщ Украинский', 'Супы', '134', '26,800₽', '1.7%', '70%', '⭐⭐⭐⭐⭐'),
            ('🥉', 'Салат Цезарь', 'Салаты', '128', '38,400₽', '2.4%', '60%', '⭐⭐⭐⭐⭐'),
            ('4', 'Котлета по-Киевски', 'Горячие блюда', '98', '39,200₽', '2.5%', '68%', '⭐⭐⭐⭐'),
            ('5', 'Пельмени Домашние', 'Горячие блюда', '87', '26,100₽', '1.7%', '72%', '⭐⭐⭐⭐'),
            ('6', 'Тирамису', 'Десерты', '76', '22,800₽', '1.5%', '75%', '⭐⭐⭐⭐⭐'),
            ('7', 'Солянка Мясная', 'Супы', '72', '21,600₽', '1.4%', '68%', '⭐⭐⭐⭐'),
            ('8', 'Оливье', 'Салаты', '68', '13,600₽', '0.9%', '58%', '⭐⭐⭐⭐'),
            ('9', 'Шашлык из Свинины', 'Горячие блюда', '65', '32,500₽', '2.1%', '70%', '⭐⭐⭐⭐⭐'),
            ('10', 'Чизкейк', 'Десерты', '62', '18,600₽', '1.2%', '73%', '⭐⭐⭐⭐'),
            ('11', 'Греческий Салат', 'Салаты', '58', '17,400₽', '1.1%', '62%', '⭐⭐⭐⭐'),
            ('12', 'Лагман', 'Горячие блюда', '54', '21,600₽', '1.4%', '66%', '⭐⭐⭐⭐'),
            ('13', 'Морс Клюквенный', 'Напитки', '52', '5,200₽', '0.3%', '80%', '⭐⭐⭐'),
            ('14', 'Крем-суп Грибной', 'Супы', '48', '14,400₽', '0.9%', '69%', '⭐⭐⭐⭐'),
            ('15', 'Мороженое', 'Десерты', '45', '9,000₽', '0.6%', '78%', '⭐⭐⭐')
        ]

        description = "Рейтинг самых популярных блюд по количеству заказов и выручке за текущий месяц."
        self.create_table_report("🏆 Топ блюда", columns, data, description)

    def show_time_analysis_table(self):
        """Показать анализ по времени в виде таблицы"""
        columns = ('Время', 'Заказов', 'Выручка', 'Доля %', 'Средний чек', 'Загрузка зала', 'Персонал')
        data = [
            ('10:00-11:00', '45', '18,500₽', '1.2%', '411₽', '🟢 25%', '3 чел.'),
            ('11:00-12:00', '78', '31,200₽', '2.0%', '400₽', '🟡 45%', '4 чел.'),
            ('12:00-13:00', '156', '62,400₽', '4.0%', '400₽', '🔴 85%', '6 чел.'),
            ('13:00-14:00', '189', '75,600₽', '4.8%', '400₽', '🔴 95%', '6 чел.'),
            ('14:00-15:00', '134', '53,600₽', '3.4%', '400₽', '🟡 70%', '5 чел.'),
            ('15:00-16:00', '89', '35,600₽', '2.3%', '400₽', '🟡 50%', '4 чел.'),
            ('16:00-17:00', '67', '26,800₽', '1.7%', '400₽', '🟢 35%', '3 чел.'),
            ('17:00-18:00', '98', '39,200₽', '2.5%', '400₽', '🟡 55%', '4 чел.'),
            ('18:00-19:00', '145', '58,000₽', '3.7%', '400₽', '🔴 80%', '6 чел.'),
            ('19:00-20:00', '198', '79,200₽', '5.1%', '400₽', '🔴 100%', '7 чел.'),
            ('20:00-21:00', '167', '66,800₽', '4.3%', '400₽', '🔴 90%', '6 чел.'),
            ('21:00-22:00', '123', '49,200₽', '3.1%', '400₽', '🟡 65%', '5 чел.')
        ]

        description = "Анализ загрузки ресторана по часам. Пиковые часы: 13:00-14:00 и 19:00-20:00."
        self.create_table_report("⏰ Анализ по времени", columns, data, description)
    
    def show_top_dishes(self):
        """Показать топ блюд"""
        data = [
            "ТОП-10 ПОПУЛЯРНЫХ БЛЮД:",
            "",
            "1. Стейк рибай - 45 заказов (8,500₽)",
            "2. Борщ украинский - 38 заказов (3,800₽)",
            "3. Салат Цезарь - 35 заказов (2,800₽)",
            "4. Котлета по-киевски - 32 заказов (4,800₽)",
            "5. Пельмени домашние - 28 заказов (2,240₽)",
            "6. Суп-харчо - 25 заказов (2,000₽)",
            "7. Шашлык из баранины - 22 заказов (5,500₽)",
            "8. Оливье - 20 заказов (1,200₽)",
            "9. Блины с икрой - 18 заказов (3,600₽)",
            "10. Солянка мясная - 15 заказов (1,800₽)",
            "",
            "📊 СТАТИСТИКА:",
            "• Общая выручка топ-10: 36,240₽",
            "• Доля в общих продажах: 63%",
            "• Средний чек топ блюда: 189₽",
            "",
            "🎯 ВЫВОДЫ:",
            "• Мясные блюда лидируют по популярности",
            "• Супы показывают стабильный спрос",
            "• Премиальные позиции приносят основную прибыль"
        ]
        self.show_text_report("🍽️ Топ Блюд", data)
    
    def show_time_analysis(self):
        """Показать анализ по времени"""
        data = [
            "АНАЛИЗ ПРОДАЖ ПО ВРЕМЕНИ:",
            "",
            "📅 ПО ДНЯМ НЕДЕЛИ:",
            "Понедельник - 35,000₽ (15%)",
            "Вторник - 42,000₽ (18%)",
            "Среда - 38,000₽ (16%)",
            "Четверг - 45,000₽ (19%)",
            "Пятница - 52,000₽ (22%)",
            "Суббота - 68,000₽ (29%)",
            "Воскресенье - 58,000₽ (25%)",
            "",
            "⏰ ПО ЧАСАМ ДНЯ:",
            "10:00-12:00 - 8,500₽ (завтрак)",
            "12:00-15:00 - 25,000₽ (обед)",
            "15:00-18:00 - 12,000₽ (полдник)",
            "18:00-22:00 - 35,000₽ (ужин)",
            "22:00-24:00 - 8,500₽ (поздний ужин)",
            "",
            "🎯 ПИКОВЫЕ ЧАСЫ:",
            "• 13:00-14:00 - максимальная загрузка",
            "• 19:00-20:00 - вечерний пик",
            "• Выходные +40% к будням"
        ]
        self.show_text_report("⏰ Анализ по Времени", data)
    
    def show_period_comparison(self):
        """Показать сравнение периодов"""
        data = [
            "СРАВНЕНИЕ ПЕРИОДОВ (2023 vs 2024):",
            "",
            "📊 ЯНВАРЬ:",
            "2023: 480,000₽",
            "2024: 520,000₽ (+8.3%)",
            "",
            "📊 ФЕВРАЛЬ:",
            "2023: 450,000₽", 
            "2024: 485,000₽ (+7.8%)",
            "",
            "📊 МАРТ:",
            "2023: 510,000₽",
            "2024: 545,000₽ (+6.9%)",
            "",
            "📈 ОБЩИЕ ТРЕНДЫ:",
            "• Стабильный рост 7-8% год к году",
            "• Улучшение показателей во всех месяцах",
            "• Рост среднего чека на 12%",
            "• Увеличение количества клиентов на 15%",
            "",
            "🎯 ПРОГНОЗ:",
            "• Ожидаемый рост в 2024: +10-12%",
            "• Целевая выручка: 6,500,000₽",
            "• Потенциал роста: высокий"
        ]
        self.show_text_report("📊 Сравнение Периодов", data)
    
    def show_profitability(self):
        """Показать анализ прибыльности"""
        data = [
            "АНАЛИЗ ПРИБЫЛЬНОСТИ:",
            "",
            "💰 ПО КАТЕГОРИЯМ БЛЮД:",
            "Горячие блюда - маржа 65%",
            "Супы - маржа 70%", 
            "Салаты - маржа 60%",
            "Напитки - маржа 80%",
            "Десерты - маржа 75%",
            "",
            "📊 РЕНТАБЕЛЬНОСТЬ:",
            "• Общая маржинальность: 68%",
            "• Валовая прибыль: 340,000₽",
            "• Чистая прибыль: 85,000₽",
            "• ROI: 25%",
            "",
            "🎯 САМЫЕ ПРИБЫЛЬНЫЕ:",
            "1. Напитки (80% маржа)",
            "2. Десерты (75% маржа)",
            "3. Супы (70% маржа)",
            "",
            "⚠️ ТРЕБУЮТ ВНИМАНИЯ:",
            "• Салаты - низкая маржа",
            "• Необходимо пересмотреть цены",
            "• Оптимизировать состав блюд"
        ]
        self.show_text_report("💰 Анализ Прибыльности", data)
    
    def show_period_comparison_table(self):
        """Показать сравнение периодов в виде таблицы"""
        columns = ('Период', 'Выручка', 'Заказов', 'Средний чек', 'Прибыль', 'Изменение', 'Тренд')
        data = [
            ('Май 2023', '580,000₽', '1,506', '385₽', '58,000₽', '-', '📊'),
            ('Май 2024', '628,600₽', '1,632', '385₽', '67,360₽', '+8.4%', '📈'),
            ('Апр 2024', '595,200₽', '1,547', '385₽', '59,520₽', '+5.6%', '📈'),
            ('Мар 2024', '612,800₽', '1,592', '385₽', '61,280₽', '+3.0%', '📈'),
            ('Фев 2024', '485,000₽', '1,260', '385₽', '48,500₽', '+7.8%', '📈'),
            ('Янв 2024', '520,000₽', '1,351', '385₽', '52,000₽', '+8.3%', '📈'),
            ('Дек 2023', '650,000₽', '1,688', '385₽', '65,000₽', '+12.1%', '📈'),
            ('Ноя 2023', '590,000₽', '1,532', '385₽', '59,000₽', '+1.7%', '📊'),
            ('Окт 2023', '605,000₽', '1,571', '385₽', '60,500₽', '+4.3%', '📈'),
            ('Сен 2023', '575,000₽', '1,494', '385₽', '57,500₽', '-0.9%', '📉')
        ]

        description = "Сравнение ключевых показателей по месяцам. Стабильный рост выручки и прибыли."
        self.create_table_report("📅 Сравнение периодов", columns, data, description)

    def show_profitability_table(self):
        """Показать анализ прибыльности в виде таблицы"""
        columns = ('Категория', 'Выручка', 'Себестоимость', 'Валовая прибыль', 'Маржа %', 'ROI %', 'Статус')
        data = [
            ('Горячие блюда', '485,600₽', '169,960₽', '315,640₽', '65%', '186%', '🟢 Отлично'),
            ('Супы', '124,800₽', '37,440₽', '87,360₽', '70%', '233%', '🟢 Отлично'),
            ('Салаты', '98,400₽', '39,360₽', '59,040₽', '60%', '150%', '🟡 Хорошо'),
            ('Напитки', '78,200₽', '15,640₽', '62,560₽', '80%', '400%', '🟢 Отлично'),
            ('Десерты', '65,800₽', '16,450₽', '49,350₽', '75%', '300%', '🟢 Отлично'),
            ('Закуски', '32,400₽', '12,960₽', '19,440₽', '60%', '150%', '🟡 Хорошо'),
            ('Алкоголь', '45,600₽', '18,240₽', '27,360₽', '60%', '150%', '🟡 Хорошо')
        ]

        description = "Анализ прибыльности по категориям меню. Напитки и десерты показывают наивысшую маржинальность."
        self.create_table_report("💹 Анализ прибыльности", columns, data, description)

    def show_revenue_structure_table(self):
        """Показать структуру доходов в виде таблицы"""
        columns = ('Источник дохода', 'Сумма', 'Доля %', 'Прошлый месяц', 'Изменение', 'Тренд')
        data = [
            ('Зал ресторана', '485,600₽', '77.3%', '448,200₽', '+8.3%', '📈'),
            ('Доставка', '89,400₽', '14.2%', '82,100₽', '+8.9%', '📈'),
            ('Takeaway', '34,800₽', '5.5%', '31,200₽', '+11.5%', '📈'),
            ('Банкеты', '18,800₽', '3.0%', '22,100₽', '-14.9%', '📉')
        ]

        description = "Структура доходов по каналам продаж. Основной доход приносит зал ресторана."
        self.create_table_report("📊 Структура доходов", columns, data, description)

    def show_cost_analysis_table(self):
        """Показать анализ затрат в виде таблицы"""
        columns = ('Статья расходов', 'Сумма', 'Доля %', 'Бюджет', 'Отклонение', 'Статус')
        data = [
            ('Продукты питания', '312,400₽', '49.7%', '320,000₽', '-7,600₽', '🟢 В рамках'),
            ('Заработная плата', '156,200₽', '24.8%', '160,000₽', '-3,800₽', '🟢 В рамках'),
            ('Аренда помещения', '45,000₽', '7.2%', '45,000₽', '0₽', '🟢 По плану'),
            ('Коммунальные услуги', '18,500₽', '2.9%', '20,000₽', '-1,500₽', '🟢 Экономия'),
            ('Маркетинг', '8,200₽', '1.3%', '10,000₽', '-1,800₽', '🟢 Экономия'),
            ('Обслуживание оборудования', '4,100₽', '0.7%', '5,000₽', '-900₽', '🟢 Экономия')
        ]

        description = "Анализ структуры затрат. Все статьи расходов находятся в рамках бюджета."
        self.create_table_report("📉 Анализ затрат", columns, data, description)

    def show_dish_roi_table(self):
        """Показать ROI по блюдам в виде таблицы"""
        columns = ('Блюдо', 'Цена продажи', 'Себестоимость', 'Прибыль', 'ROI %', 'Заказов', 'Общая прибыль')
        data = [
            ('Морс клюквенный', '100₽', '20₽', '80₽', '400%', '52', '4,160₽'),
            ('Мороженое', '200₽', '50₽', '150₽', '300%', '45', '6,750₽'),
            ('Тирамису', '300₽', '75₽', '225₽', '300%', '76', '17,100₽'),
            ('Борщ украинский', '200₽', '60₽', '140₽', '233%', '134', '18,760₽'),
            ('Стейк рибай', '600₽', '210₽', '390₽', '186%', '156', '60,840₽'),
            ('Котлета по-киевски', '400₽', '150₽', '250₽', '167%', '98', '24,500₽'),
            ('Пельмени домашние', '300₽', '120₽', '180₽', '150%', '87', '15,660₽'),
            ('Салат цезарь', '300₽', '120₽', '180₽', '150%', '128', '23,040₽'),
            ('Оливье', '200₽', '85₽', '115₽', '135%', '68', '7,820₽')
        ]

        description = "ROI анализ по блюдам. Напитки и десерты показывают наивысшую рентабельность."
        self.create_table_report("🎯 ROI по блюдам", columns, data, description)

    def show_inventory_turnover_table(self):
        """Показать оборачиваемость склада в виде таблицы"""
        columns = ('Товар', 'Средний остаток', 'Расход/месяц', 'Оборачиваемость', 'Дни запаса', 'Статус')
        data = [
            ('Лук репчатый', '15 кг', '60 кг', '4.0 раз', '7.5 дней', '🔥 Высокая'),
            ('Помидоры', '12 кг', '45 кг', '3.8 раз', '8.0 дней', '🔥 Высокая'),
            ('Говядина', '25 кг', '90 кг', '3.6 раз', '8.3 дней', '🔥 Высокая'),
            ('Молоко 3.2%', '18 л', '54 л', '3.0 раз', '10.0 дней', '🟢 Хорошая'),
            ('Мука пшеничная', '40 кг', '108 кг', '2.7 раз', '11.1 дней', '🟢 Хорошая'),
            ('Картофель', '50 кг', '120 кг', '2.4 раз', '12.5 дней', '🟡 Средняя'),
            ('Сыр твёрдый', '8 кг', '18 кг', '2.3 раз', '13.3 дней', '🟡 Средняя'),
            ('Морковь', '20 кг', '36 кг', '1.8 раз', '16.7 дней', '🟡 Средняя'),
            ('Капуста', '25 кг', '30 кг', '1.2 раз', '25.0 дней', '🔴 Низкая')
        ]

        description = "Анализ оборачиваемости товаров на складе. Высокая оборачиваемость у скоропортящихся продуктов."
        self.create_table_report("🔄 Оборачиваемость склада", columns, data, description)

    def show_abc_analysis_table(self):
        """Показать ABC анализ в виде таблицы"""
        columns = ('Товар', 'Выручка', 'Доля %', 'Накопительно %', 'Класс', 'Приоритет')
        data = [
            ('Стейк рибай', '93,600₽', '15.2%', '15.2%', 'A', '🔴 Высокий'),
            ('Салат цезарь', '38,400₽', '6.2%', '21.4%', 'A', '🔴 Высокий'),
            ('Котлета по-киевски', '39,200₽', '6.4%', '27.8%', 'A', '🔴 Высокий'),
            ('Шашлык из свинины', '32,500₽', '5.3%', '33.1%', 'A', '🔴 Высокий'),
            ('Борщ украинский', '26,800₽', '4.4%', '37.5%', 'A', '🔴 Высокий'),
            ('Пельмени домашние', '26,100₽', '4.2%', '41.7%', 'A', '🔴 Высокий'),
            ('Тирамису', '22,800₽', '3.7%', '45.4%', 'A', '🔴 Высокий'),
            ('Солянка мясная', '21,600₽', '3.5%', '48.9%', 'A', '🔴 Высокий'),
            ('Лагман', '21,600₽', '3.5%', '52.4%', 'B', '🟡 Средний'),
            ('Чизкейк', '18,600₽', '3.0%', '55.4%', 'B', '🟡 Средний'),
            ('Греческий салат', '17,400₽', '2.8%', '58.2%', 'B', '🟡 Средний'),
            ('Крем-суп грибной', '14,400₽', '2.3%', '60.5%', 'B', '🟡 Средний'),
            ('Оливье', '13,600₽', '2.2%', '62.7%', 'B', '🟡 Средний'),
            ('Мороженое', '9,000₽', '1.5%', '64.2%', 'C', '🟢 Низкий'),
            ('Морс клюквенный', '5,200₽', '0.8%', '65.0%', 'C', '🟢 Низкий')
        ]

        description = "ABC анализ товаров по выручке. Класс A (80% выручки) требует особого внимания."
        self.create_table_report("📊 ABC анализ", columns, data, description)

    def show_demand_forecast_table(self):
        """Показать прогноз потребности в виде таблицы"""
        columns = ('Товар', 'Текущий расход', 'Прогноз на неделю', 'Прогноз на месяц', 'Рекомендуемый заказ', 'Поставщик')
        data = [
            ('Говядина', '22 кг/нед', '24 кг', '96 кг', '30 кг', 'Мясокомбинат'),
            ('Мука пшеничная', '18 кг/нед', '20 кг', '80 кг', '50 кг', 'Мука-Сервис'),
            ('Молоко 3.2%', '16 л/нед', '18 л', '72 л', '25 л', 'Молочный Дом'),
            ('Картофель', '28 кг/нед', '30 кг', '120 кг', '40 кг', 'Овощи-Фрукты'),
            ('Лук репчатый', '15 кг/нед', '16 кг', '64 кг', '25 кг', 'Овощи-Фрукты'),
            ('Помидоры', '12 кг/нед', '14 кг', '56 кг', '20 кг', 'Овощи-Фрукты'),
            ('Сыр твёрдый', '8 кг/нед', '9 кг', '36 кг', '15 кг', 'Молочный Дом'),
            ('Морковь', '9 кг/нед', '10 кг', '40 кг', '15 кг', 'Овощи-Фрукты')
        ]

        description = "Прогноз потребности в продуктах на основе исторических данных и сезонности."
        self.create_table_report("🔮 Прогноз потребности", columns, data, description)

    def show_supplier_analysis_table(self):
        """Показать анализ поставщиков в виде таблицы"""
        columns = ('Поставщик', 'Сумма закупок', 'Доля %', 'Качество', 'Сроки доставки', 'Рейтинг', 'Статус')
        data = [
            ('Мясокомбинат Премиум', '156,000₽', '28.5%', '⭐⭐⭐⭐⭐', '1-2 дня', '9.8/10', '🟢 Отлично'),
            ('Овощи-Фрукты ООО', '124,800₽', '22.8%', '⭐⭐⭐⭐', '1 день', '9.2/10', '🟢 Отлично'),
            ('Молочный Дом', '98,400₽', '18.0%', '⭐⭐⭐⭐⭐', '1 день', '9.5/10', '🟢 Отлично'),
            ('Мука-Сервис', '67,200₽', '12.3%', '⭐⭐⭐⭐', '2-3 дня', '8.8/10', '🟢 Хорошо'),
            ('Масло-Продукт', '45,600₽', '8.3%', '⭐⭐⭐', '3-4 дня', '7.5/10', '🟡 Средне'),
            ('Специи-Опт', '34,200₽', '6.2%', '⭐⭐⭐⭐', '2 дня', '8.2/10', '🟢 Хорошо'),
            ('Рыба-Трейд', '21,600₽', '3.9%', '⭐⭐⭐', '1-2 дня', '7.8/10', '🟡 Средне')
        ]

        description = "Анализ работы с поставщиками по объёмам, качеству и надёжности поставок."
        self.create_table_report("🏢 Анализ поставщиков", columns, data, description)

    def show_staff_performance_table(self):
        """Показать производительность персонала в виде таблицы"""
        columns = ('Сотрудник', 'Должность', 'Выручка/смена', 'Заказов/смена', 'Рейтинг', 'Премия', 'Статус')
        data = [
            ('Иванов П.А.', 'Шеф-повар', '28,500₽', '76', '⭐⭐⭐⭐⭐', '15,000₽', '🟢 Отлично'),
            ('Петрова С.В.', 'Официант', '24,200₽', '65', '⭐⭐⭐⭐⭐', '8,000₽', '🟢 Отлично'),
            ('Сидоров А.И.', 'Повар', '22,800₽', '61', '⭐⭐⭐⭐', '6,000₽', '🟢 Хорошо'),
            ('Козлова В.П.', 'Официант', '21,600₽', '58', '⭐⭐⭐⭐', '5,000₽', '🟢 Хорошо'),
            ('Новиков Д.С.', 'Бармен', '19,800₽', '53', '⭐⭐⭐⭐', '4,000₽', '🟢 Хорошо'),
            ('Волкова М.А.', 'Официант', '18,400₽', '49', '⭐⭐⭐', '2,000₽', '🟡 Средне'),
            ('Орлов К.В.', 'Повар', '17,200₽', '46', '⭐⭐⭐', '1,500₽', '🟡 Средне')
        ]

        description = "Анализ производительности персонала по выручке и количеству обслуженных заказов."
        self.create_table_report("⚡ Производительность персонала", columns, data, description)

    def show_shift_analysis_table(self):
        """Показать анализ смен в виде таблицы"""
        columns = ('Смена', 'Время', 'Персонал', 'Выручка', 'Заказов', 'Загрузка', 'Эффективность')
        data = [
            ('Утренняя', '08:00-16:00', '8 чел.', '156,400₽', '418', '65%', '🟢 Высокая'),
            ('Дневная', '12:00-20:00', '12 чел.', '284,800₽', '761', '85%', '🟢 Высокая'),
            ('Вечерняя', '16:00-24:00', '10 чел.', '187,400₽', '453', '75%', '🟢 Хорошая'),
            ('Ночная', '20:00-04:00', '4 чел.', '45,600₽', '98', '45%', '🟡 Средняя')
        ]

        description = "Анализ эффективности работы по сменам. Дневная смена показывает максимальную загрузку."
        self.create_table_report("🕐 Анализ смен", columns, data, description)

    def show_staff_costs_table(self):
        """Показать затраты на персонал в виде таблицы"""
        columns = ('Должность', 'Количество', 'Оклад', 'Премии', 'Соц.взносы', 'Итого', 'Доля %')
        data = [
            ('Шеф-повар', '1', '80,000₽', '15,000₽', '19,000₽', '114,000₽', '18.2%'),
            ('Повара', '4', '50,000₽', '20,000₽', '14,000₽', '84,000₽', '13.4%'),
            ('Официанты', '6', '35,000₽', '18,000₽', '10,600₽', '63,600₽', '10.2%'),
            ('Бармены', '2', '40,000₽', '8,000₽', '9,600₽', '57,600₽', '9.2%'),
            ('Администраторы', '2', '45,000₽', '6,000₽', '10,200₽', '61,200₽', '9.8%'),
            ('Уборщики', '3', '25,000₽', '3,000₽', '5,600₽', '33,600₽', '5.4%'),
            ('Охрана', '2', '30,000₽', '2,000₽', '6,400₽', '38,400₽', '6.1%')
        ]

        description = "Структура затрат на персонал по должностям. Общий фонд оплаты труда: 452,400₽."
        self.create_table_report("💰 Затраты на персонал", columns, data, description)

    def refresh_data(self):
        """Обновить данные"""
        messagebox.showinfo("Обновление", "Данные аналитики обновлены")
    
    def export_pdf(self):
        """Экспорт в PDF"""
        messagebox.showinfo("Экспорт", "Отчёт экспортирован в PDF")
    
    def export_excel(self):
        """Экспорт в Excel"""
        messagebox.showinfo("Экспорт", "Отчёт экспортирован в Excel")

def create_advanced_reports_manager(parent, db_manager):
    """Создать менеджер расширенных отчётов"""
    manager = AdvancedReportsManager(parent, db_manager)
    manager.create_window()
    return manager
