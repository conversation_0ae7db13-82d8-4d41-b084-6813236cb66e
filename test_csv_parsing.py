#!/usr/bin/env python3
"""
Test CSV parsing with your specific format
"""

import pandas as pd
from datetime import datetime

def parse_date(date_value):
    """Parse date from YYYYMMDD format"""
    if pd.isna(date_value) or not date_value or str(date_value).strip() == '':
        return datetime.now().date().isoformat()
    
    try:
        date_str = str(date_value).strip()
        if not date_str or date_str.lower() in ['nan', 'null', 'none', '']:
            return datetime.now().date().isoformat()
        
        # Try YYYYMMDD format first
        try:
            parsed_date = datetime.strptime(date_str, '%Y%m%d').date()
            return parsed_date.isoformat()
        except ValueError:
            pass
        
        # Try pandas parsing as fallback
        try:
            parsed_date = pd.to_datetime(date_str).date()
            return parsed_date.isoformat()
        except:
            pass
        
        return datetime.now().date().isoformat()
    except Exception as e:
        print(f"Date parsing error for '{date_value}': {e}")
        return datetime.now().date().isoformat()

def parse_numeric(value, default=0.0):
    """Parse numeric value with European format (comma as decimal)"""
    if pd.isna(value) or not value:
        return default
    
    try:
        value_str = str(value).strip()
        if not value_str or value_str.lower() in ['nan', 'null', 'none', '']:
            return default
        
        # Handle European format (comma as decimal separator)
        cleaned = value_str.replace('₽', '').replace('$', '').replace('€', '').strip()
        
        if ',' in cleaned and '.' not in cleaned:
            # European format: comma is decimal separator
            cleaned = cleaned.replace(',', '.')
        
        cleaned = cleaned.replace(' ', '')
        result = float(cleaned)
        return result
        
    except (ValueError, TypeError) as e:
        print(f"Could not parse numeric value '{value}': {e}")
        return default

def test_parsing():
    """Test parsing with sample data from your CSV"""
    print("Testing CSV parsing with your format...")
    print("=" * 50)
    
    # Sample data from your CSV analysis
    test_data = [
        [20220508, 1, 9, '1,0000', '3,00', '3,00', None, 'Бар', 'Вода"Аура" негазированная', 23442],
        [20220508, 1, 9, '1,0000', '2,50', '2,50', None, 'Бар', 'Кофе "Американо "', 23442],
        [20220508, 1, 9, '3,0000', '3,00', '9,00', None, 'Бар', 'Вино Arta Vinia Мерло красное п/с', 23442]
    ]
    
    # Column mapping for your format
    column_mapping = {
        'order_date': 0,      # Column 1: Date (YYYYMMDD format)
        'quantity': 3,        # Column 4: Quantity (with comma decimal)
        'price_per_dish': 4,  # Column 5: Price per dish (with comma decimal)
        'total_amount': 5,    # Column 6: Total amount (with comma decimal)
        'department': 7,      # Column 8: Department (Бар, etc.)
        'dish_name': 8,       # Column 9: Dish name
        'order_number': 9,    # Column 10: Order number
    }
    
    for i, row_data in enumerate(test_data, 1):
        print(f"\nTesting row {i}:")
        print(f"Raw data: {row_data}")
        
        # Parse each field
        order_date = parse_date(row_data[column_mapping['order_date']])
        quantity = parse_numeric(row_data[column_mapping['quantity']], 1.0)
        price_per_dish = parse_numeric(row_data[column_mapping['price_per_dish']], 0.0)
        total_amount = parse_numeric(row_data[column_mapping['total_amount']], 0.0)
        department = str(row_data[column_mapping['department']]).strip()
        dish_name = str(row_data[column_mapping['dish_name']]).strip()
        order_number = str(row_data[column_mapping['order_number']]).strip()
        
        print(f"Parsed results:")
        print(f"  Date: {order_date}")
        print(f"  Quantity: {quantity}")
        print(f"  Price: {price_per_dish}")
        print(f"  Total: {total_amount}")
        print(f"  Department: {department}")
        print(f"  Dish: {dish_name}")
        print(f"  Order: {order_number}")
        
        # Validate calculations
        calculated_total = quantity * price_per_dish
        print(f"  Calculated total: {calculated_total}")
        print(f"  Total matches: {abs(calculated_total - total_amount) < 0.01}")

if __name__ == "__main__":
    test_parsing()
