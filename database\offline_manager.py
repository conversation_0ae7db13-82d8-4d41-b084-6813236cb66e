"""
Offline Data Manager for Restaurant Management System
Handles data operations when the system is offline and manages sync when back online
"""

import sqlite3
import json
import time
import threading
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import uuid
import hashlib

class OfflineDataManager:
    """Manages data operations during offline periods"""
    
    def __init__(self, connection_pool, location_id: str):
        self.connection_pool = connection_pool
        self.location_id = location_id
        self.logger = logging.getLogger(__name__)
        
        # Offline state
        self.is_offline = False
        self.offline_since = None
        self.offline_operations = []
        
        # Offline storage
        self.offline_db_path = f"offline_data_{location_id}.db"
        self.offline_connection = None
        
        # Initialize offline infrastructure
        self._initialize_offline_storage()
    
    def _initialize_offline_storage(self):
        """Initialize offline data storage"""
        try:
            # Create offline database
            self.offline_connection = sqlite3.connect(
                self.offline_db_path,
                check_same_thread=False,
                timeout=30.0
            )
            self.offline_connection.row_factory = sqlite3.Row
            
            # Create offline tables
            offline_tables_sql = [
                '''
                CREATE TABLE IF NOT EXISTS offline_operations (
                    id TEXT PRIMARY KEY,
                    table_name TEXT NOT NULL,
                    operation TEXT NOT NULL,
                    record_data TEXT NOT NULL,
                    original_id TEXT,
                    timestamp DATETIME NOT NULL,
                    status TEXT DEFAULT 'pending',
                    dependencies TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
                ''',
                '''
                CREATE TABLE IF NOT EXISTS offline_cache (
                    table_name TEXT NOT NULL,
                    record_id TEXT NOT NULL,
                    data TEXT NOT NULL,
                    last_updated DATETIME NOT NULL,
                    PRIMARY KEY (table_name, record_id)
                )
                ''',
                '''
                CREATE TABLE IF NOT EXISTS offline_metadata (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
                ''',
                '''
                CREATE INDEX IF NOT EXISTS idx_offline_ops_timestamp ON offline_operations(timestamp)
                ''',
                '''
                CREATE INDEX IF NOT EXISTS idx_offline_cache_updated ON offline_cache(last_updated)
                '''
            ]
            
            for sql in offline_tables_sql:
                self.offline_connection.execute(sql)
            
            self.offline_connection.commit()
            self.logger.info("Offline storage initialized")
            
        except Exception as e:
            self.logger.error(f"Error initializing offline storage: {e}")
    
    def set_offline_mode(self, offline: bool):
        """Set offline mode on/off"""
        if offline and not self.is_offline:
            self.is_offline = True
            self.offline_since = datetime.now()
            self._cache_critical_data()
            self.logger.info("Switched to offline mode")
            
        elif not offline and self.is_offline:
            self.is_offline = False
            offline_duration = datetime.now() - self.offline_since if self.offline_since else timedelta(0)
            self.logger.info(f"Switched to online mode after {offline_duration}")
            
            # Process offline operations when back online
            self._process_offline_operations()
            self.offline_since = None
    
    def _cache_critical_data(self):
        """Cache critical data for offline operations"""
        try:
            critical_tables = [
                'menu_items', 'recipes', 'inventory', 'staff', 
                'customers', 'payment_methods', 'departments'
            ]
            
            for table_name in critical_tables:
                try:
                    # Get all records from table
                    records = self.connection_pool.execute_query(f"SELECT * FROM {table_name}")
                    
                    for record in records:
                        record_dict = dict(record)
                        record_id = record_dict.get('id', record_dict.get('rowid'))
                        
                        if record_id:
                            self.offline_connection.execute(
                                '''INSERT OR REPLACE INTO offline_cache 
                                   (table_name, record_id, data, last_updated)
                                   VALUES (?, ?, ?, ?)''',
                                (table_name, str(record_id), json.dumps(record_dict, default=str), 
                                 datetime.now().isoformat())
                            )
                    
                    self.offline_connection.commit()
                    self.logger.debug(f"Cached {len(records)} records from {table_name}")
                    
                except Exception as e:
                    self.logger.error(f"Error caching table {table_name}: {e}")
            
            # Set offline metadata
            self._set_offline_metadata('offline_since', self.offline_since.isoformat())
            self._set_offline_metadata('cache_updated', datetime.now().isoformat())
            
        except Exception as e:
            self.logger.error(f"Error caching critical data: {e}")
    
    def execute_offline_operation(self, table_name: str, operation: str, 
                                 data: Dict[str, Any], original_id: str = None) -> str:
        """Execute an operation in offline mode"""
        if not self.is_offline:
            raise ValueError("Not in offline mode")
        
        try:
            # Generate operation ID
            operation_id = uuid.uuid4().hex
            
            # Store operation for later sync
            self.offline_connection.execute(
                '''INSERT INTO offline_operations 
                   (id, table_name, operation, record_data, original_id, timestamp)
                   VALUES (?, ?, ?, ?, ?, ?)''',
                (operation_id, table_name, operation, json.dumps(data, default=str),
                 original_id, datetime.now().isoformat())
            )
            
            # Apply operation to offline cache if applicable
            if operation == 'INSERT':
                self._cache_insert(table_name, data, operation_id)
            elif operation == 'UPDATE' and original_id:
                self._cache_update(table_name, original_id, data)
            elif operation == 'DELETE' and original_id:
                self._cache_delete(table_name, original_id)
            
            self.offline_connection.commit()
            self.offline_operations.append({
                'id': operation_id,
                'table_name': table_name,
                'operation': operation,
                'data': data,
                'timestamp': datetime.now()
            })
            
            self.logger.debug(f"Offline operation recorded: {operation} on {table_name}")
            return operation_id
            
        except Exception as e:
            self.logger.error(f"Error executing offline operation: {e}")
            raise
    
    def _cache_insert(self, table_name: str, data: Dict[str, Any], temp_id: str):
        """Cache an INSERT operation"""
        try:
            # Use temporary ID for offline records
            self.offline_connection.execute(
                '''INSERT OR REPLACE INTO offline_cache 
                   (table_name, record_id, data, last_updated)
                   VALUES (?, ?, ?, ?)''',
                (table_name, temp_id, json.dumps(data, default=str), datetime.now().isoformat())
            )
        except Exception as e:
            self.logger.error(f"Error caching insert: {e}")
    
    def _cache_update(self, table_name: str, record_id: str, data: Dict[str, Any]):
        """Cache an UPDATE operation"""
        try:
            # Get existing cached data
            existing = self.offline_connection.execute(
                "SELECT data FROM offline_cache WHERE table_name = ? AND record_id = ?",
                (table_name, record_id)
            ).fetchone()
            
            if existing:
                # Merge with existing data
                existing_data = json.loads(existing['data'])
                existing_data.update(data)
                
                self.offline_connection.execute(
                    '''UPDATE offline_cache SET data = ?, last_updated = ?
                       WHERE table_name = ? AND record_id = ?''',
                    (json.dumps(existing_data, default=str), datetime.now().isoformat(),
                     table_name, record_id)
                )
            else:
                # Create new cache entry
                self.offline_connection.execute(
                    '''INSERT INTO offline_cache 
                       (table_name, record_id, data, last_updated)
                       VALUES (?, ?, ?, ?)''',
                    (table_name, record_id, json.dumps(data, default=str), 
                     datetime.now().isoformat())
                )
        except Exception as e:
            self.logger.error(f"Error caching update: {e}")
    
    def _cache_delete(self, table_name: str, record_id: str):
        """Cache a DELETE operation"""
        try:
            self.offline_connection.execute(
                "DELETE FROM offline_cache WHERE table_name = ? AND record_id = ?",
                (table_name, record_id)
            )
        except Exception as e:
            self.logger.error(f"Error caching delete: {e}")
    
    def get_cached_data(self, table_name: str, record_id: str = None) -> Optional[List[Dict[str, Any]]]:
        """Get cached data for offline operations"""
        try:
            if record_id:
                # Get specific record
                result = self.offline_connection.execute(
                    "SELECT data FROM offline_cache WHERE table_name = ? AND record_id = ?",
                    (table_name, record_id)
                ).fetchone()
                
                if result:
                    return [json.loads(result['data'])]
                return None
            else:
                # Get all records for table
                results = self.offline_connection.execute(
                    "SELECT data FROM offline_cache WHERE table_name = ?",
                    (table_name,)
                ).fetchall()
                
                return [json.loads(row['data']) for row in results]
                
        except Exception as e:
            self.logger.error(f"Error getting cached data: {e}")
            return None
    
    def _process_offline_operations(self):
        """Process offline operations when back online"""
        try:
            # Get all pending offline operations
            operations = self.offline_connection.execute(
                "SELECT * FROM offline_operations WHERE status = 'pending' ORDER BY timestamp"
            ).fetchall()
            
            if not operations:
                return
            
            self.logger.info(f"Processing {len(operations)} offline operations")
            
            # Group operations by dependencies
            processed_count = 0
            failed_count = 0
            
            for operation in operations:
                try:
                    success = self._apply_offline_operation(operation)
                    if success:
                        # Mark as processed
                        self.offline_connection.execute(
                            "UPDATE offline_operations SET status = 'processed' WHERE id = ?",
                            (operation['id'],)
                        )
                        processed_count += 1
                    else:
                        # Mark as failed
                        self.offline_connection.execute(
                            "UPDATE offline_operations SET status = 'failed' WHERE id = ?",
                            (operation['id'],)
                        )
                        failed_count += 1
                        
                except Exception as e:
                    self.logger.error(f"Error processing offline operation {operation['id']}: {e}")
                    failed_count += 1
            
            self.offline_connection.commit()
            self.logger.info(f"Processed offline operations: {processed_count} success, {failed_count} failed")
            
            # Clear processed operations after some time
            self._cleanup_old_operations()
            
        except Exception as e:
            self.logger.error(f"Error processing offline operations: {e}")
    
    def _apply_offline_operation(self, operation) -> bool:
        """Apply a single offline operation to the main database"""
        try:
            table_name = operation['table_name']
            op_type = operation['operation']
            data = json.loads(operation['record_data'])
            original_id = operation['original_id']
            
            if op_type == 'INSERT':
                # Insert new record
                columns = list(data.keys())
                placeholders = ', '.join(['?' for _ in columns])
                column_names = ', '.join(columns)
                values = [data[col] for col in columns]
                
                query = f"INSERT INTO {table_name} ({column_names}) VALUES ({placeholders})"
                self.connection_pool.execute_query(query, tuple(values))
                
            elif op_type == 'UPDATE' and original_id:
                # Update existing record
                columns = [col for col in data.keys() if col != 'id']
                if columns:
                    set_clause = ', '.join([f"{col} = ?" for col in columns])
                    values = [data[col] for col in columns]
                    values.append(original_id)
                    
                    query = f"UPDATE {table_name} SET {set_clause} WHERE id = ?"
                    self.connection_pool.execute_query(query, tuple(values))
                
            elif op_type == 'DELETE' and original_id:
                # Delete record
                query = f"DELETE FROM {table_name} WHERE id = ?"
                self.connection_pool.execute_query(query, (original_id,))
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error applying offline operation: {e}")
            return False
    
    def _cleanup_old_operations(self):
        """Clean up old processed operations"""
        try:
            # Remove operations older than 7 days
            cutoff_date = (datetime.now() - timedelta(days=7)).isoformat()
            
            self.offline_connection.execute(
                "DELETE FROM offline_operations WHERE status = 'processed' AND timestamp < ?",
                (cutoff_date,)
            )
            
            # Clean up old cache entries
            cache_cutoff = (datetime.now() - timedelta(days=30)).isoformat()
            self.offline_connection.execute(
                "DELETE FROM offline_cache WHERE last_updated < ?",
                (cache_cutoff,)
            )
            
            self.offline_connection.commit()
            
        except Exception as e:
            self.logger.error(f"Error cleaning up old operations: {e}")
    
    def _set_offline_metadata(self, key: str, value: str):
        """Set offline metadata"""
        try:
            self.offline_connection.execute(
                "INSERT OR REPLACE INTO offline_metadata (key, value, updated_at) VALUES (?, ?, ?)",
                (key, value, datetime.now().isoformat())
            )
            self.offline_connection.commit()
        except Exception as e:
            self.logger.error(f"Error setting offline metadata: {e}")
    
    def get_offline_status(self) -> Dict[str, Any]:
        """Get offline mode status"""
        try:
            pending_ops = self.offline_connection.execute(
                "SELECT COUNT(*) as count FROM offline_operations WHERE status = 'pending'"
            ).fetchone()['count']
            
            cached_tables = self.offline_connection.execute(
                "SELECT table_name, COUNT(*) as count FROM offline_cache GROUP BY table_name"
            ).fetchall()
            
            return {
                'is_offline': self.is_offline,
                'offline_since': self.offline_since.isoformat() if self.offline_since else None,
                'pending_operations': pending_ops,
                'cached_tables': {row['table_name']: row['count'] for row in cached_tables},
                'offline_db_size': Path(self.offline_db_path).stat().st_size if Path(self.offline_db_path).exists() else 0
            }
            
        except Exception as e:
            self.logger.error(f"Error getting offline status: {e}")
            return {
                'is_offline': self.is_offline,
                'offline_since': None,
                'pending_operations': 0,
                'cached_tables': {},
                'offline_db_size': 0
            }
    
    def close(self):
        """Close offline manager and cleanup"""
        try:
            if self.offline_connection:
                self.offline_connection.close()
        except Exception as e:
            self.logger.error(f"Error closing offline manager: {e}")
