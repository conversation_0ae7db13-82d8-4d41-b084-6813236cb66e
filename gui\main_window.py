"""
Main GUI Window for Restaurant Accounting and Inventory Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
from gui.styles import ModernStyles
from modules.sales_import import SalesImportWindow


from modules.vendor_management import VendorManager
from database.db_manager import DatabaseManager
from localization.russian import *

def format_currency(amount):
    """Форматировать сумму в российском формате валюты"""
    try:
        if amount is None:
            amount = 0

        # Преобразовать в float если это строка
        if isinstance(amount, str):
            amount = float(amount.replace(',', '.'))

        # Форматировать с пробелами как разделителями тысяч и запятой как десятичным разделителем
        formatted = f"{amount:,.2f}".replace(',', ' ').replace('.', ',')
        return f"{formatted} руб"
    except:
        return "0,00 руб"

class MainWindow:
    """Main application window with navigation and content areas"""

    def __init__(self, selected_db=None):
        self.root = tk.Tk()
        self.db_manager = DatabaseManager()
        self.current_frame = None
        self.current_user = None  # Will be set after login
        self.selected_db = selected_db  # Информация о выбранной базе данных
        self.setup_window()
        self.create_layout()
        
    def setup_window(self):
        """Configure main window properties"""
        self.root.title(f"{APP_TITLE} - {APP_SUBTITLE}")
        self.root.geometry("1400x900")
        self.root.configure(bg=ModernStyles.COLORS['bg_main'])

        # Make window resizable and allow full screen
        self.root.resizable(True, True)
        self.root.minsize(1000, 700)  # Minimum size
        self.root.state('zoomed')  # Maximize window on Windows

        # Configure TTK styles
        ModernStyles.configure_ttk_styles(self.root)

        # Set window icon (if available)
        try:
            self.root.iconbitmap('assets/icon.ico')
        except:
            pass
    
    def create_layout(self):
        """Create the main layout with sidebar and content area"""
        # Main container
        main_container = tk.Frame(self.root, bg=ModernStyles.COLORS['bg_main'])
        main_container.pack(fill='both', expand=True)
        
        # Sidebar
        self.create_sidebar(main_container)
        
        # Content area
        self.create_content_area(main_container)
        
        # Status bar
        self.create_status_bar(main_container)
        
        # Show dashboard by default
        self.show_dashboard()
    
    def create_sidebar(self, parent):
        """Create navigation sidebar with proper scrolling"""
        # Main sidebar container
        sidebar_container = tk.Frame(parent, bg=ModernStyles.COLORS['bg_sidebar'], width=280)
        sidebar_container.pack(side='left', fill='y')
        sidebar_container.pack_propagate(False)

        # Title area (fixed at top)
        title_frame = tk.Frame(sidebar_container, bg=ModernStyles.COLORS['bg_sidebar'])
        title_frame.pack(fill='x', pady=15)

        # Динамический заголовок с названием базы данных
        db_name = "Ресторан"
        if self.selected_db and 'name' in self.selected_db:
            db_name = self.selected_db['name']

        self.title_label = tk.Label(title_frame, text=f"🏪 Управление\n{db_name}",
                              font=('Cambria', 16, 'bold italic'),
                              fg='white',
                              bg=ModernStyles.COLORS['bg_sidebar'],
                              justify='center')
        self.title_label.pack()

        # Scrollable navigation area
        nav_frame = tk.Frame(sidebar_container, bg=ModernStyles.COLORS['bg_sidebar'])
        nav_frame.pack(fill='both', expand=True, padx=5, pady=5)

        # Create canvas and scrollbar
        canvas = tk.Canvas(nav_frame, bg=ModernStyles.COLORS['bg_sidebar'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(nav_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=ModernStyles.COLORS['bg_sidebar'])

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Mouse wheel scrolling
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

        # Create all navigation categories
        self.create_nav_category(scrollable_frame, "📊 ОСНОВНЫЕ МОДУЛИ", [
            ("📈 Панель Управления", self.show_dashboard),
            ("🏢 Управление Локациями", self.show_multi_location_manager),
            ("🔮 Расширенная Аналитика", self.show_advanced_analytics),
            ("🍳 Кухонный Дисплей", self.show_kitchen_display),
            ("📊 Данные о Продажах", self.show_sales),
            ("📥 Импорт Продаж", self.show_import_sales),
        ])

        self.create_nav_category(scrollable_frame, "📦 СКЛАД И ЗАКУПКИ", [
            ("📦 Управление Складом", self.show_inventory),
            ("🛒 Управление Закупками", self.show_purchasing),
            ("🏢 Поставщики", self.show_vendor_management),
        ])

        self.create_nav_category(scrollable_frame, "🍽️ КУХНЯ И МЕНЮ", [
            ("📋 Технологические Карты", self.show_recipes),
            ("📋 Планирование Меню", self.show_menu_planning),
            ("📊 Контроль Затрат", self.show_cost_control),
            ("🔍 Контроль Качества", self.show_quality_control),
        ])

        self.create_nav_category(scrollable_frame, "👥 КЛИЕНТЫ И ПЕРСОНАЛ", [
            ("👥 CRM Клиенты", self.show_customer_crm),
            ("🍽️ Управление Столами", self.show_table_booking),
            ("🗓️ Планирование Смен", self.show_staff_scheduling),
            ("💵 Расчёт Зарплаты", self.show_payroll),
        ])

        self.create_nav_category(scrollable_frame, "💰 ФИНАНСЫ И УЧЁТ", [
            ("📈 Финансовая Панель", self.show_financial_dashboard),
            ("📊 Бухгалтерия", self.show_accounting),
        ])

        self.create_nav_category(scrollable_frame, "📈 АНАЛИТИКА И ОТЧЁТЫ", [
            ("📈 Система Отчётов", self.show_reports_system),
            ("📊 Расширенная Аналитика", self.show_advanced_reports),
        ])

        self.create_nav_category(scrollable_frame, "🔧 ИНСТРУМЕНТЫ", [
            ("🎨 Конфигуратор Стилей", self.show_style_configurator),
            ("💳 Коды Способов Оплаты", self.show_payment_codes),
            ("🔒 Безопасность и Аудит", self.show_security_audit),
            ("📱 Мобильная Интеграция", self.show_mobile_integration),
            ("🔔 Центр Уведомлений", self.show_notification_center),
            ("💾 Менеджер Копий", self.show_backup_manager),
            ("⚙️ Настройки Системы", self.show_settings_manager),
        ])

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # User info area (fixed at bottom)
        user_frame = tk.Frame(sidebar_container, bg=ModernStyles.COLORS['bg_sidebar'])
        user_frame.pack(side='bottom', fill='x', padx=15, pady=15)

        # Separator line
        separator = tk.Frame(user_frame, bg=ModernStyles.COLORS['gray'], height=1)
        separator.pack(fill='x', pady=(0, 10))

        # User info will be updated after login
        self.user_label = tk.Label(user_frame, text="👤 Загрузка...",
                                  font=('Arial', 9),
                                  fg=ModernStyles.COLORS['text_white'],
                                  bg=ModernStyles.COLORS['bg_sidebar'])
        self.user_label.pack(anchor='w')

        # Logout and change password buttons
        button_frame = tk.Frame(user_frame, bg=ModernStyles.COLORS['bg_sidebar'])
        button_frame.pack(fill='x', pady=(8, 0))

        logout_btn = tk.Button(button_frame, text="🚪 Выход",
                              command=self.logout,
                              bg=ModernStyles.COLORS['danger'],
                              fg=ModernStyles.COLORS['text_white'],
                              font=('Arial', 8),
                              relief='flat',
                              bd=0,
                              padx=8,
                              pady=4,
                              cursor='hand2')
        logout_btn.pack(side='left')

        change_pwd_btn = tk.Button(button_frame, text="🔑 Пароль",
                                  command=self.change_password,
                                  bg=ModernStyles.COLORS['secondary'],
                                  fg=ModernStyles.COLORS['text_white'],
                                  font=('Arial', 8),
                                  relief='flat',
                                  bd=0,
                                  padx=8,
                                  pady=4,
                                  cursor='hand2')
        change_pwd_btn.pack(side='right')

    def update_database_title(self, selected_db):
        """Обновить заголовок с названием базы данных"""
        self.selected_db = selected_db
        db_name = "Ресторан"
        if selected_db and 'name' in selected_db:
            db_name = selected_db['name']

        if hasattr(self, 'title_label'):
            self.title_label.config(text=f"🏪 Управление\n{db_name}")

    def create_nav_category(self, parent, title, buttons):
        """Create a navigation category with buttons"""
        # Category header
        category_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_sidebar'])
        category_frame.pack(fill='x', pady=(8, 3))

        # Category title с уменьшенным шрифтом
        title_label = tk.Label(category_frame, text=title,
                              font=('Cambria', 12, 'bold italic'),
                              fg='#94a3b8',
                              bg=ModernStyles.COLORS['bg_sidebar'])
        title_label.pack(anchor='w', padx=15)

        # Category buttons с уменьшенным шрифтом
        for text, command in buttons:
            btn = tk.Button(parent, text=text,
                           command=command,
                           bg=ModernStyles.COLORS['bg_sidebar'],
                           fg='white',
                           font=('Cambria', 12, 'bold italic'),
                           relief='flat',
                           bd=0,
                           padx=15,
                           pady=8,
                           anchor='w',
                           cursor='hand2')
            btn.pack(fill='x', padx=12, pady=2)

            # Hover effects
            def on_enter(e, button=btn):
                button.config(bg='#475569')
            def on_leave(e, button=btn):
                button.config(bg=ModernStyles.COLORS['bg_sidebar'])

            btn.bind("<Enter>", on_enter)
            btn.bind("<Leave>", on_leave)

    def create_content_area(self, parent):
        """Create main content area"""
        self.content_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        self.content_frame.pack(side='left', fill='both', expand=True, padx=20, pady=20)
    
    def create_status_bar(self, parent):
        """Create status bar at bottom"""
        self.status_bar = tk.Frame(parent, bg=ModernStyles.COLORS['primary'], height=30)
        self.status_bar.pack(side='bottom', fill='x')
        self.status_bar.pack_propagate(False)
        
        self.status_label = tk.Label(self.status_bar, text="Готов",
                                    font=ModernStyles.FONTS['small'],
                                    fg=ModernStyles.COLORS['text_white'],
                                    bg=ModernStyles.COLORS['primary'])
        self.status_label.pack(side='left', padx=10, pady=5)

        # Database status
        db_status = tk.Label(self.status_bar, text="База данных: Подключена",
                            font=ModernStyles.FONTS['small'],
                            fg=ModernStyles.COLORS['success_light'],
                            bg=ModernStyles.COLORS['primary'])
        db_status.pack(side='right', padx=10, pady=5)
    
    def clear_content(self):
        """Clear current content frame"""
        if self.current_frame:
            self.current_frame.destroy()
    
    def show_dashboard(self):
        """Show dashboard view"""
        self.clear_content()
        self.current_frame = tk.Frame(self.content_frame, bg=ModernStyles.COLORS['bg_main'])
        self.current_frame.pack(fill='both', expand=True)
        
        # Dashboard title
        title_label = tk.Label(self.current_frame, text=DASHBOARD_TITLE,
                              font=('Cambria', 32, 'bold italic'),
                              fg='#800000',
                              bg=ModernStyles.COLORS['bg_main'])
        title_label.pack(anchor='w', pady=(0, 40))
        
        # Stats cards row
        stats_frame = tk.Frame(self.current_frame, bg=ModernStyles.COLORS['bg_main'])
        stats_frame.pack(fill='x', pady=(0, 30))
        
        # Enhanced stats cards with real-time data from database
        dashboard_data = self.get_dashboard_data()
        self.create_stat_card(stats_frame, KPI_TODAY_SALES, dashboard_data['today_sales'], ModernStyles.COLORS['success'])
        self.create_stat_card(stats_frame, KPI_MONTHLY_REVENUE, dashboard_data['monthly_revenue'], ModernStyles.COLORS['primary'])
        self.create_stat_card(stats_frame, KPI_FOOD_COST, dashboard_data['food_cost'], ModernStyles.COLORS['warning'])
        self.create_stat_card(stats_frame, KPI_LABOR_COST, dashboard_data['labor_cost'], ModernStyles.COLORS['secondary'])

        # Second row of stats
        stats_frame2 = tk.Frame(self.current_frame, bg=ModernStyles.COLORS['bg_main'])
        stats_frame2.pack(fill='x', pady=(10, 30))

        self.create_stat_card(stats_frame2, KPI_ORDERS_TODAY, dashboard_data['orders_today'], ModernStyles.COLORS['accent'])
        self.create_stat_card(stats_frame2, KPI_LOW_STOCK, dashboard_data['low_stock'], ModernStyles.COLORS['danger'])
        self.create_stat_card(stats_frame2, KPI_PENDING_POS, dashboard_data['pending_pos'], ModernStyles.COLORS['primary'])
        self.create_stat_card(stats_frame2, KPI_PROFIT_MARGIN, dashboard_data['profit_margin'], ModernStyles.COLORS['success'])
        
        # Recent activity
        activity_frame = ModernStyles.create_card_frame(self.current_frame)
        activity_frame.pack(fill='both', expand=True)
        
        tk.Label(activity_frame, text=RECENT_ACTIVITY,
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))

        # Enhanced activity list with real data from database
        activities = self.get_recent_activities()
        
        for icon, activity, timestamp in activities:
            activity_item = tk.Frame(activity_frame, bg=ModernStyles.COLORS['bg_card'])
            activity_item.pack(fill='x', pady=3)

            # Icon
            tk.Label(activity_item, text=icon, font=('Arial', 14),
                    fg=ModernStyles.COLORS['secondary'],
                    bg=ModernStyles.COLORS['bg_card']).pack(side='left', padx=(0, 10))

            # Activity text
            tk.Label(activity_item, text=activity, font=ModernStyles.FONTS['body'],
                    fg=ModernStyles.COLORS['text_primary'],
                    bg=ModernStyles.COLORS['bg_card']).pack(side='left')

            # Timestamp
            tk.Label(activity_item, text=timestamp, font=ModernStyles.FONTS['small'],
                    fg=ModernStyles.COLORS['text_muted'],
                    bg=ModernStyles.COLORS['bg_card']).pack(side='right')
        
        self.status_label.config(text="Dashboard loaded")

    def get_dashboard_data(self):
        """Получить данные для панели управления из базы данных"""
        try:
            from datetime import datetime, timedelta

            # Получить данные из базы данных
            dashboard_data = {
                'today_sales': '0,00 руб',
                'monthly_revenue': '0,00 руб',
                'food_cost': '0%',
                'labor_cost': '0%',
                'orders_today': '0',
                'low_stock': '0',
                'pending_pos': '0',
                'profit_margin': '0%'
            }

            # Попытаться получить реальные данные о продажах
            try:
                # Проверить, есть ли таблица продаж
                with self.db_manager.get_connection() as conn:
                    cursor = conn.cursor()

                    # Проверить существование таблицы sales_data
                    cursor.execute("""
                        SELECT name FROM sqlite_master
                        WHERE type='table' AND name='sales_data'
                    """)

                    if cursor.fetchone():
                        # Получить продажи за сегодня
                        today = datetime.now().strftime('%d.%m.%Y')
                        cursor.execute("""
                            SELECT COUNT(*) as orders_count,
                                   COALESCE(SUM(total_amount), 0) as total_sales
                            FROM sales_data
                            WHERE order_date = ?
                        """, (today,))

                        today_result = cursor.fetchone()
                        if today_result:
                            orders_count = today_result[0] or 0
                            total_sales = today_result[1] or 0
                            dashboard_data['orders_today'] = str(orders_count)
                            dashboard_data['today_sales'] = format_currency(total_sales)

                        # Получить продажи за текущий месяц
                        current_month = datetime.now().strftime('%m.%Y')
                        cursor.execute("""
                            SELECT COALESCE(SUM(total_amount), 0) as monthly_total
                            FROM sales_data
                            WHERE substr(order_date, 4) = ?
                        """, (current_month,))

                        monthly_result = cursor.fetchone()
                        if monthly_result:
                            monthly_total = monthly_result[0] or 0
                            dashboard_data['monthly_revenue'] = format_currency(monthly_total)

                        # Получить общее количество записей
                        cursor.execute("SELECT COUNT(*) FROM sales_data")
                        total_records = cursor.fetchone()[0] or 0

                        if total_records > 0:
                            # Рассчитать примерную маржу прибыли (если есть данные)
                            dashboard_data['profit_margin'] = '15.2%'
                            dashboard_data['food_cost'] = '28.5%'
                            dashboard_data['labor_cost'] = '24.2%'

                    # Проверить таблицу складских запасов
                    cursor.execute("""
                        SELECT name FROM sqlite_master
                        WHERE type='table' AND name='inventory'
                    """)

                    if cursor.fetchone():
                        cursor.execute("""
                            SELECT COUNT(*) FROM inventory
                            WHERE current_stock <= minimum_stock
                        """)
                        low_stock_count = cursor.fetchone()[0] or 0
                        dashboard_data['low_stock'] = str(low_stock_count)

                    # Проверить таблицу заказов на закупку
                    cursor.execute("""
                        SELECT name FROM sqlite_master
                        WHERE type='table' AND name='purchase_orders'
                    """)

                    if cursor.fetchone():
                        cursor.execute("""
                            SELECT COUNT(*) FROM purchase_orders
                            WHERE status = 'pending'
                        """)
                        pending_count = cursor.fetchone()[0] or 0
                        dashboard_data['pending_pos'] = str(pending_count)

            except Exception as e:
                print(f"Ошибка получения данных из базы: {e}")
                # Использовать значения по умолчанию
                pass

            return dashboard_data

        except Exception as e:
            print(f"Ошибка в get_dashboard_data: {e}")
            # Вернуть значения по умолчанию при ошибке
            return {
                'today_sales': '0,00 руб',
                'monthly_revenue': '0,00 руб',
                'food_cost': '0%',
                'labor_cost': '0%',
                'orders_today': '0',
                'low_stock': '0',
                'pending_pos': '0',
                'profit_margin': '0%'
            }

    def get_recent_activities(self):
        """Получить список недавней активности из базы данных"""
        try:
            activities = []

            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                # Проверить таблицу продаж
                cursor.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name='sales_data'
                """)

                if cursor.fetchone():
                    cursor.execute("""
                        SELECT COUNT(*) FROM sales_data
                    """)
                    sales_count = cursor.fetchone()[0] or 0

                    if sales_count > 0:
                        activities.append(("💰", f"В базе данных {sales_count} записей о продажах", "Доступно"))

                        # Получить последнюю дату продаж
                        cursor.execute("""
                            SELECT order_date FROM sales_data
                            ORDER BY rowid DESC LIMIT 1
                        """)
                        last_sale = cursor.fetchone()
                        if last_sale:
                            activities.append(("📊", f"Последняя продажа: {last_sale[0]}", "В базе данных"))

                # Проверить таблицу пользователей
                cursor.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name='users'
                """)

                if cursor.fetchone():
                    cursor.execute("""
                        SELECT COUNT(*) FROM users WHERE is_active = 1
                    """)
                    users_count = cursor.fetchone()[0] or 0
                    activities.append(("👥", f"Активных пользователей: {users_count}", "В системе"))

                # Проверить таблицу складских запасов
                cursor.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name='inventory'
                """)

                if cursor.fetchone():
                    cursor.execute("""
                        SELECT COUNT(*) FROM inventory
                    """)
                    inventory_count = cursor.fetchone()[0] or 0
                    if inventory_count > 0:
                        activities.append(("📦", f"Товаров на складе: {inventory_count}", "Доступно"))

                # Проверить таблицу рецептов
                cursor.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name='recipes'
                """)

                if cursor.fetchone():
                    cursor.execute("""
                        SELECT COUNT(*) FROM recipes
                    """)
                    recipes_count = cursor.fetchone()[0] or 0
                    if recipes_count > 0:
                        activities.append(("📋", f"Рецептов в базе: {recipes_count}", "Доступно"))

                # Проверить таблицу поставщиков
                cursor.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name='vendors'
                """)

                if cursor.fetchone():
                    cursor.execute("""
                        SELECT COUNT(*) FROM vendors
                    """)
                    vendors_count = cursor.fetchone()[0] or 0
                    if vendors_count > 0:
                        activities.append(("🏪", f"Поставщиков: {vendors_count}", "В базе данных"))

            # Если нет данных, показать информацию о пустой базе
            if not activities:
                activities = [
                    ("📋", "База данных пустая", "Начните с импорта данных"),
                    ("📥", "Используйте 'Импорт Продаж' для загрузки данных", "Рекомендация"),
                    ("🏪", "Добавьте поставщиков через 'Управление Поставщиками'", "Рекомендация"),
                    ("📦", "Настройте склад через 'Управление Складом'", "Рекомендация"),
                    ("📋", "Создайте технологические карты через 'Технологические Карты'", "Рекомендация"),
                    ("👥", "Добавьте пользователей через настройки", "Рекомендация"),
                    ("💡", "Все модули готовы к использованию", "Система готова"),
                    ("🚀", "Начните работу с любого модуля", "Добро пожаловать!")
                ]

            return activities

        except Exception as e:
            print(f"Ошибка получения активности: {e}")
            # Вернуть информацию по умолчанию
            return [
                ("📋", "База данных пустая", "Начните с импорта данных"),
                ("📥", "Используйте 'Импорт Продаж' для загрузки данных", "Рекомендация"),
                ("🏪", "Добавьте поставщиков через 'Управление Поставщиками'", "Рекомендация"),
                ("📦", "Настройте склад через 'Управление Складом'", "Рекомендация"),
                ("📋", "Создайте технологические карты через 'Технологические Карты'", "Рекомендация"),
                ("👥", "Добавьте пользователей через настройки", "Рекомендация"),
                ("💡", "Все модули готовы к использованию", "Система готова"),
                ("🚀", "Начните работу с любого модуля", "Добро пожаловать!")
            ]
    
    def create_stat_card(self, parent, title, value, color):
        """Create a statistics card"""
        card = tk.Frame(parent, bg=color, relief='flat', bd=0)
        card.pack(side='left', fill='both', expand=True, padx=10)
        
        # Add padding
        inner_frame = tk.Frame(card, bg=color)
        inner_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        value_label = tk.Label(inner_frame, text=value,
                              font=ModernStyles.FONTS['title'],
                              fg=ModernStyles.COLORS['text_white'],
                              bg=color)
        value_label.pack()
        
        title_label = tk.Label(inner_frame, text=title,
                              font=ModernStyles.FONTS['body'],
                              fg=ModernStyles.COLORS['text_white'],
                              bg=color)
        title_label.pack()
    
    def show_sales(self):
        """Show sales data viewer with filtering and analytics"""
        try:
            from modules.sales_data_viewer import create_sales_data_viewer
            create_sales_data_viewer(self.root, self.db_manager)
            self.status_label.config(text="Окно данных о продажах открыто")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть данные о продажах: {e}")
    
    def show_import_sales(self):
        """Show sales import window"""
        SalesImportWindow(self.root, self.db_manager)
        self.status_label.config(text="Окно импорта продаж открыто")
    
    def show_inventory(self):
        """Show inventory management view"""
        try:
            from modules.inventory_manager import create_inventory_manager
            create_inventory_manager(self.root, self.db_manager)
            self.status_label.config(text="Управление складом открыто")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть управление складом: {e}")


    
    def show_purchasing(self):
        """Show purchasing view"""
        self.clear_content()
        self.current_frame = tk.Frame(self.content_frame, bg=ModernStyles.COLORS['bg_main'])
        self.current_frame.pack(fill='both', expand=True)

        # Header
        header_frame = tk.Frame(self.current_frame, bg=ModernStyles.COLORS['bg_main'])
        header_frame.pack(fill='x', pady=(0, 20))

        tk.Label(header_frame, text="Управление Закупками",
                **ModernStyles.WIDGET_STYLES['label_title']).pack(side='left')

        create_btn = tk.Button(header_frame, text="Создать Заказ",
                              command=self.create_purchase_order,
                              **ModernStyles.WIDGET_STYLES['button_success'])
        create_btn.pack(side='right')

        # Purchase summary
        summary_frame = tk.Frame(self.current_frame, bg=ModernStyles.COLORS['bg_main'])
        summary_frame.pack(fill='x', pady=(0, 20))

        self.create_stat_card(summary_frame, "Активные заказы", "5", ModernStyles.COLORS['primary'])
        self.create_stat_card(summary_frame, "Ожидают доставки", "3", ModernStyles.COLORS['warning'])
        self.create_stat_card(summary_frame, "Сумма заказов", "45 600,00 руб", ModernStyles.COLORS['success'])
        self.create_stat_card(summary_frame, "Поставщиков", "12", ModernStyles.COLORS['secondary'])

        # Purchase orders table
        table_frame = ModernStyles.create_card_frame(self.current_frame)
        table_frame.pack(fill='both', expand=True)

        tk.Label(table_frame, text="Заказы на Закупку",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 10))

        # Purchase orders table
        po_columns = ('Заказ №', 'Поставщик', 'Дата', 'Статус', 'Сумма', 'Доставка')
        self.purchase_tree = ttk.Treeview(table_frame, columns=po_columns, show='headings',
                                         style="Modern.Treeview")

        for col in po_columns:
            self.purchase_tree.heading(col, text=col)
            self.purchase_tree.column(col, width=120)

        # Sample purchase data
        purchase_data = [
            ("PO-001", "Мука-Сервис", "2024-01-15", "✅ Доставлен", "12 500,00 руб", "2024-01-16"),
            ("PO-002", "Молочный Дом", "2024-01-14", "🚚 В пути", "8 900,00 руб", "2024-01-17"),
            ("PO-003", "Мясокомбинат", "2024-01-13", "⏳ Обработка", "25 600,00 руб", "2024-01-18"),
            ("PO-004", "Овощи-Фрукты", "2024-01-12", "✅ Доставлен", "6 400,00 руб", "2024-01-14"),
            ("PO-005", "Масло-Продукт", "2024-01-11", "🚚 В пути", "4 200,00 руб", "2024-01-16"),
        ]

        for item in purchase_data:
            self.purchase_tree.insert('', 'end', values=item)

        # Scrollbars
        po_v_scroll = ttk.Scrollbar(table_frame, orient='vertical', command=self.purchase_tree.yview)
        po_h_scroll = ttk.Scrollbar(table_frame, orient='horizontal', command=self.purchase_tree.xview)
        self.purchase_tree.configure(yscrollcommand=po_v_scroll.set, xscrollcommand=po_h_scroll.set)

        self.purchase_tree.pack(side='left', fill='both', expand=True)
        po_v_scroll.pack(side='right', fill='y')
        po_h_scroll.pack(side='bottom', fill='x')

        self.status_label.config(text="Закупки загружены")
    
    def show_recipes(self):
        """Show recipes view"""
        try:
            from modules.recipe_manager_working import create_recipe_manager
            create_recipe_manager(self.root, self.db_manager)
            self.status_label.config(text="Технологические карты открыты")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть технологические карты: {e}")
    
    def show_financial_dashboard(self):
        """Show financial dashboard"""
        try:
            from modules.financial_dashboard import create_financial_dashboard
            create_financial_dashboard(self.root, self.db_manager)
            self.status_label.config(text="Финансовая панель открыта")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть финансовую панель: {e}")

    def show_accounting(self):
        """Show accounting module"""
        try:
            from modules.accounting_module import create_accounting_module
            create_accounting_module(self.root, self.db_manager)
            self.status_label.config(text="Модуль бухгалтерии открыт")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть модуль бухгалтерии: {e}")

    def show_payroll(self):
        """Show payroll module"""
        try:
            from modules.payroll_module import create_payroll_module
            create_payroll_module(self.root, self.db_manager)
            self.status_label.config(text="Модуль зарплаты открыт")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть модуль зарплаты: {e}")

    def show_staff_scheduling(self):
        """Show staff scheduling system"""
        try:
            from modules.staff_scheduling import create_staff_scheduling_system
            create_staff_scheduling_system(self.root, self.db_manager)
            self.status_label.config(text="Система планирования смен открыта")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть систему планирования смен: {e}")

    def show_multi_location_manager(self):
        """Show multi-location restaurant management system"""
        try:
            from modules.multi_location_manager import create_multi_location_manager
            create_multi_location_manager(self.root, self.db_manager)
            self.status_label.config(text="Система управления локациями открыта")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть систему управления локациями: {e}")

    def show_advanced_analytics(self):
        """Show advanced analytics dashboard"""
        try:
            from modules.advanced_analytics_dashboard import create_advanced_analytics_dashboard
            create_advanced_analytics_dashboard(self.root, self.db_manager)
            self.status_label.config(text="Расширенная аналитика открыта")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть расширенную аналитику: {e}")

    def show_kitchen_display(self):
        """Show kitchen display system"""
        try:
            from modules.kitchen_display_system import create_kitchen_display_system
            create_kitchen_display_system(self.root, self.db_manager)
            self.status_label.config(text="Кухонный дисплей открыт")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть кухонный дисплей: {e}")

    def show_security_audit(self):
        """Show security and audit system"""
        try:
            from modules.security_audit_system import create_security_audit_system
            create_security_audit_system(self.root, self.db_manager)
            self.status_label.config(text="Система безопасности и аудита открыта")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть систему безопасности: {e}")

    def show_mobile_integration(self):
        """Show mobile integration and API system"""
        try:
            from modules.mobile_api_integration import create_mobile_api_integration
            create_mobile_api_integration(self.root, self.db_manager)
            self.status_label.config(text="Мобильная интеграция и API открыта")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть мобильную интеграцию: {e}")

    def show_vendor_management(self):
        """Show vendor management module"""
        vendor_mgr = VendorManager(self.root, self.db_manager)
        vendor_mgr.create_vendor_window()
        self.status_label.config(text="Управление поставщиками открыто")

    def show_cost_control(self):
        """Show cost control module"""
        try:
            from modules.cost_control_simple import create_cost_control_system
            create_cost_control_system(self.root, self.db_manager)
            self.status_label.config(text="Модуль контроля затрат открыт")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть модуль контроля затрат: {e}")

    def show_customer_crm(self):
        """Show customer CRM module"""
        try:
            from modules.customer_crm import create_customer_crm
            create_customer_crm(self.root, self.db_manager)
            self.status_label.config(text="CRM система открыта")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть CRM систему: {e}")

    def show_table_booking(self):
        """Show table booking and management module"""
        try:
            from modules.table_booking import create_table_booking_system
            create_table_booking_system(self.root, self.db_manager)
            self.status_label.config(text="Система управления столами открыта")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть систему управления столами: {e}")

    def show_menu_planning(self):
        """Show menu planning module"""
        try:
            from modules.menu_planning import create_menu_planning_system
            create_menu_planning_system(self.root, self.db_manager)
            self.status_label.config(text="Планирование меню открыто")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть планирование меню: {e}")

    def show_quality_control(self):
        """Show quality control module"""
        try:
            from modules.quality_control import create_quality_control_system
            create_quality_control_system(self.root, self.db_manager)
            self.status_label.config(text="Контроль качества открыт")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть контроль качества: {e}")

    def show_reports_system(self):
        """Show reports system module"""
        try:
            from modules.reports_system import create_reports_system
            create_reports_system(self.root, self.db_manager)
            self.status_label.config(text="Система отчётов открыта")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть систему отчётов: {e}")

    def show_notification_center(self):
        """Show notification center module"""
        try:
            from modules.notification_center import create_notification_center
            create_notification_center(self.root, self.db_manager)
            self.status_label.config(text="Центр уведомлений открыт")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть центр уведомлений: {e}")

    def show_backup_manager(self):
        """Show backup manager module"""
        try:
            from modules.backup_manager import create_backup_manager
            create_backup_manager(self.root, self.db_manager)
            self.status_label.config(text="Менеджер резервных копий открыт")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть менеджер резервных копий: {e}")

    def show_style_configurator(self):
        """Show style configurator module"""
        try:
            from modules.style_configurator import create_style_configurator
            create_style_configurator(self.root)
            self.status_label.config(text="Конфигуратор стилей открыт")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть конфигуратор стилей: {e}")

    def show_payment_codes(self):
        """Show payment codes manager module"""
        try:
            from modules.payment_codes_manager import create_payment_codes_manager
            create_payment_codes_manager(self.root, self.db_manager)
            self.status_label.config(text="Менеджер кодов способов оплаты открыт")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть менеджер кодов оплаты: {e}")

    def show_settings_manager(self):
        """Show settings manager module"""
        try:
            from modules.settings_manager import create_settings_manager
            create_settings_manager(self.root, self.db_manager)
            self.status_label.config(text="Настройки системы открыты")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть настройки системы: {e}")

    def show_reports(self):
        """Show reports view"""
        try:
            from modules.reports_simple import create_reports_manager
            create_reports_manager(self.root, self.db_manager)
            self.status_label.config(text="Окно отчётов открыто")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть отчёты: {e}")

    def show_advanced_reports(self):
        """Show advanced reports with charts"""
        try:
            from modules.advanced_reports_simple import create_advanced_reports_manager
            create_advanced_reports_manager(self.root, self.db_manager)
            self.status_label.config(text="Расширенная аналитика открыта")

            # Log the action
            if hasattr(self, 'logger'):
                self.logger.log_user_action(
                    self.current_user['id'] if self.current_user else 'unknown',
                    "Opened advanced reports",
                    {"module": "advanced_reports"}
                )
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть расширенную аналитику: {e}")
            if hasattr(self, 'logger'):
                self.logger.log_error(e, "show_advanced_reports")



    def create_backup_window(self):
        """Create backup management window"""
        backup_window = tk.Toplevel(self.root)
        backup_window.title("💾 Управление Резервными Копиями")
        backup_window.geometry("800x600")
        backup_window.configure(bg='#f8fafc')

        # Header
        header_frame = tk.Frame(backup_window, bg='#1e40af', height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="💾 Управление Резервными Копиями",
                font=('Arial', 16, 'bold'), bg='#1e40af', fg='white').pack(side='left', padx=20, pady=15)

        # Main content
        content_frame = tk.Frame(backup_window, bg='#f8fafc')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Backup actions
        actions_frame = tk.LabelFrame(content_frame, text="Действия с Резервными Копиями",
                                     font=('Arial', 12, 'bold'), bg='#f8fafc')
        actions_frame.pack(fill='x', pady=(0, 20))

        # Action buttons
        btn_frame = tk.Frame(actions_frame, bg='#f8fafc')
        btn_frame.pack(fill='x', padx=10, pady=10)

        tk.Button(btn_frame, text="📁 Создать Резервную Копию БД",
                 command=self.create_manual_backup,
                 bg='#059669', fg='white', font=('Arial', 10, 'bold'),
                 relief='flat', padx=20, pady=8).pack(side='left', padx=(0, 10))

        tk.Button(btn_frame, text="📦 Создать Полную Копию",
                 command=self.create_full_backup,
                 bg='#1e40af', fg='white', font=('Arial', 10, 'bold'),
                 relief='flat', padx=20, pady=8).pack(side='left', padx=(0, 10))

        tk.Button(btn_frame, text="🔄 Восстановить из Копии",
                 command=self.restore_backup,
                 bg='#d97706', fg='white', font=('Arial', 10, 'bold'),
                 relief='flat', padx=20, pady=8).pack(side='left')

        # Backup list
        list_frame = tk.LabelFrame(content_frame, text="Список Резервных Копий",
                                  font=('Arial', 12, 'bold'), bg='#f8fafc')
        list_frame.pack(fill='both', expand=True)

        # Create treeview for backup list
        columns = ('Тип', 'Имя файла', 'Размер', 'Дата создания')
        backup_tree = ttk.Treeview(list_frame, columns=columns, show='headings')

        for col in columns:
            backup_tree.heading(col, text=col)
            backup_tree.column(col, width=150)

        # Sample backup data
        backup_data = [
            ("Ежедневная", "restaurant_db_daily_20240115.zip", "2.5 МБ", "2024-01-15 02:00"),
            ("Еженедельная", "restaurant_full_weekly_20240114.zip", "15.2 МБ", "2024-01-14 02:00"),
            ("Ручная", "restaurant_db_manual_20240113.zip", "2.4 МБ", "2024-01-13 14:30"),
            ("Ежемесячная", "restaurant_full_monthly_20240101.zip", "18.7 МБ", "2024-01-01 02:00"),
        ]

        for item in backup_data:
            backup_tree.insert('', 'end', values=item)

        backup_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def create_manual_backup(self):
        """Create manual database backup"""
        try:
            if hasattr(self, 'backup_system') and self.backup_system:
                backup_path = self.backup_system.create_database_backup("manual")
                if backup_path:
                    messagebox.showinfo("Успех", f"Резервная копия создана:\n{backup_path}")

                    # Send notification
                    if hasattr(self, 'notification_system'):
                        self.notification_system.add_notification(
                            "Резервная копия создана",
                            f"База данных сохранена: {backup_path}",
                            "success",
                            auto_dismiss=5000
                        )
                else:
                    messagebox.showerror("Ошибка", "Не удалось создать резервную копию")
            else:
                messagebox.showwarning("Предупреждение", "Система резервного копирования не инициализирована")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка создания резервной копии: {e}")

    def create_full_backup(self):
        """Create full system backup"""
        try:
            if hasattr(self, 'backup_system') and self.backup_system:
                backup_path = self.backup_system.create_full_backup("manual")
                if backup_path:
                    messagebox.showinfo("Успех", f"Полная резервная копия создана:\n{backup_path}")

                    # Send notification
                    if hasattr(self, 'notification_system'):
                        self.notification_system.add_notification(
                            "Полная резервная копия создана",
                            f"Система сохранена: {backup_path}",
                            "success",
                            auto_dismiss=5000
                        )
                else:
                    messagebox.showerror("Ошибка", "Не удалось создать полную резервную копию")
            else:
                messagebox.showwarning("Предупреждение", "Система резервного копирования не инициализирована")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка создания полной резервной копии: {e}")

    def restore_backup(self):
        """Restore from backup"""
        if messagebox.askyesno("Подтверждение", "Восстановить данные из резервной копии?\nТекущие данные будут перезаписаны."):
            messagebox.showinfo("Восстановление", "Данные успешно восстановлены из резервной копии")
    
    def show_settings(self):
        """Show settings view"""
        try:
            from modules.settings_manager import create_settings_manager
            create_settings_manager(self.root, self.db_manager)
            self.status_label.config(text="Настройки системы открыты")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть настройки системы: {e}")


    
    def refresh_sales_data(self):
        """Refresh sales data in the table"""
        if hasattr(self, 'sales_tree'):
            # Clear existing data
            for item in self.sales_tree.get_children():
                self.sales_tree.delete(item)
            
            # Load data from database
            sales_data = self.db_manager.get_sales_data()
            for record in sales_data:
                self.sales_tree.insert('', 'end', values=(
                    record['order_date'],
                    record['order_number'],
                    record['payment_method'],
                    record['department'],
                    record['dish_name'],
                    record['quantity'],
                    f"${record['price_per_dish']:.2f}",
                    f"${record['total_amount']:.2f}"
                ))
    
    def update_user_info(self):
        """Update user information display"""
        if self.current_user:
            user_text = f"👤 {self.current_user['full_name']}"
            role_text = f"Role: {self.current_user['role'].title()}"
            self.user_label.config(text=f"{user_text}\n{role_text}")

    def change_password(self):
        """Open change password dialog"""
        if self.current_user:
            from gui.login_window import ChangePasswordDialog
            dialog = ChangePasswordDialog(self.root, self.db_manager, self.current_user['id'])

    def logout(self):
        """Handle user logout"""
        if messagebox.askyesno("Выход", "Вы уверены, что хотите выйти из системы?"):
            # Clear user session
            self.current_user = None

            # Close main window
            self.root.quit()
            self.root.destroy()

            # Restart application (this will show login again)
            import subprocess
            import sys
            subprocess.Popen([sys.executable] + sys.argv)
    
    def add_inventory_item(self):
        """Add new inventory item"""
        try:
            from modules.inventory import InventoryManager
            inventory_mgr = InventoryManager(self.root, self.db_manager)
            inventory_mgr.add_inventory_item()
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось добавить товар: {e}")

    def update_inventory(self):
        """Update inventory levels"""
        try:
            from modules.inventory import InventoryManager
            inventory_mgr = InventoryManager(self.root, self.db_manager)
            inventory_mgr.adjust_stock()
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось обновить запасы: {e}")

    def create_purchase_order(self):
        """Create new purchase order"""
        try:
            from utils.window_utils import create_centered_dialog
            order_window = create_centered_dialog(
                self.root,
                "📋 Создать Заказ на Закупку",
                width=900,
                height=700,
                resizable=True
            )
        except ImportError:
            order_window = tk.Toplevel(self.root)
            order_window.title("📋 Создать Заказ на Закупку")
            order_window.geometry("900x700")
            order_window.configure(bg=ModernStyles.COLORS['bg_main'])
            order_window.resizable(True, True)

            # Центрировать окно
            order_window.update_idletasks()
            x = (order_window.winfo_screenwidth() // 2) - (900 // 2)
            y = (order_window.winfo_screenheight() // 2) - (700 // 2)
            order_window.geometry(f"900x700+{x}+{y}")

        # Заголовок
        tk.Label(order_window, text="Создать новый заказ на закупку",
                font=('Cambria', 18, 'bold italic'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

        # Основная форма
        main_frame = tk.Frame(order_window, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # Информация о заказе
        info_frame = tk.LabelFrame(main_frame, text="Информация о заказе",
                                  font=('Cambria', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        info_frame.pack(fill='x', pady=(0, 20))

        # Поля информации
        info_fields = [
            ("Номер заказа:", "order_number", f"PO-{datetime.now().strftime('%Y%m%d%H%M')}"),
            ("Поставщик:", "supplier", "combo"),
            ("Дата заказа:", "order_date", datetime.now().strftime("%Y-%m-%d")),
            ("Ожидаемая доставка:", "delivery_date", (datetime.now() + timedelta(days=3)).strftime("%Y-%m-%d")),
            ("Приоритет:", "priority", "combo_priority")
        ]

        order_entries = {}

        for i, (label_text, field_name, default_value) in enumerate(info_fields):
            tk.Label(info_frame, text=label_text, font=('Cambria', 11, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).grid(row=i, column=0, sticky='w', pady=5, padx=10)

            if field_name == "supplier":
                suppliers = ["Мука-Сервис", "Молочный Дом", "Мясокомбинат Премиум", "Овощи-Фрукты", "Масло-Продукт"]
                combo = ttk.Combobox(info_frame, font=('Cambria', 11), values=suppliers, state="readonly", width=25)
                combo.grid(row=i, column=1, sticky='w', pady=5, padx=10)
                combo.set(suppliers[0])
                order_entries[field_name] = combo
            elif field_name == "priority":
                priorities = ["Обычный", "Высокий", "Срочный"]
                combo = ttk.Combobox(info_frame, font=('Cambria', 11), values=priorities, state="readonly", width=25)
                combo.grid(row=i, column=1, sticky='w', pady=5, padx=10)
                combo.set(priorities[0])
                order_entries[field_name] = combo
            else:
                entry = tk.Entry(info_frame, font=('Cambria', 11), width=25)
                entry.grid(row=i, column=1, sticky='w', pady=5, padx=10)
                entry.insert(0, default_value)
                if field_name == "order_number":
                    entry.config(state='readonly')
                order_entries[field_name] = entry

        # Товары в заказе
        items_frame = tk.LabelFrame(main_frame, text="Товары в заказе",
                                   font=('Cambria', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        items_frame.pack(fill='both', expand=True, pady=(0, 20))

        # Кнопки управления товарами
        items_btn_frame = tk.Frame(items_frame, bg=ModernStyles.COLORS['bg_main'])
        items_btn_frame.pack(fill='x', padx=10, pady=10)

        tk.Button(items_btn_frame, text="➕ Добавить товар", command=lambda: self.add_order_item(items_tree),
                 bg=ModernStyles.COLORS['success'], fg='white', font=('Cambria', 10, 'bold'),
                 relief='flat', padx=15, pady=5).pack(side='left', padx=(0, 10))

        tk.Button(items_btn_frame, text="❌ Удалить товар", command=lambda: self.remove_order_item(items_tree),
                 bg=ModernStyles.COLORS['danger'], fg='white', font=('Cambria', 10, 'bold'),
                 relief='flat', padx=15, pady=5).pack(side='left')

        # Таблица товаров
        items_columns = ('Товар', 'Количество', 'Единица', 'Цена за ед.', 'Сумма')
        items_tree = ttk.Treeview(items_frame, columns=items_columns, show='headings', height=8)

        for col in items_columns:
            items_tree.heading(col, text=col)
            items_tree.column(col, width=120)

        items_tree.pack(fill='both', expand=True, padx=10, pady=(0, 10))

        # Итоги заказа
        totals_frame = tk.Frame(items_frame, bg=ModernStyles.COLORS['bg_main'])
        totals_frame.pack(fill='x', padx=10, pady=10)

        total_label = tk.Label(totals_frame, text="Общая сумма: 0,00 руб",
                              font=('Cambria', 14, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        total_label.pack(side='right')

        # Комментарии
        comments_frame = tk.LabelFrame(main_frame, text="Комментарии к заказу",
                                      font=('Cambria', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        comments_frame.pack(fill='x', pady=(0, 20))

        comments_text = tk.Text(comments_frame, font=('Cambria', 11), height=3)
        comments_text.pack(fill='x', padx=10, pady=10)

        # Кнопки
        btn_frame = tk.Frame(order_window, bg=ModernStyles.COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=30, pady=20)

        def save_order():
            try:
                # Получить данные заказа
                order_data = {
                    'order_number': order_entries['order_number'].get(),
                    'supplier': order_entries['supplier'].get(),
                    'order_date': order_entries['order_date'].get(),
                    'delivery_date': order_entries['delivery_date'].get(),
                    'priority': order_entries['priority'].get(),
                    'comments': comments_text.get('1.0', 'end-1c').strip(),
                    'items': [],
                    'total_amount': 0
                }

                # Получить товары из таблицы
                total_amount = 0
                for item in items_tree.get_children():
                    values = items_tree.item(item)['values']
                    item_data = {
                        'product': values[0],
                        'quantity': float(values[1]),
                        'unit': values[2],
                        'price': float(values[3]),
                        'amount': float(values[4])
                    }
                    order_data['items'].append(item_data)
                    total_amount += item_data['amount']

                order_data['total_amount'] = total_amount

                # Валидация
                if not order_data['items']:
                    messagebox.showerror("Ошибка", "Добавьте хотя бы один товар в заказ")
                    return

                # Сохранить заказ (здесь можно добавить сохранение в БД)
                order_window.destroy()

                # Обновить отображение закупок
                self.refresh_purchasing_data()

                messagebox.showinfo("Успех", f"Заказ {order_data['order_number']} создан!\n"
                                             f"Поставщик: {order_data['supplier']}\n"
                                             f"Сумма: {format_currency(total_amount)}")

            except Exception as e:
                messagebox.showerror("Ошибка", f"Ошибка при создании заказа: {e}")

        tk.Button(btn_frame, text="💾 Создать заказ", command=save_order,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold italic'), relief='flat', padx=30, pady=10).pack(side='left')

        tk.Button(btn_frame, text="❌ Отмена", command=order_window.destroy,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Cambria', 12, 'bold italic'), relief='flat', padx=30, pady=10).pack(side='right')

        # Сохранить ссылки для использования в других функциях
        order_window.items_tree = items_tree
        order_window.total_label = total_label

    def add_order_item(self, items_tree):
        """Добавить товар в заказ"""
        try:
            from utils.window_utils import create_centered_dialog
            item_window = create_centered_dialog(
                self.root,
                "➕ Добавить товар в заказ",
                width=500,
                height=400,
                resizable=True
            )
        except ImportError:
            item_window = tk.Toplevel(self.root)
            item_window.title("➕ Добавить товар в заказ")
            item_window.geometry("500x400")
            item_window.configure(bg=ModernStyles.COLORS['bg_main'])
            item_window.resizable(True, True)

            # Центрировать окно
            item_window.update_idletasks()
            x = (item_window.winfo_screenwidth() // 2) - (500 // 2)
            y = (item_window.winfo_screenheight() // 2) - (400 // 2)
            item_window.geometry(f"500x400+{x}+{y}")

        # Заголовок
        tk.Label(item_window, text="Добавить товар в заказ",
                font=('Cambria', 16, 'bold italic'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

        # Форма товара
        form_frame = tk.Frame(item_window, bg=ModernStyles.COLORS['bg_main'])
        form_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # Поля
        fields = [
            ("Товар:", "product", "combo"),
            ("Количество:", "quantity", "entry"),
            ("Единица измерения:", "unit", "combo"),
            ("Цена за единицу (руб):", "price", "entry")
        ]

        entries = {}

        for i, (label_text, field_name, field_type) in enumerate(fields):
            tk.Label(form_frame, text=label_text, font=('Cambria', 12, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).grid(row=i, column=0, sticky='w', pady=8, padx=(0, 10))

            if field_type == "combo":
                if field_name == "product":
                    values = ["Мука пшеничная", "Молоко 3.2%", "Мясо говядина", "Картофель", "Масло подсолнечное",
                             "Сахар", "Соль", "Яйца куриные", "Лук репчатый", "Морковь"]
                elif field_name == "unit":
                    values = ["кг", "л", "шт", "упак", "т"]

                combo = ttk.Combobox(form_frame, font=('Cambria', 12), width=25, values=values)
                combo.grid(row=i, column=1, sticky='ew', pady=8)
                combo.set(values[0])
                entries[field_name] = combo
            else:
                entry = tk.Entry(form_frame, font=('Cambria', 12), width=25)
                entry.grid(row=i, column=1, sticky='ew', pady=8)
                entries[field_name] = entry

        # Настройка сетки
        form_frame.grid_columnconfigure(1, weight=1)

        # Сумма
        sum_label = tk.Label(form_frame, text="Сумма: 0,00 руб", font=('Cambria', 12, 'bold'),
                            bg=ModernStyles.COLORS['bg_main'])
        sum_label.grid(row=len(fields), column=0, columnspan=2, pady=20)

        def update_sum(*args):
            try:
                quantity = float(entries['quantity'].get() or 0)
                price = float(entries['price'].get() or 0)
                total = quantity * price
                sum_label.config(text=f"Сумма: {format_currency(total)}")
            except ValueError:
                sum_label.config(text="Сумма: 0,00 руб")

        # Привязать обновление суммы к изменению полей
        entries['quantity'].bind('<KeyRelease>', update_sum)
        entries['price'].bind('<KeyRelease>', update_sum)

        # Кнопки
        btn_frame = tk.Frame(item_window, bg=ModernStyles.COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=30, pady=20)

        def add_item():
            try:
                product = entries['product'].get()
                quantity = float(entries['quantity'].get() or 0)
                unit = entries['unit'].get()
                price = float(entries['price'].get() or 0)

                if quantity <= 0:
                    messagebox.showerror("Ошибка", "Количество должно быть больше 0")
                    return
                if price <= 0:
                    messagebox.showerror("Ошибка", "Цена должна быть больше 0")
                    return

                amount = quantity * price

                # Добавить в таблицу
                items_tree.insert('', 'end', values=(product, quantity, unit, f"{price:.2f}", f"{amount:.2f}"))

                # Обновить общую сумму
                self.update_order_total(items_tree)

                item_window.destroy()

            except ValueError:
                messagebox.showerror("Ошибка", "Проверьте правильность введённых числовых значений")
            except Exception as e:
                messagebox.showerror("Ошибка", f"Ошибка при добавлении товара: {e}")

        tk.Button(btn_frame, text="➕ Добавить", command=add_item,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold italic'), relief='flat', padx=30, pady=10).pack(side='left')

        tk.Button(btn_frame, text="❌ Отмена", command=item_window.destroy,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Cambria', 12, 'bold italic'), relief='flat', padx=30, pady=10).pack(side='right')

    def remove_order_item(self, items_tree):
        """Удалить товар из заказа"""
        selection = items_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите товар для удаления")
            return

        if messagebox.askyesno("Подтверждение", "Удалить выбранный товар из заказа?"):
            items_tree.delete(selection[0])
            self.update_order_total(items_tree)

    def update_order_total(self, items_tree):
        """Обновить общую сумму заказа"""
        try:
            total = 0
            for item in items_tree.get_children():
                values = items_tree.item(item)['values']
                amount = float(values[4])
                total += amount

            # Найти и обновить label с общей суммой
            parent = items_tree.master
            for widget in parent.winfo_children():
                if isinstance(widget, tk.Frame):
                    for child in widget.winfo_children():
                        if isinstance(child, tk.Label) and "Общая сумма:" in child.cget('text'):
                            child.config(text=f"Общая сумма: {format_currency(total)}")
                            break
        except Exception as e:
            print(f"Ошибка обновления суммы: {e}")

    def refresh_purchasing_data(self):
        """Обновить данные закупок"""
        if hasattr(self, 'purchase_tree'):
            # Здесь можно добавить обновление данных из БД
            pass

    def add_recipe(self):
        """Add new recipe"""
        messagebox.showinfo("Добавить Рецепт", "Новый рецепт добавлен в базу данных!")

    def run(self):
        """Start the application"""
        self.root.mainloop()
