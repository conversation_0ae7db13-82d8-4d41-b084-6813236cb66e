"""
Utility Helper Functions for Restaurant Management System
"""

import os
import json
import csv
from datetime import datetime, date
from typing import Any, Dict, List, Optional
import tkinter as tk
from tkinter import messagebox

class DateHelper:
    """Helper functions for date operations"""
    
    @staticmethod
    def format_date(date_obj, format_str='%Y-%m-%d'):
        """Format date object to string"""
        if isinstance(date_obj, str):
            return date_obj
        if isinstance(date_obj, (date, datetime)):
            return date_obj.strftime(format_str)
        return str(date_obj)
    
    @staticmethod
    def parse_date(date_str, formats=None):
        """Parse date string to date object"""
        if formats is None:
            formats = ['%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%d-%m-%Y']
        
        for fmt in formats:
            try:
                return datetime.strptime(str(date_str), fmt).date()
            except ValueError:
                continue
        
        # If no format works, return current date
        return datetime.now().date()
    
    @staticmethod
    def get_date_range(period='month'):
        """Get date range for common periods"""
        today = datetime.now().date()
        
        if period == 'today':
            return today, today
        elif period == 'week':
            start = today - timedelta(days=today.weekday())
            end = start + timedelta(days=6)
            return start, end
        elif period == 'month':
            start = today.replace(day=1)
            if today.month == 12:
                end = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                end = today.replace(month=today.month + 1, day=1) - timedelta(days=1)
            return start, end
        elif period == 'year':
            start = today.replace(month=1, day=1)
            end = today.replace(month=12, day=31)
            return start, end
        
        return today, today

class NumberHelper:
    """Helper functions for number operations"""
    
    @staticmethod
    def safe_float(value, default=0.0):
        """Safely convert value to float"""
        try:
            return float(value) if value is not None else default
        except (ValueError, TypeError):
            return default
    
    @staticmethod
    def safe_int(value, default=0):
        """Safely convert value to int"""
        try:
            return int(value) if value is not None else default
        except (ValueError, TypeError):
            return default
    
    @staticmethod
    def format_currency(amount, currency_symbol='$'):
        """Format number as currency"""
        try:
            return f"{currency_symbol}{float(amount):,.2f}"
        except (ValueError, TypeError):
            return f"{currency_symbol}0.00"
    
    @staticmethod
    def calculate_percentage(part, total):
        """Calculate percentage"""
        try:
            if total == 0:
                return 0.0
            return (float(part) / float(total)) * 100
        except (ValueError, TypeError, ZeroDivisionError):
            return 0.0
    
    @staticmethod
    def calculate_vat(amount, vat_rate):
        """Calculate VAT amount"""
        try:
            return float(amount) * (float(vat_rate) / 100)
        except (ValueError, TypeError):
            return 0.0

class FileHelper:
    """Helper functions for file operations"""
    
    @staticmethod
    def ensure_directory(directory_path):
        """Ensure directory exists"""
        if not os.path.exists(directory_path):
            os.makedirs(directory_path)
    
    @staticmethod
    def read_json(file_path, default=None):
        """Read JSON file safely"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return default or {}
    
    @staticmethod
    def write_json(file_path, data):
        """Write data to JSON file"""
        try:
            FileHelper.ensure_directory(os.path.dirname(file_path))
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"Error writing JSON file: {e}")
            return False
    
    @staticmethod
    def export_to_csv(data, file_path, headers=None):
        """Export data to CSV file"""
        try:
            FileHelper.ensure_directory(os.path.dirname(file_path))
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                if data and isinstance(data[0], dict):
                    # Data is list of dictionaries
                    fieldnames = headers or list(data[0].keys())
                    writer = csv.DictWriter(f, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(data)
                else:
                    # Data is list of lists
                    writer = csv.writer(f)
                    if headers:
                        writer.writerow(headers)
                    writer.writerows(data)
            return True
        except Exception as e:
            print(f"Error exporting to CSV: {e}")
            return False

class ValidationHelper:
    """Helper functions for data validation"""
    
    @staticmethod
    def validate_email(email):
        """Validate email format"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def validate_required_fields(data, required_fields):
        """Validate that required fields are present and not empty"""
        missing_fields = []
        for field in required_fields:
            if field not in data or not str(data[field]).strip():
                missing_fields.append(field)
        return missing_fields
    
    @staticmethod
    def validate_numeric_range(value, min_val=None, max_val=None):
        """Validate numeric value is within range"""
        try:
            num_val = float(value)
            if min_val is not None and num_val < min_val:
                return False
            if max_val is not None and num_val > max_val:
                return False
            return True
        except (ValueError, TypeError):
            return False

class UIHelper:
    """Helper functions for UI operations"""
    
    @staticmethod
    def center_window(window, width=None, height=None):
        """Center window on screen"""
        window.update_idletasks()
        
        if width is None:
            width = window.winfo_width()
        if height is None:
            height = window.winfo_height()
        
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        window.geometry(f"{width}x{height}+{x}+{y}")
    
    @staticmethod
    def show_error(title, message):
        """Show error message dialog"""
        messagebox.showerror(title, message)
    
    @staticmethod
    def show_info(title, message):
        """Show info message dialog"""
        messagebox.showinfo(title, message)
    
    @staticmethod
    def show_warning(title, message):
        """Show warning message dialog"""
        messagebox.showwarning(title, message)
    
    @staticmethod
    def ask_yes_no(title, message):
        """Ask yes/no question"""
        return messagebox.askyesno(title, message)
    
    @staticmethod
    def create_tooltip(widget, text):
        """Create tooltip for widget"""
        def on_enter(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root + 10}+{event.y_root + 10}")
            
            label = tk.Label(tooltip, text=text, background="lightyellow",
                           relief="solid", borderwidth=1, font=("Arial", 9))
            label.pack()
            
            widget.tooltip = tooltip
        
        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip
        
        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)

class ConfigHelper:
    """Helper functions for configuration management"""
    
    CONFIG_FILE = "config.json"
    
    @staticmethod
    def load_config():
        """Load application configuration"""
        default_config = {
            "database": {
                "path": "restaurant_system.db",
                "backup_interval": 24  # hours
            },
            "ui": {
                "theme": "modern",
                "language": "en"
            },
            "business": {
                "currency": "$",
                "tax_rate": 10.0,
                "business_name": "Restaurant Management System"
            },
            "reports": {
                "export_format": "pdf",
                "auto_backup": True
            }
        }
        
        config = FileHelper.read_json(ConfigHelper.CONFIG_FILE, default_config)
        return {**default_config, **config}  # Merge with defaults
    
    @staticmethod
    def save_config(config):
        """Save application configuration"""
        return FileHelper.write_json(ConfigHelper.CONFIG_FILE, config)
    
    @staticmethod
    def get_setting(key, default=None):
        """Get specific setting value"""
        config = ConfigHelper.load_config()
        keys = key.split('.')
        
        value = config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    @staticmethod
    def set_setting(key, value):
        """Set specific setting value"""
        config = ConfigHelper.load_config()
        keys = key.split('.')
        
        current = config
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        current[keys[-1]] = value
        return ConfigHelper.save_config(config)

# Logging functions for compatibility
def log_info(message, module_name="System"):
    """Log informational message"""
    try:
        from .error_handling import log_info as error_log_info
        error_log_info(message, module_name)
    except ImportError:
        print(f"[INFO] {module_name}: {message}")

def log_warning(message, module_name="System"):
    """Log warning message"""
    try:
        from .error_handling import log_warning as error_log_warning
        error_log_warning(message, module_name)
    except ImportError:
        print(f"[WARNING] {module_name}: {message}")

def log_error(message, module_name="System"):
    """Log error message"""
    try:
        from .error_handling import error_handler
        error_handler.log_error(message, module_name)
    except ImportError:
        print(f"[ERROR] {module_name}: {message}")

def handle_module_error(error, module_name, operation="открытие модуля"):
    """Handle module-specific errors"""
    try:
        from .error_handling import handle_module_error as error_handle_module_error
        error_handle_module_error(error, module_name)
    except ImportError:
        print(f"[ERROR] {module_name}: {error}")
        try:
            from tkinter import messagebox
            messagebox.showerror("Ошибка модуля", f"Не удалось выполнить операцию '{operation}' в модуле '{module_name}':\n{error}")
        except:
            pass
