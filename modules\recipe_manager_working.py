"""
Рабочая система управления рецептами с улучшенными стилями
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from gui.enhanced_styles import EnhancedStyles
import json
import os

class RecipeManagerWorking:
    """Рабочий менеджер рецептов с улучшенными стилями"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.current_recipe = None
        self.recipes_file = "data/recipes.json"

        # Создать папку data если не существует
        os.makedirs("data", exist_ok=True)

        # Загрузить рецепты из файла или использовать данные по умолчанию
        self.recipes = self.load_recipes_from_file()

        # Инициализировать полуфабрикаты (подрецепты)
        self.sub_recipes = {
            "Соус Цезарь": {
                "category": "Соусы",
                "yield": "200 мл",
                "cost": 45.00,
                "shelf_life": "3 дня",
                "ingredients": [
                    {"name": "Майонез", "amount": "100", "unit": "г", "cost": 15.00},
                    {"name": "Пармезан тертый", "amount": "30", "unit": "г", "cost": 25.00},
                    {"name": "Чеснок", "amount": "2", "unit": "зубчика", "cost": 5.00}
                ]
            },
            "Маринованная курица": {
                "category": "Полуфабрикаты мясные",
                "yield": "500 г",
                "cost": 120.00,
                "shelf_life": "2 дня",
                "ingredients": [
                    {"name": "Куриная грудка", "amount": "500", "unit": "г", "cost": 100.00},
                    {"name": "Соевый соус", "amount": "50", "unit": "мл", "cost": 15.00},
                    {"name": "Специи", "amount": "10", "unit": "г", "cost": 5.00}
                ]
            }
        }

    def get_default_recipes(self):
        """Получить рецепты по умолчанию"""
        return {
            "Борщ украинский": {
                "category": "Супы",
                "portions": 4,
                "prep_time": "45 мин",
                "cook_time": "60 мин",
                "difficulty": "Средняя",
                "cost": 185.50,
                "price": 280.00,
                "ingredients": [
                    {"name": "Говядина", "amount": 300, "unit": "г", "cost": 180.00},
                    {"name": "Свёкла", "amount": 150, "unit": "г", "cost": 12.00},
                    {"name": "Капуста", "amount": 200, "unit": "г", "cost": 15.00},
                    {"name": "Морковь", "amount": 100, "unit": "г", "cost": 8.00},
                    {"name": "Лук", "amount": 80, "unit": "г", "cost": 6.00},
                    {"name": "Картофель", "amount": 200, "unit": "г", "cost": 14.00},
                    {"name": "Томатная паста", "amount": 50, "unit": "г", "cost": 8.00},
                    {"name": "Сметана", "amount": 50, "unit": "г", "cost": 25.00}
                ],
                "instructions": [
                    "Отварить говядину до готовности (40-50 мин)",
                    "Нарезать все овощи соломкой",
                    "Обжарить лук и морковь",
                    "Добавить свёклу и тушить 10 мин",
                    "Добавить капусту и картофель в бульон",
                    "Варить 15 мин, добавить зажарку",
                    "Добавить томатную пасту и специи",
                    "Варить ещё 10 мин",
                    "Подавать со сметаной"
                ]
            },
            "Стейк рибай": {
                "category": "Горячие блюда",
                "portions": 1,
                "prep_time": "10 мин",
                "cook_time": "15 мин",
                "difficulty": "Высокая",
                "cost": 285.00,
                "price": 450.00,
                "ingredients": [
                    {"name": "Стейк рибай", "amount": 250, "unit": "г", "cost": 250.00},
                    {"name": "Масло сливочное", "amount": 20, "unit": "г", "cost": 15.00},
                    {"name": "Соль морская", "amount": 5, "unit": "г", "cost": 3.00},
                    {"name": "Перец чёрный", "amount": 2, "unit": "г", "cost": 5.00},
                    {"name": "Розмарин", "amount": 3, "unit": "г", "cost": 12.00}
                ],
                "instructions": [
                    "Достать стейк из холодильника за 30 мин",
                    "Разогреть сковороду до максимума",
                    "Посолить и поперчить стейк",
                    "Обжарить по 2-3 мин с каждой стороны",
                    "Добавить масло и розмарин",
                    "Поливать стейк маслом 1-2 мин",
                    "Дать отдохнуть 5 мин перед подачей"
                ]
            },
            "Салат Цезарь": {
                "category": "Салаты",
                "portions": 2,
                "prep_time": "20 мин",
                "cook_time": "0 мин",
                "difficulty": "Лёгкая",
                "cost": 113.00,
                "price": 180.00,
                "ingredients": [
                    {"name": "Салат романо", "amount": 150, "unit": "г", "cost": 25.00},
                    {"name": "Куриная грудка", "amount": 100, "unit": "г", "cost": 35.00},
                    {"name": "Сыр пармезан", "amount": 30, "unit": "г", "cost": 45.00},
                    {"name": "Сухарики", "amount": 20, "unit": "г", "cost": 8.00}
                ],
                "semi_products": [
                    {
                        "name": "Соус Цезарь",
                        "amount": 40,
                        "unit": "мл",
                        "cost": 18.00,
                        "ingredients": [
                            {"name": "Майонез", "amount": 20, "unit": "мл", "cost": 6.00},
                            {"name": "Анчоусы", "amount": 4, "unit": "г", "cost": 8.00},
                            {"name": "Чеснок", "amount": 2, "unit": "г", "cost": 2.00},
                            {"name": "Лимонный сок", "amount": 4, "unit": "мл", "cost": 2.00}
                        ]
                    }
                ],
                "instructions": [
                    "Отварить куриную грудку",
                    "Нарезать салат крупными кусками",
                    "Нарезать курицу полосками",
                    "Натереть пармезан",
                    "Приготовить соус Цезарь",
                    "Смешать все ингредиенты",
                    "Заправить соусом",
                    "Подавать немедленно"
                ]
            }
        }
        
        # Подрецепты
        self.sub_recipes = {
            "Соус Цезарь": {
                "category": "Соусы",
                "yield": "200 мл",
                "shelf_life": "3 дня",
                "cost": 45.00,
                "ingredients": [
                    {"name": "Майонез", "amount": 100, "unit": "мл", "cost": 15.00},
                    {"name": "Анчоусы", "amount": 20, "unit": "г", "cost": 25.00},
                    {"name": "Чеснок", "amount": 10, "unit": "г", "cost": 3.00},
                    {"name": "Лимонный сок", "amount": 20, "unit": "мл", "cost": 5.00}
                ]
            },
            "Бульон говяжий": {
                "category": "Основы",
                "yield": "2 л",
                "shelf_life": "2 дня",
                "cost": 85.00,
                "ingredients": [
                    {"name": "Говяжьи кости", "amount": 500, "unit": "г", "cost": 50.00},
                    {"name": "Лук", "amount": 100, "unit": "г", "cost": 8.00},
                    {"name": "Морковь", "amount": 80, "unit": "г", "cost": 6.00},
                    {"name": "Сельдерей", "amount": 50, "unit": "г", "cost": 12.00},
                    {"name": "Лавровый лист", "amount": 3, "unit": "шт", "cost": 5.00}
                ]
            }
        }

    def load_recipes_from_file(self):
        """Загрузить рецепты из файла или вернуть данные по умолчанию"""
        try:
            if os.path.exists(self.recipes_file):
                with open(self.recipes_file, 'r', encoding='utf-8') as f:
                    recipes = json.load(f)
                    print(f"DEBUG: Loaded {len(recipes)} recipes from file")
                    return recipes
            else:
                print("DEBUG: Recipes file not found, using default recipes")
                default_recipes = self.get_default_recipes()
                # Сохранить данные по умолчанию в файл
                self.save_recipes_to_file_internal(default_recipes)
                return default_recipes
        except Exception as e:
            print(f"ERROR loading recipes from file: {e}")
            print("DEBUG: Using default recipes due to error")
            return self.get_default_recipes()

    def save_recipes_to_file(self):
        """Сохранить рецепты в файл"""
        return self.save_recipes_to_file_internal(self.recipes)

    def save_recipes_to_file_internal(self, recipes_data):
        """Внутренний метод сохранения рецептов в файл"""
        try:
            # Создать папку data если не существует
            os.makedirs(os.path.dirname(self.recipes_file), exist_ok=True)

            with open(self.recipes_file, 'w', encoding='utf-8') as f:
                json.dump(recipes_data, f, ensure_ascii=False, indent=2)

            print(f"DEBUG: Successfully saved {len(recipes_data)} recipes to file")
            return True
        except Exception as e:
            print(f"ERROR saving recipes to file: {e}")
            return False

    def create_window(self):
        """Создать окно управления рецептами на полный экран"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("📋 Управление Рецептами")
        self.window.configure(bg='white')
        self.window.resizable(True, True)

        # Открыть на полный экран
        self.window.state('zoomed')  # Для Windows
        # Альтернативно для других ОС:
        # self.window.attributes('-zoomed', True)  # Для Linux
        # self.window.attributes('-fullscreen', True)  # Для macOS

        self.create_interface()
        return self.window
    
    def create_interface(self):
        """Создать интерфейс с улучшенными стилями Cambria"""
        # Настроить глобальные стили для ttk виджетов
        EnhancedStyles.configure_treeview_style()
        EnhancedStyles.configure_combobox_style()
        EnhancedStyles.configure_notebook_style()

        # Компактный заголовок
        header_frame = tk.Frame(self.window, bg=EnhancedStyles.COLORS['primary'], height=70)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        # Заголовок с автоматическим цветом
        title_label = EnhancedStyles.create_label(header_frame, "📋 Система Управления Технологическими Картами",
                                                 EnhancedStyles.COLORS['primary'], 'header')
        title_label.pack(side='left', padx=25, pady=20)

        # Кнопки действий
        btn_frame = tk.Frame(header_frame, bg=EnhancedStyles.COLORS['primary'])
        btn_frame.pack(side='right', padx=25, pady=15)

        # Компактные кнопки
        add_btn = EnhancedStyles.success_button(btn_frame, "➕ Новый рецепт", self.add_recipe, padx=20, pady=8)
        add_btn.pack(side='left', padx=5)

        export_btn = EnhancedStyles.danger_button(btn_frame, "📤 Экспорт", self.export_recipes, padx=20, pady=8)
        export_btn.pack(side='left', padx=5)
        
        # Основной контент
        main_frame = tk.Frame(self.window, bg=EnhancedStyles.COLORS['light'])
        main_frame.pack(fill='both', expand=True)

        # Создать вкладки с компактными отступами
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Вкладка "Технологические Карты"
        recipes_frame = tk.Frame(notebook, bg='white')
        notebook.add(recipes_frame, text="📋 Технологические Карты")
        self.create_recipes_tab(recipes_frame)

        # Вкладка "Полуфабрикаты"
        sub_recipes_frame = tk.Frame(notebook, bg='white')
        notebook.add(sub_recipes_frame, text="🧪 Полуфабрикаты")
        self.create_sub_recipes_tab(sub_recipes_frame)
        
        # Вкладка "Калькулятор"
        calculator_frame = tk.Frame(notebook, bg='white')
        notebook.add(calculator_frame, text="💰 Калькулятор")
        self.create_calculator_tab(calculator_frame)
    
    def create_recipes_tab(self, parent):
        """Создать компактную вкладку рецептов с улучшенными стилями"""
        # Левая панель - список рецептов (компактная)
        left_frame = tk.Frame(parent, bg=EnhancedStyles.COLORS['light'], width=400)
        left_frame.pack(side='left', fill='y', padx=(15, 10), pady=15)
        left_frame.pack_propagate(False)

        # Компактный заголовок списка
        title_label = EnhancedStyles.create_label(left_frame, "📊 Технологические Карты Блюд",
                                                 EnhancedStyles.COLORS['light'], 'large')
        title_label.pack(pady=(15, 10))

        # TreeView для рецептов в виде дерева
        tree_frame = tk.Frame(left_frame, bg='white', relief='solid', bd=2)
        tree_frame.pack(fill='both', expand=True, padx=15, pady=(0, 15))

        # Создать TreeView с деревом (показывать и дерево и заголовки)
        columns = ('Тип', 'Количество', 'Единица', 'Стоимость')
        self.recipes_tree = ttk.Treeview(tree_frame, columns=columns, show='tree headings', height=12)

        # Настроить стиль TreeView
        style = ttk.Style()
        style.theme_use('clam')  # Использовать современную тему

        # Настроить стили для TreeView
        style.configure("Recipes.Treeview",
                       background='white',
                       foreground='#2c3e50',
                       fieldbackground='white',
                       font=('Cambria', 11))

        style.configure("Recipes.Treeview.Heading",
                       background='#1e3a8a',
                       foreground='white',
                       font=('Cambria', 12, 'bold'),
                       relief='flat')

        # Настроить цвета выделения и чередующиеся строки
        style.map("Recipes.Treeview",
                 background=[('selected', '#3498db'), ('!selected', 'white')],
                 foreground=[('selected', 'white'), ('!selected', '#2c3e50')])

        # Настроить стили для разных типов узлов
        self.recipes_tree.tag_configure('recipe_node',
                                       background='#e3f2fd',
                                       foreground='#1565c0',
                                       font=('Cambria', 12, 'bold'))

        self.recipes_tree.tag_configure('ingredients_header',
                                       background='#e8f5e8',
                                       foreground='#2e7d32',
                                       font=('Cambria', 11, 'bold'))

        self.recipes_tree.tag_configure('ingredient_item',
                                       background='#f1f8e9',
                                       foreground='#388e3c',
                                       font=('Cambria', 10))

        self.recipes_tree.tag_configure('semi_header',
                                       background='#fff3e0',
                                       foreground='#f57c00',
                                       font=('Cambria', 11, 'bold'))

        self.recipes_tree.tag_configure('semi_item',
                                       background='#fff8e1',
                                       foreground='#ff8f00',
                                       font=('Cambria', 10))

        self.recipes_tree.tag_configure('sub_ingredient',
                                       background='#fafafa',
                                       foreground='#616161',
                                       font=('Cambria', 9))

        self.recipes_tree.tag_configure('total_item',
                                       background='#f3e5f5',
                                       foreground='#7b1fa2',
                                       font=('Cambria', 10, 'bold'))

        self.recipes_tree.tag_configure('recipe_total',
                                       background='#ffebee',
                                       foreground='#c62828',
                                       font=('Cambria', 11, 'bold'))

        self.recipes_tree.tag_configure('sub_header',
                                       background='#e8eaf6',
                                       foreground='#3f51b5',
                                       font=('Cambria', 10, 'italic'))

        # Применить стиль
        self.recipes_tree.configure(style="Recipes.Treeview")

        # Настроить колонку дерева (первая колонка)
        self.recipes_tree.heading('#0', text='Название', anchor='w')
        self.recipes_tree.column('#0', width=200, minwidth=150, anchor='w')

        # Настроить заголовки дополнительных колонок
        column_widths = {
            'Тип': 80,
            'Количество': 80,
            'Единица': 60,
            'Стоимость': 80
        }

        for col in columns:
            self.recipes_tree.heading(col, text=col)
            self.recipes_tree.column(col, width=column_widths[col], anchor='center')

        # Добавить скроллбар для TreeView
        tree_scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.recipes_tree.yview)
        self.recipes_tree.configure(yscrollcommand=tree_scrollbar.set)

        self.recipes_tree.pack(side='left', fill='both', expand=True)
        tree_scrollbar.pack(side='right', fill='y')

        # Заполнить TreeView данными рецептов
        self.populate_recipes_tree()

        # Компактные кнопки управления технологическими картами
        recipe_buttons = tk.Frame(left_frame, bg=EnhancedStyles.COLORS['light'])
        recipe_buttons.pack(fill='x', padx=15, pady=(0, 15))

        edit_btn = EnhancedStyles.secondary_button(recipe_buttons, "✏️ Редактировать",
                                                  self.edit_recipe, padx=15, pady=6)
        edit_btn.pack(fill='x', pady=3)

        delete_btn = EnhancedStyles.danger_button(recipe_buttons, "🗑️ Удалить",
                                                 self.delete_recipe, padx=15, pady=6)
        delete_btn.pack(fill='x', pady=3)

        # Правая панель - детали рецепта (больше места)
        self.recipe_details_frame = tk.Frame(parent, bg='white')
        self.recipe_details_frame.pack(side='right', fill='both', expand=True, padx=(10, 15), pady=15)

        # Показать первый рецепт
        if self.recipes:
            first_recipe = list(self.recipes.keys())[0]
            self.current_recipe = first_recipe
            self.show_recipe_details(first_recipe)

        # Привязать событие выбора для TreeView
        self.recipes_tree.bind('<<TreeviewSelect>>', self.on_recipe_select)

    def populate_recipes_tree(self):
        """Заполнить TreeView данными рецептов в виде дерева"""
        # Очистить существующие данные
        for item in self.recipes_tree.get_children():
            self.recipes_tree.delete(item)

        # Добавить рецепты в TreeView как дерево
        for index, (recipe_name, recipe_data) in enumerate(self.recipes.items()):
            # Создать родительский узел для рецепта
            category = recipe_data.get('category', 'Не указано')
            portions = str(recipe_data.get('portions', 0))
            price = f"{recipe_data.get('price', 0):.0f}₽"

            # Определить тег для чередующихся цветов
            tag = 'recipe_node'

            # Вставить технологическую карту как родительский узел
            recipe_id = self.recipes_tree.insert('', 'end',
                                                text=f"📋 {recipe_name}",
                                                values=(category, portions, 'порций', price),
                                                tags=(tag,),
                                                open=False)  # Свернуть по умолчанию

            # Добавить узел "Состав блюда" (ингредиенты и полуфабрикаты вместе)
            composition_node = self.recipes_tree.insert(recipe_id, 'end',
                                                       text="🥘 Состав блюда",
                                                       values=('Компоненты', '', '', ''),
                                                       tags=('ingredients_header',))

            total_composition_cost = 0

            # Добавить обычные ингредиенты
            for ingredient in recipe_data.get('ingredients', []):
                ingredient_cost = ingredient.get('cost', 0)
                total_composition_cost += ingredient_cost

                self.recipes_tree.insert(composition_node, 'end',
                                       text=f"  • {ingredient['name']}",
                                       values=('Ингредиент',
                                              ingredient['amount'],
                                              ingredient['unit'],
                                              f"{ingredient_cost:.2f}₽"),
                                       tags=('ingredient_item',))

            # Добавить полуфабрикаты в состав блюда
            if 'semi_products' in recipe_data and recipe_data['semi_products']:
                for semi_product in recipe_data['semi_products']:
                    semi_cost = semi_product.get('cost', 0)
                    total_composition_cost += semi_cost

                    # Добавить полуфабрикат как компонент блюда
                    semi_id = self.recipes_tree.insert(composition_node, 'end',
                                                     text=f"  🧪 {semi_product['name']}",
                                                     values=('Полуфабрикат',
                                                            semi_product.get('amount', ''),
                                                            semi_product.get('unit', ''),
                                                            f"{semi_cost:.2f}₽"),
                                                     tags=('semi_item',))

                    # Показать состав полуфабриката
                    if 'ingredients' in semi_product and semi_product['ingredients']:
                        # Добавить заголовок состава полуфабриката
                        composition_header = self.recipes_tree.insert(semi_id, 'end',
                                                                    text=f"    📝 Состав полуфабриката:",
                                                                    values=('Состав', '', '', ''),
                                                                    tags=('sub_header',))

                        # Добавить ингредиенты полуфабриката
                        for sub_ingredient in semi_product['ingredients']:
                            self.recipes_tree.insert(composition_header, 'end',
                                                   text=f"      ◦ {sub_ingredient['name']}",
                                                   values=('Компонент',
                                                          sub_ingredient.get('amount', ''),
                                                          sub_ingredient.get('unit', ''),
                                                          f"{sub_ingredient.get('cost', 0):.2f}₽"),
                                                   tags=('sub_ingredient',))

            # Добавить итого для состава блюда
            self.recipes_tree.insert(composition_node, 'end',
                                   text="  💰 Итого состав блюда",
                                   values=('Сумма', '', '', f"{total_composition_cost:.2f}₽"),
                                   tags=('total_item',))

            # Добавить общий итог рецепта
            total_cost = recipe_data.get('cost', 0)
            self.recipes_tree.insert(recipe_id, 'end',
                                   text="💰 ОБЩАЯ СЕБЕСТОИМОСТЬ",
                                   values=('Итого', '', '', f"{total_cost:.2f}₽"),
                                   tags=('recipe_total',))

    def on_recipe_select(self, event):
        """Обработка выбора элемента в TreeView"""
        selection = self.recipes_tree.selection()
        if not selection:
            return

        item = selection[0]
        item_text = self.recipes_tree.item(item, 'text').strip()

        # Найти корневой элемент (технологическую карту)
        root_item = item
        while self.recipes_tree.parent(root_item):
            root_item = self.recipes_tree.parent(root_item)

        root_text = self.recipes_tree.item(root_item, 'text').strip()

        # Извлечь название технологической карты
        if root_text.startswith("📋 "):
            recipe_name = root_text.replace("📋 ", "", 1).strip()
            self.current_recipe = recipe_name

            # Определить что выбрано и показать соответствующие детали
            if item_text.startswith("📋 "):
                # Выбрана сама технологическая карта
                selected_recipe_name = item_text.replace("📋 ", "", 1).strip()
                self.show_recipe_details(selected_recipe_name)

            elif item_text.startswith("🧪 ") and not any(x in item_text for x in ["Состав блюда", "Полуфабрикаты"]):
                # Выбран конкретный полуфабрикат
                semi_name = item_text.replace("🧪 ", "", 1).strip()
                self.show_semi_product_details(semi_name, recipe_name)

            else:
                # Выбран любой другой элемент - показать детали технологической карты
                self.show_recipe_details(recipe_name)

    def show_semi_product_details(self, semi_name, parent_recipe):
        """Показать детали полуфабриката"""
        # Очистить панель деталей
        for widget in self.recipe_details_frame.winfo_children():
            widget.destroy()

        # Найти полуфабрикат в данных рецепта
        recipe_data = self.recipes.get(parent_recipe, {})
        semi_product = None

        # Поиск полуфабриката в составе рецепта
        for semi in recipe_data.get('semi_products', []):
            if semi['name'] == semi_name:
                semi_product = semi
                break

        # Если не найден в рецепте, поиск в общих полуфабрикатах
        if not semi_product and semi_name in self.sub_recipes:
            sub_recipe_data = self.sub_recipes[semi_name]
            semi_product = {
                'name': semi_name,
                'amount': '',
                'unit': sub_recipe_data.get('yield', ''),
                'cost': sub_recipe_data.get('cost', 0),
                'ingredients': sub_recipe_data.get('ingredients', []),
                'category': sub_recipe_data.get('category', ''),
                'shelf_life': sub_recipe_data.get('shelf_life', '')
            }

        if not semi_product:
            return

        # Заголовок полуфабриката
        header = tk.Frame(self.recipe_details_frame, bg=EnhancedStyles.COLORS['warning'], height=60)
        header.pack(fill='x')
        header.pack_propagate(False)

        title_label = EnhancedStyles.create_label(header, f"🧪 {semi_name}",
                                                 EnhancedStyles.COLORS['warning'], 'large')
        title_label.pack(side='left', padx=20, pady=15)

        # Информация о полуфабрикате
        info_frame = tk.Frame(self.recipe_details_frame, bg=EnhancedStyles.COLORS['light'])
        info_frame.pack(fill='x', padx=20, pady=(15, 10))

        # Информационные карточки
        cards_frame = tk.Frame(info_frame, bg=EnhancedStyles.COLORS['light'])
        cards_frame.pack(fill='x')

        info_data = [
            ("Тип", "Полуфабрикат"),
            ("Категория", semi_product.get('category', 'Не указано')),
            ("Выход", semi_product.get('unit', semi_product.get('amount', ''))),
            ("Срок хранения", semi_product.get('shelf_life', 'Не указано'))
        ]

        for title, value in info_data:
            card = tk.Frame(cards_frame, bg='white', relief='solid', bd=1)
            card.pack(side='left', fill='both', expand=True, padx=6, pady=10)

            title_label = EnhancedStyles.create_label(card, title, 'white', 'small')
            title_label.pack(pady=(10, 5))

            value_label = EnhancedStyles.create_label(card, str(value), 'white', 'medium', bold=True)
            value_label.pack(pady=(0, 10))

        # Финансовая информация
        finance_frame = tk.Frame(self.recipe_details_frame, bg=EnhancedStyles.COLORS['light'])
        finance_frame.pack(fill='x', padx=20, pady=10)

        cost = semi_product.get('cost', 0)

        finance_data = [
            ("Себестоимость", f"{cost:.2f}₽"),
            ("За единицу", f"{cost:.2f}₽"),
            ("Статус", "Полуфабрикат"),
            ("Использование", f"В рецепте: {parent_recipe}")
        ]

        finance_cards = tk.Frame(finance_frame, bg=EnhancedStyles.COLORS['light'])
        finance_cards.pack(fill='x')

        for title, value in finance_data:
            card = tk.Frame(finance_cards, bg='white', relief='solid', bd=1)
            card.pack(side='left', fill='both', expand=True, padx=6, pady=10)

            title_label = EnhancedStyles.create_label(card, title, 'white', 'small')
            title_label.pack(pady=(10, 5))

            value_label = EnhancedStyles.create_label(card, str(value), 'white', 'medium', bold=True)
            value_label.pack(pady=(0, 10))

        # Таблица ингредиентов полуфабриката
        self.create_semi_ingredients_table(semi_product)

    def create_semi_ingredients_table(self, semi_product):
        """Создать таблицу ингредиентов для полуфабриката"""
        # Заголовок секции ингредиентов
        ing_header = tk.Frame(self.recipe_details_frame, bg=EnhancedStyles.COLORS['warning'], height=50)
        ing_header.pack(fill='x', padx=20, pady=(15, 0))
        ing_header.pack_propagate(False)

        title_label = EnhancedStyles.create_label(ing_header, "🥘 Состав полуфабриката",
                                                 EnhancedStyles.COLORS['warning'], 'large')
        title_label.pack(side='left', padx=20, pady=12)

        # Кнопки управления ингредиентами
        ing_buttons = tk.Frame(ing_header, bg=EnhancedStyles.COLORS['warning'])
        ing_buttons.pack(side='right', padx=20, pady=8)

        add_btn = EnhancedStyles.create_button(ing_buttons, "➕", '#f39c12',
                                              lambda: self.add_semi_ingredient(semi_product['name']), padx=12, pady=6)
        add_btn.pack(side='left', padx=3)

        edit_btn = EnhancedStyles.secondary_button(ing_buttons, "✏️",
                                                  lambda: self.edit_semi_ingredient(semi_product['name']), padx=12, pady=6)
        edit_btn.pack(side='left', padx=3)

        delete_btn = EnhancedStyles.danger_button(ing_buttons, "🗑️",
                                                 lambda: self.delete_semi_ingredient(semi_product['name']), padx=12, pady=6)
        delete_btn.pack(side='left', padx=3)

        # Фрейм для таблицы
        table_frame = tk.Frame(self.recipe_details_frame, bg='white', relief='solid', bd=2)
        table_frame.pack(fill='both', expand=True, padx=20, pady=(0, 15))

        # Создать Treeview для таблицы ингредиентов
        columns = ('Ингредиент', 'Количество', 'Единица', 'Стоимость')
        self.semi_ingredients_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=10)

        # Применить стили к таблице
        EnhancedStyles.style_treeview(self.semi_ingredients_tree, 'table')

        # Настроить заголовки
        column_widths = {
            'Ингредиент': 300,
            'Количество': 120,
            'Единица': 100,
            'Стоимость': 120
        }

        for col in columns:
            self.semi_ingredients_tree.heading(col, text=col)
            self.semi_ingredients_tree.column(col, width=column_widths[col], anchor='center')

        # Добавить данные ингредиентов
        total_cost = 0
        for i, ingredient in enumerate(semi_product.get('ingredients', [])):
            values = (
                ingredient['name'],
                ingredient['amount'],
                ingredient['unit'],
                f"{ingredient['cost']:.2f}₽"
            )
            self.semi_ingredients_tree.insert('', 'end', iid=i, values=values)
            total_cost += ingredient['cost']

        # Добавить итоговую строку
        self.semi_ingredients_tree.insert('', 'end', iid='total',
                                         values=('ИТОГО', '', '', f"{total_cost:.2f}₽"))

        # Скроллбар
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.semi_ingredients_tree.yview)
        self.semi_ingredients_tree.configure(yscrollcommand=scrollbar.set)

        # Упаковка
        self.semi_ingredients_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # Привязать двойной клик для редактирования
        self.semi_ingredients_tree.bind('<Double-1>', lambda e: self.edit_semi_ingredient(semi_product['name']))

    def add_semi_ingredient(self, semi_name):
        """Добавить ингредиент в полуфабрикат"""
        messagebox.showinfo("Добавление", f"Функция добавления ингредиента в полуфабрикат: {semi_name}")

    def edit_semi_ingredient(self, semi_name):
        """Редактировать ингредиент полуфабриката"""
        messagebox.showinfo("Редактирование", f"Функция редактирования ингредиента полуфабриката: {semi_name}")

    def delete_semi_ingredient(self, semi_name):
        """Удалить ингредиент полуфабриката"""
        messagebox.showinfo("Удаление", f"Функция удаления ингредиента полуфабриката: {semi_name}")

    def show_recipe_details(self, recipe_name):
        """Показать детали рецепта с улучшенными стилями"""
        # Очистить
        for widget in self.recipe_details_frame.winfo_children():
            widget.destroy()

        if recipe_name not in self.recipes:
            # Показать сообщение об ошибке
            error_label = tk.Label(self.recipe_details_frame,
                                  text=f"❌ Технологическая карта '{recipe_name}' не найдена",
                                  font=('Cambria', 16, 'bold'),
                                  bg='white', fg='red')
            error_label.pack(expand=True)
            return

        recipe = self.recipes[recipe_name]

        # Компактный заголовок рецепта
        header = tk.Frame(self.recipe_details_frame, bg=EnhancedStyles.COLORS['secondary'], height=60)
        header.pack(fill='x')
        header.pack_propagate(False)

        title_label = EnhancedStyles.create_label(header, f"📋 {recipe_name}",
                                                 EnhancedStyles.COLORS['secondary'], 'large')
        title_label.pack(side='left', padx=20, pady=15)

        # Компактная основная информация в карточках
        info_frame = tk.Frame(self.recipe_details_frame, bg=EnhancedStyles.COLORS['light'])
        info_frame.pack(fill='x', padx=20, pady=(15, 10))

        # Информационные карточки
        cards_frame = tk.Frame(info_frame, bg=EnhancedStyles.COLORS['light'])
        cards_frame.pack(fill='x')

        info_data = [
            ("Категория", recipe['category']),
            ("Порций", str(recipe['portions'])),
            ("Время", f"{recipe['prep_time']} + {recipe['cook_time']}"),
            ("Сложность", recipe['difficulty'])
        ]

        for title, value in info_data:
            card = tk.Frame(cards_frame, bg='white', relief='solid', bd=1)
            card.pack(side='left', fill='both', expand=True, padx=6, pady=10)

            title_label = EnhancedStyles.create_label(card, title, 'white', 'small')
            title_label.pack(pady=(10, 5))

            value_label = EnhancedStyles.create_label(card, value, 'white', 'medium', bold=True)
            value_label.pack(pady=(0, 10))

        # Компактная финансовая информация
        finance_frame = tk.Frame(self.recipe_details_frame, bg=EnhancedStyles.COLORS['light'])
        finance_frame.pack(fill='x', padx=20, pady=10)

        # Защита от деления на ноль при расчете маржи
        if recipe['price'] > 0:
            margin = ((recipe['price'] - recipe['cost']) / recipe['price']) * 100
        else:
            margin = 0
        profit = recipe['price'] - recipe['cost']

        finance_data = [
            ("Себестоимость", f"{recipe['cost']:.2f}₽"),
            ("Цена продажи", f"{recipe['price']:.2f}₽"),
            ("Прибыль", f"{profit:.2f}₽"),
            ("Маржа", f"{margin:.1f}%")
        ]

        finance_cards = tk.Frame(finance_frame, bg=EnhancedStyles.COLORS['light'])
        finance_cards.pack(fill='x')

        for title, value in finance_data:
            card = tk.Frame(finance_cards, bg='white', relief='solid', bd=1)
            card.pack(side='left', fill='both', expand=True, padx=6, pady=10)

            title_label = EnhancedStyles.create_label(card, title, 'white', 'small')
            title_label.pack(pady=(10, 5))

            value_label = EnhancedStyles.create_label(card, value, 'white', 'medium', bold=True)
            value_label.pack(pady=(0, 10))

        # Таблица ингредиентов
        self.create_ingredients_table(recipe_name)

        # Инструкции
        self.create_instructions_section(recipe_name)

    def create_ingredients_table(self, recipe_name):
        """Создать компактную таблицу ингредиентов"""
        recipe = self.recipes[recipe_name]

        # Компактный заголовок секции ингредиентов
        ing_header = tk.Frame(self.recipe_details_frame, bg=EnhancedStyles.COLORS['success'], height=50)
        ing_header.pack(fill='x', padx=20, pady=(15, 0))
        ing_header.pack_propagate(False)

        title_label = EnhancedStyles.create_label(ing_header, "🥘 Ингредиенты",
                                                 EnhancedStyles.COLORS['success'], 'large')
        title_label.pack(side='left', padx=20, pady=12)

        # Компактные кнопки управления ингредиентами
        ing_buttons = tk.Frame(ing_header, bg=EnhancedStyles.COLORS['success'])
        ing_buttons.pack(side='right', padx=20, pady=8)

        add_btn = EnhancedStyles.create_button(ing_buttons, "➕", '#2ecc71',
                                              lambda: self.add_ingredient(recipe_name), padx=12, pady=6)
        add_btn.pack(side='left', padx=3)

        edit_btn = EnhancedStyles.secondary_button(ing_buttons, "✏️",
                                                  lambda: self.edit_ingredient(recipe_name), padx=12, pady=6)
        edit_btn.pack(side='left', padx=3)

        delete_btn = EnhancedStyles.danger_button(ing_buttons, "🗑️",
                                                 lambda: self.delete_ingredient(recipe_name), padx=12, pady=6)
        delete_btn.pack(side='left', padx=3)

        # Фрейм для таблицы с увеличенной высотой
        table_frame = tk.Frame(self.recipe_details_frame, bg='white', relief='solid', bd=2)
        table_frame.pack(fill='both', expand=True, padx=20, pady=(0, 15))

        # Создать Treeview для таблицы ингредиентов с увеличенной высотой
        columns = ('Ингредиент', 'Количество', 'Единица', 'Стоимость')
        self.ingredients_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=12)

        # Применить стили Cambria к таблице
        EnhancedStyles.style_treeview(self.ingredients_tree, 'table')

        # Настроить заголовки с оптимальной шириной для лучшей видимости
        column_widths = {
            'Ингредиент': 300,
            'Количество': 120,
            'Единица': 100,
            'Стоимость': 120
        }

        for col in columns:
            self.ingredients_tree.heading(col, text=col)
            self.ingredients_tree.column(col, width=column_widths[col], anchor='center')

        # Добавить данные
        total_cost = 0
        for i, ingredient in enumerate(recipe['ingredients']):
            values = (
                ingredient['name'],
                ingredient['amount'],
                ingredient['unit'],
                f"{ingredient['cost']:.2f}₽"
            )
            self.ingredients_tree.insert('', 'end', iid=i, values=values)
            total_cost += ingredient['cost']

        # Добавить итоговую строку
        self.ingredients_tree.insert('', 'end', iid='total', values=('ИТОГО', '', '', f"{total_cost:.2f}₽"))

        # Скроллбар
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.ingredients_tree.yview)
        self.ingredients_tree.configure(yscrollcommand=scrollbar.set)

        # Упаковка
        self.ingredients_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # Привязать двойной клик для редактирования
        self.ingredients_tree.bind('<Double-1>', lambda e: self.edit_ingredient(recipe_name))

    def create_instructions_section(self, recipe_name):
        """Создать компактную секцию инструкций"""
        recipe = self.recipes[recipe_name]

        # Компактный заголовок секции инструкций
        inst_header = tk.Frame(self.recipe_details_frame, bg=EnhancedStyles.COLORS['warning'], height=50)
        inst_header.pack(fill='x', padx=20, pady=(15, 0))
        inst_header.pack_propagate(False)

        title_label = EnhancedStyles.create_label(inst_header, "📝 Инструкции",
                                                 EnhancedStyles.COLORS['warning'], 'large')
        title_label.pack(side='left', padx=20, pady=12)

        # Компактные кнопки управления инструкциями
        inst_buttons = tk.Frame(inst_header, bg=EnhancedStyles.COLORS['warning'])
        inst_buttons.pack(side='right', padx=20, pady=8)

        add_btn = EnhancedStyles.create_button(inst_buttons, "➕", '#f39c12',
                                              lambda: self.add_instruction(recipe_name), padx=12, pady=6)
        add_btn.pack(side='left', padx=3)

        edit_btn = EnhancedStyles.secondary_button(inst_buttons, "✏️",
                                                  lambda: self.edit_instruction(recipe_name), padx=12, pady=6)
        edit_btn.pack(side='left', padx=3)

        delete_btn = EnhancedStyles.danger_button(inst_buttons, "🗑️",
                                                 lambda: self.delete_instruction(recipe_name), padx=12, pady=6)
        delete_btn.pack(side='left', padx=3)

        # Фрейм для инструкций
        inst_frame = tk.Frame(self.recipe_details_frame, bg='white', relief='solid', bd=2)
        inst_frame.pack(fill='both', expand=True, padx=20, pady=(0, 15))

        # Создать Treeview для инструкций с улучшенным шрифтом
        self.instructions_tree = ttk.Treeview(inst_frame, columns=('Шаг', 'Инструкция'), show='headings', height=8)

        # Применить стили Cambria к таблице инструкций
        EnhancedStyles.style_treeview(self.instructions_tree, 'table')

        # Настроить заголовки с увеличенной шириной
        self.instructions_tree.heading('Шаг', text='Шаг')
        self.instructions_tree.heading('Инструкция', text='Инструкция')
        self.instructions_tree.column('Шаг', width=120, anchor='center')
        self.instructions_tree.column('Инструкция', width=800, anchor='w')

        # Добавить данные
        for i, instruction in enumerate(recipe['instructions'], 1):
            self.instructions_tree.insert('', 'end', iid=i-1, values=(f"Шаг {i}", instruction))

        # Скроллбар для инструкций
        inst_scrollbar = ttk.Scrollbar(inst_frame, orient='vertical', command=self.instructions_tree.yview)
        self.instructions_tree.configure(yscrollcommand=inst_scrollbar.set)

        # Упаковка
        self.instructions_tree.pack(side='left', fill='both', expand=True)
        inst_scrollbar.pack(side='right', fill='y')

        # Привязать двойной клик для редактирования
        self.instructions_tree.bind('<Double-1>', lambda e: self.edit_instruction(recipe_name))

    def create_sub_recipes_tab(self, parent):
        """Создать вкладку подрецептов с улучшенными стилями"""
        # Заголовок
        header = tk.Frame(parent, bg=EnhancedStyles.COLORS['purple'], height=100)
        header.pack(fill='x')
        header.pack_propagate(False)

        title_label = EnhancedStyles.create_label(header, "🧪 Управление Полуфабрикатами",
                                                 EnhancedStyles.COLORS['purple'], 'header')
        title_label.pack(side='left', padx=45, pady=35)

        # Таблица подрецептов
        table_frame = tk.Frame(parent, bg='white')
        table_frame.pack(fill='both', expand=True, padx=45, pady=45)

        # Создать Treeview с улучшенным шрифтом
        columns = ('Название', 'Категория', 'Выход', 'Срок хранения', 'Себестоимость')
        sub_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # Применить стили Cambria к таблице
        EnhancedStyles.style_treeview(sub_tree, 'table')

        # Настроить заголовки с увеличенной шириной
        headers_config = {
            'Название': 300,
            'Категория': 180,
            'Выход': 150,
            'Срок хранения': 180,
            'Себестоимость': 180
        }

        for col, width in headers_config.items():
            sub_tree.heading(col, text=col)
            sub_tree.column(col, width=width, anchor='center')

        # Добавить данные
        for name, data in self.sub_recipes.items():
            values = (
                name,
                data['category'],
                data['yield'],
                data['shelf_life'],
                f"{data['cost']:.2f}₽"
            )
            sub_tree.insert('', 'end', values=values)

        # Скроллбар
        sub_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=sub_tree.yview)
        sub_tree.configure(yscrollcommand=sub_scrollbar.set)

        # Упаковка
        sub_tree.pack(side='left', fill='both', expand=True)
        sub_scrollbar.pack(side='right', fill='y')

    def create_calculator_tab(self, parent):
        """Создать вкладку калькулятора с улучшенными стилями"""
        # Заголовок
        header_label = EnhancedStyles.create_label(parent, "💰 Калькулятор Себестоимости",
                                                  'white', 'header')
        header_label.pack(pady=45)

        # Выбор рецепта
        calc_frame = tk.Frame(parent, bg=EnhancedStyles.COLORS['light'], relief='solid', bd=2)
        calc_frame.pack(fill='x', padx=55, pady=(0, 45))

        recipe_frame = tk.Frame(calc_frame, bg=EnhancedStyles.COLORS['light'])
        recipe_frame.pack(fill='x', padx=45, pady=35)

        recipe_label = EnhancedStyles.create_label(recipe_frame, "Технологическая карта:",
                                                  EnhancedStyles.COLORS['light'], 'medium')
        recipe_label.pack(side='left')

        self.calc_recipe_var = tk.StringVar(value=list(self.recipes.keys())[0])
        recipe_combo = ttk.Combobox(recipe_frame, textvariable=self.calc_recipe_var,
                                   values=list(self.recipes.keys()), width=40)
        EnhancedStyles.style_entry(recipe_combo, 'medium')
        recipe_combo.pack(side='left', padx=30)

        portions_label = EnhancedStyles.create_label(recipe_frame, "Порций:",
                                                    EnhancedStyles.COLORS['light'], 'medium')
        portions_label.pack(side='left', padx=(55, 20))

        self.calc_portions_var = tk.StringVar(value="1")
        portions_entry = tk.Entry(recipe_frame, textvariable=self.calc_portions_var, width=25)
        EnhancedStyles.style_entry(portions_entry, 'medium')
        portions_entry.pack(side='left', padx=20)

        calc_btn = EnhancedStyles.primary_button(recipe_frame, "💰 Рассчитать",
                                                self.calculate_cost, padx=35, pady=15)
        calc_btn.pack(side='left', padx=45)

        # Результат
        self.calc_result_frame = tk.Frame(parent, bg='white')
        self.calc_result_frame.pack(fill='both', expand=True, padx=55)

        # Показать начальный расчёт
        self.calculate_cost()

    def calculate_cost(self):
        """Рассчитать себестоимость"""
        # Очистить
        for widget in self.calc_result_frame.winfo_children():
            widget.destroy()

        try:
            portions = float(self.calc_portions_var.get())
            if portions <= 0:
                portions = 1
        except ValueError:
            portions = 1

        recipe_name = self.calc_recipe_var.get()
        if recipe_name not in self.recipes:
            return

        recipe = self.recipes[recipe_name]
        base_portions = recipe['portions']

        # Защита от деления на ноль
        if base_portions <= 0:
            messagebox.showerror("Ошибка", f"Некорректное количество базовых порций в рецепте: {base_portions}")
            return

        if portions <= 0:
            messagebox.showerror("Ошибка", f"Количество порций должно быть больше 0")
            return

        multiplier = portions / base_portions

        # Результат
        result_frame = tk.Frame(self.calc_result_frame, bg=EnhancedStyles.COLORS['light'], relief='solid', bd=2)
        result_frame.pack(fill='both', expand=True)

        title_label = EnhancedStyles.create_label(result_frame, f"💰 Расчёт технологической карты: {recipe_name}",
                                                 EnhancedStyles.COLORS['light'], 'large')
        title_label.pack(pady=30)

        details_text = f"""
📊 ПАРАМЕТРЫ:
• Базовое количество: {base_portions} порций
• Запрашиваемое: {portions} порций
• Коэффициент: {multiplier:.2f}

💰 СТОИМОСТЬ:
• Базовая себестоимость: {recipe['cost']:.2f}₽
• Себестоимость за {portions} порций: {recipe['cost'] * multiplier:.2f}₽
• Себестоимость за 1 порцию: {(recipe['cost'] * multiplier / portions) if portions > 0 else 0:.2f}₽

📈 ЦЕНООБРАЗОВАНИЕ:
• Рекомендуемая цена: {recipe['price'] * multiplier:.2f}₽
• Цена за 1 порцию: {recipe['price']:.2f}₽
• Валовая прибыль: {(recipe['price'] - recipe['cost']) * multiplier:.2f}₽
• Маржинальность: {((recipe['price'] - recipe['cost']) / recipe['price']) * 100 if recipe['price'] > 0 else 0:.1f}%
        """

        details_label = EnhancedStyles.create_label(result_frame, details_text,
                                                   EnhancedStyles.COLORS['light'], 'normal')
        details_label.pack(fill='both', expand=True, padx=45, pady=45)

    # Методы управления
    def add_recipe(self):
        """Добавить технологическую карту"""
        self.open_add_recipe_dialog()

    def open_add_recipe_dialog(self):
        """Открыть диалог добавления новой технологической карты"""
        # Создать диалоговое окно на полный экран
        dialog = tk.Toplevel(self.window)
        dialog.title("Добавление новой технологической карты")
        dialog.configure(bg='white')
        dialog.resizable(True, True)

        # Открыть на полный экран
        dialog.state('zoomed')  # Для Windows
        # Альтернативно для других ОС:
        # dialog.attributes('-zoomed', True)  # Для Linux
        # dialog.attributes('-fullscreen', True)  # Для macOS

        # Центрировать окно
        dialog.transient(self.window)
        # НЕ ДЕЛАЕМ ДИАЛОГ МОДАЛЬНЫМ
        # dialog.grab_set()

        # Компактный заголовок
        header = tk.Frame(dialog, bg=EnhancedStyles.COLORS['primary'], height=60)
        header.pack(fill='x')
        header.pack_propagate(False)

        title_label = EnhancedStyles.create_label(header, "➕ Новая Технологическая Карта",
                                                 EnhancedStyles.COLORS['primary'], 'large')
        title_label.pack(side='left', padx=20, pady=15)

        # Основной контейнер с небольшим отступом справа для таблиц
        main_container = tk.Frame(dialog, bg=EnhancedStyles.COLORS['light'])
        main_container.pack(fill='both', expand=True, padx=(0, 5), pady=0)

        # Создать Canvas для прокрутки на всю ширину
        canvas = tk.Canvas(main_container, bg=EnhancedStyles.COLORS['light'], highlightthickness=0, bd=0)
        scrollbar = ttk.Scrollbar(main_container, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=EnhancedStyles.COLORS['light'])

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True, padx=0, pady=0)
        scrollbar.pack(side="right", fill="y")

        # Форма с небольшим отступом справа
        form_frame = tk.Frame(scrollable_frame, bg=EnhancedStyles.COLORS['light'])
        form_frame.pack(fill='both', expand=True, padx=(0, 3), pady=0)

        # Создать карточку формы с небольшим отступом справа
        card_frame = tk.Frame(form_frame, bg='white', relief='flat', bd=0)
        card_frame.pack(fill='both', expand=True, padx=(0, 2), pady=0)

        # Внутренняя область карточки с небольшим отступом справа
        inner_frame = tk.Frame(card_frame, bg='white')
        inner_frame.pack(fill='both', expand=True, padx=(1, 3), pady=1)

        # Поля формы
        fields = {}

        # Компактный заголовок секции
        section_label = EnhancedStyles.create_label(inner_frame, "📋 Основная информация",
                                                   'white', 'medium', bold=True)
        section_label.pack(anchor='w', pady=(0, 5))

        # Основная строка: Название, Категория, Порции (с небольшим отступом справа)
        main_row_frame = tk.Frame(inner_frame, bg='white')
        main_row_frame.pack(fill='x', pady=(0, 5), padx=(0, 5))

        # Название блюда (60% ширины экрана - еще больше)
        name_frame = tk.Frame(main_row_frame, bg='white')
        name_frame.pack(side='left', fill='both', expand=True, padx=(0, 2))

        tk.Label(name_frame, text="Название:", font=('Cambria', 10, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 1))
        fields['name'] = tk.Entry(name_frame, font=('Cambria', 11))
        EnhancedStyles.style_entry(fields['name'], 'medium')
        fields['name'].pack(fill='x', expand=True)

        # Категория (25% ширины экрана)
        cat_frame = tk.Frame(main_row_frame, bg='white')
        cat_frame.pack(side='left', fill='both', expand=True, padx=(2, 2))

        tk.Label(cat_frame, text="Категория:", font=('Cambria', 10, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 1))
        fields['category'] = ttk.Combobox(cat_frame, font=('Cambria', 11),
                                         values=["Супы", "Салаты", "Горячие блюда", "Десерты", "Напитки", "Закуски"])
        EnhancedStyles.style_entry(fields['category'], 'medium')
        fields['category'].pack(fill='x', expand=True)

        # Количество порций (15% ширины экрана - до самого края)
        portions_frame = tk.Frame(main_row_frame, bg='white')
        portions_frame.pack(side='right', fill='both', expand=True, padx=(2, 0))

        tk.Label(portions_frame, text="Порций:", font=('Cambria', 10, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 1))
        fields['portions'] = tk.Entry(portions_frame, font=('Cambria', 11))
        EnhancedStyles.style_entry(fields['portions'], 'medium')
        fields['portions'].pack(fill='x', expand=True)
        fields['portions'].insert(0, "1")  # Значение по умолчанию

        # Объединенная секция: Время, сложность и стоимость
        details_section_label = EnhancedStyles.create_label(inner_frame, "⏱️💰 Детали блюда",
                                                           'white', 'medium', bold=True)
        details_section_label.pack(anchor='w', pady=(8, 5))

        # Единая строка: Время, сложность, стоимость (с небольшим отступом справа)
        details_row_frame = tk.Frame(inner_frame, bg='white')
        details_row_frame.pack(fill='x', pady=(0, 5), padx=(0, 5))

        # Время подготовки (12% ширины)
        prep_frame = tk.Frame(details_row_frame, bg='white')
        prep_frame.pack(side='left', fill='both', expand=True, padx=(0, 2))

        tk.Label(prep_frame, text="Подготовка:", font=('Cambria', 10, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 1))
        fields['prep_time'] = tk.Entry(prep_frame, font=('Cambria', 11))
        EnhancedStyles.style_entry(fields['prep_time'], 'medium')
        fields['prep_time'].pack(fill='x', expand=True)
        fields['prep_time'].insert(0, "15 мин")

        # Время готовки (12% ширины)
        cook_frame = tk.Frame(details_row_frame, bg='white')
        cook_frame.pack(side='left', fill='both', expand=True, padx=(2, 2))

        tk.Label(cook_frame, text="Готовка:", font=('Cambria', 10, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 1))
        fields['cook_time'] = tk.Entry(cook_frame, font=('Cambria', 11))
        EnhancedStyles.style_entry(fields['cook_time'], 'medium')
        fields['cook_time'].pack(fill='x', expand=True)
        fields['cook_time'].insert(0, "30 мин")

        # Сложность (16% ширины)
        difficulty_frame = tk.Frame(details_row_frame, bg='white')
        difficulty_frame.pack(side='left', fill='both', expand=True, padx=(2, 2))

        tk.Label(difficulty_frame, text="Сложность:", font=('Cambria', 10, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 1))
        fields['difficulty'] = ttk.Combobox(difficulty_frame, font=('Cambria', 11),
                                           values=["Легкая", "Средняя", "Сложная"])
        EnhancedStyles.style_entry(fields['difficulty'], 'medium')
        fields['difficulty'].pack(fill='x', expand=True)
        fields['difficulty'].set("Средняя")

        # Себестоимость (30% ширины)
        cost_frame = tk.Frame(details_row_frame, bg='white')
        cost_frame.pack(side='left', fill='both', expand=True, padx=(2, 2))

        tk.Label(cost_frame, text="Себестоимость:", font=('Cambria', 10, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 1))
        fields['cost'] = tk.Entry(cost_frame, font=('Cambria', 11))
        EnhancedStyles.style_entry(fields['cost'], 'medium')
        fields['cost'].pack(fill='x', expand=True)
        fields['cost'].insert(0, "0,00")

        # Цена продажи (30% ширины - до самого края)
        price_frame = tk.Frame(details_row_frame, bg='white')
        price_frame.pack(side='right', fill='both', expand=True, padx=(2, 0))

        tk.Label(price_frame, text="Цена продажи:", font=('Cambria', 10, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 1))
        fields['price'] = tk.Entry(price_frame, font=('Cambria', 11))
        EnhancedStyles.style_entry(fields['price'], 'medium')
        fields['price'].pack(fill='x', expand=True)
        fields['price'].insert(0, "0,00")

        # Инициализировать списки для ингредиентов и полуфабрикатов
        fields['ingredients_list'] = []
        fields['semi_products_list'] = []

        # Компактный заголовок секции состава
        composition_section_label = EnhancedStyles.create_label(inner_frame, "🥘 Состав блюда",
                                                               'white', 'medium', bold=True)
        composition_section_label.pack(anchor='w', pady=(5, 2))

        # Создать вкладки для ингредиентов и полуфабрикатов (абсолютно растянутые до края)
        notebook = ttk.Notebook(inner_frame)
        notebook.pack(fill='both', expand=True, pady=(0, 0))

        # Настроить стиль notebook для максимального использования пространства
        style = ttk.Style()
        style.configure('TNotebook.Tab', padding=[30, 10])

        # Вкладка ингредиентов
        ingredients_frame = tk.Frame(notebook, bg='white')
        notebook.add(ingredients_frame, text="🥘 Ингредиенты")
        self.create_ingredients_tab_for_new_recipe(ingredients_frame, fields)

        # Вкладка полуфабрикатов
        semi_products_frame = tk.Frame(notebook, bg='white')
        notebook.add(semi_products_frame, text="🧪 Полуфабрикаты")
        self.create_semi_products_tab_for_new_recipe(semi_products_frame, fields)

        # Кнопки с небольшим отступом справа
        buttons_frame = tk.Frame(dialog, bg=EnhancedStyles.COLORS['light'], height=60)
        buttons_frame.pack(fill='x', side='bottom', padx=(0, 5), pady=0)
        buttons_frame.pack_propagate(False)

        # Контейнер кнопок с небольшим отступом справа
        buttons_container = tk.Frame(buttons_frame, bg=EnhancedStyles.COLORS['light'])
        buttons_container.pack(fill='x', expand=True, padx=(2, 3), pady=5)

        # Кнопка создать (растянутая)
        create_btn = EnhancedStyles.success_button(buttons_container, "✅ Создать технологическую карту",
                                                  lambda: self.create_new_recipe(dialog, fields),
                                                  padx=30, pady=12)
        create_btn.pack(side='left', fill='x', expand=True, padx=(0, 3))

        # Кнопка отмена (с небольшим отступом справа)
        cancel_btn = EnhancedStyles.secondary_button(buttons_container, "❌ Отмена",
                                                    dialog.destroy, padx=30, pady=12)
        cancel_btn.pack(side='right', fill='x', expand=True, padx=(3, 2))

        # Фокус на поле названия
        fields['name'].focus()

    def create_ingredients_tab_for_new_recipe(self, parent, fields):
        """Создать вкладку ингредиентов для нового рецепта"""
        # Кнопки управления с небольшим отступом справа
        buttons_frame = tk.Frame(parent, bg='white')
        buttons_frame.pack(fill='x', padx=(0, 5), pady=1)

        add_btn = EnhancedStyles.success_button(buttons_frame, "➕ Добавить",
                                               lambda: self.add_ingredient_to_new_recipe(fields),
                                               padx=6, pady=3)
        add_btn.pack(side='left', padx=1)

        delete_btn = EnhancedStyles.danger_button(buttons_frame, "🗑️ Удалить",
                                                 lambda: self.delete_ingredient_from_new_recipe(fields),
                                                 padx=6, pady=3)
        delete_btn.pack(side='left', padx=1)

        # Таблица ингредиентов с небольшим отступом справа
        table_frame = tk.Frame(parent, bg='white', relief='flat', bd=0)
        table_frame.pack(fill='both', expand=True, padx=(0, 5), pady=0)

        # Создать Treeview с максимальной высотой и растянутый по ширине
        columns = ('Название', 'Количество', 'Единица', 'Стоимость')
        fields['ingredients_tree'] = ttk.Treeview(table_frame, columns=columns, show='headings', height=25)

        # Настроить заголовки с оптимальным растяжением по ширине
        # Пропорции: Название 55%, Количество 20%, Единица 12%, Стоимость 13%
        for col in columns:
            fields['ingredients_tree'].heading(col, text=col)
            if col == 'Название':
                fields['ingredients_tree'].column(col, width=900, minwidth=400, anchor='w', stretch=True)
            elif col == 'Количество':
                fields['ingredients_tree'].column(col, width=320, minwidth=150, anchor='center', stretch=True)
            elif col == 'Единица':
                fields['ingredients_tree'].column(col, width=190, minwidth=100, anchor='center', stretch=True)
            elif col == 'Стоимость':
                fields['ingredients_tree'].column(col, width=210, minwidth=120, anchor='center', stretch=True)

        # Применить стили
        EnhancedStyles.style_treeview(fields['ingredients_tree'], 'table')

        # Добавить скроллбар
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=fields['ingredients_tree'].yview)
        fields['ingredients_tree'].configure(yscrollcommand=scrollbar.set)

        fields['ingredients_tree'].pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

    def create_semi_products_tab_for_new_recipe(self, parent, fields):
        """Создать вкладку полуфабрикатов для нового рецепта"""
        # Кнопки управления с небольшим отступом справа
        buttons_frame = tk.Frame(parent, bg='white')
        buttons_frame.pack(fill='x', padx=(0, 5), pady=1)

        add_btn = EnhancedStyles.success_button(buttons_frame, "➕ Добавить",
                                               lambda: self.add_semi_product_to_new_recipe(fields),
                                               padx=6, pady=3)
        add_btn.pack(side='left', padx=1)

        delete_btn = EnhancedStyles.danger_button(buttons_frame, "🗑️ Удалить",
                                                 lambda: self.delete_semi_product_from_new_recipe(fields),
                                                 padx=6, pady=3)
        delete_btn.pack(side='left', padx=1)

        # Таблица полуфабрикатов с небольшим отступом справа
        table_frame = tk.Frame(parent, bg='white', relief='flat', bd=0)
        table_frame.pack(fill='both', expand=True, padx=(0, 5), pady=0)

        # Создать Treeview с максимальной высотой и растянутый по ширине
        columns = ('Название', 'Количество', 'Единица', 'Стоимость', 'Описание')
        fields['semi_products_tree'] = ttk.Treeview(table_frame, columns=columns, show='headings', height=25)

        # Настроить заголовки с оптимальным растяжением по ширине
        # Пропорции: Название 35%, Количество 15%, Единица 10%, Стоимость 15%, Описание 25%
        for col in columns:
            fields['semi_products_tree'].heading(col, text=col)
            if col == 'Название':
                fields['semi_products_tree'].column(col, width=560, minwidth=300, anchor='w', stretch=True)
            elif col == 'Количество':
                fields['semi_products_tree'].column(col, width=240, minwidth=120, anchor='center', stretch=True)
            elif col == 'Единица':
                fields['semi_products_tree'].column(col, width=160, minwidth=80, anchor='center', stretch=True)
            elif col == 'Стоимость':
                fields['semi_products_tree'].column(col, width=240, minwidth=120, anchor='center', stretch=True)
            elif col == 'Описание':
                fields['semi_products_tree'].column(col, width=400, minwidth=250, anchor='w', stretch=True)

        # Применить стили
        EnhancedStyles.style_treeview(fields['semi_products_tree'], 'table')

        # Добавить скроллбар
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=fields['semi_products_tree'].yview)
        fields['semi_products_tree'].configure(yscrollcommand=scrollbar.set)

        fields['semi_products_tree'].pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

    def add_ingredient_to_new_recipe(self, fields):
        """Добавить ингредиент в новый рецепт"""
        # Создать увеличенный диалог добавления ингредиента
        dialog = tk.Toplevel()
        dialog.title("Добавить ингредиент")
        dialog.geometry("700x550")
        dialog.resizable(True, True)
        dialog.configure(bg='white')

        # Центрировать диалог
        dialog.update_idletasks()
        width = 700
        height = 550
        x = (dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog.winfo_screenheight() // 2) - (height // 2)
        dialog.geometry(f"{width}x{height}+{x}+{y}")

        # Поля ввода с увеличенными отступами
        form_frame = tk.Frame(dialog, bg='white')
        form_frame.pack(fill='both', expand=True, padx=40, pady=40)

        ingredient_fields = {}

        # Название
        tk.Label(form_frame, text="Название ингредиента:", font=('Cambria', 12, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))

        name_frame = tk.Frame(form_frame, bg='white')
        name_frame.pack(fill='x', pady=(0, 15))

        ingredient_fields['name'] = tk.Entry(name_frame, font=('Cambria', 12), width=30)
        ingredient_fields['name'].pack(side='left', fill='x', expand=True, padx=(0, 10))

        search_btn = EnhancedStyles.secondary_button(name_frame, "🔍 Поиск",
                                                    lambda: self.search_ingredient_for_new_recipe(ingredient_fields, dialog),
                                                    padx=10, pady=5)
        search_btn.pack(side='right')

        # Количество
        tk.Label(form_frame, text="Количество:", font=('Cambria', 12, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 8))
        ingredient_fields['amount'] = tk.Entry(form_frame, font=('Cambria', 12))
        ingredient_fields['amount'].pack(fill='x', pady=(0, 20))
        ingredient_fields['amount'].insert(0, "100")

        # Единица измерения
        tk.Label(form_frame, text="Единица измерения:", font=('Cambria', 12, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 8))
        ingredient_fields['unit'] = ttk.Combobox(form_frame, font=('Cambria', 12),
                                                values=["г", "кг", "мл", "л", "шт", "ст.л.", "ч.л."])
        ingredient_fields['unit'].pack(fill='x', pady=(0, 20))
        ingredient_fields['unit'].set("г")

        # Стоимость
        tk.Label(form_frame, text="Стоимость (руб):", font=('Cambria', 12, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 8))
        ingredient_fields['cost'] = tk.Entry(form_frame, font=('Cambria', 12))
        ingredient_fields['cost'].pack(fill='x', pady=(0, 20))
        ingredient_fields['cost'].insert(0, "0,00")

        # Кнопки с увеличенными отступами
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(fill='x', padx=40, pady=(20, 40))

        ok_btn = EnhancedStyles.success_button(buttons_frame, "✅ Добавить",
                                              lambda: self.save_ingredient_to_new_recipe(dialog, ingredient_fields, fields),
                                              padx=25, pady=12)
        ok_btn.pack(side='left', padx=(0, 15))

        cancel_btn = EnhancedStyles.secondary_button(buttons_frame, "❌ Отмена",
                                                    dialog.destroy, padx=25, pady=12)
        cancel_btn.pack(side='left')

        ingredient_fields['name'].focus()

    def save_ingredient_to_new_recipe(self, dialog, ingredient_fields, fields):
        """Сохранить ингредиент в новый рецепт"""
        try:
            name = ingredient_fields['name'].get().strip()
            amount = ingredient_fields['amount'].get().strip()
            unit = ingredient_fields['unit'].get().strip()
            cost_str = ingredient_fields['cost'].get().strip().replace(',', '.')

            if not name or not amount or not unit:
                messagebox.showerror("Ошибка", "Заполните все поля")
                return

            # Валидация количества
            try:
                amount_clean = amount.replace(',', '.') if amount else "0"
                amount_float = float(amount_clean)
                if amount_float <= 0:
                    messagebox.showerror("Ошибка", "Количество должно быть больше 0")
                    return
            except ValueError:
                messagebox.showerror("Ошибка", "Неправильный формат количества. Используйте числа (можно с запятой)")
                return

            # Валидация стоимости
            try:
                # Заменить запятую на точку для российского формата
                cost_clean = cost_str.replace(',', '.') if cost_str else "0"
                cost = float(cost_clean)
                if cost < 0:
                    messagebox.showerror("Ошибка", "Стоимость не может быть отрицательной")
                    return
            except ValueError:
                messagebox.showerror("Ошибка", "Неправильный формат стоимости. Используйте числа (можно с запятой)")
                return

            # Добавить в список
            ingredient = {
                'name': name,
                'amount': amount_float,  # Сохранить как число
                'unit': unit,
                'cost': cost
            }

            fields['ingredients_list'].append(ingredient)

            # Обновить таблицу
            self.update_ingredients_table_for_new_recipe(fields)

            # Пересчитать себестоимость
            self.recalculate_cost_for_new_recipe(fields)

            dialog.destroy()

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка добавления ингредиента: {str(e)}")

    def update_ingredients_table_for_new_recipe(self, fields):
        """Обновить таблицу ингредиентов для нового рецепта"""
        # Очистить таблицу
        for item in fields['ingredients_tree'].get_children():
            fields['ingredients_tree'].delete(item)

        # Добавить ингредиенты
        for i, ingredient in enumerate(fields['ingredients_list']):
            values = (
                ingredient['name'],
                ingredient['amount'],
                ingredient['unit'],
                f"{ingredient['cost']:.2f} руб"
            )
            fields['ingredients_tree'].insert('', 'end', iid=f"ing_{i}", values=values)

        # Добавить итоговую строку
        total_cost = sum(ing['cost'] for ing in fields['ingredients_list'])
        fields['ingredients_tree'].insert('', 'end', iid='total_ingredients',
                                         values=('ИТОГО ИНГРЕДИЕНТЫ', '', '', f"{total_cost:.2f} руб"))

    def recalculate_cost_for_new_recipe(self, fields):
        """Пересчитать общую себестоимость для нового рецепта"""
        try:
            # Проверить, что списки существуют
            if 'ingredients_list' not in fields:
                fields['ingredients_list'] = []
            if 'semi_products_list' not in fields:
                fields['semi_products_list'] = []

            # Сумма ингредиентов с проверкой на валидность
            ingredients_cost = 0.0
            for ing in fields['ingredients_list']:
                if isinstance(ing.get('cost'), (int, float)) and ing['cost'] >= 0:
                    ingredients_cost += ing['cost']

            # Сумма полуфабрикатов с проверкой на валидность
            semi_products_cost = 0.0
            for semi in fields['semi_products_list']:
                if isinstance(semi.get('cost'), (int, float)) and semi['cost'] >= 0:
                    semi_products_cost += semi['cost']

            # Общая себестоимость
            total_cost = ingredients_cost + semi_products_cost

            # Проверить, что поле стоимости существует
            if 'cost' in fields and fields['cost']:
                # Обновить поле себестоимости
                fields['cost'].delete(0, 'end')
                fields['cost'].insert(0, f"{total_cost:.2f}".replace('.', ','))

            print(f"DEBUG: Recalculated cost - Ingredients: {ingredients_cost:.2f}, Semi-products: {semi_products_cost:.2f}, Total: {total_cost:.2f}")

        except Exception as e:
            print(f"ERROR in recalculate_cost_for_new_recipe: {e}")
            # Не показывать ошибку пользователю, просто логировать

    def delete_ingredient_from_new_recipe(self, fields):
        """Удалить ингредиент из нового рецепта"""
        selection = fields['ingredients_tree'].selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите ингредиент для удаления")
            return

        item_id = selection[0]
        if item_id == 'total_ingredients':
            messagebox.showwarning("Предупреждение", "Нельзя удалить итоговую строку")
            return

        # Получить индекс ингредиента
        try:
            ingredient_index = int(item_id.split('_')[1])
            ingredient_name = fields['ingredients_list'][ingredient_index]['name']

            if messagebox.askyesno("Подтверждение", f"Удалить ингредиент '{ingredient_name}'?"):
                # Удалить из списка
                del fields['ingredients_list'][ingredient_index]

                # Обновить таблицу
                self.update_ingredients_table_for_new_recipe(fields)

                # Пересчитать стоимость
                self.recalculate_cost_for_new_recipe(fields)

        except (IndexError, ValueError) as e:
            messagebox.showerror("Ошибка", f"Ошибка удаления ингредиента: {str(e)}")

    def search_ingredient_for_new_recipe(self, ingredient_fields, parent_dialog):
        """Поиск ингредиента для нового рецепта"""
        # Использовать существующий диалог поиска ингредиентов
        self.open_ingredient_search_dialog(ingredient_fields, parent_dialog)

    def add_semi_product_to_new_recipe(self, fields):
        """Добавить полуфабрикат в новый рецепт"""
        # Создать увеличенный диалог добавления полуфабриката
        dialog = tk.Toplevel()
        dialog.title("Добавить полуфабрикат")
        dialog.geometry("800x650")
        dialog.resizable(True, True)
        dialog.configure(bg='white')

        # Центрировать диалог
        dialog.update_idletasks()
        width = 800
        height = 650
        x = (dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog.winfo_screenheight() // 2) - (height // 2)
        dialog.geometry(f"{width}x{height}+{x}+{y}")

        # Поля ввода с увеличенными отступами
        form_frame = tk.Frame(dialog, bg='white')
        form_frame.pack(fill='both', expand=True, padx=40, pady=40)

        semi_fields = {}

        # Название
        tk.Label(form_frame, text="Название полуфабриката:", font=('Cambria', 12, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))

        name_frame = tk.Frame(form_frame, bg='white')
        name_frame.pack(fill='x', pady=(0, 15))

        semi_fields['name'] = tk.Entry(name_frame, font=('Cambria', 12), width=30)
        semi_fields['name'].pack(side='left', fill='x', expand=True, padx=(0, 10))

        search_btn = EnhancedStyles.secondary_button(name_frame, "🔍 Поиск",
                                                    lambda: self.search_semi_product_for_new_recipe(semi_fields, dialog),
                                                    padx=10, pady=5)
        search_btn.pack(side='right')

        # Количество
        tk.Label(form_frame, text="Количество:", font=('Cambria', 12, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 8))
        semi_fields['amount'] = tk.Entry(form_frame, font=('Cambria', 12))
        semi_fields['amount'].pack(fill='x', pady=(0, 20))
        semi_fields['amount'].insert(0, "1")

        # Единица измерения
        tk.Label(form_frame, text="Единица измерения:", font=('Cambria', 12, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 8))
        semi_fields['unit'] = ttk.Combobox(form_frame, font=('Cambria', 12),
                                          values=["шт", "порция", "кг", "г", "л", "мл"])
        semi_fields['unit'].pack(fill='x', pady=(0, 20))
        semi_fields['unit'].set("шт")

        # Стоимость
        tk.Label(form_frame, text="Стоимость (руб):", font=('Cambria', 12, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 8))
        semi_fields['cost'] = tk.Entry(form_frame, font=('Cambria', 12))
        semi_fields['cost'].pack(fill='x', pady=(0, 20))
        semi_fields['cost'].insert(0, "0,00")

        # Описание
        tk.Label(form_frame, text="Описание:", font=('Cambria', 12, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 8))
        semi_fields['description'] = tk.Text(form_frame, font=('Cambria', 12), height=5)
        semi_fields['description'].pack(fill='x', pady=(0, 20))

        # Кнопки с увеличенными отступами
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(fill='x', padx=40, pady=(20, 40))

        ok_btn = EnhancedStyles.success_button(buttons_frame, "✅ Добавить",
                                              lambda: self.save_semi_product_to_new_recipe(dialog, semi_fields, fields),
                                              padx=25, pady=12)
        ok_btn.pack(side='left', padx=(0, 15))

        cancel_btn = EnhancedStyles.secondary_button(buttons_frame, "❌ Отмена",
                                                    dialog.destroy, padx=25, pady=12)
        cancel_btn.pack(side='left')

        semi_fields['name'].focus()

    def save_semi_product_to_new_recipe(self, dialog, semi_fields, fields):
        """Сохранить полуфабрикат в новый рецепт"""
        try:
            name = semi_fields['name'].get().strip()
            amount = semi_fields['amount'].get().strip()
            unit = semi_fields['unit'].get().strip()
            cost_str = semi_fields['cost'].get().strip().replace(',', '.')
            description = semi_fields['description'].get('1.0', 'end-1c').strip()

            if not name or not amount or not unit:
                messagebox.showerror("Ошибка", "Заполните все обязательные поля")
                return

            # Валидация количества
            try:
                amount_clean = amount.replace(',', '.') if amount else "0"
                amount_float = float(amount_clean)
                if amount_float <= 0:
                    messagebox.showerror("Ошибка", "Количество должно быть больше 0")
                    return
            except ValueError:
                messagebox.showerror("Ошибка", "Неправильный формат количества. Используйте числа (можно с запятой)")
                return

            # Валидация стоимости
            try:
                # Заменить запятую на точку для российского формата
                cost_clean = cost_str.replace(',', '.') if cost_str else "0"
                cost = float(cost_clean)
                if cost < 0:
                    messagebox.showerror("Ошибка", "Стоимость не может быть отрицательной")
                    return
            except ValueError:
                messagebox.showerror("Ошибка", "Неправильный формат стоимости. Используйте числа (можно с запятой)")
                return

            # Добавить в список
            semi_product = {
                'name': name,
                'amount': amount_float,  # Сохранить как число
                'unit': unit,
                'cost': cost,
                'description': description
            }

            fields['semi_products_list'].append(semi_product)

            # Обновить таблицу
            self.update_semi_products_table_for_new_recipe(fields)

            # Пересчитать себестоимость
            self.recalculate_cost_for_new_recipe(fields)

            dialog.destroy()

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка добавления полуфабриката: {str(e)}")

    def update_semi_products_table_for_new_recipe(self, fields):
        """Обновить таблицу полуфабрикатов для нового рецепта"""
        # Очистить таблицу
        for item in fields['semi_products_tree'].get_children():
            fields['semi_products_tree'].delete(item)

        # Добавить полуфабрикаты
        for i, semi in enumerate(fields['semi_products_list']):
            values = (
                semi['name'],
                semi['amount'],
                semi['unit'],
                f"{semi['cost']:.2f} руб",
                semi['description'][:50] + "..." if len(semi['description']) > 50 else semi['description']
            )
            fields['semi_products_tree'].insert('', 'end', iid=f"semi_{i}", values=values)

        # Добавить итоговую строку
        total_cost = sum(semi['cost'] for semi in fields['semi_products_list'])
        fields['semi_products_tree'].insert('', 'end', iid='total_semi',
                                           values=('ИТОГО ПОЛУФАБРИКАТЫ', '', '', f"{total_cost:.2f} руб", ''))

    def delete_semi_product_from_new_recipe(self, fields):
        """Удалить полуфабрикат из нового рецепта"""
        selection = fields['semi_products_tree'].selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите полуфабрикат для удаления")
            return

        item_id = selection[0]
        if item_id == 'total_semi':
            messagebox.showwarning("Предупреждение", "Нельзя удалить итоговую строку")
            return

        # Получить индекс полуфабриката
        try:
            semi_index = int(item_id.split('_')[1])
            semi_name = fields['semi_products_list'][semi_index]['name']

            if messagebox.askyesno("Подтверждение", f"Удалить полуфабрикат '{semi_name}'?"):
                # Удалить из списка
                del fields['semi_products_list'][semi_index]

                # Обновить таблицу
                self.update_semi_products_table_for_new_recipe(fields)

                # Пересчитать стоимость
                self.recalculate_cost_for_new_recipe(fields)

        except (IndexError, ValueError) as e:
            messagebox.showerror("Ошибка", f"Ошибка удаления полуфабриката: {str(e)}")

    def search_semi_product_for_new_recipe(self, semi_fields, parent_dialog):
        """Поиск полуфабриката для нового рецепта"""
        # Использовать существующий диалог поиска полуфабрикатов
        self.open_semi_product_search_dialog(semi_fields, parent_dialog)

    def create_new_recipe(self, dialog, fields):
        """Создать новый рецепт"""
        try:
            # Получить данные из полей
            name = fields['name'].get().strip()
            category = fields['category'].get().strip()
            portions = fields['portions'].get().strip()
            prep_time = fields['prep_time'].get().strip()
            cook_time = fields['cook_time'].get().strip()
            difficulty = fields['difficulty'].get().strip()
            cost = fields['cost'].get().strip()
            price = fields['price'].get().strip()

            # Отладочная информация
            print(f"DEBUG: Creating recipe with values:")
            print(f"  Name: '{name}'")
            print(f"  Category: '{category}'")
            print(f"  Portions: '{portions}'")
            print(f"  Cost: '{cost}'")
            print(f"  Price: '{price}'")

            # Валидация
            if not name:
                messagebox.showerror("Ошибка", "Введите название блюда")
                return

            if name in self.recipes:
                messagebox.showerror("Ошибка", f"Рецепт с названием '{name}' уже существует")
                return

            if not category:
                messagebox.showerror("Ошибка", "Выберите категорию")
                return

            # Преобразовать числовые значения с учетом российского формата
            try:
                # Количество порций
                portions_int = int(portions) if portions else 1

                # Стоимость - заменить запятую на точку для float
                cost_clean = cost.replace(',', '.') if cost else "0"
                cost_float = float(cost_clean)

                # Цена продажи - заменить запятую на точку для float
                price_clean = price.replace(',', '.') if price else "0"
                price_float = float(price_clean)

            except ValueError as e:
                messagebox.showerror("Ошибка", f"Проверьте правильность числовых значений:\n- Количество порций: целое число\n- Стоимость и цена: числа (можно с запятой)\nОшибка: {str(e)}")
                return

            if portions_int <= 0:
                messagebox.showerror("Ошибка", "Количество порций должно быть больше 0")
                return

            # Дополнительная проверка на отрицательные значения
            if cost_float < 0:
                messagebox.showerror("Ошибка", "Себестоимость не может быть отрицательной")
                return

            if price_float < 0:
                messagebox.showerror("Ошибка", "Цена продажи не может быть отрицательной")
                return

            # Создать новый рецепт
            new_recipe = {
                "category": category,
                "portions": portions_int,
                "prep_time": prep_time if prep_time else "15 мин",
                "cook_time": cook_time if cook_time else "30 мин",
                "difficulty": difficulty if difficulty else "Средняя",
                "cost": cost_float,
                "price": price_float,
                "ingredients": fields['ingredients_list'].copy(),
                "semi_products": fields['semi_products_list'].copy(),
                "instructions": []
            }

            # Добавить в словарь рецептов
            self.recipes[name] = new_recipe

            # Сохранить в файл
            try:
                self.save_recipes_to_file()
                print(f"DEBUG: New recipe '{name}' saved to file")
            except Exception as save_error:
                print(f"WARNING: Could not save to file: {save_error}")

            # Обновить интерфейс
            self.populate_recipes_tree()

            # Закрыть диалог
            dialog.destroy()

            # Показать сообщение об успехе
            messagebox.showinfo("Успех", f"Технологическая карта '{name}' успешно создана!")

            # Автоматически выбрать новый рецепт
            self.current_recipe = name
            self.show_recipe_details(name)

        except Exception as e:
            print(f"ERROR in create_new_recipe: {e}")
            messagebox.showerror("Ошибка", f"Произошла ошибка при создании рецепта: {str(e)}")

    def edit_recipe(self):
        """Редактировать технологическую карту"""
        if not self.current_recipe:
            messagebox.showwarning("Предупреждение", "Выберите технологическую карту для редактирования")
            return

        self.open_recipe_edit_dialog(self.current_recipe)

    def open_recipe_edit_dialog(self, recipe_name):
        """Открыть диалог редактирования технологической карты"""
        recipe = self.recipes.get(recipe_name)
        if not recipe:
            messagebox.showerror("Ошибка", f"Технологическая карта '{recipe_name}' не найдена")
            return

        # Создать диалоговое окно на полный экран
        edit_window = tk.Toplevel(self.window)
        edit_window.title(f"Редактирование технологической карты: {recipe_name}")
        edit_window.configure(bg='white')
        edit_window.resizable(True, True)

        # Открыть на полный экран
        edit_window.state('zoomed')  # Для Windows
        # Альтернативно для других ОС:
        # edit_window.attributes('-zoomed', True)  # Для Linux
        # edit_window.attributes('-fullscreen', True)  # Для macOS

        # Сделать окно модальным
        edit_window.transient(self.window)
        edit_window.grab_set()

        # Обработчик закрытия окна
        def on_closing():
            if messagebox.askokcancel("Закрытие", "Закрыть окно редактирования? Несохраненные изменения будут потеряны."):
                edit_window.destroy()

        edit_window.protocol("WM_DELETE_WINDOW", on_closing)

        # ВАЖНО: Сохранить ссылки СРАЗУ после создания окна
        self.edit_window = edit_window
        self.edit_recipe_name = recipe_name
        self.original_recipe_data = recipe.copy()  # Для сброса формы

        # Заголовок с фиксированной высотой
        header = tk.Frame(edit_window, bg=EnhancedStyles.COLORS['primary'], height=70)
        header.pack(fill='x')
        header.pack_propagate(False)

        # Левая часть заголовка
        title_frame = tk.Frame(header, bg=EnhancedStyles.COLORS['primary'])
        title_frame.pack(side='left', fill='y', padx=20, pady=10)

        # Иконка и короткий заголовок
        title_label = tk.Label(title_frame, text="✏️ Редактирование технологической карты",
                              font=('Cambria', 16, 'bold'), bg=EnhancedStyles.COLORS['primary'],
                              fg='white')
        title_label.pack(side='top', anchor='w')

        # Название карты отдельно
        name_label = tk.Label(title_frame, text=f"📋 {recipe_name}",
                             font=('Cambria', 14), bg=EnhancedStyles.COLORS['primary'],
                             fg='#ecf0f1')
        name_label.pack(side='top', anchor='w')

        # Кнопка закрытия в заголовке
        close_btn = EnhancedStyles.danger_button(header, "❌ Закрыть", on_closing, padx=15, pady=8)
        close_btn.pack(side='right', padx=20, pady=15)

        # Основная область на весь экран
        main_frame = tk.Frame(edit_window, bg=EnhancedStyles.COLORS['light'])
        main_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # Создать Notebook для вкладок с увеличенным размером
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill='both', expand=True)

        # Настроить стили для полноэкранного режима
        style = ttk.Style()
        style.configure('TNotebook.Tab', font=('Cambria', 12, 'bold'), padding=[20, 10])

        # Вкладка "Основная информация"
        info_frame = tk.Frame(notebook, bg='white')
        notebook.add(info_frame, text="📋 Основная информация")
        self.create_recipe_info_tab(info_frame, recipe, recipe_name)

        # Вкладка "Ингредиенты"
        ingredients_frame = tk.Frame(notebook, bg='white')
        notebook.add(ingredients_frame, text="🥘 Ингредиенты")
        self.create_recipe_ingredients_tab(ingredients_frame, recipe, recipe_name)

        # Вкладка "Полуфабрикаты"
        semi_frame = tk.Frame(notebook, bg='white')
        notebook.add(semi_frame, text="🧪 Полуфабрикаты")
        self.create_recipe_semi_tab(semi_frame, recipe, recipe_name)

        # Вкладка "Инструкции"
        instructions_frame = tk.Frame(notebook, bg='white')
        notebook.add(instructions_frame, text="📝 Инструкции")
        self.create_recipe_instructions_tab(instructions_frame, recipe, recipe_name)

        # Кнопки управления в нижней части экрана
        buttons_frame = tk.Frame(edit_window, bg=EnhancedStyles.COLORS['light'], height=80)
        buttons_frame.pack(fill='x', padx=30, pady=20)
        buttons_frame.pack_propagate(False)

        # Центрировать кнопки
        center_frame = tk.Frame(buttons_frame, bg=EnhancedStyles.COLORS['light'])
        center_frame.pack(expand=True)

        save_btn = EnhancedStyles.success_button(center_frame, "💾 Сохранить изменения",
                                                lambda: self.save_recipe_changes(edit_window, recipe_name),
                                                padx=30, pady=15)
        save_btn.pack(side='left', padx=15)

        cancel_btn = EnhancedStyles.secondary_button(center_frame, "❌ Отмена",
                                                    on_closing, padx=30, pady=15)
        cancel_btn.pack(side='left', padx=15)

        # Дополнительные кнопки
        reset_btn = EnhancedStyles.secondary_button(center_frame, "🔄 Сбросить",
                                                   lambda: self.reset_recipe_form(recipe_name), padx=30, pady=15)
        reset_btn.pack(side='left', padx=15)



    def reset_recipe_form(self, recipe_name):
        """Сбросить форму к исходным значениям"""
        if messagebox.askyesno("Сброс", "Сбросить все изменения к исходным значениям?"):
            recipe = self.original_recipe_data

            # Восстановить исходные значения
            self.edit_name_var.set(recipe_name)
            self.edit_category_var.set(recipe.get('category', ''))
            self.edit_portions_var.set(str(recipe.get('portions', 1)))
            self.edit_prep_time_var.set(recipe.get('prep_time', ''))
            self.edit_cook_time_var.set(recipe.get('cook_time', ''))
            self.edit_difficulty_var.set(recipe.get('difficulty', ''))
            self.edit_cost_var.set(str(recipe.get('cost', 0)))
            self.edit_price_var.set(str(recipe.get('price', 0)))

            messagebox.showinfo("Сброс", "Форма сброшена к исходным значениям")

    def create_recipe_info_tab(self, parent, recipe, recipe_name):
        """Создать вкладку основной информации для полноэкранного режима"""
        # Основной контейнер с отступами
        main_container = tk.Frame(parent, bg='white')
        main_container.pack(fill='both', expand=True, padx=50, pady=30)

        # Создать две колонки для лучшего использования пространства
        left_column = tk.Frame(main_container, bg='white')
        left_column.pack(side='left', fill='both', expand=True, padx=(0, 25))

        right_column = tk.Frame(main_container, bg='white')
        right_column.pack(side='right', fill='both', expand=True, padx=(25, 0))

        # ЛЕВАЯ КОЛОНКА - Основная информация
        info_section = tk.LabelFrame(left_column, text="📋 Основная информация",
                                    font=('Cambria', 14, 'bold'), bg='white', fg='#2c3e50')
        info_section.pack(fill='x', pady=(0, 20))

        # Название (на всю ширину)
        name_frame = tk.Frame(info_section, bg='white')
        name_frame.pack(fill='x', padx=20, pady=15)

        tk.Label(name_frame, text="Название:", font=('Cambria', 13, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))
        self.edit_name_var = tk.StringVar(value=recipe_name)
        name_entry = tk.Entry(name_frame, textvariable=self.edit_name_var,
                             font=('Cambria', 12), width=50)
        name_entry.pack(fill='x')

        # Категория
        category_frame = tk.Frame(info_section, bg='white')
        category_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(category_frame, text="Категория:", font=('Cambria', 13, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))
        self.edit_category_var = tk.StringVar(value=recipe.get('category', ''))
        category_combo = ttk.Combobox(category_frame, textvariable=self.edit_category_var,
                                     values=['Салаты', 'Супы', 'Горячие блюда', 'Десерты', 'Напитки', 'Закуски'],
                                     font=('Cambria', 12), width=47)
        category_combo.pack(fill='x')

        # Порции
        portions_frame = tk.Frame(info_section, bg='white')
        portions_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(portions_frame, text="Количество порций:", font=('Cambria', 13, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))
        self.edit_portions_var = tk.StringVar(value=str(recipe.get('portions', 1)))
        portions_entry = tk.Entry(portions_frame, textvariable=self.edit_portions_var,
                                 font=('Cambria', 12), width=20)
        portions_entry.pack(anchor='w')

        # ПРАВАЯ КОЛОНКА - Время приготовления
        time_section = tk.LabelFrame(right_column, text="⏱️ Время приготовления",
                                    font=('Cambria', 14, 'bold'), bg='white', fg='#2c3e50')
        time_section.pack(fill='x', pady=(0, 20))

        # Время подготовки
        prep_frame = tk.Frame(time_section, bg='white')
        prep_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(prep_frame, text="Время подготовки:", font=('Cambria', 13, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))
        self.edit_prep_time_var = tk.StringVar(value=recipe.get('prep_time', ''))
        prep_entry = tk.Entry(prep_frame, textvariable=self.edit_prep_time_var,
                             font=('Cambria', 12), width=30)
        prep_entry.pack(anchor='w')

        # Время готовки
        cook_frame = tk.Frame(time_section, bg='white')
        cook_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(cook_frame, text="Время готовки:", font=('Cambria', 13, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))
        self.edit_cook_time_var = tk.StringVar(value=recipe.get('cook_time', ''))
        cook_entry = tk.Entry(cook_frame, textvariable=self.edit_cook_time_var,
                             font=('Cambria', 12), width=30)
        cook_entry.pack(anchor='w')

        # Сложность
        difficulty_frame = tk.Frame(time_section, bg='white')
        difficulty_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(difficulty_frame, text="Сложность:", font=('Cambria', 13, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))
        self.edit_difficulty_var = tk.StringVar(value=recipe.get('difficulty', ''))
        difficulty_combo = ttk.Combobox(difficulty_frame, textvariable=self.edit_difficulty_var,
                                       values=['Лёгкая', 'Средняя', 'Высокая'],
                                       font=('Cambria', 12), width=27)
        difficulty_combo.pack(anchor='w')

        # Финансовая информация (в правой колонке)
        finance_section = tk.LabelFrame(right_column, text="💰 Финансовая информация",
                                       font=('Cambria', 14, 'bold'), bg='white', fg='#2c3e50')
        finance_section.pack(fill='x', pady=(20, 0))

        # Себестоимость
        cost_frame = tk.Frame(finance_section, bg='white')
        cost_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(cost_frame, text="Себестоимость (₽):", font=('Cambria', 13, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))
        self.edit_cost_var = tk.StringVar(value=str(recipe.get('cost', 0)))
        cost_entry = tk.Entry(cost_frame, textvariable=self.edit_cost_var,
                             font=('Cambria', 12), width=25)
        cost_entry.pack(anchor='w')

        # Цена продажи
        price_frame = tk.Frame(finance_section, bg='white')
        price_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(price_frame, text="Цена продажи (₽):", font=('Cambria', 13, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))
        self.edit_price_var = tk.StringVar(value=str(recipe.get('price', 0)))
        price_entry = tk.Entry(price_frame, textvariable=self.edit_price_var,
                              font=('Cambria', 12), width=25)
        price_entry.pack(anchor='w')

    def create_recipe_ingredients_tab(self, parent, recipe, recipe_name):
        """Создать вкладку ингредиентов"""
        # Основной контейнер
        main_container = tk.Frame(parent, bg='white')
        main_container.pack(fill='both', expand=True, padx=30, pady=20)

        # Заголовок секции
        header_frame = tk.Frame(main_container, bg='white')
        header_frame.pack(fill='x', pady=(0, 20))

        title_label = tk.Label(header_frame, text="🥘 Ингредиенты технологической карты",
                              font=('Cambria', 16, 'bold'), bg='white', fg='#2c3e50')
        title_label.pack(side='left')

        # Кнопки управления ингредиентами
        buttons_frame = tk.Frame(header_frame, bg='white')
        buttons_frame.pack(side='right')

        def add_click():
            print("=== ADD INGREDIENT BUTTON CLICKED ===")
            self.add_ingredient_dialog(recipe_name)

        print("DEBUG: Creating add ingredient button...")
        add_btn = EnhancedStyles.success_button(buttons_frame, "➕ Добавить ингредиент", add_click, padx=15, pady=8)
        add_btn.pack(side='left', padx=5)
        print("DEBUG: Add ingredient button created")

        edit_btn = EnhancedStyles.secondary_button(buttons_frame, "✏️ Редактировать",
                                                  lambda: self.edit_selected_ingredient(), padx=15, pady=8)
        edit_btn.pack(side='left', padx=5)

        delete_btn = EnhancedStyles.danger_button(buttons_frame, "🗑️ Удалить",
                                                 lambda: self.delete_selected_ingredient(), padx=15, pady=8)
        delete_btn.pack(side='left', padx=5)

        # Кнопка тестирования (временная)
        def test_click():
            print("=== TEST BUTTON CLICKED ===")
            messagebox.showinfo("Тест", "Кнопка 🧪 Тест работает!")

        print("DEBUG: Creating test button...")
        test_btn = EnhancedStyles.secondary_button(buttons_frame, "🧪 Тест", test_click, padx=15, pady=8)
        test_btn.pack(side='left', padx=5)
        print("DEBUG: Test button created and packed")

        # Таблица ингредиентов
        table_frame = tk.Frame(main_container, bg='white', relief='solid', bd=2)
        table_frame.pack(fill='both', expand=True)

        # TreeView для ингредиентов
        columns = ('Название', 'Количество', 'Единица', 'Стоимость')
        self.ingredients_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # Настроить колонки
        column_widths = {
            'Название': 300,
            'Количество': 150,
            'Единица': 120,
            'Стоимость': 150
        }

        for col in columns:
            self.ingredients_tree.heading(col, text=col)
            self.ingredients_tree.column(col, width=column_widths[col], anchor='center')

        # Применить стили
        EnhancedStyles.style_treeview(self.ingredients_tree, 'table')

        # Заполнить данными
        self.populate_ingredients_table(recipe)

        # Скроллбар
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.ingredients_tree.yview)
        self.ingredients_tree.configure(yscrollcommand=scrollbar.set)

        # Упаковка
        self.ingredients_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # Привязать двойной клик для редактирования
        self.ingredients_tree.bind('<Double-1>', lambda e: self.edit_selected_ingredient())

    def populate_ingredients_table(self, recipe):
        """Заполнить таблицу ингредиентов"""
        print(f"DEBUG: populate_ingredients_table called with {len(recipe.get('ingredients', []))} ingredients")

        # Проверить, существует ли таблица
        if not hasattr(self, 'ingredients_tree') or self.ingredients_tree is None:
            print("ERROR: ingredients_tree not found!")
            return

        # Очистить таблицу
        for item in self.ingredients_tree.get_children():
            self.ingredients_tree.delete(item)

        print(f"DEBUG: Table cleared, adding ingredients...")

        # Добавить ингредиенты
        total_cost = 0
        for i, ingredient in enumerate(recipe.get('ingredients', [])):
            cost = ingredient.get('cost', 0)
            total_cost += cost

            values = (
                ingredient['name'],
                ingredient['amount'],
                ingredient['unit'],
                f"{cost:.2f} руб"
            )
            self.ingredients_tree.insert('', 'end', iid=f"ing_{i}", values=values)
            print(f"DEBUG: Added ingredient {i+1}: {ingredient['name']}")

        # Добавить итоговую строку
        if recipe.get('ingredients'):
            self.ingredients_tree.insert('', 'end', iid='total_ingredients',
                                        values=('ИТОГО ИНГРЕДИЕНТЫ', '', '', f"{total_cost:.2f} руб"))

    def add_ingredient_dialog(self, recipe_name):
        """Диалог добавления ингредиента"""
        print(f"=== ADD_INGREDIENT_DIALOG CALLED for {recipe_name} ===")
        self.open_ingredient_dialog(recipe_name, "add")

    def edit_selected_ingredient(self):
        """Редактировать выбранный ингредиент"""
        selection = self.ingredients_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите ингредиент для редактирования")
            return

        item_id = selection[0]
        if item_id.startswith('total'):
            messagebox.showwarning("Предупреждение", "Нельзя редактировать итоговую строку")
            return

        # Получить индекс ингредиента
        ingredient_index = int(item_id.split('_')[1])
        self.open_ingredient_dialog(self.edit_recipe_name, "edit", ingredient_index)

    def delete_selected_ingredient(self):
        """Удалить выбранный ингредиент"""
        selection = self.ingredients_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите ингредиент для удаления")
            return

        item_id = selection[0]
        if item_id.startswith('total'):
            messagebox.showwarning("Предупреждение", "Нельзя удалить итоговую строку")
            return

        values = self.ingredients_tree.item(item_id, 'values')
        if messagebox.askyesno("Подтверждение", f"Удалить ингредиент '{values[0]}'?"):
            # Получить индекс ингредиента
            ingredient_index = int(item_id.split('_')[1])

            # Удалить из данных
            recipe = self.recipes[self.edit_recipe_name]
            if ingredient_index < len(recipe.get('ingredients', [])):
                del recipe['ingredients'][ingredient_index]

                # Обновить таблицу
                self.populate_ingredients_table(recipe)

                messagebox.showinfo("Успех", f"Ингредиент '{values[0]}' удален")

    def open_ingredient_dialog(self, recipe_name, mode, ingredient_index=None):
        """Открыть диалог добавления/редактирования ингредиента"""
        print(f"=== OPEN_INGREDIENT_DIALOG CALLED: mode={mode}, recipe={recipe_name} ===")

        # Получить данные ингредиента для редактирования
        ingredient_data = None
        if mode == "edit" and ingredient_index is not None:
            recipe = self.recipes[recipe_name]
            ingredients = recipe.get('ingredients', [])
            if ingredient_index < len(ingredients):
                ingredient_data = ingredients[ingredient_index]

        print("DEBUG: Creating dialog window...")
        # Создать диалоговое окно
        dialog = tk.Toplevel(self.edit_window)
        title = "Добавление ингредиента" if mode == "add" else "Редактирование ингредиента"
        dialog.title(title)
        dialog.geometry("700x600")  # Увеличили размер для лучшей видимости
        dialog.resizable(True, True)  # Разрешить изменение размера
        dialog.configure(bg='white')

        # Центрировать диалог на экране
        dialog.update_idletasks()
        width = 700
        height = 600
        x = (dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog.winfo_screenheight() // 2) - (height // 2)
        dialog.geometry(f"{width}x{height}+{x}+{y}")

        print("DEBUG: Dialog window created and centered")

        # Центрировать окно
        dialog.transient(self.edit_window)
        # НЕ ДЕЛАЕМ ДИАЛОГ МОДАЛЬНЫМ - это может вызывать проблемы
        # dialog.grab_set()

        # Заголовок
        header = tk.Frame(dialog, bg=EnhancedStyles.COLORS['success'], height=60)
        header.pack(fill='x')
        header.pack_propagate(False)

        icon = "➕" if mode == "add" else "✏️"
        title_label = tk.Label(header, text=f"{icon} {title}",
                              font=('Cambria', 16, 'bold'), bg=EnhancedStyles.COLORS['success'], fg='white')
        title_label.pack(pady=15)

        # Основная форма
        form_frame = tk.Frame(dialog, bg='white')
        form_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # Поля ввода
        fields = {}

        # Название ингредиента с кнопкой поиска
        tk.Label(form_frame, text="Название ингредиента:", font=('Cambria', 12, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))

        name_frame = tk.Frame(form_frame, bg='white')
        name_frame.pack(fill='x', pady=(0, 15))

        fields['name'] = tk.Entry(name_frame, font=('Cambria', 11), width=35)
        fields['name'].pack(side='left', fill='x', expand=True)

        search_btn = EnhancedStyles.secondary_button(name_frame, "🔍 Поиск",
                                                     lambda: self.open_ingredient_search_dialog(fields, dialog),
                                                     padx=10, pady=5)
        search_btn.pack(side='right', padx=(10, 0))

        # Количество
        tk.Label(form_frame, text="Количество:", font=('Cambria', 12, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))
        fields['amount'] = tk.Entry(form_frame, font=('Cambria', 11), width=20)
        fields['amount'].pack(anchor='w', pady=(0, 15))

        # Единица измерения
        tk.Label(form_frame, text="Единица измерения:", font=('Cambria', 12, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))
        fields['unit'] = ttk.Combobox(form_frame, font=('Cambria', 11), width=37,
                                     values=['г', 'кг', 'мл', 'л', 'шт', 'ст.л.', 'ч.л.', 'стакан'])
        fields['unit'].pack(anchor='w', pady=(0, 15))

        # Стоимость
        tk.Label(form_frame, text="Стоимость (руб):", font=('Cambria', 12, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))
        fields['cost'] = tk.Entry(form_frame, font=('Cambria', 11), width=20)
        fields['cost'].pack(anchor='w', pady=(0, 15))

        # Заполнить поля при редактировании
        if ingredient_data:
            fields['name'].insert(0, ingredient_data.get('name', ''))
            fields['amount'].insert(0, str(ingredient_data.get('amount', '')))
            fields['unit'].set(ingredient_data.get('unit', ''))
            fields['cost'].insert(0, str(ingredient_data.get('cost', 0)))

        # Кнопки
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(fill='x', padx=30, pady=20)

        # ПОЛНАЯ ФУНКЦИОНАЛЬНОСТЬ БЕЗ МОДАЛЬНОСТИ
        def full_save():
            print("=== FULL SAVE CLICKED ===")
            try:
                # Получить данные из полей
                name = fields['name'].get().strip()
                amount = fields['amount'].get().strip()
                unit = fields['unit'].get().strip()
                cost_str = fields['cost'].get().strip()

                print(f"DEBUG: Field values - name: '{name}', amount: '{amount}', unit: '{unit}', cost: '{cost_str}'")

                # Валидация
                if not name:
                    messagebox.showerror("Ошибка", "Введите название ингредиента")
                    return

                if not amount:
                    messagebox.showerror("Ошибка", "Введите количество")
                    return

                if not unit:
                    messagebox.showerror("Ошибка", "Выберите единицу измерения")
                    return

                # Преобразовать стоимость
                try:
                    cost = float(cost_str) if cost_str else 0.0
                except ValueError:
                    messagebox.showerror("Ошибка", "Стоимость должна быть числом")
                    return

                # Создать объект ингредиента
                ingredient = {
                    'name': name,
                    'amount': amount,
                    'unit': unit,
                    'cost': cost
                }

                print(f"DEBUG: Created ingredient object: {ingredient}")

                # Добавить в рецепт
                if recipe_name in self.recipes:
                    recipe = self.recipes[recipe_name]
                    if 'ingredients' not in recipe:
                        recipe['ingredients'] = []

                    recipe['ingredients'].append(ingredient)
                    print(f"DEBUG: Added ingredient. Recipe now has {len(recipe['ingredients'])} ingredients")

                    # Сохранить изменения в файл
                    try:
                        self.save_recipes_to_file()
                        print("DEBUG: Recipes saved to file after adding ingredient")
                    except Exception as save_error:
                        print(f"WARNING: Could not save to file: {save_error}")

                    # Обновить таблицу
                    try:
                        if hasattr(self, 'ingredients_tree') and self.ingredients_tree is not None:
                            print("DEBUG: Updating ingredients table...")
                            self.populate_ingredients_table(recipe)
                            print("DEBUG: Table updated successfully")
                        else:
                            print("WARNING: ingredients_tree not found")
                    except Exception as table_error:
                        print(f"WARNING: Could not update table: {table_error}")

                    # Закрыть диалог
                    dialog.destroy()
                    print("DEBUG: Dialog destroyed")

                    # Показать сообщение об успехе
                    messagebox.showinfo("Успех", f"Ингредиент '{name}' добавлен и сохранен")
                    print("DEBUG: Success message shown")

                else:
                    messagebox.showerror("Ошибка", f"Рецепт '{recipe_name}' не найден")

            except Exception as e:
                print(f"ERROR in full_save: {e}")
                messagebox.showerror("Ошибка", f"Произошла ошибка: {str(e)}")

        print("DEBUG: Creating full OK button...")
        ok_btn = EnhancedStyles.success_button(buttons_frame, "✅ ОК", full_save, padx=25, pady=12)
        ok_btn.pack(side='left', padx=(0, 15))
        print("DEBUG: Full OK button created")
        ok_btn.pack(side='left', padx=(0, 15))

        # Кнопка Отмена
        cancel_btn = EnhancedStyles.secondary_button(buttons_frame, "❌ Отмена",
                                                    dialog.destroy, padx=20, pady=12)
        cancel_btn.pack(side='left')

        # Привязать клавиши
        def on_enter(event):
            try:
                self.save_ingredient(dialog, fields, recipe_name, mode, ingredient_index)
            except Exception as e:
                print(f"ERROR in on_enter: {e}")

        def on_escape(event):
            dialog.destroy()

        dialog.bind('<Return>', on_enter)
        dialog.bind('<Escape>', on_escape)

        # Фокус на первое поле
        fields['name'].focus()

    def save_ingredient(self, dialog, fields, recipe_name, mode, ingredient_index=None):
        """Сохранить ингредиент - ПОЛНАЯ ВЕРСИЯ"""
        print("=== SAVE INGREDIENT START ===")

        try:
            # Получить данные из полей
            name = fields['name'].get().strip()
            amount = fields['amount'].get().strip()
            unit = fields['unit'].get().strip()
            cost_str = fields['cost'].get().strip()

            print(f"DEBUG: Field values - name: '{name}', amount: '{amount}', unit: '{unit}', cost: '{cost_str}'")

            # Валидация
            if not name:
                messagebox.showerror("Ошибка", "Введите название ингредиента")
                return False

            if not amount:
                messagebox.showerror("Ошибка", "Введите количество")
                return False

            if not unit:
                messagebox.showerror("Ошибка", "Выберите единицу измерения")
                return False

            # Преобразовать стоимость
            try:
                cost = float(cost_str) if cost_str else 0.0
            except ValueError:
                messagebox.showerror("Ошибка", "Стоимость должна быть числом")
                return False

            # Создать объект ингредиента
            ingredient = {
                'name': name,
                'amount': amount,
                'unit': unit,
                'cost': cost
            }

            print(f"DEBUG: Created ingredient object: {ingredient}")

            # Проверить, существует ли рецепт
            if recipe_name not in self.recipes:
                messagebox.showerror("Ошибка", f"Рецепт '{recipe_name}' не найден")
                return False

            # Получить рецепт
            recipe = self.recipes[recipe_name]
            if 'ingredients' not in recipe:
                recipe['ingredients'] = []

            print(f"DEBUG: Recipe before adding: {len(recipe['ingredients'])} ingredients")

            # Добавить или обновить ингредиент
            if mode == "add":
                recipe['ingredients'].append(ingredient)
                message = f"Ингредиент '{name}' добавлен"
                print(f"DEBUG: Added ingredient. Recipe now has {len(recipe['ingredients'])} ingredients")
            else:
                if ingredient_index is not None and ingredient_index < len(recipe['ingredients']):
                    recipe['ingredients'][ingredient_index] = ingredient
                    message = f"Ингредиент '{name}' обновлен"
                    print(f"DEBUG: Updated ingredient at index {ingredient_index}")
                else:
                    messagebox.showerror("Ошибка", "Ошибка при обновлении ингредиента")
                    return False

            # Обновить таблицу ингредиентов
            print("DEBUG: Attempting to update ingredients table...")
            try:
                if hasattr(self, 'ingredients_tree') and self.ingredients_tree is not None:
                    print("DEBUG: ingredients_tree found, calling populate_ingredients_table...")
                    self.populate_ingredients_table(recipe)
                    print("DEBUG: Table updated successfully")
                else:
                    print("WARNING: ingredients_tree not found")
            except Exception as table_error:
                print(f"WARNING: Could not update table: {table_error}")

            # Закрыть диалог
            dialog.destroy()
            print("DEBUG: Dialog destroyed")

            # Показать сообщение об успехе
            messagebox.showinfo("Успех", message)
            print("DEBUG: Success message shown")

            return True

        except Exception as e:
            print(f"ERROR in save_ingredient: {e}")
            messagebox.showerror("Ошибка", f"Произошла ошибка при сохранении: {str(e)}")
            return False

    def test_add_simple_ingredient(self):
        """Простой тест"""
        print("=== TEST BUTTON CLICKED ===")
        messagebox.showinfo("Тест", "Кнопка тест работает!")
        return True

    def save_ingredient_isolated(self, dialog, fields, recipe_name, mode, ingredient_index=None):
        """Полностью изолированная версия сохранения ингредиента"""
        print("=== SAVE INGREDIENT ISOLATED START ===")

        try:
            # Получить данные из полей с проверкой
            try:
                name = fields['name'].get().strip() if fields.get('name') else ""
                amount = fields['amount'].get().strip() if fields.get('amount') else ""
                unit = fields['unit'].get().strip() if fields.get('unit') else ""
                cost_str = fields['cost'].get().strip() if fields.get('cost') else "0"
            except Exception as field_error:
                print(f"ERROR getting field values: {field_error}")
                messagebox.showerror("Ошибка", "Ошибка чтения полей формы")
                return False

            print(f"DEBUG: Field values - name: '{name}', amount: '{amount}', unit: '{unit}', cost: '{cost_str}'")

            # Простая валидация
            if not name:
                messagebox.showerror("Ошибка", "Введите название ингредиента")
                return False

            if not amount:
                messagebox.showerror("Ошибка", "Введите количество")
                return False

            if not unit:
                messagebox.showerror("Ошибка", "Выберите единицу измерения")
                return False

            # Преобразовать стоимость
            try:
                cost = float(cost_str) if cost_str else 0.0
            except ValueError:
                messagebox.showerror("Ошибка", "Стоимость должна быть числом")
                return False

            # Создать объект ингредиента
            ingredient = {
                'name': name,
                'amount': amount,
                'unit': unit,
                'cost': cost
            }

            print(f"DEBUG: Created ingredient object: {ingredient}")

            # Проверить рецепт
            try:
                if not hasattr(self, 'recipes') or recipe_name not in self.recipes:
                    messagebox.showerror("Ошибка", f"Рецепт '{recipe_name}' не найден")
                    return False

                recipe = self.recipes[recipe_name]
                if 'ingredients' not in recipe:
                    recipe['ingredients'] = []

                print(f"DEBUG: Recipe before: {len(recipe['ingredients'])} ingredients")

                # Добавить ингредиент
                if mode == "add":
                    recipe['ingredients'].append(ingredient)
                    print(f"DEBUG: Added ingredient. Recipe now has {len(recipe['ingredients'])} ingredients")
                    message = f"Ингредиент '{name}' добавлен"
                else:
                    if ingredient_index is not None and ingredient_index < len(recipe['ingredients']):
                        recipe['ingredients'][ingredient_index] = ingredient
                        message = f"Ингредиент '{name}' обновлен"
                    else:
                        messagebox.showerror("Ошибка", "Ошибка при обновлении ингредиента")
                        return False

            except Exception as recipe_error:
                print(f"ERROR working with recipe: {recipe_error}")
                messagebox.showerror("Ошибка", f"Ошибка работы с рецептом: {str(recipe_error)}")
                return False

            # Попытаться обновить таблицу (не критично если не получится)
            try:
                if hasattr(self, 'ingredients_tree') and self.ingredients_tree is not None:
                    print("DEBUG: Updating table...")
                    self.populate_ingredients_table(recipe)
                    print("DEBUG: Table updated")
                else:
                    print("WARNING: Table not found")
            except Exception as table_error:
                print(f"WARNING: Table update failed: {table_error}")

            # Закрыть диалог
            try:
                dialog.destroy()
                print("DEBUG: Dialog closed")
            except Exception as dialog_error:
                print(f"WARNING: Could not close dialog: {dialog_error}")

            # Показать сообщение
            try:
                messagebox.showinfo("Успех", message)
                print("DEBUG: Success message shown")
            except Exception as msg_error:
                print(f"WARNING: Could not show message: {msg_error}")

            return True

        except Exception as e:
            print(f"CRITICAL ERROR in save_ingredient_isolated: {e}")
            try:
                messagebox.showerror("Критическая ошибка", f"Критическая ошибка: {str(e)}")
            except:
                print("ERROR: Could not show critical error message")
            return False

    def open_ingredient_search_dialog(self, fields, parent_dialog):
        """Открыть диалог поиска ингредиентов"""
        # Создать диалог поиска
        search_dialog = tk.Toplevel(parent_dialog)
        search_dialog.title("Поиск ингредиентов")
        search_dialog.geometry("900x700")  # Увеличили размер
        search_dialog.resizable(True, True)
        search_dialog.configure(bg='white')

        # Центрировать диалог на экране
        search_dialog.update_idletasks()
        width = 900
        height = 700
        x = (search_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (search_dialog.winfo_screenheight() // 2) - (height // 2)
        search_dialog.geometry(f"{width}x{height}+{x}+{y}")

        # Центрировать окно
        search_dialog.transient(parent_dialog)
        # НЕ ДЕЛАЕМ ДИАЛОГ ПОИСКА ИНГРЕДИЕНТОВ МОДАЛЬНЫМ
        # search_dialog.grab_set()

        # Заголовок
        header = tk.Frame(search_dialog, bg=EnhancedStyles.COLORS['info'], height=60)
        header.pack(fill='x')
        header.pack_propagate(False)

        title_label = tk.Label(header, text="🔍 Поиск и выбор ингредиента",
                              font=('Cambria', 16, 'bold'), bg=EnhancedStyles.COLORS['info'], fg='white')
        title_label.pack(pady=15)

        # Основная область
        main_frame = tk.Frame(search_dialog, bg='white')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Поле поиска
        search_frame = tk.Frame(main_frame, bg='white')
        search_frame.pack(fill='x', pady=(0, 15))

        tk.Label(search_frame, text="Поиск:", font=('Cambria', 12, 'bold'),
                bg='white', fg='#2c3e50').pack(side='left', padx=(0, 10))

        search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=search_var, font=('Cambria', 11), width=30)
        search_entry.pack(side='left', fill='x', expand=True)

        # Создать список всех уникальных ингредиентов из всех рецептов
        all_ingredients = self.get_all_unique_ingredients()

        # Список ингредиентов
        list_frame = tk.Frame(main_frame, bg='white', relief='solid', bd=2)
        list_frame.pack(fill='both', expand=True, pady=(0, 15))

        # TreeView для списка ингредиентов
        columns = ('Название', 'Единица', 'Средняя стоимость', 'Используется в')
        ingredients_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=12)

        # Настроить колонки
        column_widths = {
            'Название': 200,
            'Единица': 80,
            'Средняя стоимость': 120,
            'Используется в': 150
        }

        for col in columns:
            ingredients_tree.heading(col, text=col)
            ingredients_tree.column(col, width=column_widths[col], anchor='center')

        # Применить стили
        EnhancedStyles.style_treeview(ingredients_tree, 'table')

        # Заполнить список ингредиентов
        def populate_ingredients_list(filter_text=""):
            # Очистить список
            for item in ingredients_tree.get_children():
                ingredients_tree.delete(item)

            # Добавить отфильтрованные ингредиенты
            for ingredient_info in all_ingredients:
                if filter_text.lower() in ingredient_info['name'].lower():
                    values = (
                        ingredient_info['name'],
                        ingredient_info['unit'],
                        f"{ingredient_info['avg_cost']:.2f} руб",
                        f"{ingredient_info['recipe_count']} рецептов"
                    )
                    ingredients_tree.insert('', 'end', values=values)

        # Изначально показать все ингредиенты
        populate_ingredients_list()

        # Привязать поиск к изменению текста
        def on_search_change(*args):
            populate_ingredients_list(search_var.get())

        search_var.trace('w', on_search_change)

        # Скроллбар
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=ingredients_tree.yview)
        ingredients_tree.configure(yscrollcommand=scrollbar.set)

        # Упаковка
        ingredients_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # Кнопки
        buttons_frame = tk.Frame(search_dialog, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=20)

        def select_ingredient():
            selection = ingredients_tree.selection()
            if not selection:
                messagebox.showwarning("Предупреждение", "Выберите ингредиент из списка")
                return

            # Получить выбранный ингредиент
            item = ingredients_tree.item(selection[0])
            ingredient_name = item['values'][0]
            ingredient_unit = item['values'][1]

            # Найти полную информацию об ингредиенте
            selected_ingredient = None
            for ing in all_ingredients:
                if ing['name'] == ingredient_name:
                    selected_ingredient = ing
                    break

            if selected_ingredient:
                # Заполнить поля в родительском диалоге
                fields['name'].delete(0, 'end')
                fields['name'].insert(0, selected_ingredient['name'])

                # Заполнить количество по умолчанию если поле пустое
                if not fields['amount'].get().strip():
                    fields['amount'].delete(0, 'end')
                    fields['amount'].insert(0, "100")  # Значение по умолчанию

                fields['unit'].set(selected_ingredient['unit'])
                fields['cost'].delete(0, 'end')
                fields['cost'].insert(0, f"{selected_ingredient['avg_cost']:.2f}")

                # Закрыть ТОЛЬКО диалог поиска
                search_dialog.destroy()

                # Установить фокус на поле количества для удобства редактирования
                fields['amount'].focus()
                fields['amount'].select_range(0, 'end')

                print(f"DEBUG: Ingredient '{ingredient_name}' selected and fields filled")

        select_btn = EnhancedStyles.success_button(buttons_frame, "✅ Выбрать",
                                                  select_ingredient, padx=20, pady=10)
        select_btn.pack(side='left', padx=(0, 10))

        cancel_btn = EnhancedStyles.secondary_button(buttons_frame, "❌ Отмена",
                                                    search_dialog.destroy, padx=20, pady=10)
        cancel_btn.pack(side='left')

        # Фокус на поле поиска
        search_entry.focus()

        # Привязать двойной клик для выбора
        ingredients_tree.bind('<Double-1>', lambda e: select_ingredient())

    def get_all_unique_ingredients(self):
        """Получить все уникальные ингредиенты из всех рецептов"""
        ingredients_dict = {}

        # Пройти по всем рецептам
        for recipe_name, recipe_data in self.recipes.items():
            ingredients = recipe_data.get('ingredients', [])

            for ingredient in ingredients:
                name = ingredient.get('name', '')
                unit = ingredient.get('unit', '')
                cost = ingredient.get('cost', 0)

                if name:
                    if name not in ingredients_dict:
                        ingredients_dict[name] = {
                            'name': name,
                            'unit': unit,
                            'costs': [cost],
                            'recipes': [recipe_name]
                        }
                    else:
                        # Добавить стоимость и рецепт
                        ingredients_dict[name]['costs'].append(cost)
                        if recipe_name not in ingredients_dict[name]['recipes']:
                            ingredients_dict[name]['recipes'].append(recipe_name)

                        # Обновить единицу измерения если она не была установлена
                        if not ingredients_dict[name]['unit'] and unit:
                            ingredients_dict[name]['unit'] = unit

        # Преобразовать в список с вычисленными средними значениями
        result = []
        for ingredient_info in ingredients_dict.values():
            avg_cost = sum(ingredient_info['costs']) / len(ingredient_info['costs']) if ingredient_info['costs'] else 0
            result.append({
                'name': ingredient_info['name'],
                'unit': ingredient_info['unit'],
                'avg_cost': avg_cost,
                'recipe_count': len(ingredient_info['recipes']),
                'recipes': ingredient_info['recipes']
            })

        # Сортировать по названию
        result.sort(key=lambda x: x['name'])
        return result

    def create_recipe_semi_tab(self, parent, recipe, recipe_name):
        """Создать вкладку полуфабрикатов"""
        # Основной контейнер
        main_container = tk.Frame(parent, bg='white')
        main_container.pack(fill='both', expand=True, padx=30, pady=20)

        # Заголовок секции
        header_frame = tk.Frame(main_container, bg='white')
        header_frame.pack(fill='x', pady=(0, 20))

        title_label = tk.Label(header_frame, text="🧪 Полуфабрикаты технологической карты",
                              font=('Cambria', 16, 'bold'), bg='white', fg='#2c3e50')
        title_label.pack(side='left')

        # Кнопки управления полуфабрикатами
        buttons_frame = tk.Frame(header_frame, bg='white')
        buttons_frame.pack(side='right')

        add_btn = EnhancedStyles.success_button(buttons_frame, "➕ Добавить полуфабрикат",
                                               lambda: self.add_semi_product_dialog(recipe_name), padx=15, pady=8)
        add_btn.pack(side='left', padx=5)

        edit_btn = EnhancedStyles.secondary_button(buttons_frame, "✏️ Редактировать",
                                                  lambda: self.edit_selected_semi_product(), padx=15, pady=8)
        edit_btn.pack(side='left', padx=5)

        delete_btn = EnhancedStyles.danger_button(buttons_frame, "🗑️ Удалить",
                                                 lambda: self.delete_selected_semi_product(), padx=15, pady=8)
        delete_btn.pack(side='left', padx=5)

        # Таблица полуфабрикатов
        table_frame = tk.Frame(main_container, bg='white', relief='solid', bd=2)
        table_frame.pack(fill='both', expand=True)

        # TreeView для полуфабрикатов
        columns = ('Название', 'Количество', 'Единица', 'Стоимость', 'Состав')
        self.semi_products_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # Настроить колонки
        column_widths = {
            'Название': 250,
            'Количество': 120,
            'Единица': 100,
            'Стоимость': 120,
            'Состав': 200
        }

        for col in columns:
            self.semi_products_tree.heading(col, text=col)
            self.semi_products_tree.column(col, width=column_widths[col], anchor='center')

        # Применить стили
        EnhancedStyles.style_treeview(self.semi_products_tree, 'table')

        # Заполнить данными
        self.populate_semi_products_table(recipe)

        # Скроллбар
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.semi_products_tree.yview)
        self.semi_products_tree.configure(yscrollcommand=scrollbar.set)

        # Упаковка
        self.semi_products_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # Привязать двойной клик для редактирования
        self.semi_products_tree.bind('<Double-1>', lambda e: self.edit_selected_semi_product())

    def populate_semi_products_table(self, recipe):
        """Заполнить таблицу полуфабрикатов"""
        # Очистить таблицу
        for item in self.semi_products_tree.get_children():
            self.semi_products_tree.delete(item)

        # Добавить полуфабрикаты
        total_cost = 0
        for i, semi_product in enumerate(recipe.get('semi_products', [])):
            cost = semi_product.get('cost', 0)
            total_cost += cost

            # Создать строку состава
            ingredients_list = semi_product.get('ingredients', [])
            composition = ', '.join([ing['name'] for ing in ingredients_list[:3]])
            if len(ingredients_list) > 3:
                composition += f" и еще {len(ingredients_list) - 3}"

            values = (
                semi_product['name'],
                semi_product.get('amount', ''),
                semi_product.get('unit', ''),
                f"{cost:.2f} руб",
                composition
            )
            self.semi_products_tree.insert('', 'end', iid=f"semi_{i}", values=values)

        # Добавить итоговую строку
        if recipe.get('semi_products'):
            self.semi_products_tree.insert('', 'end', iid='total_semi',
                                          values=('ИТОГО ПОЛУФАБРИКАТЫ', '', '', f"{total_cost:.2f} руб", ''))

    def add_semi_product_dialog(self, recipe_name):
        """Диалог добавления полуфабриката"""
        self.open_semi_product_dialog(recipe_name, "add")

    def edit_selected_semi_product(self):
        """Редактировать выбранный полуфабрикат"""
        selection = self.semi_products_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите полуфабрикат для редактирования")
            return

        item_id = selection[0]
        if item_id.startswith('total'):
            messagebox.showwarning("Предупреждение", "Нельзя редактировать итоговую строку")
            return

        # Получить индекс полуфабриката
        semi_index = int(item_id.split('_')[1])
        self.open_semi_product_dialog(self.edit_recipe_name, "edit", semi_index)

    def delete_selected_semi_product(self):
        """Удалить выбранный полуфабрикат"""
        selection = self.semi_products_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите полуфабрикат для удаления")
            return

        item_id = selection[0]
        if item_id.startswith('total'):
            messagebox.showwarning("Предупреждение", "Нельзя удалить итоговую строку")
            return

        values = self.semi_products_tree.item(item_id, 'values')
        if messagebox.askyesno("Подтверждение", f"Удалить полуфабрикат '{values[0]}'?"):
            # Получить индекс полуфабриката
            semi_index = int(item_id.split('_')[1])

            # Удалить из данных
            recipe = self.recipes[self.edit_recipe_name]
            if semi_index < len(recipe.get('semi_products', [])):
                del recipe['semi_products'][semi_index]

                # Обновить таблицу
                self.populate_semi_products_table(recipe)

                messagebox.showinfo("Успех", f"Полуфабрикат '{values[0]}' удален")

    def open_semi_product_dialog(self, recipe_name, mode, semi_index=None):
        """Открыть диалог добавления/редактирования полуфабриката"""
        # Получить данные полуфабриката для редактирования
        semi_data = None
        if mode == "edit" and semi_index is not None:
            recipe = self.recipes[recipe_name]
            semi_products = recipe.get('semi_products', [])
            if semi_index < len(semi_products):
                semi_data = semi_products[semi_index]

        # Создать диалоговое окно
        dialog = tk.Toplevel(self.edit_window)
        title = "Добавление полуфабриката" if mode == "add" else "Редактирование полуфабриката"
        dialog.title(title)
        dialog.geometry("800x700")  # Увеличили размер для лучшей видимости
        dialog.resizable(True, True)  # Разрешить изменение размера
        dialog.configure(bg='white')

        # Центрировать диалог на экране
        dialog.update_idletasks()
        width = 800
        height = 700
        x = (dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog.winfo_screenheight() // 2) - (height // 2)
        dialog.geometry(f"{width}x{height}+{x}+{y}")

        # Центрировать окно
        dialog.transient(self.edit_window)
        # НЕ ДЕЛАЕМ ДИАЛОГ ПОЛУФАБРИКАТОВ МОДАЛЬНЫМ
        # dialog.grab_set()

        # Заголовок
        header = tk.Frame(dialog, bg=EnhancedStyles.COLORS['warning'], height=60)
        header.pack(fill='x')
        header.pack_propagate(False)

        icon = "➕" if mode == "add" else "✏️"
        title_label = tk.Label(header, text=f"{icon} {title}",
                              font=('Cambria', 16, 'bold'), bg=EnhancedStyles.COLORS['warning'], fg='white')
        title_label.pack(pady=15)

        # Основная форма
        form_frame = tk.Frame(dialog, bg='white')
        form_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # Поля ввода
        fields = {}

        # Название полуфабриката с кнопкой поиска
        tk.Label(form_frame, text="Название полуфабриката:", font=('Cambria', 12, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))

        name_frame = tk.Frame(form_frame, bg='white')
        name_frame.pack(fill='x', pady=(0, 15))

        fields['name'] = tk.Entry(name_frame, font=('Cambria', 11), width=42)
        fields['name'].pack(side='left', fill='x', expand=True)

        search_btn = EnhancedStyles.secondary_button(name_frame, "🔍 Поиск",
                                                     lambda: self.open_semi_product_search_dialog(fields, dialog),
                                                     padx=10, pady=5)
        search_btn.pack(side='right', padx=(10, 0))

        # Количество
        tk.Label(form_frame, text="Количество:", font=('Cambria', 12, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))
        fields['amount'] = tk.Entry(form_frame, font=('Cambria', 11), width=20)
        fields['amount'].pack(anchor='w', pady=(0, 15))

        # Единица измерения
        tk.Label(form_frame, text="Единица измерения:", font=('Cambria', 12, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))
        fields['unit'] = ttk.Combobox(form_frame, font=('Cambria', 11), width=47,
                                     values=['г', 'кг', 'мл', 'л', 'шт', 'порция'])
        fields['unit'].pack(anchor='w', pady=(0, 15))

        # Стоимость
        tk.Label(form_frame, text="Стоимость (руб):", font=('Cambria', 12, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))
        fields['cost'] = tk.Entry(form_frame, font=('Cambria', 11), width=20)
        fields['cost'].pack(anchor='w', pady=(0, 15))

        # Описание состава
        tk.Label(form_frame, text="Описание состава:", font=('Cambria', 12, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 5))
        fields['description'] = tk.Text(form_frame, font=('Cambria', 11), width=50, height=4)
        fields['description'].pack(fill='x', pady=(0, 15))

        # Заполнить поля при редактировании
        if semi_data:
            fields['name'].insert(0, semi_data.get('name', ''))
            fields['amount'].insert(0, str(semi_data.get('amount', '')))
            fields['unit'].set(semi_data.get('unit', ''))
            fields['cost'].insert(0, str(semi_data.get('cost', 0)))
            fields['description'].insert('1.0', semi_data.get('description', ''))

        # Кнопки
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(fill='x', padx=30, pady=20)

        # Кнопка ОК (основная кнопка)
        ok_btn = EnhancedStyles.success_button(buttons_frame, "✅ ОК",
                                              lambda: self.save_semi_product(dialog, fields, recipe_name, mode, semi_index),
                                              padx=25, pady=12)
        ok_btn.pack(side='left', padx=(0, 15))

        # Кнопка Отмена
        cancel_btn = EnhancedStyles.secondary_button(buttons_frame, "❌ Отмена",
                                                    dialog.destroy, padx=20, pady=12)
        cancel_btn.pack(side='left')

        # Привязать клавиши
        def on_enter(event):
            self.save_semi_product(dialog, fields, recipe_name, mode, semi_index)

        def on_escape(event):
            dialog.destroy()

        dialog.bind('<Return>', on_enter)
        dialog.bind('<Escape>', on_escape)

        # Фокус на первое поле
        fields['name'].focus()

    def save_semi_product(self, dialog, fields, recipe_name, mode, semi_index=None):
        """Сохранить полуфабрикат"""
        try:
            # Получить данные из полей
            name = fields['name'].get().strip()
            amount = fields['amount'].get().strip()
            unit = fields['unit'].get().strip()
            cost = float(fields['cost'].get().strip() or 0)
            description = fields['description'].get('1.0', 'end-1c').strip()

            # Валидация
            if not name:
                messagebox.showerror("Ошибка", "Введите название полуфабриката")
                return

            if not amount:
                messagebox.showerror("Ошибка", "Введите количество")
                return

            if not unit:
                messagebox.showerror("Ошибка", "Выберите единицу измерения")
                return

            # Создать объект полуфабриката
            semi_product = {
                'name': name,
                'amount': amount,
                'unit': unit,
                'cost': cost,
                'description': description,
                'ingredients': []  # Пустой список ингредиентов для начала
            }

            # Получить рецепт
            recipe = self.recipes[recipe_name]
            if 'semi_products' not in recipe:
                recipe['semi_products'] = []

            # Добавить или обновить полуфабрикат
            if mode == "add":
                recipe['semi_products'].append(semi_product)
                message = f"Полуфабрикат '{name}' добавлен"
            else:
                if semi_index is not None and semi_index < len(recipe['semi_products']):
                    # Сохранить существующие ингредиенты
                    existing_ingredients = recipe['semi_products'][semi_index].get('ingredients', [])
                    semi_product['ingredients'] = existing_ingredients
                    recipe['semi_products'][semi_index] = semi_product
                    message = f"Полуфабрикат '{name}' обновлен"
                else:
                    messagebox.showerror("Ошибка", "Ошибка при обновлении полуфабриката")
                    return

            # Сохранить изменения в файл
            try:
                self.save_recipes_to_file()
                print("DEBUG: Recipes saved to file after adding semi-product")
            except Exception as save_error:
                print(f"WARNING: Could not save to file: {save_error}")

            # Обновить таблицу полуфабрикатов
            self.populate_semi_products_table(recipe)

            # Закрыть диалог
            dialog.destroy()

            # Показать сообщение об успехе
            messagebox.showinfo("Успех", f"{message} и сохранен")

            print(f"DEBUG: Semi-product saved successfully. Recipe now has {len(recipe.get('semi_products', []))} semi-products")

        except ValueError:
            messagebox.showerror("Ошибка", "Проверьте правильность введенной стоимости")

    def open_semi_product_search_dialog(self, fields, parent_dialog):
        """Открыть диалог поиска полуфабрикатов"""
        # Создать диалог поиска
        search_dialog = tk.Toplevel(parent_dialog)
        search_dialog.title("Поиск полуфабрикатов")
        search_dialog.geometry("1000x750")  # Увеличили размер
        search_dialog.resizable(True, True)
        search_dialog.configure(bg='white')

        # Центрировать диалог на экране
        search_dialog.update_idletasks()
        width = 1000
        height = 750
        x = (search_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (search_dialog.winfo_screenheight() // 2) - (height // 2)
        search_dialog.geometry(f"{width}x{height}+{x}+{y}")

        # Центрировать окно
        search_dialog.transient(parent_dialog)
        # НЕ ДЕЛАЕМ ДИАЛОГ ПОИСКА ПОЛУФАБРИКАТОВ МОДАЛЬНЫМ
        # search_dialog.grab_set()

        # Заголовок
        header = tk.Frame(search_dialog, bg=EnhancedStyles.COLORS['warning'], height=60)
        header.pack(fill='x')
        header.pack_propagate(False)

        title_label = tk.Label(header, text="🔍 Поиск и выбор полуфабриката",
                              font=('Cambria', 16, 'bold'), bg=EnhancedStyles.COLORS['warning'], fg='white')
        title_label.pack(pady=15)

        # Основная область
        main_frame = tk.Frame(search_dialog, bg='white')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Поле поиска
        search_frame = tk.Frame(main_frame, bg='white')
        search_frame.pack(fill='x', pady=(0, 15))

        tk.Label(search_frame, text="Поиск:", font=('Cambria', 12, 'bold'),
                bg='white', fg='#2c3e50').pack(side='left', padx=(0, 10))

        search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=search_var, font=('Cambria', 11), width=30)
        search_entry.pack(side='left', fill='x', expand=True)

        # Создать список всех уникальных полуфабрикатов из всех рецептов
        all_semi_products = self.get_all_unique_semi_products()

        # Список полуфабрикатов
        list_frame = tk.Frame(main_frame, bg='white', relief='solid', bd=2)
        list_frame.pack(fill='both', expand=True, pady=(0, 15))

        # TreeView для списка полуфабрикатов
        columns = ('Название', 'Единица', 'Средняя стоимость', 'Описание', 'Используется в')
        semi_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=12)

        # Настроить колонки
        column_widths = {
            'Название': 180,
            'Единица': 80,
            'Средняя стоимость': 120,
            'Описание': 200,
            'Используется в': 120
        }

        for col in columns:
            semi_tree.heading(col, text=col)
            semi_tree.column(col, width=column_widths[col], anchor='center')

        # Применить стили
        EnhancedStyles.style_treeview(semi_tree, 'table')

        # Заполнить список полуфабрикатов
        def populate_semi_products_list(filter_text=""):
            # Очистить список
            for item in semi_tree.get_children():
                semi_tree.delete(item)

            # Добавить отфильтрованные полуфабрикаты
            for semi_info in all_semi_products:
                if filter_text.lower() in semi_info['name'].lower():
                    description = semi_info['description'][:30] + "..." if len(semi_info['description']) > 30 else semi_info['description']
                    values = (
                        semi_info['name'],
                        semi_info['unit'],
                        f"{semi_info['avg_cost']:.2f} руб",
                        description,
                        f"{semi_info['recipe_count']} рецептов"
                    )
                    semi_tree.insert('', 'end', values=values)

        # Изначально показать все полуфабрикаты
        populate_semi_products_list()

        # Привязать поиск к изменению текста
        def on_search_change(*args):
            populate_semi_products_list(search_var.get())

        search_var.trace('w', on_search_change)

        # Скроллбар
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=semi_tree.yview)
        semi_tree.configure(yscrollcommand=scrollbar.set)

        # Упаковка
        semi_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # Кнопки
        buttons_frame = tk.Frame(search_dialog, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=20)

        def select_semi_product():
            selection = semi_tree.selection()
            if not selection:
                messagebox.showwarning("Предупреждение", "Выберите полуфабрикат из списка")
                return

            # Получить выбранный полуфабрикат
            item = semi_tree.item(selection[0])
            semi_name = item['values'][0]
            semi_unit = item['values'][1]

            # Найти полную информацию о полуфабрикате
            selected_semi = None
            for semi in all_semi_products:
                if semi['name'] == semi_name:
                    selected_semi = semi
                    break

            if selected_semi:
                # Заполнить поля в родительском диалоге
                fields['name'].delete(0, 'end')
                fields['name'].insert(0, selected_semi['name'])

                # Заполнить количество по умолчанию если поле пустое
                if not fields['amount'].get().strip():
                    fields['amount'].delete(0, 'end')
                    fields['amount'].insert(0, "1")  # Значение по умолчанию для полуфабрикатов

                fields['unit'].set(selected_semi['unit'])
                fields['cost'].delete(0, 'end')
                fields['cost'].insert(0, f"{selected_semi['avg_cost']:.2f}")
                fields['description'].delete('1.0', 'end')
                fields['description'].insert('1.0', selected_semi['description'])

                # Закрыть ТОЛЬКО диалог поиска
                search_dialog.destroy()

                # Установить фокус на поле количества для удобства редактирования
                fields['amount'].focus()
                fields['amount'].select_range(0, 'end')

                print(f"DEBUG: Semi-product '{semi_name}' selected and fields filled")

        select_btn = EnhancedStyles.success_button(buttons_frame, "✅ Выбрать",
                                                  select_semi_product, padx=20, pady=10)
        select_btn.pack(side='left', padx=(0, 10))

        cancel_btn = EnhancedStyles.secondary_button(buttons_frame, "❌ Отмена",
                                                    search_dialog.destroy, padx=20, pady=10)
        cancel_btn.pack(side='left')

        # Фокус на поле поиска
        search_entry.focus()

        # Привязать двойной клик для выбора
        semi_tree.bind('<Double-1>', lambda e: select_semi_product())

    def get_all_unique_semi_products(self):
        """Получить все уникальные полуфабрикаты из всех рецептов"""
        semi_products_dict = {}

        # Пройти по всем рецептам
        for recipe_name, recipe_data in self.recipes.items():
            semi_products = recipe_data.get('semi_products', [])

            for semi_product in semi_products:
                name = semi_product.get('name', '')
                unit = semi_product.get('unit', '')
                cost = semi_product.get('cost', 0)
                description = semi_product.get('description', '')

                if name:
                    if name not in semi_products_dict:
                        semi_products_dict[name] = {
                            'name': name,
                            'unit': unit,
                            'costs': [cost],
                            'description': description,
                            'recipes': [recipe_name]
                        }
                    else:
                        # Добавить стоимость и рецепт
                        semi_products_dict[name]['costs'].append(cost)
                        if recipe_name not in semi_products_dict[name]['recipes']:
                            semi_products_dict[name]['recipes'].append(recipe_name)

                        # Обновить единицу измерения если она не была установлена
                        if not semi_products_dict[name]['unit'] and unit:
                            semi_products_dict[name]['unit'] = unit

                        # Обновить описание если оно не было установлено
                        if not semi_products_dict[name]['description'] and description:
                            semi_products_dict[name]['description'] = description

        # Преобразовать в список с вычисленными средними значениями
        result = []
        for semi_info in semi_products_dict.values():
            avg_cost = sum(semi_info['costs']) / len(semi_info['costs']) if semi_info['costs'] else 0
            result.append({
                'name': semi_info['name'],
                'unit': semi_info['unit'],
                'avg_cost': avg_cost,
                'description': semi_info['description'],
                'recipe_count': len(semi_info['recipes']),
                'recipes': semi_info['recipes']
            })

        # Сортировать по названию
        result.sort(key=lambda x: x['name'])
        return result

    def create_recipe_instructions_tab(self, parent, recipe, recipe_name):
        """Создать вкладку инструкций"""
        # Основной контейнер
        main_container = tk.Frame(parent, bg='white')
        main_container.pack(fill='both', expand=True, padx=30, pady=20)

        # Заголовок секции
        header_frame = tk.Frame(main_container, bg='white')
        header_frame.pack(fill='x', pady=(0, 20))

        title_label = tk.Label(header_frame, text="📝 Инструкции по приготовлению",
                              font=('Cambria', 16, 'bold'), bg='white', fg='#2c3e50')
        title_label.pack(side='left')

        # Кнопки управления инструкциями
        buttons_frame = tk.Frame(header_frame, bg='white')
        buttons_frame.pack(side='right')

        add_btn = EnhancedStyles.success_button(buttons_frame, "➕ Добавить шаг",
                                               lambda: self.add_instruction_step(), padx=15, pady=8)
        add_btn.pack(side='left', padx=5)

        edit_btn = EnhancedStyles.secondary_button(buttons_frame, "✏️ Редактировать",
                                                  lambda: self.edit_selected_instruction(), padx=15, pady=8)
        edit_btn.pack(side='left', padx=5)

        delete_btn = EnhancedStyles.danger_button(buttons_frame, "🗑️ Удалить",
                                                 lambda: self.delete_selected_instruction(), padx=15, pady=8)
        delete_btn.pack(side='left', padx=5)

        # Область для инструкций
        instructions_frame = tk.Frame(main_container, bg='white', relief='solid', bd=2)
        instructions_frame.pack(fill='both', expand=True)

        # Создать прокручиваемую область
        canvas = tk.Canvas(instructions_frame, bg='white')
        scrollbar = ttk.Scrollbar(instructions_frame, orient="vertical", command=canvas.yview)
        self.instructions_scrollable_frame = tk.Frame(canvas, bg='white')

        self.instructions_scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.instructions_scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Заполнить инструкциями
        self.populate_instructions_list(recipe)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Сохранить ссылки для обновления
        self.instructions_canvas = canvas

    def populate_instructions_list(self, recipe):
        """Заполнить список инструкций"""
        # Очистить существующие инструкции
        for widget in self.instructions_scrollable_frame.winfo_children():
            widget.destroy()

        instructions = recipe.get('instructions', [])

        if not instructions:
            # Показать сообщение если нет инструкций
            empty_label = tk.Label(self.instructions_scrollable_frame,
                                  text="📝 Инструкции отсутствуют\nНажмите '➕ Добавить шаг' для создания",
                                  font=('Cambria', 14), bg='white', fg='#7f8c8d')
            empty_label.pack(expand=True, pady=50)
        else:
            # Показать каждую инструкцию
            for i, instruction in enumerate(instructions, 1):
                self.create_instruction_item(i, instruction)

    def create_instruction_item(self, step_number, instruction_text):
        """Создать элемент инструкции"""
        # Контейнер для шага
        step_frame = tk.Frame(self.instructions_scrollable_frame, bg='white', relief='solid', bd=1)
        step_frame.pack(fill='x', padx=20, pady=10)

        # Заголовок шага
        header_frame = tk.Frame(step_frame, bg='#ecf0f1', height=40)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        step_label = tk.Label(header_frame, text=f"Шаг {step_number}",
                             font=('Cambria', 12, 'bold'), bg='#ecf0f1', fg='#2c3e50')
        step_label.pack(side='left', padx=15, pady=10)

        # Кнопки управления шагом
        step_buttons = tk.Frame(header_frame, bg='#ecf0f1')
        step_buttons.pack(side='right', padx=10, pady=5)

        edit_step_btn = tk.Button(step_buttons, text="✏️", font=('Cambria', 10),
                                 command=lambda: self.edit_instruction_step(step_number-1),
                                 bg='#3498db', fg='white', relief='flat', padx=8, pady=2)
        edit_step_btn.pack(side='left', padx=2)

        delete_step_btn = tk.Button(step_buttons, text="🗑️", font=('Cambria', 10),
                                   command=lambda: self.delete_instruction_step(step_number-1),
                                   bg='#e74c3c', fg='white', relief='flat', padx=8, pady=2)
        delete_step_btn.pack(side='left', padx=2)

        # Текст инструкции
        text_frame = tk.Frame(step_frame, bg='white')
        text_frame.pack(fill='x', padx=15, pady=15)

        instruction_label = tk.Label(text_frame, text=instruction_text,
                                    font=('Cambria', 11), bg='white', fg='#2c3e50',
                                    wraplength=700, justify='left')
        instruction_label.pack(anchor='w')

    def add_instruction_step(self):
        """Добавить новый шаг инструкции"""
        self.open_instruction_dialog("add")

    def edit_selected_instruction(self):
        """Редактировать выбранную инструкцию"""
        messagebox.showinfo("Редактирование", "Выберите шаг для редактирования, нажав кнопку ✏️ рядом с ним")

    def edit_instruction_step(self, step_index):
        """Редактировать конкретный шаг"""
        self.open_instruction_dialog("edit", step_index)

    def delete_selected_instruction(self):
        """Удалить выбранную инструкцию"""
        messagebox.showinfo("Удаление", "Выберите шаг для удаления, нажав кнопку 🗑️ рядом с ним")

    def delete_instruction_step(self, step_index):
        """Удалить конкретный шаг"""
        if messagebox.askyesno("Подтверждение", f"Удалить шаг {step_index + 1}?"):
            # Удалить из данных
            recipe = self.recipes[self.edit_recipe_name]
            instructions = recipe.get('instructions', [])
            if step_index < len(instructions):
                del instructions[step_index]

                # Обновить список инструкций
                self.populate_instructions_list(recipe)

                messagebox.showinfo("Успех", f"Шаг {step_index + 1} удален")

    def open_instruction_dialog(self, mode, step_index=None):
        """Открыть диалог добавления/редактирования инструкции"""
        # Получить данные инструкции для редактирования
        instruction_text = ""
        if mode == "edit" and step_index is not None:
            recipe = self.recipes[self.edit_recipe_name]
            instructions = recipe.get('instructions', [])
            if step_index < len(instructions):
                instruction_text = instructions[step_index]

        # Создать диалоговое окно
        dialog = tk.Toplevel(self.edit_window)
        title = "Добавление шага" if mode == "add" else f"Редактирование шага {step_index + 1}"
        dialog.title(title)
        dialog.geometry("800x500")  # Увеличили размер для лучшей видимости
        dialog.resizable(True, True)
        dialog.configure(bg='white')

        # Центрировать диалог на экране
        dialog.update_idletasks()
        width = 800
        height = 500
        x = (dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog.winfo_screenheight() // 2) - (height // 2)
        dialog.geometry(f"{width}x{height}+{x}+{y}")

        # Центрировать окно
        dialog.transient(self.edit_window)
        # НЕ ДЕЛАЕМ ДИАЛОГ ИНСТРУКЦИЙ МОДАЛЬНЫМ
        # dialog.grab_set()

        # Заголовок
        header = tk.Frame(dialog, bg=EnhancedStyles.COLORS['info'], height=60)
        header.pack(fill='x')
        header.pack_propagate(False)

        icon = "➕" if mode == "add" else "✏️"
        title_label = tk.Label(header, text=f"{icon} {title}",
                              font=('Cambria', 16, 'bold'), bg=EnhancedStyles.COLORS['info'], fg='white')
        title_label.pack(pady=15)

        # Основная форма
        form_frame = tk.Frame(dialog, bg='white')
        form_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # Поле ввода инструкции
        tk.Label(form_frame, text="Текст инструкции:", font=('Cambria', 12, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w', pady=(0, 10))

        text_widget = tk.Text(form_frame, font=('Cambria', 11), width=60, height=12,
                             wrap='word', relief='solid', bd=1)
        text_widget.pack(fill='both', expand=True, pady=(0, 20))

        # Заполнить текст при редактировании
        if instruction_text:
            text_widget.insert('1.0', instruction_text)

        # Кнопки
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(fill='x', padx=30, pady=20)

        # Кнопка ОК (основная кнопка)
        ok_btn = EnhancedStyles.success_button(buttons_frame, "✅ ОК",
                                              lambda: self.save_instruction(dialog, text_widget, mode, step_index),
                                              padx=25, pady=12)
        ok_btn.pack(side='left', padx=(0, 15))

        # Кнопка Отмена
        cancel_btn = EnhancedStyles.secondary_button(buttons_frame, "❌ Отмена",
                                                    dialog.destroy, padx=20, pady=12)
        cancel_btn.pack(side='left')

        # Привязать клавиши
        def on_ctrl_enter(event):
            self.save_instruction(dialog, text_widget, mode, step_index)

        def on_escape(event):
            dialog.destroy()

        dialog.bind('<Control-Return>', on_ctrl_enter)  # Ctrl+Enter для сохранения (так как Enter нужен для новой строки)
        dialog.bind('<Escape>', on_escape)

        # Фокус на текстовое поле
        text_widget.focus()

    def save_instruction(self, dialog, text_widget, mode, step_index=None):
        """Сохранить инструкцию"""
        # Получить текст инструкции
        instruction_text = text_widget.get('1.0', 'end-1c').strip()

        # Валидация
        if not instruction_text:
            messagebox.showerror("Ошибка", "Введите текст инструкции")
            return

        # Получить рецепт
        recipe = self.recipes[self.edit_recipe_name]
        if 'instructions' not in recipe:
            recipe['instructions'] = []

        # Добавить или обновить инструкцию
        if mode == "add":
            recipe['instructions'].append(instruction_text)
            message = "Новый шаг добавлен"
        else:
            if step_index is not None and step_index < len(recipe['instructions']):
                recipe['instructions'][step_index] = instruction_text
                message = f"Шаг {step_index + 1} обновлен"
            else:
                messagebox.showerror("Ошибка", "Ошибка при обновлении инструкции")
                return

        # Сохранить изменения в файл
        try:
            self.save_recipes_to_file()
            print("DEBUG: Recipes saved to file after adding instruction")
        except Exception as save_error:
            print(f"WARNING: Could not save to file: {save_error}")

        # Обновить список инструкций
        self.populate_instructions_list(recipe)

        # Закрыть диалог
        dialog.destroy()

        messagebox.showinfo("Успех", f"{message} и сохранен")

    def save_recipe_changes(self, edit_window, recipe_name):
        """Сохранить изменения в технологической карте"""
        try:
            # Получить новые данные из полей
            new_name = self.edit_name_var.get().strip()
            new_category = self.edit_category_var.get().strip()
            new_portions = int(self.edit_portions_var.get())
            new_prep_time = self.edit_prep_time_var.get().strip()
            new_cook_time = self.edit_cook_time_var.get().strip()
            new_difficulty = self.edit_difficulty_var.get().strip()
            new_cost = float(self.edit_cost_var.get())
            new_price = float(self.edit_price_var.get())

            # Валидация
            if not new_name:
                messagebox.showerror("Ошибка", "Название не может быть пустым")
                return

            if new_portions <= 0:
                messagebox.showerror("Ошибка", "Количество порций должно быть больше 0")
                return

            if new_cost < 0 or new_price < 0:
                messagebox.showerror("Ошибка", "Стоимость и цена не могут быть отрицательными")
                return

            # Получить старые данные
            old_recipe = self.recipes[recipe_name].copy()

            # Обновить данные
            updated_recipe = {
                'category': new_category,
                'portions': new_portions,
                'prep_time': new_prep_time,
                'cook_time': new_cook_time,
                'difficulty': new_difficulty,
                'cost': new_cost,
                'price': new_price,
                'ingredients': old_recipe.get('ingredients', []),
                'semi_products': old_recipe.get('semi_products', []),
                'instructions': old_recipe.get('instructions', [])
            }

            # Если название изменилось, переименовать рецепт
            if new_name != recipe_name:
                del self.recipes[recipe_name]
                self.recipes[new_name] = updated_recipe
                self.current_recipe = new_name
            else:
                self.recipes[recipe_name] = updated_recipe

            # ВАЖНО: Сохранить изменения в файл
            try:
                self.save_recipes_to_file()
                print(f"DEBUG: Recipe '{self.current_recipe}' saved to file successfully")
            except Exception as save_error:
                print(f"ERROR: Could not save to file: {save_error}")
                messagebox.showerror("Ошибка сохранения", f"Не удалось сохранить в файл: {str(save_error)}")
                return

            # Обновить интерфейс
            self.populate_recipes_tree()
            self.show_recipe_details(self.current_recipe)

            # Закрыть окно редактирования
            edit_window.destroy()

            messagebox.showinfo("Успех", f"Технологическая карта '{self.current_recipe}' успешно обновлена и сохранена")

        except ValueError as e:
            messagebox.showerror("Ошибка", "Проверьте правильность введенных числовых значений")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Произошла ошибка при сохранении: {str(e)}")

    def delete_recipe(self):
        """Удалить технологическую карту"""
        if not self.current_recipe:
            messagebox.showwarning("Предупреждение", "Выберите технологическую карту для удаления")
            return

        # Показать подробную информацию о том, что будет удалено
        recipe = self.recipes.get(self.current_recipe, {})
        ingredients_count = len(recipe.get('ingredients', []))
        semi_products_count = len(recipe.get('semi_products', []))

        delete_message = f"""Вы действительно хотите удалить технологическую карту?

📋 Название: {self.current_recipe}
🏷️ Категория: {recipe.get('category', 'Не указано')}
👥 Порций: {recipe.get('portions', 0)}
🥘 Ингредиентов: {ingredients_count}
🧪 Полуфабрикатов: {semi_products_count}
💰 Себестоимость: {recipe.get('cost', 0):.2f}₽

⚠️ Это действие нельзя отменить!"""

        if messagebox.askyesno("Подтверждение удаления", delete_message):
            deleted_recipe_name = self.current_recipe

            # Удалить из данных
            if self.current_recipe in self.recipes:
                del self.recipes[self.current_recipe]

            # Обновить TreeView
            self.populate_recipes_tree()

            # Обновить отображение деталей
            if self.recipes:
                # Выбрать первую доступную технологическую карту
                first_recipe = list(self.recipes.keys())[0]
                self.current_recipe = first_recipe
                self.show_recipe_details(first_recipe)

                # Выбрать первый элемент в TreeView
                first_item = self.recipes_tree.get_children()[0]
                self.recipes_tree.selection_set(first_item)
                self.recipes_tree.focus(first_item)
            else:
                # Если больше нет рецептов
                self.current_recipe = None
                self.clear_recipe_details()

            messagebox.showinfo("Успех", f"Технологическая карта '{deleted_recipe_name}' успешно удалена")

    def clear_recipe_details(self):
        """Очистить панель деталей технологической карты"""
        for widget in self.recipe_details_frame.winfo_children():
            widget.destroy()

        # Показать сообщение о том, что технологические карты отсутствуют
        empty_label = tk.Label(self.recipe_details_frame,
                              text="📋 Выберите технологическую карту для просмотра деталей",
                              font=('Cambria', 16, 'italic'),
                              bg='white', fg='#7f8c8d')
        empty_label.pack(expand=True)

    def add_ingredient(self, recipe_name):
        """Добавить ингредиент"""
        messagebox.showinfo("Добавление", "Функция добавления ингредиента")

    def edit_ingredient(self, recipe_name):
        """Редактировать ингредиент"""
        messagebox.showinfo("Редактирование", "Функция редактирования ингредиента")

    def delete_ingredient(self, recipe_name):
        """Удалить ингредиент"""
        messagebox.showinfo("Удаление", "Функция удаления ингредиента")

    def add_instruction(self, recipe_name):
        """Добавить инструкцию"""
        messagebox.showinfo("Добавление", "Функция добавления инструкции")

    def edit_instruction(self, recipe_name):
        """Редактировать инструкцию"""
        messagebox.showinfo("Редактирование", "Функция редактирования инструкции")

    def delete_instruction(self, recipe_name):
        """Удалить инструкцию"""
        messagebox.showinfo("Удаление", "Функция удаления инструкции")

    def export_recipes(self):
        """Экспорт технологических карт"""
        messagebox.showinfo("Экспорт", "Технологические карты экспортированы")


def create_recipe_manager(parent, db_manager):
    """Создать рабочий менеджер рецептов"""
    manager = RecipeManagerWorking(parent, db_manager)
    return manager.create_window()
