#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Тест интерфейса для проверки прокрутки и видимости кнопок
"""

import tkinter as tk
from tkinter import ttk
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gui.styles import ModernStyles

def test_scrolling_interface():
    """Создать тестовый интерфейс для проверки прокрутки"""
    
    root = tk.Tk()
    root.title("Тест Прокрутки Главного Меню")
    root.geometry("1200x800")
    root.configure(bg=ModernStyles.COLORS['bg_main'])
    
    # Создать основной контейнер
    main_frame = tk.Frame(root, bg=ModernStyles.COLORS['bg_main'])
    main_frame.pack(fill='both', expand=True)
    
    # Создать боковую панель с прокруткой (как в главном окне)
    sidebar_container = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_sidebar'], width=280)
    sidebar_container.pack(side='left', fill='y')
    sidebar_container.pack_propagate(False)
    
    # Заголовок
    title_frame = tk.Frame(sidebar_container, bg=ModernStyles.COLORS['bg_sidebar'])
    title_frame.pack(fill='x', pady=15)
    
    title_label = tk.Label(title_frame, text="🏪 Управление\nТестовый Ресторан",
                          font=('Cambria', 16, 'bold italic'),
                          fg='white',
                          bg=ModernStyles.COLORS['bg_sidebar'],
                          justify='center')
    title_label.pack()
    
    # Область навигации с прокруткой
    nav_frame = tk.Frame(sidebar_container, bg=ModernStyles.COLORS['bg_sidebar'])
    nav_frame.pack(fill='both', expand=True, padx=5, pady=5)
    
    # Создать canvas и scrollbar
    canvas = tk.Canvas(nav_frame, bg=ModernStyles.COLORS['bg_sidebar'], highlightthickness=0)
    scrollbar = ttk.Scrollbar(nav_frame, orient="vertical", command=canvas.yview)
    scrollable_frame = tk.Frame(canvas, bg=ModernStyles.COLORS['bg_sidebar'])
    
    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )
    
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)
    
    # Прокрутка колесом мыши
    def _on_mousewheel(event):
        canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    canvas.bind_all("<MouseWheel>", _on_mousewheel)
    
    # Создать тестовые категории кнопок
    def create_test_category(parent, title, buttons):
        """Создать категорию кнопок для тестирования"""
        # Заголовок категории
        category_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_sidebar'])
        category_frame.pack(fill='x', pady=(10, 5))
        
        header_label = tk.Label(category_frame, text=title,
                               font=('Cambria', 12, 'bold italic'),
                               fg=ModernStyles.COLORS['text_white'],
                               bg=ModernStyles.COLORS['bg_sidebar'])
        header_label.pack(anchor='w')
        
        # Кнопки категории
        for btn_text, btn_command in buttons:
            btn = tk.Button(parent, text=btn_text,
                           command=btn_command,
                           bg=ModernStyles.COLORS['primary'],
                           fg=ModernStyles.COLORS['text_white'],
                           font=('Cambria', 10, 'bold italic'),
                           relief='flat',
                           bd=0,
                           padx=10,
                           pady=8,
                           cursor='hand2',
                           anchor='w')
            btn.pack(fill='x', padx=5, pady=2)
            
            # Эффект наведения
            def on_enter(e, button=btn):
                button.config(bg=ModernStyles.COLORS['primary_light'])
            def on_leave(e, button=btn):
                button.config(bg=ModernStyles.COLORS['primary'])
            
            btn.bind("<Enter>", on_enter)
            btn.bind("<Leave>", on_leave)
    
    def test_button_click(name):
        """Тестовая функция для кнопок"""
        print(f"✅ Кнопка '{name}' нажата!")
        status_label.config(text=f"Нажата кнопка: {name}")
    
    # Создать все категории (как в реальном приложении)
    create_test_category(scrollable_frame, "📊 ОСНОВНЫЕ МОДУЛИ", [
        ("📈 Панель Управления", lambda: test_button_click("Панель Управления")),
        ("📊 Панель в Реальном Времени", lambda: test_button_click("Панель в Реальном Времени")),
        ("📈 Расширенная Отчетность", lambda: test_button_click("Расширенная Отчетность")),
        ("📱 Мобильная и Веб Интеграция", lambda: test_button_click("Мобильная и Веб Интеграция")),
        ("🔌 Интеграция и API", lambda: test_button_click("Интеграция и API")),
        ("🔒 Расширенная Безопасность", lambda: test_button_click("Расширенная Безопасность")),
        ("💾 Автоматическое Резервное Копирование", lambda: test_button_click("Автоматическое Резервное Копирование")),
        ("🏢 Управление Локациями", lambda: test_button_click("Управление Локациями")),
        ("🔮 Расширенная Аналитика", lambda: test_button_click("Расширенная Аналитика")),
        ("🔐 Управление Безопасностью", lambda: test_button_click("Управление Безопасностью")),
        ("📦 Расширенное Управление Складом", lambda: test_button_click("Расширенное Управление Складом")),
        ("🍳 Кухонный Дисплей", lambda: test_button_click("Кухонный Дисплей")),
        ("📊 Данные о Продажах", lambda: test_button_click("Данные о Продажах")),
        ("📥 Импорт Продаж", lambda: test_button_click("Импорт Продаж")),
    ])
    
    create_test_category(scrollable_frame, "📦 СКЛАД И ЗАКУПКИ", [
        ("📦 Управление Складом", lambda: test_button_click("Управление Складом")),
        ("🛒 Управление Закупками", lambda: test_button_click("Управление Закупками")),
        ("🏢 Поставщики", lambda: test_button_click("Поставщики")),
    ])
    
    create_test_category(scrollable_frame, "🍽️ КУХНЯ И МЕНЮ", [
        ("📋 Технологические Карты", lambda: test_button_click("Технологические Карты")),
        ("📋 Планирование Меню", lambda: test_button_click("Планирование Меню")),
        ("📊 Контроль Затрат", lambda: test_button_click("Контроль Затрат")),
        ("🔍 Контроль Качества", lambda: test_button_click("Контроль Качества")),
    ])
    
    create_test_category(scrollable_frame, "👥 КЛИЕНТЫ И ПЕРСОНАЛ", [
        ("👥 CRM Клиенты", lambda: test_button_click("CRM Клиенты")),
        ("🍽️ Управление Столами", lambda: test_button_click("Управление Столами")),
        ("🗓️ Планирование Смен", lambda: test_button_click("Планирование Смен")),
        ("💵 Расчёт Зарплаты", lambda: test_button_click("Расчёт Зарплаты")),
    ])
    
    create_test_category(scrollable_frame, "💰 ФИНАНСЫ И УЧЁТ", [
        ("📈 Финансовая Панель", lambda: test_button_click("Финансовая Панель")),
        ("📊 Бухгалтерия", lambda: test_button_click("Бухгалтерия")),
    ])
    
    create_test_category(scrollable_frame, "📈 АНАЛИТИКА И ОТЧЁТЫ", [
        ("📊 Профессиональная Система Отчетности", lambda: test_button_click("Профессиональная Система Отчетности")),
        ("📈 Система Отчётов", lambda: test_button_click("Система Отчётов")),
        ("📊 Расширенная Аналитика", lambda: test_button_click("Расширенная Аналитика")),
        ("🤖 ИИ Аналитика и Рекомендации", lambda: test_button_click("ИИ Аналитика и Рекомендации")),
    ])
    
    create_test_category(scrollable_frame, "🔧 ИНСТРУМЕНТЫ", [
        ("⚡ Оптимизация Производительности", lambda: test_button_click("Оптимизация Производительности")),
        ("🔄 Монитор Синхронизации", lambda: test_button_click("Монитор Синхронизации")),
        ("🎨 Конфигуратор Стилей", lambda: test_button_click("Конфигуратор Стилей")),
        ("🌐 Языковые Настройки", lambda: test_button_click("Языковые Настройки")),
        ("💳 Коды Способов Оплаты", lambda: test_button_click("Коды Способов Оплаты")),
        ("🔒 Безопасность и Аудит", lambda: test_button_click("Безопасность и Аудит")),
        ("📱 Мобильная Интеграция", lambda: test_button_click("Мобильная Интеграция")),
        ("👥 CRM Система", lambda: test_button_click("CRM Система")),
        ("🎁 Система Лояльности", lambda: test_button_click("Система Лояльности")),
        ("📊 Бизнес-Аналитика", lambda: test_button_click("Бизнес-Аналитика")),
        ("🍽️ Бронирование Столов", lambda: test_button_click("Бронирование Столов")),
        ("🚚 Управление Поставками", lambda: test_button_click("Управление Поставками")),
        ("💰 Финансовое Планирование", lambda: test_button_click("Финансовое Планирование")),
        ("⚡ Мониторинг Производительности", lambda: test_button_click("Мониторинг Производительности")),
        ("📊 Экспорт/Импорт Данных", lambda: test_button_click("Экспорт/Импорт Данных")),
        ("🔔 Центр Уведомлений", lambda: test_button_click("Центр Уведомлений")),
        ("💾 Менеджер Копий", lambda: test_button_click("Менеджер Копий")),
        ("⚙️ Настройки Системы", lambda: test_button_click("Настройки Системы")),
    ])
    
    # Упаковать canvas и scrollbar
    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")
    
    # Область пользователя внизу
    user_frame = tk.Frame(sidebar_container, bg=ModernStyles.COLORS['bg_sidebar'])
    user_frame.pack(side='bottom', fill='x', padx=15, pady=15)
    
    separator = tk.Frame(user_frame, bg=ModernStyles.COLORS['gray'], height=1)
    separator.pack(fill='x', pady=(0, 10))
    
    user_label = tk.Label(user_frame, text="👤 Тестовый Пользователь",
                         font=('Arial', 9),
                         fg=ModernStyles.COLORS['text_white'],
                         bg=ModernStyles.COLORS['bg_sidebar'])
    user_label.pack(anchor='w')
    
    # Основная область контента
    content_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_main'])
    content_frame.pack(side='right', fill='both', expand=True)
    
    # Заголовок контента
    content_title = tk.Label(content_frame, text="🧪 ТЕСТ ПРОКРУТКИ ИНТЕРФЕЙСА",
                            font=('Cambria', 24, 'bold italic'),
                            fg=ModernStyles.COLORS['text_primary'],
                            bg=ModernStyles.COLORS['bg_main'])
    content_title.pack(pady=20)
    
    # Инструкции
    instructions = tk.Label(content_frame, 
                           text="Инструкции:\n\n" +
                                "1. Используйте колесо мыши для прокрутки боковой панели\n" +
                                "2. Нажимайте на кнопки для тестирования функциональности\n" +
                                "3. Проверьте, что все кнопки видны и доступны\n" +
                                "4. Убедитесь, что прокрутка работает плавно",
                           font=('Cambria', 14, 'bold italic'),
                           fg=ModernStyles.COLORS['text_secondary'],
                           bg=ModernStyles.COLORS['bg_main'],
                           justify='left')
    instructions.pack(pady=20, padx=20)
    
    # Статус
    status_label = tk.Label(content_frame, text="Готов к тестированию...",
                           font=('Cambria', 16, 'bold italic'),
                           fg=ModernStyles.COLORS['success'],
                           bg=ModernStyles.COLORS['bg_main'])
    status_label.pack(pady=20)
    
    # Кнопка закрытия
    close_btn = tk.Button(content_frame, text="Закрыть Тест",
                         command=root.destroy,
                         bg=ModernStyles.COLORS['danger'],
                         fg=ModernStyles.COLORS['text_white'],
                         font=('Cambria', 14, 'bold italic'),
                         relief='flat',
                         bd=0,
                         padx=20,
                         pady=10,
                         cursor='hand2')
    close_btn.pack(pady=20)
    
    print("🧪 Тест интерфейса запущен!")
    print("📊 Проверьте прокрутку и функциональность кнопок")
    
    root.mainloop()

if __name__ == "__main__":
    test_scrolling_interface()
