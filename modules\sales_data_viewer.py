"""
Модуль для просмотра данных о продажах с фильтрацией и аналитикой
"""

import tkinter as tk
from tkinter import ttk, messagebox
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>
from tkcalendar import DateEntry
from gui.styles import ModernStyles
from localization.russian import *

class SalesDataViewer:
    """Окно для просмотра и анализа данных о продажах"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.sales_data = []
        self.filtered_data = []
        self.start_date_var = None
        self.end_date_var = None
        self.create_window()
        self.load_sales_data()

    def create_window(self):
        """Создать окно просмотра данных о продажах"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("📊 Данные о Продажах")
        self.window.geometry("1500x900")
        self.window.configure(bg=ModernStyles.COLORS['bg_main'])
        
        # Make window resizable
        self.window.resizable(True, True)
        self.window.minsize(1200, 700)
        
        # Center the window
        self.window.update_idletasks()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - 1500) // 2
        y = (screen_height - 900) // 2
        self.window.geometry(f"1500x900+{x}+{y}")
        
        # Main container
        main_frame = tk.Frame(self.window, **ModernStyles.WIDGET_STYLES['frame_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Title and status
        title_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_main'])
        title_frame.pack(fill='x', pady=(0, 20))

        title_label = tk.Label(title_frame, text="📊 Данные о Продажах - Аналитика и Отчеты",
                              **ModernStyles.WIDGET_STYLES['label_title'])
        title_label.pack(side='left')

        # Status label
        self.status_label = tk.Label(title_frame, text="Загрузка данных...",
                                    font=('Cambria', 10, 'italic'),
                                    fg='#6b7280',
                                    bg=ModernStyles.COLORS['bg_main'])
        self.status_label.pack(side='right')
        
        # Фильтрация по периодам
        self.create_period_filter(main_frame)
        
        # Таблица данных
        self.create_data_table(main_frame)
        
        # Панель итогов
        self.create_summary_panel(main_frame)
        
        # Кнопки действий
        self.create_action_buttons(main_frame)

    def create_period_filter(self, parent):
        """Создать панель фильтрации по периодам"""
        period_frame = ModernStyles.create_card_frame(parent)
        period_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(period_frame, text="📅 Фильтрация по Периоду",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w')
        
        controls_frame = tk.Frame(period_frame, bg=ModernStyles.COLORS['bg_card'])
        controls_frame.pack(fill='x', pady=(10, 0))
        
        # Начальная дата с календарем
        tk.Label(controls_frame, text="Начало периода:",
                font=('Cambria', 11, 'bold'),
                bg=ModernStyles.COLORS['bg_card']).pack(side='left', padx=(0, 5))
        
        # Календарь для начальной даты
        start_date = datetime.now() - timedelta(days=30)  # 30 дней назад
        self.start_date_picker = DateEntry(controls_frame, 
                                          width=12, 
                                          background='darkblue',
                                          foreground='white', 
                                          borderwidth=2,
                                          date_pattern='dd.mm.yyyy',
                                          font=('Cambria', 10),
                                          year=start_date.year,
                                          month=start_date.month,
                                          day=start_date.day)
        self.start_date_picker.pack(side='left', padx=(0, 20))
        
        # Конечная дата с календарем
        tk.Label(controls_frame, text="Конец периода:",
                font=('Cambria', 11, 'bold'),
                bg=ModernStyles.COLORS['bg_card']).pack(side='left', padx=(0, 5))
        
        # Календарь для конечной даты
        self.end_date_picker = DateEntry(controls_frame, 
                                        width=12, 
                                        background='darkblue',
                                        foreground='white', 
                                        borderwidth=2,
                                        date_pattern='dd.mm.yyyy',
                                        font=('Cambria', 10))
        self.end_date_picker.pack(side='left', padx=(0, 20))
        
        # Кнопки
        filter_btn = tk.Button(controls_frame, text="🔍 Применить Фильтр",
                              command=self.apply_period_filter,
                              bg='#059669', fg='white',
                              font=('Cambria', 11, 'bold'),
                              relief='flat', padx=20, pady=8,
                              cursor='hand2')
        filter_btn.pack(side='left', padx=(0, 10))
        
        reset_btn = tk.Button(controls_frame, text="🔄 Сбросить",
                             command=self.reset_filter,
                             bg='#dc2626', fg='white',
                             font=('Cambria', 11, 'bold'),
                             relief='flat', padx=20, pady=8,
                             cursor='hand2')
        reset_btn.pack(side='left', padx=(0, 10))
        
        refresh_btn = tk.Button(controls_frame, text="🔄 Обновить Данные",
                               command=self.load_sales_data,
                               bg='#2563eb', fg='white',
                               font=('Cambria', 11, 'bold'),
                               relief='flat', padx=20, pady=8,
                               cursor='hand2')
        refresh_btn.pack(side='left')

    def create_data_table(self, parent):
        """Создать таблицу данных"""
        table_frame = ModernStyles.create_card_frame(parent)
        table_frame.pack(fill='both', expand=True, pady=(0, 20))
        
        tk.Label(table_frame, text="📋 Данные о Продажах",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w')
        
        # Treeview container
        tree_container = tk.Frame(table_frame, bg=ModernStyles.COLORS['bg_card'])
        tree_container.pack(fill='both', expand=True, pady=(10, 0))
        
        # Create Treeview with dark blue headers
        self.tree = ttk.Treeview(tree_container, style="Modern.Treeview")
        
        # Configure style for dark blue headers
        style = ttk.Style()
        style.configure("Modern.Treeview.Heading",
                       background='#1e3a8a',
                       foreground='#ffffff',
                       font=('Cambria', 12, 'bold italic'),
                       relief='solid',
                       borderwidth=2)
        
        # Define columns
        columns = (ORDER_DATE, ORDER_NUMBER, PAYMENT_METHOD, DEPARTMENT,
                  DISH_CODE, DISH_NAME, QUANTITY, PRICE_PER_DISH, TOTAL_AMOUNT)
        self.tree['columns'] = columns
        self.tree['show'] = 'headings'
        
        # Configure columns
        column_widths = {
            ORDER_DATE: 120,
            ORDER_NUMBER: 120,
            PAYMENT_METHOD: 150,
            DEPARTMENT: 100,
            DISH_CODE: 120,
            DISH_NAME: 300,
            QUANTITY: 100,
            PRICE_PER_DISH: 120,
            TOTAL_AMOUNT: 120
        }
        
        for col in columns:
            self.tree.heading(col, text=col)
            width = column_widths.get(col, 120)
            self.tree.column(col, width=width, minwidth=80, stretch=True)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(tree_container, orient='vertical', command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_container, orient='horizontal', command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack
        self.tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')

    def create_summary_panel(self, parent):
        """Создать панель итогов"""
        summary_frame = ModernStyles.create_card_frame(parent)
        summary_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(summary_frame, text="📊 Итоги и Аналитика",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w')
        
        # Контейнер для итогов
        self.summary_container = tk.Frame(summary_frame, bg=ModernStyles.COLORS['bg_card'])
        self.summary_container.pack(fill='x', pady=(10, 0))

    def create_action_buttons(self, parent):
        """Создать кнопки действий"""
        action_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        action_frame.pack(fill='x')
        
        # Export button
        export_btn = tk.Button(action_frame, text="📤 Экспорт в Excel",
                              command=self.export_to_excel,
                              **ModernStyles.WIDGET_STYLES['button_success'])
        export_btn.pack(side='left', padx=(0, 10))
        
        # Print button
        print_btn = tk.Button(action_frame, text="🖨️ Печать Отчета",
                             command=self.print_report,
                             **ModernStyles.WIDGET_STYLES['button_primary'])
        print_btn.pack(side='left', padx=(0, 10))
        
        # Close button
        close_btn = tk.Button(action_frame, text="❌ Закрыть",
                             command=self.window.destroy,
                             **ModernStyles.WIDGET_STYLES['button_secondary'])
        close_btn.pack(side='right')

    def load_sales_data(self):
        """Загрузить данные о продажах из базы данных"""
        try:
            # Загрузить данные из базы данных
            if hasattr(self.db_manager, 'get_sales_data'):
                self.sales_data = self.db_manager.get_sales_data()
            else:
                # Если метод не существует, создаем демонстрационные данные
                self.sales_data = self.create_demo_data()

            self.filtered_data = self.sales_data.copy()

            # Обновить отображение
            self.display_data()
            self.update_summary()

            # Обновить статус в интерфейсе
            if self.sales_data:
                self.status_label.config(text=f"✅ Загружено {len(self.sales_data)} записей")
                print(f"✅ Загружено {len(self.sales_data)} записей о продажах")
            else:
                self.status_label.config(text="⚠️ Используются демо-данные")
                print("⚠️ Нет данных о продажах в базе данных, используются демо-данные")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка загрузки данных: {str(e)}")
            print(f"❌ Ошибка загрузки данных: {e}")
            # В случае ошибки используем пустые данные
            self.sales_data = []
            self.filtered_data = []
            self.display_data()
            self.update_summary()

    def apply_period_filter(self):
        """Применить фильтр по периоду"""
        try:
            # Проверить, есть ли данные для фильтрации
            if not self.sales_data:
                messagebox.showwarning("Предупреждение",
                    "Нет данных для фильтрации.\n"
                    "Сначала загрузите данные о продажах через модуль импорта.")
                return

            start_date = self.start_date_picker.get_date()
            end_date = self.end_date_picker.get_date()

            if start_date > end_date:
                messagebox.showerror("Ошибка", "Начальная дата не может быть больше конечной даты.")
                return

            # Фильтрация данных
            self.filtered_data = []
            for record in self.sales_data:
                try:
                    record_date = datetime.strptime(record['order_date'], '%d.%m.%Y').date()
                    if start_date <= record_date <= end_date:
                        self.filtered_data.append(record)
                except ValueError:
                    continue

            # Обновить отображение
            self.display_data()
            self.update_summary()

            # Обновить статус
            self.status_label.config(text=f"🔍 Показано {len(self.filtered_data)} из {len(self.sales_data)} записей")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка применения фильтра: {str(e)}")

    def reset_filter(self):
        """Сбросить фильтр"""
        self.filtered_data = self.sales_data.copy()
        self.display_data()
        self.update_summary()
        # Обновить статус
        self.status_label.config(text=f"✅ Показаны все {len(self.sales_data)} записей")

    def display_data(self):
        """Отобразить данные в таблице"""
        # Очистить таблицу
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Отобразить отфильтрованные данные
        for record in self.filtered_data:
            values = (
                record['order_date'],
                record['order_number'],
                record['payment_method'],
                record['department'],
                record['dish_code'],
                record['dish_name'],
                f"{record['quantity']:.1f}",  # Количество с одним знаком
                format_currency(record['price_per_dish']),  # Цена в российском формате
                format_currency(record['total_amount'])     # Сумма в российском формате
            )
            self.tree.insert('', 'end', values=values)

    def update_summary(self):
        """Обновить панель итогов"""
        # Очистить предыдущие итоги
        for widget in self.summary_container.winfo_children():
            widget.destroy()

        if not self.filtered_data:
            tk.Label(self.summary_container, text="Нет данных для анализа",
                    font=('Cambria', 12, 'italic'),
                    bg=ModernStyles.COLORS['bg_card']).pack(pady=10)
            return

        # Создать три колонки для итогов
        columns_frame = tk.Frame(self.summary_container, bg=ModernStyles.COLORS['bg_card'])
        columns_frame.pack(fill='x', padx=10, pady=10)

        # Колонка 1: Итоги по способам оплаты
        payment_frame = tk.Frame(columns_frame, bg='#f0f9ff', relief='solid', bd=2)
        payment_frame.pack(side='left', fill='both', expand=True, padx=(0, 5))

        tk.Label(payment_frame, text="💳 Итоги по Способам Оплаты",
                font=('Cambria', 12, 'bold'),
                bg='#1e3a8a', fg='white').pack(fill='x', pady=(0, 5))

        payment_totals = {}
        for record in self.filtered_data:
            payment_method = record['payment_method']
            amount = float(record['total_amount'])
            payment_totals[payment_method] = payment_totals.get(payment_method, 0) + amount

        for payment_method, total in sorted(payment_totals.items()):
            tk.Label(payment_frame,
                    text=f"{payment_method}: {format_currency(total)}",
                    font=('Cambria', 11),
                    bg='#f0f9ff', anchor='w').pack(fill='x', padx=8, pady=2)

        # Колонка 2: Итоги по отделам
        dept_frame = tk.Frame(columns_frame, bg='#f0fdf4', relief='solid', bd=2)
        dept_frame.pack(side='left', fill='both', expand=True, padx=5)

        tk.Label(dept_frame, text="🏢 Итоги по Отделам",
                font=('Cambria', 12, 'bold'),
                bg='#1e3a8a', fg='white').pack(fill='x', pady=(0, 5))

        dept_totals = {}
        for record in self.filtered_data:
            department = record['department']
            amount = float(record['total_amount'])
            dept_totals[department] = dept_totals.get(department, 0) + amount

        for department, total in sorted(dept_totals.items()):
            tk.Label(dept_frame,
                    text=f"{department}: {format_currency(total)}",
                    font=('Cambria', 11),
                    bg='#f0fdf4', anchor='w').pack(fill='x', padx=8, pady=2)

        # Колонка 3: Общие итоги
        total_frame = tk.Frame(columns_frame, bg='#fefce8', relief='solid', bd=2)
        total_frame.pack(side='left', fill='both', expand=True, padx=(5, 0))

        tk.Label(total_frame, text="📈 Общие Итоги",
                font=('Cambria', 12, 'bold'),
                bg='#1e3a8a', fg='white').pack(fill='x', pady=(0, 5))

        # Расчет общих итогов
        total_amount = sum(float(record['total_amount']) for record in self.filtered_data)
        total_quantity = sum(float(record['quantity']) for record in self.filtered_data)
        total_records = len(self.filtered_data)
        avg_check = total_amount / total_records if total_records > 0 else 0

        tk.Label(total_frame,
                text=f"Общая сумма: {format_currency(total_amount)}",
                font=('Cambria', 11, 'bold'),
                bg='#fefce8', anchor='w').pack(fill='x', padx=8, pady=2)

        tk.Label(total_frame,
                text=f"Записей: {total_records:,}".replace(',', ' '),
                font=('Cambria', 11),
                bg='#fefce8', anchor='w').pack(fill='x', padx=8, pady=2)

        tk.Label(total_frame,
                text=f"Количество: {total_quantity:,.1f}".replace(',', ' '),
                font=('Cambria', 11),
                bg='#fefce8', anchor='w').pack(fill='x', padx=8, pady=2)

        tk.Label(total_frame,
                text=f"Средний чек: {format_currency(avg_check)}",
                font=('Cambria', 11),
                bg='#fefce8', anchor='w').pack(fill='x', padx=8, pady=2)

    def export_to_excel(self):
        """Экспорт данных в Excel"""
        try:
            if not self.filtered_data:
                messagebox.showwarning("Предупреждение", "Нет данных для экспорта")
                return

            from tkinter import filedialog
            import openpyxl
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
            from datetime import datetime

            # Диалог сохранения файла
            filename = filedialog.asksaveasfilename(
                title="Сохранить отчет о продажах",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialfile=f"Отчет_продажи_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )

            if not filename:
                return  # Пользователь отменил сохранение

            # Создать новую книгу Excel
            wb = openpyxl.Workbook()

            # Лист с данными о продажах
            ws_data = wb.active
            ws_data.title = "Данные о продажах"

            # Стили для заголовков
            header_font = Font(name='Cambria', size=12, bold=True, color='FFFFFF')
            header_fill = PatternFill(start_color='1e3a8a', end_color='1e3a8a', fill_type='solid')
            header_alignment = Alignment(horizontal='center', vertical='center')
            border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # Заголовки столбцов
            headers = [
                "Дата заказа", "Номер заказа", "Способ оплаты", "Отдел",
                "Код блюда", "Название блюда", "Количество", "Цена за блюдо", "Общая сумма"
            ]

            # Записать заголовки
            for col, header in enumerate(headers, 1):
                cell = ws_data.cell(row=1, column=col, value=header)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
                cell.border = border

            # Записать данные
            for row, record in enumerate(self.filtered_data, 2):
                ws_data.cell(row=row, column=1, value=record['order_date']).border = border
                ws_data.cell(row=row, column=2, value=record['order_number']).border = border
                ws_data.cell(row=row, column=3, value=record['payment_method']).border = border
                ws_data.cell(row=row, column=4, value=record['department']).border = border
                ws_data.cell(row=row, column=5, value=record['dish_code']).border = border
                ws_data.cell(row=row, column=6, value=record['dish_name']).border = border
                ws_data.cell(row=row, column=7, value=float(record['quantity'])).border = border
                ws_data.cell(row=row, column=8, value=float(record['price_per_dish'])).border = border
                ws_data.cell(row=row, column=9, value=float(record['total_amount'])).border = border

            # Автоширина столбцов
            for column in ws_data.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws_data.column_dimensions[column_letter].width = adjusted_width

            # Лист с итогами
            ws_summary = wb.create_sheet("Итоги")

            # Заголовок итогов
            ws_summary.cell(row=1, column=1, value="ОТЧЕТ О ПРОДАЖАХ - ИТОГИ").font = Font(name='Cambria', size=16, bold=True)
            ws_summary.cell(row=2, column=1, value=f"Период: {datetime.now().strftime('%d.%m.%Y')}").font = Font(name='Cambria', size=12)
            ws_summary.cell(row=3, column=1, value=f"Записей: {len(self.filtered_data)}").font = Font(name='Cambria', size=12)

            # Итоги по способам оплаты
            current_row = 5
            ws_summary.cell(row=current_row, column=1, value="ИТОГИ ПО СПОСОБАМ ОПЛАТЫ").font = Font(name='Cambria', size=14, bold=True)
            current_row += 1

            payment_totals = {}
            for record in self.filtered_data:
                payment_method = record['payment_method']
                amount = float(record['total_amount'])
                payment_totals[payment_method] = payment_totals.get(payment_method, 0) + amount

            for payment_method, total in sorted(payment_totals.items()):
                ws_summary.cell(row=current_row, column=1, value=payment_method)
                ws_summary.cell(row=current_row, column=2, value=total)
                current_row += 1

            # Итоги по отделам
            current_row += 2
            ws_summary.cell(row=current_row, column=1, value="ИТОГИ ПО ОТДЕЛАМ").font = Font(name='Cambria', size=14, bold=True)
            current_row += 1

            dept_totals = {}
            for record in self.filtered_data:
                department = record['department']
                amount = float(record['total_amount'])
                dept_totals[department] = dept_totals.get(department, 0) + amount

            for department, total in sorted(dept_totals.items()):
                ws_summary.cell(row=current_row, column=1, value=department)
                ws_summary.cell(row=current_row, column=2, value=total)
                current_row += 1

            # Общие итоги
            current_row += 2
            ws_summary.cell(row=current_row, column=1, value="ОБЩИЕ ИТОГИ").font = Font(name='Cambria', size=14, bold=True)
            current_row += 1

            total_amount = sum(float(record['total_amount']) for record in self.filtered_data)
            total_quantity = sum(float(record['quantity']) for record in self.filtered_data)
            total_records = len(self.filtered_data)
            avg_check = total_amount / total_records if total_records > 0 else 0

            ws_summary.cell(row=current_row, column=1, value="Общая сумма:")
            ws_summary.cell(row=current_row, column=2, value=total_amount)
            current_row += 1

            ws_summary.cell(row=current_row, column=1, value="Количество записей:")
            ws_summary.cell(row=current_row, column=2, value=total_records)
            current_row += 1

            ws_summary.cell(row=current_row, column=1, value="Общее количество:")
            ws_summary.cell(row=current_row, column=2, value=total_quantity)
            current_row += 1

            ws_summary.cell(row=current_row, column=1, value="Средний чек:")
            ws_summary.cell(row=current_row, column=2, value=avg_check)

            # Автоширина для листа итогов
            for column in ws_summary.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws_summary.column_dimensions[column_letter].width = adjusted_width

            # Сохранить файл
            wb.save(filename)

            messagebox.showinfo("Успех",
                f"✅ Отчет успешно экспортирован!\n\n"
                f"📁 Файл: {filename}\n"
                f"📊 Записей: {len(self.filtered_data)}\n"
                f"💰 Общая сумма: {format_currency(total_amount)}")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка экспорта в Excel:\n{str(e)}")
            print(f"❌ Ошибка экспорта: {e}")

    def print_report(self):
        """Печать отчета"""
        try:
            if not self.filtered_data:
                messagebox.showwarning("Предупреждение", "Нет данных для печати")
                return

            # Создать HTML отчет для печати
            html_content = self.generate_html_report()

            # Сохранить во временный файл
            import tempfile
            import os
            import webbrowser

            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_file = f.name

            # Открыть в браузере для печати
            webbrowser.open(f'file://{temp_file}')

            messagebox.showinfo("Печать",
                "✅ Отчет открыт в браузере для печати\n\n"
                "Используйте Ctrl+P для печати отчета")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка печати: {str(e)}")

    def generate_html_report(self):
        """Генерировать HTML отчет"""
        from datetime import datetime

        # Расчет итогов
        total_amount = sum(float(record['total_amount']) for record in self.filtered_data)
        total_quantity = sum(float(record['quantity']) for record in self.filtered_data)
        total_records = len(self.filtered_data)
        avg_check = total_amount / total_records if total_records > 0 else 0

        # Итоги по способам оплаты
        payment_totals = {}
        for record in self.filtered_data:
            payment_method = record['payment_method']
            amount = float(record['total_amount'])
            payment_totals[payment_method] = payment_totals.get(payment_method, 0) + amount

        # Итоги по отделам
        dept_totals = {}
        for record in self.filtered_data:
            department = record['department']
            amount = float(record['total_amount'])
            dept_totals[department] = dept_totals.get(department, 0) + amount

        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Отчет о продажах</title>
            <style>
                body {{ font-family: 'Cambria', serif; margin: 20px; }}
                h1 {{ color: #1e3a8a; text-align: center; }}
                h2 {{ color: #1e3a8a; border-bottom: 2px solid #1e3a8a; }}
                table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #1e3a8a; color: white; font-weight: bold; }}
                .summary {{ display: flex; justify-content: space-between; margin: 20px 0; }}
                .summary-box {{ border: 1px solid #ddd; padding: 15px; width: 30%; }}
                .total {{ font-weight: bold; font-size: 1.2em; color: #1e3a8a; }}
                @media print {{
                    body {{ margin: 0; }}
                    .no-print {{ display: none; }}
                }}
            </style>
        </head>
        <body>
            <h1>📊 ОТЧЕТ О ПРОДАЖАХ</h1>
            <p><strong>Дата формирования:</strong> {datetime.now().strftime('%d.%m.%Y %H:%M')}</p>
            <p><strong>Количество записей:</strong> {total_records}</p>

            <h2>Данные о продажах</h2>
            <table>
                <thead>
                    <tr>
                        <th>Дата</th>
                        <th>№ заказа</th>
                        <th>Способ оплаты</th>
                        <th>Отдел</th>
                        <th>Код блюда</th>
                        <th>Название блюда</th>
                        <th>Кол-во</th>
                        <th>Цена</th>
                        <th>Сумма</th>
                    </tr>
                </thead>
                <tbody>
        """

        # Добавить строки данных
        for record in self.filtered_data:
            html += f"""
                    <tr>
                        <td>{record['order_date']}</td>
                        <td>{record['order_number']}</td>
                        <td>{record['payment_method']}</td>
                        <td>{record['department']}</td>
                        <td>{record['dish_code']}</td>
                        <td>{record['dish_name']}</td>
                        <td>{record['quantity']:.1f}</td>
                        <td>{format_currency(record['price_per_dish'])}</td>
                        <td>{format_currency(record['total_amount'])}</td>
                    </tr>
            """

        html += """
                </tbody>
            </table>

            <div class="summary">
                <div class="summary-box">
                    <h3>💳 Итоги по способам оплаты</h3>
        """

        for payment_method, total in sorted(payment_totals.items()):
            html += f"<p>{payment_method}: {format_currency(total)}</p>"

        html += """
                </div>
                <div class="summary-box">
                    <h3>🏢 Итоги по отделам</h3>
        """

        for department, total in sorted(dept_totals.items()):
            html += f"<p>{department}: {format_currency(total)}</p>"

        html += f"""
                </div>
                <div class="summary-box">
                    <h3>📈 Общие итоги</h3>
                    <p class="total">Общая сумма: {format_currency(total_amount)}</p>
                    <p>Записей: {total_records:,}".replace(',', ' ')</p>
                    <p>Количество: {total_quantity:,.1f}".replace(',', ' ')</p>
                    <p>Средний чек: {format_currency(avg_check)}</p>
                </div>
            </div>
        </body>
        </html>
        """

        return html

    def create_demo_data(self):
        """Создать демонстрационные данные для тестирования"""
        from datetime import datetime, timedelta
        import random

        demo_data = []

        # Создать данные за последние 30 дней
        start_date = datetime.now() - timedelta(days=30)

        dishes = [
            ("DISH001", "Борщ украинский"),
            ("DISH002", "Котлета по-киевски"),
            ("DISH003", "Салат Цезарь"),
            ("DISH004", "Паста Карбонара"),
            ("DISH005", "Стейк из говядины"),
            ("DISH006", "Суп-харчо"),
            ("DISH007", "Пицца Маргарита"),
            ("DISH008", "Рыба на гриле"),
            ("DISH009", "Плов узбекский"),
            ("DISH010", "Десерт Тирамису")
        ]

        payment_methods = ["Наличные", "Банковская карта", "Безналичный расчёт", "Мобильный платёж"]
        departments = ["Кухня", "Бар", "Гриль", "Кондитерская"]

        for i in range(50):  # Создать 50 записей
            # Случайная дата за последние 30 дней
            random_days = random.randint(0, 30)
            order_date = (start_date + timedelta(days=random_days)).strftime('%d.%m.%Y')

            # Случайное блюдо
            dish_code, dish_name = random.choice(dishes)

            # Случайные значения
            quantity = random.randint(1, 5)
            price_per_dish = random.uniform(150, 800)
            total_amount = quantity * price_per_dish

            record = {
                'order_date': order_date,
                'order_number': f'ORD{i+1:04d}',
                'payment_method': random.choice(payment_methods),
                'department': random.choice(departments),
                'dish_code': dish_code,
                'dish_name': dish_name,
                'quantity': quantity,
                'price_per_dish': round(price_per_dish, 2),
                'total_amount': round(total_amount, 2)
            }
            demo_data.append(record)

        return demo_data

def create_sales_data_viewer(parent, db_manager):
    """Создать окно просмотра данных о продажах"""
    return SalesDataViewer(parent, db_manager)
