"""
Alias for modules.database_manager to maintain compatibility
"""

# Import everything from modules.database_manager
try:
    from modules.database_manager import *
except ImportError:
    # Fallback - create basic database manager class
    import sqlite3
    import os
    
    class DatabaseManager:
        def __init__(self, db_path="restaurant_system.db"):
            self.db_path = db_path
            self.connection = None
            
        def connect(self):
            try:
                self.connection = sqlite3.connect(self.db_path)
                return self.connection
            except Exception as e:
                print(f"Database connection error: {e}")
                return None
                
        def close(self):
            if self.connection:
                self.connection.close()
                
        def execute_query(self, query, params=None):
            try:
                if not self.connection:
                    self.connect()
                cursor = self.connection.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                self.connection.commit()
                return cursor.fetchall()
            except Exception as e:
                print(f"Query execution error: {e}")
                return []
