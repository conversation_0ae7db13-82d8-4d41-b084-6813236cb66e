"""
Полностью функциональный центр уведомлений
Управление уведомлениями, алерты, напоминания
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import json
import os
from gui.styles import ModernStyles

class NotificationCenter:
    """Центр уведомлений"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        
        # Уведомления
        self.notifications = [
            {
                "id": 1, "title": "Низкий остаток товара", "message": "Мука пшеничная: осталось 5 кг (минимум 10 кг)",
                "type": "warning", "timestamp": datetime.now() - timedelta(minutes=15),
                "read": False, "priority": "high", "category": "Склад"
            },
            {
                "id": 2, "title": "Новый заказ", "message": "Поступил заказ #1234 на сумму 2,500₽",
                "type": "info", "timestamp": datetime.now() - timedelta(minutes=30),
                "read": True, "priority": "medium", "category": "Продажи"
            },
            {
                "id": 3, "title": "Истекает сертификат", "message": "Сертификат соответствия истекает через 30 дней",
                "type": "warning", "timestamp": datetime.now() - timedelta(hours=2),
                "read": False, "priority": "high", "category": "Документы"
            },
            {
                "id": 4, "title": "Превышен бюджет", "message": "Затраты на продукты превысили бюджет на 5,000₽",
                "type": "error", "timestamp": datetime.now() - timedelta(hours=4),
                "read": False, "priority": "critical", "category": "Финансы"
            },
            {
                "id": 5, "title": "Успешная поставка", "message": "Поставка от 'Мясокомбинат Премиум' получена",
                "type": "success", "timestamp": datetime.now() - timedelta(hours=6),
                "read": True, "priority": "low", "category": "Закупки"
            },
            {
                "id": 6, "title": "Напоминание", "message": "Запланирована проверка качества на 16:00",
                "type": "info", "timestamp": datetime.now() - timedelta(hours=8),
                "read": False, "priority": "medium", "category": "Качество"
            }
        ]
        
        # Настройки уведомлений
        self.notification_settings = {
            "email_notifications": True,
            "sound_alerts": True,
            "desktop_notifications": True,
            "auto_mark_read": False,
            "notification_frequency": "immediate"
        }
    
    def create_window(self):
        """Создать окно центра уведомлений"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("🔔 Центр Уведомлений")
        self.window.geometry("1200x800")
        self.window.configure(bg=ModernStyles.COLORS['bg_main'])
        self.window.resizable(True, True)
        
        self.create_interface()
    
    def create_interface(self):
        """Создать интерфейс центра уведомлений"""
        # Заголовок
        header_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['primary'], height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="🔔 Центр Уведомлений",
                font=('Arial', 16, 'bold'), bg=ModernStyles.COLORS['primary'], 
                fg='white').pack(side='left', padx=20, pady=15)
        
        # Счётчик непрочитанных
        unread_count = len([n for n in self.notifications if not n['read']])
        tk.Label(header_frame, text=f"Непрочитанных: {unread_count}",
                font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['danger'], 
                fg='white', padx=10, pady=5).pack(side='right', padx=20, pady=15)
        
        # Кнопки действий
        btn_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['primary'])
        btn_frame.pack(side='right', padx=20, pady=10)
        
        tk.Button(btn_frame, text="✅ Отметить все как прочитанные", command=self.mark_all_read,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="🗑️ Очистить", command=self.clear_notifications,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        # Основной контент
        main_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Создать вкладки
        self.create_tabs(main_frame)
    
    def create_tabs(self, parent):
        """Создать вкладки модуля"""
        notebook = ttk.Notebook(parent)
        notebook.pack(fill='both', expand=True)
        
        # Вкладка уведомлений
        notifications_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(notifications_frame, text="🔔 Уведомления")
        self.create_notifications_tab(notifications_frame)
        
        # Вкладка фильтров
        filters_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(filters_frame, text="🔍 Фильтры")
        self.create_filters_tab(filters_frame)
        
        # Вкладка настроек
        settings_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(settings_frame, text="⚙️ Настройки")
        self.create_settings_tab(settings_frame)
    
    def create_notifications_tab(self, parent):
        """Создать вкладку уведомлений"""
        # Статистика
        stats_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        stats_frame.pack(fill='x', padx=20, pady=10)
        
        total_notifications = len(self.notifications)
        unread_notifications = len([n for n in self.notifications if not n['read']])
        critical_notifications = len([n for n in self.notifications if n['priority'] == 'critical'])
        
        self.create_stat_card(stats_frame, "Всего", str(total_notifications), ModernStyles.COLORS['primary'])
        self.create_stat_card(stats_frame, "Непрочитанных", str(unread_notifications), ModernStyles.COLORS['warning'])
        self.create_stat_card(stats_frame, "Критических", str(critical_notifications), ModernStyles.COLORS['danger'])
        
        # Список уведомлений
        notifications_list_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        notifications_list_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Создать скроллируемый список
        canvas = tk.Canvas(notifications_list_frame, bg=ModernStyles.COLORS['bg_main'])
        scrollbar = ttk.Scrollbar(notifications_list_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=ModernStyles.COLORS['bg_main'])
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Отобразить уведомления
        for notification in sorted(self.notifications, key=lambda x: x['timestamp'], reverse=True):
            self.create_notification_card(scrollable_frame, notification)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_notification_card(self, parent, notification):
        """Создать карточку уведомления"""
        # Определить цвета по типу
        type_colors = {
            'info': ModernStyles.COLORS['info'],
            'success': ModernStyles.COLORS['success'],
            'warning': ModernStyles.COLORS['warning'],
            'error': ModernStyles.COLORS['danger']
        }
        
        # Определить иконки по типу
        type_icons = {
            'info': 'ℹ️',
            'success': '✅',
            'warning': '⚠️',
            'error': '❌'
        }
        
        # Основная карточка
        card_bg = ModernStyles.COLORS['bg_card'] if notification['read'] else '#f8f9fa'
        card = tk.Frame(parent, bg=card_bg, relief='solid', bd=1)
        card.pack(fill='x', pady=5, padx=5)
        
        # Заголовок карточки
        header = tk.Frame(card, bg=type_colors.get(notification['type'], ModernStyles.COLORS['primary']))
        header.pack(fill='x')
        
        # Иконка и заголовок
        title_frame = tk.Frame(header, bg=type_colors.get(notification['type'], ModernStyles.COLORS['primary']))
        title_frame.pack(side='left', fill='x', expand=True)
        
        icon = type_icons.get(notification['type'], '📢')
        tk.Label(title_frame, text=f"{icon} {notification['title']}", 
                font=('Arial', 11, 'bold'), bg=type_colors.get(notification['type'], ModernStyles.COLORS['primary']), 
                fg='white').pack(side='left', padx=15, pady=8)
        
        # Время и категория
        time_str = notification['timestamp'].strftime('%H:%M')
        tk.Label(header, text=f"{notification['category']} • {time_str}",
                font=('Arial', 9), bg=type_colors.get(notification['type'], ModernStyles.COLORS['primary']), 
                fg='white').pack(side='right', padx=15, pady=8)
        
        # Содержимое
        content_frame = tk.Frame(card, bg=card_bg)
        content_frame.pack(fill='x', padx=15, pady=10)
        
        tk.Label(content_frame, text=notification['message'],
                font=('Arial', 10), bg=card_bg, wraplength=800, justify='left').pack(anchor='w')
        
        # Кнопки действий
        actions_frame = tk.Frame(card, bg=card_bg)
        actions_frame.pack(fill='x', padx=15, pady=(0, 10))
        
        if not notification['read']:
            tk.Button(actions_frame, text="✅ Отметить как прочитанное", 
                     command=lambda n=notification: self.mark_as_read(n),
                     bg=ModernStyles.COLORS['success'], fg='white',
                     font=('Arial', 8, 'bold'), relief='flat', padx=10, pady=3).pack(side='left')
        
        tk.Button(actions_frame, text="🗑️ Удалить", 
                 command=lambda n=notification: self.delete_notification(n),
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Arial', 8, 'bold'), relief='flat', padx=10, pady=3).pack(side='right')
    
    def create_filters_tab(self, parent):
        """Создать вкладку фильтров"""
        tk.Label(parent, text="Фильтры Уведомлений",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)
        
        # Фильтры
        filters_frame = tk.LabelFrame(parent, text="Фильтровать по:",
                                     font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        filters_frame.pack(fill='x', padx=20, pady=20)
        
        # Фильтр по типу
        type_frame = tk.Frame(filters_frame, bg=ModernStyles.COLORS['bg_main'])
        type_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(type_frame, text="Тип:", font=('Arial', 10, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).pack(side='left')
        
        type_combo = ttk.Combobox(type_frame, values=["Все", "Информация", "Успех", "Предупреждение", "Ошибка"])
        type_combo.set("Все")
        type_combo.pack(side='left', padx=10)
        
        # Фильтр по категории
        category_frame = tk.Frame(filters_frame, bg=ModernStyles.COLORS['bg_main'])
        category_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(category_frame, text="Категория:", font=('Arial', 10, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).pack(side='left')
        
        categories = ["Все"] + list(set(n['category'] for n in self.notifications))
        category_combo = ttk.Combobox(category_frame, values=categories)
        category_combo.set("Все")
        category_combo.pack(side='left', padx=10)
        
        # Кнопка применить фильтр
        tk.Button(filters_frame, text="🔍 Применить Фильтр", command=self.apply_filters,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(pady=20)
    
    def create_settings_tab(self, parent):
        """Создать вкладку настроек"""
        tk.Label(parent, text="Настройки Уведомлений",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)
        
        # Настройки
        settings_frame = tk.LabelFrame(parent, text="Параметры уведомлений:",
                                      font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        settings_frame.pack(fill='x', padx=20, pady=20)
        
        # Чекбоксы настроек
        self.email_var = tk.BooleanVar(value=self.notification_settings['email_notifications'])
        self.sound_var = tk.BooleanVar(value=self.notification_settings['sound_alerts'])
        self.desktop_var = tk.BooleanVar(value=self.notification_settings['desktop_notifications'])
        self.auto_read_var = tk.BooleanVar(value=self.notification_settings['auto_mark_read'])
        
        tk.Checkbutton(settings_frame, text="📧 Email уведомления", variable=self.email_var,
                      font=('Arial', 10), bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', padx=20, pady=5)
        
        tk.Checkbutton(settings_frame, text="🔊 Звуковые уведомления", variable=self.sound_var,
                      font=('Arial', 10), bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', padx=20, pady=5)
        
        tk.Checkbutton(settings_frame, text="🖥️ Уведомления на рабочем столе", variable=self.desktop_var,
                      font=('Arial', 10), bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', padx=20, pady=5)
        
        tk.Checkbutton(settings_frame, text="✅ Автоматически отмечать как прочитанные", variable=self.auto_read_var,
                      font=('Arial', 10), bg=ModernStyles.COLORS['bg_main']).pack(anchor='w', padx=20, pady=5)
        
        # Кнопка сохранить
        tk.Button(settings_frame, text="💾 Сохранить Настройки", command=self.save_settings,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(pady=20)
    
    def create_stat_card(self, parent, title, value, color):
        """Создать карточку статистики"""
        card = tk.Frame(parent, bg=color, relief='solid', bd=1)
        card.pack(side='left', fill='both', expand=True, padx=5)
        
        tk.Label(card, text=title, font=('Arial', 10), bg=color, fg='white').pack(pady=(15, 5))
        tk.Label(card, text=value, font=('Arial', 14, 'bold'), bg=color, fg='white').pack(pady=(0, 15))
    
    def mark_as_read(self, notification):
        """Отметить уведомление как прочитанное"""
        notification['read'] = True
        self.refresh_notifications()
        messagebox.showinfo("Успех", "Уведомление отмечено как прочитанное")
    
    def delete_notification(self, notification):
        """Удалить уведомление"""
        if messagebox.askyesno("Подтверждение", "Удалить это уведомление?"):
            self.notifications.remove(notification)
            self.refresh_notifications()
    
    def mark_all_read(self):
        """Отметить все уведомления как прочитанные"""
        for notification in self.notifications:
            notification['read'] = True
        self.refresh_notifications()
        messagebox.showinfo("Успех", "Все уведомления отмечены как прочитанные")
    
    def clear_notifications(self):
        """Очистить все уведомления"""
        if messagebox.askyesno("Подтверждение", "Удалить все уведомления?"):
            self.notifications.clear()
            self.refresh_notifications()
    
    def apply_filters(self):
        """Применить фильтры"""
        messagebox.showinfo("Фильтры", "Фильтры применены")
    
    def save_settings(self):
        """Сохранить настройки"""
        self.notification_settings.update({
            'email_notifications': self.email_var.get(),
            'sound_alerts': self.sound_var.get(),
            'desktop_notifications': self.desktop_var.get(),
            'auto_mark_read': self.auto_read_var.get()
        })
        messagebox.showinfo("Успех", "Настройки сохранены")
    
    def refresh_notifications(self):
        """Обновить отображение уведомлений"""
        if self.window and self.window.winfo_exists():
            self.window.destroy()
            self.create_window()

def create_notification_center(parent, db_manager):
    """Создать центр уведомлений"""
    center = NotificationCenter(parent, db_manager)
    center.create_window()
    return center
