"""
Advanced Database Connection Pool for Restaurant Management System
Provides connection pooling, query optimization, and performance monitoring
"""

import sqlite3
import threading
import time
import queue
import logging
from typing import Optional, Dict, Any, List, Tuple
from contextlib import contextmanager
from datetime import datetime, timedelta
import weakref

class DatabaseConnectionPool:
    """Advanced database connection pool with performance monitoring"""
    
    def __init__(self, db_path: str, min_connections: int = 5, max_connections: int = 20):
        self.db_path = db_path
        self.min_connections = min_connections
        self.max_connections = max_connections
        self.pool = queue.Queue(maxsize=max_connections)
        self.active_connections = {}
        self.connection_stats = {
            'total_created': 0,
            'total_closed': 0,
            'active_count': 0,
            'peak_usage': 0,
            'query_count': 0,
            'avg_query_time': 0.0,
            'slow_queries': []
        }
        self.lock = threading.RLock()
        self.logger = logging.getLogger(__name__)
        
        # Initialize minimum connections
        self._initialize_pool()
        
        # Start monitoring thread
        self.monitoring_thread = threading.Thread(target=self._monitor_connections, daemon=True)
        self.monitoring_thread.start()
    
    def _initialize_pool(self):
        """Initialize the connection pool with minimum connections"""
        for _ in range(self.min_connections):
            conn = self._create_connection()
            self.pool.put(conn)
    
    def _create_connection(self) -> sqlite3.Connection:
        """Create a new database connection with optimizations"""
        conn = sqlite3.connect(
            self.db_path,
            check_same_thread=False,
            timeout=30.0,
            isolation_level=None  # Autocommit mode
        )
        
        # Enable WAL mode for better concurrency
        conn.execute("PRAGMA journal_mode=WAL")
        
        # Optimize SQLite settings
        conn.execute("PRAGMA synchronous=NORMAL")
        conn.execute("PRAGMA cache_size=10000")
        conn.execute("PRAGMA temp_store=MEMORY")
        conn.execute("PRAGMA mmap_size=268435456")  # 256MB
        
        # Enable foreign keys
        conn.execute("PRAGMA foreign_keys=ON")
        
        # Set row factory
        conn.row_factory = sqlite3.Row
        
        with self.lock:
            self.connection_stats['total_created'] += 1
            
        return conn
    
    @contextmanager
    def get_connection(self):
        """Get a connection from the pool"""
        conn = None
        start_time = time.time()
        
        try:
            # Try to get connection from pool
            try:
                conn = self.pool.get_nowait()
            except queue.Empty:
                # Create new connection if under limit
                with self.lock:
                    if len(self.active_connections) < self.max_connections:
                        conn = self._create_connection()
                    else:
                        # Wait for available connection
                        conn = self.pool.get(timeout=10.0)
            
            # Track active connection
            conn_id = id(conn)
            with self.lock:
                self.active_connections[conn_id] = {
                    'connection': conn,
                    'acquired_at': datetime.now(),
                    'thread_id': threading.get_ident()
                }
                self.connection_stats['active_count'] = len(self.active_connections)
                if self.connection_stats['active_count'] > self.connection_stats['peak_usage']:
                    self.connection_stats['peak_usage'] = self.connection_stats['active_count']
            
            yield conn
            
        finally:
            if conn:
                # Return connection to pool
                conn_id = id(conn)
                with self.lock:
                    if conn_id in self.active_connections:
                        del self.active_connections[conn_id]
                        self.connection_stats['active_count'] = len(self.active_connections)
                
                # Check if connection is still valid
                try:
                    conn.execute("SELECT 1")
                    self.pool.put(conn)
                except sqlite3.Error:
                    # Connection is broken, create a new one
                    try:
                        conn.close()
                    except:
                        pass
                    with self.lock:
                        self.connection_stats['total_closed'] += 1
    
    def execute_query(self, query: str, params: Tuple = None, fetch_all: bool = True) -> Any:
        """Execute a query with performance monitoring"""
        start_time = time.time()
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            try:
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                if query.strip().upper().startswith('SELECT'):
                    result = cursor.fetchall() if fetch_all else cursor.fetchone()
                else:
                    conn.commit()
                    result = cursor.rowcount
                
                # Update statistics
                execution_time = time.time() - start_time
                with self.lock:
                    self.connection_stats['query_count'] += 1
                    
                    # Update average query time
                    total_time = (self.connection_stats['avg_query_time'] * 
                                (self.connection_stats['query_count'] - 1) + execution_time)
                    self.connection_stats['avg_query_time'] = total_time / self.connection_stats['query_count']
                    
                    # Track slow queries (> 1 second)
                    if execution_time > 1.0:
                        self.connection_stats['slow_queries'].append({
                            'query': query[:100] + '...' if len(query) > 100 else query,
                            'execution_time': execution_time,
                            'timestamp': datetime.now(),
                            'params': str(params) if params else None
                        })
                        
                        # Keep only last 50 slow queries
                        if len(self.connection_stats['slow_queries']) > 50:
                            self.connection_stats['slow_queries'] = self.connection_stats['slow_queries'][-50:]
                
                return result
                
            except Exception as e:
                self.logger.error(f"Query execution error: {e}")
                self.logger.error(f"Query: {query}")
                self.logger.error(f"Params: {params}")
                raise
    
    def execute_many(self, query: str, params_list: List[Tuple]) -> int:
        """Execute multiple queries in a transaction"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                cursor.execute("BEGIN TRANSACTION")
                cursor.executemany(query, params_list)
                cursor.execute("COMMIT")
                return cursor.rowcount
            except Exception as e:
                cursor.execute("ROLLBACK")
                raise e
    
    def _monitor_connections(self):
        """Monitor connection pool health"""
        while True:
            try:
                time.sleep(60)  # Check every minute
                
                with self.lock:
                    # Clean up stale connections
                    current_time = datetime.now()
                    stale_connections = []
                    
                    for conn_id, info in self.active_connections.items():
                        if current_time - info['acquired_at'] > timedelta(minutes=30):
                            stale_connections.append(conn_id)
                    
                    for conn_id in stale_connections:
                        if conn_id in self.active_connections:
                            self.logger.warning(f"Cleaning up stale connection {conn_id}")
                            try:
                                self.active_connections[conn_id]['connection'].close()
                            except:
                                pass
                            del self.active_connections[conn_id]
                            self.connection_stats['total_closed'] += 1
                    
                    # Ensure minimum connections
                    current_pool_size = self.pool.qsize()
                    if current_pool_size < self.min_connections:
                        for _ in range(self.min_connections - current_pool_size):
                            try:
                                conn = self._create_connection()
                                self.pool.put(conn)
                            except Exception as e:
                                self.logger.error(f"Error creating connection: {e}")
                                break
                
            except Exception as e:
                self.logger.error(f"Connection monitoring error: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get connection pool statistics"""
        with self.lock:
            return {
                'pool_size': self.pool.qsize(),
                'active_connections': len(self.active_connections),
                'total_created': self.connection_stats['total_created'],
                'total_closed': self.connection_stats['total_closed'],
                'peak_usage': self.connection_stats['peak_usage'],
                'query_count': self.connection_stats['query_count'],
                'avg_query_time': round(self.connection_stats['avg_query_time'], 4),
                'slow_queries_count': len(self.connection_stats['slow_queries']),
                'recent_slow_queries': self.connection_stats['slow_queries'][-5:] if self.connection_stats['slow_queries'] else []
            }
    
    def close_all(self):
        """Close all connections in the pool"""
        with self.lock:
            # Close active connections
            for info in self.active_connections.values():
                try:
                    info['connection'].close()
                except:
                    pass
            self.active_connections.clear()
            
            # Close pooled connections
            while not self.pool.empty():
                try:
                    conn = self.pool.get_nowait()
                    conn.close()
                    self.connection_stats['total_closed'] += 1
                except:
                    break

# Global connection pool instance
_connection_pool = None
_pool_lock = threading.Lock()

def get_connection_pool(db_path: str) -> DatabaseConnectionPool:
    """Get or create the global connection pool"""
    global _connection_pool
    
    with _pool_lock:
        if _connection_pool is None or _connection_pool.db_path != db_path:
            if _connection_pool:
                _connection_pool.close_all()
            _connection_pool = DatabaseConnectionPool(db_path)
    
    return _connection_pool
