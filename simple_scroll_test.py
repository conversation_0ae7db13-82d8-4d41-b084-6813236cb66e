#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Простой тест прокрутки
"""

import tkinter as tk
from tkinter import ttk

def simple_scroll_test():
    """Простой тест прокрутки"""
    
    root = tk.Tk()
    root.title("Простой Тест Прокрутки")
    root.geometry("800x600")
    
    # Создать основной фрейм
    main_frame = tk.Frame(root)
    main_frame.pack(fill='both', expand=True, padx=10, pady=10)
    
    # Создать canvas и scrollbar
    canvas = tk.Canvas(main_frame, bg='lightgray')
    scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
    scrollable_frame = tk.Frame(canvas, bg='white')
    
    # Настроить прокрутку
    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )
    
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)
    
    # Прокрутка колесом мыши
    def _on_mousewheel(event):
        canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    canvas.bind_all("<MouseWheel>", _on_mousewheel)
    
    # Создать много кнопок для тестирования прокрутки
    for i in range(50):
        btn = tk.Button(scrollable_frame, text=f"Кнопка {i+1}",
                       command=lambda x=i: print(f"Нажата кнопка {x+1}"),
                       bg='lightblue',
                       pady=10)
        btn.pack(fill='x', padx=5, pady=2)
    
    # Упаковать canvas и scrollbar
    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")
    
    print("✅ Простой тест прокрутки запущен")
    print("🖱️ Используйте колесо мыши для прокрутки")
    
    root.mainloop()

if __name__ == "__main__":
    simple_scroll_test()
