#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import ctypes
import ctypes.wintypes

def check_available_keyboard_layouts():
    """Проверить доступные раскладки клавиатуры в системе"""
    
    print("🔍 ДИАГНОСТИКА РАСКЛАДОК КЛАВИАТУРЫ В СИСТЕМЕ")
    print("=" * 60)
    
    try:
        user32 = ctypes.windll.user32
        
        # Получаем количество доступных раскладок
        num_layouts = user32.GetKeyboardLayoutList(0, None)
        print(f"📊 Количество установленных раскладок: {num_layouts}")
        
        if num_layouts > 0:
            # Создаем массив для хранения раскладок
            layouts = (ctypes.wintypes.HKL * num_layouts)()
            
            # Получаем список раскладок
            actual_count = user32.GetKeyboardLayoutList(num_layouts, layouts)
            print(f"📋 Получено раскладок: {actual_count}")
            
            print("\n🌐 СПИСОК ДОСТУПНЫХ РАСКЛАДОК:")
            print("-" * 40)
            
            for i, layout in enumerate(layouts[:actual_count]):
                layout_id = layout & 0xFFFF
                layout_hex = hex(layout)
                
                # Определяем язык по ID
                if layout_id == 0x0409:
                    lang_name = "🇺🇸 Английская (США)"
                elif layout_id == 0x0419:
                    lang_name = "🇷🇺 Русская"
                elif layout_id == 0x0809:
                    lang_name = "🇬🇧 Английская (Великобритания)"
                elif layout_id == 0x040C:
                    lang_name = "🇫🇷 Французская"
                elif layout_id == 0x0407:
                    lang_name = "🇩🇪 Немецкая"
                elif layout_id == 0x0410:
                    lang_name = "🇮🇹 Итальянская"
                elif layout_id == 0x040A:
                    lang_name = "🇪🇸 Испанская"
                else:
                    lang_name = f"🌐 Неизвестная (ID: {hex(layout_id)})"
                
                print(f"{i+1:2d}. {lang_name}")
                print(f"    HKL: {layout_hex} ({layout})")
                print(f"    ID:  {hex(layout_id)}")
                print()
        
        # Проверяем текущую активную раскладку
        print("📍 ТЕКУЩАЯ АКТИВНАЯ РАСКЛАДКА:")
        print("-" * 30)
        
        hwnd = user32.GetForegroundWindow()
        thread_id = user32.GetWindowThreadProcessId(hwnd, None)
        current_hkl = user32.GetKeyboardLayout(thread_id)
        current_id = current_hkl & 0xFFFF
        
        if current_id == 0x0409:
            current_name = "🇺🇸 Английская"
        elif current_id == 0x0419:
            current_name = "🇷🇺 Русская"
        else:
            current_name = f"🌐 Неизвестная (ID: {hex(current_id)})"
        
        print(f"Текущая: {current_name}")
        print(f"HKL: {hex(current_hkl)} ({current_hkl})")
        print(f"ID:  {hex(current_id)}")
        
        # Проверяем, есть ли русская раскладка
        print("\n🔍 АНАЛИЗ РУССКОЙ РАСКЛАДКИ:")
        print("-" * 30)
        
        russian_found = False
        for layout in layouts[:actual_count]:
            if (layout & 0xFFFF) == 0x0419:
                russian_found = True
                print("✅ Русская раскладка НАЙДЕНА в системе!")
                print(f"   HKL: {hex(layout)} ({layout})")
                break
        
        if not russian_found:
            print("❌ Русская раскладка НЕ НАЙДЕНА в системе!")
            print("💡 Решение:")
            print("   1. Откройте Настройки Windows")
            print("   2. Перейдите в Время и язык > Язык")
            print("   3. Добавьте русский язык")
            print("   4. Установите русскую раскладку клавиатуры")
        
        # Попробуем загрузить русскую раскладку
        print("\n🔧 ТЕСТИРОВАНИЕ ЗАГРУЗКИ РУССКОЙ РАСКЛАДКИ:")
        print("-" * 45)
        
        try:
            # Попробуем загрузить русскую раскладку
            hkl_ru = user32.LoadKeyboardLayoutW("00000419", 0x00000001)
            if hkl_ru:
                print(f"✅ Русская раскладка успешно загружена: {hex(hkl_ru)}")
                
                # Попробуем активировать
                result = user32.ActivateKeyboardLayout(hkl_ru, 0)
                if result:
                    print(f"✅ Русская раскладка успешно активирована: {hex(result)}")
                else:
                    print("❌ Не удалось активировать русскую раскладку")
            else:
                print("❌ Не удалось загрузить русскую раскладку")
                print("💡 Возможные причины:")
                print("   - Русская раскладка не установлена")
                print("   - Неправильный идентификатор раскладки")
                print("   - Системные ограничения")
        except Exception as e:
            print(f"❌ Ошибка при загрузке русской раскладки: {e}")
        
    except Exception as e:
        print(f"❌ Ошибка при получении списка раскладок: {e}")
    
    print("\n" + "=" * 60)
    print("🏁 ДИАГНОСТИКА ЗАВЕРШЕНА")

if __name__ == "__main__":
    check_available_keyboard_layouts()
    input("\nНажмите Enter для выхода...")
