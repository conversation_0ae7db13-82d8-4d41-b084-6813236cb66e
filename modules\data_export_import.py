"""
Advanced Data Export and Import System for Restaurant Management System
Supports multiple formats: Excel, CSV, JSON, XML with data validation and transformation
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import csv
import xml.etree.ElementTree as ET
from datetime import datetime
import os
from typing import Dict, List, Any, Optional
from gui.styles import ModernStyles
from utils.window_utils import create_standard_window, apply_standard_styling
from utils.error_handling import handle_module_error, log_info
from utils.data_transformation import data_validator, data_transformer, data_mapper

try:
    import openpyxl
    from openpyxl.styles import Font, PatternFill, Alignment
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False


class DataExportImportSystem:
    """Advanced data export and import system"""

    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None

        # Supported formats
        self.export_formats = {
            'Excel (.xlsx)': 'xlsx',
            'CSV (.csv)': 'csv',
            'JSON (.json)': 'json',
            'XML (.xml)': 'xml'
        }

        # Available tables for export/import
        self.available_tables = {
            'sales': 'Данные о Продажах',
            'raw_materials': 'Сырьё и Материалы',
            'purchase_orders': 'Заказы на Закупку',
            'recipes': 'Технологические Карты',
            'inventory_adjustments': 'Корректировки Склада',
            'users': 'Пользователи',
            'customers': 'Клиенты',
            'suppliers': 'Поставщики',
            'menu_items': 'Позиции Меню',
            'staff_schedules': 'Расписание Персонала'
        }

        # Export/Import settings
        self.export_settings = {
            'include_headers': True,
            'date_format': 'dd.mm.yyyy',
            'number_format': 'russian',
            'encoding': 'utf-8',
            'delimiter': ';'
        }

    def show_system(self):
        """Show data export/import system"""
        try:
            if self.window and self.window.winfo_exists():
                self.window.lift()
                self.window.focus_set()
                return

            log_info("Открытие системы экспорта/импорта данных", "DataExportImportSystem")

            self.create_window()
            self.create_interface()

        except Exception as e:
            handle_module_error(e, "Система экспорта/импорта данных", "открытие системы")

    def create_window(self):
        """Create system window"""
        try:
            self.window = create_standard_window(
                parent=self.parent,
                title="Экспорт и Импорт Данных",
                width=1000,
                height=700,
                resizable=True,
                maximized=False,
                modal=False,
                icon_emoji="📊"
            )

        except Exception as e:
            handle_module_error(e, "Система экспорта/импорта данных", "создание окна")
            raise

    def create_interface(self):
        """Create system interface"""
        try:
            # Apply standard styling
            main_container = apply_standard_styling(
                self.window,
                "Система Экспорта и Импорта Данных",
                "📊"
            )

            # Create notebook for export/import tabs
            self.notebook = ttk.Notebook(main_container)
            self.notebook.pack(fill='both', expand=True, pady=(0, 20))

            # Create tabs
            self.create_export_tab()
            self.create_import_tab()
            self.create_settings_tab()

            # Create control buttons
            self.create_control_buttons(main_container)

        except Exception as e:
            handle_module_error(e, "Система экспорта/импорта данных", "создание интерфейса")

    def create_export_tab(self):
        """Create data export tab"""
        try:
            tab_frame = ttk.Frame(self.notebook)
            self.notebook.add(tab_frame, text="📤 Экспорт Данных")

            # Create main frame with scrolling
            canvas = tk.Canvas(tab_frame, bg=ModernStyles.COLORS['bg_main'])
            scrollbar = ttk.Scrollbar(tab_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg=ModernStyles.COLORS['bg_main'])

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            # Table selection frame
            table_frame = tk.LabelFrame(scrollable_frame, text="📋 Выбор Таблиц для Экспорта",
                                      font=('Cambria', 14, 'bold'),
                                      fg=ModernStyles.COLORS['text_primary'],
                                      bg=ModernStyles.COLORS['bg_secondary'])
            table_frame.pack(fill='x', padx=20, pady=10)

            # Create checkboxes for tables
            self.export_table_vars = {}
            row = 0
            col = 0
            for table_key, table_name in self.available_tables.items():
                var = tk.BooleanVar()
                self.export_table_vars[table_key] = var

                cb = tk.Checkbutton(table_frame, text=table_name, variable=var,
                                  font=('Cambria', 12),
                                  fg=ModernStyles.COLORS['text_primary'],
                                  bg=ModernStyles.COLORS['bg_secondary'],
                                  selectcolor=ModernStyles.COLORS['bg_main'])
                cb.grid(row=row, column=col, sticky='w', padx=10, pady=5)

                col += 1
                if col > 2:
                    col = 0
                    row += 1

            # Select all/none buttons
            select_frame = tk.Frame(table_frame, bg=ModernStyles.COLORS['bg_secondary'])
            select_frame.grid(row=row+1, column=0, columnspan=3, pady=10)

            tk.Button(select_frame, text="✅ Выбрать Все",
                     command=self.select_all_export_tables,
                     bg=ModernStyles.COLORS['success'], fg='white',
                     font=('Cambria', 11, 'bold'), relief='flat',
                     padx=15, pady=5).pack(side='left', padx=5)

            tk.Button(select_frame, text="❌ Снять Все",
                     command=self.deselect_all_export_tables,
                     bg=ModernStyles.COLORS['warning'], fg='white',
                     font=('Cambria', 11, 'bold'), relief='flat',
                     padx=15, pady=5).pack(side='left', padx=5)

            # Format selection frame
            format_frame = tk.LabelFrame(scrollable_frame, text="📄 Формат Экспорта",
                                       font=('Cambria', 14, 'bold'),
                                       fg=ModernStyles.COLORS['text_primary'],
                                       bg=ModernStyles.COLORS['bg_secondary'])
            format_frame.pack(fill='x', padx=20, pady=10)

            self.export_format_var = tk.StringVar(value='xlsx')
            for format_name, format_code in self.export_formats.items():
                if format_code == 'xlsx' and not EXCEL_AVAILABLE:
                    continue  # Skip Excel if not available

                rb = tk.Radiobutton(format_frame, text=format_name,
                                  variable=self.export_format_var, value=format_code,
                                  font=('Cambria', 12),
                                  fg=ModernStyles.COLORS['text_primary'],
                                  bg=ModernStyles.COLORS['bg_secondary'],
                                  selectcolor=ModernStyles.COLORS['bg_main'])
                rb.pack(anchor='w', padx=10, pady=5)

            # Date range frame
            date_frame = tk.LabelFrame(scrollable_frame, text="📅 Диапазон Дат (опционально)",
                                     font=('Cambria', 14, 'bold'),
                                     fg=ModernStyles.COLORS['text_primary'],
                                     bg=ModernStyles.COLORS['bg_secondary'])
            date_frame.pack(fill='x', padx=20, pady=10)

            date_inner_frame = tk.Frame(date_frame, bg=ModernStyles.COLORS['bg_secondary'])
            date_inner_frame.pack(fill='x', padx=10, pady=10)

            tk.Label(date_inner_frame, text="С:",
                    font=('Cambria', 12, 'bold'),
                    fg=ModernStyles.COLORS['text_primary'],
                    bg=ModernStyles.COLORS['bg_secondary']).grid(row=0, column=0, padx=5, pady=5)

            self.export_start_date = tk.Entry(date_inner_frame, font=('Cambria', 12), width=12)
            self.export_start_date.grid(row=0, column=1, padx=5, pady=5)
            self.export_start_date.insert(0, datetime.now().strftime('%d.%m.%Y'))

            tk.Label(date_inner_frame, text="По:",
                    font=('Cambria', 12, 'bold'),
                    fg=ModernStyles.COLORS['text_primary'],
                    bg=ModernStyles.COLORS['bg_secondary']).grid(row=0, column=2, padx=5, pady=5)

            self.export_end_date = tk.Entry(date_inner_frame, font=('Cambria', 12), width=12)
            self.export_end_date.grid(row=0, column=3, padx=5, pady=5)
            self.export_end_date.insert(0, datetime.now().strftime('%d.%m.%Y'))

            # Export button
            export_button_frame = tk.Frame(scrollable_frame, bg=ModernStyles.COLORS['bg_main'])
            export_button_frame.pack(fill='x', padx=20, pady=20)

            tk.Button(export_button_frame, text="📤 Экспортировать Данные",
                     command=self.export_data,
                     bg=ModernStyles.COLORS['primary'], fg='white',
                     font=('Cambria', 14, 'bold'), relief='flat',
                     padx=30, pady=15).pack()

            # Pack canvas and scrollbar
            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

        except Exception as e:
            handle_module_error(e, "Система экспорта/импорта данных", "создание вкладки экспорта")

    def create_import_tab(self):
        """Create data import tab"""
        try:
            tab_frame = ttk.Frame(self.notebook)
            self.notebook.add(tab_frame, text="📥 Импорт Данных")

            # Create main frame with scrolling
            canvas = tk.Canvas(tab_frame, bg=ModernStyles.COLORS['bg_main'])
            scrollbar = ttk.Scrollbar(tab_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg=ModernStyles.COLORS['bg_main'])

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            # File selection frame
            file_frame = tk.LabelFrame(scrollable_frame, text="📁 Выбор Файла для Импорта",
                                     font=('Cambria', 14, 'bold'),
                                     fg=ModernStyles.COLORS['text_primary'],
                                     bg=ModernStyles.COLORS['bg_secondary'])
            file_frame.pack(fill='x', padx=20, pady=10)

            file_inner_frame = tk.Frame(file_frame, bg=ModernStyles.COLORS['bg_secondary'])
            file_inner_frame.pack(fill='x', padx=10, pady=10)

            self.import_file_path = tk.StringVar()
            tk.Entry(file_inner_frame, textvariable=self.import_file_path,
                    font=('Cambria', 12), width=50, state='readonly').pack(side='left', padx=5)

            tk.Button(file_inner_frame, text="📁 Обзор",
                     command=self.browse_import_file,
                     bg=ModernStyles.COLORS['info'], fg='white',
                     font=('Cambria', 12, 'bold'), relief='flat',
                     padx=15, pady=5).pack(side='left', padx=5)

            # Target table selection
            target_frame = tk.LabelFrame(scrollable_frame, text="🎯 Целевая Таблица",
                                       font=('Cambria', 14, 'bold'),
                                       fg=ModernStyles.COLORS['text_primary'],
                                       bg=ModernStyles.COLORS['bg_secondary'])
            target_frame.pack(fill='x', padx=20, pady=10)

            self.import_table_var = tk.StringVar()
            table_combo = ttk.Combobox(target_frame, textvariable=self.import_table_var,
                                     values=list(self.available_tables.values()),
                                     font=('Cambria', 12), state='readonly', width=40)
            table_combo.pack(padx=10, pady=10)

            # Import options frame
            options_frame = tk.LabelFrame(scrollable_frame, text="⚙️ Опции Импорта",
                                        font=('Cambria', 14, 'bold'),
                                        fg=ModernStyles.COLORS['text_primary'],
                                        bg=ModernStyles.COLORS['bg_secondary'])
            options_frame.pack(fill='x', padx=20, pady=10)

            self.import_mode_var = tk.StringVar(value='append')

            tk.Radiobutton(options_frame, text="➕ Добавить к существующим данным",
                          variable=self.import_mode_var, value='append',
                          font=('Cambria', 12),
                          fg=ModernStyles.COLORS['text_primary'],
                          bg=ModernStyles.COLORS['bg_secondary']).pack(anchor='w', padx=10, pady=5)

            tk.Radiobutton(options_frame, text="🔄 Заменить существующие данные",
                          variable=self.import_mode_var, value='replace',
                          font=('Cambria', 12),
                          fg=ModernStyles.COLORS['text_primary'],
                          bg=ModernStyles.COLORS['bg_secondary']).pack(anchor='w', padx=10, pady=5)

            self.validate_data_var = tk.BooleanVar(value=True)
            tk.Checkbutton(options_frame, text="✅ Проверять данные перед импортом",
                          variable=self.validate_data_var,
                          font=('Cambria', 12),
                          fg=ModernStyles.COLORS['text_primary'],
                          bg=ModernStyles.COLORS['bg_secondary']).pack(anchor='w', padx=10, pady=5)

            # Import button
            import_button_frame = tk.Frame(scrollable_frame, bg=ModernStyles.COLORS['bg_main'])
            import_button_frame.pack(fill='x', padx=20, pady=20)

            tk.Button(import_button_frame, text="📥 Импортировать Данные",
                     command=self.import_data,
                     bg=ModernStyles.COLORS['success'], fg='white',
                     font=('Cambria', 14, 'bold'), relief='flat',
                     padx=30, pady=15).pack()

            # Pack canvas and scrollbar
            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

        except Exception as e:
            handle_module_error(e, "Система экспорта/импорта данных", "создание вкладки импорта")

    def create_settings_tab(self):
        """Create settings tab"""
        try:
            tab_frame = ttk.Frame(self.notebook)
            self.notebook.add(tab_frame, text="⚙️ Настройки")

            # Create main frame with scrolling
            canvas = tk.Canvas(tab_frame, bg=ModernStyles.COLORS['bg_main'])
            scrollbar = ttk.Scrollbar(tab_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg=ModernStyles.COLORS['bg_main'])

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            # Export settings frame
            export_settings_frame = tk.LabelFrame(scrollable_frame, text="📤 Настройки Экспорта",
                                                font=('Cambria', 14, 'bold'),
                                                fg=ModernStyles.COLORS['text_primary'],
                                                bg=ModernStyles.COLORS['bg_secondary'])
            export_settings_frame.pack(fill='x', padx=20, pady=10)

            # Include headers option
            self.include_headers_var = tk.BooleanVar(value=self.export_settings['include_headers'])
            tk.Checkbutton(export_settings_frame, text="✅ Включать заголовки в экспорт",
                          variable=self.include_headers_var,
                          font=('Cambria', 12),
                          fg=ModernStyles.COLORS['text_primary'],
                          bg=ModernStyles.COLORS['bg_secondary']).pack(anchor='w', padx=10, pady=5)

            # Date format selection
            date_format_frame = tk.Frame(export_settings_frame, bg=ModernStyles.COLORS['bg_secondary'])
            date_format_frame.pack(fill='x', padx=10, pady=5)

            tk.Label(date_format_frame, text="📅 Формат даты:",
                    font=('Cambria', 12, 'bold'),
                    fg=ModernStyles.COLORS['text_primary'],
                    bg=ModernStyles.COLORS['bg_secondary']).pack(side='left')

            self.date_format_var = tk.StringVar(value=self.export_settings['date_format'])
            date_formats = ['dd.mm.yyyy', 'yyyy-mm-dd', 'dd/mm/yyyy', 'mm/dd/yyyy']
            date_combo = ttk.Combobox(date_format_frame, textvariable=self.date_format_var,
                                    values=date_formats, font=('Cambria', 11), state='readonly', width=15)
            date_combo.pack(side='left', padx=10)

            # Number format selection
            number_format_frame = tk.Frame(export_settings_frame, bg=ModernStyles.COLORS['bg_secondary'])
            number_format_frame.pack(fill='x', padx=10, pady=5)

            tk.Label(number_format_frame, text="🔢 Формат чисел:",
                    font=('Cambria', 12, 'bold'),
                    fg=ModernStyles.COLORS['text_primary'],
                    bg=ModernStyles.COLORS['bg_secondary']).pack(side='left')

            self.number_format_var = tk.StringVar(value=self.export_settings['number_format'])
            number_formats = ['russian', 'international']
            number_combo = ttk.Combobox(number_format_frame, textvariable=self.number_format_var,
                                      values=number_formats, font=('Cambria', 11), state='readonly', width=15)
            number_combo.pack(side='left', padx=10)

            # Import settings frame
            import_settings_frame = tk.LabelFrame(scrollable_frame, text="📥 Настройки Импорта",
                                                font=('Cambria', 14, 'bold'),
                                                fg=ModernStyles.COLORS['text_primary'],
                                                bg=ModernStyles.COLORS['bg_secondary'])
            import_settings_frame.pack(fill='x', padx=20, pady=10)

            # Encoding selection
            encoding_frame = tk.Frame(import_settings_frame, bg=ModernStyles.COLORS['bg_secondary'])
            encoding_frame.pack(fill='x', padx=10, pady=5)

            tk.Label(encoding_frame, text="🔤 Кодировка:",
                    font=('Cambria', 12, 'bold'),
                    fg=ModernStyles.COLORS['text_primary'],
                    bg=ModernStyles.COLORS['bg_secondary']).pack(side='left')

            self.encoding_var = tk.StringVar(value=self.export_settings['encoding'])
            encodings = ['utf-8', 'windows-1251', 'cp1252']
            encoding_combo = ttk.Combobox(encoding_frame, textvariable=self.encoding_var,
                                        values=encodings, font=('Cambria', 11), state='readonly', width=15)
            encoding_combo.pack(side='left', padx=10)

            # CSV delimiter selection
            delimiter_frame = tk.Frame(import_settings_frame, bg=ModernStyles.COLORS['bg_secondary'])
            delimiter_frame.pack(fill='x', padx=10, pady=5)

            tk.Label(delimiter_frame, text="📊 Разделитель CSV:",
                    font=('Cambria', 12, 'bold'),
                    fg=ModernStyles.COLORS['text_primary'],
                    bg=ModernStyles.COLORS['bg_secondary']).pack(side='left')

            self.delimiter_var = tk.StringVar(value=self.export_settings['delimiter'])
            delimiters = [';', ',', '\t']
            delimiter_combo = ttk.Combobox(delimiter_frame, textvariable=self.delimiter_var,
                                         values=delimiters, font=('Cambria', 11), state='readonly', width=10)
            delimiter_combo.pack(side='left', padx=10)

            # Save settings button
            settings_button_frame = tk.Frame(scrollable_frame, bg=ModernStyles.COLORS['bg_main'])
            settings_button_frame.pack(fill='x', padx=20, pady=20)

            tk.Button(settings_button_frame, text="💾 Сохранить Настройки",
                     command=self.save_settings,
                     bg=ModernStyles.COLORS['primary'], fg='white',
                     font=('Cambria', 12, 'bold'), relief='flat',
                     padx=20, pady=10).pack()

            # Pack canvas and scrollbar
            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

        except Exception as e:
            handle_module_error(e, "Система экспорта/импорта данных", "создание вкладки настроек")

    def save_settings(self):
        """Save export/import settings"""
        try:
            # Update settings
            self.export_settings['include_headers'] = self.include_headers_var.get()
            self.export_settings['date_format'] = self.date_format_var.get()
            self.export_settings['number_format'] = self.number_format_var.get()
            self.export_settings['encoding'] = self.encoding_var.get()
            self.export_settings['delimiter'] = self.delimiter_var.get()

            messagebox.showinfo("Успех", "Настройки сохранены")
            log_info("Настройки экспорта/импорта сохранены", "DataExportImportSystem")

        except Exception as e:
            handle_module_error(e, "Система экспорта/импорта данных", "сохранение настроек")

    def create_control_buttons(self, parent):
        """Create control buttons"""
        try:
            button_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
            button_frame.pack(fill='x', pady=(10, 0))

            tk.Button(button_frame, text="❌ Закрыть",
                     command=self.close_system,
                     bg=ModernStyles.COLORS['secondary'], fg='white',
                     font=('Cambria', 12, 'bold'), relief='flat',
                     padx=30, pady=10).pack(side='right', padx=5)

        except Exception as e:
            handle_module_error(e, "Система экспорта/импорта данных", "создание кнопок управления")

    def select_all_export_tables(self):
        """Select all tables for export"""
        try:
            for var in self.export_table_vars.values():
                var.set(True)
        except Exception as e:
            handle_module_error(e, "Система экспорта/импорта данных", "выбор всех таблиц")

    def deselect_all_export_tables(self):
        """Deselect all tables for export"""
        try:
            for var in self.export_table_vars.values():
                var.set(False)
        except Exception as e:
            handle_module_error(e, "Система экспорта/импорта данных", "снятие выбора таблиц")

    def browse_import_file(self):
        """Browse for import file"""
        try:
            filetypes = [
                ('Все поддерживаемые', '*.xlsx;*.csv;*.json;*.xml'),
                ('Excel файлы', '*.xlsx'),
                ('CSV файлы', '*.csv'),
                ('JSON файлы', '*.json'),
                ('XML файлы', '*.xml'),
                ('Все файлы', '*.*')
            ]

            filename = filedialog.askopenfilename(
                title="Выберите файл для импорта",
                filetypes=filetypes
            )

            if filename:
                self.import_file_path.set(filename)

        except Exception as e:
            handle_module_error(e, "Система экспорта/импорта данных", "выбор файла импорта")

    def export_data(self):
        """Export selected data"""
        try:
            # Get selected tables
            selected_tables = []
            for table_key, var in self.export_table_vars.items():
                if var.get():
                    selected_tables.append(table_key)

            if not selected_tables:
                messagebox.showwarning("Предупреждение", "Выберите хотя бы одну таблицу для экспорта")
                return

            # Get export format
            export_format = self.export_format_var.get()

            # Get date range
            start_date = self.export_start_date.get().strip()
            end_date = self.export_end_date.get().strip()

            # Choose save location
            format_extensions = {
                'xlsx': '.xlsx',
                'csv': '.csv',
                'json': '.json',
                'xml': '.xml'
            }

            default_filename = f"restaurant_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}{format_extensions[export_format]}"

            filename = filedialog.asksaveasfilename(
                title="Сохранить экспорт как",
                defaultextension=format_extensions[export_format],
                initialvalue=default_filename,
                filetypes=[(f"{export_format.upper()} файлы", f"*{format_extensions[export_format]}")]
            )

            if not filename:
                return

            # Perform export
            success = self._perform_export(selected_tables, export_format, filename, start_date, end_date)

            if success:
                messagebox.showinfo("Успех", f"Данные успешно экспортированы в файл:\n{filename}")
            else:
                messagebox.showerror("Ошибка", "Не удалось экспортировать данные")

        except Exception as e:
            handle_module_error(e, "Система экспорта/импорта данных", "экспорт данных")

    def _perform_export(self, tables: List[str], format_type: str, filename: str,
                       start_date: str = None, end_date: str = None) -> bool:
        """Perform the actual data export"""
        try:
            # Collect data from selected tables
            export_data = {}

            for table in tables:
                try:
                    # Get data from database
                    if table in ['sales'] and start_date and end_date:
                        # Apply date filtering for sales data
                        data = self.db_manager.get_sales_data(start_date, end_date)
                    else:
                        # Get all data for other tables
                        query = f"SELECT * FROM {table}"
                        data = self.db_manager.fetch_all(query)

                    export_data[table] = data
                    log_info(f"Получено {len(data)} записей из таблицы {table}", "DataExportImportSystem")

                except Exception as e:
                    log_info(f"Ошибка получения данных из таблицы {table}: {e}", "DataExportImportSystem")
                    continue

            # Export based on format
            if format_type == 'xlsx':
                return self._export_to_excel(export_data, filename)
            elif format_type == 'csv':
                return self._export_to_csv(export_data, filename)
            elif format_type == 'json':
                return self._export_to_json(export_data, filename)
            elif format_type == 'xml':
                return self._export_to_xml(export_data, filename)
            else:
                return False

        except Exception as e:
            log_info(f"Ошибка выполнения экспорта: {e}", "DataExportImportSystem")
            return False

    def _export_to_excel(self, data: Dict[str, List[Dict]], filename: str) -> bool:
        """Export data to Excel format"""
        try:
            if not EXCEL_AVAILABLE:
                messagebox.showerror("Ошибка", "Библиотека openpyxl не установлена")
                return False

            workbook = openpyxl.Workbook()

            # Remove default sheet
            workbook.remove(workbook.active)

            for table_name, table_data in data.items():
                if not table_data:
                    continue

                # Create worksheet
                sheet_name = self.available_tables.get(table_name, table_name)[:31]  # Excel limit
                worksheet = workbook.create_sheet(title=sheet_name)

                # Add headers
                if table_data:
                    headers = list(table_data[0].keys())
                    for col, header in enumerate(headers, 1):
                        cell = worksheet.cell(row=1, column=col, value=header)
                        cell.font = Font(bold=True)
                        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                        cell.font = Font(color="FFFFFF", bold=True)

                    # Add data with formatting
                    for row, record in enumerate(table_data, 2):
                        for col, header in enumerate(headers, 1):
                            value = record.get(header, '')

                            # Format value based on type and field name
                            formatted_value = self._format_export_value(value, header, table_name)
                            worksheet.cell(row=row, column=col, value=formatted_value)

                # Auto-adjust column widths
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            # Save workbook
            workbook.save(filename)
            return True

        except Exception as e:
            log_info(f"Ошибка экспорта в Excel: {e}", "DataExportImportSystem")
            return False

    def _export_to_csv(self, data: Dict[str, List[Dict]], filename: str) -> bool:
        """Export data to CSV format"""
        try:
            # For CSV, we'll create separate files for each table or combine them
            if len(data) == 1:
                # Single table - use the filename as is
                table_name, table_data = next(iter(data.items()))
                return self._write_csv_file(filename, table_data)
            else:
                # Multiple tables - create separate files
                base_name = os.path.splitext(filename)[0]
                success_count = 0

                for table_name, table_data in data.items():
                    table_filename = f"{base_name}_{table_name}.csv"
                    if self._write_csv_file(table_filename, table_data):
                        success_count += 1

                return success_count > 0

        except Exception as e:
            log_info(f"Ошибка экспорта в CSV: {e}", "DataExportImportSystem")
            return False

    def _write_csv_file(self, filename: str, data: List[Dict]) -> bool:
        """Write data to a single CSV file"""
        try:
            if not data:
                return True

            with open(filename, 'w', newline='', encoding=self.export_settings['encoding']) as csvfile:
                fieldnames = list(data[0].keys())
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames,
                                      delimiter=self.export_settings['delimiter'])

                if self.export_settings['include_headers']:
                    writer.writeheader()

                for row in data:
                    writer.writerow(row)

            return True

        except Exception as e:
            log_info(f"Ошибка записи CSV файла {filename}: {e}", "DataExportImportSystem")
            return False

    def _export_to_json(self, data: Dict[str, List[Dict]], filename: str) -> bool:
        """Export data to JSON format"""
        try:
            # Prepare data for JSON export
            export_data = {
                'export_info': {
                    'timestamp': datetime.now().isoformat(),
                    'system': 'Restaurant Management System',
                    'version': '1.0',
                    'tables_count': len(data),
                    'total_records': sum(len(table_data) for table_data in data.values())
                },
                'data': {}
            }

            for table_name, table_data in data.items():
                export_data['data'][table_name] = {
                    'table_info': {
                        'name': self.available_tables.get(table_name, table_name),
                        'records_count': len(table_data)
                    },
                    'records': table_data
                }

            # Write JSON file
            with open(filename, 'w', encoding=self.export_settings['encoding']) as jsonfile:
                json.dump(export_data, jsonfile, ensure_ascii=False, indent=2, default=str)

            return True

        except Exception as e:
            log_info(f"Ошибка экспорта в JSON: {e}", "DataExportImportSystem")
            return False

    def _export_to_xml(self, data: Dict[str, List[Dict]], filename: str) -> bool:
        """Export data to XML format"""
        try:
            # Create root element
            root = ET.Element("restaurant_export")

            # Add export info
            info_elem = ET.SubElement(root, "export_info")
            ET.SubElement(info_elem, "timestamp").text = datetime.now().isoformat()
            ET.SubElement(info_elem, "system").text = "Restaurant Management System"
            ET.SubElement(info_elem, "version").text = "1.0"
            ET.SubElement(info_elem, "tables_count").text = str(len(data))
            ET.SubElement(info_elem, "total_records").text = str(sum(len(table_data) for table_data in data.values()))

            # Add data
            data_elem = ET.SubElement(root, "data")

            for table_name, table_data in data.items():
                table_elem = ET.SubElement(data_elem, "table")
                table_elem.set("name", table_name)
                table_elem.set("display_name", self.available_tables.get(table_name, table_name))
                table_elem.set("records_count", str(len(table_data)))

                records_elem = ET.SubElement(table_elem, "records")

                for record in table_data:
                    record_elem = ET.SubElement(records_elem, "record")
                    for field, value in record.items():
                        field_elem = ET.SubElement(record_elem, "field")
                        field_elem.set("name", field)
                        field_elem.text = str(value) if value is not None else ""

            # Write XML file
            tree = ET.ElementTree(root)
            tree.write(filename, encoding=self.export_settings['encoding'], xml_declaration=True)

            return True

        except Exception as e:
            log_info(f"Ошибка экспорта в XML: {e}", "DataExportImportSystem")
            return False

    def import_data(self):
        """Import data from selected file"""
        try:
            file_path = self.import_file_path.get()
            if not file_path:
                messagebox.showwarning("Предупреждение", "Выберите файл для импорта")
                return

            if not os.path.exists(file_path):
                messagebox.showerror("Ошибка", "Выбранный файл не существует")
                return

            target_table = self.import_table_var.get()
            if not target_table:
                messagebox.showwarning("Предупреждение", "Выберите целевую таблицу")
                return

            # Get table key from display name
            table_key = None
            for key, display_name in self.available_tables.items():
                if display_name == target_table:
                    table_key = key
                    break

            if not table_key:
                messagebox.showerror("Ошибка", "Неверная целевая таблица")
                return

            # Determine file format
            file_ext = os.path.splitext(file_path)[1].lower()

            # Perform import
            success = False
            if file_ext == '.xlsx':
                success = self._import_from_excel(file_path, table_key)
            elif file_ext == '.csv':
                success = self._import_from_csv(file_path, table_key)
            elif file_ext == '.json':
                success = self._import_from_json(file_path, table_key)
            elif file_ext == '.xml':
                success = self._import_from_xml(file_path, table_key)
            else:
                messagebox.showerror("Ошибка", f"Неподдерживаемый формат файла: {file_ext}")
                return

            if success:
                messagebox.showinfo("Успех", "Данные успешно импортированы")
            else:
                messagebox.showerror("Ошибка", "Не удалось импортировать данные")

        except Exception as e:
            handle_module_error(e, "Система экспорта/импорта данных", "импорт данных")

    def _import_from_excel(self, filename: str, table_key: str) -> bool:
        """Import data from Excel file"""
        try:
            if not EXCEL_AVAILABLE:
                messagebox.showerror("Ошибка", "Библиотека openpyxl не установлена")
                return False

            workbook = openpyxl.load_workbook(filename)

            # Use first sheet or find sheet by name
            worksheet = workbook.active

            # Read data
            data = []
            headers = []

            for row_num, row in enumerate(worksheet.iter_rows(values_only=True), 1):
                if row_num == 1:
                    headers = [str(cell) if cell is not None else f"column_{i}" for i, cell in enumerate(row)]
                else:
                    if any(cell is not None for cell in row):
                        record = {}
                        for i, cell in enumerate(row):
                            if i < len(headers):
                                record[headers[i]] = cell
                        data.append(record)

            return self._insert_imported_data(data, table_key)

        except Exception as e:
            log_info(f"Ошибка импорта из Excel: {e}", "DataExportImportSystem")
            return False

    def _import_from_csv(self, filename: str, table_key: str) -> bool:
        """Import data from CSV file"""
        try:
            data = []

            with open(filename, 'r', encoding=self.export_settings['encoding']) as csvfile:
                # Try to detect delimiter
                sample = csvfile.read(1024)
                csvfile.seek(0)

                delimiter = ';' if ';' in sample else ','

                reader = csv.DictReader(csvfile, delimiter=delimiter)
                for row in reader:
                    data.append(dict(row))

            return self._insert_imported_data(data, table_key)

        except Exception as e:
            log_info(f"Ошибка импорта из CSV: {e}", "DataExportImportSystem")
            return False

    def _import_from_json(self, filename: str, table_key: str) -> bool:
        """Import data from JSON file"""
        try:
            with open(filename, 'r', encoding=self.export_settings['encoding']) as jsonfile:
                json_data = json.load(jsonfile)

            # Extract data based on structure
            data = []
            if isinstance(json_data, dict):
                if 'data' in json_data and table_key in json_data['data']:
                    # Our export format
                    data = json_data['data'][table_key].get('records', [])
                elif table_key in json_data:
                    # Direct table data
                    data = json_data[table_key]
                elif 'records' in json_data:
                    # Simple records format
                    data = json_data['records']
            elif isinstance(json_data, list):
                # Array of records
                data = json_data

            return self._insert_imported_data(data, table_key)

        except Exception as e:
            log_info(f"Ошибка импорта из JSON: {e}", "DataExportImportSystem")
            return False

    def _import_from_xml(self, filename: str, table_key: str) -> bool:
        """Import data from XML file"""
        try:
            tree = ET.parse(filename)
            root = tree.getroot()

            data = []

            # Find table data
            for table_elem in root.findall(".//table"):
                if table_elem.get("name") == table_key:
                    for record_elem in table_elem.findall(".//record"):
                        record = {}
                        for field_elem in record_elem.findall("field"):
                            field_name = field_elem.get("name")
                            field_value = field_elem.text
                            record[field_name] = field_value
                        data.append(record)
                    break

            return self._insert_imported_data(data, table_key)

        except Exception as e:
            log_info(f"Ошибка импорта из XML: {e}", "DataExportImportSystem")
            return False

    def _insert_imported_data(self, data: List[Dict], table_key: str) -> bool:
        """Insert imported data into database"""
        try:
            if not data:
                messagebox.showwarning("Предупреждение", "Нет данных для импорта")
                return False

            # Transform and map data
            transformed_data = []
            validation_errors = []

            for i, record in enumerate(data):
                try:
                    # Map fields according to table configuration
                    mapped_record = data_mapper.map_record_fields(record, table_key)

                    # Validate mapped record if requested
                    if self.validate_data_var.get():
                        record_errors = data_mapper.validate_mapped_record(mapped_record, table_key)
                        if record_errors:
                            for error in record_errors:
                                validation_errors.append(f"Строка {i+1}: {error}")
                            continue

                    transformed_data.append(mapped_record)

                except Exception as e:
                    validation_errors.append(f"Строка {i+1}: Ошибка обработки данных - {e}")

            # Check validation errors
            if validation_errors:
                error_msg = "Найдены ошибки в данных:\n" + "\n".join(validation_errors[:10])
                if len(validation_errors) > 10:
                    error_msg += f"\n... и ещё {len(validation_errors) - 10} ошибок"
                messagebox.showerror("Ошибки валидации", error_msg)
                return False

            if not transformed_data:
                messagebox.showwarning("Предупреждение", "Нет корректных данных для импорта")
                return False

            # Handle import mode
            if self.import_mode_var.get() == 'replace':
                # Clear existing data
                with self.db_manager.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute(f"DELETE FROM {table_key}")
                    conn.commit()

            # Insert data using table-specific methods
            success_count = self._insert_table_data(transformed_data, table_key)

            log_info(f"Успешно импортировано {success_count} из {len(data)} записей", "DataExportImportSystem")
            return success_count > 0

        except Exception as e:
            log_info(f"Ошибка вставки импортированных данных: {e}", "DataExportImportSystem")
            return False

    def _insert_table_data(self, data: List[Dict], table_key: str) -> int:
        """Insert data into specific table"""
        success_count = 0

        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                for record in data:
                    try:
                        if table_key == 'sales':
                            success_count += self._insert_sales_record(cursor, record)
                        elif table_key == 'raw_materials':
                            success_count += self._insert_raw_material_record(cursor, record)
                        elif table_key == 'customers':
                            success_count += self._insert_customer_record(cursor, record)
                        else:
                            # Generic insertion for other tables
                            success_count += self._insert_generic_record(cursor, table_key, record)

                    except Exception as e:
                        log_info(f"Ошибка вставки записи в {table_key}: {e}", "DataExportImportSystem")

                conn.commit()

        except Exception as e:
            log_info(f"Ошибка вставки данных в таблицу {table_key}: {e}", "DataExportImportSystem")

        return success_count

    def _insert_sales_record(self, cursor, record: Dict) -> int:
        """Insert sales record"""
        try:
            query = """
                INSERT INTO sales (order_date, total_amount, payment_method, department,
                                 customer_id, notes, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """

            values = (
                record.get('order_date'),
                float(record.get('total_amount', 0)) if record.get('total_amount') else 0,
                record.get('payment_method', 'Наличные'),
                record.get('department', 'Основной'),
                record.get('customer_id'),
                record.get('notes', ''),
                datetime.now().isoformat()
            )

            cursor.execute(query, values)
            return 1

        except Exception as e:
            log_info(f"Ошибка вставки записи продажи: {e}", "DataExportImportSystem")
            return 0

    def _insert_raw_material_record(self, cursor, record: Dict) -> int:
        """Insert raw material record"""
        try:
            query = """
                INSERT INTO raw_materials (name, unit, price, supplier_id,
                                         category, notes, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """

            values = (
                record.get('name', ''),
                record.get('unit', 'шт'),
                float(record.get('price', 0)) if record.get('price') else 0,
                record.get('supplier_id'),
                record.get('category', 'Общие'),
                record.get('notes', ''),
                datetime.now().isoformat()
            )

            cursor.execute(query, values)
            return 1

        except Exception as e:
            log_info(f"Ошибка вставки записи сырья: {e}", "DataExportImportSystem")
            return 0

    def _insert_customer_record(self, cursor, record: Dict) -> int:
        """Insert customer record"""
        try:
            query = """
                INSERT INTO customers (name, phone, email, address,
                                     loyalty_points, notes, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """

            values = (
                record.get('name', ''),
                record.get('phone', ''),
                record.get('email', ''),
                record.get('address', ''),
                int(record.get('loyalty_points', 0)) if record.get('loyalty_points') else 0,
                record.get('notes', ''),
                datetime.now().isoformat()
            )

            cursor.execute(query, values)
            return 1

        except Exception as e:
            log_info(f"Ошибка вставки записи клиента: {e}", "DataExportImportSystem")
            return 0

    def _insert_generic_record(self, cursor, table_key: str, record: Dict) -> int:
        """Insert generic record (placeholder for other tables)"""
        try:
            # This is a simplified generic insertion
            # In a real implementation, you would need specific logic for each table
            log_info(f"Импорт записи в {table_key}: {record}", "DataExportImportSystem")
            return 1

        except Exception as e:
            log_info(f"Ошибка вставки записи в {table_key}: {e}", "DataExportImportSystem")
            return 0

    def _validate_import_data(self, data: List[Dict], table_key: str) -> List[str]:
        """Validate imported data using transformation utilities"""
        errors = []

        try:
            for i, record in enumerate(data):
                if not record:
                    errors.append(f"Строка {i+1}: Пустая запись")
                    continue

                # Map and validate record
                try:
                    mapped_record = data_mapper.map_record_fields(record, table_key)
                    record_errors = data_mapper.validate_mapped_record(mapped_record, table_key)

                    for error in record_errors:
                        errors.append(f"Строка {i+1}: {error}")

                    # Additional table-specific validation
                    if table_key == 'sales':
                        # Validate date format
                        if 'order_date' in mapped_record:
                            is_valid, _ = data_validator.validate_date(str(mapped_record['order_date']))
                            if not is_valid:
                                errors.append(f"Строка {i+1}: Неверный формат даты")

                        # Validate amount
                        if 'total_amount' in mapped_record:
                            is_valid, _ = data_validator.validate_number(mapped_record['total_amount'], allow_negative=False)
                            if not is_valid:
                                errors.append(f"Строка {i+1}: Неверная сумма")

                    elif table_key == 'customers':
                        # Validate email if present
                        if 'email' in mapped_record and mapped_record['email']:
                            if not data_validator.validate_email(mapped_record['email']):
                                errors.append(f"Строка {i+1}: Неверный формат email")

                        # Validate phone if present
                        if 'phone' in mapped_record and mapped_record['phone']:
                            is_valid, _ = data_validator.validate_phone(mapped_record['phone'])
                            if not is_valid:
                                errors.append(f"Строка {i+1}: Неверный формат телефона")

                    elif table_key == 'raw_materials':
                        # Validate price
                        if 'price' in mapped_record and mapped_record['price']:
                            is_valid, _ = data_validator.validate_number(mapped_record['price'], allow_negative=False)
                            if not is_valid:
                                errors.append(f"Строка {i+1}: Неверная цена")

                except Exception as e:
                    errors.append(f"Строка {i+1}: Ошибка обработки данных - {e}")

        except Exception as e:
            errors.append(f"Ошибка валидации: {e}")

        return errors

    def _format_export_value(self, value: Any, field_name: str, table_name: str) -> Any:
        """Format value for export based on field type"""
        try:
            if value is None:
                return ""

            field_lower = field_name.lower()

            # Date fields
            if 'date' in field_lower or 'дата' in field_lower:
                return data_transformer.format_date(value)

            # Currency/amount fields
            if any(keyword in field_lower for keyword in ['amount', 'price', 'cost', 'сумма', 'цена', 'стоимость']):
                if isinstance(value, (int, float)) or (isinstance(value, str) and value.replace('.', '').replace(',', '').isdigit()):
                    return data_transformer.format_currency(value)

            # Phone fields
            if 'phone' in field_lower or 'телефон' in field_lower:
                is_valid, formatted_phone = data_validator.validate_phone(str(value))
                return formatted_phone if is_valid else value

            # Text fields
            if isinstance(value, str):
                return data_transformer.normalize_text(value)

            return value

        except Exception:
            return value

    def close_system(self):
        """Close the system"""
        try:
            if self.window:
                self.window.destroy()
            log_info("Система экспорта/импорта данных закрыта", "DataExportImportSystem")

        except Exception as e:
            log_info(f"Ошибка закрытия системы: {e}", "DataExportImportSystem")


def create_data_export_import_system(parent, db_manager):
    """Create and show data export/import system"""
    try:
        system = DataExportImportSystem(parent, db_manager)
        system.show_system()
        return system

    except Exception as e:
        handle_module_error(e, "Система экспорта/импорта данных")
        return None