#!/usr/bin/env python3
"""
Test Russian encoding detection and display
"""

import pandas as pd
import os

def test_russian_encodings(file_path):
    """Test different encodings for Russian text"""
    print(f"Testing Russian encodings for: {file_path}")
    print("=" * 60)
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return
    
    # Russian encodings to test
    encodings = ['cp1251', 'windows-1251', 'cp866', 'koi8-r', 'utf-8', 'latin-1', 'cp1252']
    separators = [';', ',']
    
    results = []
    
    for encoding in encodings:
        for separator in separators:
            try:
                df = pd.read_csv(file_path, header=None, encoding=encoding, 
                               sep=separator, nrows=3)  # Read only first 3 rows
                
                if df.shape[1] > 10:  # Only consider results with many columns
                    # Look for Russian text in department and dish name columns
                    dept_sample = str(df.iloc[0, 7]) if len(df.columns) > 7 else ""
                    dish_sample = str(df.iloc[0, 8]) if len(df.columns) > 8 else ""
                    
                    # Score Russian text quality
                    russian_chars = set('абвгдеёжзийклмнопрстуфхцчшщъыьэюяАБВГДЕЁЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ')
                    score = 0
                    
                    for text in [dept_sample, dish_sample]:
                        if len(text) > 2:
                            russian_count = sum(1 for char in text if char in russian_chars)
                            score += russian_count
                            
                            # Penalize garbled text
                            if any(bad in text for bad in ['Ã', 'Â', 'Ñ', 'ï', 'ð']):
                                score -= 20
                    
                    results.append({
                        'encoding': encoding,
                        'separator': separator,
                        'columns': df.shape[1],
                        'score': score,
                        'dept_sample': dept_sample,
                        'dish_sample': dish_sample
                    })
                    
                    print(f"✅ {encoding:12} + '{separator}' -> {df.shape[1]:2d} cols, score: {score:3d}")
                    print(f"   Dept: '{dept_sample[:30]}...'")
                    print(f"   Dish: '{dish_sample[:30]}...'")
                    print()
                    
            except Exception as e:
                continue
    
    if results:
        # Find best result
        best = max(results, key=lambda x: (x['score'], x['columns']))
        print("🎯 BEST RESULT:")
        print(f"   Encoding: {best['encoding']}")
        print(f"   Separator: '{best['separator']}'")
        print(f"   Columns: {best['columns']}")
        print(f"   Russian score: {best['score']}")
        print(f"   Department: '{best['dept_sample']}'")
        print(f"   Dish name: '{best['dish_sample']}'")
        
        # Test full file with best settings
        try:
            full_df = pd.read_csv(file_path, header=None, 
                                encoding=best['encoding'], 
                                sep=best['separator'])
            print(f"\n📊 Full file with best settings: {full_df.shape[0]} rows, {full_df.shape[1]} columns")
            
            # Show more samples
            print("\n📋 More samples:")
            for i in range(min(5, len(full_df))):
                dept = str(full_df.iloc[i, 7]) if len(full_df.columns) > 7 else ""
                dish = str(full_df.iloc[i, 8]) if len(full_df.columns) > 8 else ""
                print(f"   Row {i+1}: {dept} | {dish}")
                
        except Exception as e:
            print(f"❌ Error reading full file: {e}")
    else:
        print("❌ No successful reads found")

if __name__ == "__main__":
    # Test with the correct file path
    file_path = r"C:\Users\<USER>\Desktop\1_20220508.csv"
    test_russian_encodings(file_path)
