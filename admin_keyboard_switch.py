#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import ctypes
import sys
import subprocess
import os

def is_admin():
    """Проверить, запущен ли скрипт от имени администратора"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """Запустить скрипт от имени администратора"""
    if is_admin():
        return True
    else:
        print("🔐 Запуск от имени администратора...")
        try:
            # Перезапускаем скрипт с правами администратора
            ctypes.windll.shell32.ShellExecuteW(
                None, 
                "runas", 
                sys.executable, 
                " ".join(['"' + arg + '"' for arg in sys.argv]), 
                None, 
                1
            )
            return False
        except Exception as e:
            print(f"❌ Не удалось запустить от имени администратора: {e}")
            return False

def admin_keyboard_switch():
    """Переключение раскладки с правами администратора"""
    print("🚀 ПЕРЕКЛЮЧЕНИЕ РАСКЛАДКИ С ПРАВАМИ АДМИНИСТРАТОРА")
    print("=" * 80)
    
    if not is_admin():
        print("❌ Скрипт не запущен от имени администратора!")
        if run_as_admin():
            return
        else:
            print("💡 Запустите PowerShell от имени администратора и выполните:")
            print("   python admin_keyboard_switch.py")
            return
    
    print("✅ Скрипт запущен с правами администратора")
    
    # Загружаем библиотеки
    user32 = ctypes.windll.user32
    kernel32 = ctypes.windll.kernel32
    advapi32 = ctypes.windll.advapi32
    
    # МЕТОД 1: Изменение системного реестра
    try:
        print("🔧 Метод 1: Системный реестр...")
        
        # Открываем ключ реестра с максимальными правами
        import winreg
        
        # HKEY_CURRENT_USER\Keyboard Layout\Preload
        key_path = r"Keyboard Layout\Preload"
        try:
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_ALL_ACCESS)
            
            # Устанавливаем русскую раскладку как первую
            winreg.SetValueEx(key, "1", 0, winreg.REG_SZ, "00000419")
            winreg.SetValueEx(key, "2", 0, winreg.REG_SZ, "00000409")
            
            winreg.CloseKey(key)
            print("   ✅ Системный реестр обновлен")
            
        except Exception as e:
            print(f"   ❌ Системный реестр ошибка: {e}")
        
        # HKEY_USERS\.DEFAULT\Keyboard Layout\Preload (для всех пользователей)
        try:
            key_path = r".DEFAULT\Keyboard Layout\Preload"
            key = winreg.OpenKey(winreg.HKEY_USERS, key_path, 0, winreg.KEY_ALL_ACCESS)
            
            winreg.SetValueEx(key, "1", 0, winreg.REG_SZ, "00000419")
            winreg.SetValueEx(key, "2", 0, winreg.REG_SZ, "00000409")
            
            winreg.CloseKey(key)
            print("   ✅ Реестр для всех пользователей обновлен")
            
        except Exception as e:
            print(f"   ❌ Реестр для всех пользователей ошибка: {e}")
            
    except Exception as e:
        print(f"   ❌ Системный реестр общая ошибка: {e}")
    
    # МЕТОД 2: Использование SetSystemDefaultLCID
    try:
        print("🔧 Метод 2: SetSystemDefaultLCID...")
        
        # Русский LCID
        russian_lcid = 0x0419
        
        result = kernel32.SetSystemDefaultLCID(russian_lcid)
        if result:
            print("   ✅ SetSystemDefaultLCID успешно")
        else:
            error = kernel32.GetLastError()
            print(f"   ❌ SetSystemDefaultLCID не удался (ошибка: {error})")
            
    except Exception as e:
        print(f"   ❌ SetSystemDefaultLCID ошибка: {e}")
    
    # МЕТОД 3: Использование SetUserDefaultLCID
    try:
        print("🔧 Метод 3: SetUserDefaultLCID...")
        
        result = kernel32.SetUserDefaultLCID(0x0419)
        if result:
            print("   ✅ SetUserDefaultLCID успешно")
        else:
            error = kernel32.GetLastError()
            print(f"   ❌ SetUserDefaultLCID не удался (ошибка: {error})")
            
    except Exception as e:
        print(f"   ❌ SetUserDefaultLCID ошибка: {e}")
    
    # МЕТОД 4: Принудительная загрузка и активация
    try:
        print("🔧 Метод 4: Принудительная загрузка...")
        
        # Загружаем русскую раскладку с флагом KLF_ACTIVATE
        hkl = user32.LoadKeyboardLayoutW("00000419", 0x00000001 | 0x00000008)  # KLF_ACTIVATE | KLF_REORDER
        
        if hkl:
            print(f"   ✅ Раскладка загружена: {hex(hkl)}")
            
            # Активируем для всех потоков
            result = user32.ActivateKeyboardLayout(hkl, 0x00000008)  # KLF_REORDER
            if result:
                print("   ✅ Раскладка активирована")
            else:
                print("   ❌ Активация не удалась")
        else:
            print("   ❌ Загрузка раскладки не удалась")
            
    except Exception as e:
        print(f"   ❌ Принудительная загрузка ошибка: {e}")
    
    # МЕТОД 5: Использование WTSSetUserConfig (Terminal Services)
    try:
        print("🔧 Метод 5: WTSSetUserConfig...")
        
        wtsapi32 = ctypes.windll.wtsapi32
        
        # WTSUserConfigInitialProgram = 0
        # Пытаемся установить конфигурацию пользователя
        result = wtsapi32.WTSSetUserConfigW(
            None,  # Server name (local)
            ctypes.c_wchar_p(os.getenv('USERNAME')),  # Username
            0,     # WTSUserConfigInitialProgram
            ctypes.c_wchar_p("00000419"),  # Russian layout
            4      # Length
        )
        
        if result:
            print("   ✅ WTSSetUserConfig успешно")
        else:
            error = kernel32.GetLastError()
            print(f"   ❌ WTSSetUserConfig не удался (ошибка: {error})")
            
    except Exception as e:
        print(f"   ❌ WTSSetUserConfig ошибка: {e}")
    
    # МЕТОД 6: Использование PowerShell с правами администратора
    try:
        print("🔧 Метод 6: PowerShell администратор...")
        
        ps_script = '''
        Set-WinUserLanguageList -LanguageList ru-RU, en-US -Force
        Set-WinDefaultInputMethodOverride -InputTip "0419:00000419"
        '''
        
        result = subprocess.run([
            'powershell', 
            '-ExecutionPolicy', 'Bypass',
            '-Command', ps_script
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("   ✅ PowerShell администратор успешно")
        else:
            print(f"   ❌ PowerShell администратор ошибка: {result.stderr}")
            
    except Exception as e:
        print(f"   ❌ PowerShell администратор исключение: {e}")
    
    # Проверяем результат
    import time
    time.sleep(3)
    
    try:
        hwnd = user32.GetForegroundWindow()
        thread_id = user32.GetWindowThreadProcessId(hwnd, None)
        current_hkl = user32.GetKeyboardLayout(thread_id)
        current_id = current_hkl & 0xFFFF
        
        print("\n" + "=" * 80)
        print(f"📋 РЕЗУЛЬТАТ: Текущая раскладка {hex(current_hkl)} (ID: {hex(current_id)})")
        
        if current_id == 0x0419:
            print("🎉 УСПЕХ! Раскладка переключена на русскую с правами администратора!")
            return True
        else:
            print("❌ Даже с правами администратора раскладка не переключилась")
            print("💡 Это указывает на фундаментальные ограничения Windows")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка при проверке результата: {e}")
        return False

if __name__ == "__main__":
    success = admin_keyboard_switch()
    
    if not success:
        print("\n🔍 ДИАГНОСТИКА:")
        print("Проблема не в правах доступа, а в архитектуре Windows.")
        print("Windows 10/11 блокирует программное изменение глобальной раскладки")
        print("по соображениям безопасности и стабильности системы.")
        
        print("\n✅ ОКОНЧАТЕЛЬНОЕ РЕШЕНИЕ:")
        print("Добавить в приложение четкое уведомление пользователю")
        print("о необходимости ручного переключения Alt+Shift после входа.")
    
    input("\nНажмите Enter для выхода...")
