"""
Supply Chain and Vendor Management System
Develop vendor management system with supplier profiles, purchase order automation,
delivery tracking, quality control, and vendor performance analytics.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from datetime import datetime, timedelta
import json
import uuid
import random
from gui.styles import ModernStyles

class SupplyChainVendorManagement:
    """Supply Chain and Vendor Management System"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.notebook = None
        
        # Vendor categories
        self.vendor_categories = {
            "food": {"name": "Продукты питания", "icon": "🥘", "color": ModernStyles.COLORS['success']},
            "beverages": {"name": "Напитки", "icon": "🥤", "color": ModernStyles.COLORS['info']},
            "equipment": {"name": "Оборудование", "icon": "🔧", "color": ModernStyles.COLORS['warning']},
            "supplies": {"name": "Расходные материалы", "icon": "📦", "color": ModernStyles.COLORS['primary']},
            "services": {"name": "Услуги", "icon": "🛠️", "color": ModernStyles.COLORS['secondary']},
            "cleaning": {"name": "Уборка и санитария", "icon": "🧽", "color": ModernStyles.COLORS['danger']}
        }
        
        # Purchase order statuses
        self.po_statuses = {
            "draft": {"name": "Черновик", "color": ModernStyles.COLORS['secondary']},
            "pending": {"name": "Ожидает", "color": ModernStyles.COLORS['warning']},
            "approved": {"name": "Утверждено", "color": ModernStyles.COLORS['info']},
            "ordered": {"name": "Заказано", "color": ModernStyles.COLORS['primary']},
            "delivered": {"name": "Доставлено", "color": ModernStyles.COLORS['success']},
            "cancelled": {"name": "Отменено", "color": ModernStyles.COLORS['danger']}
        }
        
        # Delivery statuses
        self.delivery_statuses = {
            "scheduled": {"name": "Запланировано", "color": ModernStyles.COLORS['info']},
            "in_transit": {"name": "В пути", "color": ModernStyles.COLORS['warning']},
            "delivered": {"name": "Доставлено", "color": ModernStyles.COLORS['success']},
            "delayed": {"name": "Задержка", "color": ModernStyles.COLORS['danger']},
            "partial": {"name": "Частично", "color": ModernStyles.COLORS['secondary']}
        }
        
        # Quality control ratings
        self.quality_ratings = {
            "excellent": {"name": "Отлично", "score": 5, "color": ModernStyles.COLORS['success']},
            "good": {"name": "Хорошо", "score": 4, "color": ModernStyles.COLORS['info']},
            "average": {"name": "Средне", "score": 3, "color": ModernStyles.COLORS['warning']},
            "poor": {"name": "Плохо", "score": 2, "color": ModernStyles.COLORS['danger']},
            "unacceptable": {"name": "Неприемлемо", "score": 1, "color": ModernStyles.COLORS['danger']}
        }
        
        # Performance metrics
        self.performance_metrics = {
            "delivery_time": "Время доставки",
            "quality_score": "Оценка качества",
            "price_competitiveness": "Конкурентоспособность цен",
            "reliability": "Надежность",
            "communication": "Коммуникация",
            "flexibility": "Гибкость"
        }
        
        # Initialize supply chain database tables
        self._init_supply_chain_tables()
    
    def _init_supply_chain_tables(self):
        """Initialize supply chain database tables"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Vendors/Suppliers
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS vendors (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        vendor_code TEXT NOT NULL UNIQUE,
                        vendor_name TEXT NOT NULL,
                        vendor_category TEXT NOT NULL,
                        contact_person TEXT,
                        phone TEXT,
                        email TEXT,
                        address TEXT,
                        tax_id TEXT,
                        payment_terms INTEGER DEFAULT 30,
                        credit_limit DECIMAL(15,2) DEFAULT 0,
                        is_active BOOLEAN DEFAULT 1,
                        rating DECIMAL(3,2) DEFAULT 0,
                        notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Purchase Orders
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS purchase_orders (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        po_number TEXT NOT NULL UNIQUE,
                        vendor_id INTEGER NOT NULL,
                        order_date DATE NOT NULL,
                        expected_delivery_date DATE,
                        actual_delivery_date DATE,
                        status TEXT DEFAULT 'draft',
                        subtotal DECIMAL(15,2) DEFAULT 0,
                        tax_amount DECIMAL(15,2) DEFAULT 0,
                        total_amount DECIMAL(15,2) DEFAULT 0,
                        currency TEXT DEFAULT 'RUB',
                        payment_terms INTEGER DEFAULT 30,
                        delivery_address TEXT,
                        special_instructions TEXT,
                        created_by INTEGER,
                        approved_by INTEGER,
                        approved_at TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (vendor_id) REFERENCES vendors (id),
                        FOREIGN KEY (created_by) REFERENCES users (id),
                        FOREIGN KEY (approved_by) REFERENCES users (id)
                    )
                ''')
                
                # Purchase Order Items
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS purchase_order_items (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        po_id INTEGER NOT NULL,
                        product_name TEXT NOT NULL,
                        product_code TEXT,
                        description TEXT,
                        quantity DECIMAL(10,3) NOT NULL,
                        unit_of_measure TEXT NOT NULL,
                        unit_price DECIMAL(15,4) NOT NULL,
                        line_total DECIMAL(15,2) NOT NULL,
                        received_quantity DECIMAL(10,3) DEFAULT 0,
                        quality_rating TEXT,
                        notes TEXT,
                        FOREIGN KEY (po_id) REFERENCES purchase_orders (id)
                    )
                ''')
                
                # Deliveries
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS deliveries (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        delivery_number TEXT NOT NULL UNIQUE,
                        po_id INTEGER NOT NULL,
                        vendor_id INTEGER NOT NULL,
                        scheduled_date DATE NOT NULL,
                        scheduled_time TIME,
                        actual_delivery_date DATE,
                        actual_delivery_time TIME,
                        delivery_status TEXT DEFAULT 'scheduled',
                        driver_name TEXT,
                        vehicle_info TEXT,
                        tracking_number TEXT,
                        delivery_notes TEXT,
                        received_by INTEGER,
                        received_at TIMESTAMP,
                        FOREIGN KEY (po_id) REFERENCES purchase_orders (id),
                        FOREIGN KEY (vendor_id) REFERENCES vendors (id),
                        FOREIGN KEY (received_by) REFERENCES users (id)
                    )
                ''')
                
                # Quality Control Inspections
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS quality_inspections (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        inspection_number TEXT NOT NULL UNIQUE,
                        delivery_id INTEGER NOT NULL,
                        po_item_id INTEGER NOT NULL,
                        inspector_id INTEGER NOT NULL,
                        inspection_date DATE NOT NULL,
                        inspection_time TIME NOT NULL,
                        overall_rating TEXT NOT NULL,
                        temperature_check BOOLEAN DEFAULT 0,
                        packaging_condition TEXT,
                        expiry_date_check BOOLEAN DEFAULT 0,
                        quantity_accuracy BOOLEAN DEFAULT 0,
                        quality_notes TEXT,
                        action_required TEXT,
                        approved BOOLEAN DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (delivery_id) REFERENCES deliveries (id),
                        FOREIGN KEY (po_item_id) REFERENCES purchase_order_items (id),
                        FOREIGN KEY (inspector_id) REFERENCES users (id)
                    )
                ''')
                
                # Vendor Performance Analytics
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS vendor_performance (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        vendor_id INTEGER NOT NULL,
                        evaluation_period_start DATE NOT NULL,
                        evaluation_period_end DATE NOT NULL,
                        total_orders INTEGER DEFAULT 0,
                        on_time_deliveries INTEGER DEFAULT 0,
                        late_deliveries INTEGER DEFAULT 0,
                        average_delivery_time DECIMAL(5,2) DEFAULT 0,
                        quality_score DECIMAL(3,2) DEFAULT 0,
                        price_competitiveness_score DECIMAL(3,2) DEFAULT 0,
                        reliability_score DECIMAL(3,2) DEFAULT 0,
                        communication_score DECIMAL(3,2) DEFAULT 0,
                        overall_performance_score DECIMAL(3,2) DEFAULT 0,
                        total_spend DECIMAL(15,2) DEFAULT 0,
                        cost_savings DECIMAL(15,2) DEFAULT 0,
                        issues_reported INTEGER DEFAULT 0,
                        issues_resolved INTEGER DEFAULT 0,
                        recommendations TEXT,
                        evaluated_by INTEGER,
                        evaluated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (vendor_id) REFERENCES vendors (id),
                        FOREIGN KEY (evaluated_by) REFERENCES users (id)
                    )
                ''')
                
                conn.commit()
                print("Supply chain database tables initialized successfully")
                
        except Exception as e:
            print(f"Error initializing supply chain tables: {e}")
    
    def show_supply_chain_system(self):
        """Show supply chain and vendor management window"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
            
        self.create_window()
        self.create_interface()
        self.load_supply_chain_data()
    
    def create_window(self):
        """Create the supply chain system window"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("🚚 Система Управления Поставками и Поставщиками")
        self.window.geometry("1600x1000")
        self.window.configure(bg=ModernStyles.COLORS['bg_main'])
        self.window.resizable(True, True)
        self.window.state('zoomed')
        
        # Center window
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # Configure styles
        ModernStyles.configure_ttk_styles(self.window)
    
    def create_interface(self):
        """Create the supply chain system interface"""
        # Main container
        main_container = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        main_container.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Header with real-time status
        header_frame = tk.Frame(main_container, bg=ModernStyles.COLORS['bg_main'])
        header_frame.pack(fill='x', pady=(0, 20))
        
        title_label = tk.Label(header_frame, text="🚚 Система Управления Поставками и Поставщиками",
                              font=('Cambria', 24, 'bold italic'),
                              fg=ModernStyles.COLORS['text_primary'],
                              bg=ModernStyles.COLORS['bg_main'])
        title_label.pack(side='left')
        
        # Real-time status indicators
        self.status_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['bg_main'])
        self.status_frame.pack(side='right')
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_container)
        self.notebook.pack(fill='both', expand=True)
        
        # Create tabs
        self.create_vendors_tab()
        self.create_purchase_orders_tab()
        self.create_deliveries_tab()
        self.create_quality_control_tab()
        self.create_performance_analytics_tab()
        self.create_reports_tab()

    def create_vendors_tab(self):
        """Create vendors management tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="🏢 Поставщики")

        # Vendor controls
        vendor_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        vendor_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(vendor_controls_frame, text="➕ Добавить Поставщика",
                 command=self.add_vendor,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(vendor_controls_frame, text="✏️ Редактировать",
                 command=self.edit_vendor,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(vendor_controls_frame, text="📊 Оценить Производительность",
                 command=self.evaluate_vendor_performance,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(vendor_controls_frame, text="🔍 Поиск",
                 command=self.search_vendors,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Vendors display
        vendors_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        vendors_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Vendors treeview
        vendor_columns = ('vendor_code', 'vendor_name', 'category', 'contact_person', 'phone', 'email', 'rating', 'status', 'last_order')
        self.vendors_tree = ttk.Treeview(vendors_frame, columns=vendor_columns, show='headings', height=15)

        # Configure vendor columns
        self.vendors_tree.heading('vendor_code', text='Код')
        self.vendors_tree.heading('vendor_name', text='Название')
        self.vendors_tree.heading('category', text='Категория')
        self.vendors_tree.heading('contact_person', text='Контактное Лицо')
        self.vendors_tree.heading('phone', text='Телефон')
        self.vendors_tree.heading('email', text='Email')
        self.vendors_tree.heading('rating', text='Рейтинг')
        self.vendors_tree.heading('status', text='Статус')
        self.vendors_tree.heading('last_order', text='Последний Заказ')

        self.vendors_tree.column('vendor_code', width=100)
        self.vendors_tree.column('vendor_name', width=200)
        self.vendors_tree.column('category', width=150)
        self.vendors_tree.column('contact_person', width=150)
        self.vendors_tree.column('phone', width=120)
        self.vendors_tree.column('email', width=180)
        self.vendors_tree.column('rating', width=80)
        self.vendors_tree.column('status', width=100)
        self.vendors_tree.column('last_order', width=120)

        # Vendors scrollbar
        vendors_scrollbar = ttk.Scrollbar(vendors_frame, orient='vertical', command=self.vendors_tree.yview)
        self.vendors_tree.configure(yscrollcommand=vendors_scrollbar.set)

        self.vendors_tree.pack(side='left', fill='both', expand=True)
        vendors_scrollbar.pack(side='right', fill='y')

    def create_purchase_orders_tab(self):
        """Create purchase orders management tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="📋 Заказы на Поставку")

        # Purchase order controls
        po_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        po_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(po_controls_frame, text="➕ Создать Заказ",
                 command=self.create_purchase_order,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(po_controls_frame, text="✅ Утвердить",
                 command=self.approve_purchase_order,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(po_controls_frame, text="📤 Отправить Поставщику",
                 command=self.send_to_vendor,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(po_controls_frame, text="❌ Отменить",
                 command=self.cancel_purchase_order,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Status filter
        tk.Label(po_controls_frame, text="Статус:",
                font=('Cambria', 12, 'bold'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_secondary']).pack(side='left', padx=(20, 5))

        self.po_status_filter = ttk.Combobox(po_controls_frame, font=('Cambria', 11), width=12, state='readonly')
        self.po_status_filter['values'] = ['Все', 'Черновик', 'Ожидает', 'Утверждено', 'Заказано', 'Доставлено', 'Отменено']
        self.po_status_filter.set('Все')
        self.po_status_filter.pack(side='left', padx=5)

        # Purchase orders display
        po_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        po_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Purchase orders treeview
        po_columns = ('po_number', 'vendor_name', 'order_date', 'expected_delivery', 'total_amount', 'status', 'created_by', 'approved_by')
        self.po_tree = ttk.Treeview(po_frame, columns=po_columns, show='headings', height=12)

        # Configure PO columns
        self.po_tree.heading('po_number', text='№ Заказа')
        self.po_tree.heading('vendor_name', text='Поставщик')
        self.po_tree.heading('order_date', text='Дата Заказа')
        self.po_tree.heading('expected_delivery', text='Ожидаемая Доставка')
        self.po_tree.heading('total_amount', text='Сумма')
        self.po_tree.heading('status', text='Статус')
        self.po_tree.heading('created_by', text='Создал')
        self.po_tree.heading('approved_by', text='Утвердил')

        self.po_tree.column('po_number', width=120)
        self.po_tree.column('vendor_name', width=200)
        self.po_tree.column('order_date', width=120)
        self.po_tree.column('expected_delivery', width=140)
        self.po_tree.column('total_amount', width=120)
        self.po_tree.column('status', width=120)
        self.po_tree.column('created_by', width=120)
        self.po_tree.column('approved_by', width=120)

        # PO scrollbar
        po_scrollbar = ttk.Scrollbar(po_frame, orient='vertical', command=self.po_tree.yview)
        self.po_tree.configure(yscrollcommand=po_scrollbar.set)

        self.po_tree.pack(side='left', fill='both', expand=True)
        po_scrollbar.pack(side='right', fill='y')

    def create_deliveries_tab(self):
        """Create deliveries tracking tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="🚛 Доставки")

        # Delivery controls
        delivery_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        delivery_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(delivery_controls_frame, text="📅 Запланировать Доставку",
                 command=self.schedule_delivery,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(delivery_controls_frame, text="✅ Подтвердить Получение",
                 command=self.confirm_delivery,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(delivery_controls_frame, text="📍 Отследить",
                 command=self.track_delivery,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(delivery_controls_frame, text="⚠️ Сообщить о Проблеме",
                 command=self.report_delivery_issue,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Deliveries display
        deliveries_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        deliveries_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Deliveries treeview
        delivery_columns = ('delivery_number', 'po_number', 'vendor_name', 'scheduled_date', 'actual_date', 'status', 'driver', 'tracking')
        self.deliveries_tree = ttk.Treeview(deliveries_frame, columns=delivery_columns, show='headings', height=12)

        # Configure delivery columns
        self.deliveries_tree.heading('delivery_number', text='№ Доставки')
        self.deliveries_tree.heading('po_number', text='№ Заказа')
        self.deliveries_tree.heading('vendor_name', text='Поставщик')
        self.deliveries_tree.heading('scheduled_date', text='Запланировано')
        self.deliveries_tree.heading('actual_date', text='Фактически')
        self.deliveries_tree.heading('status', text='Статус')
        self.deliveries_tree.heading('driver', text='Водитель')
        self.deliveries_tree.heading('tracking', text='Трекинг')

        self.deliveries_tree.column('delivery_number', width=120)
        self.deliveries_tree.column('po_number', width=120)
        self.deliveries_tree.column('vendor_name', width=180)
        self.deliveries_tree.column('scheduled_date', width=120)
        self.deliveries_tree.column('actual_date', width=120)
        self.deliveries_tree.column('status', width=120)
        self.deliveries_tree.column('driver', width=150)
        self.deliveries_tree.column('tracking', width=150)

        # Deliveries scrollbar
        deliveries_scrollbar = ttk.Scrollbar(deliveries_frame, orient='vertical', command=self.deliveries_tree.yview)
        self.deliveries_tree.configure(yscrollcommand=deliveries_scrollbar.set)

        self.deliveries_tree.pack(side='left', fill='both', expand=True)
        deliveries_scrollbar.pack(side='right', fill='y')

    def create_quality_control_tab(self):
        """Create quality control tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="🔍 Контроль Качества")

        # Quality control controls
        qc_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        qc_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(qc_controls_frame, text="🔍 Новая Инспекция",
                 command=self.create_quality_inspection,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(qc_controls_frame, text="✅ Одобрить",
                 command=self.approve_quality_inspection,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(qc_controls_frame, text="❌ Отклонить",
                 command=self.reject_quality_inspection,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(qc_controls_frame, text="📊 Отчет по Качеству",
                 command=self.generate_quality_report,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Quality inspections display
        qc_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        qc_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Quality inspections treeview
        qc_columns = ('inspection_number', 'delivery_number', 'product_name', 'inspector', 'inspection_date', 'rating', 'approved', 'notes')
        self.qc_tree = ttk.Treeview(qc_frame, columns=qc_columns, show='headings', height=12)

        # Configure QC columns
        self.qc_tree.heading('inspection_number', text='№ Инспекции')
        self.qc_tree.heading('delivery_number', text='№ Доставки')
        self.qc_tree.heading('product_name', text='Продукт')
        self.qc_tree.heading('inspector', text='Инспектор')
        self.qc_tree.heading('inspection_date', text='Дата')
        self.qc_tree.heading('rating', text='Оценка')
        self.qc_tree.heading('approved', text='Одобрено')
        self.qc_tree.heading('notes', text='Примечания')

        self.qc_tree.column('inspection_number', width=120)
        self.qc_tree.column('delivery_number', width=120)
        self.qc_tree.column('product_name', width=180)
        self.qc_tree.column('inspector', width=120)
        self.qc_tree.column('inspection_date', width=120)
        self.qc_tree.column('rating', width=100)
        self.qc_tree.column('approved', width=100)
        self.qc_tree.column('notes', width=200)

        # QC scrollbar
        qc_scrollbar = ttk.Scrollbar(qc_frame, orient='vertical', command=self.qc_tree.yview)
        self.qc_tree.configure(yscrollcommand=qc_scrollbar.set)

        self.qc_tree.pack(side='left', fill='both', expand=True)
        qc_scrollbar.pack(side='right', fill='y')

    def create_performance_analytics_tab(self):
        """Create vendor performance analytics tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="📈 Аналитика Производительности")

        # Performance analytics controls
        analytics_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        analytics_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(analytics_controls_frame, text="📊 Оценить Поставщика",
                 command=self.evaluate_vendor,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(analytics_controls_frame, text="📈 Сравнить Поставщиков",
                 command=self.compare_vendors,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(analytics_controls_frame, text="🎯 Установить KPI",
                 command=self.set_vendor_kpis,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Performance analytics display
        analytics_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        analytics_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Performance analytics text display
        analytics_text = tk.Text(analytics_frame, font=('Cambria', 11),
                               bg=ModernStyles.COLORS['bg_main'],
                               fg=ModernStyles.COLORS['text_primary'],
                               wrap='word', state='disabled')
        analytics_text.pack(fill='both', expand=True, padx=15, pady=15)

        # Sample performance analytics data
        analytics_data = """📈 АНАЛИТИКА ПРОИЗВОДИТЕЛЬНОСТИ ПОСТАВЩИКОВ

🏆 ТОП-5 ПОСТАВЩИКОВ ПО ОБЩЕЙ ПРОИЗВОДИТЕЛЬНОСТИ:
1. ООО "Свежие Продукты" - 4.8/5.0 (Отлично)
2. ИП Петров В.А. - 4.6/5.0 (Отлично)
3. ООО "Мясной Двор" - 4.4/5.0 (Хорошо)
4. ООО "Овощи и Фрукты" - 4.2/5.0 (Хорошо)
5. ИП Сидоров С.С. - 4.0/5.0 (Хорошо)

📊 КЛЮЧЕВЫЕ ПОКАЗАТЕЛИ ЭФФЕКТИВНОСТИ (KPI):

⏰ СВОЕВРЕМЕННОСТЬ ДОСТАВОК:
• ООО "Свежие Продукты": 98% (49/50 заказов)
• ИП Петров В.А.: 95% (38/40 заказов)
• ООО "Мясной Двор": 92% (46/50 заказов)
• ООО "Овощи и Фрукты": 88% (35/40 заказов)

🎯 КАЧЕСТВО ПРОДУКЦИИ:
• ООО "Свежие Продукты": 4.9/5.0
• ИП Петров В.А.: 4.7/5.0
• ООО "Мясной Двор": 4.5/5.0
• ООО "Овощи и Фрукты": 4.3/5.0

💰 КОНКУРЕНТОСПОСОБНОСТЬ ЦЕН:
• ИП Петров В.А.: 4.8/5.0 (Лучшие цены)
• ООО "Овощи и Фрукты": 4.6/5.0
• ООО "Свежие Продукты": 4.4/5.0
• ООО "Мясной Двор": 4.2/5.0

📞 КАЧЕСТВО КОММУНИКАЦИИ:
• ООО "Свежие Продукты": 4.9/5.0
• ИП Петров В.А.: 4.8/5.0
• ООО "Мясной Двор": 4.6/5.0
• ООО "Овощи и Фрукты": 4.4/5.0

💸 ФИНАНСОВЫЕ ПОКАЗАТЕЛИ (ПОСЛЕДНИЕ 30 ДНЕЙ):
• Общий объем закупок: 2 847 592,45 руб
• Экономия от переговоров: 142 379,62 руб (5%)
• Средняя стоимость заказа: 28 475,92 руб
• Количество поставщиков: 12

⚠️ ПРОБЛЕМНЫЕ ОБЛАСТИ:
• Задержки доставок: 8% от общего количества
• Проблемы с качеством: 3% от общего количества
• Неполные поставки: 2% от общего количества

🎯 РЕКОМЕНДАЦИИ:
• Увеличить долю заказов у топ-3 поставщиков
• Провести переговоры с ООО "Овощи и Фрукты" по улучшению сроков
• Рассмотреть альтернативных поставщиков для резервирования
• Внедрить систему штрафов за задержки доставок
"""

        analytics_text.config(state='normal')
        analytics_text.insert('1.0', analytics_data)
        analytics_text.config(state='disabled')

    def create_reports_tab(self):
        """Create supply chain reports tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="📋 Отчеты")

        # Reports controls
        reports_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        reports_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(reports_controls_frame, text="📊 Отчет по Поставщикам",
                 command=self.generate_vendor_report,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(reports_controls_frame, text="📋 Отчет по Заказам",
                 command=self.generate_orders_report,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(reports_controls_frame, text="🚛 Отчет по Доставкам",
                 command=self.generate_delivery_report,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(reports_controls_frame, text="💰 Финансовый Отчет",
                 command=self.generate_financial_report,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Reports display
        reports_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        reports_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Reports text display
        reports_text = tk.Text(reports_frame, font=('Cambria', 11),
                             bg=ModernStyles.COLORS['bg_main'],
                             fg=ModernStyles.COLORS['text_primary'],
                             wrap='word', state='disabled')
        reports_text.pack(fill='both', expand=True, padx=15, pady=15)

        # Sample reports data
        reports_data = """📋 ОТЧЕТЫ ПО УПРАВЛЕНИЮ ПОСТАВКАМИ

📊 СВОДНЫЙ ОТЧЕТ ЗА ТЕКУЩИЙ МЕСЯЦ (ИЮНЬ 2024):

🏢 ПОСТАВЩИКИ:
• Всего активных поставщиков: 12
• Новых поставщиков добавлено: 2
• Поставщиков на испытательном сроке: 1
• Средний рейтинг поставщиков: 4.4/5.0

📋 ЗАКАЗЫ НА ПОСТАВКУ:
• Всего заказов создано: 87
• Утверждено заказов: 82
• Отправлено поставщикам: 78
• Выполнено заказов: 74
• Отменено заказов: 5
• Средняя сумма заказа: 32 547,83 руб

🚛 ДОСТАВКИ:
• Всего доставок: 74
• Доставлено вовремя: 68 (92%)
• Доставлено с опозданием: 6 (8%)
• Средняя задержка: 2.3 часа
• Проблемных доставок: 3

🔍 КОНТРОЛЬ КАЧЕСТВА:
• Проведено инспекций: 156
• Одобрено без замечаний: 142 (91%)
• Одобрено с замечаниями: 12 (8%)
• Отклонено: 2 (1%)
• Средняя оценка качества: 4.6/5.0

💰 ФИНАНСОВЫЕ ПОКАЗАТЕЛИ:
• Общий объем закупок: 2 847 592,45 руб
• Экономия от переговоров: 142 379,62 руб
• Средняя рентабельность: 23.5%
• Просроченная задолженность: 0 руб

📈 ТРЕНДЫ И АНАЛИТИКА:
• Рост объема закупок: +12% к прошлому месяцу
• Улучшение качества: +0.3 балла
• Сокращение времени доставки: -15%
• Увеличение количества поставщиков: +2

⚠️ ПРОБЛЕМЫ И РИСКИ:
• Зависимость от 3 основных поставщиков (70% объема)
• Сезонные колебания цен на овощи и фрукты
• Необходимость резервных поставщиков

🎯 РЕКОМЕНДАЦИИ:
• Диверсифицировать базу поставщиков
• Заключить долгосрочные контракты с топ-поставщиками
• Внедрить автоматизацию процесса заказов
• Улучшить систему прогнозирования потребностей
"""

        reports_text.config(state='normal')
        reports_text.insert('1.0', reports_data)
        reports_text.config(state='disabled')

    def load_supply_chain_data(self):
        """Load supply chain data and populate sample data"""
        try:
            self.create_sample_supply_chain_data()
            self.load_vendors_data()
            self.load_purchase_orders_data()
            self.load_deliveries_data()
            self.load_quality_inspections_data()
            self.update_status_indicators()
        except Exception as e:
            print(f"Error loading supply chain data: {e}")

    def create_sample_supply_chain_data(self):
        """Create sample supply chain data"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                # Sample vendors
                sample_vendors = [
                    ('VND001', 'ООО "Свежие Продукты"', 'food', 'Иванов И.И.', '******-123-4567', '<EMAIL>', 'Москва, ул. Садовая, 15', '7701234567', 30, 500000, 1, 4.8),
                    ('VND002', 'ИП Петров В.А.', 'beverages', 'Петров В.А.', '******-234-5678', '<EMAIL>', 'Москва, пр. Мира, 45', '7702345678', 15, 300000, 1, 4.6),
                    ('VND003', 'ООО "Мясной Двор"', 'food', 'Сидоров С.С.', '******-345-6789', '<EMAIL>', 'Москва, ул. Мясницкая, 8', '7703456789', 21, 800000, 1, 4.4),
                    ('VND004', 'ООО "Овощи и Фрукты"', 'food', 'Козлова А.В.', '******-456-7890', '<EMAIL>', 'Москва, рынок Садовод', '7704567890', 14, 400000, 1, 4.2),
                    ('VND005', 'ИП Николаев Н.Н.', 'equipment', 'Николаев Н.Н.', '******-567-8901', '<EMAIL>', 'Москва, ул. Промышленная, 12', '7705678901', 45, 1000000, 1, 4.0),
                    ('VND006', 'ООО "Чистота и Порядок"', 'cleaning', 'Морозова М.М.', '******-678-9012', '<EMAIL>', 'Москва, ул. Чистая, 7', '7706789012', 30, 200000, 1, 4.3)
                ]

                for vendor in sample_vendors:
                    cursor.execute('''
                        INSERT OR IGNORE INTO vendors
                        (vendor_code, vendor_name, vendor_category, contact_person, phone, email, address, tax_id, payment_terms, credit_limit, is_active, rating)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', vendor)

                # Sample purchase orders
                sample_pos = [
                    ('PO-2024-001', 1, '2024-06-01', '2024-06-03', '2024-06-03', 'delivered', 45000, 4500, 49500, 'RUB', 30, 'Основной склад', 'Срочная доставка', 1, 1, '2024-06-01 10:00:00'),
                    ('PO-2024-002', 2, '2024-06-02', '2024-06-04', '2024-06-04', 'delivered', 28000, 2800, 30800, 'RUB', 15, 'Основной склад', '', 1, 1, '2024-06-02 11:00:00'),
                    ('PO-2024-003', 3, '2024-06-03', '2024-06-05', None, 'ordered', 67000, 6700, 73700, 'RUB', 21, 'Основной склад', 'Проверить качество мяса', 1, 1, '2024-06-03 09:00:00'),
                    ('PO-2024-004', 4, '2024-06-04', '2024-06-06', None, 'approved', 32000, 3200, 35200, 'RUB', 14, 'Основной склад', '', 1, 1, '2024-06-04 14:00:00'),
                    ('PO-2024-005', 1, '2024-06-05', '2024-06-07', None, 'pending', 52000, 5200, 57200, 'RUB', 30, 'Основной склад', '', 1, None, None)
                ]

                for po in sample_pos:
                    cursor.execute('''
                        INSERT OR IGNORE INTO purchase_orders
                        (po_number, vendor_id, order_date, expected_delivery_date, actual_delivery_date, status, subtotal, tax_amount, total_amount, currency, payment_terms, delivery_address, special_instructions, created_by, approved_by, approved_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', po)

                # Sample deliveries
                sample_deliveries = [
                    ('DEL-2024-001', 1, 1, '2024-06-03', '09:00', '2024-06-03', '09:15', 'delivered', 'Иванов А.А.', 'ГАЗель А123БВ', 'TRK001', 'Доставлено в срок', 1, '2024-06-03 09:15:00'),
                    ('DEL-2024-002', 2, 2, '2024-06-04', '10:00', '2024-06-04', '10:30', 'delivered', 'Петров Б.Б.', 'Форд В456ГД', 'TRK002', 'Небольшая задержка', 1, '2024-06-04 10:30:00'),
                    ('DEL-2024-003', 3, 3, '2024-06-05', '08:00', None, None, 'in_transit', 'Сидоров В.В.', 'МАЗ Г789ЕЖ', 'TRK003', 'В пути', None, None),
                    ('DEL-2024-004', 4, 4, '2024-06-06', '11:00', None, None, 'scheduled', 'Козлов Г.Г.', 'Камаз Д012ЗИ', 'TRK004', 'Запланировано', None, None)
                ]

                for delivery in sample_deliveries:
                    cursor.execute('''
                        INSERT OR IGNORE INTO deliveries
                        (delivery_number, po_id, vendor_id, scheduled_date, scheduled_time, actual_delivery_date, actual_delivery_time, delivery_status, driver_name, vehicle_info, tracking_number, delivery_notes, received_by, received_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', delivery)

                conn.commit()
                print("Sample supply chain data created successfully")

        except Exception as e:
            print(f"Error creating sample supply chain data: {e}")

    def load_vendors_data(self):
        """Load vendors data into the tree view"""
        try:
            # Clear existing data
            for item in self.vendors_tree.get_children():
                self.vendors_tree.delete(item)

            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT v.vendor_code, v.vendor_name, v.vendor_category, v.contact_person,
                           v.phone, v.email, v.rating,
                           CASE WHEN v.is_active = 1 THEN 'Активен' ELSE 'Неактивен' END as status,
                           COALESCE(MAX(po.order_date), 'Нет заказов') as last_order
                    FROM vendors v
                    LEFT JOIN purchase_orders po ON v.id = po.vendor_id
                    GROUP BY v.id
                    ORDER BY v.vendor_name
                ''')

                vendors = cursor.fetchall()
                for vendor in vendors:
                    # Format rating
                    rating = f"{vendor[6]:.1f}/5.0" if vendor[6] else "Не оценен"

                    # Format category
                    category = self.vendor_categories.get(vendor[2], {}).get('name', vendor[2])

                    self.vendors_tree.insert('', 'end', values=(
                        vendor[0], vendor[1], category, vendor[3], vendor[4], vendor[5], rating, vendor[7], vendor[8]
                    ))

        except Exception as e:
            print(f"Error loading vendors data: {e}")

    def load_purchase_orders_data(self):
        """Load purchase orders data into the tree view"""
        try:
            # Clear existing data
            for item in self.po_tree.get_children():
                self.po_tree.delete(item)

            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT po.po_number, v.vendor_name, po.order_date, po.expected_delivery_date,
                           po.total_amount, po.status,
                           COALESCE(u1.username, 'Система') as created_by,
                           COALESCE(u2.username, '-') as approved_by
                    FROM purchase_orders po
                    JOIN vendors v ON po.vendor_id = v.id
                    LEFT JOIN users u1 ON po.created_by = u1.id
                    LEFT JOIN users u2 ON po.approved_by = u2.id
                    ORDER BY po.order_date DESC
                ''')

                orders = cursor.fetchall()
                for order in orders:
                    # Format amount
                    amount = f"{order[4]:,.2f} руб".replace(',', ' ').replace('.', ',')

                    # Format status
                    status = self.po_statuses.get(order[5], {}).get('name', order[5])

                    self.po_tree.insert('', 'end', values=(
                        order[0], order[1], order[2], order[3] or '-', amount, status, order[6], order[7]
                    ))

        except Exception as e:
            print(f"Error loading purchase orders data: {e}")

    def load_deliveries_data(self):
        """Load deliveries data into the tree view"""
        try:
            # Clear existing data
            for item in self.deliveries_tree.get_children():
                self.deliveries_tree.delete(item)

            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT d.delivery_number, po.po_number, v.vendor_name, d.scheduled_date,
                           d.actual_delivery_date, d.delivery_status, d.driver_name, d.tracking_number
                    FROM deliveries d
                    JOIN purchase_orders po ON d.po_id = po.id
                    JOIN vendors v ON d.vendor_id = v.id
                    ORDER BY d.scheduled_date DESC
                ''')

                deliveries = cursor.fetchall()
                for delivery in deliveries:
                    # Format status
                    status = self.delivery_statuses.get(delivery[5], {}).get('name', delivery[5])

                    self.deliveries_tree.insert('', 'end', values=(
                        delivery[0], delivery[1], delivery[2], delivery[3],
                        delivery[4] or '-', status, delivery[6] or '-', delivery[7] or '-'
                    ))

        except Exception as e:
            print(f"Error loading deliveries data: {e}")

    def load_quality_inspections_data(self):
        """Load quality inspections data into the tree view"""
        try:
            # Clear existing data
            for item in self.qc_tree.get_children():
                self.qc_tree.delete(item)

            # Sample quality inspections data
            sample_inspections = [
                ('QC-2024-001', 'DEL-2024-001', 'Картофель молодой', 'Иванов И.И.', '2024-06-03', 'Отлично', 'Да', 'Высокое качество'),
                ('QC-2024-002', 'DEL-2024-002', 'Сок апельсиновый', 'Петров П.П.', '2024-06-04', 'Хорошо', 'Да', 'Соответствует стандартам'),
                ('QC-2024-003', 'DEL-2024-001', 'Морковь свежая', 'Иванов И.И.', '2024-06-03', 'Отлично', 'Да', 'Отличное качество'),
                ('QC-2024-004', 'DEL-2024-002', 'Вода минеральная', 'Сидоров С.С.', '2024-06-04', 'Хорошо', 'Да', 'Без замечаний')
            ]

            for inspection in sample_inspections:
                self.qc_tree.insert('', 'end', values=inspection)

        except Exception as e:
            print(f"Error loading quality inspections data: {e}")

    def update_status_indicators(self):
        """Update real-time status indicators"""
        try:
            # Clear existing status indicators
            for widget in self.status_frame.winfo_children():
                widget.destroy()

            # Active vendors count
            active_vendors_label = tk.Label(self.status_frame,
                                          text="🏢 Активных поставщиков: 6",
                                          font=('Cambria', 12, 'bold'),
                                          fg=ModernStyles.COLORS['success'],
                                          bg=ModernStyles.COLORS['bg_main'])
            active_vendors_label.pack(side='left', padx=10)

            # Pending orders count
            pending_orders_label = tk.Label(self.status_frame,
                                          text="📋 Ожидающих заказов: 2",
                                          font=('Cambria', 12, 'bold'),
                                          fg=ModernStyles.COLORS['warning'],
                                          bg=ModernStyles.COLORS['bg_main'])
            pending_orders_label.pack(side='left', padx=10)

            # In transit deliveries
            transit_deliveries_label = tk.Label(self.status_frame,
                                               text="🚛 В пути: 1",
                                               font=('Cambria', 12, 'bold'),
                                               fg=ModernStyles.COLORS['info'],
                                               bg=ModernStyles.COLORS['bg_main'])
            transit_deliveries_label.pack(side='left', padx=10)

            # Quality issues
            quality_issues_label = tk.Label(self.status_frame,
                                          text="⚠️ Проблем качества: 0",
                                          font=('Cambria', 12, 'bold'),
                                          fg=ModernStyles.COLORS['success'],
                                          bg=ModernStyles.COLORS['bg_main'])
            quality_issues_label.pack(side='left', padx=10)

        except Exception as e:
            print(f"Error updating status indicators: {e}")

    # Action methods (placeholder implementations)
    def add_vendor(self):
        """Add new vendor"""
        messagebox.showinfo("Добавить Поставщика", "Функция добавления нового поставщика будет реализована")

    def edit_vendor(self):
        """Edit selected vendor"""
        messagebox.showinfo("Редактировать Поставщика", "Функция редактирования поставщика будет реализована")

    def evaluate_vendor_performance(self):
        """Evaluate vendor performance"""
        messagebox.showinfo("Оценить Производительность", "Функция оценки производительности поставщика будет реализована")

    def search_vendors(self):
        """Search vendors"""
        messagebox.showinfo("Поиск Поставщиков", "Функция поиска поставщиков будет реализована")

    def create_purchase_order(self):
        """Create new purchase order"""
        messagebox.showinfo("Создать Заказ", "Функция создания заказа на поставку будет реализована")

    def approve_purchase_order(self):
        """Approve purchase order"""
        messagebox.showinfo("Утвердить Заказ", "Функция утверждения заказа будет реализована")

    def send_to_vendor(self):
        """Send purchase order to vendor"""
        messagebox.showinfo("Отправить Поставщику", "Функция отправки заказа поставщику будет реализована")

    def cancel_purchase_order(self):
        """Cancel purchase order"""
        messagebox.showinfo("Отменить Заказ", "Функция отмены заказа будет реализована")

    def schedule_delivery(self):
        """Schedule delivery"""
        messagebox.showinfo("Запланировать Доставку", "Функция планирования доставки будет реализована")

    def confirm_delivery(self):
        """Confirm delivery receipt"""
        messagebox.showinfo("Подтвердить Получение", "Функция подтверждения получения доставки будет реализована")

    def track_delivery(self):
        """Track delivery"""
        messagebox.showinfo("Отследить Доставку", "Функция отслеживания доставки будет реализована")

    def report_delivery_issue(self):
        """Report delivery issue"""
        messagebox.showinfo("Сообщить о Проблеме", "Функция сообщения о проблеме с доставкой будет реализована")

    def create_quality_inspection(self):
        """Create quality inspection"""
        messagebox.showinfo("Новая Инспекция", "Функция создания инспекции качества будет реализована")

    def approve_quality_inspection(self):
        """Approve quality inspection"""
        messagebox.showinfo("Одобрить Инспекцию", "Функция одобрения инспекции качества будет реализована")

    def reject_quality_inspection(self):
        """Reject quality inspection"""
        messagebox.showinfo("Отклонить Инспекцию", "Функция отклонения инспекции качества будет реализована")

    def generate_quality_report(self):
        """Generate quality report"""
        messagebox.showinfo("Отчет по Качеству", "Функция генерации отчета по качеству будет реализована")

    def evaluate_vendor(self):
        """Evaluate vendor"""
        messagebox.showinfo("Оценить Поставщика", "Функция оценки поставщика будет реализована")

    def compare_vendors(self):
        """Compare vendors"""
        messagebox.showinfo("Сравнить Поставщиков", "Функция сравнения поставщиков будет реализована")

    def set_vendor_kpis(self):
        """Set vendor KPIs"""
        messagebox.showinfo("Установить KPI", "Функция установки KPI поставщиков будет реализована")

    def generate_vendor_report(self):
        """Generate vendor report"""
        messagebox.showinfo("Отчет по Поставщикам", "Функция генерации отчета по поставщикам будет реализована")

    def generate_orders_report(self):
        """Generate orders report"""
        messagebox.showinfo("Отчет по Заказам", "Функция генерации отчета по заказам будет реализована")

    def generate_delivery_report(self):
        """Generate delivery report"""
        messagebox.showinfo("Отчет по Доставкам", "Функция генерации отчета по доставкам будет реализована")

    def generate_financial_report(self):
        """Generate financial report"""
        messagebox.showinfo("Финансовый Отчет", "Функция генерации финансового отчета будет реализована")

def create_supply_chain_vendor_management(parent, db_manager):
    """Create and show the supply chain and vendor management system"""
    try:
        supply_chain_system = SupplyChainVendorManagement(parent, db_manager)
        supply_chain_system.show_supply_chain_system()
        return supply_chain_system
    except Exception as e:
        messagebox.showerror("Ошибка", f"Не удалось открыть систему управления поставками: {e}")
        return None
