"""
Data Synchronization Monitor for Restaurant Management System
Provides interface for monitoring and managing multi-location data synchronization
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import threading
import time

from gui.styles import ModernStyles
from database.sync_manager import DataSync<PERSON>anager, SyncStatus, ConflictResolution

class SyncMonitorWindow:
    """Data synchronization monitoring and management interface"""
    
    def __init__(self, parent, db_manager, location_id: str = "main"):
        self.parent = parent
        self.db_manager = db_manager
        self.location_id = location_id
        self.window = None
        
        # Initialize sync manager
        self.sync_manager = DataSyncManager(
            db_manager.connection_pool,
            location_id
        )
        
        # Monitoring state
        self.monitoring_active = False
        self.monitoring_thread = None
        self.auto_refresh = True
        
        # UI components
        self.status_labels = {}
        self.tree_views = {}
        self.charts = {}
        
        self.create_window()
    
    def create_window(self):
        """Create the sync monitor window"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("Монитор Синхронизации Данных")
        self.window.geometry("1400x900")
        self.window.configure(bg=ModernStyles.COLORS['bg_main'])
        
        # Make window resizable
        self.window.resizable(True, True)
        
        # Center window
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # Create main interface
        self.create_main_interface()
        
        # Start monitoring
        self.start_monitoring()
        
        # Handle window close
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_main_interface(self):
        """Create the main interface"""
        # Create notebook for tabs
        notebook = ttk.Notebook(self.window)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Create tabs
        self.create_overview_tab(notebook)
        self.create_sync_log_tab(notebook)
        self.create_conflicts_tab(notebook)
        self.create_settings_tab(notebook)
        self.create_statistics_tab(notebook)
    
    def create_overview_tab(self, notebook):
        """Create overview tab"""
        overview_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(overview_frame, text="📊 Обзор")
        
        # Status section
        status_frame = tk.LabelFrame(overview_frame, text="Статус Синхронизации",
                                   font=('Cambria', 12, 'bold'),
                                   bg=ModernStyles.COLORS['bg_main'],
                                   fg=ModernStyles.COLORS['text_primary'])
        status_frame.pack(fill='x', padx=10, pady=10)
        
        # Status metrics
        metrics_frame = tk.Frame(status_frame, bg=ModernStyles.COLORS['bg_main'])
        metrics_frame.pack(fill='x', padx=10, pady=10)
        
        # Create status cards
        self.create_status_card(metrics_frame, "🌐 Статус", "Онлайн", ModernStyles.COLORS['success'], 0, 0)
        self.create_status_card(metrics_frame, "🔄 Синхронизация", "Активна", ModernStyles.COLORS['primary'], 0, 1)
        self.create_status_card(metrics_frame, "📍 Локация", self.location_id, ModernStyles.COLORS['info'], 0, 2)
        self.create_status_card(metrics_frame, "⏰ Последняя Синхронизация", "Никогда", ModernStyles.COLORS['secondary'], 0, 3)
        
        self.create_status_card(metrics_frame, "📤 Ожидающие Отправки", "0", ModernStyles.COLORS['warning'], 1, 0)
        self.create_status_card(metrics_frame, "⚠️ Конфликты", "0", ModernStyles.COLORS['danger'], 1, 1)
        self.create_status_card(metrics_frame, "✅ Синхронизировано", "0", ModernStyles.COLORS['success'], 1, 2)
        self.create_status_card(metrics_frame, "❌ Ошибки", "0", ModernStyles.COLORS['danger'], 1, 3)
        
        # Control buttons
        controls_frame = tk.Frame(overview_frame, bg=ModernStyles.COLORS['bg_main'])
        controls_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Button(controls_frame, text="🔄 Принудительная Синхронизация",
                 font=('Cambria', 10, 'bold'),
                 bg=ModernStyles.COLORS['primary'],
                 fg='white',
                 command=self.force_sync).pack(side='left', padx=5)
        
        tk.Button(controls_frame, text="⏸️ Приостановить Синхронизацию",
                 font=('Cambria', 10, 'bold'),
                 bg=ModernStyles.COLORS['warning'],
                 fg='white',
                 command=self.toggle_sync).pack(side='left', padx=5)
        
        tk.Button(controls_frame, text="🌐 Переключить Режим",
                 font=('Cambria', 10, 'bold'),
                 bg=ModernStyles.COLORS['info'],
                 fg='white',
                 command=self.toggle_online_mode).pack(side='left', padx=5)
        
        tk.Button(controls_frame, text="🔄 Обновить",
                 font=('Cambria', 10, 'bold'),
                 bg=ModernStyles.COLORS['secondary'],
                 fg='white',
                 command=self.refresh_data).pack(side='right', padx=5)
        
        # Recent activity
        activity_frame = tk.LabelFrame(overview_frame, text="Последняя Активность",
                                     font=('Cambria', 12, 'bold'),
                                     bg=ModernStyles.COLORS['bg_main'],
                                     fg=ModernStyles.COLORS['text_primary'])
        activity_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Activity tree view
        columns = ('Время', 'Таблица', 'Операция', 'Статус', 'Сообщение')
        self.tree_views['activity'] = ttk.Treeview(activity_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.tree_views['activity'].heading(col, text=col)
            self.tree_views['activity'].column(col, width=150)
        
        # Scrollbar for activity
        activity_scrollbar = ttk.Scrollbar(activity_frame, orient='vertical', 
                                         command=self.tree_views['activity'].set)
        self.tree_views['activity'].configure(yscrollcommand=activity_scrollbar.set)
        
        self.tree_views['activity'].pack(side='left', fill='both', expand=True, padx=10, pady=10)
        activity_scrollbar.pack(side='right', fill='y', pady=10)
    
    def create_status_card(self, parent, title, value, color, row, col):
        """Create a status card widget"""
        card_frame = tk.Frame(parent, bg=color, relief='raised', bd=2)
        card_frame.grid(row=row, column=col, padx=5, pady=5, sticky='ew')
        parent.grid_columnconfigure(col, weight=1)
        
        tk.Label(card_frame, text=title,
                font=('Cambria', 9, 'bold'),
                fg='white', bg=color).pack(pady=(5, 2))
        
        value_label = tk.Label(card_frame, text=value,
                              font=('Cambria', 12, 'bold'),
                              fg='white', bg=color)
        value_label.pack(pady=(0, 5))
        
        # Store reference for updating
        self.status_labels[title] = value_label
    
    def create_sync_log_tab(self, notebook):
        """Create sync log tab"""
        log_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(log_frame, text="📋 Журнал Синхронизации")
        
        # Filter controls
        filter_frame = tk.Frame(log_frame, bg=ModernStyles.COLORS['bg_main'])
        filter_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(filter_frame, text="Фильтр по статусу:",
                font=('Cambria', 10),
                bg=ModernStyles.COLORS['bg_main'],
                fg=ModernStyles.COLORS['text_primary']).pack(side='left', padx=5)
        
        self.status_filter = ttk.Combobox(filter_frame, values=['Все', 'Ожидает', 'Выполнено', 'Ошибка', 'Конфликт'])
        self.status_filter.set('Все')
        self.status_filter.pack(side='left', padx=5)
        
        tk.Button(filter_frame, text="Применить Фильтр",
                 font=('Cambria', 9),
                 bg=ModernStyles.COLORS['primary'],
                 fg='white',
                 command=self.apply_log_filter).pack(side='left', padx=5)
        
        # Sync log tree view
        log_columns = ('ID', 'Время', 'Таблица', 'Запись', 'Операция', 'Статус', 'Попытки', 'Ошибка')
        self.tree_views['sync_log'] = ttk.Treeview(log_frame, columns=log_columns, show='headings', height=20)
        
        for col in log_columns:
            self.tree_views['sync_log'].heading(col, text=col)
            if col == 'ID':
                self.tree_views['sync_log'].column(col, width=80)
            elif col == 'Ошибка':
                self.tree_views['sync_log'].column(col, width=200)
            else:
                self.tree_views['sync_log'].column(col, width=120)
        
        # Scrollbars
        log_v_scrollbar = ttk.Scrollbar(log_frame, orient='vertical', 
                                       command=self.tree_views['sync_log'].set)
        log_h_scrollbar = ttk.Scrollbar(log_frame, orient='horizontal',
                                       command=self.tree_views['sync_log'].set)
        
        self.tree_views['sync_log'].configure(yscrollcommand=log_v_scrollbar.set,
                                            xscrollcommand=log_h_scrollbar.set)
        
        self.tree_views['sync_log'].pack(side='left', fill='both', expand=True, padx=10, pady=10)
        log_v_scrollbar.pack(side='right', fill='y', pady=10)
        log_h_scrollbar.pack(side='bottom', fill='x', padx=10)
    
    def create_conflicts_tab(self, notebook):
        """Create conflicts resolution tab"""
        conflicts_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(conflicts_frame, text="⚠️ Конфликты")
        
        # Conflicts list
        conflicts_list_frame = tk.LabelFrame(conflicts_frame, text="Неразрешенные Конфликты",
                                           font=('Cambria', 12, 'bold'),
                                           bg=ModernStyles.COLORS['bg_main'],
                                           fg=ModernStyles.COLORS['text_primary'])
        conflicts_list_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Conflicts tree view
        conflict_columns = ('ID', 'Таблица', 'Запись', 'Локальное Время', 'Удаленное Время', 'Стратегия')
        self.tree_views['conflicts'] = ttk.Treeview(conflicts_list_frame, columns=conflict_columns, 
                                                  show='headings', height=15)
        
        for col in conflict_columns:
            self.tree_views['conflicts'].heading(col, text=col)
            self.tree_views['conflicts'].column(col, width=150)
        
        # Conflict details and resolution
        details_frame = tk.Frame(conflicts_list_frame, bg=ModernStyles.COLORS['bg_main'])
        details_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Button(details_frame, text="📋 Показать Детали",
                 font=('Cambria', 10, 'bold'),
                 bg=ModernStyles.COLORS['info'],
                 fg='white',
                 command=self.show_conflict_details).pack(side='left', padx=5)
        
        tk.Button(details_frame, text="✅ Разрешить Автоматически",
                 font=('Cambria', 10, 'bold'),
                 bg=ModernStyles.COLORS['success'],
                 fg='white',
                 command=self.auto_resolve_conflict).pack(side='left', padx=5)
        
        tk.Button(details_frame, text="🔧 Разрешить Вручную",
                 font=('Cambria', 10, 'bold'),
                 bg=ModernStyles.COLORS['warning'],
                 fg='white',
                 command=self.manual_resolve_conflict).pack(side='left', padx=5)
        
        # Scrollbar for conflicts
        conflicts_scrollbar = ttk.Scrollbar(conflicts_list_frame, orient='vertical',
                                          command=self.tree_views['conflicts'].set)
        self.tree_views['conflicts'].configure(yscrollcommand=conflicts_scrollbar.set)
        
        self.tree_views['conflicts'].pack(side='left', fill='both', expand=True, padx=10, pady=10)
        conflicts_scrollbar.pack(side='right', fill='y', pady=10)
    
    def create_settings_tab(self, notebook):
        """Create synchronization settings tab"""
        settings_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(settings_frame, text="⚙️ Настройки")
        
        # Sync configuration
        config_frame = tk.LabelFrame(settings_frame, text="Конфигурация Синхронизации",
                                   font=('Cambria', 12, 'bold'),
                                   bg=ModernStyles.COLORS['bg_main'],
                                   fg=ModernStyles.COLORS['text_primary'])
        config_frame.pack(fill='x', padx=10, pady=10)
        
        # Settings grid
        settings_grid = tk.Frame(config_frame, bg=ModernStyles.COLORS['bg_main'])
        settings_grid.pack(fill='x', padx=10, pady=10)
        
        # Sync interval
        tk.Label(settings_grid, text="Интервал синхронизации (сек):",
                font=('Cambria', 10),
                bg=ModernStyles.COLORS['bg_main'],
                fg=ModernStyles.COLORS['text_primary']).grid(row=0, column=0, sticky='w', padx=5, pady=5)
        
        self.sync_interval_var = tk.StringVar(value="30")
        tk.Entry(settings_grid, textvariable=self.sync_interval_var,
                font=('Cambria', 10), width=10).grid(row=0, column=1, padx=5, pady=5)
        
        # Batch size
        tk.Label(settings_grid, text="Размер пакета:",
                font=('Cambria', 10),
                bg=ModernStyles.COLORS['bg_main'],
                fg=ModernStyles.COLORS['text_primary']).grid(row=1, column=0, sticky='w', padx=5, pady=5)
        
        self.batch_size_var = tk.StringVar(value="100")
        tk.Entry(settings_grid, textvariable=self.batch_size_var,
                font=('Cambria', 10), width=10).grid(row=1, column=1, padx=5, pady=5)
        
        # Conflict resolution strategy
        tk.Label(settings_grid, text="Стратегия разрешения конфликтов:",
                font=('Cambria', 10),
                bg=ModernStyles.COLORS['bg_main'],
                fg=ModernStyles.COLORS['text_primary']).grid(row=2, column=0, sticky='w', padx=5, pady=5)
        
        self.conflict_strategy_var = tk.StringVar(value="latest_wins")
        strategy_combo = ttk.Combobox(settings_grid, textvariable=self.conflict_strategy_var,
                                    values=['latest_wins', 'manual', 'merge', 'source_wins'])
        strategy_combo.grid(row=2, column=1, padx=5, pady=5)
        
        # Apply settings button
        tk.Button(config_frame, text="💾 Применить Настройки",
                 font=('Cambria', 10, 'bold'),
                 bg=ModernStyles.COLORS['primary'],
                 fg='white',
                 command=self.apply_settings).pack(pady=10)
    
    def create_statistics_tab(self, notebook):
        """Create statistics tab"""
        stats_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(stats_frame, text="📈 Статистика")
        
        # Statistics display
        stats_display = tk.Text(stats_frame, font=('Cambria', 10),
                              bg='white', fg=ModernStyles.COLORS['text_primary'],
                              wrap='word', height=25)
        stats_display.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Store reference
        self.stats_display = stats_display

    def start_monitoring(self):
        """Start the monitoring thread"""
        if not self.monitoring_active:
            self.monitoring_active = True
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()

    def stop_monitoring(self):
        """Stop the monitoring thread"""
        self.monitoring_active = False

    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                if self.auto_refresh:
                    self.update_all_data()
                time.sleep(5)  # Update every 5 seconds
            except Exception as e:
                print(f"Monitoring error: {e}")
                time.sleep(10)

    def update_all_data(self):
        """Update all monitoring data"""
        if not self.window or not self.window.winfo_exists():
            self.stop_monitoring()
            return

        try:
            # Get sync status
            status = self.sync_manager.get_sync_status()

            # Update status cards
            self.update_status_cards(status)

            # Update activity log
            self.update_activity_log()

            # Update sync log
            self.update_sync_log()

            # Update conflicts
            self.update_conflicts()

            # Update statistics
            self.update_statistics(status)

        except Exception as e:
            print(f"Error updating monitoring data: {e}")

    def update_status_cards(self, status: Dict[str, Any]):
        """Update status cards"""
        try:
            # Online status
            online_status = "Онлайн" if status['is_online'] else "Офлайн"
            if "🌐 Статус" in self.status_labels:
                self.status_labels["🌐 Статус"].config(text=online_status)

            # Sync status
            sync_status = "Активна" if status['sync_active'] else "Неактивна"
            if "🔄 Синхронизация" in self.status_labels:
                self.status_labels["🔄 Синхронизация"].config(text=sync_status)

            # Last sync
            last_sync = status.get('last_sync', 'Никогда')
            if last_sync and last_sync != 'Никогда':
                try:
                    last_sync_dt = datetime.fromisoformat(last_sync)
                    last_sync = last_sync_dt.strftime('%d.%m.%Y %H:%M')
                except:
                    pass
            if "⏰ Последняя Синхронизация" in self.status_labels:
                self.status_labels["⏰ Последняя Синхронизация"].config(text=last_sync)

            # Pending changes
            pending = status.get('pending_changes', 0)
            if "📤 Ожидающие Отправки" in self.status_labels:
                self.status_labels["📤 Ожидающие Отправки"].config(text=str(pending))

            # Conflicts
            conflicts = status.get('unresolved_conflicts', 0)
            if "⚠️ Конфликты" in self.status_labels:
                self.status_labels["⚠️ Конфликты"].config(text=str(conflicts))

            # Statistics
            stats = status.get('statistics', {})
            synced = stats.get('total_synced', 0)
            if "✅ Синхронизировано" in self.status_labels:
                self.status_labels["✅ Синхронизировано"].config(text=str(synced))

            failed = stats.get('failed_syncs', 0)
            if "❌ Ошибки" in self.status_labels:
                self.status_labels["❌ Ошибки"].config(text=str(failed))

        except Exception as e:
            print(f"Error updating status cards: {e}")

    def update_activity_log(self):
        """Update recent activity log"""
        try:
            if 'activity' not in self.tree_views:
                return

            # Clear existing items
            for item in self.tree_views['activity'].get_children():
                self.tree_views['activity'].delete(item)

            # Get recent sync log entries
            recent_logs = self.sync_manager.connection_pool.execute_query(
                "SELECT * FROM sync_log ORDER BY timestamp DESC LIMIT 20"
            )

            for log in recent_logs:
                timestamp = datetime.fromisoformat(log['timestamp']).strftime('%H:%M:%S')
                status_text = log['status'].title()
                error_msg = log['error_message'] or ''

                self.tree_views['activity'].insert('', 'end', values=(
                    timestamp,
                    log['table_name'],
                    log['operation'],
                    status_text,
                    error_msg[:50] + '...' if len(error_msg) > 50 else error_msg
                ))

        except Exception as e:
            print(f"Error updating activity log: {e}")

    def update_sync_log(self):
        """Update sync log tab"""
        try:
            if 'sync_log' not in self.tree_views:
                return

            # Clear existing items
            for item in self.tree_views['sync_log'].get_children():
                self.tree_views['sync_log'].delete(item)

            # Get sync log entries
            logs = self.sync_manager.connection_pool.execute_query(
                "SELECT * FROM sync_log ORDER BY timestamp DESC LIMIT 100"
            )

            for log in logs:
                timestamp = datetime.fromisoformat(log['timestamp']).strftime('%d.%m.%Y %H:%M:%S')

                self.tree_views['sync_log'].insert('', 'end', values=(
                    log['id'][:8] + '...',
                    timestamp,
                    log['table_name'],
                    log['record_id'],
                    log['operation'],
                    log['status'].title(),
                    log['retry_count'],
                    log['error_message'] or ''
                ))

        except Exception as e:
            print(f"Error updating sync log: {e}")

    def update_conflicts(self):
        """Update conflicts tab"""
        try:
            if 'conflicts' not in self.tree_views:
                return

            # Clear existing items
            for item in self.tree_views['conflicts'].get_children():
                self.tree_views['conflicts'].delete(item)

            # Get pending conflicts
            conflicts = self.sync_manager.get_pending_conflicts()

            for conflict in conflicts:
                local_time = datetime.fromisoformat(conflict['local_timestamp']).strftime('%d.%m.%Y %H:%M')
                remote_time = datetime.fromisoformat(conflict['remote_timestamp']).strftime('%d.%m.%Y %H:%M')

                self.tree_views['conflicts'].insert('', 'end', values=(
                    conflict['id'][:8] + '...',
                    conflict['table_name'],
                    conflict['record_id'],
                    local_time,
                    remote_time,
                    conflict['resolution_strategy'].title()
                ))

        except Exception as e:
            print(f"Error updating conflicts: {e}")

    def update_statistics(self, status: Dict[str, Any]):
        """Update statistics display"""
        try:
            if not hasattr(self, 'stats_display'):
                return

            stats = status.get('statistics', {})

            stats_text = f"""
СТАТИСТИКА СИНХРОНИЗАЦИИ ДАННЫХ
═══════════════════════════════════════

Общая информация:
├─ ID локации: {status['location_id']}
├─ Статус подключения: {'Онлайн' if status['is_online'] else 'Офлайн'}
├─ Статус синхронизации: {'Активна' if status['sync_active'] else 'Неактивна'}
└─ Последняя синхронизация: {status.get('last_sync', 'Никогда')}

Статистика операций:
├─ Всего синхронизировано: {stats.get('total_synced', 0):,}
├─ Конфликтов разрешено: {stats.get('conflicts_resolved', 0):,}
├─ Неудачных синхронизаций: {stats.get('failed_syncs', 0):,}
└─ Среднее время синхронизации: {stats.get('sync_duration', 0):.2f} сек

Текущее состояние:
├─ Ожидающие отправки: {status.get('pending_changes', 0):,}
├─ Неразрешенные конфликты: {status.get('unresolved_conflicts', 0):,}
└─ Активные соединения: {self.sync_manager.connection_pool.get_statistics().get('active_connections', 0)}

Конфигурация:
├─ Интервал синхронизации: {self.sync_manager.sync_config.get('sync_interval', 30)} сек
├─ Размер пакета: {self.sync_manager.sync_config.get('batch_size', 100)}
├─ Максимум попыток: {self.sync_manager.sync_config.get('max_retries', 3)}
└─ Стратегия конфликтов: {self.sync_manager.sync_config.get('conflict_resolution', 'latest_wins').value}

Синхронизируемые таблицы:
{chr(10).join(['├─ ' + table for table in self.sync_manager.sync_config.get('sync_tables', [])])}

Последние события:
{self._get_recent_events()}
            """

            self.stats_display.delete(1.0, tk.END)
            self.stats_display.insert(1.0, stats_text.strip())

        except Exception as e:
            print(f"Error updating statistics: {e}")

    def _get_recent_events(self) -> str:
        """Get recent sync events for statistics"""
        try:
            events = self.sync_manager.connection_pool.execute_query(
                "SELECT operation, table_name, status, timestamp FROM sync_log ORDER BY timestamp DESC LIMIT 5"
            )

            if not events:
                return "Нет недавних событий"

            event_lines = []
            for event in events:
                timestamp = datetime.fromisoformat(event['timestamp']).strftime('%H:%M:%S')
                event_lines.append(f"├─ {timestamp}: {event['operation']} в {event['table_name']} - {event['status']}")

            return '\n'.join(event_lines)

        except Exception as e:
            return f"Ошибка получения событий: {e}"

    def force_sync(self):
        """Force immediate synchronization"""
        try:
            result = self.sync_manager.force_sync()
            if result['status'] == 'success':
                messagebox.showinfo("Синхронизация", "Принудительная синхронизация выполнена успешно!")
            else:
                messagebox.showerror("Ошибка", f"Ошибка синхронизации: {result['message']}")

            self.refresh_data()

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка принудительной синхронизации: {e}")

    def toggle_sync(self):
        """Toggle synchronization on/off"""
        try:
            if self.sync_manager.sync_active:
                self.sync_manager.stop_sync_service()
                messagebox.showinfo("Синхронизация", "Синхронизация приостановлена")
            else:
                self.sync_manager.start_sync_service()
                messagebox.showinfo("Синхронизация", "Синхронизация возобновлена")

            self.refresh_data()

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка переключения синхронизации: {e}")

    def toggle_online_mode(self):
        """Toggle online/offline mode"""
        try:
            new_status = not self.sync_manager.is_online
            self.sync_manager.set_online_status(new_status)

            mode = "онлайн" if new_status else "офлайн"
            messagebox.showinfo("Режим работы", f"Переключено в режим {mode}")

            self.refresh_data()

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка переключения режима: {e}")

    def refresh_data(self):
        """Manually refresh all data"""
        self.update_all_data()

    def apply_log_filter(self):
        """Apply filter to sync log"""
        # This would filter the sync log based on selected status
        self.update_sync_log()

    def show_conflict_details(self):
        """Show detailed conflict information"""
        selection = self.tree_views['conflicts'].selection()
        if not selection:
            messagebox.showwarning("Выбор", "Выберите конфликт для просмотра деталей")
            return

        # Get conflict details and show in a dialog
        messagebox.showinfo("Детали конфликта", "Функция в разработке")

    def auto_resolve_conflict(self):
        """Automatically resolve selected conflict"""
        selection = self.tree_views['conflicts'].selection()
        if not selection:
            messagebox.showwarning("Выбор", "Выберите конфликт для автоматического разрешения")
            return

        messagebox.showinfo("Разрешение конфликта", "Функция в разработке")

    def manual_resolve_conflict(self):
        """Manually resolve selected conflict"""
        selection = self.tree_views['conflicts'].selection()
        if not selection:
            messagebox.showwarning("Выбор", "Выберите конфликт для ручного разрешения")
            return

        messagebox.showinfo("Ручное разрешение", "Функция в разработке")

    def apply_settings(self):
        """Apply synchronization settings"""
        try:
            # Update sync configuration
            new_config = self.sync_manager.sync_config.copy()
            new_config['sync_interval'] = int(self.sync_interval_var.get())
            new_config['batch_size'] = int(self.batch_size_var.get())
            new_config['conflict_resolution'] = ConflictResolution(self.conflict_strategy_var.get())

            self.sync_manager.sync_config = new_config

            messagebox.showinfo("Настройки", "Настройки синхронизации применены успешно!")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка применения настроек: {e}")

    def destroy(self):
        """Destroy the window"""
        self.on_closing()

    def on_closing(self):
        """Handle window closing"""
        self.stop_monitoring()
        if self.window:
            self.window.destroy()
