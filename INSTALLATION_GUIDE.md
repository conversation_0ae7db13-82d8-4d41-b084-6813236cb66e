# Installation Guide - Restaurant Accounting & Inventory Management System

## System Requirements

### Minimum Requirements
- **Operating System**: Windows 10/11, macOS 10.14+, or Linux Ubuntu 18.04+
- **Python**: Version 3.8 or higher
- **RAM**: 4 GB minimum, 8 GB recommended
- **Storage**: 500 MB free space
- **Display**: 1280x720 minimum resolution

### Recommended Requirements
- **Python**: Version 3.9 or higher
- **RAM**: 8 GB or more
- **Storage**: 1 GB free space
- **Display**: 1920x1080 or higher resolution

## Installation Steps

### Step 1: Install Python

#### Windows
1. Download Python from [python.org](https://www.python.org/downloads/)
2. Run the installer and **check "Add Python to PATH"**
3. Verify installation by opening Command Prompt and typing:
   ```cmd
   python --version
   ```

#### macOS
1. Install using Homebrew (recommended):
   ```bash
   brew install python
   ```
   Or download from [python.org](https://www.python.org/downloads/)

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install python3 python3-pip python3-tk
```

### Step 2: Download the Application

1. Download the project files to your desired location
2. Extract if downloaded as a ZIP file
3. Open terminal/command prompt in the project directory

### Step 3: Install Dependencies

#### Automatic Installation (Recommended)
```bash
pip install -r requirements.txt
```

#### Manual Installation
If automatic installation fails, install packages individually:
```bash
pip install pandas>=1.5.0
pip install openpyxl>=3.0.0
pip install matplotlib>=3.5.0
pip install seaborn>=0.11.0
pip install pillow>=9.0.0
pip install ttkthemes>=3.2.0
pip install customtkinter>=5.0.0
pip install reportlab>=3.6.0
```

### Step 4: Verify Installation

Run the test script to verify everything is working:
```bash
python test_system.py
```

You should see:
```
============================================================
Restaurant Management System - Test Suite
============================================================
Testing database functionality...
✓ Database initialized successfully
✓ Raw material inserted with ID: 1
✓ Retrieved 1 raw materials
✓ Sales data inserted successfully
✓ Retrieved 1 sales records

Testing GUI imports...
✓ Styles module imported
✓ Main window module imported
✓ Sales import module imported

Testing CSV parsing...
✓ Sample CSV loaded with 15 rows and 17 columns
✓ CSV has sufficient columns for data extraction

============================================================
Test Results: 3/3 tests passed
🎉 All tests passed! System is ready to use.
============================================================
```

### Step 5: Launch the Application

#### Windows
- Double-click `start_restaurant_system.bat`
- Or run: `python main.py`

#### macOS/Linux
```bash
python main.py
```

## Troubleshooting

### Common Issues and Solutions

#### Issue: "Python is not recognized"
**Solution**: Python is not in your system PATH
- Windows: Reinstall Python and check "Add Python to PATH"
- macOS/Linux: Use full path to Python or add to PATH

#### Issue: "No module named 'pandas'"
**Solution**: Dependencies not installed
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

#### Issue: "Permission denied" on Linux/macOS
**Solution**: Use pip with --user flag
```bash
pip install --user -r requirements.txt
```

#### Issue: Tkinter not available on Linux
**Solution**: Install tkinter package
```bash
sudo apt install python3-tk
```

#### Issue: Application window doesn't appear
**Solution**: Check if running in virtual environment or try:
```bash
python -m tkinter
```

### Virtual Environment Setup (Optional but Recommended)

#### Create Virtual Environment
```bash
python -m venv restaurant_env
```

#### Activate Virtual Environment
**Windows:**
```cmd
restaurant_env\Scripts\activate
```

**macOS/Linux:**
```bash
source restaurant_env/bin/activate
```

#### Install Dependencies in Virtual Environment
```bash
pip install -r requirements.txt
```

#### Deactivate Virtual Environment
```bash
deactivate
```

## First Time Setup

### Default Login Credentials
- **Username**: admin
- **Password**: admin123

⚠️ **Important**: Change the default password after first login!

### Initial Configuration

1. **Launch the application**
2. **Login with default credentials**
3. **Import sample data** (optional):
   - Go to "Import Sales" section
   - Select `sample_sales_data.csv`
   - Review and save the data
4. **Add inventory items**:
   - Go to "Inventory" section
   - Add your raw materials and ingredients
5. **Configure settings**:
   - Go to "Settings" section
   - Update business information
   - Set currency and tax rates

## File Structure After Installation

```
Restaurant/
├── main.py                    # Application entry point
├── start_restaurant_system.bat # Windows startup script
├── test_system.py            # System test script
├── requirements.txt          # Python dependencies
├── README.md                 # Main documentation
├── INSTALLATION_GUIDE.md     # This file
├── sample_sales_data.csv     # Sample data for testing
├── restaurant_system.db      # SQLite database (created on first run)
├── config.json              # Configuration file (created on first run)
├── database/
│   ├── db_manager.py         # Database operations
│   └── models.py             # Database schema
├── gui/
│   ├── main_window.py        # Main GUI window
│   └── styles.py             # UI styling
├── modules/
│   ├── sales_import.py       # Sales data import
│   ├── inventory.py          # Inventory management
│   ├── reports.py            # Reports and analytics
│   └── purchasing.py         # Purchase orders
└── utils/
    └── helpers.py            # Utility functions
```

## Performance Optimization

### For Better Performance
1. **Close unnecessary applications** while running the system
2. **Use SSD storage** for better database performance
3. **Increase virtual memory** if working with large datasets
4. **Regular database maintenance** (backup and optimize)

### Memory Usage
- **Typical usage**: 100-200 MB RAM
- **With large datasets**: 300-500 MB RAM
- **During report generation**: Up to 1 GB RAM

## Security Considerations

### Database Security
- Database file is stored locally
- No network access required
- Regular backups recommended

### User Access
- Change default admin password
- Create separate user accounts for different staff levels
- Regular password updates recommended

## Backup and Recovery

### Automatic Backups
The system creates automatic backups of:
- Database file (`restaurant_system.db`)
- Configuration file (`config.json`)

### Manual Backup
Copy these files to a safe location:
- `restaurant_system.db`
- `config.json`
- Any exported reports

### Recovery
To restore from backup:
1. Close the application
2. Replace database and config files
3. Restart the application

## Getting Help

### Documentation
- Read the main `README.md` file
- Check code comments for technical details
- Review sample data format

### Support Channels
1. Check the troubleshooting section above
2. Review error messages carefully
3. Test with sample data first
4. Contact system administrator

### Reporting Issues
When reporting issues, include:
- Operating system and version
- Python version
- Error messages (full text)
- Steps to reproduce the problem
- Screenshots if applicable

---

**Installation Guide Version**: 1.0.0  
**Last Updated**: January 2024  
**Compatible with**: Restaurant Management System v1.0.0
