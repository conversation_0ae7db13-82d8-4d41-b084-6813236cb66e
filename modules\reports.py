"""
Reports and Analytics Module for Restaurant Management System
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import pandas as pd
from gui.styles import ModernStyles
from utils.helpers import DateHelper, NumberHelper, FileHelper

class ReportsManager:
    """Reports and analytics functionality"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
    
    def create_reports_window(self):
        """Create reports management window"""
        window = tk.Toplevel(self.parent)
        window.title("Reports & Analytics")
        window.geometry("1400x900")
        window.configure(bg=ModernStyles.COLORS['bg_main'])
        
        # Main container
        main_frame = tk.Frame(window, **ModernStyles.WIDGET_STYLES['frame_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Title
        title_label = tk.Label(main_frame, text="Reports & Analytics",
                              **ModernStyles.WIDGET_STYLES['label_title'])
        title_label.pack(anchor='w', pady=(0, 20))
        
        # Create notebook for different report types
        notebook = ttk.Notebook(main_frame, style="Modern.TNotebook")
        notebook.pack(fill='both', expand=True)
        
        # Sales Reports Tab
        sales_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(sales_frame, text="Sales Reports")
        self.create_sales_reports_tab(sales_frame)
        
        # Inventory Reports Tab
        inventory_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(inventory_frame, text="Inventory Reports")
        self.create_inventory_reports_tab(inventory_frame)
        
        # Financial Reports Tab
        financial_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(financial_frame, text="Financial Reports")
        self.create_financial_reports_tab(financial_frame)
        
        return window
    
    def create_sales_reports_tab(self, parent):
        """Create sales reports tab"""
        # Controls frame
        controls_frame = ModernStyles.create_card_frame(parent)
        controls_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(controls_frame, text="Sales Report Parameters",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Date range selection
        date_frame = tk.Frame(controls_frame, bg=ModernStyles.COLORS['bg_card'])
        date_frame.pack(fill='x', pady=(0, 10))
        
        tk.Label(date_frame, text="Date Range:",
                **ModernStyles.WIDGET_STYLES['label_body']).pack(side='left')
        
        self.sales_start_date = tk.StringVar(value=(datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
        self.sales_end_date = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
        
        tk.Entry(date_frame, textvariable=self.sales_start_date, width=12,
                **ModernStyles.WIDGET_STYLES['entry']).pack(side='left', padx=(10, 5))
        
        tk.Label(date_frame, text="to", bg=ModernStyles.COLORS['bg_card']).pack(side='left', padx=5)
        
        tk.Entry(date_frame, textvariable=self.sales_end_date, width=12,
                **ModernStyles.WIDGET_STYLES['entry']).pack(side='left', padx=(5, 10))
        
        # Report buttons
        btn_frame = tk.Frame(controls_frame, bg=ModernStyles.COLORS['bg_card'])
        btn_frame.pack(fill='x', pady=(10, 0))
        
        tk.Button(btn_frame, text="Generate Sales Summary",
                 command=self.generate_sales_summary,
                 **ModernStyles.WIDGET_STYLES['button_primary']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Daily Sales Chart",
                 command=self.show_daily_sales_chart,
                 **ModernStyles.WIDGET_STYLES['button_secondary']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Export to Excel",
                 command=self.export_sales_report,
                 **ModernStyles.WIDGET_STYLES['button_success']).pack(side='left')
        
        # Results frame
        self.sales_results_frame = ModernStyles.create_card_frame(parent)
        self.sales_results_frame.pack(fill='both', expand=True)
        
        # Initial placeholder
        tk.Label(self.sales_results_frame, text="Select report parameters and click Generate to view results",
                font=ModernStyles.FONTS['body'],
                fg=ModernStyles.COLORS['text_secondary'],
                bg=ModernStyles.COLORS['bg_card']).pack(expand=True)
    
    def create_inventory_reports_tab(self, parent):
        """Create inventory reports tab"""
        # Controls frame
        controls_frame = ModernStyles.create_card_frame(parent)
        controls_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(controls_frame, text="Inventory Report Options",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Report buttons
        btn_frame = tk.Frame(controls_frame, bg=ModernStyles.COLORS['bg_card'])
        btn_frame.pack(fill='x')
        
        tk.Button(btn_frame, text="Current Stock Levels",
                 command=self.show_stock_levels,
                 **ModernStyles.WIDGET_STYLES['button_primary']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Low Stock Alert",
                 command=self.show_low_stock_alert,
                 **ModernStyles.WIDGET_STYLES['button_warning']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Stock Movement",
                 command=self.show_stock_movement,
                 **ModernStyles.WIDGET_STYLES['button_secondary']).pack(side='left')
        
        # Results frame
        self.inventory_results_frame = ModernStyles.create_card_frame(parent)
        self.inventory_results_frame.pack(fill='both', expand=True)
        
        # Initial placeholder
        tk.Label(self.inventory_results_frame, text="Select a report type to view inventory analysis",
                font=ModernStyles.FONTS['body'],
                fg=ModernStyles.COLORS['text_secondary'],
                bg=ModernStyles.COLORS['bg_card']).pack(expand=True)
    
    def create_financial_reports_tab(self, parent):
        """Create financial reports tab"""
        # Controls frame
        controls_frame = ModernStyles.create_card_frame(parent)
        controls_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(controls_frame, text="Financial Analysis",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Report buttons
        btn_frame = tk.Frame(controls_frame, bg=ModernStyles.COLORS['bg_card'])
        btn_frame.pack(fill='x')
        
        tk.Button(btn_frame, text="Revenue Analysis",
                 command=self.show_revenue_analysis,
                 **ModernStyles.WIDGET_STYLES['button_primary']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Cost Analysis",
                 command=self.show_cost_analysis,
                 **ModernStyles.WIDGET_STYLES['button_secondary']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Profit Margins",
                 command=self.show_profit_margins,
                 **ModernStyles.WIDGET_STYLES['button_success']).pack(side='left')
        
        # Results frame
        self.financial_results_frame = ModernStyles.create_card_frame(parent)
        self.financial_results_frame.pack(fill='both', expand=True)
        
        # Initial placeholder
        tk.Label(self.financial_results_frame, text="Select a financial report to view analysis",
                font=ModernStyles.FONTS['body'],
                fg=ModernStyles.COLORS['text_secondary'],
                bg=ModernStyles.COLORS['bg_card']).pack(expand=True)
    
    def clear_results_frame(self, frame):
        """Clear results frame"""
        for widget in frame.winfo_children():
            widget.destroy()
    
    def generate_sales_summary(self):
        """Generate sales summary report"""
        try:
            start_date = self.sales_start_date.get()
            end_date = self.sales_end_date.get()
            
            # Get sales data
            sales_data = self.db_manager.get_sales_data(start_date, end_date)
            
            if not sales_data:
                messagebox.showinfo("No Data", "No sales data found for the selected date range.")
                return
            
            # Clear previous results
            self.clear_results_frame(self.sales_results_frame)
            
            # Create summary statistics
            df = pd.DataFrame(sales_data)
            
            # Summary stats
            total_sales = df['total_amount'].sum()
            total_orders = len(df['order_number'].unique())
            avg_order_value = total_sales / total_orders if total_orders > 0 else 0
            
            # Create summary display
            summary_frame = tk.Frame(self.sales_results_frame, bg=ModernStyles.COLORS['bg_card'])
            summary_frame.pack(fill='x', pady=(0, 20))
            
            # Stats cards
            stats_frame = tk.Frame(summary_frame, bg=ModernStyles.COLORS['bg_card'])
            stats_frame.pack(fill='x', pady=10)
            
            self.create_stat_display(stats_frame, "Total Sales", NumberHelper.format_currency(total_sales))
            self.create_stat_display(stats_frame, "Total Orders", str(total_orders))
            self.create_stat_display(stats_frame, "Avg Order Value", NumberHelper.format_currency(avg_order_value))
            
            # Top dishes
            top_dishes = df.groupby('dish_name')['quantity'].sum().sort_values(ascending=False).head(5)
            
            top_dishes_frame = tk.Frame(self.sales_results_frame, bg=ModernStyles.COLORS['bg_card'])
            top_dishes_frame.pack(fill='x', pady=(0, 10))
            
            tk.Label(top_dishes_frame, text="Top 5 Dishes by Quantity",
                    **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 10))
            
            for dish, qty in top_dishes.items():
                dish_frame = tk.Frame(top_dishes_frame, bg=ModernStyles.COLORS['bg_card'])
                dish_frame.pack(fill='x', pady=2)
                
                tk.Label(dish_frame, text=f"• {dish}",
                        font=ModernStyles.FONTS['body'],
                        bg=ModernStyles.COLORS['bg_card']).pack(side='left')
                
                tk.Label(dish_frame, text=f"{qty:.0f} units",
                        font=ModernStyles.FONTS['body_bold'],
                        fg=ModernStyles.COLORS['secondary'],
                        bg=ModernStyles.COLORS['bg_card']).pack(side='right')
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate sales summary: {str(e)}")
    
    def create_stat_display(self, parent, label, value):
        """Create a statistic display widget"""
        stat_frame = tk.Frame(parent, bg=ModernStyles.COLORS['secondary'], relief='flat', bd=0)
        stat_frame.pack(side='left', fill='both', expand=True, padx=5)
        
        inner_frame = tk.Frame(stat_frame, bg=ModernStyles.COLORS['secondary'])
        inner_frame.pack(fill='both', expand=True, padx=15, pady=15)
        
        value_label = tk.Label(inner_frame, text=value,
                              font=ModernStyles.FONTS['heading'],
                              fg=ModernStyles.COLORS['text_white'],
                              bg=ModernStyles.COLORS['secondary'])
        value_label.pack()
        
        label_label = tk.Label(inner_frame, text=label,
                              font=ModernStyles.FONTS['body'],
                              fg=ModernStyles.COLORS['text_white'],
                              bg=ModernStyles.COLORS['secondary'])
        label_label.pack()
    
    def show_daily_sales_chart(self):
        """Show daily sales chart"""
        try:
            start_date = self.sales_start_date.get()
            end_date = self.sales_end_date.get()
            
            sales_data = self.db_manager.get_sales_data(start_date, end_date)
            
            if not sales_data:
                messagebox.showinfo("No Data", "No sales data found for the selected date range.")
                return
            
            # Clear previous results
            self.clear_results_frame(self.sales_results_frame)
            
            # Create chart
            df = pd.DataFrame(sales_data)
            daily_sales = df.groupby('order_date')['total_amount'].sum()
            
            # Create matplotlib figure
            fig, ax = plt.subplots(figsize=(10, 6))
            daily_sales.plot(kind='line', ax=ax, marker='o', linewidth=2, markersize=6)
            
            ax.set_title('Daily Sales Trend', fontsize=16, fontweight='bold')
            ax.set_xlabel('Date', fontsize=12)
            ax.set_ylabel('Sales Amount ($)', fontsize=12)
            ax.grid(True, alpha=0.3)
            
            # Format y-axis as currency
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
            
            # Embed chart in tkinter
            canvas = FigureCanvasTkAgg(fig, self.sales_results_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to create chart: {str(e)}")
    
    def export_sales_report(self):
        """Export sales report to Excel"""
        try:
            start_date = self.sales_start_date.get()
            end_date = self.sales_end_date.get()
            
            sales_data = self.db_manager.get_sales_data(start_date, end_date)
            
            if not sales_data:
                messagebox.showinfo("No Data", "No sales data found for export.")
                return
            
            # Ask for save location
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("CSV files", "*.csv")],
                title="Save Sales Report"
            )
            
            if file_path:
                df = pd.DataFrame(sales_data)
                
                if file_path.endswith('.xlsx'):
                    df.to_excel(file_path, index=False)
                else:
                    df.to_csv(file_path, index=False)
                
                messagebox.showinfo("Success", f"Sales report exported to {file_path}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export report: {str(e)}")
    
    def show_stock_levels(self):
        """Show current stock levels"""
        try:
            # Clear previous results
            self.clear_results_frame(self.inventory_results_frame)
            
            # Get inventory data
            inventory_data = self.db_manager.get_raw_materials()
            
            if not inventory_data:
                tk.Label(self.inventory_results_frame, text="No inventory data available",
                        font=ModernStyles.FONTS['body'],
                        fg=ModernStyles.COLORS['text_secondary'],
                        bg=ModernStyles.COLORS['bg_card']).pack(expand=True)
                return
            
            # Create treeview for stock levels
            columns = ('Item', 'Current Stock', 'Unit', 'Min Stock', 'Status')
            tree = ttk.Treeview(self.inventory_results_frame, columns=columns, show='headings',
                               style="Modern.Treeview")
            
            for col in columns:
                tree.heading(col, text=col)
                tree.column(col, width=120)
            
            # Add data
            for item in inventory_data:
                current = item['current_stock']
                minimum = item['minimum_stock']
                
                if current <= minimum:
                    status = "LOW STOCK"
                    tags = ['low_stock']
                elif current <= minimum * 1.5:
                    status = "MEDIUM"
                    tags = ['medium_stock']
                else:
                    status = "OK"
                    tags = ['ok_stock']
                
                tree.insert('', 'end', values=(
                    item['name'],
                    f"{current:.2f}",
                    item['unit_of_measure'],
                    f"{minimum:.2f}",
                    status
                ), tags=tags)
            
            # Configure tags
            tree.tag_configure('low_stock', background='#ffebee', foreground='#c62828')
            tree.tag_configure('medium_stock', background='#fff3e0', foreground='#ef6c00')
            tree.tag_configure('ok_stock', background='#e8f5e8', foreground='#2e7d32')
            
            # Add scrollbar
            scrollbar = ttk.Scrollbar(self.inventory_results_frame, orient='vertical', command=tree.yview)
            tree.configure(yscrollcommand=scrollbar.set)
            
            tree.pack(side='left', fill='both', expand=True)
            scrollbar.pack(side='right', fill='y')
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to show stock levels: {str(e)}")
    
    def show_low_stock_alert(self):
        """Show low stock alert"""
        try:
            # Clear previous results
            self.clear_results_frame(self.inventory_results_frame)

            # Get inventory data
            inventory_data = self.db_manager.get_raw_materials()

            if not inventory_data:
                tk.Label(self.inventory_results_frame, text="No inventory data available",
                        font=ModernStyles.FONTS['body'],
                        fg=ModernStyles.COLORS['text_secondary'],
                        bg=ModernStyles.COLORS['bg_card']).pack(expand=True)
                return

            # Filter low stock items
            low_stock_items = [item for item in inventory_data
                             if item['current_stock'] <= item['minimum_stock']]

            if not low_stock_items:
                tk.Label(self.inventory_results_frame, text="✅ Все товары в достаточном количестве",
                        font=ModernStyles.FONTS['heading'],
                        fg=ModernStyles.COLORS['success'],
                        bg=ModernStyles.COLORS['bg_card']).pack(expand=True)
                return

            # Create alert display
            alert_frame = tk.Frame(self.inventory_results_frame, bg=ModernStyles.COLORS['bg_card'])
            alert_frame.pack(fill='both', expand=True, padx=10, pady=10)

            tk.Label(alert_frame, text="⚠️ ВНИМАНИЕ: Товары с низким остатком",
                    font=ModernStyles.FONTS['heading'],
                    fg=ModernStyles.COLORS['danger'],
                    bg=ModernStyles.COLORS['bg_card']).pack(pady=10)

            # Create treeview for low stock items
            columns = ('Товар', 'Текущий остаток', 'Минимум', 'Нужно заказать')
            tree = ttk.Treeview(alert_frame, columns=columns, show='headings', style="Modern.Treeview")

            for col in columns:
                tree.heading(col, text=col)
                tree.column(col, width=150)

            for item in low_stock_items:
                need_to_order = max(0, item['minimum_stock'] * 2 - item['current_stock'])
                tree.insert('', 'end', values=(
                    item['name'],
                    f"{item['current_stock']:.1f} {item['unit_of_measure']}",
                    f"{item['minimum_stock']:.1f} {item['unit_of_measure']}",
                    f"{need_to_order:.1f} {item['unit_of_measure']}"
                ), tags=['low_stock'])

            tree.tag_configure('low_stock', background='#ffebee', foreground='#c62828')
            tree.pack(fill='both', expand=True, pady=10)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to show low stock alert: {str(e)}")

    def show_stock_movement(self):
        """Show stock movement report"""
        try:
            # Clear previous results
            self.clear_results_frame(self.inventory_results_frame)

            # Create stock movement display
            movement_frame = tk.Frame(self.inventory_results_frame, bg=ModernStyles.COLORS['bg_card'])
            movement_frame.pack(fill='both', expand=True, padx=10, pady=10)

            tk.Label(movement_frame, text="📦 Движение Товаров за Последние 7 дней",
                    font=ModernStyles.FONTS['heading'],
                    bg=ModernStyles.COLORS['bg_card']).pack(pady=10)

            # Sample stock movement data
            movements = [
                {"date": "2024-01-15", "item": "Мука пшеничная", "type": "Поступление", "quantity": 50, "unit": "кг"},
                {"date": "2024-01-15", "item": "Мука пшеничная", "type": "Расход", "quantity": -15, "unit": "кг"},
                {"date": "2024-01-14", "item": "Молоко 3.2%", "type": "Поступление", "quantity": 20, "unit": "л"},
                {"date": "2024-01-14", "item": "Говядина", "type": "Поступление", "quantity": 25, "unit": "кг"},
                {"date": "2024-01-13", "item": "Картофель", "type": "Расход", "quantity": -30, "unit": "кг"},
                {"date": "2024-01-12", "item": "Лук репчатый", "type": "Расход", "quantity": -10, "unit": "кг"},
            ]

            # Create treeview for movements
            columns = ('Дата', 'Товар', 'Тип операции', 'Количество', 'Единица')
            tree = ttk.Treeview(movement_frame, columns=columns, show='headings', style="Modern.Treeview")

            for col in columns:
                tree.heading(col, text=col)
                tree.column(col, width=120)

            for movement in movements:
                operation_type = "📥 Поступление" if movement['quantity'] > 0 else "📤 Расход"
                tree.insert('', 'end', values=(
                    movement['date'],
                    movement['item'],
                    operation_type,
                    f"{abs(movement['quantity'])}",
                    movement['unit']
                ))

            tree.pack(fill='both', expand=True, pady=10)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to show stock movement: {str(e)}")

    def show_revenue_analysis(self):
        """Show revenue analysis"""
        try:
            # Clear previous results
            self.clear_results_frame(self.financial_results_frame)

            # Create revenue analysis display
            revenue_frame = tk.Frame(self.financial_results_frame, bg=ModernStyles.COLORS['bg_card'])
            revenue_frame.pack(fill='both', expand=True, padx=10, pady=10)

            tk.Label(revenue_frame, text="💰 Анализ Выручки",
                    font=ModernStyles.FONTS['heading'],
                    bg=ModernStyles.COLORS['bg_card']).pack(pady=10)

            # Revenue statistics
            stats_frame = tk.Frame(revenue_frame, bg=ModernStyles.COLORS['bg_card'])
            stats_frame.pack(fill='x', pady=10)

            # Sample revenue data
            revenue_stats = {
                "Сегодня": 45600,
                "Вчера": 52300,
                "Эта неделя": 285000,
                "Прошлая неделя": 268000,
                "Этот месяц": 1250000,
                "Прошлый месяц": 1180000
            }

            row = 0
            for period, amount in revenue_stats.items():
                period_frame = tk.Frame(stats_frame, bg=ModernStyles.COLORS['bg_card'])
                period_frame.grid(row=row//2, column=row%2, sticky='ew', padx=10, pady=5)

                tk.Label(period_frame, text=f"{period}:",
                        font=ModernStyles.FONTS['body_bold'],
                        bg=ModernStyles.COLORS['bg_card']).pack(side='left')

                tk.Label(period_frame, text=f"{amount:,}₽",
                        font=ModernStyles.FONTS['body'],
                        fg=ModernStyles.COLORS['success'],
                        bg=ModernStyles.COLORS['bg_card']).pack(side='right')

                row += 1

            stats_frame.grid_columnconfigure(0, weight=1)
            stats_frame.grid_columnconfigure(1, weight=1)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to show revenue analysis: {str(e)}")

    def show_cost_analysis(self):
        """Show cost analysis"""
        try:
            # Clear previous results
            self.clear_results_frame(self.financial_results_frame)

            # Create cost analysis display
            cost_frame = tk.Frame(self.financial_results_frame, bg=ModernStyles.COLORS['bg_card'])
            cost_frame.pack(fill='both', expand=True, padx=10, pady=10)

            tk.Label(cost_frame, text="📊 Анализ Затрат",
                    font=ModernStyles.FONTS['heading'],
                    bg=ModernStyles.COLORS['bg_card']).pack(pady=10)

            # Cost breakdown
            cost_categories = [
                {"category": "Продукты", "amount": 180000, "percentage": 38},
                {"category": "Зарплата", "amount": 150000, "percentage": 32},
                {"category": "Аренда", "amount": 80000, "percentage": 17},
                {"category": "Коммунальные", "amount": 35000, "percentage": 7},
                {"category": "Прочие", "amount": 25000, "percentage": 6}
            ]

            # Create cost breakdown table
            columns = ('Категория', 'Сумма', 'Доля (%)')
            tree = ttk.Treeview(cost_frame, columns=columns, show='headings', style="Modern.Treeview")

            for col in columns:
                tree.heading(col, text=col)
                tree.column(col, width=150)

            total_costs = sum(cat['amount'] for cat in cost_categories)

            for category in cost_categories:
                tree.insert('', 'end', values=(
                    category['category'],
                    f"{category['amount']:,}₽",
                    f"{category['percentage']}%"
                ))

            # Add total row
            tree.insert('', 'end', values=(
                "ИТОГО",
                f"{total_costs:,}₽",
                "100%"
            ), tags=['total'])

            tree.tag_configure('total', background='#e3f2fd', font=('Arial', 10, 'bold'))
            tree.pack(fill='both', expand=True, pady=10)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to show cost analysis: {str(e)}")

    def show_profit_margins(self):
        """Show profit margins analysis"""
        try:
            # Clear previous results
            self.clear_results_frame(self.financial_results_frame)

            # Create profit margins display
            margin_frame = tk.Frame(self.financial_results_frame, bg=ModernStyles.COLORS['bg_card'])
            margin_frame.pack(fill='both', expand=True, padx=10, pady=10)

            tk.Label(margin_frame, text="📈 Анализ Маржинальности",
                    font=ModernStyles.FONTS['heading'],
                    bg=ModernStyles.COLORS['bg_card']).pack(pady=10)

            # Profit margin data by category
            margin_data = [
                {"category": "Горячие блюда", "revenue": 320000, "cost": 180000, "margin": 43.8},
                {"category": "Супы", "revenue": 180000, "cost": 85000, "margin": 52.8},
                {"category": "Салаты", "revenue": 150000, "cost": 75000, "margin": 50.0},
                {"category": "Напитки", "revenue": 95000, "cost": 35000, "margin": 63.2},
                {"category": "Десерты", "revenue": 75000, "cost": 30000, "margin": 60.0}
            ]

            # Create margin analysis table
            columns = ('Категория', 'Выручка', 'Себестоимость', 'Прибыль', 'Маржа %')
            tree = ttk.Treeview(margin_frame, columns=columns, show='headings', style="Modern.Treeview")

            for col in columns:
                tree.heading(col, text=col)
                tree.column(col, width=120)

            for item in margin_data:
                profit = item['revenue'] - item['cost']

                # Color coding based on margin
                if item['margin'] >= 50:
                    tags = ['high_margin']
                elif item['margin'] >= 40:
                    tags = ['medium_margin']
                else:
                    tags = ['low_margin']

                tree.insert('', 'end', values=(
                    item['category'],
                    f"{item['revenue']:,}₽",
                    f"{item['cost']:,}₽",
                    f"{profit:,}₽",
                    f"{item['margin']:.1f}%"
                ), tags=tags)

            # Configure tags
            tree.tag_configure('high_margin', background='#e8f5e8', foreground='#2e7d32')
            tree.tag_configure('medium_margin', background='#fff3e0', foreground='#ef6c00')
            tree.tag_configure('low_margin', background='#ffebee', foreground='#c62828')

            tree.pack(fill='both', expand=True, pady=10)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to show profit margins: {str(e)}")
