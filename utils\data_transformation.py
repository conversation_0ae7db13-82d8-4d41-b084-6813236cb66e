"""
Data Transformation and Validation Utilities for Restaurant Management System
Provides data mapping, validation, and transformation capabilities for export/import operations
"""

import re
from datetime import datetime, date
from typing import Dict, List, Any, Optional, Union, Tuple
from decimal import Decimal, InvalidOperation
from utils.error_handling import log_info


class DataValidator:
    """Data validation utilities"""
    
    @staticmethod
    def validate_date(date_str: str, formats: List[str] = None) -> Tuple[bool, Optional[datetime]]:
        """Validate and parse date string"""
        if formats is None:
            formats = ['%d.%m.%Y', '%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y']
        
        if not date_str or not isinstance(date_str, str):
            return False, None
        
        date_str = date_str.strip()
        
        for fmt in formats:
            try:
                parsed_date = datetime.strptime(date_str, fmt)
                return True, parsed_date
            except ValueError:
                continue
        
        return False, None
    
    @staticmethod
    def validate_number(value: Any, allow_negative: bool = True) -> Tuple[bool, Optional[Decimal]]:
        """Validate and parse numeric value"""
        if value is None:
            return False, None
        
        try:
            # Handle different input types
            if isinstance(value, (int, float)):
                decimal_value = Decimal(str(value))
            elif isinstance(value, str):
                # Clean string - remove spaces, replace comma with dot
                cleaned = value.strip().replace(' ', '').replace(',', '.')
                if not cleaned:
                    return False, None
                decimal_value = Decimal(cleaned)
            else:
                return False, None
            
            # Check negative values
            if not allow_negative and decimal_value < 0:
                return False, None
            
            return True, decimal_value
            
        except (InvalidOperation, ValueError):
            return False, None
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """Validate email address"""
        if not email or not isinstance(email, str):
            return False
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email.strip()))
    
    @staticmethod
    def validate_phone(phone: str) -> Tuple[bool, Optional[str]]:
        """Validate and normalize phone number"""
        if not phone or not isinstance(phone, str):
            return False, None
        
        # Remove all non-digit characters
        digits = re.sub(r'\D', '', phone)
        
        # Check length and format
        if len(digits) == 11 and digits.startswith('8'):
            # Russian format starting with 8
            normalized = f"+7{digits[1:]}"
            return True, normalized
        elif len(digits) == 11 and digits.startswith('7'):
            # Russian format starting with 7
            normalized = f"+{digits}"
            return True, normalized
        elif len(digits) == 10:
            # 10 digits - assume Russian mobile
            normalized = f"+7{digits}"
            return True, normalized
        elif len(digits) >= 7:
            # Other formats - keep as is with +
            normalized = f"+{digits}"
            return True, normalized
        
        return False, None
    
    @staticmethod
    def validate_required_fields(record: Dict[str, Any], required_fields: List[str]) -> List[str]:
        """Validate required fields in record"""
        errors = []
        
        for field in required_fields:
            if field not in record or record[field] is None or str(record[field]).strip() == '':
                errors.append(f"Отсутствует обязательное поле: {field}")
        
        return errors


class DataTransformer:
    """Data transformation utilities"""
    
    @staticmethod
    def format_currency(amount: Union[int, float, Decimal, str], currency: str = 'руб') -> str:
        """Format currency according to Russian standards"""
        try:
            if isinstance(amount, str):
                # Parse string amount
                is_valid, decimal_amount = DataValidator.validate_number(amount)
                if not is_valid:
                    return f"0,00 {currency}"
                amount = decimal_amount
            
            # Convert to Decimal for precise formatting
            if not isinstance(amount, Decimal):
                amount = Decimal(str(amount))
            
            # Format with Russian number formatting (space as thousand separator, comma as decimal)
            formatted = f"{amount:,.2f}".replace(',', ' ').replace('.', ',')
            
            # Handle the decimal separator properly
            if ',' not in formatted:
                formatted += ',00'
            
            return f"{formatted} {currency}"
            
        except Exception:
            return f"0,00 {currency}"
    
    @staticmethod
    def format_date(date_obj: Union[datetime, date, str], format_str: str = '%d.%m.%Y') -> str:
        """Format date according to specified format"""
        try:
            if isinstance(date_obj, str):
                # Parse string date first
                is_valid, parsed_date = DataValidator.validate_date(date_obj)
                if not is_valid:
                    return ""
                date_obj = parsed_date
            
            if isinstance(date_obj, datetime):
                return date_obj.strftime(format_str)
            elif isinstance(date_obj, date):
                return date_obj.strftime(format_str)
            
            return ""
            
        except Exception:
            return ""
    
    @staticmethod
    def normalize_text(text: str, max_length: Optional[int] = None) -> str:
        """Normalize text - trim, clean, limit length"""
        if not text or not isinstance(text, str):
            return ""
        
        # Trim whitespace and normalize spaces
        normalized = ' '.join(text.strip().split())
        
        # Limit length if specified
        if max_length and len(normalized) > max_length:
            normalized = normalized[:max_length].strip()
        
        return normalized
    
    @staticmethod
    def convert_boolean(value: Any) -> Optional[bool]:
        """Convert various representations to boolean"""
        if isinstance(value, bool):
            return value
        
        if isinstance(value, str):
            value = value.strip().lower()
            if value in ['true', '1', 'да', 'yes', 'y', '+']:
                return True
            elif value in ['false', '0', 'нет', 'no', 'n', '-']:
                return False
        
        if isinstance(value, (int, float)):
            return bool(value)
        
        return None


class DataMapper:
    """Data field mapping utilities"""
    
    def __init__(self):
        # Define field mappings for different tables
        self.table_mappings = {
            'sales': {
                'required_fields': ['order_date', 'total_amount'],
                'field_mappings': {
                    'дата': 'order_date',
                    'дата_заказа': 'order_date',
                    'date': 'order_date',
                    'сумма': 'total_amount',
                    'общая_сумма': 'total_amount',
                    'amount': 'total_amount',
                    'total': 'total_amount',
                    'способ_оплаты': 'payment_method',
                    'payment': 'payment_method',
                    'отдел': 'department',
                    'department': 'department'
                },
                'transformations': {
                    'order_date': lambda x: DataTransformer.format_date(x),
                    'total_amount': lambda x: DataValidator.validate_number(x)[1]
                }
            },
            'raw_materials': {
                'required_fields': ['name', 'unit'],
                'field_mappings': {
                    'название': 'name',
                    'наименование': 'name',
                    'name': 'name',
                    'единица': 'unit',
                    'ед_изм': 'unit',
                    'unit': 'unit',
                    'цена': 'price',
                    'стоимость': 'price',
                    'price': 'price'
                },
                'transformations': {
                    'name': lambda x: DataTransformer.normalize_text(x, 200),
                    'price': lambda x: DataValidator.validate_number(x, allow_negative=False)[1]
                }
            },
            'customers': {
                'required_fields': ['name'],
                'field_mappings': {
                    'имя': 'name',
                    'название': 'name',
                    'name': 'name',
                    'телефон': 'phone',
                    'phone': 'phone',
                    'email': 'email',
                    'почта': 'email',
                    'адрес': 'address',
                    'address': 'address'
                },
                'transformations': {
                    'name': lambda x: DataTransformer.normalize_text(x, 100),
                    'phone': lambda x: DataValidator.validate_phone(x)[1],
                    'email': lambda x: x.strip().lower() if DataValidator.validate_email(x) else None
                }
            }
        }
    
    def get_table_mapping(self, table_key: str) -> Dict[str, Any]:
        """Get mapping configuration for table"""
        return self.table_mappings.get(table_key, {
            'required_fields': [],
            'field_mappings': {},
            'transformations': {}
        })
    
    def map_record_fields(self, record: Dict[str, Any], table_key: str) -> Dict[str, Any]:
        """Map record fields according to table configuration"""
        mapping_config = self.get_table_mapping(table_key)
        field_mappings = mapping_config.get('field_mappings', {})
        transformations = mapping_config.get('transformations', {})
        
        mapped_record = {}
        
        # Map fields
        for original_field, value in record.items():
            # Find mapped field name
            mapped_field = field_mappings.get(original_field.lower(), original_field)
            
            # Apply transformation if available
            if mapped_field in transformations:
                try:
                    transformed_value = transformations[mapped_field](value)
                    mapped_record[mapped_field] = transformed_value
                except Exception as e:
                    log_info(f"Ошибка трансформации поля {mapped_field}: {e}", "DataMapper")
                    mapped_record[mapped_field] = value
            else:
                mapped_record[mapped_field] = value
        
        return mapped_record
    
    def validate_mapped_record(self, record: Dict[str, Any], table_key: str) -> List[str]:
        """Validate mapped record"""
        mapping_config = self.get_table_mapping(table_key)
        required_fields = mapping_config.get('required_fields', [])
        
        return DataValidator.validate_required_fields(record, required_fields)


# Global instances
data_validator = DataValidator()
data_transformer = DataTransformer()
data_mapper = DataMapper()
