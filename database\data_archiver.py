"""
Advanced Data Archiving System for Restaurant Management System
Provides automated data archiving, compression, and retrieval capabilities
"""

import sqlite3
import os
import gzip
import json
import shutil
import threading
import schedule
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import logging
from pathlib import Path

class DataArchiver:
    """Advanced data archiving system with automated scheduling and compression"""
    
    def __init__(self, connection_pool, archive_path: str = "archives"):
        self.connection_pool = connection_pool
        self.archive_path = Path(archive_path)
        self.archive_path.mkdir(exist_ok=True)
        self.logger = logging.getLogger(__name__)
        
        # Archive configuration
        self.archive_rules = {
            'sales': {
                'retention_days': 365 * 2,  # 2 years
                'archive_after_days': 365,  # Archive after 1 year
                'date_column': 'order_date',
                'compress': True,
                'batch_size': 10000
            },
            'audit_log': {
                'retention_days': 365 * 7,  # 7 years for compliance
                'archive_after_days': 365,
                'date_column': 'timestamp',
                'compress': True,
                'batch_size': 5000
            },
            'loyalty_transactions': {
                'retention_days': 365 * 3,  # 3 years
                'archive_after_days': 180,  # 6 months
                'date_column': 'transaction_date',
                'compress': True,
                'batch_size': 10000
            },
            'staff_schedules': {
                'retention_days': 365 * 2,
                'archive_after_days': 90,  # 3 months
                'date_column': 'schedule_date',
                'compress': True,
                'batch_size': 5000
            },
            'purchase_orders': {
                'retention_days': 365 * 5,  # 5 years for tax purposes
                'archive_after_days': 365,
                'date_column': 'order_date',
                'compress': False,  # Keep uncompressed for easy access
                'batch_size': 1000
            }
        }
        
        # Statistics
        self.archive_stats = {
            'total_archived_records': 0,
            'total_archived_size': 0,
            'last_archive_run': None,
            'archives_created': 0,
            'compression_ratio': 0.0
        }
        
        # Start scheduler thread
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        # Schedule daily archiving at 2 AM
        schedule.every().day.at("02:00").do(self._run_daily_archive)
    
    def _run_scheduler(self):
        """Run the archive scheduler"""
        while True:
            try:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
            except Exception as e:
                self.logger.error(f"Scheduler error: {e}")
                time.sleep(300)  # Wait 5 minutes on error
    
    def _run_daily_archive(self):
        """Run daily archiving process"""
        try:
            self.logger.info("Starting daily archive process")
            
            for table_name in self.archive_rules.keys():
                self.archive_table_data(table_name)
            
            # Clean up old archives
            self._cleanup_old_archives()
            
            # Update statistics
            self.archive_stats['last_archive_run'] = datetime.now()
            
            self.logger.info("Daily archive process completed")
            
        except Exception as e:
            self.logger.error(f"Daily archive process failed: {e}")
    
    def archive_table_data(self, table_name: str, force: bool = False) -> Dict[str, Any]:
        """Archive data from a specific table"""
        if table_name not in self.archive_rules:
            raise ValueError(f"No archive rule defined for table: {table_name}")
        
        rule = self.archive_rules[table_name]
        archive_date = datetime.now() - timedelta(days=rule['archive_after_days'])
        
        try:
            # Check if table exists
            table_exists = self.connection_pool.execute_query(
                "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                (table_name,)
            )
            
            if not table_exists:
                self.logger.warning(f"Table {table_name} does not exist, skipping archive")
                return {'status': 'skipped', 'reason': 'table_not_found'}
            
            # Count records to archive
            count_query = f"""
                SELECT COUNT(*) as count 
                FROM {table_name} 
                WHERE {rule['date_column']} < ?
            """
            
            count_result = self.connection_pool.execute_query(
                count_query, (archive_date.strftime('%Y-%m-%d'),), fetch_all=False
            )
            
            records_to_archive = count_result['count'] if count_result else 0
            
            if records_to_archive == 0 and not force:
                return {'status': 'no_data', 'records_to_archive': 0}
            
            self.logger.info(f"Archiving {records_to_archive} records from {table_name}")
            
            # Create archive directory for this table
            table_archive_path = self.archive_path / table_name
            table_archive_path.mkdir(exist_ok=True)
            
            # Archive data in batches
            archived_records = 0
            batch_number = 0
            
            while True:
                # Fetch batch of data
                select_query = f"""
                    SELECT * FROM {table_name} 
                    WHERE {rule['date_column']} < ?
                    ORDER BY {rule['date_column']}
                    LIMIT ?
                """
                
                batch_data = self.connection_pool.execute_query(
                    select_query, 
                    (archive_date.strftime('%Y-%m-%d'), rule['batch_size'])
                )
                
                if not batch_data:
                    break
                
                # Convert to JSON-serializable format
                json_data = []
                for row in batch_data:
                    json_data.append(dict(row))
                
                # Create archive file
                archive_filename = f"{table_name}_{archive_date.strftime('%Y%m%d')}_batch_{batch_number:04d}"
                
                if rule['compress']:
                    archive_file = table_archive_path / f"{archive_filename}.json.gz"
                    with gzip.open(archive_file, 'wt', encoding='utf-8') as f:
                        json.dump({
                            'table': table_name,
                            'archive_date': archive_date.isoformat(),
                            'batch_number': batch_number,
                            'record_count': len(json_data),
                            'data': json_data
                        }, f, indent=2, default=str)
                else:
                    archive_file = table_archive_path / f"{archive_filename}.json"
                    with open(archive_file, 'w', encoding='utf-8') as f:
                        json.dump({
                            'table': table_name,
                            'archive_date': archive_date.isoformat(),
                            'batch_number': batch_number,
                            'record_count': len(json_data),
                            'data': json_data
                        }, f, indent=2, default=str)
                
                # Delete archived records from main table
                if len(json_data) > 0:
                    # Get primary key column
                    pk_column = self._get_primary_key(table_name)
                    if pk_column:
                        pk_values = [row[pk_column] for row in json_data]
                        placeholders = ','.join(['?' for _ in pk_values])
                        delete_query = f"DELETE FROM {table_name} WHERE {pk_column} IN ({placeholders})"
                        self.connection_pool.execute_query(delete_query, tuple(pk_values))
                    else:
                        # Fallback: delete by date range (less precise)
                        delete_query = f"""
                            DELETE FROM {table_name} 
                            WHERE {rule['date_column']} < ? 
                            LIMIT ?
                        """
                        self.connection_pool.execute_query(
                            delete_query, 
                            (archive_date.strftime('%Y-%m-%d'), len(json_data))
                        )
                
                archived_records += len(json_data)
                batch_number += 1
                
                # Update statistics
                file_size = archive_file.stat().st_size
                self.archive_stats['total_archived_size'] += file_size
                
                self.logger.info(f"Archived batch {batch_number} ({len(json_data)} records) to {archive_file}")
                
                # Break if we got less than batch size (last batch)
                if len(json_data) < rule['batch_size']:
                    break
            
            # Update statistics
            self.archive_stats['total_archived_records'] += archived_records
            self.archive_stats['archives_created'] += batch_number
            
            # Vacuum the table to reclaim space
            self.connection_pool.execute_query(f"VACUUM")
            
            return {
                'status': 'success',
                'table': table_name,
                'records_archived': archived_records,
                'batches_created': batch_number,
                'archive_path': str(table_archive_path)
            }
            
        except Exception as e:
            self.logger.error(f"Error archiving table {table_name}: {e}")
            return {
                'status': 'error',
                'table': table_name,
                'error': str(e)
            }
    
    def _get_primary_key(self, table_name: str) -> Optional[str]:
        """Get the primary key column for a table"""
        try:
            pragma_result = self.connection_pool.execute_query(f"PRAGMA table_info({table_name})")
            for column in pragma_result:
                if column['pk'] == 1:
                    return column['name']
            return None
        except Exception:
            return None
    
    def restore_archived_data(self, table_name: str, start_date: str, end_date: str) -> Dict[str, Any]:
        """Restore archived data for a specific date range"""
        if table_name not in self.archive_rules:
            raise ValueError(f"No archive rule defined for table: {table_name}")
        
        table_archive_path = self.archive_path / table_name
        if not table_archive_path.exists():
            return {'status': 'no_archives', 'message': f'No archives found for table {table_name}'}
        
        restored_records = 0
        processed_files = 0
        
        try:
            # Find relevant archive files
            archive_files = []
            for archive_file in table_archive_path.glob("*.json*"):
                archive_files.append(archive_file)
            
            for archive_file in archive_files:
                try:
                    # Read archive file
                    if archive_file.suffix == '.gz':
                        with gzip.open(archive_file, 'rt', encoding='utf-8') as f:
                            archive_data = json.load(f)
                    else:
                        with open(archive_file, 'r', encoding='utf-8') as f:
                            archive_data = json.load(f)
                    
                    # Filter data by date range
                    rule = self.archive_rules[table_name]
                    filtered_data = []
                    
                    for record in archive_data['data']:
                        record_date = record.get(rule['date_column'])
                        if record_date and start_date <= record_date <= end_date:
                            filtered_data.append(record)
                    
                    if filtered_data:
                        # Restore data to main table
                        self._restore_records_to_table(table_name, filtered_data)
                        restored_records += len(filtered_data)
                    
                    processed_files += 1
                    
                except Exception as e:
                    self.logger.error(f"Error processing archive file {archive_file}: {e}")
                    continue
            
            return {
                'status': 'success',
                'table': table_name,
                'records_restored': restored_records,
                'files_processed': processed_files
            }
            
        except Exception as e:
            self.logger.error(f"Error restoring archived data for {table_name}: {e}")
            return {
                'status': 'error',
                'table': table_name,
                'error': str(e)
            }
    
    def _restore_records_to_table(self, table_name: str, records: List[Dict[str, Any]]):
        """Restore records to the main table"""
        if not records:
            return
        
        # Get table schema
        schema_result = self.connection_pool.execute_query(f"PRAGMA table_info({table_name})")
        columns = [col['name'] for col in schema_result]
        
        # Prepare insert query
        placeholders = ','.join(['?' for _ in columns])
        insert_query = f"INSERT OR REPLACE INTO {table_name} ({','.join(columns)}) VALUES ({placeholders})"
        
        # Prepare data for insertion
        insert_data = []
        for record in records:
            row_data = []
            for column in columns:
                row_data.append(record.get(column))
            insert_data.append(tuple(row_data))
        
        # Insert in batches
        batch_size = 1000
        for i in range(0, len(insert_data), batch_size):
            batch = insert_data[i:i + batch_size]
            self.connection_pool.execute_many(insert_query, batch)
    
    def _cleanup_old_archives(self):
        """Clean up archives older than retention period"""
        try:
            for table_name, rule in self.archive_rules.items():
                table_archive_path = self.archive_path / table_name
                if not table_archive_path.exists():
                    continue
                
                retention_date = datetime.now() - timedelta(days=rule['retention_days'])
                
                for archive_file in table_archive_path.glob("*.json*"):
                    file_date = datetime.fromtimestamp(archive_file.stat().st_mtime)
                    if file_date < retention_date:
                        try:
                            archive_file.unlink()
                            self.logger.info(f"Deleted old archive: {archive_file}")
                        except Exception as e:
                            self.logger.error(f"Error deleting archive {archive_file}: {e}")
                            
        except Exception as e:
            self.logger.error(f"Error cleaning up old archives: {e}")
    
    def get_archive_statistics(self) -> Dict[str, Any]:
        """Get archiving statistics"""
        stats = self.archive_stats.copy()
        
        # Calculate compression ratio
        if stats['total_archived_size'] > 0:
            # Estimate original size (rough calculation)
            estimated_original_size = stats['total_archived_records'] * 200  # Assume 200 bytes per record
            stats['compression_ratio'] = 1 - (stats['total_archived_size'] / estimated_original_size)
        
        # Add archive directory information
        stats['archive_directories'] = []
        for table_name in self.archive_rules.keys():
            table_archive_path = self.archive_path / table_name
            if table_archive_path.exists():
                archive_files = list(table_archive_path.glob("*.json*"))
                total_size = sum(f.stat().st_size for f in archive_files)
                stats['archive_directories'].append({
                    'table': table_name,
                    'file_count': len(archive_files),
                    'total_size': total_size,
                    'path': str(table_archive_path)
                })
        
        return stats
    
    def force_archive_all(self) -> Dict[str, Any]:
        """Force archiving of all tables"""
        results = {}
        
        for table_name in self.archive_rules.keys():
            results[table_name] = self.archive_table_data(table_name, force=True)
        
        return results
