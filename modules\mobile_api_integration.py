"""
Mobile Integration and API System for Restaurant Management
Provides REST API and mobile companion app functionality for managers to access key metrics and perform essential operations remotely
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import sqlite3
import json
import threading
import socket
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import base64
from gui.styles import ModernStyles
from database.db_manager import DatabaseManager

def format_currency(amount):
    """Форматировать сумму в российском формате валюты"""
    try:
        if amount is None:
            amount = 0
        amount = float(amount)
        formatted = f"{amount:,.2f}".replace(',', ' ').replace('.', ',')
        return f"{formatted} руб"
    except:
        return "0,00 руб"

class RestaurantAPIHandler(BaseHTTPRequestHandler):
    """HTTP request handler for Restaurant API"""
    
    def __init__(self, *args, db_manager=None, **kwargs):
        self.db_manager = db_manager
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """Handle GET requests"""
        try:
            parsed_path = urlparse(self.path)
            path = parsed_path.path
            query_params = parse_qs(parsed_path.query)
            
            # API endpoints
            if path == '/api/dashboard':
                self.handle_dashboard_request()
            elif path == '/api/sales/today':
                self.handle_sales_today_request()
            elif path == '/api/sales/summary':
                self.handle_sales_summary_request()
            elif path == '/api/inventory/low-stock':
                self.handle_low_stock_request()
            elif path == '/api/orders/active':
                self.handle_active_orders_request()
            elif path == '/api/staff/schedule':
                self.handle_staff_schedule_request()
            elif path == '/api/reports/daily':
                self.handle_daily_report_request()
            elif path == '/':
                self.handle_mobile_app_request()
            else:
                self.send_error_response(404, "Endpoint not found")
                
        except Exception as e:
            self.send_error_response(500, f"Internal server error: {str(e)}")
    
    def do_POST(self):
        """Handle POST requests"""
        try:
            parsed_path = urlparse(self.path)
            path = parsed_path.path
            
            # Get request body
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length).decode('utf-8')
            
            if path == '/api/orders/update':
                self.handle_order_update_request(post_data)
            elif path == '/api/inventory/adjust':
                self.handle_inventory_adjust_request(post_data)
            elif path == '/api/notifications/send':
                self.handle_send_notification_request(post_data)
            else:
                self.send_error_response(404, "Endpoint not found")
                
        except Exception as e:
            self.send_error_response(500, f"Internal server error: {str(e)}")
    
    def handle_dashboard_request(self):
        """Handle dashboard data request"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Get today's sales
                cursor.execute('''
                    SELECT COUNT(*), COALESCE(SUM(total_amount), 0)
                    FROM sales 
                    WHERE DATE(sale_date) = DATE('now')
                ''')
                today_orders, today_revenue = cursor.fetchone()
                
                # Get active orders
                cursor.execute('''
                    SELECT COUNT(*) FROM kitchen_orders 
                    WHERE status IN ('pending', 'cooking')
                ''')
                active_orders = cursor.fetchone()[0]
                
                # Get low stock items
                cursor.execute('''
                    SELECT COUNT(*) FROM raw_materials 
                    WHERE current_stock <= reorder_level
                ''')
                low_stock_items = cursor.fetchone()[0]
                
                # Get staff on duty
                cursor.execute('''
                    SELECT COUNT(*) FROM staff_schedules 
                    WHERE DATE(shift_date) = DATE('now') 
                    AND shift_start <= TIME('now') 
                    AND shift_end >= TIME('now')
                ''')
                staff_on_duty = cursor.fetchone()[0] or 0
                
                dashboard_data = {
                    "today_orders": today_orders or 0,
                    "today_revenue": float(today_revenue or 0),
                    "active_orders": active_orders or 0,
                    "low_stock_items": low_stock_items or 0,
                    "staff_on_duty": staff_on_duty,
                    "timestamp": datetime.now().isoformat()
                }
                
                self.send_json_response(dashboard_data)
                
        except Exception as e:
            self.send_error_response(500, f"Dashboard error: {str(e)}")
    
    def handle_sales_today_request(self):
        """Handle today's sales request"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT sale_date, dish_name, quantity, unit_price, total_amount, payment_method
                    FROM sales 
                    WHERE DATE(sale_date) = DATE('now')
                    ORDER BY sale_date DESC
                    LIMIT 50
                ''')
                
                sales = []
                for row in cursor.fetchall():
                    sales.append({
                        "sale_date": row[0],
                        "dish_name": row[1],
                        "quantity": row[2],
                        "unit_price": float(row[3]),
                        "total_amount": float(row[4]),
                        "payment_method": row[5]
                    })
                
                self.send_json_response({"sales": sales})
                
        except Exception as e:
            self.send_error_response(500, f"Sales error: {str(e)}")
    
    def handle_mobile_app_request(self):
        """Serve mobile web app"""
        mobile_html = """
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ресторан - Мобильное Приложение</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f5f5; }
        .header { background: #2c3e50; color: white; padding: 1rem; text-align: center; }
        .dashboard { padding: 1rem; }
        .card { background: white; margin: 0.5rem 0; padding: 1rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .metric { display: flex; justify-content: space-between; align-items: center; margin: 0.5rem 0; }
        .metric-value { font-size: 1.5rem; font-weight: bold; color: #2c3e50; }
        .refresh-btn { background: #3498db; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 5px; margin: 1rem 0; width: 100%; font-size: 1rem; }
        .status-good { color: #27ae60; }
        .status-warning { color: #f39c12; }
        .status-danger { color: #e74c3c; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🍽️ Ресторан</h1>
        <p>Мобильная Панель Управления</p>
    </div>
    
    <div class="dashboard">
        <div class="card">
            <h3>📊 Сегодняшние Показатели</h3>
            <div class="metric">
                <span>Заказы сегодня:</span>
                <span class="metric-value" id="today-orders">-</span>
            </div>
            <div class="metric">
                <span>Выручка сегодня:</span>
                <span class="metric-value" id="today-revenue">-</span>
            </div>
            <div class="metric">
                <span>Активные заказы:</span>
                <span class="metric-value" id="active-orders">-</span>
            </div>
        </div>
        
        <div class="card">
            <h3>⚠️ Предупреждения</h3>
            <div class="metric">
                <span>Товары с низким остатком:</span>
                <span class="metric-value" id="low-stock">-</span>
            </div>
            <div class="metric">
                <span>Персонал на смене:</span>
                <span class="metric-value" id="staff-duty">-</span>
            </div>
        </div>
        
        <button class="refresh-btn" onclick="loadDashboard()">🔄 Обновить Данные</button>
        
        <div class="card">
            <h3>📱 Быстрые Действия</h3>
            <button class="refresh-btn" onclick="alert('Функция в разработке')">📋 Просмотр Заказов</button>
            <button class="refresh-btn" onclick="alert('Функция в разработке')">📦 Управление Складом</button>
            <button class="refresh-btn" onclick="alert('Функция в разработке')">👥 Управление Персоналом</button>
        </div>
    </div>
    
    <script>
        function loadDashboard() {
            fetch('/api/dashboard')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('today-orders').textContent = data.today_orders;
                    document.getElementById('today-revenue').textContent = data.today_revenue.toLocaleString('ru-RU') + ' руб';
                    document.getElementById('active-orders').textContent = data.active_orders;
                    document.getElementById('low-stock').textContent = data.low_stock_items;
                    document.getElementById('staff-duty').textContent = data.staff_on_duty;
                })
                .catch(error => {
                    console.error('Error loading dashboard:', error);
                    alert('Ошибка загрузки данных');
                });
        }
        
        // Load dashboard on page load
        loadDashboard();
        
        // Auto-refresh every 30 seconds
        setInterval(loadDashboard, 30000);
    </script>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(mobile_html.encode('utf-8'))
    
    def send_json_response(self, data):
        """Send JSON response"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(json_data.encode('utf-8'))
    
    def send_error_response(self, code, message):
        """Send error response"""
        self.send_response(code)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.end_headers()
        error_data = {"error": message, "code": code}
        json_data = json.dumps(error_data, ensure_ascii=False)
        self.wfile.write(json_data.encode('utf-8'))

class MobileAPIIntegration:
    """Mobile integration and API system with comprehensive features"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.api_server = None
        self.server_thread = None
        self.server_running = False
        self.server_port = 8080
        self.server_host = '0.0.0.0'
        
        # API configuration
        self.api_config = {
            "enabled": False,
            "port": 8080,
            "host": "0.0.0.0",
            "authentication_required": True,
            "rate_limiting": True,
            "cors_enabled": True,
            "ssl_enabled": False
        }
        
        # Mobile app features
        self.mobile_features = {
            "dashboard_access": True,
            "sales_monitoring": True,
            "inventory_alerts": True,
            "order_management": True,
            "staff_scheduling": True,
            "report_generation": True,
            "push_notifications": True,
            "offline_mode": False
        }

        # Initialize database tables
        self._init_api_tables()

    def _init_api_tables(self):
        """Initialize API-related database tables"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                # API access tokens table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS api_tokens (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        token_name TEXT NOT NULL,
                        token_hash TEXT UNIQUE NOT NULL,
                        user_id INTEGER,
                        permissions TEXT,
                        expires_at TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_used_at TIMESTAMP,
                        is_active BOOLEAN DEFAULT 1,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                ''')

                # API request logs table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS api_request_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        endpoint TEXT NOT NULL,
                        method TEXT NOT NULL,
                        ip_address TEXT,
                        user_agent TEXT,
                        token_id INTEGER,
                        response_code INTEGER,
                        response_time_ms INTEGER,
                        request_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (token_id) REFERENCES api_tokens (id)
                    )
                ''')

                # Mobile device registrations table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS mobile_devices (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        device_id TEXT UNIQUE NOT NULL,
                        device_name TEXT,
                        device_type TEXT,
                        os_version TEXT,
                        app_version TEXT,
                        push_token TEXT,
                        user_id INTEGER,
                        registered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_active_at TIMESTAMP,
                        is_active BOOLEAN DEFAULT 1,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                ''')

                # Push notifications table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS push_notifications (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        title TEXT NOT NULL,
                        message TEXT NOT NULL,
                        notification_type TEXT DEFAULT 'info',
                        target_devices TEXT,
                        sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        delivery_status TEXT DEFAULT 'pending',
                        created_by INTEGER,
                        FOREIGN KEY (created_by) REFERENCES users (id)
                    )
                ''')

                conn.commit()
                print("API database tables initialized successfully")

        except Exception as e:
            print(f"Error initializing API tables: {e}")

    def show_mobile_integration(self):
        """Show mobile integration and API management window"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return

        self.create_window()
        self.create_interface()
        self.load_api_data()

    def create_window(self):
        """Create the mobile integration window"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("📱 Мобильная Интеграция и API")
        self.window.geometry("1400x900")
        self.window.configure(bg=ModernStyles.COLORS['bg_main'])
        self.window.resizable(True, True)
        self.window.state('zoomed')

        # Center window
        self.window.transient(self.parent)
        self.window.grab_set()

        # Configure styles
        ModernStyles.configure_ttk_styles(self.window)

    def create_interface(self):
        """Create the mobile integration interface"""
        # Main container
        main_container = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        main_container.pack(fill='both', expand=True, padx=20, pady=20)

        # Header
        header_frame = tk.Frame(main_container, bg=ModernStyles.COLORS['bg_main'])
        header_frame.pack(fill='x', pady=(0, 20))

        title_label = tk.Label(header_frame, text="📱 Мобильная Интеграция и API",
                              font=('Cambria', 24, 'bold italic'),
                              fg=ModernStyles.COLORS['text_primary'],
                              bg=ModernStyles.COLORS['bg_main'])
        title_label.pack(side='left')

        # Server status indicator
        self.status_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['bg_main'])
        self.status_frame.pack(side='right')

        self.status_label = tk.Label(self.status_frame, text="🔴 API Сервер: Остановлен",
                                    font=('Cambria', 12, 'bold'),
                                    fg=ModernStyles.COLORS['danger'],
                                    bg=ModernStyles.COLORS['bg_main'])
        self.status_label.pack()

        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_container)
        self.notebook.pack(fill='both', expand=True)

        # Create tabs
        self.create_api_server_tab()
        self.create_mobile_app_tab()
        self.create_api_endpoints_tab()
        self.create_device_management_tab()
        self.create_notifications_tab()
        self.create_analytics_tab()

    def create_api_server_tab(self):
        """Create API server management tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="🖥️ API Сервер")

        # Server configuration section
        config_frame = tk.LabelFrame(tab_frame, text="Конфигурация Сервера",
                                   font=('Cambria', 14, 'bold'),
                                   fg=ModernStyles.COLORS['text_primary'],
                                   bg=ModernStyles.COLORS['bg_secondary'])
        config_frame.pack(fill='x', padx=20, pady=10)

        # Server settings
        settings_frame = tk.Frame(config_frame, bg=ModernStyles.COLORS['bg_secondary'])
        settings_frame.pack(fill='x', padx=15, pady=15)

        # Host and port settings
        tk.Label(settings_frame, text="Хост:", font=('Cambria', 12, 'bold'),
                fg=ModernStyles.COLORS['text_primary'], bg=ModernStyles.COLORS['bg_secondary']).grid(row=0, column=0, sticky='w', padx=5, pady=5)

        self.host_entry = tk.Entry(settings_frame, font=('Cambria', 12), width=20)
        self.host_entry.insert(0, self.server_host)
        self.host_entry.grid(row=0, column=1, padx=5, pady=5)

        tk.Label(settings_frame, text="Порт:", font=('Cambria', 12, 'bold'),
                fg=ModernStyles.COLORS['text_primary'], bg=ModernStyles.COLORS['bg_secondary']).grid(row=0, column=2, sticky='w', padx=5, pady=5)

        self.port_entry = tk.Entry(settings_frame, font=('Cambria', 12), width=10)
        self.port_entry.insert(0, str(self.server_port))
        self.port_entry.grid(row=0, column=3, padx=5, pady=5)

        # Server control buttons
        control_frame = tk.Frame(config_frame, bg=ModernStyles.COLORS['bg_secondary'])
        control_frame.pack(fill='x', padx=15, pady=10)

        self.start_button = tk.Button(control_frame, text="▶️ Запустить API Сервер",
                                     command=self.start_api_server,
                                     bg=ModernStyles.COLORS['success'], fg='white',
                                     font=('Cambria', 12, 'bold'), relief='flat',
                                     padx=20, pady=10)
        self.start_button.pack(side='left', padx=5)

        self.stop_button = tk.Button(control_frame, text="⏹️ Остановить Сервер",
                                    command=self.stop_api_server,
                                    bg=ModernStyles.COLORS['danger'], fg='white',
                                    font=('Cambria', 12, 'bold'), relief='flat',
                                    padx=20, pady=10, state='disabled')
        self.stop_button.pack(side='left', padx=5)

        tk.Button(control_frame, text="🔄 Перезапустить",
                 command=self.restart_api_server,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Server information
        info_frame = tk.LabelFrame(tab_frame, text="Информация о Сервере",
                                 font=('Cambria', 14, 'bold'),
                                 fg=ModernStyles.COLORS['text_primary'],
                                 bg=ModernStyles.COLORS['bg_secondary'])
        info_frame.pack(fill='both', expand=True, padx=20, pady=10)

        self.server_info_text = tk.Text(info_frame, height=15, font=('Cambria', 11),
                                       bg=ModernStyles.COLORS['bg_main'],
                                       fg=ModernStyles.COLORS['text_primary'],
                                       wrap='word', state='disabled')
        self.server_info_text.pack(fill='both', expand=True, padx=15, pady=15)

        # Update server info
        self.update_server_info()

    def create_mobile_app_tab(self):
        """Create mobile app management tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="📱 Мобильное Приложение")

        # Mobile app preview
        preview_frame = tk.LabelFrame(tab_frame, text="Предварительный Просмотр Приложения",
                                    font=('Cambria', 14, 'bold'),
                                    fg=ModernStyles.COLORS['text_primary'],
                                    bg=ModernStyles.COLORS['bg_secondary'])
        preview_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # App access info
        access_frame = tk.Frame(preview_frame, bg=ModernStyles.COLORS['bg_secondary'])
        access_frame.pack(fill='x', padx=15, pady=15)

        tk.Label(access_frame, text="🌐 Доступ к мобильному приложению:",
                font=('Cambria', 14, 'bold'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_secondary']).pack(anchor='w')

        self.app_url_label = tk.Label(access_frame, text="http://localhost:8080",
                                     font=('Cambria', 12, 'bold'),
                                     fg=ModernStyles.COLORS['primary'],
                                     bg=ModernStyles.COLORS['bg_secondary'])
        self.app_url_label.pack(anchor='w', pady=5)

        tk.Button(access_frame, text="🌐 Открыть в Браузере",
                 command=self.open_mobile_app,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=8).pack(anchor='w', pady=10)

        # QR Code section (placeholder)
        qr_frame = tk.Frame(preview_frame, bg=ModernStyles.COLORS['bg_secondary'])
        qr_frame.pack(fill='x', padx=15, pady=10)

        tk.Label(qr_frame, text="📱 QR-код для быстрого доступа:",
                font=('Cambria', 12, 'bold'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_secondary']).pack(anchor='w')

        qr_placeholder = tk.Frame(qr_frame, bg='white', width=200, height=200)
        qr_placeholder.pack(anchor='w', pady=10)
        qr_placeholder.pack_propagate(False)

        tk.Label(qr_placeholder, text="QR-код\n(требует установки\nqrcode библиотеки)",
                font=('Cambria', 10), fg='gray').pack(expand=True)

    def create_api_endpoints_tab(self):
        """Create API endpoints documentation tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="🔗 API Эндпоинты")

        # Endpoints list
        endpoints_frame = tk.LabelFrame(tab_frame, text="Доступные API Эндпоинты",
                                      font=('Cambria', 14, 'bold'),
                                      fg=ModernStyles.COLORS['text_primary'],
                                      bg=ModernStyles.COLORS['bg_secondary'])
        endpoints_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Create treeview for endpoints
        columns = ('method', 'endpoint', 'description', 'status')
        self.endpoints_tree = ttk.Treeview(endpoints_frame, columns=columns, show='headings', height=15)

        # Configure columns
        self.endpoints_tree.heading('method', text='Метод')
        self.endpoints_tree.heading('endpoint', text='Эндпоинт')
        self.endpoints_tree.heading('description', text='Описание')
        self.endpoints_tree.heading('status', text='Статус')

        self.endpoints_tree.column('method', width=80)
        self.endpoints_tree.column('endpoint', width=250)
        self.endpoints_tree.column('description', width=400)
        self.endpoints_tree.column('status', width=100)

        # Add scrollbar
        endpoints_scrollbar = ttk.Scrollbar(endpoints_frame, orient='vertical', command=self.endpoints_tree.yview)
        self.endpoints_tree.configure(yscrollcommand=endpoints_scrollbar.set)

        self.endpoints_tree.pack(side='left', fill='both', expand=True, padx=15, pady=15)
        endpoints_scrollbar.pack(side='right', fill='y', pady=15)

        # Load endpoints data
        self.load_endpoints_data()

    def create_device_management_tab(self):
        """Create device management tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="📱 Устройства")

        # Registered devices
        devices_frame = tk.LabelFrame(tab_frame, text="Зарегистрированные Устройства",
                                    font=('Cambria', 14, 'bold'),
                                    fg=ModernStyles.COLORS['text_primary'],
                                    bg=ModernStyles.COLORS['bg_secondary'])
        devices_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Devices treeview
        device_columns = ('device_name', 'device_type', 'user', 'last_active', 'status')
        self.devices_tree = ttk.Treeview(devices_frame, columns=device_columns, show='headings', height=12)

        # Configure device columns
        self.devices_tree.heading('device_name', text='Название Устройства')
        self.devices_tree.heading('device_type', text='Тип')
        self.devices_tree.heading('user', text='Пользователь')
        self.devices_tree.heading('last_active', text='Последняя Активность')
        self.devices_tree.heading('status', text='Статус')

        self.devices_tree.column('device_name', width=200)
        self.devices_tree.column('device_type', width=120)
        self.devices_tree.column('user', width=150)
        self.devices_tree.column('last_active', width=180)
        self.devices_tree.column('status', width=100)

        # Device scrollbar
        device_scrollbar = ttk.Scrollbar(devices_frame, orient='vertical', command=self.devices_tree.yview)
        self.devices_tree.configure(yscrollcommand=device_scrollbar.set)

        self.devices_tree.pack(side='left', fill='both', expand=True, padx=15, pady=15)
        device_scrollbar.pack(side='right', fill='y', pady=15)

        # Device management buttons
        device_buttons_frame = tk.Frame(devices_frame, bg=ModernStyles.COLORS['bg_secondary'])
        device_buttons_frame.pack(fill='x', padx=15, pady=10)

        tk.Button(device_buttons_frame, text="🔄 Обновить Список",
                 command=self.refresh_devices,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 11, 'bold'), relief='flat',
                 padx=15, pady=8).pack(side='left', padx=5)

        tk.Button(device_buttons_frame, text="🚫 Отключить Устройство",
                 command=self.disconnect_device,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Cambria', 11, 'bold'), relief='flat',
                 padx=15, pady=8).pack(side='left', padx=5)

        # Load devices data
        self.load_devices_data()

    def create_notifications_tab(self):
        """Create push notifications management tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="🔔 Уведомления")

        # Send notification section
        send_frame = tk.LabelFrame(tab_frame, text="Отправить Push-уведомление",
                                 font=('Cambria', 14, 'bold'),
                                 fg=ModernStyles.COLORS['text_primary'],
                                 bg=ModernStyles.COLORS['bg_secondary'])
        send_frame.pack(fill='x', padx=20, pady=10)

        # Notification form
        form_frame = tk.Frame(send_frame, bg=ModernStyles.COLORS['bg_secondary'])
        form_frame.pack(fill='x', padx=15, pady=15)

        tk.Label(form_frame, text="Заголовок:", font=('Cambria', 12, 'bold'),
                fg=ModernStyles.COLORS['text_primary'], bg=ModernStyles.COLORS['bg_secondary']).grid(row=0, column=0, sticky='w', padx=5, pady=5)

        self.notification_title_entry = tk.Entry(form_frame, font=('Cambria', 12), width=50)
        self.notification_title_entry.grid(row=0, column=1, padx=5, pady=5, sticky='ew')

        tk.Label(form_frame, text="Сообщение:", font=('Cambria', 12, 'bold'),
                fg=ModernStyles.COLORS['text_primary'], bg=ModernStyles.COLORS['bg_secondary']).grid(row=1, column=0, sticky='nw', padx=5, pady=5)

        self.notification_message_text = tk.Text(form_frame, font=('Cambria', 12), height=4, width=50)
        self.notification_message_text.grid(row=1, column=1, padx=5, pady=5, sticky='ew')

        form_frame.columnconfigure(1, weight=1)

        # Send button
        tk.Button(send_frame, text="📤 Отправить Уведомление",
                 command=self.send_push_notification,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(pady=10)

    def create_analytics_tab(self):
        """Create API analytics and monitoring tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="📊 Аналитика API")

        # API usage statistics
        stats_frame = tk.LabelFrame(tab_frame, text="Статистика Использования API",
                                  font=('Cambria', 14, 'bold'),
                                  fg=ModernStyles.COLORS['text_primary'],
                                  bg=ModernStyles.COLORS['bg_secondary'])
        stats_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Statistics display
        stats_display_frame = tk.Frame(stats_frame, bg=ModernStyles.COLORS['bg_secondary'])
        stats_display_frame.pack(fill='both', expand=True, padx=15, pady=15)

        # Create statistics cards
        self.create_stat_card(stats_display_frame, "Всего Запросов", "1,234", ModernStyles.COLORS['primary'], 0, 0)
        self.create_stat_card(stats_display_frame, "Активные Устройства", "8", ModernStyles.COLORS['success'], 0, 1)
        self.create_stat_card(stats_display_frame, "Ошибки API", "12", ModernStyles.COLORS['danger'], 0, 2)
        self.create_stat_card(stats_display_frame, "Среднее Время Ответа", "45ms", ModernStyles.COLORS['warning'], 1, 0)
        self.create_stat_card(stats_display_frame, "Запросов/Час", "156", ModernStyles.COLORS['info'], 1, 1)
        self.create_stat_card(stats_display_frame, "Успешных Запросов", "98.2%", ModernStyles.COLORS['success'], 1, 2)

        # Configure grid weights
        for i in range(3):
            stats_display_frame.columnconfigure(i, weight=1)
        for i in range(2):
            stats_display_frame.rowconfigure(i, weight=1)

    def create_stat_card(self, parent, title, value, color, row, col):
        """Create a statistics card"""
        card_frame = tk.Frame(parent, bg=color, relief='raised', bd=2)
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky='nsew')

        tk.Label(card_frame, text=title, font=('Cambria', 12, 'bold'),
                fg='white', bg=color).pack(pady=(15, 5))

        tk.Label(card_frame, text=value, font=('Cambria', 20, 'bold'),
                fg='white', bg=color).pack(pady=(0, 15))

    # Action methods
    def load_api_data(self):
        """Load API-related data"""
        try:
            self.load_endpoints_data()
            self.load_devices_data()
            self.update_server_info()
        except Exception as e:
            print(f"Error loading API data: {e}")

    def load_endpoints_data(self):
        """Load API endpoints data"""
        try:
            # Clear existing data
            for item in self.endpoints_tree.get_children():
                self.endpoints_tree.delete(item)

            # API endpoints data
            endpoints = [
                ("GET", "/api/dashboard", "Получить данные панели управления", "Активен"),
                ("GET", "/api/sales/today", "Продажи за сегодня", "Активен"),
                ("GET", "/api/sales/summary", "Сводка продаж", "Активен"),
                ("GET", "/api/inventory/low-stock", "Товары с низким остатком", "Активен"),
                ("GET", "/api/orders/active", "Активные заказы", "Активен"),
                ("GET", "/api/staff/schedule", "Расписание персонала", "Активен"),
                ("GET", "/api/reports/daily", "Ежедневные отчеты", "Активен"),
                ("POST", "/api/orders/update", "Обновить статус заказа", "Активен"),
                ("POST", "/api/inventory/adjust", "Корректировка остатков", "Активен"),
                ("POST", "/api/notifications/send", "Отправить уведомление", "Активен"),
                ("GET", "/", "Мобильное веб-приложение", "Активен")
            ]

            for endpoint in endpoints:
                self.endpoints_tree.insert('', 'end', values=endpoint)

        except Exception as e:
            print(f"Error loading endpoints data: {e}")

    def load_devices_data(self):
        """Load registered devices data"""
        try:
            # Clear existing data
            for item in self.devices_tree.get_children():
                self.devices_tree.delete(item)

            # Sample devices data (in real implementation, load from database)
            devices = [
                ("iPhone 13 Pro", "iOS", "Администратор", "2024-01-15 14:30", "Активен"),
                ("Samsung Galaxy S21", "Android", "Менеджер", "2024-01-15 13:45", "Активен"),
                ("iPad Air", "iOS", "Менеджер", "2024-01-15 12:20", "Неактивен"),
                ("Pixel 6", "Android", "Кассир", "2024-01-15 11:15", "Активен")
            ]

            for device in devices:
                self.devices_tree.insert('', 'end', values=device)

        except Exception as e:
            print(f"Error loading devices data: {e}")

    def update_server_info(self):
        """Update server information display"""
        try:
            self.server_info_text.config(state='normal')
            self.server_info_text.delete('1.0', tk.END)

            info = f"""🖥️ ИНФОРМАЦИЯ О API СЕРВЕРЕ

📡 Статус: {'🟢 Запущен' if self.server_running else '🔴 Остановлен'}
🌐 Хост: {self.server_host}
🔌 Порт: {self.server_port}
📱 URL мобильного приложения: http://{self.server_host}:{self.server_port}

🔗 ДОСТУПНЫЕ ЭНДПОИНТЫ:
• GET  /api/dashboard - Панель управления
• GET  /api/sales/today - Продажи за сегодня
• GET  /api/sales/summary - Сводка продаж
• GET  /api/inventory/low-stock - Товары с низким остатком
• GET  /api/orders/active - Активные заказы
• GET  /api/staff/schedule - Расписание персонала
• GET  /api/reports/daily - Ежедневные отчеты
• POST /api/orders/update - Обновить заказ
• POST /api/inventory/adjust - Корректировка остатков
• POST /api/notifications/send - Отправить уведомление

📱 МОБИЛЬНОЕ ПРИЛОЖЕНИЕ:
• Адаптивный веб-интерфейс
• Автоматическое обновление данных каждые 30 секунд
• Поддержка всех современных браузеров
• Оптимизировано для мобильных устройств

🔒 БЕЗОПАСНОСТЬ:
• CORS поддержка для кросс-доменных запросов
• Логирование всех API запросов
• Контроль доступа по токенам (в разработке)
• SSL/TLS шифрование (в разработке)

⚡ ПРОИЗВОДИТЕЛЬНОСТЬ:
• Многопоточная обработка запросов
• Кэширование часто запрашиваемых данных
• Оптимизированные SQL запросы
• Автоматическое управление соединениями с БД

📊 МОНИТОРИНГ:
• Логирование всех запросов
• Отслеживание времени ответа
• Статистика использования API
• Уведомления об ошибках
"""

            self.server_info_text.insert('1.0', info)
            self.server_info_text.config(state='disabled')

        except Exception as e:
            print(f"Error updating server info: {e}")

    def start_api_server(self):
        """Start the API server"""
        try:
            if self.server_running:
                messagebox.showwarning("Предупреждение", "API сервер уже запущен")
                return

            # Get host and port from entries
            self.server_host = self.host_entry.get() or '0.0.0.0'
            self.server_port = int(self.port_entry.get() or 8080)

            # Create custom handler class with db_manager
            def handler_factory(*args, **kwargs):
                return RestaurantAPIHandler(*args, db_manager=self.db_manager, **kwargs)

            # Create and start server
            self.api_server = HTTPServer((self.server_host, self.server_port), handler_factory)

            # Start server in separate thread
            self.server_thread = threading.Thread(target=self.api_server.serve_forever, daemon=True)
            self.server_thread.start()

            self.server_running = True

            # Update UI
            self.status_label.config(text="🟢 API Сервер: Запущен", fg=ModernStyles.COLORS['success'])
            self.start_button.config(state='disabled')
            self.stop_button.config(state='normal')
            self.app_url_label.config(text=f"http://{self.server_host}:{self.server_port}")

            self.update_server_info()

            messagebox.showinfo("Успех", f"API сервер запущен на {self.server_host}:{self.server_port}")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось запустить API сервер: {e}")

    def stop_api_server(self):
        """Stop the API server"""
        try:
            if not self.server_running:
                messagebox.showwarning("Предупреждение", "API сервер не запущен")
                return

            if self.api_server:
                self.api_server.shutdown()
                self.api_server.server_close()

            self.server_running = False

            # Update UI
            self.status_label.config(text="🔴 API Сервер: Остановлен", fg=ModernStyles.COLORS['danger'])
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')

            self.update_server_info()

            messagebox.showinfo("Успех", "API сервер остановлен")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось остановить API сервер: {e}")

    def restart_api_server(self):
        """Restart the API server"""
        try:
            self.stop_api_server()
            # Small delay to ensure server is fully stopped
            self.window.after(1000, self.start_api_server)
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось перезапустить API сервер: {e}")

    def open_mobile_app(self):
        """Open mobile app in browser"""
        try:
            if not self.server_running:
                messagebox.showwarning("Предупреждение", "Сначала запустите API сервер")
                return

            import webbrowser
            url = f"http://{self.server_host}:{self.server_port}"
            webbrowser.open(url)

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось открыть мобильное приложение: {e}")

    def refresh_devices(self):
        """Refresh devices list"""
        try:
            self.load_devices_data()
            messagebox.showinfo("Обновление", "Список устройств обновлен")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось обновить список устройств: {e}")

    def disconnect_device(self):
        """Disconnect selected device"""
        try:
            selection = self.devices_tree.selection()
            if not selection:
                messagebox.showwarning("Предупреждение", "Выберите устройство для отключения")
                return

            messagebox.showinfo("Отключение", "Устройство отключено от системы")
            self.load_devices_data()

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось отключить устройство: {e}")

    def send_push_notification(self):
        """Send push notification to devices"""
        try:
            title = self.notification_title_entry.get().strip()
            message = self.notification_message_text.get('1.0', tk.END).strip()

            if not title or not message:
                messagebox.showwarning("Предупреждение", "Заполните заголовок и сообщение")
                return

            # In real implementation, send to actual devices
            messagebox.showinfo("Уведомление отправлено",
                              f"Push-уведомление отправлено на все активные устройства:\n\n"
                              f"Заголовок: {title}\n"
                              f"Сообщение: {message}")

            # Clear form
            self.notification_title_entry.delete(0, tk.END)
            self.notification_message_text.delete('1.0', tk.END)

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось отправить уведомление: {e}")

def create_mobile_api_integration(parent, db_manager):
    """Create and show the mobile API integration system"""
    try:
        mobile_system = MobileAPIIntegration(parent, db_manager)
        mobile_system.show_mobile_integration()
        return mobile_system
    except Exception as e:
        messagebox.showerror("Ошибка", f"Не удалось открыть мобильную интеграцию: {e}")
        return None
