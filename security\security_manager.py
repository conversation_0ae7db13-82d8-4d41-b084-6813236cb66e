"""
Enterprise Security Framework
Comprehensive security management with role-based access, audit trails, and encryption
"""

import sqlite3
import hashlib
import secrets
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import base64
import os

class UserRole(Enum):
    """User roles with different permission levels"""
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    MANAGER = "manager"
    CASHIER = "cashier"
    KITCHEN_STAFF = "kitchen_staff"
    WAITER = "waiter"
    VIEWER = "viewer"

class Permission(Enum):
    """System permissions"""
    # User management
    CREATE_USER = "create_user"
    EDIT_USER = "edit_user"
    DELETE_USER = "delete_user"
    VIEW_USERS = "view_users"
    
    # Financial
    VIEW_FINANCIAL = "view_financial"
    EDIT_FINANCIAL = "edit_financial"
    EXPORT_FINANCIAL = "export_financial"
    
    # Inventory
    VIEW_INVENTORY = "view_inventory"
    EDIT_INVENTORY = "edit_inventory"
    MANAGE_SUPPLIERS = "manage_suppliers"
    
    # Sales and Orders
    VIEW_SALES = "view_sales"
    CREATE_ORDER = "create_order"
    EDIT_ORDER = "edit_order"
    CANCEL_ORDER = "cancel_order"
    PROCESS_PAYMENT = "process_payment"
    
    # Reports
    VIEW_REPORTS = "view_reports"
    EXPORT_REPORTS = "export_reports"
    ADVANCED_ANALYTICS = "advanced_analytics"
    
    # System
    SYSTEM_SETTINGS = "system_settings"
    DATABASE_BACKUP = "database_backup"
    AUDIT_LOGS = "audit_logs"

@dataclass
class User:
    """User data structure"""
    id: int
    username: str
    email: str
    role: UserRole
    is_active: bool
    created_at: datetime
    last_login: Optional[datetime] = None
    failed_login_attempts: int = 0
    locked_until: Optional[datetime] = None

@dataclass
class AuditLogEntry:
    """Audit log entry structure"""
    id: int
    user_id: int
    username: str
    action: str
    resource: str
    details: str
    ip_address: str
    timestamp: datetime
    success: bool

class SecurityManager:
    """Enterprise Security Manager"""
    
    # Role permissions mapping
    ROLE_PERMISSIONS = {
        UserRole.SUPER_ADMIN: list(Permission),  # All permissions
        UserRole.ADMIN: [
            Permission.CREATE_USER, Permission.EDIT_USER, Permission.VIEW_USERS,
            Permission.VIEW_FINANCIAL, Permission.EDIT_FINANCIAL, Permission.EXPORT_FINANCIAL,
            Permission.VIEW_INVENTORY, Permission.EDIT_INVENTORY, Permission.MANAGE_SUPPLIERS,
            Permission.VIEW_SALES, Permission.CREATE_ORDER, Permission.EDIT_ORDER, Permission.CANCEL_ORDER,
            Permission.PROCESS_PAYMENT, Permission.VIEW_REPORTS, Permission.EXPORT_REPORTS,
            Permission.ADVANCED_ANALYTICS, Permission.SYSTEM_SETTINGS, Permission.DATABASE_BACKUP,
            Permission.AUDIT_LOGS
        ],
        UserRole.MANAGER: [
            Permission.VIEW_USERS, Permission.VIEW_FINANCIAL, Permission.EXPORT_FINANCIAL,
            Permission.VIEW_INVENTORY, Permission.EDIT_INVENTORY, Permission.VIEW_SALES,
            Permission.CREATE_ORDER, Permission.EDIT_ORDER, Permission.CANCEL_ORDER,
            Permission.PROCESS_PAYMENT, Permission.VIEW_REPORTS, Permission.EXPORT_REPORTS,
            Permission.ADVANCED_ANALYTICS
        ],
        UserRole.CASHIER: [
            Permission.VIEW_INVENTORY, Permission.VIEW_SALES, Permission.CREATE_ORDER,
            Permission.EDIT_ORDER, Permission.PROCESS_PAYMENT, Permission.VIEW_REPORTS
        ],
        UserRole.KITCHEN_STAFF: [
            Permission.VIEW_INVENTORY, Permission.VIEW_SALES, Permission.CREATE_ORDER
        ],
        UserRole.WAITER: [
            Permission.VIEW_INVENTORY, Permission.CREATE_ORDER, Permission.EDIT_ORDER
        ],
        UserRole.VIEWER: [
            Permission.VIEW_INVENTORY, Permission.VIEW_SALES, Permission.VIEW_REPORTS
        ]
    }
    
    def __init__(self, db_connection):
        self.db = db_connection
        self.logger = logging.getLogger(__name__)
        self.current_user = None
        self.session_token = None
        self.init_security_tables()
    
    def init_security_tables(self):
        """Initialize security-related database tables"""
        cursor = self.db.cursor()
        
        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS security_users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                salt TEXT NOT NULL,
                role TEXT NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                failed_login_attempts INTEGER DEFAULT 0,
                locked_until TIMESTAMP,
                two_factor_secret TEXT,
                password_reset_token TEXT,
                password_reset_expires TIMESTAMP
            )
        ''')
        
        # Sessions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS security_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                session_token TEXT UNIQUE NOT NULL,
                ip_address TEXT,
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES security_users (id)
            )
        ''')
        
        # Audit logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS security_audit_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                username TEXT,
                action TEXT NOT NULL,
                resource TEXT,
                details TEXT,
                ip_address TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                success BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES security_users (id)
            )
        ''')
        
        # Permission overrides table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS security_permission_overrides (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                permission TEXT NOT NULL,
                granted BOOLEAN NOT NULL,
                granted_by INTEGER,
                granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES security_users (id),
                FOREIGN KEY (granted_by) REFERENCES security_users (id)
            )
        ''')
        
        self.db.commit()
        
        # Create default admin user if no users exist
        self.create_default_admin()
    
    def create_default_admin(self):
        """Create default admin user if no users exist"""
        cursor = self.db.cursor()
        cursor.execute("SELECT COUNT(*) FROM security_users")
        user_count = cursor.fetchone()[0]
        
        if user_count == 0:
            self.create_user(
                username="admin",
                email="<EMAIL>",
                password="admin123",
                role=UserRole.SUPER_ADMIN
            )
            self.logger.info("Default admin user created: admin/admin123")
    
    def hash_password(self, password: str, salt: str = None) -> Tuple[str, str]:
        """Hash password with salt"""
        if salt is None:
            salt = secrets.token_hex(32)
        
        # Use PBKDF2 for password hashing
        password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return base64.b64encode(password_hash).decode(), salt
    
    def verify_password(self, password: str, password_hash: str, salt: str) -> bool:
        """Verify password against hash"""
        computed_hash, _ = self.hash_password(password, salt)
        return secrets.compare_digest(computed_hash, password_hash)
    
    def create_user(self, username: str, email: str, password: str, role: UserRole) -> bool:
        """Create new user"""
        try:
            password_hash, salt = self.hash_password(password)
            
            cursor = self.db.cursor()
            cursor.execute('''
                INSERT INTO security_users (username, email, password_hash, salt, role)
                VALUES (?, ?, ?, ?, ?)
            ''', (username, email, password_hash, salt, role.value))
            
            user_id = cursor.lastrowid
            self.db.commit()
            
            self.log_audit_event(
                user_id=self.current_user.id if self.current_user else None,
                username=self.current_user.username if self.current_user else "system",
                action="CREATE_USER",
                resource="security_users",
                details=f"Created user: {username} with role: {role.value}",
                success=True
            )
            
            return True
            
        except sqlite3.IntegrityError as e:
            self.logger.error(f"Failed to create user {username}: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Error creating user: {e}")
            return False
    
    def authenticate_user(self, username: str, password: str, ip_address: str = None) -> Optional[User]:
        """Authenticate user and create session"""
        try:
            cursor = self.db.cursor()
            cursor.execute('''
                SELECT id, username, email, password_hash, salt, role, is_active,
                       created_at, last_login, failed_login_attempts, locked_until
                FROM security_users WHERE username = ?
            ''', (username,))
            
            user_data = cursor.fetchone()
            if not user_data:
                self.log_audit_event(
                    username=username,
                    action="LOGIN_FAILED",
                    resource="authentication",
                    details="User not found",
                    ip_address=ip_address,
                    success=False
                )
                return None
            
            user_id, db_username, email, password_hash, salt, role, is_active, created_at, last_login, failed_attempts, locked_until = user_data
            
            # Check if user is locked
            if locked_until and datetime.fromisoformat(locked_until) > datetime.now():
                self.log_audit_event(
                    user_id=user_id,
                    username=username,
                    action="LOGIN_FAILED",
                    resource="authentication",
                    details="Account locked",
                    ip_address=ip_address,
                    success=False
                )
                return None
            
            # Check if user is active
            if not is_active:
                self.log_audit_event(
                    user_id=user_id,
                    username=username,
                    action="LOGIN_FAILED",
                    resource="authentication",
                    details="Account disabled",
                    ip_address=ip_address,
                    success=False
                )
                return None
            
            # Verify password
            if not self.verify_password(password, password_hash, salt):
                # Increment failed attempts
                failed_attempts += 1
                locked_until_time = None
                
                if failed_attempts >= 5:  # Lock after 5 failed attempts
                    locked_until_time = datetime.now() + timedelta(minutes=30)
                
                cursor.execute('''
                    UPDATE security_users 
                    SET failed_login_attempts = ?, locked_until = ?
                    WHERE id = ?
                ''', (failed_attempts, locked_until_time, user_id))
                self.db.commit()
                
                self.log_audit_event(
                    user_id=user_id,
                    username=username,
                    action="LOGIN_FAILED",
                    resource="authentication",
                    details=f"Invalid password (attempt {failed_attempts})",
                    ip_address=ip_address,
                    success=False
                )
                return None
            
            # Successful login - reset failed attempts and update last login
            cursor.execute('''
                UPDATE security_users 
                SET failed_login_attempts = 0, locked_until = NULL, last_login = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (user_id,))
            self.db.commit()
            
            # Create user object
            user = User(
                id=user_id,
                username=db_username,
                email=email,
                role=UserRole(role),
                is_active=bool(is_active),
                created_at=datetime.fromisoformat(created_at),
                last_login=datetime.fromisoformat(last_login) if last_login else None,
                failed_login_attempts=0
            )
            
            # Create session
            self.create_session(user, ip_address)
            self.current_user = user
            
            self.log_audit_event(
                user_id=user_id,
                username=username,
                action="LOGIN_SUCCESS",
                resource="authentication",
                details="Successful login",
                ip_address=ip_address,
                success=True
            )
            
            return user
            
        except Exception as e:
            self.logger.error(f"Authentication error: {e}")
            return None

    def create_session(self, user: User, ip_address: str = None):
        """Create user session"""
        try:
            # Generate session token
            self.session_token = secrets.token_urlsafe(32)
            expires_at = datetime.now() + timedelta(hours=8)  # 8-hour session

            cursor = self.db.cursor()
            cursor.execute('''
                INSERT INTO security_sessions (user_id, session_token, ip_address, expires_at)
                VALUES (?, ?, ?, ?)
            ''', (user.id, self.session_token, ip_address, expires_at))
            self.db.commit()

        except Exception as e:
            self.logger.error(f"Error creating session: {e}")

    def validate_session(self, session_token: str) -> Optional[User]:
        """Validate session token and return user"""
        try:
            cursor = self.db.cursor()
            cursor.execute('''
                SELECT s.user_id, u.username, u.email, u.role, u.is_active
                FROM security_sessions s
                JOIN security_users u ON s.user_id = u.id
                WHERE s.session_token = ? AND s.is_active = 1 AND s.expires_at > CURRENT_TIMESTAMP
            ''', (session_token,))

            session_data = cursor.fetchone()
            if not session_data:
                return None

            user_id, username, email, role, is_active = session_data

            if not is_active:
                return None

            return User(
                id=user_id,
                username=username,
                email=email,
                role=UserRole(role),
                is_active=bool(is_active),
                created_at=datetime.now()  # Simplified for session validation
            )

        except Exception as e:
            self.logger.error(f"Session validation error: {e}")
            return None

    def logout_user(self):
        """Logout current user and invalidate session"""
        try:
            if self.session_token:
                cursor = self.db.cursor()
                cursor.execute('''
                    UPDATE security_sessions SET is_active = 0 WHERE session_token = ?
                ''', (self.session_token,))
                self.db.commit()

                if self.current_user:
                    self.log_audit_event(
                        user_id=self.current_user.id,
                        username=self.current_user.username,
                        action="LOGOUT",
                        resource="authentication",
                        details="User logged out",
                        success=True
                    )

            self.current_user = None
            self.session_token = None

        except Exception as e:
            self.logger.error(f"Logout error: {e}")

    def has_permission(self, permission: Permission, user: User = None) -> bool:
        """Check if user has specific permission"""
        if user is None:
            user = self.current_user

        if not user:
            return False

        # Check role-based permissions
        role_permissions = self.ROLE_PERMISSIONS.get(user.role, [])
        has_role_permission = permission in role_permissions

        # Check permission overrides
        try:
            cursor = self.db.cursor()
            cursor.execute('''
                SELECT granted FROM security_permission_overrides
                WHERE user_id = ? AND permission = ?
                ORDER BY granted_at DESC LIMIT 1
            ''', (user.id, permission.value))

            override = cursor.fetchone()
            if override:
                return bool(override[0])

            return has_role_permission

        except Exception as e:
            self.logger.error(f"Permission check error: {e}")
            return has_role_permission

    def log_audit_event(self, user_id: int = None, username: str = None, action: str = "",
                       resource: str = "", details: str = "", ip_address: str = None, success: bool = True):
        """Log audit event"""
        try:
            cursor = self.db.cursor()
            cursor.execute('''
                INSERT INTO security_audit_logs (user_id, username, action, resource, details, ip_address, success)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (user_id, username, action, resource, details, ip_address, success))
            self.db.commit()

        except Exception as e:
            self.logger.error(f"Audit logging error: {e}")

    def get_audit_logs(self, limit: int = 100, user_id: int = None,
                      action: str = None, start_date: datetime = None,
                      end_date: datetime = None) -> List[AuditLogEntry]:
        """Get audit logs with filtering"""
        try:
            query = "SELECT * FROM security_audit_logs WHERE 1=1"
            params = []

            if user_id:
                query += " AND user_id = ?"
                params.append(user_id)

            if action:
                query += " AND action = ?"
                params.append(action)

            if start_date:
                query += " AND timestamp >= ?"
                params.append(start_date.isoformat())

            if end_date:
                query += " AND timestamp <= ?"
                params.append(end_date.isoformat())

            query += " ORDER BY timestamp DESC LIMIT ?"
            params.append(limit)

            cursor = self.db.cursor()
            cursor.execute(query, params)

            logs = []
            for row in cursor.fetchall():
                logs.append(AuditLogEntry(
                    id=row[0],
                    user_id=row[1],
                    username=row[2] or "Unknown",
                    action=row[3],
                    resource=row[4] or "",
                    details=row[5] or "",
                    ip_address=row[6] or "",
                    timestamp=datetime.fromisoformat(row[7]),
                    success=bool(row[8])
                ))

            return logs

        except Exception as e:
            self.logger.error(f"Error retrieving audit logs: {e}")
            return []

    def get_all_users(self) -> List[User]:
        """Get all users (requires VIEW_USERS permission)"""
        if not self.has_permission(Permission.VIEW_USERS):
            raise PermissionError("Insufficient permissions to view users")

        try:
            cursor = self.db.cursor()
            cursor.execute('''
                SELECT id, username, email, role, is_active, created_at, last_login,
                       failed_login_attempts, locked_until
                FROM security_users ORDER BY username
            ''')

            users = []
            for row in cursor.fetchall():
                users.append(User(
                    id=row[0],
                    username=row[1],
                    email=row[2],
                    role=UserRole(row[3]),
                    is_active=bool(row[4]),
                    created_at=datetime.fromisoformat(row[5]),
                    last_login=datetime.fromisoformat(row[6]) if row[6] else None,
                    failed_login_attempts=row[7],
                    locked_until=datetime.fromisoformat(row[8]) if row[8] else None
                ))

            return users

        except Exception as e:
            self.logger.error(f"Error retrieving users: {e}")
            return []

    def update_user(self, user_id: int, **kwargs) -> bool:
        """Update user information"""
        if not self.has_permission(Permission.EDIT_USER):
            raise PermissionError("Insufficient permissions to edit users")

        try:
            allowed_fields = ['username', 'email', 'role', 'is_active']
            updates = []
            params = []

            for field, value in kwargs.items():
                if field in allowed_fields:
                    updates.append(f"{field} = ?")
                    if field == 'role' and isinstance(value, UserRole):
                        params.append(value.value)
                    else:
                        params.append(value)

            if not updates:
                return False

            params.append(user_id)

            cursor = self.db.cursor()
            cursor.execute(f'''
                UPDATE security_users SET {', '.join(updates)} WHERE id = ?
            ''', params)
            self.db.commit()

            self.log_audit_event(
                user_id=self.current_user.id,
                username=self.current_user.username,
                action="UPDATE_USER",
                resource="security_users",
                details=f"Updated user {user_id}: {kwargs}",
                success=True
            )

            return True

        except Exception as e:
            self.logger.error(f"Error updating user: {e}")
            return False

    def delete_user(self, user_id: int) -> bool:
        """Delete user (soft delete by deactivating)"""
        if not self.has_permission(Permission.DELETE_USER):
            raise PermissionError("Insufficient permissions to delete users")

        if user_id == self.current_user.id:
            raise ValueError("Cannot delete your own account")

        try:
            cursor = self.db.cursor()
            cursor.execute('UPDATE security_users SET is_active = 0 WHERE id = ?', (user_id,))
            self.db.commit()

            self.log_audit_event(
                user_id=self.current_user.id,
                username=self.current_user.username,
                action="DELETE_USER",
                resource="security_users",
                details=f"Deactivated user {user_id}",
                success=True
            )

            return True

        except Exception as e:
            self.logger.error(f"Error deleting user: {e}")
            return False

    def change_password(self, user_id: int, new_password: str, old_password: str = None) -> bool:
        """Change user password"""
        try:
            # If changing own password, verify old password
            if user_id == self.current_user.id and old_password:
                cursor = self.db.cursor()
                cursor.execute('SELECT password_hash, salt FROM security_users WHERE id = ?', (user_id,))
                result = cursor.fetchone()

                if not result or not self.verify_password(old_password, result[0], result[1]):
                    return False
            elif user_id != self.current_user.id and not self.has_permission(Permission.EDIT_USER):
                raise PermissionError("Insufficient permissions to change other user's password")

            # Hash new password
            password_hash, salt = self.hash_password(new_password)

            cursor = self.db.cursor()
            cursor.execute('''
                UPDATE security_users SET password_hash = ?, salt = ?,
                       failed_login_attempts = 0, locked_until = NULL
                WHERE id = ?
            ''', (password_hash, salt, user_id))
            self.db.commit()

            self.log_audit_event(
                user_id=self.current_user.id,
                username=self.current_user.username,
                action="CHANGE_PASSWORD",
                resource="security_users",
                details=f"Password changed for user {user_id}",
                success=True
            )

            return True

        except Exception as e:
            self.logger.error(f"Error changing password: {e}")
            return False
