"""
Advanced Financial Dashboard Module for Restaurant Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
from gui.styles import ModernStyles

# Проверяем наличие matplotlib
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    import pandas as pd
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

# Простые хелперы если utils недоступны
class NumberHelper:
    @staticmethod
    def format_currency(amount):
        """Форматировать сумму в российском формате: 25 952,59 руб"""
        try:
            if amount is None:
                amount = 0
            amount = float(amount)
            formatted = f"{amount:,.2f}".replace(',', ' ').replace('.', ',')
            return f"{formatted} руб"
        except:
            return "0,00 руб"

class DateHelper:
    @staticmethod
    def format_date(date):
        return date.strftime("%d.%m.%Y")

class FinancialDashboard:
    """Advanced financial dashboard with real-time metrics and analytics"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.refresh_interval = 30000  # 30 seconds
    
    def create_dashboard_window(self):
        """Create comprehensive financial dashboard"""
        try:
            from utils.window_utils import create_centered_dialog
            window = create_centered_dialog(
                self.parent,
                "💰 Финансовая Панель Управления",
                width=1600,
                height=1000,
                resizable=True
            )
        except ImportError:
            # Fallback если utils не доступны
            window = tk.Toplevel(self.parent)
            window.title("💰 Финансовая Панель Управления")
            window.geometry("1600x1000")
            window.configure(bg=ModernStyles.COLORS['bg_main'])
            window.resizable(True, True)

            # Центрировать окно
            window.update_idletasks()
            x = (window.winfo_screenwidth() // 2) - (1600 // 2)
            y = (window.winfo_screenheight() // 2) - (1000 // 2)
            window.geometry(f"1600x1000+{x}+{y}")
        
        # Main container
        main_frame = tk.Frame(window, **ModernStyles.WIDGET_STYLES['frame_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Title and refresh
        header_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_main'])
        header_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(header_frame, text="Financial Dashboard",
                **ModernStyles.WIDGET_STYLES['label_title']).pack(side='left')
        
        tk.Button(header_frame, text="Refresh", command=self.refresh_dashboard,
                 **ModernStyles.WIDGET_STYLES['button_primary']).pack(side='right')
        
        # Create scrollable content
        canvas = tk.Canvas(main_frame, bg=ModernStyles.COLORS['bg_main'])
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=ModernStyles.COLORS['bg_main'])
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Pack scrollable components
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Dashboard sections
        self.create_kpi_section(scrollable_frame)
        self.create_revenue_section(scrollable_frame)
        self.create_expense_section(scrollable_frame)
        self.create_profitability_section(scrollable_frame)
        self.create_cash_flow_section(scrollable_frame)
        self.create_trends_section(scrollable_frame)
        
        # Auto-refresh
        self.schedule_refresh(window)
        
        return window
    
    def create_kpi_section(self, parent):
        """Create Key Performance Indicators section"""
        kpi_frame = ModernStyles.create_card_frame(parent)
        kpi_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(kpi_frame, text="Key Performance Indicators",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # KPI cards container
        kpi_container = tk.Frame(kpi_frame, bg=ModernStyles.COLORS['bg_card'])
        kpi_container.pack(fill='x')
        
        # Calculate KPIs
        today_sales = self.get_today_sales()
        monthly_sales = self.get_monthly_sales()
        daily_avg = self.get_daily_average()
        profit_margin = self.get_profit_margin()
        
        # Create KPI cards
        self.create_kpi_card(kpi_container, "Today's Sales", NumberHelper.format_currency(today_sales), 
                           ModernStyles.COLORS['success'], "↗")
        self.create_kpi_card(kpi_container, "Monthly Sales", NumberHelper.format_currency(monthly_sales), 
                           ModernStyles.COLORS['primary'], "📊")
        self.create_kpi_card(kpi_container, "Daily Average", NumberHelper.format_currency(daily_avg), 
                           ModernStyles.COLORS['secondary'], "📈")
        self.create_kpi_card(kpi_container, "Profit Margin", f"{profit_margin:.1f}%", 
                           ModernStyles.COLORS['warning'], "💰")
    
    def create_revenue_section(self, parent):
        """Create revenue analysis section"""
        revenue_frame = ModernStyles.create_card_frame(parent)
        revenue_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(revenue_frame, text="Revenue Analysis",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Revenue chart
        self.create_revenue_chart(revenue_frame)
        
        # Revenue breakdown
        breakdown_frame = tk.Frame(revenue_frame, bg=ModernStyles.COLORS['bg_card'])
        breakdown_frame.pack(fill='x', pady=(15, 0))
        
        # Revenue by category
        categories = self.get_revenue_by_category()
        for category, amount in categories.items():
            cat_frame = tk.Frame(breakdown_frame, bg=ModernStyles.COLORS['bg_card'])
            cat_frame.pack(fill='x', pady=2)
            
            tk.Label(cat_frame, text=f"• {category}",
                    font=ModernStyles.FONTS['body'],
                    bg=ModernStyles.COLORS['bg_card']).pack(side='left')
            
            tk.Label(cat_frame, text=NumberHelper.format_currency(amount),
                    font=ModernStyles.FONTS['body_bold'],
                    fg=ModernStyles.COLORS['success'],
                    bg=ModernStyles.COLORS['bg_card']).pack(side='right')
    
    def create_expense_section(self, parent):
        """Create expense analysis section"""
        expense_frame = ModernStyles.create_card_frame(parent)
        expense_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(expense_frame, text="Expense Analysis",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Expense summary
        summary_frame = tk.Frame(expense_frame, bg=ModernStyles.COLORS['bg_card'])
        summary_frame.pack(fill='x')
        
        # Get expense data
        food_costs = self.get_food_costs()
        labor_costs = self.get_labor_costs()
        overhead_costs = self.get_overhead_costs()
        total_expenses = food_costs + labor_costs + overhead_costs
        
        # Expense breakdown
        expenses = [
            ("Food Costs", food_costs, ModernStyles.COLORS['danger']),
            ("Labor Costs", labor_costs, ModernStyles.COLORS['warning']),
            ("Overhead", overhead_costs, ModernStyles.COLORS['secondary']),
            ("Total Expenses", total_expenses, ModernStyles.COLORS['primary'])
        ]
        
        for name, amount, color in expenses:
            self.create_expense_item(summary_frame, name, amount, color)
    
    def create_profitability_section(self, parent):
        """Create profitability analysis section"""
        profit_frame = ModernStyles.create_card_frame(parent)
        profit_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(profit_frame, text="Profitability Analysis",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Profit metrics
        metrics_frame = tk.Frame(profit_frame, bg=ModernStyles.COLORS['bg_card'])
        metrics_frame.pack(fill='x')
        
        # Calculate profit metrics
        gross_profit = self.get_gross_profit()
        net_profit = self.get_net_profit()
        profit_margin = self.get_profit_margin()
        roi = self.get_roi()
        
        # Create profit display
        profit_data = [
            ("Gross Profit", gross_profit),
            ("Net Profit", net_profit),
            ("Profit Margin", f"{profit_margin:.1f}%"),
            ("ROI", f"{roi:.1f}%")
        ]
        
        for label, value in profit_data:
            self.create_profit_metric(metrics_frame, label, value)
    
    def create_cash_flow_section(self, parent):
        """Create cash flow section"""
        cash_frame = ModernStyles.create_card_frame(parent)
        cash_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(cash_frame, text="Cash Flow Summary",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Cash flow chart
        self.create_cash_flow_chart(cash_frame)
    
    def create_trends_section(self, parent):
        """Create trends analysis section"""
        trends_frame = ModernStyles.create_card_frame(parent)
        trends_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(trends_frame, text="Trends & Forecasting",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Trends chart
        self.create_trends_chart(trends_frame)
    
    def create_kpi_card(self, parent, title, value, color, icon):
        """Create a KPI card widget"""
        card = tk.Frame(parent, bg=color, relief='flat', bd=0)
        card.pack(side='left', fill='both', expand=True, padx=5)
        
        inner_frame = tk.Frame(card, bg=color)
        inner_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Icon and value
        top_frame = tk.Frame(inner_frame, bg=color)
        top_frame.pack(fill='x')
        
        tk.Label(top_frame, text=icon, font=('Arial', 20),
                fg=ModernStyles.COLORS['text_white'], bg=color).pack(side='left')
        
        tk.Label(top_frame, text=value, font=ModernStyles.FONTS['title'],
                fg=ModernStyles.COLORS['text_white'], bg=color).pack(side='right')
        
        # Title
        tk.Label(inner_frame, text=title, font=ModernStyles.FONTS['body'],
                fg=ModernStyles.COLORS['text_white'], bg=color).pack(anchor='w', pady=(5, 0))
    
    def create_expense_item(self, parent, name, amount, color):
        """Create expense item display"""
        item_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_card'])
        item_frame.pack(fill='x', pady=5)
        
        # Color indicator
        indicator = tk.Frame(item_frame, bg=color, width=4)
        indicator.pack(side='left', fill='y', padx=(0, 10))
        
        # Name and amount
        tk.Label(item_frame, text=name, font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_card']).pack(side='left')
        
        tk.Label(item_frame, text=NumberHelper.format_currency(amount),
                font=ModernStyles.FONTS['body_bold'], fg=color,
                bg=ModernStyles.COLORS['bg_card']).pack(side='right')
    
    def create_profit_metric(self, parent, label, value):
        """Create profit metric display"""
        metric_frame = tk.Frame(parent, bg=ModernStyles.COLORS['success'], relief='flat', bd=0)
        metric_frame.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        
        inner_frame = tk.Frame(metric_frame, bg=ModernStyles.COLORS['success'])
        inner_frame.pack(fill='both', expand=True, padx=15, pady=15)
        
        tk.Label(inner_frame, text=str(value), font=ModernStyles.FONTS['heading'],
                fg=ModernStyles.COLORS['text_white'],
                bg=ModernStyles.COLORS['success']).pack()
        
        tk.Label(inner_frame, text=label, font=ModernStyles.FONTS['body'],
                fg=ModernStyles.COLORS['text_white'],
                bg=ModernStyles.COLORS['success']).pack()
    
    def create_revenue_chart(self, parent):
        """Create revenue trend chart"""
        if not MATPLOTLIB_AVAILABLE:
            # Показать текстовые данные вместо графика
            chart_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_card'])
            chart_frame.pack(fill='x', pady=(0, 10))

            tk.Label(chart_frame, text="📈 Динамика Доходов (Последние 7 дней)",
                    font=('Arial', 12, 'bold'),
                    bg=ModernStyles.COLORS['bg_card']).pack(pady=10)

            revenue_data = [
                ("Понедельник", "45,000₽"),
                ("Вторник", "52,000₽"),
                ("Среда", "48,000₽"),
                ("Четверг", "61,000₽"),
                ("Пятница", "58,000₽"),
                ("Суббота", "67,000₽"),
                ("Воскресенье", "72,000₽")
            ]

            for day, amount in revenue_data:
                day_frame = tk.Frame(chart_frame, bg=ModernStyles.COLORS['bg_card'])
                day_frame.pack(fill='x', padx=20, pady=2)

                tk.Label(day_frame, text=day, font=('Arial', 10),
                        bg=ModernStyles.COLORS['bg_card']).pack(side='left')
                tk.Label(day_frame, text=amount, font=('Arial', 10, 'bold'),
                        fg=ModernStyles.COLORS['success'],
                        bg=ModernStyles.COLORS['bg_card']).pack(side='right')
            return

        try:
            # Get revenue data for last 30 days
            revenue_data = self.get_daily_revenue_data()

            if revenue_data:
                fig, ax = plt.subplots(figsize=(10, 4))

                dates = list(revenue_data.keys())
                amounts = list(revenue_data.values())

                ax.plot(dates, amounts, marker='o', linewidth=2, markersize=4,
                       color=ModernStyles.COLORS['primary'])
                ax.fill_between(dates, amounts, alpha=0.3, color=ModernStyles.COLORS['primary'])

                ax.set_title('Daily Revenue Trend (Last 30 Days)', fontsize=14, fontweight='bold')
                ax.set_ylabel('Revenue ($)')
                ax.grid(True, alpha=0.3)

                # Format y-axis as currency
                ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))

                # Rotate x-axis labels
                plt.xticks(rotation=45)
                plt.tight_layout()

                # Embed chart
                canvas = FigureCanvasTkAgg(fig, parent)
                canvas.draw()
                canvas.get_tk_widget().pack(fill='x', pady=(0, 10))

        except Exception as e:
            tk.Label(parent, text=f"График недоступен: {str(e)}",
                    font=ModernStyles.FONTS['body'],
                    fg=ModernStyles.COLORS['text_secondary'],
                    bg=ModernStyles.COLORS['bg_card']).pack()
    
    def create_cash_flow_chart(self, parent):
        """Create cash flow chart"""
        if not MATPLOTLIB_AVAILABLE:
            # Показать текстовые данные
            chart_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_card'])
            chart_frame.pack(fill='x', pady=(0, 10))

            tk.Label(chart_frame, text="💰 Анализ Денежных Потоков",
                    font=('Arial', 12, 'bold'),
                    bg=ModernStyles.COLORS['bg_card']).pack(pady=10)

            cash_data = [
                ("Январь", "Приход: 75,000₽", "Расход: 60,000₽"),
                ("Февраль", "Приход: 90,000₽", "Расход: 70,000₽"),
                ("Март", "Приход: 82,500₽", "Расход: 67,500₽"),
                ("Апрель", "Приход: 95,000₽", "Расход: 75,000₽"),
                ("Май", "Приход: 87,500₽", "Расход: 72,500₽"),
                ("Июнь", "Приход: 100,000₽", "Расход: 80,000₽")
            ]

            for month, inflow, outflow in cash_data:
                month_frame = tk.Frame(chart_frame, bg=ModernStyles.COLORS['bg_card'])
                month_frame.pack(fill='x', padx=20, pady=2)

                tk.Label(month_frame, text=month, font=('Arial', 10, 'bold'),
                        bg=ModernStyles.COLORS['bg_card']).pack(side='left')
                tk.Label(month_frame, text=inflow, font=('Arial', 9),
                        fg=ModernStyles.COLORS['success'],
                        bg=ModernStyles.COLORS['bg_card']).pack(side='right')
                tk.Label(month_frame, text=outflow, font=('Arial', 9),
                        fg=ModernStyles.COLORS['danger'],
                        bg=ModernStyles.COLORS['bg_card']).pack(side='right', padx=(0, 10))
            return

        try:
            # Sample cash flow data
            fig, ax = plt.subplots(figsize=(10, 4))

            months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
            inflow = [15000, 18000, 16500, 19000, 17500, 20000]
            outflow = [12000, 14000, 13500, 15000, 14500, 16000]

            x = range(len(months))
            width = 0.35

            ax.bar([i - width/2 for i in x], inflow, width, label='Cash Inflow',
                  color=ModernStyles.COLORS['success'], alpha=0.8)
            ax.bar([i + width/2 for i in x], outflow, width, label='Cash Outflow',
                  color=ModernStyles.COLORS['danger'], alpha=0.8)

            ax.set_title('Cash Flow Analysis', fontsize=14, fontweight='bold')
            ax.set_ylabel('Amount ($)')
            ax.set_xticks(x)
            ax.set_xticklabels(months)
            ax.legend()
            ax.grid(True, alpha=0.3)

            # Format y-axis as currency
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))

            plt.tight_layout()

            # Embed chart
            canvas = FigureCanvasTkAgg(fig, parent)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='x', pady=(0, 10))

        except Exception as e:
            tk.Label(parent, text=f"График денежных потоков недоступен: {str(e)}",
                    font=ModernStyles.FONTS['body'],
                    fg=ModernStyles.COLORS['text_secondary'],
                    bg=ModernStyles.COLORS['bg_card']).pack()
    
    def create_trends_chart(self, parent):
        """Create trends and forecasting chart"""
        if not MATPLOTLIB_AVAILABLE:
            # Показать текстовые данные
            chart_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_card'])
            chart_frame.pack(fill='x')

            tk.Label(chart_frame, text="📊 Тренды и Прогнозы",
                    font=('Arial', 12, 'bold'),
                    bg=ModernStyles.COLORS['bg_card']).pack(pady=10)

            trend_data = [
                ("Неделя 1", "125,000₽", "Факт"),
                ("Неделя 2", "135,000₽", "Факт"),
                ("Неделя 3", "132,500₽", "Факт"),
                ("Неделя 4", "140,000₽", "Факт"),
                ("Неделя 5", "147,500₽", "Прогноз"),
            ]

            for week, amount, type_label in trend_data:
                week_frame = tk.Frame(chart_frame, bg=ModernStyles.COLORS['bg_card'])
                week_frame.pack(fill='x', padx=20, pady=2)

                tk.Label(week_frame, text=week, font=('Arial', 10),
                        bg=ModernStyles.COLORS['bg_card']).pack(side='left')

                color = ModernStyles.COLORS['primary'] if type_label == "Факт" else ModernStyles.COLORS['warning']
                tk.Label(week_frame, text=f"{amount} ({type_label})", font=('Arial', 10, 'bold'),
                        fg=color, bg=ModernStyles.COLORS['bg_card']).pack(side='right')
            return

        try:
            fig, ax = plt.subplots(figsize=(10, 4))

            # Sample trend data
            weeks = ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Forecast']
            actual = [25000, 27000, 26500, 28000, None]
            forecast = [None, None, None, None, 29500]

            ax.plot(weeks[:4], actual[:4], marker='o', linewidth=2,
                   label='Actual', color=ModernStyles.COLORS['primary'])
            ax.plot(weeks[3:], [actual[3], forecast[4]], marker='s', linewidth=2,
                   linestyle='--', label='Forecast', color=ModernStyles.COLORS['warning'])

            ax.set_title('Revenue Trends & Forecast', fontsize=14, fontweight='bold')
            ax.set_ylabel('Revenue ($)')
            ax.legend()
            ax.grid(True, alpha=0.3)

            # Format y-axis as currency
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))

            plt.tight_layout()

            # Embed chart
            canvas = FigureCanvasTkAgg(fig, parent)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='x')

        except Exception as e:
            tk.Label(parent, text=f"График трендов недоступен: {str(e)}",
                    font=ModernStyles.FONTS['body'],
                    fg=ModernStyles.COLORS['text_secondary'],
                    bg=ModernStyles.COLORS['bg_card']).pack()
    
    # Data calculation methods
    def get_today_sales(self):
        """Get today's sales total"""
        try:
            today = datetime.now().date()
            sales_data = self.db_manager.get_sales_data(str(today), str(today))
            return sum(record['total_amount'] for record in sales_data)
        except:
            return 2450.00  # Sample data
    
    def get_monthly_sales(self):
        """Get current month's sales"""
        try:
            start_of_month = datetime.now().replace(day=1).date()
            today = datetime.now().date()
            sales_data = self.db_manager.get_sales_data(str(start_of_month), str(today))
            return sum(record['total_amount'] for record in sales_data)
        except:
            return 75000.00  # Sample data
    
    def get_daily_average(self):
        """Get daily average sales"""
        return self.get_monthly_sales() / datetime.now().day
    
    def get_profit_margin(self):
        """Get profit margin percentage"""
        return 15.5  # Sample data
    
    def get_revenue_by_category(self):
        """Get revenue breakdown by category"""
        return {
            "Food Sales": 45000,
            "Beverage Sales": 18000,
            "Desserts": 8000,
            "Other": 4000
        }
    
    def get_food_costs(self):
        """Get food costs"""
        return 22500.00
    
    def get_labor_costs(self):
        """Get labor costs"""
        return 18000.00
    
    def get_overhead_costs(self):
        """Get overhead costs"""
        return 12000.00
    
    def get_gross_profit(self):
        """Get gross profit"""
        return 52500.00
    
    def get_net_profit(self):
        """Get net profit"""
        return 22500.00
    
    def get_roi(self):
        """Get return on investment"""
        return 18.5
    
    def get_daily_revenue_data(self):
        """Get daily revenue data for charting"""
        # Sample data - replace with actual database query
        base_date = datetime.now() - timedelta(days=30)
        revenue_data = {}
        
        for i in range(30):
            date = base_date + timedelta(days=i)
            revenue_data[date.strftime('%m-%d')] = 1500 + (i * 50) + (i % 7 * 200)
        
        return revenue_data
    
    def refresh_dashboard(self):
        """Refresh all dashboard data"""
        messagebox.showinfo("Refreshed", "Dashboard data refreshed successfully!")
    
    def schedule_refresh(self, window):
        """Schedule automatic refresh"""
        def auto_refresh():
            if window.winfo_exists():
                self.refresh_dashboard()
                window.after(self.refresh_interval, auto_refresh)

def create_financial_dashboard(parent, db_manager):
    """Создать финансовую панель"""
    dashboard = FinancialDashboard(parent, db_manager)
    return dashboard.create_dashboard_window()
