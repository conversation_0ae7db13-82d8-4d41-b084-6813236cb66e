"""
Полностью функциональная CRM система для управления клиентами
Лояльность, история заказов, маркетин<PERSON>, аналитика клиентов
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import json
import os
from gui.styles import ModernStyles

class CustomerCRM:
    """CRM система управления клиентами"""

    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None

        # База данных клиентов
        self.customers = [
            {
                "id": 1, "name": "Иванов Алексей Петрович", "phone": "+7 (495) 123-45-67",
                "email": "i<PERSON><PERSON>@email.ru", "birthday": "1985-03-15", "registration_date": "2023-01-20",
                "total_orders": 45, "total_spent": 125600, "last_visit": "2024-01-14",
                "loyalty_points": 1256, "status": "VIP", "preferred_table": "У окна",
                "notes": "Предпочитает мясные блюда, не ест острое"
            },
            {
                "id": 2, "name": "Петрова Мария Сергеевна", "phone": "+7 (495) 234-56-78",
                "email": "<EMAIL>", "birthday": "1990-07-22", "registration_date": "2023-03-10",
                "total_orders": 28, "total_spent": 78400, "last_visit": "2024-01-13",
                "loyalty_points": 784, "status": "Постоянный", "preferred_table": "В зале",
                "notes": "Вегетарианка, любит салаты и рыбу"
            },
            {
                "id": 3, "name": "Сидоров Дмитрий Андреевич", "phone": "+7 (495) 345-67-89",
                "email": "<EMAIL>", "birthday": "1978-11-08", "registration_date": "2022-11-15",
                "total_orders": 67, "total_spent": 189300, "last_visit": "2024-01-15",
                "loyalty_points": 1893, "status": "VIP", "preferred_table": "Отдельная комната",
                "notes": "Деловые встречи, предпочитает тихую обстановку"
            },
            {
                "id": 4, "name": "Козлова Елена Викторовна", "phone": "+7 (495) 456-78-90",
                "email": "<EMAIL>", "birthday": "1995-05-30", "registration_date": "2023-06-05",
                "total_orders": 15, "total_spent": 42500, "last_visit": "2024-01-10",
                "loyalty_points": 425, "status": "Новый", "preferred_table": "Барная стойка",
                "notes": "Молодая пара, часто заказывает десерты"
            },
            {
                "id": 5, "name": "Морозов Игорь Владимирович", "phone": "+7 (495) 567-89-01",
                "email": "<EMAIL>", "birthday": "1982-12-12", "registration_date": "2023-08-20",
                "total_orders": 22, "total_spent": 65800, "last_visit": "2024-01-12",
                "loyalty_points": 658, "status": "Постоянный", "preferred_table": "Терраса",
                "notes": "Любит пиво и закуски, приходит с друзьями"
            }
        ]

        # История заказов клиентов
        self.order_history = [
            {"customer_id": 1, "date": "2024-01-14", "items": "Стейк рибай, Салат Цезарь", "amount": 1270, "table": "У окна"},
            {"customer_id": 1, "date": "2024-01-07", "items": "Борщ, Котлета по-киевски", "amount": 700, "table": "У окна"},
            {"customer_id": 2, "date": "2024-01-13", "items": "Салат с лососем, Паста", "amount": 890, "table": "В зале"},
            {"customer_id": 3, "date": "2024-01-15", "items": "Бизнес-ланч, Кофе", "amount": 1200, "table": "Отдельная комната"},
            {"customer_id": 4, "date": "2024-01-10", "items": "Десерт, Капучино", "amount": 450, "table": "Барная стойка"},
            {"customer_id": 5, "date": "2024-01-12", "items": "Пиво, Крылышки", "amount": 680, "table": "Терраса"},
        ]

        # Программа лояльности
        self.loyalty_tiers = {
            "Новый": {"min_spent": 0, "discount": 0, "points_rate": 1},
            "Постоянный": {"min_spent": 50000, "discount": 5, "points_rate": 1.2},
            "VIP": {"min_spent": 100000, "discount": 10, "points_rate": 1.5}
        }

    def create_window(self):
        """Создать окно CRM системы"""
        try:
            from utils.window_utils import create_centered_dialog
            self.window = create_centered_dialog(
                self.parent,
                "👥 CRM - Управление Клиентами",
                width=1500,
                height=950,
                resizable=True
            )
        except ImportError:
            # Fallback если utils не доступны
            self.window = tk.Toplevel(self.parent)
            self.window.title("👥 CRM - Управление Клиентами")
            self.window.geometry("1500x950")
            self.window.configure(bg=ModernStyles.COLORS['bg_main'])
            self.window.resizable(True, True)

            # Центрировать окно
            self.window.update_idletasks()
            x = (self.window.winfo_screenwidth() // 2) - (1500 // 2)
            y = (self.window.winfo_screenheight() // 2) - (950 // 2)
            self.window.geometry(f"1500x950+{x}+{y}")

        self.create_interface()

    def create_interface(self):
        """Создать интерфейс CRM системы"""
        # Заголовок
        header_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['primary'], height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="👥 CRM - Управление Клиентами",
                font=('Arial', 16, 'bold'), bg=ModernStyles.COLORS['primary'],
                fg='white').pack(side='left', padx=20, pady=15)

        # Кнопки действий
        btn_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['primary'])
        btn_frame.pack(side='right', padx=20, pady=10)

        tk.Button(btn_frame, text="➕ Новый Клиент", command=self.add_customer,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(btn_frame, text="🎁 Акции", command=self.manage_promotions,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(btn_frame, text="📊 Отчёт", command=self.generate_customer_report,
                 bg=ModernStyles.COLORS['secondary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        # Основной контент
        main_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Создать вкладки
        self.create_tabs(main_frame)

    def create_tabs(self, parent):
        """Создать вкладки CRM модуля"""
        notebook = ttk.Notebook(parent)
        notebook.pack(fill='both', expand=True)

        # Вкладка клиентов
        customers_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(customers_frame, text="👥 База Клиентов")
        self.create_customers_tab(customers_frame)

        # Вкладка истории заказов
        orders_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(orders_frame, text="📋 История Заказов")
        self.create_orders_tab(orders_frame)

        # Вкладка программы лояльности
        loyalty_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(loyalty_frame, text="⭐ Программа Лояльности")
        self.create_loyalty_tab(loyalty_frame)

        # Вкладка аналитики
        analytics_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(analytics_frame, text="📈 Аналитика Клиентов")
        self.create_analytics_tab(analytics_frame)

    def create_customers_tab(self, parent):
        """Создать вкладку базы клиентов"""
        # Заголовок
        tk.Label(parent, text="База Данных Клиентов",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)

        # Статистика клиентов
        stats_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        stats_frame.pack(fill='x', padx=20, pady=10)

        total_customers = len(self.customers)
        vip_customers = len([c for c in self.customers if c['status'] == 'VIP'])
        avg_spent = sum(c['total_spent'] for c in self.customers) / total_customers if total_customers > 0 else 0

        self.create_stat_card(stats_frame, "Всего клиентов", str(total_customers), ModernStyles.COLORS['primary'])
        self.create_stat_card(stats_frame, "VIP клиенты", str(vip_customers), ModernStyles.COLORS['warning'])
        self.create_stat_card(stats_frame, "Средний чек", f"{avg_spent:,.0f}₽", ModernStyles.COLORS['success'])

        # Поиск клиентов
        search_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        search_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(search_frame, text="Поиск:", font=('Arial', 10, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).pack(side='left')
        search_entry = tk.Entry(search_frame, font=('Arial', 10))
        search_entry.pack(side='left', padx=10)

        tk.Button(search_frame, text="🔍 Найти", command=lambda: self.search_customers(search_entry.get()),
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Arial', 9, 'bold'), relief='flat', padx=10, pady=3).pack(side='left', padx=5)

        # Таблица клиентов
        columns = ('ID', 'ФИО', 'Телефон', 'Заказов', 'Потрачено', 'Статус', 'Последний визит')
        self.customers_tree = ttk.Treeview(parent, columns=columns, show='headings', style="Modern.Treeview")

        # Настройка столбцов
        column_widths = {'ID': 50, 'ФИО': 200, 'Телефон': 130, 'Заказов': 80, 'Потрачено': 100, 'Статус': 100, 'Последний визит': 120}
        for col in columns:
            self.customers_tree.heading(col, text=col)
            self.customers_tree.column(col, width=column_widths.get(col, 100))

        # Заполнить данными
        self.refresh_customers()

        # Скроллбары
        cust_v_scroll = ttk.Scrollbar(parent, orient='vertical', command=self.customers_tree.yview)
        cust_h_scroll = ttk.Scrollbar(parent, orient='horizontal', command=self.customers_tree.xview)
        self.customers_tree.configure(yscrollcommand=cust_v_scroll.set, xscrollcommand=cust_h_scroll.set)

        self.customers_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        cust_v_scroll.pack(side='right', fill='y')
        cust_h_scroll.pack(side='bottom', fill='x')

        # Кнопки управления
        cust_btn_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        cust_btn_frame.pack(fill='x', padx=10, pady=10)

        tk.Button(cust_btn_frame, text="👁️ Профиль", command=self.view_customer_profile,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(cust_btn_frame, text="✏️ Редактировать", command=self.edit_customer,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(cust_btn_frame, text="📧 Отправить SMS", command=self.send_sms,
                 bg=ModernStyles.COLORS['secondary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

    def create_orders_tab(self, parent):
        """Создать вкладку истории заказов"""
        # Заголовок
        tk.Label(parent, text="История Заказов Клиентов",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)

        # Таблица заказов
        columns = ('Дата', 'Клиент', 'Блюда', 'Сумма', 'Стол')
        orders_tree = ttk.Treeview(parent, columns=columns, show='headings', style="Modern.Treeview")

        for col in columns:
            orders_tree.heading(col, text=col)
            orders_tree.column(col, width=150)

        # Заполнить историю заказов
        for order in self.order_history:
            customer = next((c for c in self.customers if c['id'] == order['customer_id']), None)
            customer_name = customer['name'] if customer else 'Неизвестно'

            orders_tree.insert('', 'end', values=(
                order['date'],
                customer_name,
                order['items'],
                f"{order['amount']:,}₽",
                order['table']
            ))

        orders_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def create_loyalty_tab(self, parent):
        """Создать вкладку программы лояльности"""
        # Заголовок
        tk.Label(parent, text="Программа Лояльности",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)

        # Уровни лояльности
        levels_frame = tk.LabelFrame(parent, text="Уровни Лояльности",
                                    font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        levels_frame.pack(fill='x', padx=20, pady=10)

        for tier, data in self.loyalty_tiers.items():
            tier_frame = tk.Frame(levels_frame, bg=ModernStyles.COLORS['bg_card'], relief='solid', bd=1)
            tier_frame.pack(fill='x', padx=10, pady=5)

            tk.Label(tier_frame, text=f"🏆 {tier}", font=('Arial', 12, 'bold'),
                    bg=ModernStyles.COLORS['bg_card']).pack(side='left', padx=10, pady=10)

            info_text = f"От {data['min_spent']:,}₽ • Скидка {data['discount']}% • Бонусы x{data['points_rate']}"
            tk.Label(tier_frame, text=info_text, font=('Arial', 10),
                    bg=ModernStyles.COLORS['bg_card']).pack(side='right', padx=10, pady=10)

        # Топ клиенты по баллам
        top_frame = tk.LabelFrame(parent, text="Топ Клиенты по Баллам Лояльности",
                                 font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        top_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Сортировать по баллам
        sorted_customers = sorted(self.customers, key=lambda x: x['loyalty_points'], reverse=True)

        for i, customer in enumerate(sorted_customers[:5]):
            row = tk.Frame(top_frame, bg=ModernStyles.COLORS['bg_main'])
            row.pack(fill='x', padx=10, pady=5)

            # Позиция и медаль
            medal = "🥇" if i == 0 else "🥈" if i == 1 else "🥉" if i == 2 else f"{i+1}."
            tk.Label(row, text=medal, font=('Arial', 12, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).pack(side='left')

            # Имя и статус
            tk.Label(row, text=f"{customer['name']} ({customer['status']})", font=('Arial', 11),
                    bg=ModernStyles.COLORS['bg_main']).pack(side='left', padx=10)

            # Баллы
            tk.Label(row, text=f"⭐ {customer['loyalty_points']} баллов", font=('Arial', 11, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).pack(side='right')

    def create_analytics_tab(self, parent):
        """Создать вкладку аналитики клиентов"""
        # Заголовок
        tk.Label(parent, text="Аналитика Клиентов",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)

        # Общая статистика
        general_stats_frame = tk.LabelFrame(parent, text="Общая Статистика",
                                           font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        general_stats_frame.pack(fill='x', padx=20, pady=10)

        # Рассчитать статистику
        total_revenue = sum(c['total_spent'] for c in self.customers)
        avg_order_value = sum(o['amount'] for o in self.order_history) / len(self.order_history) if self.order_history else 0
        repeat_customers = len([c for c in self.customers if c['total_orders'] > 1])

        stats_text = f"""
Общая статистика клиентской базы:
• Всего клиентов: {len(self.customers)}
• Общая выручка: {total_revenue:,}₽
• Средний чек: {avg_order_value:,.0f}₽
• Повторные клиенты: {repeat_customers} ({repeat_customers/len(self.customers)*100:.1f}%)
• VIP клиенты: {len([c for c in self.customers if c['status'] == 'VIP'])}
        """

        tk.Label(general_stats_frame, text=stats_text, font=('Arial', 11),
                bg=ModernStyles.COLORS['bg_main'], justify='left').pack(padx=20, pady=20)

        # Анализ по статусам
        status_frame = tk.LabelFrame(parent, text="Анализ по Статусам Клиентов",
                                    font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        status_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Группировка по статусам
        status_stats = {}
        for customer in self.customers:
            status = customer['status']
            if status not in status_stats:
                status_stats[status] = {'count': 0, 'total_spent': 0, 'avg_orders': 0}

            status_stats[status]['count'] += 1
            status_stats[status]['total_spent'] += customer['total_spent']
            status_stats[status]['avg_orders'] += customer['total_orders']

        # Рассчитать средние значения
        for status in status_stats:
            if status_stats[status]['count'] > 0:
                status_stats[status]['avg_orders'] /= status_stats[status]['count']

        # Отобразить статистику по статусам
        for status, stats in status_stats.items():
            row = tk.Frame(status_frame, bg=ModernStyles.COLORS['bg_main'])
            row.pack(fill='x', padx=10, pady=5)

            status_icon = "👑" if status == "VIP" else "⭐" if status == "Постоянный" else "👤"

            tk.Label(row, text=f"{status_icon} {status}", font=('Arial', 12, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).pack(side='left')

            info_text = f"{stats['count']} клиентов • {stats['total_spent']:,}₽ • {stats['avg_orders']:.1f} заказов"
            tk.Label(row, text=info_text, font=('Arial', 11),
                    bg=ModernStyles.COLORS['bg_main']).pack(side='right')

    def create_stat_card(self, parent, title, value, color):
        """Создать карточку статистики"""
        card = tk.Frame(parent, bg=color, relief='solid', bd=1)
        card.pack(side='left', fill='both', expand=True, padx=5)

        tk.Label(card, text=title, font=('Arial', 10), bg=color, fg='white').pack(pady=(15, 5))
        tk.Label(card, text=value, font=('Arial', 14, 'bold'), bg=color, fg='white').pack(pady=(0, 15))

    def refresh_customers(self):
        """Обновить список клиентов"""
        for item in self.customers_tree.get_children():
            self.customers_tree.delete(item)

        for customer in self.customers:
            status_icon = "👑" if customer['status'] == "VIP" else "⭐" if customer['status'] == "Постоянный" else "👤"

            self.customers_tree.insert('', 'end', values=(
                customer['id'],
                customer['name'],
                customer['phone'],
                customer['total_orders'],
                f"{customer['total_spent']:,}₽",
                f"{status_icon} {customer['status']}",
                customer['last_visit']
            ))

    def search_customers(self, query):
        """Поиск клиентов"""
        if not query:
            self.refresh_customers()
            return

        # Очистить таблицу
        for item in self.customers_tree.get_children():
            self.customers_tree.delete(item)

        # Найти совпадения
        for customer in self.customers:
            if (query.lower() in customer['name'].lower() or
                query in customer['phone'] or
                query.lower() in customer['email'].lower()):

                status_icon = "👑" if customer['status'] == "VIP" else "⭐" if customer['status'] == "Постоянный" else "👤"

                self.customers_tree.insert('', 'end', values=(
                    customer['id'],
                    customer['name'],
                    customer['phone'],
                    customer['total_orders'],
                    f"{customer['total_spent']:,}₽",
                    f"{status_icon} {customer['status']}",
                    customer['last_visit']
                ))

    def add_customer(self):
        """Добавить нового клиента"""
        try:
            from utils.window_utils import create_centered_dialog
            customer_window = create_centered_dialog(
                self.window,
                "➕ Новый Клиент",
                width=700,
                height=700,
                resizable=True
            )
        except ImportError:
            # Fallback если utils не доступны
            customer_window = tk.Toplevel(self.window)
            customer_window.title("➕ Новый Клиент")
            customer_window.geometry("700x700")
            customer_window.configure(bg=ModernStyles.COLORS['bg_main'])
            customer_window.resizable(True, True)

            # Центрировать окно
            customer_window.update_idletasks()
            x = (customer_window.winfo_screenwidth() // 2) - (700 // 2)
            y = (customer_window.winfo_screenheight() // 2) - (700 // 2)
            customer_window.geometry(f"700x700+{x}+{y}")

        # Заголовок
        tk.Label(customer_window, text="Добавить Нового Клиента",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

        # Форма
        form_frame = tk.Frame(customer_window, bg=ModernStyles.COLORS['bg_main'])
        form_frame.pack(fill='both', expand=True, padx=20)

        # Поля формы
        fields = [
            ("ФИО:", "name"),
            ("Телефон:", "phone"),
            ("Email:", "email"),
            ("Дата рождения:", "birthday"),
            ("Предпочитаемый стол:", "preferred_table"),
            ("Заметки:", "notes")
        ]

        entries = {}
        for i, (label, field) in enumerate(fields):
            tk.Label(form_frame, text=label, font=('Arial', 10, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).grid(row=i, column=0, sticky='w', pady=5)

            if field == "notes":
                entry = tk.Text(form_frame, font=('Arial', 10), height=3, width=30)
            else:
                entry = tk.Entry(form_frame, font=('Arial', 10))

            entry.grid(row=i, column=1, sticky='ew', padx=(10, 0), pady=5)
            entries[field] = entry

        # Предзаполнить дату рождения
        entries['birthday'].insert(0, "ГГГГ-ММ-ДД")

        form_frame.grid_columnconfigure(1, weight=1)

        # Кнопки
        btn_frame = tk.Frame(customer_window, bg=ModernStyles.COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=20, pady=20)

        def save_customer():
            try:
                notes_text = entries['notes'].get('1.0', 'end-1c') if hasattr(entries['notes'], 'get') else entries['notes'].get()

                new_customer = {
                    "id": max(c['id'] for c in self.customers) + 1,
                    "name": entries['name'].get(),
                    "phone": entries['phone'].get(),
                    "email": entries['email'].get(),
                    "birthday": entries['birthday'].get(),
                    "registration_date": datetime.now().strftime("%Y-%m-%d"),
                    "total_orders": 0,
                    "total_spent": 0,
                    "last_visit": "Не было",
                    "loyalty_points": 0,
                    "status": "Новый",
                    "preferred_table": entries['preferred_table'].get(),
                    "notes": notes_text
                }

                self.customers.append(new_customer)
                self.refresh_customers()
                customer_window.destroy()
                messagebox.showinfo("Успех", "Клиент добавлен в базу")

            except Exception as e:
                messagebox.showerror("Ошибка", f"Ошибка добавления клиента: {e}")

        tk.Button(btn_frame, text="💾 Сохранить", command=save_customer,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(side='left')

        tk.Button(btn_frame, text="❌ Отмена", command=customer_window.destroy,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(side='right')

    def view_customer_profile(self):
        """Просмотр профиля клиента"""
        selection = self.customers_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите клиента для просмотра профиля")
            return

        # Получить ID клиента
        item = self.customers_tree.item(selection[0])
        customer_id = int(item['values'][0])
        customer = next((c for c in self.customers if c['id'] == customer_id), None)

        if not customer:
            messagebox.showerror("Ошибка", "Клиент не найден")
            return

        # Создать окно профиля
        profile_window = tk.Toplevel(self.window)
        profile_window.title(f"👤 Профиль: {customer['name']}")
        profile_window.geometry("600x700")
        profile_window.configure(bg='white')

        # Заголовок
        header = tk.Frame(profile_window, bg=ModernStyles.COLORS['primary'], height=80)
        header.pack(fill='x')
        header.pack_propagate(False)

        status_icon = "👑" if customer['status'] == "VIP" else "⭐" if customer['status'] == "Постоянный" else "👤"
        tk.Label(header, text=f"{status_icon} {customer['name']}", font=('Arial', 16, 'bold'),
                bg=ModernStyles.COLORS['primary'], fg='white').pack(pady=20)

        # Информация о клиенте
        info_frame = tk.Frame(profile_window, bg='white')
        info_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Основная информация
        basic_info = [
            ("📞 Телефон:", customer['phone']),
            ("📧 Email:", customer['email']),
            ("🎂 День рождения:", customer['birthday']),
            ("📅 Дата регистрации:", customer['registration_date']),
            ("🏆 Статус:", customer['status']),
            ("⭐ Баллы лояльности:", str(customer['loyalty_points'])),
            ("🍽️ Всего заказов:", str(customer['total_orders'])),
            ("💰 Потрачено:", f"{customer['total_spent']:,}₽"),
            ("📍 Последний визит:", customer['last_visit']),
            ("🪑 Предпочитаемый стол:", customer['preferred_table'])
        ]

        for label, value in basic_info:
            row = tk.Frame(info_frame, bg='white')
            row.pack(fill='x', pady=5)

            tk.Label(row, text=label, font=('Arial', 11, 'bold'), bg='white').pack(side='left')
            tk.Label(row, text=value, font=('Arial', 11), bg='white').pack(side='left', padx=10)

        # Заметки
        if customer['notes']:
            notes_frame = tk.LabelFrame(info_frame, text="Заметки", font=('Arial', 12, 'bold'), bg='white')
            notes_frame.pack(fill='x', pady=20)

            tk.Label(notes_frame, text=customer['notes'], font=('Arial', 10), bg='white',
                    wraplength=500, justify='left').pack(padx=10, pady=10)

        # История заказов клиента
        orders_frame = tk.LabelFrame(info_frame, text="История Заказов", font=('Arial', 12, 'bold'), bg='white')
        orders_frame.pack(fill='both', expand=True, pady=20)

        customer_orders = [o for o in self.order_history if o['customer_id'] == customer_id]

        if customer_orders:
            for order in customer_orders[-5:]:  # Последние 5 заказов
                order_row = tk.Frame(orders_frame, bg='white')
                order_row.pack(fill='x', padx=10, pady=2)

                tk.Label(order_row, text=order['date'], font=('Arial', 10), bg='white').pack(side='left')
                tk.Label(order_row, text=order['items'], font=('Arial', 10), bg='white').pack(side='left', padx=20)
                tk.Label(order_row, text=f"{order['amount']:,}₽", font=('Arial', 10, 'bold'), bg='white').pack(side='right')
        else:
            tk.Label(orders_frame, text="Заказов пока нет", font=('Arial', 10), bg='white').pack(pady=20)

    def edit_customer(self):
        """Редактировать клиента"""
        selection = self.customers_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите клиента для редактирования")
            return

        messagebox.showinfo("В разработке", "Функция редактирования клиента будет реализована в следующей версии")

    def send_sms(self):
        """Отправить SMS клиенту"""
        selection = self.customers_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите клиента для отправки SMS")
            return

        messagebox.showinfo("В разработке", "Функция отправки SMS будет реализована в следующей версии")

    def manage_promotions(self):
        """Управление акциями"""
        promo_window = tk.Toplevel(self.window)
        promo_window.title("🎁 Управление Акциями")
        promo_window.geometry("600x500")
        promo_window.configure(bg='white')

        # Заголовок
        tk.Label(promo_window, text="🎁 Управление Акциями и Предложениями",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        # Текущие акции
        promos_frame = tk.LabelFrame(promo_window, text="Текущие Акции", font=('Arial', 12, 'bold'), bg='white')
        promos_frame.pack(fill='both', expand=True, padx=20, pady=20)

        current_promos = [
            "🎂 Скидка 20% в день рождения",
            "👑 VIP клиентам - бесплатный десерт",
            "⭐ За 10 посещений - скидка 15%",
            "📱 При заказе через приложение - скидка 5%",
            "👥 Приведи друга - скидка 10% обоим"
        ]

        for promo in current_promos:
            tk.Label(promos_frame, text=promo, font=('Arial', 11), bg='white').pack(anchor='w', padx=20, pady=5)

        # Кнопки управления
        btn_frame = tk.Frame(promo_window, bg='white')
        btn_frame.pack(fill='x', padx=20, pady=20)

        tk.Button(btn_frame, text="➕ Новая Акция", command=lambda: messagebox.showinfo("В разработке", "Функция будет добавлена"),
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(side='left')

        tk.Button(btn_frame, text="📧 Рассылка", command=lambda: messagebox.showinfo("В разработке", "Функция будет добавлена"),
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(side='right')

    def generate_customer_report(self):
        """Генерировать отчёт по клиентам"""
        report_window = tk.Toplevel(self.window)
        report_window.title("📊 Отчёт по Клиентам")
        report_window.geometry("600x500")
        report_window.configure(bg='white')

        # Заголовок
        tk.Label(report_window, text="📊 Отчёт по Клиентской Базе",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        # Содержимое отчёта
        report_text = tk.Text(report_window, font=('Arial', 10), wrap='word')
        report_text.pack(fill='both', expand=True, padx=20, pady=20)

        # Генерировать отчёт
        total_customers = len(self.customers)
        total_revenue = sum(c['total_spent'] for c in self.customers)
        vip_customers = len([c for c in self.customers if c['status'] == 'VIP'])
        avg_spent = total_revenue / total_customers if total_customers > 0 else 0

        report_content = f"""
ОТЧЁТ ПО КЛИЕНТСКОЙ БАЗЕ
========================

Дата формирования: {datetime.now().strftime('%d.%m.%Y %H:%M')}

ОБЩАЯ СТАТИСТИКА:
• Всего клиентов: {total_customers}
• Общая выручка: {total_revenue:,}₽
• Средние траты на клиента: {avg_spent:,.0f}₽
• VIP клиенты: {vip_customers} ({vip_customers/total_customers*100:.1f}%)

ТОП КЛИЕНТЫ ПО ТРАТАМ:
"""

        sorted_customers = sorted(self.customers, key=lambda x: x['total_spent'], reverse=True)
        for i, customer in enumerate(sorted_customers[:5]):
            report_content += f"{i+1}. {customer['name']} - {customer['total_spent']:,}₽ ({customer['status']})\n"

        report_content += f"""

АНАЛИЗ ПО СТАТУСАМ:
• Новые клиенты: {len([c for c in self.customers if c['status'] == 'Новый'])}
• Постоянные клиенты: {len([c for c in self.customers if c['status'] == 'Постоянный'])}
• VIP клиенты: {len([c for c in self.customers if c['status'] == 'VIP'])}

РЕКОМЕНДАЦИИ:
• Развивать программу лояльности для удержания клиентов
• Проводить персональные акции для VIP клиентов
• Работать с новыми клиентами для повышения их статуса
• Анализировать предпочтения клиентов для улучшения сервиса
        """

        report_text.insert('1.0', report_content)
        report_text.config(state='disabled')

def create_customer_crm(parent, db_manager):
    """Создать CRM систему"""
    crm = CustomerCRM(parent, db_manager)
    crm.create_window()
    return crm