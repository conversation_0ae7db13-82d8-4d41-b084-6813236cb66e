"""
Advanced Analytics Dashboard
Provides comprehensive business intelligence and machine learning insights
"""

import tkinter as tk
from tkinter import ttk, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gui.styles import ModernStyles
from analytics.ml_engine import AnalyticsEngine
from utils.error_handler import handle_module_error, log_info

class AdvancedAnalyticsWindow:
    """Advanced Analytics Dashboard with ML insights"""

    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.analytics_engine = AnalyticsEngine(db_manager.get_connection())

        # Create main window
        self.window = tk.Toplevel(parent)
        self.window.title("🔮 Расширенная Аналитика и ИИ")
        self.window.geometry("1400x900")
        self.window.configure(bg=ModernStyles.COLORS['bg_primary'])

        # Center window
        self.window.transient(parent)
        self.window.grab_set()

        # Configure matplotlib for better appearance
        plt.style.use('default')
        plt.rcParams.update({
            'font.size': 10,
            'axes.titlesize': 12,
            'axes.labelsize': 10,
            'xtick.labelsize': 9,
            'ytick.labelsize': 9,
            'legend.fontsize': 9,
            'figure.titlesize': 14
        })

        self.create_widgets()
        self.load_analytics_data()

        log_info("Расширенная аналитика открыта", "AdvancedAnalytics")

    def create_widgets(self):
        """Create the analytics dashboard interface"""
        # Main container
        main_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Title
        title_label = tk.Label(
            main_frame,
            text="🔮 Расширенная Аналитика и Машинное Обучение",
            font=ModernStyles.FONTS['title'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        title_label.pack(pady=(0, 20))

        # Create notebook for different analytics sections
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill='both', expand=True)

        # Sales Analytics Tab
        self.create_sales_analytics_tab()

        # Inventory Analytics Tab
        self.create_inventory_analytics_tab()

        # Customer Analytics Tab
        self.create_customer_analytics_tab()

        # Predictions Tab
        self.create_predictions_tab()

        # Control buttons
        self.create_control_buttons(main_frame)

    def create_sales_analytics_tab(self):
        """Create sales analytics tab"""
        sales_frame = ttk.Frame(self.notebook)
        self.notebook.add(sales_frame, text="📊 Анализ Продаж")

        # Create scrollable frame
        canvas = tk.Canvas(sales_frame, bg=ModernStyles.COLORS['bg_secondary'])
        scrollbar = ttk.Scrollbar(sales_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Sales metrics frame
        metrics_frame = tk.LabelFrame(
            scrollable_frame,
            text="Ключевые Метрики Продаж",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        metrics_frame.pack(fill='x', padx=10, pady=10)

        # Metrics display
        self.sales_metrics_frame = tk.Frame(metrics_frame, bg=ModernStyles.COLORS['bg_secondary'])
        self.sales_metrics_frame.pack(fill='x', padx=10, pady=10)

        # Sales chart frame
        chart_frame = tk.LabelFrame(
            scrollable_frame,
            text="График Продаж и Тренды",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        chart_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Chart container
        self.sales_chart_frame = tk.Frame(chart_frame, bg=ModernStyles.COLORS['bg_secondary'])
        self.sales_chart_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Insights frame
        insights_frame = tk.LabelFrame(
            scrollable_frame,
            text="ИИ Инсайты и Рекомендации",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        insights_frame.pack(fill='x', padx=10, pady=10)

        self.sales_insights_text = tk.Text(
            insights_frame,
            height=6,
            font=ModernStyles.FONTS['body'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary'],
            wrap=tk.WORD
        )
        self.sales_insights_text.pack(fill='x', padx=10, pady=10)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_inventory_analytics_tab(self):
        """Create inventory analytics tab"""
        inventory_frame = ttk.Frame(self.notebook)
        self.notebook.add(inventory_frame, text="📦 Анализ Склада")

        # Inventory optimization frame
        optimization_frame = tk.LabelFrame(
            inventory_frame,
            text="Оптимизация Запасов",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        optimization_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Create treeview for inventory analysis
        columns = ('Товар', 'Текущий Запас', 'Статус', 'Рекомендация')
        self.inventory_tree = ttk.Treeview(optimization_frame, columns=columns, show='headings', height=15)

        for col in columns:
            self.inventory_tree.heading(col, text=col)
            self.inventory_tree.column(col, width=200)

        # Scrollbars for inventory tree
        inv_v_scrollbar = ttk.Scrollbar(optimization_frame, orient="vertical", command=self.inventory_tree.yview)
        inv_h_scrollbar = ttk.Scrollbar(optimization_frame, orient="horizontal", command=self.inventory_tree.xview)
        self.inventory_tree.configure(yscrollcommand=inv_v_scrollbar.set, xscrollcommand=inv_h_scrollbar.set)

        self.inventory_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        inv_v_scrollbar.pack(side="right", fill="y", pady=10)
        inv_h_scrollbar.pack(side="bottom", fill="x", padx=10)

        # Inventory insights
        inv_insights_frame = tk.LabelFrame(
            inventory_frame,
            text="Рекомендации по Складу",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        inv_insights_frame.pack(fill='x', padx=10, pady=10)

        self.inventory_insights_text = tk.Text(
            inv_insights_frame,
            height=6,
            font=ModernStyles.FONTS['body'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary'],
            wrap=tk.WORD
        )
        self.inventory_insights_text.pack(fill='x', padx=10, pady=10)

    def create_customer_analytics_tab(self):
        """Create customer analytics tab"""
        customer_frame = ttk.Frame(self.notebook)
        self.notebook.add(customer_frame, text="👥 Анализ Клиентов")

        # Customer segments frame
        segments_frame = tk.LabelFrame(
            customer_frame,
            text="Сегментация Клиентов",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        segments_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Customer metrics
        self.customer_metrics_frame = tk.Frame(segments_frame, bg=ModernStyles.COLORS['bg_secondary'])
        self.customer_metrics_frame.pack(fill='x', padx=10, pady=10)

        # Top customers table
        top_customers_frame = tk.LabelFrame(
            customer_frame,
            text="Топ Клиенты",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        top_customers_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Create treeview for top customers
        cust_columns = ('Имя', 'Телефон', 'Заказов', 'Потрачено', 'Средний Чек')
        self.customers_tree = ttk.Treeview(top_customers_frame, columns=cust_columns, show='headings', height=10)

        for col in cust_columns:
            self.customers_tree.heading(col, text=col)
            self.customers_tree.column(col, width=150)

        self.customers_tree.pack(fill='both', expand=True, padx=10, pady=10)

        # Customer insights
        cust_insights_frame = tk.LabelFrame(
            customer_frame,
            text="Рекомендации по Работе с Клиентами",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        cust_insights_frame.pack(fill='x', padx=10, pady=10)

        self.customer_insights_text = tk.Text(
            cust_insights_frame,
            height=5,
            font=ModernStyles.FONTS['body'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary'],
            wrap=tk.WORD
        )
        self.customer_insights_text.pack(fill='x', padx=10, pady=10)

    def create_predictions_tab(self):
        """Create predictions and forecasting tab"""
        predictions_frame = ttk.Frame(self.notebook)
        self.notebook.add(predictions_frame, text="🔮 Прогнозы")

        # Predictions chart frame
        pred_chart_frame = tk.LabelFrame(
            predictions_frame,
            text="Прогноз Продаж на 7 Дней",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        pred_chart_frame.pack(fill='both', expand=True, padx=10, pady=10)

        self.predictions_chart_frame = tk.Frame(pred_chart_frame, bg=ModernStyles.COLORS['bg_secondary'])
        self.predictions_chart_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Predictions insights
        pred_insights_frame = tk.LabelFrame(
            predictions_frame,
            text="Прогнозы и Рекомендации",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        pred_insights_frame.pack(fill='x', padx=10, pady=10)

        self.predictions_insights_text = tk.Text(
            pred_insights_frame,
            height=8,
            font=ModernStyles.FONTS['body'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary'],
            wrap=tk.WORD
        )
        self.predictions_insights_text.pack(fill='x', padx=10, pady=10)

    def create_control_buttons(self, parent):
        """Create control buttons"""
        button_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_primary'])
        button_frame.pack(fill='x', pady=(20, 0))

        # Refresh button
        refresh_btn = tk.Button(
            button_frame,
            text="🔄 Обновить Аналитику",
            command=self.load_analytics_data,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['accent'],
            fg='white',
            relief='flat',
            padx=20,
            pady=10
        )
        refresh_btn.pack(side='left', padx=(0, 10))

        # Export button
        export_btn = tk.Button(
            button_frame,
            text="📊 Экспорт Отчета",
            command=self.export_analytics_report,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['success'],
            fg='white',
            relief='flat',
            padx=20,
            pady=10
        )
        export_btn.pack(side='left', padx=(0, 10))

        # Close button
        close_btn = tk.Button(
            button_frame,
            text="❌ Закрыть",
            command=self.window.destroy,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['danger'],
            fg='white',
            relief='flat',
            padx=20,
            pady=10
        )
        close_btn.pack(side='right')

    def load_analytics_data(self):
        """Load and display all analytics data"""
        try:
            # Load sales analytics
            self.load_sales_analytics()

            # Load inventory analytics
            self.load_inventory_analytics()

            # Load customer analytics
            self.load_customer_analytics()

            # Load predictions
            self.load_predictions()

            messagebox.showinfo("Успех", "Аналитические данные обновлены")

        except Exception as e:
            handle_module_error(e, "Расширенная Аналитика", "загрузка данных")

    def load_sales_analytics(self):
        """Load sales analytics data"""
        try:
            # Get sales analysis
            sales_data = self.analytics_engine.analyze_sales_trends(30)

            if "error" in sales_data:
                self.display_error_message(self.sales_metrics_frame, sales_data["error"])
                return

            # Display metrics
            self.display_sales_metrics(sales_data)

            # Create sales chart
            self.create_sales_chart(sales_data)

            # Display insights
            self.display_sales_insights(sales_data)

        except Exception as e:
            self.display_error_message(self.sales_metrics_frame, f"Ошибка загрузки данных о продажах: {e}")

    def load_inventory_analytics(self):
        """Load inventory analytics data"""
        try:
            # Get inventory analysis
            inventory_data = self.analytics_engine.analyze_inventory_optimization()

            if "error" in inventory_data:
                self.display_error_message(self.inventory_tree.master, inventory_data["error"])
                return

            # Display inventory data
            self.display_inventory_data(inventory_data)

            # Display insights
            self.display_inventory_insights(inventory_data)

        except Exception as e:
            self.display_error_message(self.inventory_tree.master, f"Ошибка анализа склада: {e}")

    def load_customer_analytics(self):
        """Load customer analytics data"""
        try:
            # Get customer analysis
            customer_data = self.analytics_engine.analyze_customer_behavior()

            if "error" in customer_data:
                self.display_error_message(self.customer_metrics_frame, customer_data["error"])
                return

            # Display customer metrics
            self.display_customer_metrics(customer_data)

            # Display top customers
            self.display_top_customers(customer_data)

            # Display insights
            self.display_customer_insights(customer_data)

        except Exception as e:
            self.display_error_message(self.customer_metrics_frame, f"Ошибка анализа клиентов: {e}")

    def load_predictions(self):
        """Load predictions and forecasting data"""
        try:
            # Get sales data for predictions
            sales_data = self.analytics_engine.analyze_sales_trends(30)

            if "error" in sales_data:
                self.display_error_message(self.predictions_chart_frame, sales_data["error"])
                return

            # Create predictions chart
            self.create_predictions_chart(sales_data)

            # Display predictions insights
            self.display_predictions_insights(sales_data)

        except Exception as e:
            self.display_error_message(self.predictions_chart_frame, f"Ошибка создания прогнозов: {e}")

    def display_sales_metrics(self, sales_data):
        """Display sales metrics"""
        # Clear existing widgets
        for widget in self.sales_metrics_frame.winfo_children():
            widget.destroy()

        # Create metrics cards
        metrics = [
            ("Общая Выручка", f"{sales_data.get('total_sales', 0):,.2f} руб", "💰"),
            ("Всего Заказов", f"{sales_data.get('total_orders', 0):,}", "📋"),
            ("Средняя Дневная Выручка", f"{sales_data.get('average_daily_sales', 0):,.2f} руб", "📊"),
            ("Тренд", sales_data.get('trend', {}).get('direction', 'неизвестно'), "📈")
        ]

        for i, (title, value, icon) in enumerate(metrics):
            card_frame = tk.Frame(self.sales_metrics_frame, bg=ModernStyles.COLORS['bg_primary'], relief='raised', bd=1)
            card_frame.grid(row=0, column=i, padx=10, pady=10, sticky='ew')

            icon_label = tk.Label(card_frame, text=icon, font=('Arial', 24), bg=ModernStyles.COLORS['bg_primary'])
            icon_label.pack(pady=(10, 5))

            title_label = tk.Label(card_frame, text=title, font=ModernStyles.FONTS['body'],
                                 bg=ModernStyles.COLORS['bg_primary'], fg=ModernStyles.COLORS['text_secondary'])
            title_label.pack()

            value_label = tk.Label(card_frame, text=value, font=ModernStyles.FONTS['heading'],
                                 bg=ModernStyles.COLORS['bg_primary'], fg=ModernStyles.COLORS['text_primary'])
            value_label.pack(pady=(5, 10))

        # Configure grid weights
        for i in range(len(metrics)):
            self.sales_metrics_frame.grid_columnconfigure(i, weight=1)

    def create_sales_chart(self, sales_data):
        """Create sales trend chart"""
        # Clear existing chart
        for widget in self.sales_chart_frame.winfo_children():
            widget.destroy()

        try:
            # Create figure
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
            fig.patch.set_facecolor('#f0f0f0')

            # Sample data for demonstration (in real implementation, get from sales_data)
            dates = [datetime.now() - timedelta(days=i) for i in range(30, 0, -1)]
            sales = [1000 + i*50 + (i%7)*200 for i in range(30)]  # Sample sales data

            # Sales trend
            ax1.plot(dates, sales, linewidth=2, color='#2E86AB', marker='o', markersize=4)
            ax1.set_title('Тренд Продаж (30 дней)', fontsize=14, fontweight='bold')
            ax1.set_ylabel('Выручка (руб)')
            ax1.grid(True, alpha=0.3)
            ax1.xaxis.set_major_formatter(mdates.DateFormatter('%d.%m'))

            # Moving average
            if len(sales) >= 7:
                from analytics.ml_engine import MovingAverage
                ma_sales = MovingAverage.simple_moving_average(sales, 7)
                ax1.plot(dates, ma_sales, linewidth=2, color='#A23B72', linestyle='--', label='Скользящее среднее (7 дней)')
                ax1.legend()

            # Predictions
            if 'predictions' in sales_data and 'predictions' in sales_data['predictions']:
                pred_dates = [dates[-1] + timedelta(days=i+1) for i in range(7)]
                predictions = sales_data['predictions']['predictions']

                ax2.plot(dates[-7:], sales[-7:], linewidth=2, color='#2E86AB', marker='o', label='Фактические')
                ax2.plot(pred_dates, predictions, linewidth=2, color='#F18F01', marker='s', linestyle='--', label='Прогноз')
                ax2.set_title('Прогноз Продаж на 7 дней', fontsize=14, fontweight='bold')
                ax2.set_ylabel('Выручка (руб)')
                ax2.grid(True, alpha=0.3)
                ax2.legend()
                ax2.xaxis.set_major_formatter(mdates.DateFormatter('%d.%m'))

            plt.tight_layout()

            # Embed chart
            canvas = FigureCanvasTkAgg(fig, self.sales_chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)

        except Exception as e:
            error_label = tk.Label(self.sales_chart_frame, text=f"Ошибка создания графика: {e}",
                                 font=ModernStyles.FONTS['body'], fg='red')
            error_label.pack(pady=20)

    def display_sales_insights(self, sales_data):
        """Display sales insights and recommendations"""
        self.sales_insights_text.delete(1.0, tk.END)

        insights = sales_data.get('insights', [])

        if insights:
            self.sales_insights_text.insert(tk.END, "🔍 АНАЛИЗ ПРОДАЖ:\n\n")
            for insight in insights:
                self.sales_insights_text.insert(tk.END, f"• {insight}\n")

        # Add trend analysis
        trend = sales_data.get('trend', {})
        if trend:
            self.sales_insights_text.insert(tk.END, f"\n📈 ТРЕНД АНАЛИЗ:\n")
            self.sales_insights_text.insert(tk.END, f"• Направление: {trend.get('direction', 'неизвестно')}\n")
            self.sales_insights_text.insert(tk.END, f"• Сила тренда: {trend.get('strength', 0):.2f}\n")

        # Add seasonal patterns
        seasonal = sales_data.get('seasonal_patterns', {})
        if seasonal and 'insights' in seasonal:
            self.sales_insights_text.insert(tk.END, f"\n📅 СЕЗОННЫЕ ПАТТЕРНЫ:\n")
            for insight in seasonal['insights']:
                self.sales_insights_text.insert(tk.END, f"• {insight}\n")

    def display_inventory_data(self, inventory_data):
        """Display inventory optimization data"""
        # Clear existing data
        for item in self.inventory_tree.get_children():
            self.inventory_tree.delete(item)

        # Add low stock items
        low_stock = inventory_data.get('low_stock_items', [])
        for item in low_stock:
            self.inventory_tree.insert('', 'end', values=(
                item['item'],
                item['current'],
                '🔴 Низкий запас',
                f"Пополнить на {item['shortage']} единиц"
            ))

        # Add overstock items
        overstock = inventory_data.get('overstock_items', [])
        for item in overstock:
            self.inventory_tree.insert('', 'end', values=(
                item['item'],
                item['current'],
                '🟡 Избыток',
                f"Избыток: {item['excess']} единиц"
            ))

        # Add optimal items (sample)
        optimal_count = inventory_data.get('optimal_stock_count', 0)
        if optimal_count > 0:
            self.inventory_tree.insert('', 'end', values=(
                f"Оптимальных товаров: {optimal_count}",
                "-",
                '✅ Оптимально',
                "Запасы в норме"
            ))

    def display_inventory_insights(self, inventory_data):
        """Display inventory insights"""
        self.inventory_insights_text.delete(1.0, tk.END)

        insights = inventory_data.get('insights', [])
        recommendations = inventory_data.get('recommendations', [])

        if insights:
            self.inventory_insights_text.insert(tk.END, "📦 АНАЛИЗ СКЛАДА:\n\n")
            for insight in insights:
                self.inventory_insights_text.insert(tk.END, f"• {insight}\n")

        if recommendations:
            self.inventory_insights_text.insert(tk.END, f"\n💡 РЕКОМЕНДАЦИИ:\n")
            for rec in recommendations:
                self.inventory_insights_text.insert(tk.END, f"• {rec}\n")

    def display_customer_metrics(self, customer_data):
        """Display customer metrics"""
        # Clear existing widgets
        for widget in self.customer_metrics_frame.winfo_children():
            widget.destroy()

        # Create customer metrics cards
        metrics = [
            ("Всего Клиентов", f"{customer_data.get('total_customers', 0):,}", "👥"),
            ("VIP Клиенты", f"{len(customer_data.get('high_value_customers', []))}", "👑"),
            ("Постоянные", f"{customer_data.get('regular_customers_count', 0)}", "🎯"),
            ("Новые", f"{customer_data.get('new_customers_count', 0)}", "🆕")
        ]

        for i, (title, value, icon) in enumerate(metrics):
            card_frame = tk.Frame(self.customer_metrics_frame, bg=ModernStyles.COLORS['bg_primary'], relief='raised', bd=1)
            card_frame.grid(row=0, column=i, padx=10, pady=10, sticky='ew')

            icon_label = tk.Label(card_frame, text=icon, font=('Arial', 20), bg=ModernStyles.COLORS['bg_primary'])
            icon_label.pack(pady=(10, 5))

            title_label = tk.Label(card_frame, text=title, font=ModernStyles.FONTS['body'],
                                 bg=ModernStyles.COLORS['bg_primary'], fg=ModernStyles.COLORS['text_secondary'])
            title_label.pack()

            value_label = tk.Label(card_frame, text=value, font=ModernStyles.FONTS['heading'],
                                 bg=ModernStyles.COLORS['bg_primary'], fg=ModernStyles.COLORS['text_primary'])
            value_label.pack(pady=(5, 10))

        # Configure grid weights
        for i in range(len(metrics)):
            self.customer_metrics_frame.grid_columnconfigure(i, weight=1)

    def display_top_customers(self, customer_data):
        """Display top customers"""
        # Clear existing data
        for item in self.customers_tree.get_children():
            self.customers_tree.delete(item)

        # Add high value customers
        high_value = customer_data.get('high_value_customers', [])
        for customer in high_value:
            self.customers_tree.insert('', 'end', values=(
                customer.get('name', 'Неизвестно'),
                customer.get('phone', ''),
                customer.get('order_count', 0),
                f"{customer.get('total_spent', 0):,.2f} руб",
                f"{customer.get('avg_order', 0):,.2f} руб"
            ))

    def display_customer_insights(self, customer_data):
        """Display customer insights"""
        self.customer_insights_text.delete(1.0, tk.END)

        insights = customer_data.get('insights', [])
        recommendations = customer_data.get('recommendations', [])

        if insights:
            self.customer_insights_text.insert(tk.END, "👥 АНАЛИЗ КЛИЕНТОВ:\n\n")
            for insight in insights:
                self.customer_insights_text.insert(tk.END, f"• {insight}\n")

        if recommendations:
            self.customer_insights_text.insert(tk.END, f"\n💡 РЕКОМЕНДАЦИИ:\n")
            for rec in recommendations:
                self.customer_insights_text.insert(tk.END, f"• {rec}\n")

    def create_predictions_chart(self, sales_data):
        """Create predictions chart"""
        # Clear existing chart
        for widget in self.predictions_chart_frame.winfo_children():
            widget.destroy()

        try:
            predictions = sales_data.get('predictions', {})
            if not predictions or 'predictions' not in predictions:
                error_label = tk.Label(self.predictions_chart_frame, text="Недостаточно данных для прогнозирования",
                                     font=ModernStyles.FONTS['body'], fg='orange')
                error_label.pack(pady=20)
                return

            # Create figure
            fig, ax = plt.subplots(figsize=(12, 6))
            fig.patch.set_facecolor('#f0f0f0')

            # Historical data (sample)
            hist_dates = [datetime.now() - timedelta(days=i) for i in range(7, 0, -1)]
            hist_sales = [1000 + i*100 for i in range(7)]  # Sample data

            # Prediction data
            pred_dates = [datetime.now() + timedelta(days=i+1) for i in range(7)]
            pred_sales = predictions['predictions']

            # Plot historical and predictions
            ax.plot(hist_dates, hist_sales, linewidth=3, color='#2E86AB', marker='o', markersize=6, label='Исторические данные')
            ax.plot(pred_dates, pred_sales, linewidth=3, color='#F18F01', marker='s', markersize=6, linestyle='--', label='Прогноз')

            # Add confidence interval (simple implementation)
            confidence = predictions.get('confidence', 0.7)
            pred_upper = [p * (1 + (1-confidence)) for p in pred_sales]
            pred_lower = [p * (1 - (1-confidence)) for p in pred_sales]
            ax.fill_between(pred_dates, pred_lower, pred_upper, alpha=0.2, color='#F18F01', label='Доверительный интервал')

            ax.set_title('Прогноз Продаж на 7 дней', fontsize=16, fontweight='bold')
            ax.set_ylabel('Выручка (руб)', fontsize=12)
            ax.grid(True, alpha=0.3)
            ax.legend(fontsize=10)
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%d.%m'))

            plt.xticks(rotation=45)
            plt.tight_layout()

            # Embed chart
            canvas = FigureCanvasTkAgg(fig, self.predictions_chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)

        except Exception as e:
            error_label = tk.Label(self.predictions_chart_frame, text=f"Ошибка создания прогноза: {e}",
                                 font=ModernStyles.FONTS['body'], fg='red')
            error_label.pack(pady=20)

    def display_predictions_insights(self, sales_data):
        """Display predictions insights"""
        self.predictions_insights_text.delete(1.0, tk.END)

        predictions = sales_data.get('predictions', {})

        if predictions:
            self.predictions_insights_text.insert(tk.END, "🔮 ПРОГНОЗЫ И РЕКОМЕНДАЦИИ:\n\n")

            method = predictions.get('method', 'неизвестно')
            confidence = predictions.get('confidence', 0)
            period = predictions.get('period', 'неизвестно')

            self.predictions_insights_text.insert(tk.END, f"📊 Метод прогнозирования: {method}\n")
            self.predictions_insights_text.insert(tk.END, f"🎯 Уровень доверия: {confidence:.1%}\n")
            self.predictions_insights_text.insert(tk.END, f"📅 Период прогноза: {period}\n\n")

            # Add trend-based recommendations
            trend = sales_data.get('trend', {})
            if trend:
                direction = trend.get('direction', 'неизвестно')
                if direction == 'increasing':
                    self.predictions_insights_text.insert(tk.END, "📈 РЕКОМЕНДАЦИИ (Растущий тренд):\n")
                    self.predictions_insights_text.insert(tk.END, "• Увеличить запасы популярных товаров\n")
                    self.predictions_insights_text.insert(tk.END, "• Подготовить дополнительный персонал\n")
                    self.predictions_insights_text.insert(tk.END, "• Рассмотреть расширение меню\n")
                elif direction == 'decreasing':
                    self.predictions_insights_text.insert(tk.END, "📉 РЕКОМЕНДАЦИИ (Снижающийся тренд):\n")
                    self.predictions_insights_text.insert(tk.END, "• Запустить маркетинговые акции\n")
                    self.predictions_insights_text.insert(tk.END, "• Оптимизировать расходы\n")
                    self.predictions_insights_text.insert(tk.END, "• Проанализировать причины снижения\n")
                else:
                    self.predictions_insights_text.insert(tk.END, "📊 РЕКОМЕНДАЦИИ (Стабильный тренд):\n")
                    self.predictions_insights_text.insert(tk.END, "• Поддерживать текущий уровень запасов\n")
                    self.predictions_insights_text.insert(tk.END, "• Фокус на качестве обслуживания\n")
                    self.predictions_insights_text.insert(tk.END, "• Искать возможности для роста\n")

    def display_error_message(self, parent, message):
        """Display error message in a frame"""
        for widget in parent.winfo_children():
            widget.destroy()

        error_label = tk.Label(parent, text=f"⚠️ {message}",
                             font=ModernStyles.FONTS['body'], fg='red')
        error_label.pack(pady=20)

    def export_analytics_report(self):
        """Export analytics report to file"""
        try:
            from tkinter import filedialog
            import json

            # Get all analytics data
            sales_data = self.analytics_engine.analyze_sales_trends(30)
            inventory_data = self.analytics_engine.analyze_inventory_optimization()
            customer_data = self.analytics_engine.analyze_customer_behavior()

            # Create comprehensive report
            report = {
                "report_date": datetime.now().isoformat(),
                "sales_analytics": sales_data,
                "inventory_analytics": inventory_data,
                "customer_analytics": customer_data
            }

            # Ask user for save location
            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                title="Сохранить отчет аналитики"
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(report, f, ensure_ascii=False, indent=2)

                messagebox.showinfo("Успех", f"Отчет сохранен: {filename}")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось экспортировать отчет: {e}")

def show_advanced_analytics(parent, db_manager):
    """Show advanced analytics window"""
    try:
        AdvancedAnalyticsWindow(parent, db_manager)
    except Exception as e:
        handle_module_error(e, "Расширенная Аналитика", "открытие окна")