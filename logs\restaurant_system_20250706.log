2025-07-06 12:04:09,420 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 12:04:09,422 - INFO - logger - Backup system initialized
2025-07-06 12:04:19,599 - INFO - logger - Authentication cancelled or failed
2025-07-06 12:12:47,324 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 12:12:47,327 - INFO - logger - Backup system initialized
2025-07-06 12:13:08,011 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T12:13:08.011074"}
2025-07-06 12:13:08,402 - INFO - logger - Notification system initialized
2025-07-06 12:13:08,430 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 12:30:09,950 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 12:30:09,954 - INFO - logger - Backup system initialized
2025-07-06 12:58:43,594 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 12:58:43,596 - INFO - logger - Backup system initialized
2025-07-06 12:58:53,322 - INFO - logger - Authentication cancelled or failed
2025-07-06 12:59:14,190 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 12:59:14,192 - INFO - logger - Backup system initialized
2025-07-06 12:59:28,289 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T12:59:28.289892"}
2025-07-06 12:59:28,701 - INFO - logger - Notification system initialized
2025-07-06 12:59:28,735 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 14:13:15,929 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 14:13:15,934 - INFO - logger - Backup system initialized
2025-07-06 14:13:45,728 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T14:13:45.727782"}
2025-07-06 14:13:46,229 - INFO - logger - Notification system initialized
2025-07-06 14:13:46,271 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 14:22:27,382 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 14:22:27,385 - INFO - logger - Backup system initialized
2025-07-06 14:22:45,980 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T14:22:45.979944"}
2025-07-06 14:22:46,394 - INFO - logger - Notification system initialized
2025-07-06 14:22:46,420 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 14:36:03,805 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 14:36:03,807 - INFO - logger - Backup system initialized
2025-07-06 14:36:14,342 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T14:36:14.342707"}
2025-07-06 14:36:14,789 - INFO - logger - Notification system initialized
2025-07-06 14:36:14,818 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 14:48:08,205 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 14:48:08,207 - INFO - logger - Backup system initialized
2025-07-06 14:48:20,871 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T14:48:20.871610"}
2025-07-06 14:48:21,302 - INFO - logger - Notification system initialized
2025-07-06 14:48:21,328 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 14:54:43,804 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 14:54:43,805 - INFO - logger - Backup system initialized
2025-07-06 14:55:02,039 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T14:55:02.039033"}
2025-07-06 14:55:02,415 - INFO - logger - Notification system initialized
2025-07-06 14:55:02,440 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 15:23:42,387 - INFO - sync_manager - Sync status changed to: offline
2025-07-06 15:23:42,389 - ERROR - connection_pool - Query execution error: no such table: menu_items
2025-07-06 15:23:42,389 - ERROR - connection_pool - Query: SELECT * FROM menu_items
2025-07-06 15:23:42,389 - ERROR - connection_pool - Params: None
2025-07-06 15:23:42,390 - ERROR - offline_manager - Error caching table menu_items: no such table: menu_items
2025-07-06 15:23:42,390 - ERROR - connection_pool - Query execution error: no such table: inventory
2025-07-06 15:23:42,391 - ERROR - connection_pool - Query: SELECT * FROM inventory
2025-07-06 15:23:42,391 - ERROR - connection_pool - Params: None
2025-07-06 15:23:42,391 - ERROR - offline_manager - Error caching table inventory: no such table: inventory
2025-07-06 15:23:42,391 - ERROR - connection_pool - Query execution error: no such table: staff
2025-07-06 15:23:42,391 - ERROR - connection_pool - Query: SELECT * FROM staff
2025-07-06 15:23:42,391 - ERROR - connection_pool - Params: None
2025-07-06 15:23:42,391 - ERROR - offline_manager - Error caching table staff: no such table: staff
2025-07-06 15:23:42,392 - ERROR - connection_pool - Query execution error: no such table: payment_methods
2025-07-06 15:23:42,392 - ERROR - connection_pool - Query: SELECT * FROM payment_methods
2025-07-06 15:23:42,392 - ERROR - connection_pool - Params: None
2025-07-06 15:23:42,393 - ERROR - offline_manager - Error caching table payment_methods: no such table: payment_methods
2025-07-06 15:23:42,393 - ERROR - connection_pool - Query execution error: no such table: departments
2025-07-06 15:23:42,393 - ERROR - connection_pool - Query: SELECT * FROM departments
2025-07-06 15:23:42,393 - ERROR - connection_pool - Params: None
2025-07-06 15:23:42,394 - ERROR - offline_manager - Error caching table departments: no such table: departments
2025-07-06 15:23:42,403 - INFO - offline_manager - Switched to offline mode
2025-07-06 15:23:42,408 - INFO - sync_manager - Sync status changed to: online
2025-07-06 15:23:42,409 - INFO - offline_manager - Switched to online mode after 0:00:00.019953
2025-07-06 15:23:42,409 - INFO - offline_manager - Processing 1 offline operations
2025-07-06 15:23:42,410 - ERROR - connection_pool - Query execution error: no such table: menu_items
2025-07-06 15:23:42,410 - ERROR - connection_pool - Query: INSERT INTO menu_items (name, price, category, created_at) VALUES (?, ?, ?, ?)
2025-07-06 15:23:42,410 - ERROR - connection_pool - Params: ('Test Menu Item', 15.99, 'Main Course', '2025-07-06T15:23:42.403603')
2025-07-06 15:23:42,410 - ERROR - offline_manager - Error applying offline operation: no such table: menu_items
2025-07-06 15:23:42,414 - INFO - offline_manager - Processed offline operations: 0 success, 1 failed
2025-07-06 15:26:07,137 - INFO - sync_manager - Sync status changed to: offline
2025-07-06 15:26:07,138 - ERROR - connection_pool - Query execution error: no such table: menu_items
2025-07-06 15:26:07,138 - ERROR - connection_pool - Query: SELECT * FROM menu_items
2025-07-06 15:26:07,139 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,139 - ERROR - offline_manager - Error caching table menu_items: no such table: menu_items
2025-07-06 15:26:07,139 - ERROR - connection_pool - Query execution error: no such table: inventory
2025-07-06 15:26:07,139 - ERROR - connection_pool - Query: SELECT * FROM inventory
2025-07-06 15:26:07,139 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,139 - ERROR - offline_manager - Error caching table inventory: no such table: inventory
2025-07-06 15:26:07,140 - ERROR - connection_pool - Query execution error: no such table: staff
2025-07-06 15:26:07,140 - ERROR - connection_pool - Query: SELECT * FROM staff
2025-07-06 15:26:07,140 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,140 - ERROR - offline_manager - Error caching table staff: no such table: staff
2025-07-06 15:26:07,141 - ERROR - connection_pool - Query execution error: no such table: payment_methods
2025-07-06 15:26:07,141 - ERROR - connection_pool - Query: SELECT * FROM payment_methods
2025-07-06 15:26:07,141 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,141 - ERROR - offline_manager - Error caching table payment_methods: no such table: payment_methods
2025-07-06 15:26:07,142 - ERROR - connection_pool - Query execution error: no such table: departments
2025-07-06 15:26:07,142 - ERROR - connection_pool - Query: SELECT * FROM departments
2025-07-06 15:26:07,142 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,142 - ERROR - offline_manager - Error caching table departments: no such table: departments
2025-07-06 15:26:07,150 - INFO - offline_manager - Switched to offline mode
2025-07-06 15:26:07,155 - INFO - sync_manager - Sync status changed to: online
2025-07-06 15:26:07,155 - INFO - offline_manager - Switched to online mode after 0:00:00.016808
2025-07-06 15:26:07,155 - INFO - offline_manager - Processing 1 offline operations
2025-07-06 15:26:07,156 - ERROR - connection_pool - Query execution error: no such table: menu_items
2025-07-06 15:26:07,156 - ERROR - connection_pool - Query: INSERT INTO menu_items (name, price, category, created_at) VALUES (?, ?, ?, ?)
2025-07-06 15:26:07,156 - ERROR - connection_pool - Params: ('Test Menu Item', 15.99, 'Main Course', '2025-07-06T15:26:07.150800')
2025-07-06 15:26:07,156 - ERROR - offline_manager - Error applying offline operation: no such table: menu_items
2025-07-06 15:26:07,160 - INFO - offline_manager - Processed offline operations: 0 success, 1 failed
2025-07-06 15:26:07,179 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 15:26:07,180 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 15:26:07,180 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,180 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 15:26:07,181 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 15:26:07,181 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 15:26:07,181 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,181 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 15:26:07,181 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 15:26:07,182 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 15:26:07,182 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,182 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 15:26:07,182 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 15:26:07,182 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 15:26:07,182 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,183 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 15:26:07,183 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 15:26:07,183 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 15:26:07,183 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,183 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 15:26:07,183 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 15:26:07,184 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 15:26:07,184 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,184 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 15:26:07,184 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 15:26:07,184 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 15:26:07,184 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,185 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 15:26:07,185 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 15:26:07,185 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 15:26:07,185 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,185 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 15:26:07,186 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 15:26:07,186 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 15:26:07,186 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,186 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 15:26:07,186 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 15:26:07,187 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 15:26:07,187 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,187 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 15:26:07,187 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 15:26:07,187 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 15:26:07,187 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,188 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 15:26:07,188 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 15:26:07,188 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 15:26:07,188 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,188 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 15:26:07,189 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 15:26:07,189 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 15:26:07,189 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,189 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 15:26:07,189 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 15:26:07,189 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 15:26:07,189 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,190 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 15:26:07,190 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 15:26:07,190 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 15:26:07,190 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,190 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 15:26:07,190 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 15:26:07,191 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 15:26:07,191 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,191 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 15:26:07,191 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 15:26:07,191 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 15:26:07,191 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,191 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 15:26:07,191 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 15:26:07,192 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 15:26:07,192 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,192 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 15:26:07,193 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 15:26:07,193 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 15:26:07,194 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 15:26:07,194 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 15:26:07,194 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 15:26:07,194 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 15:26:07,194 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 15:26:07,195 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 15:26:07,195 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 15:26:07,195 - INFO - sync_manager - Data synchronization service started
2025-07-06 15:26:07,198 - INFO - offline_manager - Offline storage initialized
2025-07-06 15:26:07,199 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 15:26:07,199 - INFO - sync_manager - Sync status changed to: offline
2025-07-06 15:26:07,200 - ERROR - connection_pool - Query execution error: no such table: menu_items
2025-07-06 15:26:07,200 - ERROR - connection_pool - Query: SELECT * FROM menu_items
2025-07-06 15:26:07,200 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,200 - ERROR - offline_manager - Error caching table menu_items: no such table: menu_items
2025-07-06 15:26:07,201 - ERROR - connection_pool - Query execution error: no such table: inventory
2025-07-06 15:26:07,201 - ERROR - connection_pool - Query: SELECT * FROM inventory
2025-07-06 15:26:07,201 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,201 - ERROR - offline_manager - Error caching table inventory: no such table: inventory
2025-07-06 15:26:07,201 - ERROR - connection_pool - Query execution error: no such table: staff
2025-07-06 15:26:07,201 - ERROR - connection_pool - Query: SELECT * FROM staff
2025-07-06 15:26:07,202 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,202 - ERROR - offline_manager - Error caching table staff: no such table: staff
2025-07-06 15:26:07,202 - ERROR - connection_pool - Query execution error: no such table: payment_methods
2025-07-06 15:26:07,202 - ERROR - connection_pool - Query: SELECT * FROM payment_methods
2025-07-06 15:26:07,202 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,202 - ERROR - offline_manager - Error caching table payment_methods: no such table: payment_methods
2025-07-06 15:26:07,203 - ERROR - connection_pool - Query execution error: no such table: departments
2025-07-06 15:26:07,203 - ERROR - connection_pool - Query: SELECT * FROM departments
2025-07-06 15:26:07,203 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,203 - ERROR - offline_manager - Error caching table departments: no such table: departments
2025-07-06 15:26:07,209 - INFO - offline_manager - Switched to offline mode
2025-07-06 15:26:07,210 - INFO - sync_manager - Sync status changed to: online
2025-07-06 15:26:07,210 - INFO - offline_manager - Switched to online mode after 0:00:00.010346
2025-07-06 15:55:07,605 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 15:55:07,608 - INFO - logger - Backup system initialized
2025-07-06 16:03:42,279 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:03:42,281 - INFO - logger - Backup system initialized
2025-07-06 16:03:55,465 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:03:55.465193"}
2025-07-06 16:03:55,617 - INFO - query_optimizer - Created essential index: idx_sales_order_date
2025-07-06 16:03:55,620 - INFO - query_optimizer - Created essential index: idx_sales_payment_method
2025-07-06 16:03:55,622 - INFO - query_optimizer - Created essential index: idx_sales_department
2025-07-06 16:03:55,625 - INFO - query_optimizer - Created essential index: idx_sales_dish_code
2025-07-06 16:03:55,628 - INFO - query_optimizer - Created essential index: idx_sales_order_number
2025-07-06 16:03:55,630 - INFO - query_optimizer - Created essential index: idx_sales_composite
2025-07-06 16:03:55,632 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:03:55,632 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:03:55,632 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,633 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:03:55,635 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:03:55,635 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:03:55,635 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,636 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:03:55,637 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:03:55,638 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:03:55,639 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,642 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:03:55,643 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:03:55,644 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:03:55,644 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,644 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:03:55,645 - INFO - query_optimizer - Created essential index: idx_recipes_name
2025-07-06 16:03:55,647 - INFO - query_optimizer - Created essential index: idx_recipes_category
2025-07-06 16:03:55,649 - INFO - query_optimizer - Created essential index: idx_recipe_ingredients_recipe
2025-07-06 16:03:55,651 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:03:55,651 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:03:55,651 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,652 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:03:55,654 - INFO - query_optimizer - Created essential index: idx_purchase_orders_date
2025-07-06 16:03:55,656 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:03:55,656 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:03:55,657 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,657 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:03:55,660 - INFO - query_optimizer - Created essential index: idx_purchase_orders_status
2025-07-06 16:03:55,661 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:03:55,661 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:03:55,662 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,662 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:03:55,664 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:03:55,665 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:03:55,665 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,665 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:03:55,667 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:03:55,667 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:03:55,668 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,668 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:03:55,670 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:03:55,672 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:03:55,672 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,673 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:03:55,674 - INFO - query_optimizer - Created essential index: idx_customers_phone
2025-07-06 16:03:55,677 - INFO - query_optimizer - Created essential index: idx_customers_email
2025-07-06 16:03:55,678 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:03:55,679 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:03:55,679 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,680 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:03:55,682 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:03:55,682 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:03:55,683 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,683 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:03:55,685 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:03:55,685 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:03:55,685 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,686 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:03:55,687 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:03:55,688 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:03:55,689 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,689 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:03:55,690 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:03:55,690 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:03:55,690 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,691 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:03:55,691 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:03:55,692 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:03:55,692 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,693 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:03:55,693 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:03:55,693 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:03:55,694 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,694 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:03:55,695 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:03:55,695 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:03:55,695 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,696 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:03:55,713 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:03:55,714 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:03:55,715 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:03:55,715 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:03:55,716 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:03:55,716 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:03:55,717 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:03:55,717 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:03:55,718 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:03:55,719 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:03:55,721 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:03:55,724 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:03:56,529 - INFO - logger - Notification system initialized
2025-07-06 16:03:56,583 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:12:30,363 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:12:30,366 - INFO - logger - Backup system initialized
2025-07-06 16:12:42,074 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:12:42.074052"}
2025-07-06 16:12:42,152 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:12:42,152 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:12:42,152 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,152 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:12:42,153 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:12:42,153 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:12:42,153 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,153 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:12:42,153 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:12:42,154 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:12:42,154 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,154 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:12:42,154 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:12:42,154 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:12:42,154 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,155 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:12:42,155 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:12:42,155 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:12:42,155 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,155 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:12:42,155 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:12:42,155 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:12:42,155 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,156 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:12:42,156 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:12:42,156 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:12:42,156 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,156 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:12:42,156 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:12:42,156 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:12:42,156 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,156 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:12:42,156 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:12:42,157 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:12:42,157 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,157 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:12:42,157 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:12:42,157 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:12:42,157 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,157 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:12:42,157 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:12:42,158 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:12:42,158 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,158 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:12:42,158 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:12:42,158 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:12:42,159 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,159 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:12:42,159 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:12:42,159 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:12:42,159 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,159 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:12:42,160 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:12:42,160 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:12:42,160 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,160 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:12:42,161 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:12:42,161 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:12:42,161 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,161 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:12:42,161 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:12:42,162 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:12:42,162 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,162 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:12:42,163 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:12:42,163 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:12:42,163 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,163 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:12:42,163 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:12:42,163 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:12:42,163 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,164 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:12:42,165 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:12:42,166 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:12:42,166 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:12:42,166 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:12:42,167 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:12:42,167 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:12:42,167 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:12:42,167 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:12:42,168 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:12:42,168 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:12:42,169 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:12:42,170 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:12:42,567 - INFO - logger - Notification system initialized
2025-07-06 16:12:42,601 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:22:16,928 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:22:16,930 - INFO - logger - Backup system initialized
2025-07-06 16:22:26,809 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:22:26.808856"}
2025-07-06 16:22:27,028 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:22:27,029 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:22:27,029 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,030 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:22:27,031 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:22:27,031 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:22:27,031 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,032 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:22:27,032 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:22:27,032 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:22:27,032 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,033 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:22:27,033 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:22:27,034 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:22:27,034 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,035 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:22:27,035 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:22:27,036 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:22:27,036 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,036 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:22:27,037 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:22:27,037 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:22:27,037 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,038 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:22:27,038 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:22:27,038 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:22:27,038 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,039 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:22:27,039 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:22:27,040 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:22:27,040 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,040 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:22:27,041 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:22:27,041 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:22:27,041 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,042 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:22:27,042 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:22:27,042 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:22:27,043 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,043 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:22:27,044 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:22:27,044 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:22:27,044 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,044 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:22:27,045 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:22:27,045 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:22:27,045 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,046 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:22:27,047 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:22:27,047 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:22:27,048 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,048 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:22:27,049 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:22:27,049 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:22:27,049 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,049 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:22:27,050 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:22:27,050 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:22:27,050 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,051 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:22:27,051 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:22:27,051 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:22:27,052 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,052 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:22:27,052 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:22:27,053 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:22:27,053 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,053 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:22:27,053 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:22:27,054 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:22:27,054 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,054 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:22:27,058 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:22:27,058 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:22:27,059 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:22:27,059 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:22:27,060 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:22:27,061 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:22:27,061 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:22:27,061 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:22:27,062 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:22:27,063 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:22:27,065 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:22:27,067 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:22:27,999 - INFO - logger - Notification system initialized
2025-07-06 16:22:28,054 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:23:21,371 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:23:21,373 - INFO - logger - Backup system initialized
2025-07-06 16:23:30,565 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:23:30.565308"}
2025-07-06 16:23:30,671 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:23:30,671 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:23:30,671 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,671 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:23:30,672 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:23:30,672 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:23:30,672 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,672 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:23:30,673 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:23:30,673 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:23:30,673 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,673 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:23:30,673 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:23:30,673 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:23:30,673 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,674 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:23:30,674 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:23:30,674 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:23:30,674 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,674 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:23:30,675 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:23:30,675 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:23:30,675 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,676 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:23:30,676 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:23:30,676 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:23:30,676 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,677 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:23:30,677 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:23:30,677 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:23:30,677 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,677 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:23:30,678 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:23:30,678 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:23:30,678 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,678 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:23:30,679 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:23:30,679 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:23:30,679 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,679 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:23:30,680 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:23:30,680 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:23:30,680 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,680 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:23:30,680 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:23:30,680 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:23:30,681 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,681 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:23:30,681 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:23:30,682 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:23:30,682 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,682 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:23:30,682 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:23:30,682 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:23:30,683 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,683 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:23:30,683 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:23:30,684 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:23:30,684 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,684 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:23:30,684 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:23:30,684 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:23:30,684 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,684 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:23:30,685 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:23:30,685 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:23:30,685 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,685 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:23:30,685 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:23:30,685 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:23:30,685 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,686 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:23:30,689 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:23:30,689 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:23:30,690 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:23:30,690 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:23:30,691 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:23:30,691 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:23:30,692 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:23:30,692 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:23:30,692 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:23:30,693 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:23:30,694 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:23:30,695 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:23:31,115 - INFO - logger - Notification system initialized
2025-07-06 16:23:31,140 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:30:27,479 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:30:27,481 - INFO - logger - Backup system initialized
2025-07-06 16:30:41,481 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:30:41.481683"}
2025-07-06 16:30:41,590 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:30:41,591 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:30:41,591 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,591 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:30:41,591 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:30:41,592 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:30:41,592 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,592 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:30:41,592 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:30:41,592 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:30:41,593 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,593 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:30:41,593 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:30:41,593 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:30:41,593 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,594 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:30:41,594 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:30:41,594 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:30:41,594 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,594 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:30:41,594 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:30:41,594 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:30:41,594 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,594 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:30:41,595 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:30:41,595 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:30:41,595 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,595 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:30:41,595 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:30:41,595 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:30:41,595 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,595 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:30:41,596 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:30:41,596 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:30:41,596 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,596 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:30:41,596 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:30:41,596 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:30:41,596 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,596 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:30:41,597 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:30:41,597 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:30:41,597 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,597 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:30:41,598 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:30:41,598 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:30:41,598 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,598 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:30:41,598 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:30:41,598 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:30:41,598 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,598 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:30:41,599 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:30:41,599 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:30:41,599 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,599 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:30:41,600 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:30:41,600 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:30:41,600 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,600 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:30:41,600 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:30:41,600 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:30:41,600 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,601 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:30:41,601 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:30:41,601 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:30:41,601 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,601 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:30:41,601 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:30:41,602 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:30:41,602 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,602 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:30:41,604 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:30:41,605 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:30:41,605 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:30:41,605 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:30:41,605 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:30:41,605 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:30:41,606 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:30:41,606 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:30:41,606 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:30:41,606 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:30:41,607 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:30:41,608 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:30:42,030 - INFO - logger - Notification system initialized
2025-07-06 16:30:42,069 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:31:22,217 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:31:22,219 - INFO - logger - Backup system initialized
2025-07-06 16:31:35,881 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:31:35.881297"}
2025-07-06 16:31:35,955 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:31:35,956 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:31:35,956 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,956 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:31:35,956 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:31:35,957 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:31:35,957 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,957 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:31:35,957 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:31:35,957 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:31:35,957 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,958 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:31:35,958 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:31:35,958 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:31:35,958 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,958 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:31:35,958 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:31:35,958 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:31:35,959 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,959 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:31:35,959 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:31:35,959 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:31:35,960 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,960 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:31:35,960 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:31:35,960 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:31:35,960 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,961 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:31:35,961 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:31:35,961 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:31:35,961 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,961 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:31:35,962 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:31:35,962 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:31:35,962 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,962 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:31:35,962 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:31:35,963 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:31:35,963 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,963 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:31:35,963 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:31:35,963 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:31:35,963 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,964 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:31:35,964 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:31:35,964 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:31:35,964 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,964 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:31:35,965 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:31:35,965 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:31:35,965 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,965 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:31:35,966 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:31:35,966 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:31:35,966 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,966 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:31:35,966 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:31:35,967 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:31:35,967 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,967 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:31:35,967 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:31:35,967 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:31:35,967 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,968 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:31:35,968 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:31:35,968 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:31:35,968 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,968 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:31:35,969 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:31:35,969 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:31:35,969 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,969 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:31:35,971 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:31:35,972 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:31:35,972 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:31:35,973 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:31:35,973 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:31:35,973 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:31:35,973 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:31:35,974 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:31:35,974 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:31:35,974 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:31:35,975 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:31:35,976 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:31:36,339 - INFO - logger - Notification system initialized
2025-07-06 16:31:36,367 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:32:20,837 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:32:20,839 - INFO - logger - Backup system initialized
2025-07-06 16:32:31,910 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:32:31.910134"}
2025-07-06 16:32:31,981 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:32:31,982 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:32:31,982 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,982 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:32:31,982 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:32:31,983 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:32:31,983 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,983 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:32:31,983 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:32:31,984 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:32:31,984 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,984 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:32:31,984 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:32:31,985 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:32:31,985 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,985 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:32:31,985 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:32:31,985 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:32:31,986 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,986 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:32:31,986 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:32:31,987 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:32:31,987 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,987 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:32:31,988 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:32:31,988 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:32:31,988 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,988 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:32:31,988 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:32:31,989 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:32:31,989 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,989 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:32:31,989 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:32:31,989 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:32:31,990 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,990 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:32:31,990 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:32:31,990 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:32:31,990 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,991 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:32:31,991 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:32:31,991 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:32:31,991 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,991 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:32:31,991 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:32:31,992 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:32:31,992 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,992 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:32:31,992 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:32:31,992 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:32:31,992 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,992 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:32:31,992 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:32:31,992 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:32:31,992 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,993 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:32:31,993 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:32:31,993 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:32:31,993 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,993 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:32:31,993 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:32:31,993 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:32:31,993 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,993 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:32:31,994 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:32:31,994 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:32:31,994 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,994 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:32:31,994 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:32:31,994 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:32:31,995 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,995 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:32:31,997 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:32:31,997 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:32:31,998 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:32:31,998 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:32:31,998 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:32:31,998 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:32:31,998 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:32:31,999 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:32:31,999 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:32:31,999 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:32:32,000 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:32:32,001 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:32:32,360 - INFO - logger - Notification system initialized
2025-07-06 16:32:32,385 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:35:11,373 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:35:11,375 - INFO - logger - Backup system initialized
2025-07-06 16:35:21,972 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:35:21.972577"}
2025-07-06 16:35:22,053 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:35:22,054 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:35:22,054 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,054 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:35:22,054 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:35:22,055 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:35:22,055 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,055 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:35:22,055 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:35:22,056 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:35:22,056 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,056 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:35:22,056 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:35:22,056 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:35:22,057 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,057 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:35:22,057 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:35:22,057 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:35:22,057 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,057 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:35:22,057 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:35:22,058 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:35:22,058 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,058 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:35:22,058 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:35:22,058 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:35:22,058 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,058 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:35:22,058 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:35:22,058 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:35:22,059 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,059 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:35:22,059 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:35:22,059 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:35:22,059 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,060 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:35:22,060 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:35:22,060 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:35:22,060 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,060 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:35:22,061 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:35:22,061 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:35:22,061 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,061 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:35:22,061 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:35:22,061 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:35:22,061 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,061 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:35:22,061 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:35:22,062 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:35:22,062 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,062 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:35:22,062 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:35:22,062 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:35:22,062 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,063 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:35:22,063 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:35:22,063 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:35:22,063 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,063 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:35:22,063 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:35:22,063 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:35:22,064 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,064 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:35:22,064 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:35:22,064 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:35:22,064 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,064 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:35:22,064 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:35:22,065 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:35:22,065 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,065 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:35:22,067 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:35:22,067 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:35:22,068 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:35:22,068 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:35:22,068 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:35:22,068 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:35:22,068 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:35:22,069 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:35:22,069 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:35:22,070 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:35:22,071 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:35:22,072 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:35:22,450 - INFO - logger - Notification system initialized
2025-07-06 16:35:22,471 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:37:18,259 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:37:18,261 - INFO - logger - Backup system initialized
2025-07-06 16:37:29,180 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:37:29.180193"}
2025-07-06 16:37:29,274 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:37:29,275 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:37:29,275 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,275 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:37:29,276 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:37:29,276 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:37:29,276 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,276 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:37:29,276 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:37:29,277 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:37:29,277 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,277 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:37:29,277 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:37:29,277 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:37:29,277 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,278 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:37:29,278 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:37:29,278 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:37:29,278 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,278 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:37:29,278 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:37:29,278 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:37:29,279 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,279 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:37:29,279 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:37:29,279 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:37:29,279 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,279 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:37:29,280 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:37:29,280 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:37:29,280 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,280 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:37:29,280 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:37:29,281 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:37:29,281 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,281 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:37:29,281 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:37:29,281 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:37:29,281 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,282 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:37:29,282 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:37:29,282 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:37:29,282 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,282 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:37:29,282 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:37:29,283 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:37:29,283 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,283 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:37:29,283 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:37:29,284 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:37:29,284 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,284 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:37:29,284 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:37:29,285 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:37:29,285 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,285 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:37:29,285 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:37:29,285 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:37:29,285 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,286 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:37:29,286 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:37:29,286 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:37:29,286 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,286 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:37:29,287 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:37:29,287 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:37:29,287 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,287 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:37:29,287 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:37:29,287 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:37:29,288 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,288 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:37:29,289 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:37:29,290 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:37:29,290 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:37:29,290 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:37:29,291 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:37:29,291 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:37:29,291 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:37:29,292 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:37:29,292 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:37:29,292 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:37:29,293 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:37:29,295 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:37:29,649 - INFO - logger - Notification system initialized
2025-07-06 16:37:29,674 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:41:47,566 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:41:47,568 - INFO - logger - Backup system initialized
2025-07-06 16:41:58,597 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:41:58.597621"}
2025-07-06 16:41:58,684 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:41:58,684 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:41:58,684 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,685 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:41:58,685 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:41:58,685 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:41:58,686 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,686 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:41:58,686 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:41:58,686 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:41:58,687 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,687 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:41:58,687 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:41:58,687 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:41:58,687 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,688 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:41:58,688 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:41:58,688 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:41:58,688 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,688 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:41:58,689 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:41:58,689 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:41:58,689 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,689 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:41:58,689 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:41:58,690 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:41:58,690 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,690 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:41:58,690 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:41:58,690 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:41:58,690 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,690 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:41:58,691 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:41:58,691 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:41:58,691 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,691 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:41:58,691 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:41:58,691 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:41:58,691 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,692 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:41:58,692 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:41:58,692 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:41:58,692 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,692 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:41:58,692 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:41:58,693 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:41:58,693 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,693 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:41:58,693 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:41:58,693 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:41:58,693 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,693 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:41:58,694 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:41:58,694 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:41:58,694 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,694 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:41:58,694 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:41:58,695 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:41:58,695 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,695 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:41:58,695 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:41:58,696 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:41:58,696 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,696 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:41:58,696 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:41:58,696 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:41:58,696 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,696 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:41:58,697 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:41:58,697 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:41:58,697 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,697 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:41:58,700 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:41:58,700 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:41:58,700 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:41:58,701 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:41:58,701 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:41:58,702 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:41:58,702 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:41:58,702 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:41:58,702 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:41:58,703 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:41:58,704 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:41:58,705 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:41:59,101 - INFO - logger - Notification system initialized
2025-07-06 16:41:59,128 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:45:47,459 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:45:47,461 - INFO - logger - Backup system initialized
2025-07-06 16:45:56,668 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:45:56.668746"}
2025-07-06 16:45:56,747 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:45:56,747 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:45:56,747 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,747 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:45:56,748 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:45:56,748 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:45:56,748 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,749 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:45:56,749 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:45:56,749 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:45:56,749 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,750 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:45:56,750 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:45:56,750 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:45:56,750 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,751 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:45:56,751 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:45:56,751 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:45:56,751 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,752 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:45:56,752 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:45:56,752 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:45:56,752 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,753 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:45:56,753 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:45:56,753 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:45:56,753 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,753 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:45:56,753 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:45:56,754 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:45:56,754 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,754 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:45:56,754 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:45:56,754 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:45:56,754 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,754 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:45:56,755 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:45:56,755 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:45:56,755 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,755 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:45:56,755 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:45:56,755 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:45:56,755 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,755 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:45:56,756 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:45:56,756 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:45:56,756 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,756 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:45:56,756 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:45:56,756 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:45:56,756 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,757 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:45:56,757 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:45:56,757 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:45:56,757 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,757 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:45:56,758 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:45:56,758 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:45:56,758 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,758 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:45:56,758 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:45:56,759 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:45:56,759 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,759 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:45:56,759 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:45:56,759 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:45:56,760 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,760 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:45:56,760 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:45:56,760 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:45:56,760 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,760 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:45:56,762 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:45:56,763 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:45:56,764 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:45:56,764 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:45:56,764 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:45:56,765 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:45:56,765 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:45:56,765 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:45:56,765 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:45:56,766 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:45:56,767 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:45:56,768 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:45:57,164 - INFO - logger - Notification system initialized
2025-07-06 16:45:57,194 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:49:40,478 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:49:40,480 - INFO - logger - Backup system initialized
2025-07-06 16:49:52,517 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:49:52.517237"}
2025-07-06 16:49:52,619 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:49:52,620 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:49:52,620 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,620 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:49:52,621 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:49:52,621 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:49:52,621 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,621 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:49:52,622 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:49:52,622 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:49:52,622 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,622 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:49:52,623 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:49:52,623 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:49:52,623 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,623 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:49:52,623 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:49:52,624 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:49:52,624 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,624 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:49:52,624 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:49:52,624 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:49:52,625 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,625 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:49:52,625 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:49:52,625 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:49:52,625 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,626 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:49:52,626 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:49:52,626 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:49:52,626 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,627 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:49:52,627 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:49:52,627 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:49:52,627 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,628 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:49:52,628 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:49:52,628 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:49:52,628 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,628 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:49:52,628 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:49:52,629 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:49:52,629 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,629 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:49:52,629 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:49:52,629 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:49:52,630 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,630 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:49:52,630 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:49:52,630 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:49:52,630 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,630 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:49:52,631 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:49:52,631 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:49:52,631 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,631 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:49:52,631 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:49:52,632 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:49:52,632 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,632 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:49:52,632 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:49:52,632 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:49:52,632 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,632 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:49:52,632 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:49:52,632 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:49:52,633 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,633 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:49:52,633 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:49:52,633 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:49:52,633 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,633 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:49:52,635 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:49:52,635 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:49:52,636 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:49:52,637 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:49:52,638 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:49:52,638 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:49:52,638 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:49:52,638 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:49:52,638 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:49:52,639 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:49:52,640 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:49:52,641 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:49:53,015 - INFO - logger - Notification system initialized
2025-07-06 16:49:53,045 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:51:31,767 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:51:31,769 - INFO - logger - Backup system initialized
2025-07-06 16:51:48,641 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:51:48.641574"}
2025-07-06 16:51:48,721 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:51:48,722 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:51:48,722 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,722 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:51:48,723 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:51:48,723 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:51:48,723 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,723 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:51:48,723 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:51:48,724 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:51:48,724 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,724 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:51:48,725 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:51:48,725 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:51:48,725 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,725 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:51:48,725 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:51:48,726 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:51:48,726 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,726 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:51:48,726 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:51:48,726 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:51:48,727 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,727 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:51:48,727 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:51:48,727 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:51:48,727 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,727 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:51:48,728 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:51:48,728 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:51:48,728 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,728 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:51:48,729 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:51:48,729 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:51:48,729 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,729 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:51:48,729 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:51:48,730 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:51:48,730 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,730 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:51:48,730 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:51:48,730 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:51:48,730 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,731 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:51:48,731 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:51:48,731 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:51:48,731 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,731 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:51:48,731 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:51:48,731 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:51:48,731 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,731 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:51:48,732 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:51:48,732 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:51:48,732 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,732 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:51:48,732 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:51:48,732 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:51:48,732 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,733 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:51:48,733 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:51:48,733 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:51:48,733 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,733 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:51:48,733 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:51:48,734 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:51:48,734 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,734 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:51:48,734 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:51:48,735 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:51:48,735 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,735 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:51:48,737 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:51:48,737 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:51:48,738 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:51:48,738 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:51:48,739 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:51:48,739 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:51:48,739 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:51:48,739 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:51:48,739 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:51:48,740 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:51:48,741 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:51:48,742 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:51:49,113 - INFO - logger - Notification system initialized
2025-07-06 16:51:49,142 - INFO - logger - Application initialized successfully | {"user_id": 1}
