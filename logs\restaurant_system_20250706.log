2025-07-06 12:04:09,420 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 12:04:09,422 - INFO - logger - Backup system initialized
2025-07-06 12:04:19,599 - INFO - logger - Authentication cancelled or failed
2025-07-06 12:12:47,324 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 12:12:47,327 - INFO - logger - Backup system initialized
2025-07-06 12:13:08,011 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T12:13:08.011074"}
2025-07-06 12:13:08,402 - INFO - logger - Notification system initialized
2025-07-06 12:13:08,430 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 12:30:09,950 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 12:30:09,954 - INFO - logger - Backup system initialized
2025-07-06 12:58:43,594 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 12:58:43,596 - INFO - logger - Backup system initialized
2025-07-06 12:58:53,322 - INFO - logger - Authentication cancelled or failed
2025-07-06 12:59:14,190 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 12:59:14,192 - INFO - logger - Backup system initialized
2025-07-06 12:59:28,289 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T12:59:28.289892"}
2025-07-06 12:59:28,701 - INFO - logger - Notification system initialized
2025-07-06 12:59:28,735 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 14:13:15,929 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 14:13:15,934 - INFO - logger - Backup system initialized
2025-07-06 14:13:45,728 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T14:13:45.727782"}
2025-07-06 14:13:46,229 - INFO - logger - Notification system initialized
2025-07-06 14:13:46,271 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 14:22:27,382 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 14:22:27,385 - INFO - logger - Backup system initialized
2025-07-06 14:22:45,980 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T14:22:45.979944"}
2025-07-06 14:22:46,394 - INFO - logger - Notification system initialized
2025-07-06 14:22:46,420 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 14:36:03,805 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 14:36:03,807 - INFO - logger - Backup system initialized
2025-07-06 14:36:14,342 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T14:36:14.342707"}
2025-07-06 14:36:14,789 - INFO - logger - Notification system initialized
2025-07-06 14:36:14,818 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 14:48:08,205 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 14:48:08,207 - INFO - logger - Backup system initialized
2025-07-06 14:48:20,871 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T14:48:20.871610"}
2025-07-06 14:48:21,302 - INFO - logger - Notification system initialized
2025-07-06 14:48:21,328 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 14:54:43,804 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 14:54:43,805 - INFO - logger - Backup system initialized
2025-07-06 14:55:02,039 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T14:55:02.039033"}
2025-07-06 14:55:02,415 - INFO - logger - Notification system initialized
2025-07-06 14:55:02,440 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 15:23:42,387 - INFO - sync_manager - Sync status changed to: offline
2025-07-06 15:23:42,389 - ERROR - connection_pool - Query execution error: no such table: menu_items
2025-07-06 15:23:42,389 - ERROR - connection_pool - Query: SELECT * FROM menu_items
2025-07-06 15:23:42,389 - ERROR - connection_pool - Params: None
2025-07-06 15:23:42,390 - ERROR - offline_manager - Error caching table menu_items: no such table: menu_items
2025-07-06 15:23:42,390 - ERROR - connection_pool - Query execution error: no such table: inventory
2025-07-06 15:23:42,391 - ERROR - connection_pool - Query: SELECT * FROM inventory
2025-07-06 15:23:42,391 - ERROR - connection_pool - Params: None
2025-07-06 15:23:42,391 - ERROR - offline_manager - Error caching table inventory: no such table: inventory
2025-07-06 15:23:42,391 - ERROR - connection_pool - Query execution error: no such table: staff
2025-07-06 15:23:42,391 - ERROR - connection_pool - Query: SELECT * FROM staff
2025-07-06 15:23:42,391 - ERROR - connection_pool - Params: None
2025-07-06 15:23:42,391 - ERROR - offline_manager - Error caching table staff: no such table: staff
2025-07-06 15:23:42,392 - ERROR - connection_pool - Query execution error: no such table: payment_methods
2025-07-06 15:23:42,392 - ERROR - connection_pool - Query: SELECT * FROM payment_methods
2025-07-06 15:23:42,392 - ERROR - connection_pool - Params: None
2025-07-06 15:23:42,393 - ERROR - offline_manager - Error caching table payment_methods: no such table: payment_methods
2025-07-06 15:23:42,393 - ERROR - connection_pool - Query execution error: no such table: departments
2025-07-06 15:23:42,393 - ERROR - connection_pool - Query: SELECT * FROM departments
2025-07-06 15:23:42,393 - ERROR - connection_pool - Params: None
2025-07-06 15:23:42,394 - ERROR - offline_manager - Error caching table departments: no such table: departments
2025-07-06 15:23:42,403 - INFO - offline_manager - Switched to offline mode
2025-07-06 15:23:42,408 - INFO - sync_manager - Sync status changed to: online
2025-07-06 15:23:42,409 - INFO - offline_manager - Switched to online mode after 0:00:00.019953
2025-07-06 15:23:42,409 - INFO - offline_manager - Processing 1 offline operations
2025-07-06 15:23:42,410 - ERROR - connection_pool - Query execution error: no such table: menu_items
2025-07-06 15:23:42,410 - ERROR - connection_pool - Query: INSERT INTO menu_items (name, price, category, created_at) VALUES (?, ?, ?, ?)
2025-07-06 15:23:42,410 - ERROR - connection_pool - Params: ('Test Menu Item', 15.99, 'Main Course', '2025-07-06T15:23:42.403603')
2025-07-06 15:23:42,410 - ERROR - offline_manager - Error applying offline operation: no such table: menu_items
2025-07-06 15:23:42,414 - INFO - offline_manager - Processed offline operations: 0 success, 1 failed
2025-07-06 15:26:07,137 - INFO - sync_manager - Sync status changed to: offline
2025-07-06 15:26:07,138 - ERROR - connection_pool - Query execution error: no such table: menu_items
2025-07-06 15:26:07,138 - ERROR - connection_pool - Query: SELECT * FROM menu_items
2025-07-06 15:26:07,139 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,139 - ERROR - offline_manager - Error caching table menu_items: no such table: menu_items
2025-07-06 15:26:07,139 - ERROR - connection_pool - Query execution error: no such table: inventory
2025-07-06 15:26:07,139 - ERROR - connection_pool - Query: SELECT * FROM inventory
2025-07-06 15:26:07,139 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,139 - ERROR - offline_manager - Error caching table inventory: no such table: inventory
2025-07-06 15:26:07,140 - ERROR - connection_pool - Query execution error: no such table: staff
2025-07-06 15:26:07,140 - ERROR - connection_pool - Query: SELECT * FROM staff
2025-07-06 15:26:07,140 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,140 - ERROR - offline_manager - Error caching table staff: no such table: staff
2025-07-06 15:26:07,141 - ERROR - connection_pool - Query execution error: no such table: payment_methods
2025-07-06 15:26:07,141 - ERROR - connection_pool - Query: SELECT * FROM payment_methods
2025-07-06 15:26:07,141 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,141 - ERROR - offline_manager - Error caching table payment_methods: no such table: payment_methods
2025-07-06 15:26:07,142 - ERROR - connection_pool - Query execution error: no such table: departments
2025-07-06 15:26:07,142 - ERROR - connection_pool - Query: SELECT * FROM departments
2025-07-06 15:26:07,142 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,142 - ERROR - offline_manager - Error caching table departments: no such table: departments
2025-07-06 15:26:07,150 - INFO - offline_manager - Switched to offline mode
2025-07-06 15:26:07,155 - INFO - sync_manager - Sync status changed to: online
2025-07-06 15:26:07,155 - INFO - offline_manager - Switched to online mode after 0:00:00.016808
2025-07-06 15:26:07,155 - INFO - offline_manager - Processing 1 offline operations
2025-07-06 15:26:07,156 - ERROR - connection_pool - Query execution error: no such table: menu_items
2025-07-06 15:26:07,156 - ERROR - connection_pool - Query: INSERT INTO menu_items (name, price, category, created_at) VALUES (?, ?, ?, ?)
2025-07-06 15:26:07,156 - ERROR - connection_pool - Params: ('Test Menu Item', 15.99, 'Main Course', '2025-07-06T15:26:07.150800')
2025-07-06 15:26:07,156 - ERROR - offline_manager - Error applying offline operation: no such table: menu_items
2025-07-06 15:26:07,160 - INFO - offline_manager - Processed offline operations: 0 success, 1 failed
2025-07-06 15:26:07,179 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 15:26:07,180 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 15:26:07,180 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,180 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 15:26:07,181 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 15:26:07,181 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 15:26:07,181 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,181 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 15:26:07,181 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 15:26:07,182 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 15:26:07,182 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,182 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 15:26:07,182 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 15:26:07,182 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 15:26:07,182 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,183 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 15:26:07,183 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 15:26:07,183 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 15:26:07,183 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,183 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 15:26:07,183 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 15:26:07,184 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 15:26:07,184 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,184 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 15:26:07,184 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 15:26:07,184 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 15:26:07,184 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,185 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 15:26:07,185 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 15:26:07,185 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 15:26:07,185 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,185 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 15:26:07,186 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 15:26:07,186 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 15:26:07,186 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,186 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 15:26:07,186 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 15:26:07,187 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 15:26:07,187 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,187 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 15:26:07,187 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 15:26:07,187 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 15:26:07,187 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,188 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 15:26:07,188 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 15:26:07,188 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 15:26:07,188 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,188 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 15:26:07,189 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 15:26:07,189 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 15:26:07,189 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,189 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 15:26:07,189 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 15:26:07,189 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 15:26:07,189 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,190 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 15:26:07,190 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 15:26:07,190 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 15:26:07,190 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,190 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 15:26:07,190 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 15:26:07,191 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 15:26:07,191 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,191 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 15:26:07,191 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 15:26:07,191 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 15:26:07,191 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,191 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 15:26:07,191 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 15:26:07,192 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 15:26:07,192 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,192 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 15:26:07,193 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 15:26:07,193 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 15:26:07,194 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 15:26:07,194 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 15:26:07,194 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 15:26:07,194 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 15:26:07,194 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 15:26:07,195 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 15:26:07,195 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 15:26:07,195 - INFO - sync_manager - Data synchronization service started
2025-07-06 15:26:07,198 - INFO - offline_manager - Offline storage initialized
2025-07-06 15:26:07,199 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 15:26:07,199 - INFO - sync_manager - Sync status changed to: offline
2025-07-06 15:26:07,200 - ERROR - connection_pool - Query execution error: no such table: menu_items
2025-07-06 15:26:07,200 - ERROR - connection_pool - Query: SELECT * FROM menu_items
2025-07-06 15:26:07,200 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,200 - ERROR - offline_manager - Error caching table menu_items: no such table: menu_items
2025-07-06 15:26:07,201 - ERROR - connection_pool - Query execution error: no such table: inventory
2025-07-06 15:26:07,201 - ERROR - connection_pool - Query: SELECT * FROM inventory
2025-07-06 15:26:07,201 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,201 - ERROR - offline_manager - Error caching table inventory: no such table: inventory
2025-07-06 15:26:07,201 - ERROR - connection_pool - Query execution error: no such table: staff
2025-07-06 15:26:07,201 - ERROR - connection_pool - Query: SELECT * FROM staff
2025-07-06 15:26:07,202 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,202 - ERROR - offline_manager - Error caching table staff: no such table: staff
2025-07-06 15:26:07,202 - ERROR - connection_pool - Query execution error: no such table: payment_methods
2025-07-06 15:26:07,202 - ERROR - connection_pool - Query: SELECT * FROM payment_methods
2025-07-06 15:26:07,202 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,202 - ERROR - offline_manager - Error caching table payment_methods: no such table: payment_methods
2025-07-06 15:26:07,203 - ERROR - connection_pool - Query execution error: no such table: departments
2025-07-06 15:26:07,203 - ERROR - connection_pool - Query: SELECT * FROM departments
2025-07-06 15:26:07,203 - ERROR - connection_pool - Params: None
2025-07-06 15:26:07,203 - ERROR - offline_manager - Error caching table departments: no such table: departments
2025-07-06 15:26:07,209 - INFO - offline_manager - Switched to offline mode
2025-07-06 15:26:07,210 - INFO - sync_manager - Sync status changed to: online
2025-07-06 15:26:07,210 - INFO - offline_manager - Switched to online mode after 0:00:00.010346
2025-07-06 15:55:07,605 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 15:55:07,608 - INFO - logger - Backup system initialized
2025-07-06 16:03:42,279 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:03:42,281 - INFO - logger - Backup system initialized
2025-07-06 16:03:55,465 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:03:55.465193"}
2025-07-06 16:03:55,617 - INFO - query_optimizer - Created essential index: idx_sales_order_date
2025-07-06 16:03:55,620 - INFO - query_optimizer - Created essential index: idx_sales_payment_method
2025-07-06 16:03:55,622 - INFO - query_optimizer - Created essential index: idx_sales_department
2025-07-06 16:03:55,625 - INFO - query_optimizer - Created essential index: idx_sales_dish_code
2025-07-06 16:03:55,628 - INFO - query_optimizer - Created essential index: idx_sales_order_number
2025-07-06 16:03:55,630 - INFO - query_optimizer - Created essential index: idx_sales_composite
2025-07-06 16:03:55,632 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:03:55,632 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:03:55,632 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,633 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:03:55,635 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:03:55,635 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:03:55,635 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,636 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:03:55,637 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:03:55,638 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:03:55,639 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,642 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:03:55,643 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:03:55,644 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:03:55,644 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,644 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:03:55,645 - INFO - query_optimizer - Created essential index: idx_recipes_name
2025-07-06 16:03:55,647 - INFO - query_optimizer - Created essential index: idx_recipes_category
2025-07-06 16:03:55,649 - INFO - query_optimizer - Created essential index: idx_recipe_ingredients_recipe
2025-07-06 16:03:55,651 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:03:55,651 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:03:55,651 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,652 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:03:55,654 - INFO - query_optimizer - Created essential index: idx_purchase_orders_date
2025-07-06 16:03:55,656 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:03:55,656 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:03:55,657 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,657 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:03:55,660 - INFO - query_optimizer - Created essential index: idx_purchase_orders_status
2025-07-06 16:03:55,661 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:03:55,661 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:03:55,662 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,662 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:03:55,664 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:03:55,665 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:03:55,665 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,665 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:03:55,667 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:03:55,667 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:03:55,668 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,668 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:03:55,670 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:03:55,672 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:03:55,672 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,673 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:03:55,674 - INFO - query_optimizer - Created essential index: idx_customers_phone
2025-07-06 16:03:55,677 - INFO - query_optimizer - Created essential index: idx_customers_email
2025-07-06 16:03:55,678 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:03:55,679 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:03:55,679 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,680 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:03:55,682 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:03:55,682 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:03:55,683 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,683 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:03:55,685 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:03:55,685 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:03:55,685 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,686 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:03:55,687 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:03:55,688 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:03:55,689 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,689 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:03:55,690 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:03:55,690 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:03:55,690 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,691 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:03:55,691 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:03:55,692 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:03:55,692 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,693 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:03:55,693 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:03:55,693 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:03:55,694 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,694 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:03:55,695 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:03:55,695 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:03:55,695 - ERROR - connection_pool - Params: None
2025-07-06 16:03:55,696 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:03:55,713 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:03:55,714 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:03:55,715 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:03:55,715 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:03:55,716 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:03:55,716 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:03:55,717 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:03:55,717 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:03:55,718 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:03:55,719 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:03:55,721 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:03:55,724 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:03:56,529 - INFO - logger - Notification system initialized
2025-07-06 16:03:56,583 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:12:30,363 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:12:30,366 - INFO - logger - Backup system initialized
2025-07-06 16:12:42,074 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:12:42.074052"}
2025-07-06 16:12:42,152 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:12:42,152 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:12:42,152 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,152 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:12:42,153 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:12:42,153 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:12:42,153 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,153 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:12:42,153 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:12:42,154 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:12:42,154 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,154 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:12:42,154 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:12:42,154 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:12:42,154 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,155 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:12:42,155 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:12:42,155 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:12:42,155 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,155 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:12:42,155 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:12:42,155 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:12:42,155 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,156 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:12:42,156 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:12:42,156 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:12:42,156 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,156 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:12:42,156 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:12:42,156 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:12:42,156 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,156 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:12:42,156 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:12:42,157 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:12:42,157 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,157 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:12:42,157 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:12:42,157 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:12:42,157 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,157 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:12:42,157 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:12:42,158 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:12:42,158 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,158 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:12:42,158 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:12:42,158 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:12:42,159 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,159 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:12:42,159 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:12:42,159 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:12:42,159 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,159 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:12:42,160 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:12:42,160 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:12:42,160 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,160 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:12:42,161 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:12:42,161 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:12:42,161 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,161 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:12:42,161 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:12:42,162 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:12:42,162 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,162 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:12:42,163 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:12:42,163 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:12:42,163 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,163 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:12:42,163 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:12:42,163 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:12:42,163 - ERROR - connection_pool - Params: None
2025-07-06 16:12:42,164 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:12:42,165 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:12:42,166 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:12:42,166 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:12:42,166 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:12:42,167 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:12:42,167 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:12:42,167 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:12:42,167 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:12:42,168 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:12:42,168 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:12:42,169 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:12:42,170 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:12:42,567 - INFO - logger - Notification system initialized
2025-07-06 16:12:42,601 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:22:16,928 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:22:16,930 - INFO - logger - Backup system initialized
2025-07-06 16:22:26,809 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:22:26.808856"}
2025-07-06 16:22:27,028 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:22:27,029 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:22:27,029 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,030 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:22:27,031 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:22:27,031 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:22:27,031 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,032 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:22:27,032 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:22:27,032 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:22:27,032 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,033 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:22:27,033 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:22:27,034 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:22:27,034 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,035 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:22:27,035 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:22:27,036 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:22:27,036 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,036 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:22:27,037 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:22:27,037 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:22:27,037 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,038 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:22:27,038 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:22:27,038 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:22:27,038 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,039 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:22:27,039 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:22:27,040 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:22:27,040 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,040 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:22:27,041 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:22:27,041 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:22:27,041 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,042 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:22:27,042 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:22:27,042 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:22:27,043 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,043 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:22:27,044 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:22:27,044 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:22:27,044 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,044 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:22:27,045 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:22:27,045 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:22:27,045 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,046 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:22:27,047 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:22:27,047 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:22:27,048 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,048 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:22:27,049 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:22:27,049 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:22:27,049 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,049 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:22:27,050 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:22:27,050 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:22:27,050 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,051 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:22:27,051 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:22:27,051 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:22:27,052 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,052 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:22:27,052 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:22:27,053 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:22:27,053 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,053 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:22:27,053 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:22:27,054 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:22:27,054 - ERROR - connection_pool - Params: None
2025-07-06 16:22:27,054 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:22:27,058 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:22:27,058 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:22:27,059 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:22:27,059 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:22:27,060 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:22:27,061 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:22:27,061 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:22:27,061 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:22:27,062 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:22:27,063 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:22:27,065 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:22:27,067 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:22:27,999 - INFO - logger - Notification system initialized
2025-07-06 16:22:28,054 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:23:21,371 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:23:21,373 - INFO - logger - Backup system initialized
2025-07-06 16:23:30,565 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:23:30.565308"}
2025-07-06 16:23:30,671 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:23:30,671 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:23:30,671 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,671 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:23:30,672 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:23:30,672 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:23:30,672 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,672 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:23:30,673 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:23:30,673 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:23:30,673 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,673 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:23:30,673 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:23:30,673 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:23:30,673 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,674 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:23:30,674 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:23:30,674 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:23:30,674 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,674 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:23:30,675 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:23:30,675 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:23:30,675 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,676 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:23:30,676 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:23:30,676 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:23:30,676 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,677 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:23:30,677 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:23:30,677 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:23:30,677 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,677 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:23:30,678 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:23:30,678 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:23:30,678 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,678 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:23:30,679 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:23:30,679 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:23:30,679 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,679 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:23:30,680 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:23:30,680 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:23:30,680 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,680 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:23:30,680 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:23:30,680 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:23:30,681 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,681 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:23:30,681 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:23:30,682 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:23:30,682 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,682 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:23:30,682 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:23:30,682 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:23:30,683 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,683 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:23:30,683 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:23:30,684 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:23:30,684 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,684 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:23:30,684 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:23:30,684 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:23:30,684 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,684 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:23:30,685 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:23:30,685 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:23:30,685 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,685 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:23:30,685 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:23:30,685 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:23:30,685 - ERROR - connection_pool - Params: None
2025-07-06 16:23:30,686 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:23:30,689 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:23:30,689 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:23:30,690 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:23:30,690 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:23:30,691 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:23:30,691 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:23:30,692 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:23:30,692 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:23:30,692 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:23:30,693 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:23:30,694 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:23:30,695 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:23:31,115 - INFO - logger - Notification system initialized
2025-07-06 16:23:31,140 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:30:27,479 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:30:27,481 - INFO - logger - Backup system initialized
2025-07-06 16:30:41,481 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:30:41.481683"}
2025-07-06 16:30:41,590 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:30:41,591 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:30:41,591 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,591 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:30:41,591 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:30:41,592 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:30:41,592 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,592 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:30:41,592 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:30:41,592 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:30:41,593 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,593 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:30:41,593 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:30:41,593 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:30:41,593 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,594 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:30:41,594 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:30:41,594 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:30:41,594 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,594 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:30:41,594 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:30:41,594 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:30:41,594 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,594 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:30:41,595 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:30:41,595 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:30:41,595 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,595 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:30:41,595 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:30:41,595 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:30:41,595 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,595 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:30:41,596 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:30:41,596 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:30:41,596 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,596 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:30:41,596 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:30:41,596 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:30:41,596 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,596 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:30:41,597 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:30:41,597 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:30:41,597 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,597 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:30:41,598 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:30:41,598 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:30:41,598 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,598 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:30:41,598 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:30:41,598 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:30:41,598 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,598 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:30:41,599 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:30:41,599 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:30:41,599 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,599 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:30:41,600 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:30:41,600 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:30:41,600 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,600 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:30:41,600 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:30:41,600 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:30:41,600 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,601 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:30:41,601 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:30:41,601 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:30:41,601 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,601 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:30:41,601 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:30:41,602 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:30:41,602 - ERROR - connection_pool - Params: None
2025-07-06 16:30:41,602 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:30:41,604 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:30:41,605 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:30:41,605 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:30:41,605 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:30:41,605 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:30:41,605 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:30:41,606 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:30:41,606 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:30:41,606 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:30:41,606 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:30:41,607 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:30:41,608 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:30:42,030 - INFO - logger - Notification system initialized
2025-07-06 16:30:42,069 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:31:22,217 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:31:22,219 - INFO - logger - Backup system initialized
2025-07-06 16:31:35,881 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:31:35.881297"}
2025-07-06 16:31:35,955 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:31:35,956 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:31:35,956 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,956 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:31:35,956 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:31:35,957 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:31:35,957 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,957 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:31:35,957 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:31:35,957 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:31:35,957 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,958 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:31:35,958 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:31:35,958 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:31:35,958 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,958 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:31:35,958 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:31:35,958 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:31:35,959 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,959 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:31:35,959 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:31:35,959 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:31:35,960 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,960 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:31:35,960 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:31:35,960 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:31:35,960 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,961 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:31:35,961 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:31:35,961 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:31:35,961 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,961 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:31:35,962 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:31:35,962 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:31:35,962 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,962 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:31:35,962 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:31:35,963 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:31:35,963 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,963 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:31:35,963 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:31:35,963 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:31:35,963 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,964 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:31:35,964 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:31:35,964 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:31:35,964 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,964 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:31:35,965 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:31:35,965 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:31:35,965 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,965 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:31:35,966 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:31:35,966 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:31:35,966 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,966 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:31:35,966 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:31:35,967 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:31:35,967 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,967 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:31:35,967 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:31:35,967 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:31:35,967 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,968 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:31:35,968 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:31:35,968 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:31:35,968 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,968 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:31:35,969 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:31:35,969 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:31:35,969 - ERROR - connection_pool - Params: None
2025-07-06 16:31:35,969 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:31:35,971 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:31:35,972 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:31:35,972 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:31:35,973 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:31:35,973 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:31:35,973 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:31:35,973 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:31:35,974 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:31:35,974 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:31:35,974 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:31:35,975 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:31:35,976 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:31:36,339 - INFO - logger - Notification system initialized
2025-07-06 16:31:36,367 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:32:20,837 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:32:20,839 - INFO - logger - Backup system initialized
2025-07-06 16:32:31,910 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:32:31.910134"}
2025-07-06 16:32:31,981 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:32:31,982 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:32:31,982 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,982 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:32:31,982 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:32:31,983 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:32:31,983 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,983 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:32:31,983 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:32:31,984 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:32:31,984 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,984 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:32:31,984 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:32:31,985 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:32:31,985 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,985 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:32:31,985 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:32:31,985 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:32:31,986 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,986 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:32:31,986 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:32:31,987 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:32:31,987 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,987 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:32:31,988 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:32:31,988 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:32:31,988 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,988 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:32:31,988 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:32:31,989 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:32:31,989 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,989 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:32:31,989 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:32:31,989 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:32:31,990 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,990 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:32:31,990 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:32:31,990 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:32:31,990 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,991 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:32:31,991 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:32:31,991 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:32:31,991 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,991 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:32:31,991 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:32:31,992 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:32:31,992 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,992 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:32:31,992 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:32:31,992 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:32:31,992 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,992 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:32:31,992 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:32:31,992 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:32:31,992 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,993 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:32:31,993 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:32:31,993 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:32:31,993 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,993 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:32:31,993 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:32:31,993 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:32:31,993 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,993 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:32:31,994 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:32:31,994 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:32:31,994 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,994 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:32:31,994 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:32:31,994 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:32:31,995 - ERROR - connection_pool - Params: None
2025-07-06 16:32:31,995 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:32:31,997 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:32:31,997 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:32:31,998 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:32:31,998 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:32:31,998 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:32:31,998 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:32:31,998 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:32:31,999 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:32:31,999 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:32:31,999 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:32:32,000 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:32:32,001 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:32:32,360 - INFO - logger - Notification system initialized
2025-07-06 16:32:32,385 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:35:11,373 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:35:11,375 - INFO - logger - Backup system initialized
2025-07-06 16:35:21,972 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:35:21.972577"}
2025-07-06 16:35:22,053 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:35:22,054 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:35:22,054 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,054 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:35:22,054 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:35:22,055 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:35:22,055 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,055 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:35:22,055 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:35:22,056 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:35:22,056 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,056 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:35:22,056 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:35:22,056 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:35:22,057 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,057 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:35:22,057 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:35:22,057 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:35:22,057 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,057 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:35:22,057 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:35:22,058 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:35:22,058 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,058 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:35:22,058 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:35:22,058 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:35:22,058 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,058 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:35:22,058 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:35:22,058 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:35:22,059 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,059 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:35:22,059 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:35:22,059 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:35:22,059 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,060 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:35:22,060 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:35:22,060 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:35:22,060 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,060 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:35:22,061 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:35:22,061 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:35:22,061 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,061 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:35:22,061 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:35:22,061 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:35:22,061 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,061 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:35:22,061 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:35:22,062 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:35:22,062 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,062 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:35:22,062 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:35:22,062 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:35:22,062 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,063 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:35:22,063 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:35:22,063 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:35:22,063 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,063 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:35:22,063 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:35:22,063 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:35:22,064 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,064 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:35:22,064 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:35:22,064 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:35:22,064 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,064 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:35:22,064 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:35:22,065 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:35:22,065 - ERROR - connection_pool - Params: None
2025-07-06 16:35:22,065 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:35:22,067 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:35:22,067 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:35:22,068 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:35:22,068 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:35:22,068 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:35:22,068 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:35:22,068 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:35:22,069 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:35:22,069 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:35:22,070 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:35:22,071 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:35:22,072 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:35:22,450 - INFO - logger - Notification system initialized
2025-07-06 16:35:22,471 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:37:18,259 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:37:18,261 - INFO - logger - Backup system initialized
2025-07-06 16:37:29,180 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:37:29.180193"}
2025-07-06 16:37:29,274 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:37:29,275 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:37:29,275 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,275 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:37:29,276 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:37:29,276 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:37:29,276 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,276 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:37:29,276 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:37:29,277 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:37:29,277 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,277 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:37:29,277 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:37:29,277 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:37:29,277 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,278 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:37:29,278 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:37:29,278 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:37:29,278 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,278 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:37:29,278 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:37:29,278 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:37:29,279 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,279 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:37:29,279 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:37:29,279 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:37:29,279 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,279 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:37:29,280 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:37:29,280 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:37:29,280 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,280 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:37:29,280 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:37:29,281 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:37:29,281 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,281 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:37:29,281 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:37:29,281 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:37:29,281 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,282 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:37:29,282 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:37:29,282 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:37:29,282 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,282 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:37:29,282 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:37:29,283 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:37:29,283 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,283 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:37:29,283 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:37:29,284 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:37:29,284 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,284 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:37:29,284 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:37:29,285 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:37:29,285 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,285 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:37:29,285 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:37:29,285 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:37:29,285 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,286 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:37:29,286 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:37:29,286 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:37:29,286 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,286 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:37:29,287 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:37:29,287 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:37:29,287 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,287 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:37:29,287 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:37:29,287 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:37:29,288 - ERROR - connection_pool - Params: None
2025-07-06 16:37:29,288 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:37:29,289 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:37:29,290 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:37:29,290 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:37:29,290 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:37:29,291 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:37:29,291 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:37:29,291 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:37:29,292 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:37:29,292 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:37:29,292 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:37:29,293 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:37:29,295 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:37:29,649 - INFO - logger - Notification system initialized
2025-07-06 16:37:29,674 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:41:47,566 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:41:47,568 - INFO - logger - Backup system initialized
2025-07-06 16:41:58,597 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:41:58.597621"}
2025-07-06 16:41:58,684 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:41:58,684 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:41:58,684 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,685 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:41:58,685 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:41:58,685 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:41:58,686 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,686 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:41:58,686 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:41:58,686 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:41:58,687 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,687 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:41:58,687 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:41:58,687 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:41:58,687 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,688 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:41:58,688 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:41:58,688 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:41:58,688 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,688 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:41:58,689 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:41:58,689 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:41:58,689 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,689 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:41:58,689 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:41:58,690 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:41:58,690 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,690 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:41:58,690 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:41:58,690 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:41:58,690 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,690 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:41:58,691 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:41:58,691 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:41:58,691 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,691 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:41:58,691 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:41:58,691 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:41:58,691 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,692 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:41:58,692 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:41:58,692 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:41:58,692 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,692 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:41:58,692 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:41:58,693 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:41:58,693 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,693 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:41:58,693 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:41:58,693 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:41:58,693 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,693 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:41:58,694 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:41:58,694 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:41:58,694 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,694 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:41:58,694 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:41:58,695 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:41:58,695 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,695 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:41:58,695 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:41:58,696 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:41:58,696 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,696 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:41:58,696 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:41:58,696 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:41:58,696 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,696 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:41:58,697 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:41:58,697 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:41:58,697 - ERROR - connection_pool - Params: None
2025-07-06 16:41:58,697 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:41:58,700 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:41:58,700 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:41:58,700 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:41:58,701 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:41:58,701 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:41:58,702 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:41:58,702 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:41:58,702 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:41:58,702 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:41:58,703 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:41:58,704 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:41:58,705 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:41:59,101 - INFO - logger - Notification system initialized
2025-07-06 16:41:59,128 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:45:47,459 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:45:47,461 - INFO - logger - Backup system initialized
2025-07-06 16:45:56,668 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:45:56.668746"}
2025-07-06 16:45:56,747 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:45:56,747 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:45:56,747 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,747 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:45:56,748 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:45:56,748 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:45:56,748 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,749 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:45:56,749 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:45:56,749 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:45:56,749 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,750 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:45:56,750 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:45:56,750 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:45:56,750 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,751 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:45:56,751 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:45:56,751 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:45:56,751 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,752 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:45:56,752 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:45:56,752 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:45:56,752 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,753 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:45:56,753 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:45:56,753 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:45:56,753 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,753 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:45:56,753 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:45:56,754 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:45:56,754 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,754 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:45:56,754 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:45:56,754 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:45:56,754 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,754 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:45:56,755 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:45:56,755 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:45:56,755 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,755 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:45:56,755 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:45:56,755 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:45:56,755 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,755 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:45:56,756 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:45:56,756 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:45:56,756 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,756 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:45:56,756 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:45:56,756 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:45:56,756 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,757 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:45:56,757 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:45:56,757 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:45:56,757 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,757 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:45:56,758 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:45:56,758 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:45:56,758 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,758 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:45:56,758 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:45:56,759 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:45:56,759 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,759 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:45:56,759 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:45:56,759 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:45:56,760 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,760 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:45:56,760 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:45:56,760 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:45:56,760 - ERROR - connection_pool - Params: None
2025-07-06 16:45:56,760 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:45:56,762 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:45:56,763 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:45:56,764 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:45:56,764 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:45:56,764 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:45:56,765 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:45:56,765 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:45:56,765 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:45:56,765 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:45:56,766 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:45:56,767 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:45:56,768 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:45:57,164 - INFO - logger - Notification system initialized
2025-07-06 16:45:57,194 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:49:40,478 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:49:40,480 - INFO - logger - Backup system initialized
2025-07-06 16:49:52,517 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:49:52.517237"}
2025-07-06 16:49:52,619 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:49:52,620 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:49:52,620 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,620 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:49:52,621 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:49:52,621 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:49:52,621 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,621 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:49:52,622 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:49:52,622 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:49:52,622 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,622 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:49:52,623 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:49:52,623 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:49:52,623 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,623 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:49:52,623 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:49:52,624 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:49:52,624 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,624 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:49:52,624 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:49:52,624 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:49:52,625 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,625 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:49:52,625 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:49:52,625 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:49:52,625 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,626 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:49:52,626 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:49:52,626 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:49:52,626 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,627 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:49:52,627 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:49:52,627 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:49:52,627 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,628 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:49:52,628 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:49:52,628 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:49:52,628 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,628 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:49:52,628 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:49:52,629 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:49:52,629 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,629 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:49:52,629 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:49:52,629 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:49:52,630 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,630 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:49:52,630 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:49:52,630 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:49:52,630 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,630 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:49:52,631 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:49:52,631 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:49:52,631 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,631 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:49:52,631 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:49:52,632 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:49:52,632 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,632 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:49:52,632 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:49:52,632 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:49:52,632 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,632 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:49:52,632 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:49:52,632 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:49:52,633 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,633 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:49:52,633 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:49:52,633 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:49:52,633 - ERROR - connection_pool - Params: None
2025-07-06 16:49:52,633 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:49:52,635 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:49:52,635 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:49:52,636 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:49:52,637 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:49:52,638 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:49:52,638 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:49:52,638 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:49:52,638 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:49:52,638 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:49:52,639 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:49:52,640 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:49:52,641 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:49:53,015 - INFO - logger - Notification system initialized
2025-07-06 16:49:53,045 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 16:51:31,767 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 16:51:31,769 - INFO - logger - Backup system initialized
2025-07-06 16:51:48,641 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T16:51:48.641574"}
2025-07-06 16:51:48,721 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:51:48,722 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 16:51:48,722 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,722 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 16:51:48,723 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:51:48,723 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 16:51:48,723 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,723 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 16:51:48,723 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:51:48,724 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 16:51:48,724 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,724 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 16:51:48,725 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 16:51:48,725 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 16:51:48,725 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,725 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 16:51:48,725 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 16:51:48,726 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 16:51:48,726 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,726 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 16:51:48,726 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 16:51:48,726 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 16:51:48,727 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,727 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 16:51:48,727 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:51:48,727 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 16:51:48,727 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,727 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 16:51:48,728 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 16:51:48,728 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 16:51:48,728 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,728 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 16:51:48,729 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:51:48,729 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 16:51:48,729 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,729 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 16:51:48,729 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 16:51:48,730 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 16:51:48,730 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,730 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 16:51:48,730 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 16:51:48,730 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 16:51:48,730 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,731 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 16:51:48,731 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 16:51:48,731 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 16:51:48,731 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,731 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 16:51:48,731 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:51:48,731 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 16:51:48,731 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,731 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 16:51:48,732 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 16:51:48,732 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 16:51:48,732 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,732 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 16:51:48,732 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 16:51:48,732 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 16:51:48,732 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,733 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 16:51:48,733 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:51:48,733 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 16:51:48,733 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,733 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 16:51:48,733 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:51:48,734 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 16:51:48,734 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,734 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 16:51:48,734 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 16:51:48,735 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 16:51:48,735 - ERROR - connection_pool - Params: None
2025-07-06 16:51:48,735 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 16:51:48,737 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 16:51:48,737 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 16:51:48,738 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 16:51:48,738 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 16:51:48,739 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 16:51:48,739 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 16:51:48,739 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 16:51:48,739 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 16:51:48,739 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 16:51:48,740 - INFO - sync_manager - Data synchronization service started
2025-07-06 16:51:48,741 - INFO - offline_manager - Offline storage initialized
2025-07-06 16:51:48,742 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 16:51:49,113 - INFO - logger - Notification system initialized
2025-07-06 16:51:49,142 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 17:02:32,470 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 17:02:32,472 - INFO - logger - Backup system initialized
2025-07-06 17:02:41,135 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T17:02:41.135632"}
2025-07-06 17:02:41,209 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:02:41,209 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 17:02:41,210 - ERROR - connection_pool - Params: None
2025-07-06 17:02:41,210 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 17:02:41,210 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:02:41,211 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 17:02:41,211 - ERROR - connection_pool - Params: None
2025-07-06 17:02:41,211 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 17:02:41,211 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:02:41,212 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 17:02:41,212 - ERROR - connection_pool - Params: None
2025-07-06 17:02:41,212 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 17:02:41,212 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:02:41,212 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 17:02:41,212 - ERROR - connection_pool - Params: None
2025-07-06 17:02:41,213 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 17:02:41,213 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 17:02:41,213 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 17:02:41,213 - ERROR - connection_pool - Params: None
2025-07-06 17:02:41,214 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 17:02:41,214 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 17:02:41,214 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 17:02:41,214 - ERROR - connection_pool - Params: None
2025-07-06 17:02:41,214 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 17:02:41,215 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 17:02:41,215 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 17:02:41,215 - ERROR - connection_pool - Params: None
2025-07-06 17:02:41,215 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 17:02:41,215 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 17:02:41,216 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 17:02:41,216 - ERROR - connection_pool - Params: None
2025-07-06 17:02:41,216 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 17:02:41,217 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 17:02:41,217 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 17:02:41,217 - ERROR - connection_pool - Params: None
2025-07-06 17:02:41,217 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 17:02:41,217 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 17:02:41,217 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 17:02:41,218 - ERROR - connection_pool - Params: None
2025-07-06 17:02:41,218 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 17:02:41,218 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 17:02:41,218 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 17:02:41,218 - ERROR - connection_pool - Params: None
2025-07-06 17:02:41,218 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 17:02:41,218 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 17:02:41,219 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 17:02:41,219 - ERROR - connection_pool - Params: None
2025-07-06 17:02:41,219 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 17:02:41,219 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 17:02:41,219 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 17:02:41,219 - ERROR - connection_pool - Params: None
2025-07-06 17:02:41,219 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 17:02:41,220 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 17:02:41,220 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 17:02:41,220 - ERROR - connection_pool - Params: None
2025-07-06 17:02:41,220 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 17:02:41,220 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 17:02:41,220 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 17:02:41,220 - ERROR - connection_pool - Params: None
2025-07-06 17:02:41,220 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 17:02:41,220 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:02:41,221 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 17:02:41,221 - ERROR - connection_pool - Params: None
2025-07-06 17:02:41,221 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 17:02:41,221 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:02:41,221 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 17:02:41,221 - ERROR - connection_pool - Params: None
2025-07-06 17:02:41,222 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 17:02:41,222 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:02:41,222 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 17:02:41,222 - ERROR - connection_pool - Params: None
2025-07-06 17:02:41,222 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 17:02:41,224 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 17:02:41,225 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 17:02:41,225 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 17:02:41,225 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 17:02:41,225 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 17:02:41,226 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 17:02:41,226 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 17:02:41,226 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 17:02:41,226 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 17:02:41,227 - INFO - sync_manager - Data synchronization service started
2025-07-06 17:02:41,228 - INFO - offline_manager - Offline storage initialized
2025-07-06 17:02:41,228 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 17:02:41,609 - INFO - logger - Notification system initialized
2025-07-06 17:02:41,636 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 17:07:41,483 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 17:07:41,485 - INFO - logger - Backup system initialized
2025-07-06 17:07:55,928 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T17:07:55.928259"}
2025-07-06 17:07:56,000 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:07:56,000 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 17:07:56,001 - ERROR - connection_pool - Params: None
2025-07-06 17:07:56,001 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 17:07:56,002 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:07:56,002 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 17:07:56,002 - ERROR - connection_pool - Params: None
2025-07-06 17:07:56,002 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 17:07:56,003 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:07:56,003 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 17:07:56,003 - ERROR - connection_pool - Params: None
2025-07-06 17:07:56,003 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 17:07:56,003 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:07:56,003 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 17:07:56,004 - ERROR - connection_pool - Params: None
2025-07-06 17:07:56,004 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 17:07:56,004 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 17:07:56,005 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 17:07:56,005 - ERROR - connection_pool - Params: None
2025-07-06 17:07:56,005 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 17:07:56,005 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 17:07:56,006 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 17:07:56,006 - ERROR - connection_pool - Params: None
2025-07-06 17:07:56,006 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 17:07:56,006 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 17:07:56,007 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 17:07:56,007 - ERROR - connection_pool - Params: None
2025-07-06 17:07:56,007 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 17:07:56,007 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 17:07:56,007 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 17:07:56,007 - ERROR - connection_pool - Params: None
2025-07-06 17:07:56,008 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 17:07:56,008 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 17:07:56,008 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 17:07:56,008 - ERROR - connection_pool - Params: None
2025-07-06 17:07:56,008 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 17:07:56,009 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 17:07:56,009 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 17:07:56,009 - ERROR - connection_pool - Params: None
2025-07-06 17:07:56,009 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 17:07:56,009 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 17:07:56,009 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 17:07:56,009 - ERROR - connection_pool - Params: None
2025-07-06 17:07:56,010 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 17:07:56,010 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 17:07:56,010 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 17:07:56,010 - ERROR - connection_pool - Params: None
2025-07-06 17:07:56,010 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 17:07:56,010 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 17:07:56,010 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 17:07:56,010 - ERROR - connection_pool - Params: None
2025-07-06 17:07:56,011 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 17:07:56,011 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 17:07:56,011 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 17:07:56,011 - ERROR - connection_pool - Params: None
2025-07-06 17:07:56,011 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 17:07:56,011 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 17:07:56,011 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 17:07:56,011 - ERROR - connection_pool - Params: None
2025-07-06 17:07:56,012 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 17:07:56,012 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:07:56,012 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 17:07:56,012 - ERROR - connection_pool - Params: None
2025-07-06 17:07:56,012 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 17:07:56,013 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:07:56,013 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 17:07:56,013 - ERROR - connection_pool - Params: None
2025-07-06 17:07:56,013 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 17:07:56,013 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:07:56,013 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 17:07:56,013 - ERROR - connection_pool - Params: None
2025-07-06 17:07:56,014 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 17:07:56,015 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 17:07:56,015 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 17:07:56,016 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 17:07:56,016 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 17:07:56,016 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 17:07:56,016 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 17:07:56,016 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 17:07:56,017 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 17:07:56,017 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 17:07:56,017 - INFO - sync_manager - Data synchronization service started
2025-07-06 17:07:56,018 - INFO - offline_manager - Offline storage initialized
2025-07-06 17:07:56,019 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 17:07:56,372 - INFO - logger - Notification system initialized
2025-07-06 17:07:56,395 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 17:09:14,374 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 17:09:14,376 - INFO - logger - Backup system initialized
2025-07-06 17:09:25,705 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T17:09:25.705519"}
2025-07-06 17:09:25,813 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:09:25,813 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 17:09:25,813 - ERROR - connection_pool - Params: None
2025-07-06 17:09:25,813 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 17:09:25,814 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:09:25,814 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 17:09:25,814 - ERROR - connection_pool - Params: None
2025-07-06 17:09:25,814 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 17:09:25,815 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:09:25,815 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 17:09:25,815 - ERROR - connection_pool - Params: None
2025-07-06 17:09:25,815 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 17:09:25,815 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:09:25,816 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 17:09:25,816 - ERROR - connection_pool - Params: None
2025-07-06 17:09:25,816 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 17:09:25,816 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 17:09:25,816 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 17:09:25,816 - ERROR - connection_pool - Params: None
2025-07-06 17:09:25,816 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 17:09:25,817 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 17:09:25,817 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 17:09:25,817 - ERROR - connection_pool - Params: None
2025-07-06 17:09:25,817 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 17:09:25,817 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 17:09:25,817 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 17:09:25,818 - ERROR - connection_pool - Params: None
2025-07-06 17:09:25,818 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 17:09:25,818 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 17:09:25,818 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 17:09:25,819 - ERROR - connection_pool - Params: None
2025-07-06 17:09:25,819 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 17:09:25,819 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 17:09:25,819 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 17:09:25,819 - ERROR - connection_pool - Params: None
2025-07-06 17:09:25,819 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 17:09:25,820 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 17:09:25,820 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 17:09:25,820 - ERROR - connection_pool - Params: None
2025-07-06 17:09:25,820 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 17:09:25,820 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 17:09:25,820 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 17:09:25,821 - ERROR - connection_pool - Params: None
2025-07-06 17:09:25,821 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 17:09:25,821 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 17:09:25,821 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 17:09:25,822 - ERROR - connection_pool - Params: None
2025-07-06 17:09:25,822 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 17:09:25,822 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 17:09:25,822 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 17:09:25,822 - ERROR - connection_pool - Params: None
2025-07-06 17:09:25,822 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 17:09:25,823 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 17:09:25,823 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 17:09:25,823 - ERROR - connection_pool - Params: None
2025-07-06 17:09:25,823 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 17:09:25,824 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 17:09:25,824 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 17:09:25,824 - ERROR - connection_pool - Params: None
2025-07-06 17:09:25,825 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 17:09:25,825 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:09:25,825 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 17:09:25,825 - ERROR - connection_pool - Params: None
2025-07-06 17:09:25,826 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 17:09:25,826 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:09:25,826 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 17:09:25,826 - ERROR - connection_pool - Params: None
2025-07-06 17:09:25,826 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 17:09:25,827 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:09:25,827 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 17:09:25,827 - ERROR - connection_pool - Params: None
2025-07-06 17:09:25,827 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 17:09:25,830 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 17:09:25,830 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 17:09:25,830 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 17:09:25,831 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 17:09:25,831 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 17:09:25,831 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 17:09:25,831 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 17:09:25,831 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 17:09:25,832 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 17:09:25,832 - INFO - sync_manager - Data synchronization service started
2025-07-06 17:09:25,833 - INFO - offline_manager - Offline storage initialized
2025-07-06 17:09:25,834 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 17:09:26,185 - INFO - logger - Notification system initialized
2025-07-06 17:09:26,209 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 17:14:25,666 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 17:14:25,668 - INFO - logger - Backup system initialized
2025-07-06 17:14:39,266 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T17:14:39.266332"}
2025-07-06 17:14:39,342 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:14:39,342 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 17:14:39,343 - ERROR - connection_pool - Params: None
2025-07-06 17:14:39,343 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 17:14:39,343 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:14:39,343 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 17:14:39,343 - ERROR - connection_pool - Params: None
2025-07-06 17:14:39,344 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 17:14:39,344 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:14:39,344 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 17:14:39,345 - ERROR - connection_pool - Params: None
2025-07-06 17:14:39,345 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 17:14:39,345 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:14:39,345 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 17:14:39,345 - ERROR - connection_pool - Params: None
2025-07-06 17:14:39,346 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 17:14:39,346 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 17:14:39,346 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 17:14:39,346 - ERROR - connection_pool - Params: None
2025-07-06 17:14:39,346 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 17:14:39,346 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 17:14:39,346 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 17:14:39,347 - ERROR - connection_pool - Params: None
2025-07-06 17:14:39,347 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 17:14:39,347 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 17:14:39,347 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 17:14:39,347 - ERROR - connection_pool - Params: None
2025-07-06 17:14:39,347 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 17:14:39,348 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 17:14:39,348 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 17:14:39,348 - ERROR - connection_pool - Params: None
2025-07-06 17:14:39,348 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 17:14:39,348 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 17:14:39,349 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 17:14:39,349 - ERROR - connection_pool - Params: None
2025-07-06 17:14:39,349 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 17:14:39,349 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 17:14:39,349 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 17:14:39,349 - ERROR - connection_pool - Params: None
2025-07-06 17:14:39,350 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 17:14:39,350 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 17:14:39,350 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 17:14:39,350 - ERROR - connection_pool - Params: None
2025-07-06 17:14:39,350 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 17:14:39,350 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 17:14:39,350 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 17:14:39,350 - ERROR - connection_pool - Params: None
2025-07-06 17:14:39,351 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 17:14:39,351 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 17:14:39,351 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 17:14:39,351 - ERROR - connection_pool - Params: None
2025-07-06 17:14:39,351 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 17:14:39,351 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 17:14:39,351 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 17:14:39,352 - ERROR - connection_pool - Params: None
2025-07-06 17:14:39,352 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 17:14:39,352 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 17:14:39,352 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 17:14:39,352 - ERROR - connection_pool - Params: None
2025-07-06 17:14:39,353 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 17:14:39,353 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:14:39,353 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 17:14:39,353 - ERROR - connection_pool - Params: None
2025-07-06 17:14:39,353 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 17:14:39,353 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:14:39,354 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 17:14:39,354 - ERROR - connection_pool - Params: None
2025-07-06 17:14:39,354 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 17:14:39,354 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:14:39,354 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 17:14:39,354 - ERROR - connection_pool - Params: None
2025-07-06 17:14:39,354 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 17:14:39,356 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 17:14:39,357 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 17:14:39,357 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 17:14:39,357 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 17:14:39,358 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 17:14:39,358 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 17:14:39,358 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 17:14:39,359 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 17:14:39,359 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 17:14:39,360 - INFO - sync_manager - Data synchronization service started
2025-07-06 17:14:39,361 - INFO - offline_manager - Offline storage initialized
2025-07-06 17:14:39,363 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 17:14:39,724 - INFO - logger - Notification system initialized
2025-07-06 17:14:39,748 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 17:21:40,866 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 17:21:40,868 - INFO - logger - Backup system initialized
2025-07-06 17:21:51,469 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T17:21:51.469313"}
2025-07-06 17:21:51,561 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:21:51,562 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 17:21:51,562 - ERROR - connection_pool - Params: None
2025-07-06 17:21:51,563 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 17:21:51,563 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:21:51,563 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 17:21:51,563 - ERROR - connection_pool - Params: None
2025-07-06 17:21:51,564 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 17:21:51,564 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:21:51,564 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 17:21:51,564 - ERROR - connection_pool - Params: None
2025-07-06 17:21:51,564 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 17:21:51,565 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:21:51,565 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 17:21:51,565 - ERROR - connection_pool - Params: None
2025-07-06 17:21:51,565 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 17:21:51,565 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 17:21:51,565 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 17:21:51,565 - ERROR - connection_pool - Params: None
2025-07-06 17:21:51,565 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 17:21:51,566 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 17:21:51,566 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 17:21:51,566 - ERROR - connection_pool - Params: None
2025-07-06 17:21:51,566 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 17:21:51,567 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 17:21:51,567 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 17:21:51,567 - ERROR - connection_pool - Params: None
2025-07-06 17:21:51,567 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 17:21:51,567 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 17:21:51,567 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 17:21:51,568 - ERROR - connection_pool - Params: None
2025-07-06 17:21:51,568 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 17:21:51,568 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 17:21:51,568 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 17:21:51,568 - ERROR - connection_pool - Params: None
2025-07-06 17:21:51,568 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 17:21:51,568 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 17:21:51,569 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 17:21:51,569 - ERROR - connection_pool - Params: None
2025-07-06 17:21:51,569 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 17:21:51,569 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 17:21:51,569 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 17:21:51,569 - ERROR - connection_pool - Params: None
2025-07-06 17:21:51,569 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 17:21:51,569 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 17:21:51,569 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 17:21:51,570 - ERROR - connection_pool - Params: None
2025-07-06 17:21:51,570 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 17:21:51,570 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 17:21:51,570 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 17:21:51,570 - ERROR - connection_pool - Params: None
2025-07-06 17:21:51,570 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 17:21:51,570 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 17:21:51,570 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 17:21:51,571 - ERROR - connection_pool - Params: None
2025-07-06 17:21:51,571 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 17:21:51,571 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 17:21:51,571 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 17:21:51,571 - ERROR - connection_pool - Params: None
2025-07-06 17:21:51,572 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 17:21:51,572 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:21:51,572 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 17:21:51,572 - ERROR - connection_pool - Params: None
2025-07-06 17:21:51,573 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 17:21:51,573 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:21:51,573 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 17:21:51,573 - ERROR - connection_pool - Params: None
2025-07-06 17:21:51,574 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 17:21:51,574 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:21:51,574 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 17:21:51,574 - ERROR - connection_pool - Params: None
2025-07-06 17:21:51,574 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 17:21:51,576 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 17:21:51,576 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 17:21:51,577 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 17:21:51,577 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 17:21:51,577 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 17:21:51,577 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 17:21:51,578 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 17:21:51,578 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 17:21:51,578 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 17:21:51,578 - INFO - sync_manager - Data synchronization service started
2025-07-06 17:21:51,579 - INFO - offline_manager - Offline storage initialized
2025-07-06 17:21:51,580 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 17:21:51,894 - INFO - logger - Notification system initialized
2025-07-06 17:21:51,916 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 17:23:12,069 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 17:23:12,070 - INFO - logger - Backup system initialized
2025-07-06 17:23:25,604 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T17:23:25.604009"}
2025-07-06 17:23:25,684 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:23:25,684 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 17:23:25,684 - ERROR - connection_pool - Params: None
2025-07-06 17:23:25,684 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 17:23:25,685 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:23:25,685 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 17:23:25,685 - ERROR - connection_pool - Params: None
2025-07-06 17:23:25,685 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 17:23:25,685 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:23:25,686 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 17:23:25,686 - ERROR - connection_pool - Params: None
2025-07-06 17:23:25,686 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 17:23:25,686 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:23:25,687 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 17:23:25,687 - ERROR - connection_pool - Params: None
2025-07-06 17:23:25,687 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 17:23:25,687 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 17:23:25,687 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 17:23:25,688 - ERROR - connection_pool - Params: None
2025-07-06 17:23:25,688 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 17:23:25,688 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 17:23:25,688 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 17:23:25,688 - ERROR - connection_pool - Params: None
2025-07-06 17:23:25,689 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 17:23:25,689 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 17:23:25,689 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 17:23:25,689 - ERROR - connection_pool - Params: None
2025-07-06 17:23:25,689 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 17:23:25,690 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 17:23:25,690 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 17:23:25,690 - ERROR - connection_pool - Params: None
2025-07-06 17:23:25,690 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 17:23:25,691 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 17:23:25,691 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 17:23:25,691 - ERROR - connection_pool - Params: None
2025-07-06 17:23:25,691 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 17:23:25,691 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 17:23:25,691 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 17:23:25,692 - ERROR - connection_pool - Params: None
2025-07-06 17:23:25,692 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 17:23:25,692 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 17:23:25,692 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 17:23:25,692 - ERROR - connection_pool - Params: None
2025-07-06 17:23:25,692 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 17:23:25,692 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 17:23:25,692 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 17:23:25,693 - ERROR - connection_pool - Params: None
2025-07-06 17:23:25,693 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 17:23:25,693 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 17:23:25,693 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 17:23:25,693 - ERROR - connection_pool - Params: None
2025-07-06 17:23:25,693 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 17:23:25,693 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 17:23:25,693 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 17:23:25,693 - ERROR - connection_pool - Params: None
2025-07-06 17:23:25,694 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 17:23:25,694 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 17:23:25,694 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 17:23:25,694 - ERROR - connection_pool - Params: None
2025-07-06 17:23:25,694 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 17:23:25,695 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:23:25,695 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 17:23:25,695 - ERROR - connection_pool - Params: None
2025-07-06 17:23:25,695 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 17:23:25,695 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:23:25,695 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 17:23:25,695 - ERROR - connection_pool - Params: None
2025-07-06 17:23:25,696 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 17:23:25,696 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:23:25,696 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 17:23:25,696 - ERROR - connection_pool - Params: None
2025-07-06 17:23:25,696 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 17:23:25,698 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 17:23:25,698 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 17:23:25,698 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 17:23:25,698 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 17:23:25,699 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 17:23:25,700 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 17:23:25,700 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 17:23:25,700 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 17:23:25,701 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 17:23:25,702 - INFO - sync_manager - Data synchronization service started
2025-07-06 17:23:25,703 - INFO - offline_manager - Offline storage initialized
2025-07-06 17:23:25,704 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 17:23:25,994 - INFO - logger - Notification system initialized
2025-07-06 17:23:26,016 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 17:32:49,589 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 17:32:49,591 - INFO - logger - Backup system initialized
2025-07-06 17:33:02,802 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T17:33:02.802749"}
2025-07-06 17:33:02,903 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:33:02,903 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 17:33:02,903 - ERROR - connection_pool - Params: None
2025-07-06 17:33:02,904 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 17:33:02,904 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:33:02,904 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 17:33:02,905 - ERROR - connection_pool - Params: None
2025-07-06 17:33:02,905 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 17:33:02,905 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:33:02,905 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 17:33:02,905 - ERROR - connection_pool - Params: None
2025-07-06 17:33:02,906 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 17:33:02,906 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:33:02,906 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 17:33:02,906 - ERROR - connection_pool - Params: None
2025-07-06 17:33:02,906 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 17:33:02,907 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 17:33:02,907 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 17:33:02,907 - ERROR - connection_pool - Params: None
2025-07-06 17:33:02,907 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 17:33:02,907 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 17:33:02,908 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 17:33:02,908 - ERROR - connection_pool - Params: None
2025-07-06 17:33:02,908 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 17:33:02,908 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 17:33:02,908 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 17:33:02,909 - ERROR - connection_pool - Params: None
2025-07-06 17:33:02,909 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 17:33:02,909 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 17:33:02,909 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 17:33:02,909 - ERROR - connection_pool - Params: None
2025-07-06 17:33:02,910 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 17:33:02,910 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 17:33:02,910 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 17:33:02,910 - ERROR - connection_pool - Params: None
2025-07-06 17:33:02,911 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 17:33:02,911 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 17:33:02,911 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 17:33:02,911 - ERROR - connection_pool - Params: None
2025-07-06 17:33:02,911 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 17:33:02,911 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 17:33:02,912 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 17:33:02,912 - ERROR - connection_pool - Params: None
2025-07-06 17:33:02,912 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 17:33:02,912 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 17:33:02,912 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 17:33:02,912 - ERROR - connection_pool - Params: None
2025-07-06 17:33:02,912 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 17:33:02,912 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 17:33:02,912 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 17:33:02,912 - ERROR - connection_pool - Params: None
2025-07-06 17:33:02,913 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 17:33:02,913 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 17:33:02,913 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 17:33:02,913 - ERROR - connection_pool - Params: None
2025-07-06 17:33:02,913 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 17:33:02,913 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 17:33:02,913 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 17:33:02,914 - ERROR - connection_pool - Params: None
2025-07-06 17:33:02,914 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 17:33:02,914 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:33:02,914 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 17:33:02,914 - ERROR - connection_pool - Params: None
2025-07-06 17:33:02,914 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 17:33:02,914 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:33:02,915 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 17:33:02,915 - ERROR - connection_pool - Params: None
2025-07-06 17:33:02,915 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 17:33:02,915 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:33:02,915 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 17:33:02,915 - ERROR - connection_pool - Params: None
2025-07-06 17:33:02,916 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 17:33:02,917 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 17:33:02,917 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 17:33:02,918 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 17:33:02,918 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 17:33:02,918 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 17:33:02,918 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 17:33:02,919 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 17:33:02,919 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 17:33:02,919 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 17:33:02,919 - INFO - sync_manager - Data synchronization service started
2025-07-06 17:33:02,920 - INFO - offline_manager - Offline storage initialized
2025-07-06 17:33:02,921 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 17:33:03,216 - INFO - logger - Notification system initialized
2025-07-06 17:33:03,237 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 17:38:37,838 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 17:38:37,840 - INFO - logger - Backup system initialized
2025-07-06 17:38:47,480 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T17:38:47.480774"}
2025-07-06 17:38:47,565 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:38:47,566 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 17:38:47,566 - ERROR - connection_pool - Params: None
2025-07-06 17:38:47,566 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 17:38:47,567 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:38:47,567 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 17:38:47,567 - ERROR - connection_pool - Params: None
2025-07-06 17:38:47,567 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 17:38:47,568 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:38:47,568 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 17:38:47,568 - ERROR - connection_pool - Params: None
2025-07-06 17:38:47,568 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 17:38:47,568 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:38:47,569 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 17:38:47,569 - ERROR - connection_pool - Params: None
2025-07-06 17:38:47,569 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 17:38:47,570 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 17:38:47,570 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 17:38:47,570 - ERROR - connection_pool - Params: None
2025-07-06 17:38:47,570 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 17:38:47,571 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 17:38:47,571 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 17:38:47,571 - ERROR - connection_pool - Params: None
2025-07-06 17:38:47,571 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 17:38:47,571 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 17:38:47,572 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 17:38:47,572 - ERROR - connection_pool - Params: None
2025-07-06 17:38:47,572 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 17:38:47,572 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 17:38:47,572 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 17:38:47,572 - ERROR - connection_pool - Params: None
2025-07-06 17:38:47,572 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 17:38:47,572 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 17:38:47,572 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 17:38:47,573 - ERROR - connection_pool - Params: None
2025-07-06 17:38:47,573 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 17:38:47,573 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 17:38:47,573 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 17:38:47,573 - ERROR - connection_pool - Params: None
2025-07-06 17:38:47,574 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 17:38:47,574 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 17:38:47,574 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 17:38:47,574 - ERROR - connection_pool - Params: None
2025-07-06 17:38:47,574 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 17:38:47,574 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 17:38:47,575 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 17:38:47,575 - ERROR - connection_pool - Params: None
2025-07-06 17:38:47,575 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 17:38:47,575 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 17:38:47,575 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 17:38:47,575 - ERROR - connection_pool - Params: None
2025-07-06 17:38:47,575 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 17:38:47,576 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 17:38:47,576 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 17:38:47,576 - ERROR - connection_pool - Params: None
2025-07-06 17:38:47,576 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 17:38:47,576 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 17:38:47,576 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 17:38:47,576 - ERROR - connection_pool - Params: None
2025-07-06 17:38:47,576 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 17:38:47,577 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:38:47,577 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 17:38:47,577 - ERROR - connection_pool - Params: None
2025-07-06 17:38:47,577 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 17:38:47,577 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:38:47,577 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 17:38:47,577 - ERROR - connection_pool - Params: None
2025-07-06 17:38:47,578 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 17:38:47,578 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:38:47,578 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 17:38:47,578 - ERROR - connection_pool - Params: None
2025-07-06 17:38:47,578 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 17:38:47,580 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 17:38:47,580 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 17:38:47,581 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 17:38:47,581 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 17:38:47,581 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 17:38:47,581 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 17:38:47,582 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 17:38:47,582 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 17:38:47,582 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 17:38:47,582 - INFO - sync_manager - Data synchronization service started
2025-07-06 17:38:47,583 - INFO - offline_manager - Offline storage initialized
2025-07-06 17:38:47,584 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 17:38:47,893 - INFO - logger - Notification system initialized
2025-07-06 17:38:47,914 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 17:42:54,479 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 17:42:54,480 - INFO - logger - Backup system initialized
2025-07-06 17:43:08,338 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T17:43:08.338083"}
2025-07-06 17:43:08,422 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:43:08,422 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 17:43:08,422 - ERROR - connection_pool - Params: None
2025-07-06 17:43:08,423 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 17:43:08,423 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:43:08,423 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 17:43:08,424 - ERROR - connection_pool - Params: None
2025-07-06 17:43:08,424 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 17:43:08,424 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:43:08,424 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 17:43:08,425 - ERROR - connection_pool - Params: None
2025-07-06 17:43:08,425 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 17:43:08,425 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:43:08,425 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 17:43:08,425 - ERROR - connection_pool - Params: None
2025-07-06 17:43:08,426 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 17:43:08,426 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 17:43:08,426 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 17:43:08,427 - ERROR - connection_pool - Params: None
2025-07-06 17:43:08,427 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 17:43:08,427 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 17:43:08,427 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 17:43:08,428 - ERROR - connection_pool - Params: None
2025-07-06 17:43:08,428 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 17:43:08,428 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 17:43:08,428 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 17:43:08,428 - ERROR - connection_pool - Params: None
2025-07-06 17:43:08,428 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 17:43:08,429 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 17:43:08,429 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 17:43:08,429 - ERROR - connection_pool - Params: None
2025-07-06 17:43:08,429 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 17:43:08,429 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 17:43:08,429 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 17:43:08,429 - ERROR - connection_pool - Params: None
2025-07-06 17:43:08,430 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 17:43:08,430 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 17:43:08,430 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 17:43:08,430 - ERROR - connection_pool - Params: None
2025-07-06 17:43:08,430 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 17:43:08,430 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 17:43:08,430 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 17:43:08,430 - ERROR - connection_pool - Params: None
2025-07-06 17:43:08,430 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 17:43:08,431 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 17:43:08,431 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 17:43:08,431 - ERROR - connection_pool - Params: None
2025-07-06 17:43:08,431 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 17:43:08,431 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 17:43:08,432 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 17:43:08,432 - ERROR - connection_pool - Params: None
2025-07-06 17:43:08,432 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 17:43:08,432 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 17:43:08,433 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 17:43:08,433 - ERROR - connection_pool - Params: None
2025-07-06 17:43:08,433 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 17:43:08,434 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 17:43:08,434 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 17:43:08,434 - ERROR - connection_pool - Params: None
2025-07-06 17:43:08,434 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 17:43:08,434 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:43:08,434 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 17:43:08,434 - ERROR - connection_pool - Params: None
2025-07-06 17:43:08,435 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 17:43:08,435 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:43:08,435 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 17:43:08,435 - ERROR - connection_pool - Params: None
2025-07-06 17:43:08,435 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 17:43:08,435 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:43:08,435 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 17:43:08,435 - ERROR - connection_pool - Params: None
2025-07-06 17:43:08,436 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 17:43:08,437 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 17:43:08,437 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 17:43:08,437 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 17:43:08,438 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 17:43:08,438 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 17:43:08,438 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 17:43:08,439 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 17:43:08,439 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 17:43:08,439 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 17:43:08,440 - INFO - sync_manager - Data synchronization service started
2025-07-06 17:43:08,441 - INFO - offline_manager - Offline storage initialized
2025-07-06 17:43:08,443 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 17:43:08,741 - INFO - logger - Notification system initialized
2025-07-06 17:43:08,761 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 17:55:48,831 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 17:55:48,832 - INFO - logger - Backup system initialized
2025-07-06 17:55:57,771 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T17:55:57.771492"}
2025-07-06 17:55:57,898 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:55:57,898 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 17:55:57,899 - ERROR - connection_pool - Params: None
2025-07-06 17:55:57,899 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 17:55:57,899 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:55:57,899 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 17:55:57,900 - ERROR - connection_pool - Params: None
2025-07-06 17:55:57,900 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 17:55:57,900 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:55:57,900 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 17:55:57,900 - ERROR - connection_pool - Params: None
2025-07-06 17:55:57,901 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 17:55:57,901 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:55:57,901 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 17:55:57,901 - ERROR - connection_pool - Params: None
2025-07-06 17:55:57,901 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 17:55:57,901 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 17:55:57,901 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 17:55:57,902 - ERROR - connection_pool - Params: None
2025-07-06 17:55:57,902 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 17:55:57,902 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 17:55:57,902 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 17:55:57,902 - ERROR - connection_pool - Params: None
2025-07-06 17:55:57,903 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 17:55:57,903 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 17:55:57,903 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 17:55:57,904 - ERROR - connection_pool - Params: None
2025-07-06 17:55:57,904 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 17:55:57,904 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 17:55:57,904 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 17:55:57,905 - ERROR - connection_pool - Params: None
2025-07-06 17:55:57,905 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 17:55:57,905 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 17:55:57,905 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 17:55:57,905 - ERROR - connection_pool - Params: None
2025-07-06 17:55:57,905 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 17:55:57,906 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 17:55:57,906 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 17:55:57,906 - ERROR - connection_pool - Params: None
2025-07-06 17:55:57,906 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 17:55:57,906 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 17:55:57,906 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 17:55:57,906 - ERROR - connection_pool - Params: None
2025-07-06 17:55:57,907 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 17:55:57,907 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 17:55:57,907 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 17:55:57,907 - ERROR - connection_pool - Params: None
2025-07-06 17:55:57,907 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 17:55:57,908 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 17:55:57,908 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 17:55:57,908 - ERROR - connection_pool - Params: None
2025-07-06 17:55:57,908 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 17:55:57,908 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 17:55:57,908 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 17:55:57,909 - ERROR - connection_pool - Params: None
2025-07-06 17:55:57,909 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 17:55:57,909 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 17:55:57,909 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 17:55:57,909 - ERROR - connection_pool - Params: None
2025-07-06 17:55:57,909 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 17:55:57,910 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:55:57,910 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 17:55:57,910 - ERROR - connection_pool - Params: None
2025-07-06 17:55:57,910 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 17:55:57,910 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:55:57,910 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 17:55:57,911 - ERROR - connection_pool - Params: None
2025-07-06 17:55:57,911 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 17:55:57,911 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:55:57,911 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 17:55:57,911 - ERROR - connection_pool - Params: None
2025-07-06 17:55:57,911 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 17:55:57,913 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 17:55:57,913 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 17:55:57,913 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 17:55:57,914 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 17:55:57,914 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 17:55:57,914 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 17:55:57,914 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 17:55:57,914 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 17:55:57,914 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 17:55:57,915 - INFO - sync_manager - Data synchronization service started
2025-07-06 17:55:57,916 - INFO - offline_manager - Offline storage initialized
2025-07-06 17:55:57,917 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 17:55:58,239 - INFO - logger - Notification system initialized
2025-07-06 17:55:58,267 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 17:57:13,535 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 17:57:13,536 - INFO - logger - Backup system initialized
2025-07-06 17:57:25,381 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T17:57:25.381076"}
2025-07-06 17:57:25,468 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:57:25,468 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 17:57:25,469 - ERROR - connection_pool - Params: None
2025-07-06 17:57:25,469 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 17:57:25,469 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:57:25,469 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 17:57:25,470 - ERROR - connection_pool - Params: None
2025-07-06 17:57:25,470 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 17:57:25,470 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:57:25,470 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 17:57:25,471 - ERROR - connection_pool - Params: None
2025-07-06 17:57:25,471 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 17:57:25,471 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 17:57:25,471 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 17:57:25,471 - ERROR - connection_pool - Params: None
2025-07-06 17:57:25,471 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 17:57:25,472 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 17:57:25,472 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 17:57:25,472 - ERROR - connection_pool - Params: None
2025-07-06 17:57:25,473 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 17:57:25,473 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 17:57:25,473 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 17:57:25,473 - ERROR - connection_pool - Params: None
2025-07-06 17:57:25,473 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 17:57:25,473 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 17:57:25,473 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 17:57:25,473 - ERROR - connection_pool - Params: None
2025-07-06 17:57:25,474 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 17:57:25,474 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 17:57:25,474 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 17:57:25,474 - ERROR - connection_pool - Params: None
2025-07-06 17:57:25,474 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 17:57:25,475 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 17:57:25,475 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 17:57:25,475 - ERROR - connection_pool - Params: None
2025-07-06 17:57:25,475 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 17:57:25,475 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 17:57:25,476 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 17:57:25,476 - ERROR - connection_pool - Params: None
2025-07-06 17:57:25,476 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 17:57:25,476 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 17:57:25,477 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 17:57:25,477 - ERROR - connection_pool - Params: None
2025-07-06 17:57:25,477 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 17:57:25,477 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 17:57:25,477 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 17:57:25,478 - ERROR - connection_pool - Params: None
2025-07-06 17:57:25,478 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 17:57:25,478 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 17:57:25,478 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 17:57:25,478 - ERROR - connection_pool - Params: None
2025-07-06 17:57:25,479 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 17:57:25,479 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 17:57:25,479 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 17:57:25,479 - ERROR - connection_pool - Params: None
2025-07-06 17:57:25,479 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 17:57:25,480 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 17:57:25,480 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 17:57:25,480 - ERROR - connection_pool - Params: None
2025-07-06 17:57:25,480 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 17:57:25,481 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:57:25,481 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 17:57:25,481 - ERROR - connection_pool - Params: None
2025-07-06 17:57:25,481 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 17:57:25,481 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:57:25,481 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 17:57:25,481 - ERROR - connection_pool - Params: None
2025-07-06 17:57:25,481 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 17:57:25,482 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 17:57:25,482 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 17:57:25,482 - ERROR - connection_pool - Params: None
2025-07-06 17:57:25,482 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 17:57:25,484 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 17:57:25,485 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 17:57:25,485 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 17:57:25,485 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 17:57:25,486 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 17:57:25,486 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 17:57:25,486 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 17:57:25,486 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 17:57:25,486 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 17:57:25,487 - INFO - sync_manager - Data synchronization service started
2025-07-06 17:57:25,488 - INFO - offline_manager - Offline storage initialized
2025-07-06 17:57:25,490 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 17:57:25,794 - INFO - logger - Notification system initialized
2025-07-06 17:57:25,814 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 19:06:46,680 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 19:06:46,683 - INFO - logger - Backup system initialized
2025-07-06 19:07:01,029 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T19:07:01.029437"}
2025-07-06 19:07:01,132 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:07:01,133 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 19:07:01,133 - ERROR - connection_pool - Params: None
2025-07-06 19:07:01,133 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 19:07:01,133 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:07:01,134 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 19:07:01,134 - ERROR - connection_pool - Params: None
2025-07-06 19:07:01,134 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 19:07:01,134 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:07:01,135 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 19:07:01,135 - ERROR - connection_pool - Params: None
2025-07-06 19:07:01,135 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 19:07:01,135 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:07:01,136 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 19:07:01,136 - ERROR - connection_pool - Params: None
2025-07-06 19:07:01,136 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 19:07:01,136 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 19:07:01,136 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 19:07:01,136 - ERROR - connection_pool - Params: None
2025-07-06 19:07:01,137 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 19:07:01,137 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 19:07:01,137 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 19:07:01,137 - ERROR - connection_pool - Params: None
2025-07-06 19:07:01,137 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 19:07:01,138 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:07:01,138 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 19:07:01,138 - ERROR - connection_pool - Params: None
2025-07-06 19:07:01,138 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 19:07:01,139 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:07:01,139 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 19:07:01,139 - ERROR - connection_pool - Params: None
2025-07-06 19:07:01,139 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 19:07:01,139 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:07:01,139 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 19:07:01,140 - ERROR - connection_pool - Params: None
2025-07-06 19:07:01,140 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 19:07:01,140 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:07:01,140 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 19:07:01,140 - ERROR - connection_pool - Params: None
2025-07-06 19:07:01,140 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 19:07:01,140 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 19:07:01,141 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 19:07:01,141 - ERROR - connection_pool - Params: None
2025-07-06 19:07:01,141 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 19:07:01,141 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 19:07:01,141 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 19:07:01,142 - ERROR - connection_pool - Params: None
2025-07-06 19:07:01,142 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 19:07:01,142 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:07:01,142 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 19:07:01,142 - ERROR - connection_pool - Params: None
2025-07-06 19:07:01,142 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 19:07:01,142 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:07:01,142 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 19:07:01,143 - ERROR - connection_pool - Params: None
2025-07-06 19:07:01,143 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 19:07:01,143 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 19:07:01,143 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 19:07:01,143 - ERROR - connection_pool - Params: None
2025-07-06 19:07:01,143 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 19:07:01,143 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:07:01,144 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 19:07:01,144 - ERROR - connection_pool - Params: None
2025-07-06 19:07:01,144 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 19:07:01,144 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:07:01,145 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 19:07:01,145 - ERROR - connection_pool - Params: None
2025-07-06 19:07:01,145 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 19:07:01,145 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:07:01,145 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 19:07:01,145 - ERROR - connection_pool - Params: None
2025-07-06 19:07:01,145 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 19:07:01,147 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 19:07:01,147 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 19:07:01,148 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 19:07:01,148 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 19:07:01,149 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 19:07:01,149 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 19:07:01,150 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 19:07:01,150 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 19:07:01,150 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 19:07:01,151 - INFO - sync_manager - Data synchronization service started
2025-07-06 19:07:01,152 - INFO - offline_manager - Offline storage initialized
2025-07-06 19:07:01,153 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 19:07:01,483 - INFO - logger - Notification system initialized
2025-07-06 19:07:01,510 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 19:11:22,400 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 19:11:22,402 - INFO - logger - Backup system initialized
2025-07-06 19:11:35,045 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T19:11:35.045261"}
2025-07-06 19:11:35,129 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:11:35,130 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 19:11:35,130 - ERROR - connection_pool - Params: None
2025-07-06 19:11:35,130 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 19:11:35,130 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:11:35,131 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 19:11:35,131 - ERROR - connection_pool - Params: None
2025-07-06 19:11:35,131 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 19:11:35,131 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:11:35,131 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 19:11:35,132 - ERROR - connection_pool - Params: None
2025-07-06 19:11:35,132 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 19:11:35,132 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:11:35,132 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 19:11:35,132 - ERROR - connection_pool - Params: None
2025-07-06 19:11:35,133 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 19:11:35,133 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 19:11:35,133 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 19:11:35,133 - ERROR - connection_pool - Params: None
2025-07-06 19:11:35,133 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 19:11:35,133 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 19:11:35,134 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 19:11:35,134 - ERROR - connection_pool - Params: None
2025-07-06 19:11:35,134 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 19:11:35,134 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:11:35,134 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 19:11:35,134 - ERROR - connection_pool - Params: None
2025-07-06 19:11:35,134 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 19:11:35,135 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:11:35,135 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 19:11:35,135 - ERROR - connection_pool - Params: None
2025-07-06 19:11:35,135 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 19:11:35,135 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:11:35,135 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 19:11:35,135 - ERROR - connection_pool - Params: None
2025-07-06 19:11:35,135 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 19:11:35,136 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:11:35,136 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 19:11:35,136 - ERROR - connection_pool - Params: None
2025-07-06 19:11:35,136 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 19:11:35,136 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 19:11:35,136 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 19:11:35,136 - ERROR - connection_pool - Params: None
2025-07-06 19:11:35,136 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 19:11:35,137 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 19:11:35,137 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 19:11:35,137 - ERROR - connection_pool - Params: None
2025-07-06 19:11:35,137 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 19:11:35,137 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:11:35,137 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 19:11:35,137 - ERROR - connection_pool - Params: None
2025-07-06 19:11:35,137 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 19:11:35,138 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:11:35,138 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 19:11:35,138 - ERROR - connection_pool - Params: None
2025-07-06 19:11:35,138 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 19:11:35,138 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 19:11:35,138 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 19:11:35,138 - ERROR - connection_pool - Params: None
2025-07-06 19:11:35,139 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 19:11:35,139 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:11:35,139 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 19:11:35,139 - ERROR - connection_pool - Params: None
2025-07-06 19:11:35,139 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 19:11:35,139 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:11:35,139 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 19:11:35,140 - ERROR - connection_pool - Params: None
2025-07-06 19:11:35,140 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 19:11:35,140 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:11:35,140 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 19:11:35,140 - ERROR - connection_pool - Params: None
2025-07-06 19:11:35,140 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 19:11:35,142 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 19:11:35,143 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 19:11:35,143 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 19:11:35,144 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 19:11:35,144 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 19:11:35,144 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 19:11:35,144 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 19:11:35,145 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 19:11:35,145 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 19:11:35,146 - INFO - sync_manager - Data synchronization service started
2025-07-06 19:11:35,147 - INFO - offline_manager - Offline storage initialized
2025-07-06 19:11:35,147 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 19:11:35,485 - INFO - logger - Notification system initialized
2025-07-06 19:11:35,510 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 19:17:23,790 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 19:17:23,791 - INFO - logger - Backup system initialized
2025-07-06 19:17:46,498 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T19:17:46.498571"}
2025-07-06 19:17:46,580 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:17:46,580 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 19:17:46,580 - ERROR - connection_pool - Params: None
2025-07-06 19:17:46,580 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 19:17:46,581 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:17:46,581 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 19:17:46,581 - ERROR - connection_pool - Params: None
2025-07-06 19:17:46,581 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 19:17:46,582 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:17:46,582 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 19:17:46,582 - ERROR - connection_pool - Params: None
2025-07-06 19:17:46,582 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 19:17:46,582 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:17:46,582 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 19:17:46,582 - ERROR - connection_pool - Params: None
2025-07-06 19:17:46,583 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 19:17:46,583 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 19:17:46,583 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 19:17:46,583 - ERROR - connection_pool - Params: None
2025-07-06 19:17:46,583 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 19:17:46,584 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 19:17:46,584 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 19:17:46,584 - ERROR - connection_pool - Params: None
2025-07-06 19:17:46,584 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 19:17:46,584 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:17:46,584 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 19:17:46,585 - ERROR - connection_pool - Params: None
2025-07-06 19:17:46,585 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 19:17:46,585 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:17:46,585 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 19:17:46,585 - ERROR - connection_pool - Params: None
2025-07-06 19:17:46,585 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 19:17:46,586 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:17:46,586 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 19:17:46,586 - ERROR - connection_pool - Params: None
2025-07-06 19:17:46,586 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 19:17:46,586 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:17:46,586 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 19:17:46,587 - ERROR - connection_pool - Params: None
2025-07-06 19:17:46,587 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 19:17:46,587 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 19:17:46,587 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 19:17:46,588 - ERROR - connection_pool - Params: None
2025-07-06 19:17:46,588 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 19:17:46,588 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 19:17:46,588 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 19:17:46,588 - ERROR - connection_pool - Params: None
2025-07-06 19:17:46,588 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 19:17:46,588 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:17:46,589 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 19:17:46,589 - ERROR - connection_pool - Params: None
2025-07-06 19:17:46,589 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 19:17:46,589 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:17:46,589 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 19:17:46,589 - ERROR - connection_pool - Params: None
2025-07-06 19:17:46,589 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 19:17:46,589 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 19:17:46,590 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 19:17:46,590 - ERROR - connection_pool - Params: None
2025-07-06 19:17:46,590 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 19:17:46,590 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:17:46,590 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 19:17:46,591 - ERROR - connection_pool - Params: None
2025-07-06 19:17:46,591 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 19:17:46,591 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:17:46,591 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 19:17:46,591 - ERROR - connection_pool - Params: None
2025-07-06 19:17:46,591 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 19:17:46,592 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:17:46,592 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 19:17:46,592 - ERROR - connection_pool - Params: None
2025-07-06 19:17:46,592 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 19:17:46,594 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 19:17:46,594 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 19:17:46,595 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 19:17:46,596 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 19:17:46,596 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 19:17:46,596 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 19:17:46,597 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 19:17:46,597 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 19:17:46,597 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 19:17:46,598 - INFO - sync_manager - Data synchronization service started
2025-07-06 19:17:46,599 - INFO - offline_manager - Offline storage initialized
2025-07-06 19:17:46,599 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 19:17:46,925 - INFO - logger - Notification system initialized
2025-07-06 19:17:46,949 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 19:18:26,460 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 19:18:26,462 - INFO - logger - Backup system initialized
2025-07-06 19:18:36,843 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T19:18:36.843245"}
2025-07-06 19:18:36,920 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:18:36,920 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 19:18:36,921 - ERROR - connection_pool - Params: None
2025-07-06 19:18:36,921 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 19:18:36,921 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:18:36,921 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 19:18:36,922 - ERROR - connection_pool - Params: None
2025-07-06 19:18:36,922 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 19:18:36,922 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:18:36,922 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 19:18:36,922 - ERROR - connection_pool - Params: None
2025-07-06 19:18:36,923 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 19:18:36,923 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:18:36,923 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 19:18:36,923 - ERROR - connection_pool - Params: None
2025-07-06 19:18:36,923 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 19:18:36,923 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 19:18:36,924 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 19:18:36,924 - ERROR - connection_pool - Params: None
2025-07-06 19:18:36,924 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 19:18:36,924 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 19:18:36,924 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 19:18:36,924 - ERROR - connection_pool - Params: None
2025-07-06 19:18:36,925 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 19:18:36,925 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:18:36,925 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 19:18:36,925 - ERROR - connection_pool - Params: None
2025-07-06 19:18:36,925 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 19:18:36,925 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:18:36,926 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 19:18:36,926 - ERROR - connection_pool - Params: None
2025-07-06 19:18:36,926 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 19:18:36,926 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:18:36,927 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 19:18:36,927 - ERROR - connection_pool - Params: None
2025-07-06 19:18:36,927 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 19:18:36,927 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:18:36,927 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 19:18:36,928 - ERROR - connection_pool - Params: None
2025-07-06 19:18:36,928 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 19:18:36,928 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 19:18:36,928 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 19:18:36,928 - ERROR - connection_pool - Params: None
2025-07-06 19:18:36,928 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 19:18:36,928 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 19:18:36,929 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 19:18:36,929 - ERROR - connection_pool - Params: None
2025-07-06 19:18:36,929 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 19:18:36,929 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:18:36,929 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 19:18:36,929 - ERROR - connection_pool - Params: None
2025-07-06 19:18:36,929 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 19:18:36,930 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:18:36,930 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 19:18:36,930 - ERROR - connection_pool - Params: None
2025-07-06 19:18:36,930 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 19:18:36,930 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 19:18:36,931 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 19:18:36,931 - ERROR - connection_pool - Params: None
2025-07-06 19:18:36,931 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 19:18:36,931 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:18:36,931 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 19:18:36,932 - ERROR - connection_pool - Params: None
2025-07-06 19:18:36,932 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 19:18:36,932 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:18:36,932 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 19:18:36,932 - ERROR - connection_pool - Params: None
2025-07-06 19:18:36,932 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 19:18:36,933 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:18:36,933 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 19:18:36,933 - ERROR - connection_pool - Params: None
2025-07-06 19:18:36,933 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 19:18:36,935 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 19:18:36,936 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 19:18:36,937 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 19:18:36,937 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 19:18:36,937 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 19:18:36,938 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 19:18:36,938 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 19:18:36,938 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 19:18:36,939 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 19:18:36,939 - INFO - sync_manager - Data synchronization service started
2025-07-06 19:18:36,940 - INFO - offline_manager - Offline storage initialized
2025-07-06 19:18:36,941 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 19:18:37,250 - INFO - logger - Notification system initialized
2025-07-06 19:18:37,274 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 19:18:53,914 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 19:18:53,915 - INFO - logger - Backup system initialized
2025-07-06 19:19:03,342 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T19:19:03.342297"}
2025-07-06 19:19:03,421 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:19:03,421 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 19:19:03,422 - ERROR - connection_pool - Params: None
2025-07-06 19:19:03,422 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 19:19:03,422 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:19:03,422 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 19:19:03,423 - ERROR - connection_pool - Params: None
2025-07-06 19:19:03,423 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 19:19:03,423 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:19:03,423 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 19:19:03,423 - ERROR - connection_pool - Params: None
2025-07-06 19:19:03,424 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 19:19:03,424 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:19:03,424 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 19:19:03,424 - ERROR - connection_pool - Params: None
2025-07-06 19:19:03,424 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 19:19:03,425 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 19:19:03,425 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 19:19:03,425 - ERROR - connection_pool - Params: None
2025-07-06 19:19:03,425 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 19:19:03,425 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 19:19:03,425 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 19:19:03,426 - ERROR - connection_pool - Params: None
2025-07-06 19:19:03,426 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 19:19:03,426 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:19:03,426 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 19:19:03,426 - ERROR - connection_pool - Params: None
2025-07-06 19:19:03,426 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 19:19:03,426 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:19:03,427 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 19:19:03,427 - ERROR - connection_pool - Params: None
2025-07-06 19:19:03,427 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 19:19:03,427 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:19:03,427 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 19:19:03,427 - ERROR - connection_pool - Params: None
2025-07-06 19:19:03,428 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 19:19:03,428 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:19:03,428 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 19:19:03,428 - ERROR - connection_pool - Params: None
2025-07-06 19:19:03,428 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 19:19:03,428 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 19:19:03,429 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 19:19:03,429 - ERROR - connection_pool - Params: None
2025-07-06 19:19:03,429 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 19:19:03,429 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 19:19:03,429 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 19:19:03,429 - ERROR - connection_pool - Params: None
2025-07-06 19:19:03,429 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 19:19:03,429 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:19:03,430 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 19:19:03,430 - ERROR - connection_pool - Params: None
2025-07-06 19:19:03,430 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 19:19:03,430 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:19:03,430 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 19:19:03,430 - ERROR - connection_pool - Params: None
2025-07-06 19:19:03,430 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 19:19:03,431 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 19:19:03,431 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 19:19:03,431 - ERROR - connection_pool - Params: None
2025-07-06 19:19:03,431 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 19:19:03,431 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:19:03,431 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 19:19:03,431 - ERROR - connection_pool - Params: None
2025-07-06 19:19:03,431 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 19:19:03,431 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:19:03,431 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 19:19:03,432 - ERROR - connection_pool - Params: None
2025-07-06 19:19:03,432 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 19:19:03,432 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:19:03,432 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 19:19:03,432 - ERROR - connection_pool - Params: None
2025-07-06 19:19:03,432 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 19:19:03,434 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 19:19:03,435 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 19:19:03,435 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 19:19:03,436 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 19:19:03,436 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 19:19:03,436 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 19:19:03,436 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 19:19:03,437 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 19:19:03,437 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 19:19:03,437 - INFO - sync_manager - Data synchronization service started
2025-07-06 19:19:03,439 - INFO - offline_manager - Offline storage initialized
2025-07-06 19:19:03,440 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 19:19:03,726 - INFO - logger - Notification system initialized
2025-07-06 19:19:03,746 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 19:19:29,795 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 19:19:29,796 - INFO - logger - Backup system initialized
2025-07-06 19:19:41,028 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T19:19:41.028420"}
2025-07-06 19:19:41,109 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:19:41,109 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 19:19:41,109 - ERROR - connection_pool - Params: None
2025-07-06 19:19:41,110 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 19:19:41,110 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:19:41,110 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 19:19:41,110 - ERROR - connection_pool - Params: None
2025-07-06 19:19:41,111 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 19:19:41,111 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:19:41,111 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 19:19:41,111 - ERROR - connection_pool - Params: None
2025-07-06 19:19:41,111 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 19:19:41,112 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:19:41,112 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 19:19:41,112 - ERROR - connection_pool - Params: None
2025-07-06 19:19:41,112 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 19:19:41,112 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 19:19:41,112 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 19:19:41,113 - ERROR - connection_pool - Params: None
2025-07-06 19:19:41,113 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 19:19:41,113 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 19:19:41,113 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 19:19:41,113 - ERROR - connection_pool - Params: None
2025-07-06 19:19:41,113 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 19:19:41,114 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:19:41,114 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 19:19:41,114 - ERROR - connection_pool - Params: None
2025-07-06 19:19:41,114 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 19:19:41,115 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:19:41,115 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 19:19:41,115 - ERROR - connection_pool - Params: None
2025-07-06 19:19:41,115 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 19:19:41,115 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:19:41,116 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 19:19:41,116 - ERROR - connection_pool - Params: None
2025-07-06 19:19:41,116 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 19:19:41,117 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:19:41,117 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 19:19:41,117 - ERROR - connection_pool - Params: None
2025-07-06 19:19:41,117 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 19:19:41,117 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 19:19:41,118 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 19:19:41,118 - ERROR - connection_pool - Params: None
2025-07-06 19:19:41,118 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 19:19:41,118 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 19:19:41,119 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 19:19:41,119 - ERROR - connection_pool - Params: None
2025-07-06 19:19:41,119 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 19:19:41,119 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:19:41,120 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 19:19:41,120 - ERROR - connection_pool - Params: None
2025-07-06 19:19:41,120 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 19:19:41,120 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:19:41,120 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 19:19:41,121 - ERROR - connection_pool - Params: None
2025-07-06 19:19:41,121 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 19:19:41,121 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 19:19:41,121 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 19:19:41,121 - ERROR - connection_pool - Params: None
2025-07-06 19:19:41,121 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 19:19:41,121 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:19:41,122 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 19:19:41,122 - ERROR - connection_pool - Params: None
2025-07-06 19:19:41,122 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 19:19:41,122 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:19:41,122 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 19:19:41,122 - ERROR - connection_pool - Params: None
2025-07-06 19:19:41,122 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 19:19:41,122 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:19:41,123 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 19:19:41,123 - ERROR - connection_pool - Params: None
2025-07-06 19:19:41,123 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 19:19:41,124 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 19:19:41,125 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 19:19:41,125 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 19:19:41,125 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 19:19:41,126 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 19:19:41,126 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 19:19:41,126 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 19:19:41,127 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 19:19:41,127 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 19:19:41,128 - INFO - sync_manager - Data synchronization service started
2025-07-06 19:19:41,128 - INFO - offline_manager - Offline storage initialized
2025-07-06 19:19:41,129 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 19:19:41,437 - INFO - logger - Notification system initialized
2025-07-06 19:19:41,457 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 19:23:13,876 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 19:23:13,877 - INFO - logger - Backup system initialized
2025-07-06 19:23:29,663 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T19:23:29.663151"}
2025-07-06 19:23:29,745 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:23:29,746 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 19:23:29,746 - ERROR - connection_pool - Params: None
2025-07-06 19:23:29,746 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 19:23:29,746 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:23:29,747 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 19:23:29,747 - ERROR - connection_pool - Params: None
2025-07-06 19:23:29,747 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 19:23:29,747 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:23:29,747 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 19:23:29,748 - ERROR - connection_pool - Params: None
2025-07-06 19:23:29,748 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 19:23:29,748 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:23:29,749 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 19:23:29,749 - ERROR - connection_pool - Params: None
2025-07-06 19:23:29,749 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 19:23:29,749 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 19:23:29,749 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 19:23:29,750 - ERROR - connection_pool - Params: None
2025-07-06 19:23:29,750 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 19:23:29,750 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 19:23:29,750 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 19:23:29,751 - ERROR - connection_pool - Params: None
2025-07-06 19:23:29,751 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 19:23:29,751 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:23:29,751 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 19:23:29,751 - ERROR - connection_pool - Params: None
2025-07-06 19:23:29,751 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 19:23:29,752 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:23:29,752 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 19:23:29,752 - ERROR - connection_pool - Params: None
2025-07-06 19:23:29,752 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 19:23:29,752 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:23:29,752 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 19:23:29,753 - ERROR - connection_pool - Params: None
2025-07-06 19:23:29,753 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 19:23:29,753 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:23:29,753 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 19:23:29,753 - ERROR - connection_pool - Params: None
2025-07-06 19:23:29,753 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 19:23:29,753 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 19:23:29,754 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 19:23:29,754 - ERROR - connection_pool - Params: None
2025-07-06 19:23:29,754 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 19:23:29,754 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 19:23:29,754 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 19:23:29,754 - ERROR - connection_pool - Params: None
2025-07-06 19:23:29,754 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 19:23:29,755 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:23:29,755 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 19:23:29,755 - ERROR - connection_pool - Params: None
2025-07-06 19:23:29,755 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 19:23:29,756 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:23:29,756 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 19:23:29,756 - ERROR - connection_pool - Params: None
2025-07-06 19:23:29,756 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 19:23:29,757 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 19:23:29,757 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 19:23:29,757 - ERROR - connection_pool - Params: None
2025-07-06 19:23:29,757 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 19:23:29,757 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:23:29,758 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 19:23:29,758 - ERROR - connection_pool - Params: None
2025-07-06 19:23:29,758 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 19:23:29,758 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:23:29,758 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 19:23:29,758 - ERROR - connection_pool - Params: None
2025-07-06 19:23:29,758 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 19:23:29,758 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:23:29,759 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 19:23:29,759 - ERROR - connection_pool - Params: None
2025-07-06 19:23:29,759 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 19:23:29,760 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 19:23:29,761 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 19:23:29,762 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 19:23:29,762 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 19:23:29,762 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 19:23:29,762 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 19:23:29,763 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 19:23:29,763 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 19:23:29,763 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 19:23:29,763 - INFO - sync_manager - Data synchronization service started
2025-07-06 19:23:29,764 - INFO - offline_manager - Offline storage initialized
2025-07-06 19:23:29,765 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 19:23:30,076 - INFO - logger - Notification system initialized
2025-07-06 19:23:30,101 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 19:27:20,712 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 19:27:20,714 - INFO - logger - Backup system initialized
2025-07-06 19:27:35,937 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T19:27:35.937164"}
2025-07-06 19:27:36,026 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:27:36,027 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 19:27:36,027 - ERROR - connection_pool - Params: None
2025-07-06 19:27:36,027 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 19:27:36,027 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:27:36,028 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 19:27:36,028 - ERROR - connection_pool - Params: None
2025-07-06 19:27:36,028 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 19:27:36,028 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:27:36,028 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 19:27:36,029 - ERROR - connection_pool - Params: None
2025-07-06 19:27:36,029 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 19:27:36,029 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:27:36,030 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 19:27:36,030 - ERROR - connection_pool - Params: None
2025-07-06 19:27:36,030 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 19:27:36,030 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 19:27:36,030 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 19:27:36,031 - ERROR - connection_pool - Params: None
2025-07-06 19:27:36,031 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 19:27:36,031 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 19:27:36,031 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 19:27:36,031 - ERROR - connection_pool - Params: None
2025-07-06 19:27:36,032 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 19:27:36,032 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:27:36,032 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 19:27:36,032 - ERROR - connection_pool - Params: None
2025-07-06 19:27:36,033 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 19:27:36,033 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:27:36,033 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 19:27:36,033 - ERROR - connection_pool - Params: None
2025-07-06 19:27:36,033 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 19:27:36,034 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:27:36,034 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 19:27:36,034 - ERROR - connection_pool - Params: None
2025-07-06 19:27:36,034 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 19:27:36,034 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:27:36,034 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 19:27:36,034 - ERROR - connection_pool - Params: None
2025-07-06 19:27:36,035 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 19:27:36,035 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 19:27:36,035 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 19:27:36,035 - ERROR - connection_pool - Params: None
2025-07-06 19:27:36,035 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 19:27:36,035 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 19:27:36,035 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 19:27:36,035 - ERROR - connection_pool - Params: None
2025-07-06 19:27:36,036 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 19:27:36,036 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:27:36,036 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 19:27:36,036 - ERROR - connection_pool - Params: None
2025-07-06 19:27:36,036 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 19:27:36,036 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:27:36,036 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 19:27:36,036 - ERROR - connection_pool - Params: None
2025-07-06 19:27:36,037 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 19:27:36,037 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 19:27:36,037 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 19:27:36,037 - ERROR - connection_pool - Params: None
2025-07-06 19:27:36,037 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 19:27:36,037 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:27:36,038 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 19:27:36,038 - ERROR - connection_pool - Params: None
2025-07-06 19:27:36,038 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 19:27:36,038 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:27:36,038 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 19:27:36,038 - ERROR - connection_pool - Params: None
2025-07-06 19:27:36,038 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 19:27:36,039 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:27:36,039 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 19:27:36,039 - ERROR - connection_pool - Params: None
2025-07-06 19:27:36,039 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 19:27:36,041 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 19:27:36,041 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 19:27:36,042 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 19:27:36,042 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 19:27:36,043 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 19:27:36,043 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 19:27:36,043 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 19:27:36,043 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 19:27:36,043 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 19:27:36,044 - INFO - sync_manager - Data synchronization service started
2025-07-06 19:27:36,045 - INFO - offline_manager - Offline storage initialized
2025-07-06 19:27:36,046 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 19:27:36,352 - INFO - logger - Notification system initialized
2025-07-06 19:27:36,372 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 19:28:13,669 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 19:28:13,670 - INFO - logger - Backup system initialized
2025-07-06 19:28:30,406 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T19:28:30.406040"}
2025-07-06 19:28:30,487 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:28:30,487 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 19:28:30,488 - ERROR - connection_pool - Params: None
2025-07-06 19:28:30,488 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 19:28:30,488 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:28:30,488 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 19:28:30,488 - ERROR - connection_pool - Params: None
2025-07-06 19:28:30,488 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 19:28:30,489 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:28:30,489 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 19:28:30,489 - ERROR - connection_pool - Params: None
2025-07-06 19:28:30,489 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 19:28:30,490 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:28:30,490 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 19:28:30,490 - ERROR - connection_pool - Params: None
2025-07-06 19:28:30,490 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 19:28:30,490 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 19:28:30,491 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 19:28:30,491 - ERROR - connection_pool - Params: None
2025-07-06 19:28:30,491 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 19:28:30,491 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 19:28:30,491 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 19:28:30,491 - ERROR - connection_pool - Params: None
2025-07-06 19:28:30,492 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 19:28:30,492 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:28:30,492 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 19:28:30,492 - ERROR - connection_pool - Params: None
2025-07-06 19:28:30,492 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 19:28:30,492 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:28:30,492 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 19:28:30,493 - ERROR - connection_pool - Params: None
2025-07-06 19:28:30,493 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 19:28:30,493 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:28:30,493 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 19:28:30,493 - ERROR - connection_pool - Params: None
2025-07-06 19:28:30,493 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 19:28:30,493 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:28:30,494 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 19:28:30,494 - ERROR - connection_pool - Params: None
2025-07-06 19:28:30,494 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 19:28:30,494 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 19:28:30,494 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 19:28:30,494 - ERROR - connection_pool - Params: None
2025-07-06 19:28:30,494 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 19:28:30,495 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 19:28:30,495 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 19:28:30,495 - ERROR - connection_pool - Params: None
2025-07-06 19:28:30,495 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 19:28:30,495 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:28:30,495 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 19:28:30,495 - ERROR - connection_pool - Params: None
2025-07-06 19:28:30,495 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 19:28:30,495 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:28:30,496 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 19:28:30,496 - ERROR - connection_pool - Params: None
2025-07-06 19:28:30,496 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 19:28:30,496 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 19:28:30,496 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 19:28:30,496 - ERROR - connection_pool - Params: None
2025-07-06 19:28:30,496 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 19:28:30,496 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:28:30,496 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 19:28:30,497 - ERROR - connection_pool - Params: None
2025-07-06 19:28:30,497 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 19:28:30,497 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:28:30,497 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 19:28:30,497 - ERROR - connection_pool - Params: None
2025-07-06 19:28:30,497 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 19:28:30,497 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:28:30,497 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 19:28:30,497 - ERROR - connection_pool - Params: None
2025-07-06 19:28:30,498 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 19:28:30,500 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 19:28:30,500 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 19:28:30,500 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 19:28:30,501 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 19:28:30,501 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 19:28:30,501 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 19:28:30,501 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 19:28:30,501 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 19:28:30,502 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 19:28:30,502 - INFO - sync_manager - Data synchronization service started
2025-07-06 19:28:30,504 - INFO - offline_manager - Offline storage initialized
2025-07-06 19:28:30,505 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 19:28:30,822 - INFO - logger - Notification system initialized
2025-07-06 19:28:30,846 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 19:31:11,028 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 19:31:11,029 - INFO - logger - Backup system initialized
2025-07-06 19:31:23,426 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T19:31:23.426191"}
2025-07-06 19:31:23,514 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:31:23,515 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 19:31:23,515 - ERROR - connection_pool - Params: None
2025-07-06 19:31:23,515 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 19:31:23,516 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:31:23,516 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 19:31:23,516 - ERROR - connection_pool - Params: None
2025-07-06 19:31:23,516 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 19:31:23,517 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:31:23,517 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 19:31:23,517 - ERROR - connection_pool - Params: None
2025-07-06 19:31:23,517 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 19:31:23,518 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:31:23,518 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 19:31:23,518 - ERROR - connection_pool - Params: None
2025-07-06 19:31:23,518 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 19:31:23,519 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 19:31:23,519 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 19:31:23,519 - ERROR - connection_pool - Params: None
2025-07-06 19:31:23,519 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 19:31:23,519 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 19:31:23,519 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 19:31:23,520 - ERROR - connection_pool - Params: None
2025-07-06 19:31:23,520 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 19:31:23,520 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:31:23,521 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 19:31:23,521 - ERROR - connection_pool - Params: None
2025-07-06 19:31:23,521 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 19:31:23,521 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:31:23,521 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 19:31:23,522 - ERROR - connection_pool - Params: None
2025-07-06 19:31:23,522 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 19:31:23,522 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:31:23,522 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 19:31:23,522 - ERROR - connection_pool - Params: None
2025-07-06 19:31:23,522 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 19:31:23,523 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:31:23,523 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 19:31:23,523 - ERROR - connection_pool - Params: None
2025-07-06 19:31:23,523 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 19:31:23,524 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 19:31:23,524 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 19:31:23,524 - ERROR - connection_pool - Params: None
2025-07-06 19:31:23,524 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 19:31:23,524 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 19:31:23,524 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 19:31:23,525 - ERROR - connection_pool - Params: None
2025-07-06 19:31:23,525 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 19:31:23,525 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:31:23,525 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 19:31:23,525 - ERROR - connection_pool - Params: None
2025-07-06 19:31:23,526 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 19:31:23,526 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:31:23,526 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 19:31:23,526 - ERROR - connection_pool - Params: None
2025-07-06 19:31:23,526 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 19:31:23,526 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 19:31:23,527 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 19:31:23,527 - ERROR - connection_pool - Params: None
2025-07-06 19:31:23,527 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 19:31:23,527 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:31:23,528 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 19:31:23,528 - ERROR - connection_pool - Params: None
2025-07-06 19:31:23,528 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 19:31:23,528 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:31:23,528 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 19:31:23,529 - ERROR - connection_pool - Params: None
2025-07-06 19:31:23,529 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 19:31:23,530 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:31:23,530 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 19:31:23,530 - ERROR - connection_pool - Params: None
2025-07-06 19:31:23,530 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 19:31:23,532 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 19:31:23,533 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 19:31:23,533 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 19:31:23,533 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 19:31:23,533 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 19:31:23,534 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 19:31:23,534 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 19:31:23,534 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 19:31:23,534 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 19:31:23,535 - INFO - sync_manager - Data synchronization service started
2025-07-06 19:31:23,536 - INFO - offline_manager - Offline storage initialized
2025-07-06 19:31:23,537 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 19:31:23,854 - INFO - logger - Notification system initialized
2025-07-06 19:31:23,877 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 19:32:24,639 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 19:32:24,640 - INFO - logger - Backup system initialized
2025-07-06 19:32:34,939 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T19:32:34.939718"}
2025-07-06 19:32:35,023 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:32:35,023 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 19:32:35,023 - ERROR - connection_pool - Params: None
2025-07-06 19:32:35,024 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 19:32:35,024 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:32:35,024 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 19:32:35,024 - ERROR - connection_pool - Params: None
2025-07-06 19:32:35,024 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 19:32:35,025 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:32:35,025 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 19:32:35,025 - ERROR - connection_pool - Params: None
2025-07-06 19:32:35,025 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 19:32:35,025 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:32:35,026 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 19:32:35,026 - ERROR - connection_pool - Params: None
2025-07-06 19:32:35,026 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 19:32:35,026 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 19:32:35,027 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 19:32:35,027 - ERROR - connection_pool - Params: None
2025-07-06 19:32:35,027 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 19:32:35,027 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 19:32:35,027 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 19:32:35,028 - ERROR - connection_pool - Params: None
2025-07-06 19:32:35,028 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 19:32:35,028 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:32:35,028 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 19:32:35,029 - ERROR - connection_pool - Params: None
2025-07-06 19:32:35,029 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 19:32:35,029 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:32:35,029 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 19:32:35,029 - ERROR - connection_pool - Params: None
2025-07-06 19:32:35,030 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 19:32:35,030 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:32:35,030 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 19:32:35,030 - ERROR - connection_pool - Params: None
2025-07-06 19:32:35,030 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 19:32:35,030 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:32:35,031 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 19:32:35,031 - ERROR - connection_pool - Params: None
2025-07-06 19:32:35,031 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 19:32:35,031 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 19:32:35,031 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 19:32:35,031 - ERROR - connection_pool - Params: None
2025-07-06 19:32:35,031 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 19:32:35,031 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 19:32:35,032 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 19:32:35,032 - ERROR - connection_pool - Params: None
2025-07-06 19:32:35,032 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 19:32:35,032 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:32:35,032 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 19:32:35,032 - ERROR - connection_pool - Params: None
2025-07-06 19:32:35,032 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 19:32:35,032 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:32:35,033 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 19:32:35,033 - ERROR - connection_pool - Params: None
2025-07-06 19:32:35,033 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 19:32:35,033 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 19:32:35,033 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 19:32:35,033 - ERROR - connection_pool - Params: None
2025-07-06 19:32:35,033 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 19:32:35,034 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:32:35,034 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 19:32:35,034 - ERROR - connection_pool - Params: None
2025-07-06 19:32:35,034 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 19:32:35,034 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:32:35,034 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 19:32:35,035 - ERROR - connection_pool - Params: None
2025-07-06 19:32:35,035 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 19:32:35,035 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:32:35,035 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 19:32:35,035 - ERROR - connection_pool - Params: None
2025-07-06 19:32:35,035 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 19:32:35,038 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 19:32:35,038 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 19:32:35,039 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 19:32:35,039 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 19:32:35,040 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 19:32:35,040 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 19:32:35,040 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 19:32:35,041 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 19:32:35,041 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 19:32:35,042 - INFO - sync_manager - Data synchronization service started
2025-07-06 19:32:35,043 - INFO - offline_manager - Offline storage initialized
2025-07-06 19:32:35,044 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 19:32:35,354 - INFO - logger - Notification system initialized
2025-07-06 19:32:35,376 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 19:41:41,570 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 19:41:41,573 - INFO - logger - Backup system initialized
2025-07-06 19:41:53,008 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T19:41:53.008800"}
2025-07-06 19:41:53,116 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:41:53,117 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 19:41:53,117 - ERROR - connection_pool - Params: None
2025-07-06 19:41:53,117 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 19:41:53,118 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:41:53,118 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 19:41:53,118 - ERROR - connection_pool - Params: None
2025-07-06 19:41:53,118 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 19:41:53,119 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:41:53,119 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 19:41:53,119 - ERROR - connection_pool - Params: None
2025-07-06 19:41:53,119 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 19:41:53,119 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:41:53,120 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 19:41:53,120 - ERROR - connection_pool - Params: None
2025-07-06 19:41:53,120 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 19:41:53,120 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 19:41:53,120 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 19:41:53,120 - ERROR - connection_pool - Params: None
2025-07-06 19:41:53,121 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 19:41:53,121 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 19:41:53,121 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 19:41:53,121 - ERROR - connection_pool - Params: None
2025-07-06 19:41:53,122 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 19:41:53,122 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:41:53,122 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 19:41:53,123 - ERROR - connection_pool - Params: None
2025-07-06 19:41:53,123 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 19:41:53,123 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:41:53,123 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 19:41:53,123 - ERROR - connection_pool - Params: None
2025-07-06 19:41:53,123 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 19:41:53,124 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:41:53,124 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 19:41:53,124 - ERROR - connection_pool - Params: None
2025-07-06 19:41:53,124 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 19:41:53,124 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:41:53,124 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 19:41:53,125 - ERROR - connection_pool - Params: None
2025-07-06 19:41:53,125 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 19:41:53,125 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 19:41:53,125 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 19:41:53,125 - ERROR - connection_pool - Params: None
2025-07-06 19:41:53,126 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 19:41:53,126 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 19:41:53,126 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 19:41:53,126 - ERROR - connection_pool - Params: None
2025-07-06 19:41:53,126 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 19:41:53,127 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:41:53,127 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 19:41:53,127 - ERROR - connection_pool - Params: None
2025-07-06 19:41:53,127 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 19:41:53,127 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:41:53,127 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 19:41:53,127 - ERROR - connection_pool - Params: None
2025-07-06 19:41:53,128 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 19:41:53,128 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 19:41:53,128 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 19:41:53,128 - ERROR - connection_pool - Params: None
2025-07-06 19:41:53,128 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 19:41:53,129 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:41:53,129 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 19:41:53,129 - ERROR - connection_pool - Params: None
2025-07-06 19:41:53,129 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 19:41:53,129 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:41:53,129 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 19:41:53,129 - ERROR - connection_pool - Params: None
2025-07-06 19:41:53,130 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 19:41:53,130 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:41:53,130 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 19:41:53,130 - ERROR - connection_pool - Params: None
2025-07-06 19:41:53,130 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 19:41:53,132 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 19:41:53,132 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 19:41:53,132 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 19:41:53,132 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 19:41:53,133 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 19:41:53,133 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 19:41:53,133 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 19:41:53,133 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 19:41:53,134 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 19:41:53,134 - INFO - sync_manager - Data synchronization service started
2025-07-06 19:41:53,135 - INFO - offline_manager - Offline storage initialized
2025-07-06 19:41:53,136 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 19:41:53,471 - INFO - logger - Notification system initialized
2025-07-06 19:41:53,496 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 19:49:28,282 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 19:49:28,283 - INFO - logger - Backup system initialized
2025-07-06 19:49:39,477 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T19:49:39.476910"}
2025-07-06 19:49:39,568 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:49:39,568 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 19:49:39,569 - ERROR - connection_pool - Params: None
2025-07-06 19:49:39,569 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 19:49:39,570 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:49:39,570 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 19:49:39,570 - ERROR - connection_pool - Params: None
2025-07-06 19:49:39,570 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 19:49:39,570 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:49:39,570 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 19:49:39,571 - ERROR - connection_pool - Params: None
2025-07-06 19:49:39,571 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 19:49:39,571 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:49:39,571 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 19:49:39,571 - ERROR - connection_pool - Params: None
2025-07-06 19:49:39,572 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 19:49:39,572 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 19:49:39,572 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 19:49:39,572 - ERROR - connection_pool - Params: None
2025-07-06 19:49:39,572 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 19:49:39,572 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 19:49:39,573 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 19:49:39,573 - ERROR - connection_pool - Params: None
2025-07-06 19:49:39,573 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 19:49:39,573 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:49:39,573 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 19:49:39,573 - ERROR - connection_pool - Params: None
2025-07-06 19:49:39,574 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 19:49:39,574 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:49:39,574 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 19:49:39,574 - ERROR - connection_pool - Params: None
2025-07-06 19:49:39,574 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 19:49:39,575 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:49:39,575 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 19:49:39,575 - ERROR - connection_pool - Params: None
2025-07-06 19:49:39,575 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 19:49:39,575 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:49:39,575 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 19:49:39,575 - ERROR - connection_pool - Params: None
2025-07-06 19:49:39,576 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 19:49:39,576 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 19:49:39,576 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 19:49:39,576 - ERROR - connection_pool - Params: None
2025-07-06 19:49:39,576 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 19:49:39,576 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 19:49:39,576 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 19:49:39,576 - ERROR - connection_pool - Params: None
2025-07-06 19:49:39,576 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 19:49:39,577 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:49:39,577 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 19:49:39,577 - ERROR - connection_pool - Params: None
2025-07-06 19:49:39,577 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 19:49:39,577 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:49:39,577 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 19:49:39,577 - ERROR - connection_pool - Params: None
2025-07-06 19:49:39,577 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 19:49:39,577 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 19:49:39,578 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 19:49:39,578 - ERROR - connection_pool - Params: None
2025-07-06 19:49:39,578 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 19:49:39,578 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:49:39,578 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 19:49:39,578 - ERROR - connection_pool - Params: None
2025-07-06 19:49:39,578 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 19:49:39,579 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:49:39,579 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 19:49:39,579 - ERROR - connection_pool - Params: None
2025-07-06 19:49:39,579 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 19:49:39,580 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:49:39,580 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 19:49:39,580 - ERROR - connection_pool - Params: None
2025-07-06 19:49:39,580 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 19:49:39,582 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 19:49:39,582 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 19:49:39,583 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 19:49:39,583 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 19:49:39,584 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 19:49:39,584 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 19:49:39,584 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 19:49:39,585 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 19:49:39,585 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 19:49:39,586 - INFO - sync_manager - Data synchronization service started
2025-07-06 19:49:39,586 - INFO - offline_manager - Offline storage initialized
2025-07-06 19:49:39,587 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 19:49:39,899 - INFO - logger - Notification system initialized
2025-07-06 19:49:39,922 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 19:53:35,419 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 19:53:35,422 - INFO - logger - Backup system initialized
2025-07-06 19:53:46,392 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T19:53:46.392072"}
2025-07-06 19:53:46,480 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:53:46,480 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 19:53:46,480 - ERROR - connection_pool - Params: None
2025-07-06 19:53:46,480 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 19:53:46,481 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:53:46,481 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 19:53:46,481 - ERROR - connection_pool - Params: None
2025-07-06 19:53:46,481 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 19:53:46,482 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:53:46,482 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 19:53:46,482 - ERROR - connection_pool - Params: None
2025-07-06 19:53:46,482 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 19:53:46,482 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:53:46,482 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 19:53:46,483 - ERROR - connection_pool - Params: None
2025-07-06 19:53:46,483 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 19:53:46,483 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 19:53:46,483 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 19:53:46,483 - ERROR - connection_pool - Params: None
2025-07-06 19:53:46,484 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 19:53:46,484 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 19:53:46,484 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 19:53:46,484 - ERROR - connection_pool - Params: None
2025-07-06 19:53:46,484 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 19:53:46,485 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:53:46,485 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 19:53:46,485 - ERROR - connection_pool - Params: None
2025-07-06 19:53:46,485 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 19:53:46,486 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:53:46,486 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 19:53:46,486 - ERROR - connection_pool - Params: None
2025-07-06 19:53:46,486 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 19:53:46,487 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:53:46,487 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 19:53:46,487 - ERROR - connection_pool - Params: None
2025-07-06 19:53:46,487 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 19:53:46,487 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:53:46,488 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 19:53:46,488 - ERROR - connection_pool - Params: None
2025-07-06 19:53:46,488 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 19:53:46,488 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 19:53:46,488 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 19:53:46,488 - ERROR - connection_pool - Params: None
2025-07-06 19:53:46,489 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 19:53:46,489 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 19:53:46,489 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 19:53:46,489 - ERROR - connection_pool - Params: None
2025-07-06 19:53:46,489 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 19:53:46,489 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:53:46,489 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 19:53:46,489 - ERROR - connection_pool - Params: None
2025-07-06 19:53:46,490 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 19:53:46,490 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:53:46,490 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 19:53:46,490 - ERROR - connection_pool - Params: None
2025-07-06 19:53:46,490 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 19:53:46,491 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 19:53:46,491 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 19:53:46,491 - ERROR - connection_pool - Params: None
2025-07-06 19:53:46,491 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 19:53:46,491 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:53:46,491 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 19:53:46,492 - ERROR - connection_pool - Params: None
2025-07-06 19:53:46,492 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 19:53:46,492 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:53:46,492 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 19:53:46,492 - ERROR - connection_pool - Params: None
2025-07-06 19:53:46,492 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 19:53:46,492 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:53:46,493 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 19:53:46,493 - ERROR - connection_pool - Params: None
2025-07-06 19:53:46,493 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 19:53:46,494 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 19:53:46,495 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 19:53:46,495 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 19:53:46,495 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 19:53:46,496 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 19:53:46,496 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 19:53:46,496 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 19:53:46,496 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 19:53:46,496 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 19:53:46,497 - INFO - sync_manager - Data synchronization service started
2025-07-06 19:53:46,498 - INFO - offline_manager - Offline storage initialized
2025-07-06 19:53:46,498 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 19:53:46,838 - INFO - logger - Notification system initialized
2025-07-06 19:53:46,864 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-06 19:54:06,204 - INFO - error_handling - IntegrationAPIs: Конфигурации интеграций загружены
2025-07-06 19:54:06,205 - INFO - error_handling - IntegrationAPIs: Система интеграции и API создана
2025-07-06 19:54:39,736 - INFO - security_manager - Default admin user created: admin/admin123
2025-07-06 19:56:16,972 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-06 19:56:16,974 - INFO - logger - Backup system initialized
2025-07-06 19:56:46,848 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-06T19:56:46.848358"}
2025-07-06 19:56:46,931 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:56:46,932 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-06 19:56:46,932 - ERROR - connection_pool - Params: None
2025-07-06 19:56:46,933 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-06 19:56:46,933 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:56:46,933 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-06 19:56:46,933 - ERROR - connection_pool - Params: None
2025-07-06 19:56:46,934 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-06 19:56:46,934 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:56:46,934 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-06 19:56:46,934 - ERROR - connection_pool - Params: None
2025-07-06 19:56:46,934 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-06 19:56:46,935 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-06 19:56:46,935 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-06 19:56:46,935 - ERROR - connection_pool - Params: None
2025-07-06 19:56:46,935 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-06 19:56:46,935 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-06 19:56:46,935 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-06 19:56:46,936 - ERROR - connection_pool - Params: None
2025-07-06 19:56:46,936 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-06 19:56:46,936 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-06 19:56:46,936 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-06 19:56:46,936 - ERROR - connection_pool - Params: None
2025-07-06 19:56:46,937 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-06 19:56:46,937 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:56:46,937 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-06 19:56:46,937 - ERROR - connection_pool - Params: None
2025-07-06 19:56:46,937 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-06 19:56:46,937 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-06 19:56:46,938 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-06 19:56:46,938 - ERROR - connection_pool - Params: None
2025-07-06 19:56:46,938 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-06 19:56:46,938 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:56:46,938 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-06 19:56:46,938 - ERROR - connection_pool - Params: None
2025-07-06 19:56:46,938 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-06 19:56:46,939 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-06 19:56:46,939 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-06 19:56:46,939 - ERROR - connection_pool - Params: None
2025-07-06 19:56:46,939 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-06 19:56:46,939 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-06 19:56:46,939 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-06 19:56:46,940 - ERROR - connection_pool - Params: None
2025-07-06 19:56:46,940 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-06 19:56:46,940 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-06 19:56:46,940 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-06 19:56:46,940 - ERROR - connection_pool - Params: None
2025-07-06 19:56:46,940 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-06 19:56:46,940 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:56:46,941 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-06 19:56:46,941 - ERROR - connection_pool - Params: None
2025-07-06 19:56:46,941 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-06 19:56:46,941 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-06 19:56:46,941 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-06 19:56:46,941 - ERROR - connection_pool - Params: None
2025-07-06 19:56:46,941 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-06 19:56:46,941 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-06 19:56:46,941 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-06 19:56:46,941 - ERROR - connection_pool - Params: None
2025-07-06 19:56:46,942 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-06 19:56:46,942 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:56:46,942 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-06 19:56:46,942 - ERROR - connection_pool - Params: None
2025-07-06 19:56:46,942 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-06 19:56:46,943 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:56:46,943 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-06 19:56:46,943 - ERROR - connection_pool - Params: None
2025-07-06 19:56:46,943 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-06 19:56:46,943 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-06 19:56:46,943 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-06 19:56:46,943 - ERROR - connection_pool - Params: None
2025-07-06 19:56:46,944 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-06 19:56:46,946 - WARNING - sync_manager - Empty schema for table sales
2025-07-06 19:56:46,946 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-06 19:56:46,946 - WARNING - sync_manager - Empty schema for table recipes
2025-07-06 19:56:46,947 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-06 19:56:46,947 - WARNING - sync_manager - Empty schema for table customers
2025-07-06 19:56:46,947 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-06 19:56:46,948 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-06 19:56:46,948 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-06 19:56:46,948 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-06 19:56:46,949 - INFO - sync_manager - Data synchronization service started
2025-07-06 19:56:46,950 - INFO - offline_manager - Offline storage initialized
2025-07-06 19:56:46,951 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-06 19:56:47,244 - INFO - logger - Notification system initialized
2025-07-06 19:56:47,266 - INFO - logger - Application initialized successfully | {"user_id": 1}
