# -*- coding: utf-8 -*-
"""
Language Settings Module for Restaurant Management System
Provides interface for changing language and localization settings
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
from datetime import datetime

try:
    from localization.localization_manager import get_localization_manager, get_text, format_currency, format_date
    from utils.error_handling import handle_module_error
    from utils.logger import log_info, log_error
    from utils.window_utils import center_window, apply_theme
except ImportError as e:
    print(f"Import error in language_settings: {e}")

class LanguageSettingsManager:
    """Manager for language and localization settings"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.localization_manager = get_localization_manager()
        self.settings_file = "config/language_settings.json"
        self.current_settings = self.load_settings()
    
    def show_language_settings(self):
        """Show language settings window"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
        
        self.create_language_settings_window()
    
    def create_language_settings_window(self):
        """Create the language settings window"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(get_text('LANGUAGE_SETTINGS_TITLE', 'Language Settings'))
        self.window.geometry("800x600")
        self.window.resizable(True, True)
        
        # Apply theme
        try:
            apply_theme(self.window)
        except:
            pass
        
        # Center window
        try:
            center_window(self.window)
        except:
            pass
        
        # Create main frame
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill='both', expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, 
                               text=get_text('LANGUAGE_SETTINGS_TITLE', 'Language & Localization Settings'),
                               font=('Cambria', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # Create notebook for different settings categories
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill='both', expand=True, pady=(0, 20))
        
        # Language Selection Tab
        self.create_language_tab(notebook)
        
        # Regional Settings Tab
        self.create_regional_tab(notebook)
        
        # Format Preview Tab
        self.create_preview_tab(notebook)
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill='x', pady=(10, 0))
        
        ttk.Button(buttons_frame, text=get_text('BUTTON_SAVE', 'Save'),
                  command=self.save_settings).pack(side='right', padx=(5, 0))
        ttk.Button(buttons_frame, text=get_text('BUTTON_CANCEL', 'Cancel'),
                  command=self.window.destroy).pack(side='right')
        ttk.Button(buttons_frame, text=get_text('BUTTON_APPLY', 'Apply'),
                  command=self.apply_settings).pack(side='right', padx=(0, 5))
    
    def create_language_tab(self, notebook):
        """Create language selection tab"""
        lang_frame = ttk.Frame(notebook, padding="20")
        notebook.add(lang_frame, text=get_text('TAB_LANGUAGE', 'Language'))
        
        # Current language section
        current_section = ttk.LabelFrame(lang_frame, text=get_text('CURRENT_LANGUAGE', 'Current Language'), padding="10")
        current_section.pack(fill='x', pady=(0, 20))
        
        current_lang = self.localization_manager.get_available_languages().get(
            self.localization_manager.current_language, 'Unknown')
        ttk.Label(current_section, text=f"{get_text('CURRENT', 'Current')}: {current_lang}",
                 font=('Cambria', 12, 'bold')).pack(anchor='w')
        
        # Language selection section
        selection_section = ttk.LabelFrame(lang_frame, text=get_text('SELECT_LANGUAGE', 'Select Language'), padding="10")
        selection_section.pack(fill='x', pady=(0, 20))
        
        self.language_var = tk.StringVar(value=self.localization_manager.current_language)
        
        languages = self.localization_manager.get_available_languages()
        for code, name in languages.items():
            ttk.Radiobutton(selection_section, text=f"{name} ({code})", 
                           variable=self.language_var, value=code,
                           command=self.on_language_change).pack(anchor='w', pady=2)
        
        # Language info section
        info_section = ttk.LabelFrame(lang_frame, text=get_text('LANGUAGE_INFO', 'Language Information'), padding="10")
        info_section.pack(fill='both', expand=True)
        
        self.language_info_text = tk.Text(info_section, height=8, wrap='word')
        scrollbar = ttk.Scrollbar(info_section, orient='vertical', command=self.language_info_text.yview)
        self.language_info_text.configure(yscrollcommand=scrollbar.set)
        
        self.language_info_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        self.update_language_info()
    
    def create_regional_tab(self, notebook):
        """Create regional settings tab"""
        regional_frame = ttk.Frame(notebook, padding="20")
        notebook.add(regional_frame, text=get_text('TAB_REGIONAL', 'Regional'))
        
        # Currency settings
        currency_section = ttk.LabelFrame(regional_frame, text=get_text('CURRENCY_SETTINGS', 'Currency Settings'), padding="10")
        currency_section.pack(fill='x', pady=(0, 20))
        
        # Currency format info
        current_currency = self.localization_manager.currency_formats.get(
            self.localization_manager.current_language, {})
        
        ttk.Label(currency_section, text=f"{get_text('CURRENCY_SYMBOL', 'Symbol')}: {current_currency.get('symbol', 'N/A')}").pack(anchor='w')
        ttk.Label(currency_section, text=f"{get_text('DECIMAL_SEPARATOR', 'Decimal Separator')}: {current_currency.get('decimal_separator', 'N/A')}").pack(anchor='w')
        ttk.Label(currency_section, text=f"{get_text('THOUSAND_SEPARATOR', 'Thousand Separator')}: '{current_currency.get('thousand_separator', 'N/A')}'").pack(anchor='w')
        
        # Date format settings
        date_section = ttk.LabelFrame(regional_frame, text=get_text('DATE_SETTINGS', 'Date Format Settings'), padding="10")
        date_section.pack(fill='x', pady=(0, 20))
        
        current_date_format = self.localization_manager.date_formats.get(
            self.localization_manager.current_language, 'N/A')
        ttk.Label(date_section, text=f"{get_text('DATE_FORMAT', 'Date Format')}: {current_date_format}").pack(anchor='w')
        
        # Cultural preferences
        cultural_section = ttk.LabelFrame(regional_frame, text=get_text('CULTURAL_PREFERENCES', 'Cultural Preferences'), padding="10")
        cultural_section.pack(fill='both', expand=True)
        
        prefs = self.localization_manager.cultural_preferences.get(
            self.localization_manager.current_language, {})
        
        ttk.Label(cultural_section, text=f"{get_text('FIRST_DAY_WEEK', 'First Day of Week')}: {self.get_weekday_name(prefs.get('first_day_of_week', 0))}").pack(anchor='w')
        ttk.Label(cultural_section, text=f"{get_text('TIME_FORMAT', 'Time Format')}: {prefs.get('time_format', 'N/A')}").pack(anchor='w')
        ttk.Label(cultural_section, text=f"{get_text('MEASUREMENT_SYSTEM', 'Measurement System')}: {prefs.get('measurement_system', 'N/A')}").pack(anchor='w')
    
    def create_preview_tab(self, notebook):
        """Create format preview tab"""
        preview_frame = ttk.Frame(notebook, padding="20")
        notebook.add(preview_frame, text=get_text('TAB_PREVIEW', 'Preview'))
        
        # Sample data section
        sample_section = ttk.LabelFrame(preview_frame, text=get_text('FORMAT_PREVIEW', 'Format Preview'), padding="10")
        sample_section.pack(fill='both', expand=True)
        
        # Create preview text widget
        self.preview_text = tk.Text(sample_section, height=15, wrap='word')
        preview_scrollbar = ttk.Scrollbar(sample_section, orient='vertical', command=self.preview_text.yview)
        self.preview_text.configure(yscrollcommand=preview_scrollbar.set)
        
        self.preview_text.pack(side='left', fill='both', expand=True)
        preview_scrollbar.pack(side='right', fill='y')
        
        self.update_format_preview()
        
        # Refresh button
        ttk.Button(preview_frame, text=get_text('BUTTON_REFRESH', 'Refresh Preview'),
                  command=self.update_format_preview).pack(pady=(10, 0))
    
    def on_language_change(self):
        """Handle language change"""
        new_language = self.language_var.get()
        self.localization_manager.set_language(new_language)
        self.update_language_info()
        self.update_format_preview()
    
    def update_language_info(self):
        """Update language information display"""
        current_lang = self.localization_manager.current_language
        languages = self.localization_manager.get_available_languages()
        
        info_text = f"Selected Language: {languages.get(current_lang, 'Unknown')}\n"
        info_text += f"Language Code: {current_lang}\n\n"
        
        # Currency info
        currency_info = self.localization_manager.currency_formats.get(current_lang, {})
        info_text += "Currency Format:\n"
        info_text += f"  Symbol: {currency_info.get('symbol', 'N/A')}\n"
        info_text += f"  Position: {currency_info.get('position', 'N/A')}\n"
        info_text += f"  Decimal Places: {currency_info.get('decimal_places', 'N/A')}\n"
        info_text += f"  Format: {currency_info.get('format', 'N/A')}\n\n"
        
        # Date info
        date_format = self.localization_manager.date_formats.get(current_lang, 'N/A')
        info_text += f"Date Format: {date_format}\n\n"
        
        # Cultural preferences
        prefs = self.localization_manager.cultural_preferences.get(current_lang, {})
        info_text += "Cultural Preferences:\n"
        info_text += f"  First Day of Week: {self.get_weekday_name(prefs.get('first_day_of_week', 0))}\n"
        info_text += f"  Time Format: {prefs.get('time_format', 'N/A')}\n"
        info_text += f"  Measurement System: {prefs.get('measurement_system', 'N/A')}\n"
        
        self.language_info_text.delete(1.0, tk.END)
        self.language_info_text.insert(1.0, info_text)
    
    def update_format_preview(self):
        """Update format preview"""
        preview_text = "FORMAT PREVIEW\n" + "="*50 + "\n\n"
        
        # Currency examples
        preview_text += "Currency Formatting:\n"
        sample_amounts = [1234.56, 25952.59, 0.99, 1000000.00]
        for amount in sample_amounts:
            formatted = format_currency(amount)
            preview_text += f"  {amount:>10.2f} → {formatted}\n"
        
        preview_text += "\n"
        
        # Date examples
        preview_text += "Date Formatting:\n"
        sample_dates = [
            datetime.now(),
            datetime(2024, 1, 1),
            datetime(2024, 12, 31),
            datetime(2024, 6, 15)
        ]
        for date in sample_dates:
            formatted = format_date(date)
            preview_text += f"  {date.strftime('%Y-%m-%d')} → {formatted}\n"
        
        preview_text += "\n"
        
        # Sample text translations
        preview_text += "Sample Text Translations:\n"
        sample_keys = [
            'APP_TITLE', 'LOGIN_TITLE', 'DASHBOARD_TITLE',
            'INVENTORY_TITLE', 'SALES_TITLE', 'REPORTS_TITLE'
        ]
        for key in sample_keys:
            translated = get_text(key, key)
            preview_text += f"  {key}: {translated}\n"
        
        self.preview_text.delete(1.0, tk.END)
        self.preview_text.insert(1.0, preview_text)
    
    def get_weekday_name(self, day_num):
        """Get weekday name from number"""
        days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
        return days[day_num] if 0 <= day_num < 7 else 'Unknown'
    
    def load_settings(self):
        """Load language settings from file"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            log_error(f"Error loading language settings: {e}")
        
        return {
            'language': 'ru',
            'auto_detect': False,
            'date_format_override': None,
            'currency_format_override': None
        }
    
    def save_settings(self):
        """Save current settings"""
        try:
            settings = {
                'language': self.localization_manager.current_language,
                'auto_detect': False,
                'date_format_override': None,
                'currency_format_override': None,
                'last_updated': datetime.now().isoformat()
            }
            
            # Ensure config directory exists
            os.makedirs(os.path.dirname(self.settings_file), exist_ok=True)
            
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
            
            messagebox.showinfo(get_text('SUCCESS', 'Success'), 
                              get_text('SETTINGS_SAVED', 'Settings saved successfully'))
            log_info(f"Language settings saved: {settings['language']}")
            
        except Exception as e:
            error_msg = get_text('ERROR_SAVING_SETTINGS', f'Error saving settings: {e}')
            messagebox.showerror(get_text('ERROR', 'Error'), error_msg)
            log_error(f"Error saving language settings: {e}")
    
    def apply_settings(self):
        """Apply current settings without saving"""
        try:
            # Settings are already applied through the localization manager
            messagebox.showinfo(get_text('SUCCESS', 'Success'), 
                              get_text('SETTINGS_APPLIED', 'Settings applied successfully'))
            log_info(f"Language settings applied: {self.localization_manager.current_language}")
            
        except Exception as e:
            error_msg = get_text('ERROR_APPLYING_SETTINGS', f'Error applying settings: {e}')
            messagebox.showerror(get_text('ERROR', 'Error'), error_msg)
            log_error(f"Error applying language settings: {e}")


def create_language_settings_manager(parent, db_manager):
    """Factory function to create language settings manager"""
    try:
        manager = LanguageSettingsManager(parent, db_manager)
        manager.show_language_settings()
        return manager
    except Exception as e:
        handle_module_error(e, "Language Settings", "creating language settings manager")
        return None
