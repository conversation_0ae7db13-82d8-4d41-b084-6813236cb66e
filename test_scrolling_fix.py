#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Тест исправлений прокрутки в главном окне
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gui.styles import ModernStyles
from database.db_manager import DatabaseManager

def test_main_window_scrolling():
    """Тест прокрутки главного окна"""
    
    print("🧪 ТЕСТ ПРОКРУТКИ ГЛАВНОГО ОКНА")
    print("=" * 50)
    
    try:
        # Создать корневое окно
        root = tk.Tk()
        root.title("Тест Прокрутки - Система Управления Рестораном")
        root.geometry("1400x900")
        root.configure(bg=ModernStyles.COLORS['bg_main'])
        
        # Создать менеджер базы данных
        db_manager = DatabaseManager("test_restaurant.db")
        
        # Создать главное окно (упрощенная версия)
        class TestMainWindow:
            def __init__(self, root, db_manager):
                self.root = root
                self.db_manager = db_manager
                self.selected_db = {'name': 'Тестовый Ресторан'}
                self.current_frame = None
                
                self.create_interface()
                
            def create_interface(self):
                """Создать интерфейс"""
                # Основной контейнер
                main_frame = tk.Frame(self.root, bg=ModernStyles.COLORS['bg_main'])
                main_frame.pack(fill='both', expand=True)
                
                # Создать боковую панель
                self.create_sidebar(main_frame)
                
                # Создать область контента
                self.create_content_area(main_frame)
                
                # Создать статус бар
                self.create_status_bar(main_frame)
                
                print("✅ Интерфейс создан")
                
            def create_sidebar(self, parent):
                """Создать боковую панель с прокруткой"""
                # Основной контейнер боковой панели
                sidebar_container = tk.Frame(parent, bg=ModernStyles.COLORS['bg_sidebar'], width=300)
                sidebar_container.pack(side='left', fill='y')
                sidebar_container.pack_propagate(False)
                
                # Заголовок
                title_frame = tk.Frame(sidebar_container, bg=ModernStyles.COLORS['bg_sidebar'])
                title_frame.pack(fill='x', pady=15)
                
                self.title_label = tk.Label(title_frame, text="🏪 Управление\nТестовый Ресторан",
                                          font=('Cambria', 16, 'bold italic'),
                                          fg='white',
                                          bg=ModernStyles.COLORS['bg_sidebar'],
                                          justify='center')
                self.title_label.pack()
                
                # Область навигации с прокруткой
                nav_frame = tk.Frame(sidebar_container, bg=ModernStyles.COLORS['bg_sidebar'])
                nav_frame.pack(fill='both', expand=True, padx=5, pady=5)
                
                # Создать canvas и scrollbar
                canvas = tk.Canvas(nav_frame, bg=ModernStyles.COLORS['bg_sidebar'], 
                                 highlightthickness=0, width=280)
                scrollbar = ttk.Scrollbar(nav_frame, orient="vertical", command=canvas.yview)
                scrollable_frame = tk.Frame(canvas, bg=ModernStyles.COLORS['bg_sidebar'])
                
                # Настроить прокрутку
                def configure_scroll_region(event=None):
                    canvas.configure(scrollregion=canvas.bbox("all"))
                    print(f"📏 Scroll region updated: {canvas.bbox('all')}")
                
                scrollable_frame.bind("<Configure>", configure_scroll_region)
                canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
                canvas.configure(yscrollcommand=scrollbar.set)
                
                # Прокрутка колесом мыши
                def _on_mousewheel(event):
                    canvas.yview_scroll(int(-1*(event.delta/120)), "units")
                    print(f"🖱️ Mouse wheel: {event.delta}")
                
                canvas.bind("<MouseWheel>", _on_mousewheel)
                scrollable_frame.bind("<MouseWheel>", _on_mousewheel)
                
                # Создать тестовые кнопки
                self.create_test_buttons(scrollable_frame)
                
                # Упаковать canvas и scrollbar
                canvas.pack(side="left", fill="both", expand=True)
                scrollbar.pack(side="right", fill="y")
                
                # Принудительно обновить область прокрутки
                canvas.update_idletasks()
                canvas.configure(scrollregion=canvas.bbox("all"))
                
                # Сохранить ссылки
                self.nav_canvas = canvas
                self.nav_scrollbar = scrollbar
                self.nav_scrollable_frame = scrollable_frame
                
                print(f"📊 Canvas size: {canvas.winfo_reqwidth()}x{canvas.winfo_reqheight()}")
                print(f"📊 Scrollable frame size: {scrollable_frame.winfo_reqwidth()}x{scrollable_frame.winfo_reqheight()}")
                
            def create_test_buttons(self, parent):
                """Создать тестовые кнопки"""
                categories = [
                    ("📊 ОСНОВНЫЕ МОДУЛИ", 14),
                    ("📦 СКЛАД И ЗАКУПКИ", 3),
                    ("🍽️ КУХНЯ И МЕНЮ", 4),
                    ("👥 КЛИЕНТЫ И ПЕРСОНАЛ", 4),
                    ("💰 ФИНАНСЫ И УЧЁТ", 2),
                    ("📈 АНАЛИТИКА И ОТЧЁТЫ", 4),
                    ("🔧 ИНСТРУМЕНТЫ", 16),
                ]
                
                button_count = 0
                for category_name, button_count_in_category in categories:
                    # Заголовок категории
                    category_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_sidebar'])
                    category_frame.pack(fill='x', pady=(10, 5))
                    
                    title_label = tk.Label(category_frame, text=category_name,
                                         font=('Cambria', 12, 'bold italic'),
                                         fg='#94a3b8',
                                         bg=ModernStyles.COLORS['bg_sidebar'])
                    title_label.pack(anchor='w', padx=15)
                    
                    # Кнопки категории
                    for i in range(button_count_in_category):
                        button_count += 1
                        btn_text = f"🔘 Тестовая Кнопка {button_count}"
                        
                        btn = tk.Button(parent, text=btn_text,
                                       command=lambda t=btn_text: self.test_button_click(t),
                                       bg=ModernStyles.COLORS['primary'],
                                       fg='white',
                                       font=('Cambria', 11, 'bold italic'),
                                       relief='flat',
                                       bd=1,
                                       padx=15,
                                       pady=10,
                                       anchor='w',
                                       cursor='hand2')
                        btn.pack(fill='x', padx=8, pady=3)
                        
                        # Эффекты наведения
                        def on_enter(e, button=btn):
                            button.config(bg=ModernStyles.COLORS['primary_light'], relief='raised', bd=2)
                        def on_leave(e, button=btn):
                            button.config(bg=ModernStyles.COLORS['primary'], relief='flat', bd=1)
                        
                        btn.bind("<Enter>", on_enter)
                        btn.bind("<Leave>", on_leave)
                
                print(f"✅ Создано {button_count} тестовых кнопок")
                
            def test_button_click(self, button_text):
                """Обработчик нажатия тестовой кнопки"""
                print(f"🔘 Нажата кнопка: {button_text}")
                self.status_label.config(text=f"Нажата: {button_text}")
                
            def create_content_area(self, parent):
                """Создать область контента"""
                self.content_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
                self.content_frame.pack(side='left', fill='both', expand=True, padx=20, pady=20)
                
                # Заголовок
                title = tk.Label(self.content_frame, text="🧪 ТЕСТ ПРОКРУТКИ БОКОВОЙ ПАНЕЛИ",
                               font=('Cambria', 24, 'bold italic'),
                               fg=ModernStyles.COLORS['text_primary'],
                               bg=ModernStyles.COLORS['bg_main'])
                title.pack(pady=20)
                
                # Инструкции
                instructions = tk.Label(self.content_frame,
                                      text="Инструкции для тестирования:\n\n" +
                                           "1. Используйте колесо мыши для прокрутки боковой панели\n" +
                                           "2. Проверьте, что все кнопки видны при прокрутке\n" +
                                           "3. Нажимайте на кнопки для проверки функциональности\n" +
                                           "4. Убедитесь, что прокрутка работает плавно\n" +
                                           "5. Проверьте, что полоса прокрутки видна и работает",
                                      font=('Cambria', 14, 'bold italic'),
                                      fg=ModernStyles.COLORS['text_secondary'],
                                      bg=ModernStyles.COLORS['bg_main'],
                                      justify='left')
                instructions.pack(pady=20, padx=20)
                
            def create_status_bar(self, parent):
                """Создать статус бар"""
                status_frame = tk.Frame(parent, bg=ModernStyles.COLORS['primary'], height=30)
                status_frame.pack(side='bottom', fill='x')
                status_frame.pack_propagate(False)
                
                self.status_label = tk.Label(status_frame, text="Готов к тестированию прокрутки...",
                                           font=('Cambria', 12, 'bold italic'),
                                           fg='white',
                                           bg=ModernStyles.COLORS['primary'])
                self.status_label.pack(pady=5)
        
        # Создать тестовое главное окно
        test_window = TestMainWindow(root, db_manager)
        
        print("🚀 Тест запущен! Проверьте прокрутку в боковой панели.")
        
        # Запустить главный цикл
        root.mainloop()
        
    except Exception as e:
        print(f"❌ Ошибка в тесте: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_main_window_scrolling()
