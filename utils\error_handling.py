"""
Comprehensive Error Handling Utilities for Restaurant Management System
Provides consistent error handling, logging, and user-friendly error messages in Russian
"""

import tkinter as tk
from tkinter import messagebox
import traceback
import logging
import os
from datetime import datetime


class RestaurantErrorHandler:
    """Centralized error handling for the restaurant management system"""
    
    def __init__(self):
        self.setup_logging()
    
    def setup_logging(self):
        """Setup logging configuration"""
        try:
            # Create logs directory if it doesn't exist
            if not os.path.exists('logs'):
                os.makedirs('logs')
            
            # Configure logging
            log_filename = f"logs/restaurant_system_{datetime.now().strftime('%Y%m%d')}.log"
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(levelname)s - %(module)s - %(message)s',
                handlers=[
                    logging.FileHandler(log_filename, encoding='utf-8'),
                    logging.StreamHandler()
                ]
            )
            
            self.logger = logging.getLogger(__name__)
            
        except Exception as e:
            print(f"Ошибка настройки логирования: {e}")
            self.logger = None
    
    def log_error(self, error, module_name="Unknown", operation="Unknown"):
        """Log error with context information"""
        try:
            if self.logger:
                error_msg = f"Модуль: {module_name}, Операция: {operation}, Ошибка: {str(error)}"
                self.logger.error(error_msg)
                self.logger.error(f"Трассировка: {traceback.format_exc()}")
        except:
            pass
    
    def show_error(self, error, title="Ошибка", module_name="Unknown", operation="Unknown", 
                   user_message=None, show_details=False):
        """Show user-friendly error message"""
        try:
            # Log the error
            self.log_error(error, module_name, operation)
            
            # Prepare user message
            if user_message:
                display_message = user_message
            else:
                display_message = self.get_user_friendly_message(error, operation)
            
            # Show details if requested
            if show_details:
                display_message += f"\n\nТехнические детали:\n{str(error)}"
            
            messagebox.showerror(title, display_message)
            
        except Exception as e:
            # Fallback error handling
            messagebox.showerror("Критическая Ошибка", 
                               f"Произошла критическая ошибка в системе обработки ошибок:\n{str(e)}")
    
    def get_user_friendly_message(self, error, operation):
        """Convert technical error to user-friendly Russian message"""
        error_str = str(error).lower()
        
        # Database errors
        if 'database' in error_str or 'sqlite' in error_str:
            return f"Ошибка базы данных при выполнении операции '{operation}'.\nПроверьте подключение к базе данных."
        
        # Import errors
        if 'import' in error_str or 'module' in error_str:
            return f"Ошибка загрузки модуля при выполнении операции '{operation}'.\nВозможно, отсутствует необходимый компонент системы."
        
        # Permission errors
        if 'permission' in error_str or 'access' in error_str:
            return f"Ошибка доступа при выполнении операции '{operation}'.\nПроверьте права доступа к файлам и папкам."
        
        # Network errors
        if 'network' in error_str or 'connection' in error_str:
            return f"Ошибка сети при выполнении операции '{operation}'.\nПроверьте подключение к интернету."
        
        # File errors
        if 'file' in error_str or 'directory' in error_str:
            return f"Ошибка работы с файлами при выполнении операции '{operation}'.\nПроверьте существование и доступность файлов."
        
        # Memory errors
        if 'memory' in error_str:
            return f"Недостаточно памяти для выполнения операции '{operation}'.\nЗакройте ненужные программы и повторите попытку."
        
        # Generic error
        return f"Произошла ошибка при выполнении операции '{operation}'.\nОбратитесь к администратору системы."
    
    def handle_module_error(self, error, module_name, operation="открытие модуля"):
        """Handle module-specific errors"""
        self.show_error(
            error, 
            title=f"Ошибка модуля {module_name}",
            module_name=module_name,
            operation=operation,
            user_message=f"Не удалось выполнить операцию '{operation}' в модуле '{module_name}'.\n\nВозможные причины:\n• Модуль не полностью загружен\n• Отсутствуют необходимые данные\n• Ошибка конфигурации\n\nПопробуйте перезапустить приложение."
        )
    
    def handle_database_error(self, error, operation="работа с базой данных"):
        """Handle database-specific errors"""
        self.show_error(
            error,
            title="Ошибка базы данных",
            module_name="Database",
            operation=operation,
            user_message=f"Ошибка при выполнении операции с базой данных: '{operation}'.\n\nВозможные причины:\n• База данных заблокирована другим процессом\n• Недостаточно места на диске\n• Повреждение файла базы данных\n\nПопробуйте перезапустить приложение."
        )
    
    def handle_import_error(self, error, module_name):
        """Handle import errors"""
        self.show_error(
            error,
            title="Ошибка загрузки модуля",
            module_name=module_name,
            operation="импорт модуля",
            user_message=f"Не удалось загрузить модуль '{module_name}'.\n\nВозможные причины:\n• Отсутствует файл модуля\n• Ошибка в коде модуля\n• Отсутствуют зависимости\n\nОбратитесь к администратору системы."
        )
    
    def safe_execute(self, func, *args, **kwargs):
        """Safely execute function with error handling"""
        try:
            return func(*args, **kwargs)
        except Exception as e:
            self.handle_module_error(e, func.__name__ if hasattr(func, '__name__') else "Unknown")
            return None
    
    def create_error_dialog(self, parent, error, title="Подробности ошибки"):
        """Create detailed error dialog"""
        try:
            dialog = tk.Toplevel(parent)
            dialog.title(title)
            dialog.geometry("600x400")
            dialog.configure(bg='#f8f9fa')
            dialog.resizable(True, True)
            
            # Center dialog
            dialog.update_idletasks()
            x = (dialog.winfo_screenwidth() // 2) - (600 // 2)
            y = (dialog.winfo_screenheight() // 2) - (400 // 2)
            dialog.geometry(f"600x400+{x}+{y}")
            
            # Error details
            text_frame = tk.Frame(dialog, bg='#f8f9fa')
            text_frame.pack(fill='both', expand=True, padx=20, pady=20)
            
            tk.Label(text_frame, text="Подробная информация об ошибке:",
                    font=('Arial', 12, 'bold'),
                    bg='#f8f9fa').pack(anchor='w', pady=(0, 10))
            
            # Text widget with scrollbar
            text_widget = tk.Text(text_frame, wrap='word', height=15)
            scrollbar = tk.Scrollbar(text_frame, orient='vertical', command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)
            
            # Insert error details
            error_details = f"Ошибка: {str(error)}\n\nТрассировка:\n{traceback.format_exc()}"
            text_widget.insert('1.0', error_details)
            text_widget.configure(state='disabled')
            
            text_widget.pack(side='left', fill='both', expand=True)
            scrollbar.pack(side='right', fill='y')
            
            # Close button
            button_frame = tk.Frame(dialog, bg='#f8f9fa')
            button_frame.pack(fill='x', padx=20, pady=(0, 20))
            
            tk.Button(button_frame, text="Закрыть", command=dialog.destroy,
                     bg='#dc3545', fg='white', font=('Arial', 10, 'bold'),
                     padx=20, pady=5).pack(side='right')
            
            return dialog
            
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось создать диалог ошибки: {e}")
            return None


# Global error handler instance
error_handler = RestaurantErrorHandler()


def handle_error(error, module_name="Unknown", operation="Unknown", show_details=False):
    """Global error handling function"""
    error_handler.show_error(error, module_name=module_name, operation=operation, show_details=show_details)


def handle_module_error(error, module_name, operation="открытие модуля"):
    """Handle module-specific errors"""
    error_handler.handle_module_error(error, module_name, operation)


def handle_database_error(error, operation="работа с базой данных"):
    """Handle database errors"""
    error_handler.handle_database_error(error, operation)


def handle_import_error(error, module_name):
    """Handle import errors"""
    error_handler.handle_import_error(error, module_name)


def safe_execute(func, *args, **kwargs):
    """Safely execute function with error handling"""
    return error_handler.safe_execute(func, *args, **kwargs)


def log_info(message, module_name="System"):
    """Log informational message"""
    try:
        if error_handler.logger:
            error_handler.logger.info(f"{module_name}: {message}")
    except:
        pass


def log_warning(message, module_name="System"):
    """Log warning message"""
    try:
        if error_handler.logger:
            error_handler.logger.warning(f"{module_name}: {message}")
    except:
        pass
