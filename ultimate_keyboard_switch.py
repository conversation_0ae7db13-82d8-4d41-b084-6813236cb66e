#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import ctypes
import ctypes.wintypes
import time
import subprocess
import os
import sys

def ultimate_keyboard_switch():
    """УЛЬТИМАТИВНОЕ переключение раскладки - самые агрессивные методы"""
    print("🚀 УЛЬТИМАТИВНОЕ ПЕРЕКЛЮЧЕНИЕ РАСКЛАДКИ КЛАВИАТУРЫ")
    print("=" * 80)
    
    # Загружаем все необходимые DLL
    user32 = ctypes.windll.user32
    kernel32 = ctypes.windll.kernel32
    shell32 = ctypes.windll.shell32
    
    success_methods = []
    
    # МЕТОД 1: Принудительная установка через SystemParametersInfo
    try:
        print("🔧 Метод 1: SystemParametersInfo...")
        
        # SPI_SETDEFAULTINPUTLANG = 0x005A
        result = user32.SystemParametersInfoW(
            0x005A,  # SPI_SETDEFAULTINPUTLANG
            0,
            ctypes.c_wchar_p("00000419"),  # Русская раскладка
            0x0002   # SPIF_SENDCHANGE
        )
        
        if result:
            print("   ✅ SystemParametersInfo успешно")
            success_methods.append("SystemParametersInfo")
        else:
            print("   ❌ SystemParametersInfo не удался")
            
    except Exception as e:
        print(f"   ❌ SystemParametersInfo ошибка: {e}")
    
    # МЕТОД 2: Прямое изменение через SetKeyboardState
    try:
        print("🔧 Метод 2: SetKeyboardState...")
        
        # Получаем текущее состояние клавиатуры
        keyboard_state = (ctypes.c_ubyte * 256)()
        user32.GetKeyboardState(keyboard_state)
        
        # Устанавливаем русскую раскладку
        hkl_russian = user32.LoadKeyboardLayoutW("00000419", 0x00000001)
        if hkl_russian:
            result = user32.ActivateKeyboardLayout(hkl_russian, 0x00000008)
            if result:
                # Принудительно устанавливаем состояние
                user32.SetKeyboardState(keyboard_state)
                print("   ✅ SetKeyboardState успешно")
                success_methods.append("SetKeyboardState")
            else:
                print("   ❌ ActivateKeyboardLayout не удался")
        else:
            print("   ❌ LoadKeyboardLayoutW не удался")
            
    except Exception as e:
        print(f"   ❌ SetKeyboardState ошибка: {e}")
    
    # МЕТОД 3: Использование PostMessage для всех окон
    try:
        print("🔧 Метод 3: PostMessage для всех окон...")
        
        def enum_windows_proc(hwnd, lParam):
            try:
                # WM_INPUTLANGCHANGEREQUEST = 0x0050
                user32.PostMessageW(hwnd, 0x0050, 0, 0x4190419)
                return True
            except:
                return True
        
        # Определяем тип функции callback
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.wintypes.HWND, ctypes.wintypes.LPARAM)
        enum_proc = EnumWindowsProc(enum_windows_proc)
        
        # Перебираем все окна
        user32.EnumWindows(enum_proc, 0)
        
        print("   ✅ PostMessage для всех окон выполнен")
        success_methods.append("PostMessage для всех окон")
        
    except Exception as e:
        print(f"   ❌ PostMessage для всех окон ошибка: {e}")
    
    # МЕТОД 4: Использование WM_SETTINGCHANGE
    try:
        print("🔧 Метод 4: WM_SETTINGCHANGE...")
        
        # WM_SETTINGCHANGE = 0x001A
        # HWND_BROADCAST = 0xFFFF
        result = user32.SendMessageW(
            0xFFFF,  # HWND_BROADCAST
            0x001A,  # WM_SETTINGCHANGE
            0,
            ctypes.c_wchar_p("intl")
        )
        
        print(f"   ✅ WM_SETTINGCHANGE отправлен (результат: {result})")
        success_methods.append("WM_SETTINGCHANGE")
        
    except Exception as e:
        print(f"   ❌ WM_SETTINGCHANGE ошибка: {e}")
    
    # МЕТОД 5: Использование rundll32 с разными параметрами
    try:
        print("🔧 Метод 5: rundll32 команды...")
        
        commands = [
            "rundll32.exe shell32.dll,Control_RunDLL input.dll,,{C07337D3-DB2C-4D0B-9A93-B722A6C106E2}",
            "rundll32.exe user32.dll,ActivateKeyboardLayout 67699721 8",
            "rundll32.exe input.dll,SetDefaultKeyboardLayout 00000419"
        ]
        
        for cmd in commands:
            try:
                result = subprocess.run(cmd.split(), capture_output=True, timeout=5)
                print(f"   ✅ Команда выполнена: {cmd[:50]}...")
            except:
                print(f"   ❌ Команда не удалась: {cmd[:50]}...")
        
        success_methods.append("rundll32 команды")
        
    except Exception as e:
        print(f"   ❌ rundll32 команды ошибка: {e}")
    
    # МЕТОД 6: Использование reg add для изменения реестра
    try:
        print("🔧 Метод 6: Реестр через reg add...")
        
        reg_commands = [
            'reg add "HKCU\\Keyboard Layout\\Preload" /v "1" /t REG_SZ /d "00000419" /f',
            'reg add "HKCU\\Keyboard Layout\\Preload" /v "2" /t REG_SZ /d "00000409" /f',
            'reg add "HKCU\\Control Panel\\International" /v "Locale" /t REG_SZ /d "00000419" /f'
        ]
        
        for cmd in reg_commands:
            try:
                result = subprocess.run(cmd, shell=True, capture_output=True, timeout=5)
                if result.returncode == 0:
                    print(f"   ✅ Реестр обновлен: {cmd[:50]}...")
                else:
                    print(f"   ❌ Реестр не обновлен: {cmd[:50]}...")
            except:
                print(f"   ❌ Реестр ошибка: {cmd[:50]}...")
        
        success_methods.append("Реестр через reg add")
        
    except Exception as e:
        print(f"   ❌ Реестр через reg add ошибка: {e}")
    
    # МЕТОД 7: Использование SetThreadLocale
    try:
        print("🔧 Метод 7: SetThreadLocale...")
        
        # LCID для русского языка
        russian_lcid = 0x0419
        
        result = kernel32.SetThreadLocale(russian_lcid)
        if result:
            print("   ✅ SetThreadLocale успешно")
            success_methods.append("SetThreadLocale")
        else:
            print("   ❌ SetThreadLocale не удался")
            
    except Exception as e:
        print(f"   ❌ SetThreadLocale ошибка: {e}")
    
    # МЕТОД 8: Множественные keybd_event с задержками
    try:
        print("🔧 Метод 8: Множественные keybd_event...")
        
        # Отправляем Alt+Shift 10 раз с разными задержками
        for i in range(10):
            user32.keybd_event(0x12, 0, 0, 0)  # Alt down
            user32.keybd_event(0x10, 0, 0, 0)  # Shift down
            kernel32.Sleep(50 + i * 10)
            user32.keybd_event(0x10, 0, 2, 0)  # Shift up
            user32.keybd_event(0x12, 0, 2, 0)  # Alt up
            kernel32.Sleep(100 + i * 20)
        
        print("   ✅ Множественные keybd_event отправлены")
        success_methods.append("Множественные keybd_event")
        
    except Exception as e:
        print(f"   ❌ Множественные keybd_event ошибка: {e}")
    
    # Проверяем результат
    time.sleep(2)
    
    try:
        hwnd = user32.GetForegroundWindow()
        thread_id = user32.GetWindowThreadProcessId(hwnd, None)
        current_hkl = user32.GetKeyboardLayout(thread_id)
        current_id = current_hkl & 0xFFFF
        
        print("\n" + "=" * 80)
        print(f"📋 РЕЗУЛЬТАТ: Текущая раскладка {hex(current_hkl)} (ID: {hex(current_id)})")
        
        if current_id == 0x0419:
            print("🎉 УСПЕХ! Раскладка переключена на русскую!")
            print(f"✅ Успешные методы: {', '.join(success_methods)}")
            return True
        else:
            print("❌ Раскладка все еще не русская")
            print(f"🔧 Попробованные методы: {', '.join(success_methods)}")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка при проверке результата: {e}")
        return False

if __name__ == "__main__":
    success = ultimate_keyboard_switch()
    
    if not success:
        print("\n💡 ДОПОЛНИТЕЛЬНЫЕ РЕКОМЕНДАЦИИ:")
        print("1. Запустите скрипт от имени администратора")
        print("2. Перезагрузите компьютер после изменения реестра")
        print("3. Проверьте настройки языка в Windows")
        print("4. Используйте ручное переключение Alt+Shift")
    
    input("\nНажмите Enter для выхода...")
