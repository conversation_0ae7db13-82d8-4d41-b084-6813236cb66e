#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
АБСОЛЮТНОЕ РЕШЕНИЕ ПРОБЛЕМЫ ПЕРЕКЛЮЧЕНИЯ РАСКЛАДКИ
Самый радикальный подход - физическая эмуляция нажатий клавиш
"""

import ctypes
import time
import threading
import tkinter as tk
import tkinter.messagebox as msgbox

def safe_print(text):
    """Безопасный вывод с обработкой Unicode ошибок"""
    try:
        print(text)
    except UnicodeEncodeError:
        ascii_text = text.encode('ascii', 'ignore').decode('ascii')
        print(ascii_text)

def get_current_layout():
    """Получить текущую раскладку клавиатуры"""
    try:
        user32 = ctypes.windll.user32
        hwnd = user32.GetForegroundWindow()
        thread_id = user32.GetWindowThreadProcessId(hwnd, None)
        hkl = user32.GetKeyboardLayout(thread_id)
        layout_id = hkl & 0xFFFF
        return layout_id
    except:
        return None

def physical_alt_shift_press():
    """Физическая эмуляция нажатия Alt+Shift с максимальной точностью"""
    try:
        safe_print("🔄 ФИЗИЧЕСКАЯ эмуляция Alt+Shift...")
        user32 = ctypes.windll.user32
        
        # Получаем коды виртуальных клавиш
        VK_MENU = 0x12      # Alt
        VK_LSHIFT = 0xA0    # Left Shift
        VK_RSHIFT = 0xA1    # Right Shift
        
        # Метод 1: Alt + Left Shift
        safe_print("   Попытка 1: Alt + Left Shift")
        user32.keybd_event(VK_MENU, 0, 0, 0)        # Alt down
        time.sleep(0.05)
        user32.keybd_event(VK_LSHIFT, 0, 0, 0)      # Left Shift down
        time.sleep(0.1)
        user32.keybd_event(VK_LSHIFT, 0, 2, 0)      # Left Shift up
        time.sleep(0.05)
        user32.keybd_event(VK_MENU, 0, 2, 0)        # Alt up
        time.sleep(0.5)
        
        current = get_current_layout()
        if current == 0x0419:
            safe_print("   ✅ Метод 1 сработал!")
            return True
        
        # Метод 2: Alt + Right Shift
        safe_print("   Попытка 2: Alt + Right Shift")
        user32.keybd_event(VK_MENU, 0, 0, 0)        # Alt down
        time.sleep(0.05)
        user32.keybd_event(VK_RSHIFT, 0, 0, 0)      # Right Shift down
        time.sleep(0.1)
        user32.keybd_event(VK_RSHIFT, 0, 2, 0)      # Right Shift up
        time.sleep(0.05)
        user32.keybd_event(VK_MENU, 0, 2, 0)        # Alt up
        time.sleep(0.5)
        
        current = get_current_layout()
        if current == 0x0419:
            safe_print("   ✅ Метод 2 сработал!")
            return True
        
        # Метод 3: Ctrl + Shift
        safe_print("   Попытка 3: Ctrl + Shift")
        VK_CONTROL = 0x11
        user32.keybd_event(VK_CONTROL, 0, 0, 0)     # Ctrl down
        time.sleep(0.05)
        user32.keybd_event(VK_LSHIFT, 0, 0, 0)      # Left Shift down
        time.sleep(0.1)
        user32.keybd_event(VK_LSHIFT, 0, 2, 0)      # Left Shift up
        time.sleep(0.05)
        user32.keybd_event(VK_CONTROL, 0, 2, 0)     # Ctrl up
        time.sleep(0.5)
        
        current = get_current_layout()
        if current == 0x0419:
            safe_print("   ✅ Метод 3 сработал!")
            return True
        
        # Метод 4: Множественные нажатия Alt+Shift
        safe_print("   Попытка 4: Множественные Alt+Shift")
        for i in range(5):
            user32.keybd_event(VK_MENU, 0, 0, 0)        # Alt down
            time.sleep(0.02)
            user32.keybd_event(VK_LSHIFT, 0, 0, 0)      # Left Shift down
            time.sleep(0.05)
            user32.keybd_event(VK_LSHIFT, 0, 2, 0)      # Left Shift up
            time.sleep(0.02)
            user32.keybd_event(VK_MENU, 0, 2, 0)        # Alt up
            time.sleep(0.2)
            
            current = get_current_layout()
            if current == 0x0419:
                safe_print(f"   ✅ Метод 4 сработал на попытке {i+1}!")
                return True
        
        safe_print("   ❌ Все физические методы не сработали")
        return False
        
    except Exception as e:
        safe_print(f"❌ Ошибка физической эмуляции: {e}")
        return False

def show_persistent_notification():
    """Показать постоянное уведомление пользователю"""
    def show_dialog():
        try:
            root = tk.Tk()
            root.title("🔄 ПЕРЕКЛЮЧЕНИЕ РАСКЛАДКИ")
            root.geometry("500x300")
            root.configure(bg='#2c3e50')
            
            # Делаем окно поверх всех
            root.attributes('-topmost', True)
            root.attributes('-toolwindow', True)
            
            # Центрируем окно
            root.update_idletasks()
            x = (root.winfo_screenwidth() // 2) - (500 // 2)
            y = (root.winfo_screenheight() // 2) - (300 // 2)
            root.geometry(f"500x300+{x}+{y}")
            
            # Заголовок
            title_label = tk.Label(root, 
                                 text="🔄 ПЕРЕКЛЮЧЕНИЕ РАСКЛАДКИ", 
                                 font=("Arial", 16, "bold"),
                                 fg='white', bg='#2c3e50')
            title_label.pack(pady=20)
            
            # Основное сообщение
            message = """⚠️ АВТОМАТИЧЕСКОЕ ПЕРЕКЛЮЧЕНИЕ НЕ СРАБОТАЛО

🎯 НАЖМИТЕ ОДНУ ИЗ КОМБИНАЦИЙ:

🔹 Alt + Shift (основной способ)
🔹 Ctrl + Shift (альтернативный)
🔹 Win + Пробел (Windows 10/11)

💡 После нажатия раскладка переключится на русскую"""
            
            msg_label = tk.Label(root, 
                               text=message,
                               font=("Arial", 12),
                               fg='white', bg='#2c3e50',
                               justify='left')
            msg_label.pack(pady=20, padx=20)
            
            # Кнопка закрытия
            def close_dialog():
                root.destroy()
            
            close_btn = tk.Button(root, 
                                text="✅ ПОНЯТНО", 
                                font=("Arial", 12, "bold"),
                                bg='#27ae60', fg='white',
                                command=close_dialog,
                                width=15, height=2)
            close_btn.pack(pady=20)
            
            # Автоматическое закрытие через 10 секунд
            root.after(10000, close_dialog)
            
            root.mainloop()
            
        except Exception as e:
            safe_print(f"Ошибка показа уведомления: {e}")
    
    # Запускаем в отдельном потоке
    thread = threading.Thread(target=show_dialog, daemon=True)
    thread.start()

def absolute_keyboard_switch():
    """АБСОЛЮТНОЕ переключение раскладки клавиатуры"""
    safe_print("🚀 АБСОЛЮТНОЕ РЕШЕНИЕ ПЕРЕКЛЮЧЕНИЯ РАСКЛАДКИ")
    safe_print("=" * 60)
    
    initial_layout = get_current_layout()
    safe_print(f"Начальная раскладка: {hex(initial_layout) if initial_layout else 'ОШИБКА'}")
    
    if initial_layout == 0x0419:
        safe_print("✅ Раскладка уже русская!")
        return True
    
    # Пробуем физическую эмуляцию
    if physical_alt_shift_press():
        safe_print("🎉 ФИЗИЧЕСКАЯ ЭМУЛЯЦИЯ СРАБОТАЛА!")
        return True
    
    # Если не сработало, показываем постоянное уведомление
    safe_print("⚠️ Физическая эмуляция не сработала")
    safe_print("📢 Показываем пользователю постоянное уведомление...")
    
    show_persistent_notification()
    
    # Ждем 2 секунды и проверяем еще раз
    time.sleep(2)
    final_layout = get_current_layout()
    
    safe_print("\n" + "=" * 60)
    safe_print(f"Начальная раскладка: {hex(initial_layout) if initial_layout else 'ОШИБКА'}")
    safe_print(f"Финальная раскладка: {hex(final_layout) if final_layout else 'ОШИБКА'}")
    
    if final_layout == 0x0419:
        safe_print("🎉 РАСКЛАДКА ПЕРЕКЛЮЧЕНА НА РУССКУЮ!")
        return True
    else:
        safe_print("⚠️ ПОЛЬЗОВАТЕЛЮ НУЖНО НАЖАТЬ Alt+Shift ВРУЧНУЮ")
        return False

def main():
    """Главная функция"""
    absolute_keyboard_switch()

if __name__ == "__main__":
    main()
