"""
Advanced Database Manager for Restaurant Accounting and Inventory Management System
Includes connection pooling, query optimization, data archiving, and performance monitoring
"""

import sqlite3
import os
import time
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any, Tuple
from database.models import DatabaseModels
from database.connection_pool import get_connection_pool
from database.query_optimizer import QueryOptimizer
from database.data_archiver import DataArchiver
from database.sync_manager import DataSyncManager
from database.offline_manager import OfflineDataManager

class DatabaseManager:
    """Advanced database manager with enterprise features"""

    def __init__(self, db_path: str = "restaurant_system.db", location_id: str = "main"):
        self.db_path = db_path
        self.location_id = location_id
        self.logger = logging.getLogger(__name__)

        # Initialize advanced database components
        self.connection_pool = get_connection_pool(db_path)
        self.query_optimizer = QueryOptimizer(self.connection_pool)
        self.data_archiver = DataArchiver(self.connection_pool)

        # Initialize sync and offline managers
        self.sync_manager = DataSyncManager(self.connection_pool, location_id)
        self.offline_manager = OfflineDataManager(self.connection_pool, location_id)

        # Legacy performance optimizer support
        self.performance_optimizer = None

        # Initialize database
        self.init_database()

        # Initialize performance optimizer after database is ready
        try:
            from utils.performance_optimizer import get_performance_optimizer
            self.performance_optimizer = get_performance_optimizer(self)
        except ImportError:
            pass  # Performance optimizer not available
    
    def init_database(self):
        """Initialize database and create tables with advanced features"""
        try:
            # Use connection pool for initialization
            with self.connection_pool.get_connection() as conn:
                DatabaseModels.create_tables(conn)
                self._create_default_admin()

            self.logger.info("Database initialized successfully with advanced features")

        except Exception as e:
            self.logger.error(f"Error initializing database: {e}")
            print(f"Error initializing database: {e}")

    def _create_default_admin(self):
        """Create default admin user if none exists"""
        try:
            result = self.connection_pool.execute_query(
                "SELECT COUNT(*) as count FROM users WHERE role = 'admin'",
                fetch_all=False
            )

            admin_count = result['count'] if result else 0

            if admin_count == 0:
                self.connection_pool.execute_query('''
                    INSERT INTO users (username, password_hash, role, full_name, email)
                    VALUES (?, ?, ?, ?, ?)
                ''', ('admin', 'admin123', 'admin', 'System Administrator', '<EMAIL>'))

                self.logger.info("Default admin user created (username: admin, password: admin123)")
                print("Default admin user created (username: admin, password: admin123)")

        except Exception as e:
            self.logger.error(f"Error creating default admin: {e}")
            print(f"Error creating default admin: {e}")

    def get_connection(self) -> sqlite3.Connection:
        """Get database connection with row factory (legacy support)"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn

    def execute_optimized_query(self, query: str, params: Tuple = None, fetch_all: bool = True) -> Any:
        """Execute query with optimization analysis"""
        # Analyze query for optimization opportunities
        analysis = self.query_optimizer.analyze_query(query, params)

        # Execute query through connection pool
        start_time = time.time()
        result = self.connection_pool.execute_query(query, params, fetch_all)
        execution_time = time.time() - start_time

        # Record execution time for analysis
        self.query_optimizer.record_execution_time(analysis['query_hash'], execution_time)

        return result
    
    # Advanced Database Management Methods
    def get_database_statistics(self) -> Dict[str, Any]:
        """Get comprehensive database statistics"""
        try:
            stats = {
                'connection_pool': self.connection_pool.get_statistics(),
                'query_optimizer': self.query_optimizer.get_performance_report(),
                'data_archiver': self.data_archiver.get_archive_statistics(),
                'database_size': os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0,
                'table_statistics': self._get_table_statistics()
            }
            return stats
        except Exception as e:
            self.logger.error(f"Error getting database statistics: {e}")
            return {}

    def _get_table_statistics(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all tables"""
        table_stats = {}

        try:
            # Get all table names
            tables = self.connection_pool.execute_query(
                "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
            )

            for table in tables:
                table_name = table['name']

                # Get row count
                count_result = self.connection_pool.execute_query(
                    f"SELECT COUNT(*) as count FROM {table_name}",
                    fetch_all=False
                )
                row_count = count_result['count'] if count_result else 0

                # Get table size (approximate)
                size_result = self.connection_pool.execute_query(
                    f"SELECT SUM(LENGTH(sql)) as size FROM sqlite_master WHERE tbl_name='{table_name}'",
                    fetch_all=False
                )
                table_size = size_result['size'] if size_result and size_result['size'] else 0

                table_stats[table_name] = {
                    'row_count': row_count,
                    'estimated_size': table_size
                }

        except Exception as e:
            self.logger.error(f"Error getting table statistics: {e}")

        return table_stats

    def optimize_database(self) -> Dict[str, Any]:
        """Perform comprehensive database optimization"""
        optimization_results = {
            'vacuum_performed': False,
            'analyze_performed': False,
            'indexes_created': 0,
            'archive_results': {},
            'errors': []
        }

        try:
            # Perform VACUUM to reclaim space
            self.connection_pool.execute_query("VACUUM")
            optimization_results['vacuum_performed'] = True
            self.logger.info("Database VACUUM completed")

            # Perform ANALYZE to update statistics
            self.connection_pool.execute_query("ANALYZE")
            optimization_results['analyze_performed'] = True
            self.logger.info("Database ANALYZE completed")

            # Archive old data
            optimization_results['archive_results'] = self.data_archiver.force_archive_all()

        except Exception as e:
            error_msg = f"Database optimization error: {e}"
            optimization_results['errors'].append(error_msg)
            self.logger.error(error_msg)

        return optimization_results

    # Enhanced Sales Data Operations
    def insert_sales_data(self, sales_data: List[Dict[str, Any]]) -> bool:
        """Insert multiple sales records with optimization"""
        try:
            # Prepare data for batch insert
            insert_data = []
            for record in sales_data:
                insert_data.append((
                    record['order_date'], record['order_number'], record['payment_method'],
                    record['department'], record['dish_code'], record['dish_name'],
                    record['quantity'], record['price_per_dish'], record['total_amount']
                ))

            # Use optimized batch insert
            query = '''
                INSERT INTO sales (order_date, order_number, payment_method,
                                 department, dish_code, dish_name, quantity,
                                 price_per_dish, total_amount)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''

            rows_affected = self.connection_pool.execute_many(query, insert_data)
            self.logger.info(f"Inserted {rows_affected} sales records")
            return True

        except Exception as e:
            self.logger.error(f"Error inserting sales data: {e}")
            print(f"Error inserting sales data: {e}")
            return False
    
    def get_sales_data(self, start_date: str = None, end_date: str = None, limit: int = None) -> List[Dict]:
        """Retrieve sales data with optional date filtering and optimization"""
        try:
            query = "SELECT * FROM sales"
            params = []

            # Build WHERE clause
            where_conditions = []
            if start_date and end_date:
                where_conditions.append("order_date BETWEEN ? AND ?")
                params.extend([start_date, end_date])
            elif start_date:
                where_conditions.append("order_date >= ?")
                params.append(start_date)
            elif end_date:
                where_conditions.append("order_date <= ?")
                params.append(end_date)

            if where_conditions:
                query += " WHERE " + " AND ".join(where_conditions)

            query += " ORDER BY order_date DESC"

            # Add limit for performance
            if limit:
                query += f" LIMIT {limit}"
            elif not (start_date or end_date):
                # Default limit for unrestricted queries
                query += " LIMIT 10000"

            # Use optimized query execution
            result = self.execute_optimized_query(query, tuple(params) if params else None)

            # Convert to list of dictionaries
            return [dict(row) for row in result] if result else []

        except Exception as e:
            self.logger.error(f"Error retrieving sales data: {e}")
            print(f"Error retrieving sales data: {e}")
            return []
    
    # Raw Materials Operations
    def insert_raw_material(self, material_data: Dict[str, Any]) -> int:
        """Insert new raw material"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO raw_materials (name, category, unit_of_measure, 
                                             current_stock, minimum_stock, supplier)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    material_data['name'], material_data.get('category', ''),
                    material_data['unit_of_measure'], material_data.get('current_stock', 0),
                    material_data.get('minimum_stock', 0), material_data.get('supplier', '')
                ))
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            print(f"Error inserting raw material: {e}")
            return 0
    
    def get_raw_materials(self) -> List[Dict]:
        """Get all raw materials"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM raw_materials ORDER BY name")
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"Error retrieving raw materials: {e}")
            return []
    
    def update_stock(self, material_id: int, new_stock: float, reason: str = "Manual adjustment"):
        """Update raw material stock with adjustment tracking"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Get current stock
                cursor.execute("SELECT current_stock FROM raw_materials WHERE id = ?", (material_id,))
                current_stock = cursor.fetchone()[0]
                
                # Update stock
                cursor.execute("UPDATE raw_materials SET current_stock = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?", 
                             (new_stock, material_id))
                
                # Record adjustment
                cursor.execute('''
                    INSERT INTO inventory_adjustments 
                    (raw_material_id, adjustment_type, quantity_before, quantity_after, 
                     adjustment_quantity, reason, adjustment_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    material_id, 'manual', current_stock, new_stock, 
                    new_stock - current_stock, reason, datetime.now().date()
                ))
                
                conn.commit()
                return True
        except Exception as e:
            print(f"Error updating stock: {e}")
            return False
    
    # Purchase Orders Operations
    def create_purchase_order(self, order_data: Dict[str, Any], items: List[Dict[str, Any]]) -> int:
        """Create new purchase order with items"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Insert purchase order
                cursor.execute('''
                    INSERT INTO purchase_orders 
                    (order_number, supplier, order_date, subtotal, vat_percentage, 
                     vat_amount, discount_percentage, discount_amount, total_amount, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    order_data['order_number'], order_data['supplier'], order_data['order_date'],
                    order_data['subtotal'], order_data.get('vat_percentage', 0),
                    order_data.get('vat_amount', 0), order_data.get('discount_percentage', 0),
                    order_data.get('discount_amount', 0), order_data['total_amount'],
                    order_data.get('notes', '')
                ))
                
                order_id = cursor.lastrowid
                
                # Insert order items
                for item in items:
                    cursor.execute('''
                        INSERT INTO purchase_order_items 
                        (purchase_order_id, raw_material_id, quantity, unit_price, total_price)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (
                        order_id, item['raw_material_id'], item['quantity'],
                        item['unit_price'], item['total_price']
                    ))
                
                conn.commit()
                return order_id
        except Exception as e:
            print(f"Error creating purchase order: {e}")
            return 0
    
    def get_purchase_orders(self) -> List[Dict]:
        """Get all purchase orders"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT po.*, COUNT(poi.id) as item_count
                    FROM purchase_orders po
                    LEFT JOIN purchase_order_items poi ON po.id = poi.purchase_order_id
                    GROUP BY po.id
                    ORDER BY po.order_date DESC
                ''')
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"Error retrieving purchase orders: {e}")
            return []

    # Performance Optimized Query Methods
    def fetch_all(self, query: str, params: List = None) -> List[Dict]:
        """Execute query and return all results"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params or [])
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"Error executing query: {e}")
            return []

    def fetch_one(self, query: str, params: List = None) -> Optional[Dict]:
        """Execute query and return first result"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params or [])
                row = cursor.fetchone()
                return dict(row) if row else None
        except Exception as e:
            print(f"Error executing query: {e}")
            return None

    def cached_fetch_all(self, query: str, params: List = None, ttl: int = None) -> List[Dict]:
        """Execute query with caching if performance optimizer is available"""
        if self.performance_optimizer:
            return self.performance_optimizer.cached_query(query, params, ttl)
        else:
            return self.fetch_all(query, params)

    def get_paginated_data(self, table: str, page: int = 0, filters: Dict = None, order_by: str = None) -> Dict:
        """Get paginated data with lazy loading if performance optimizer is available"""
        if self.performance_optimizer:
            return self.performance_optimizer.get_paginated_data(table, page, filters, order_by)
        else:
            # Fallback to regular query
            try:
                page_size = 100
                offset = page * page_size

                query = f"SELECT * FROM {table}"
                params = []

                if filters:
                    where_clauses = []
                    for field, value in filters.items():
                        where_clauses.append(f"{field} = ?")
                        params.append(value)
                    query += " WHERE " + " AND ".join(where_clauses)

                if order_by:
                    query += f" ORDER BY {order_by}"

                query += f" LIMIT {page_size} OFFSET {offset}"

                data = self.fetch_all(query, params)

                # Get total count
                count_query = f"SELECT COUNT(*) as count FROM {table}"
                if filters:
                    count_query += " WHERE " + " AND ".join([f"{k} = ?" for k in filters.keys()])

                count_result = self.fetch_one(count_query, params[:-2] if filters else [])
                total_count = count_result['count'] if count_result else 0

                return {
                    'data': data,
                    'page': page,
                    'page_size': page_size,
                    'total_count': total_count,
                    'total_pages': (total_count + page_size - 1) // page_size,
                    'has_next': (page + 1) * page_size < total_count,
                    'has_prev': page > 0
                }

            except Exception as e:
                print(f"Error getting paginated data: {e}")
                return {
                    'data': [],
                    'page': page,
                    'page_size': 100,
                    'total_count': 0,
                    'total_pages': 0,
                    'has_next': False,
                    'has_prev': False
                }

    def invalidate_cache(self, pattern: str = None):
        """Invalidate cache if performance optimizer is available"""
        if self.performance_optimizer:
            self.performance_optimizer.invalidate_cache(pattern)

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics if optimizer is available"""
        if self.performance_optimizer:
            return {
                'cache_stats': self.performance_optimizer.get_cache_stats(),
                'performance_report': self.performance_optimizer.get_performance_report(),
                'optimization_suggestions': self.performance_optimizer.get_optimization_suggestions()
            }
        else:
            return {
                'cache_stats': {},
                'performance_report': {},
                'optimization_suggestions': []
            }

    def get_sync_status(self) -> Dict[str, Any]:
        """Get synchronization status"""
        try:
            sync_status = self.sync_manager.get_sync_status()
            offline_status = self.offline_manager.get_offline_status()

            return {
                'sync': sync_status,
                'offline': offline_status,
                'location_id': self.location_id
            }
        except Exception as e:
            self.logger.error(f"Error getting sync status: {e}")
            return {
                'sync': {'error': str(e)},
                'offline': {'error': str(e)},
                'location_id': self.location_id
            }

    def force_sync(self) -> Dict[str, Any]:
        """Force immediate synchronization"""
        try:
            return self.sync_manager.force_sync()
        except Exception as e:
            self.logger.error(f"Error forcing sync: {e}")
            return {'status': 'error', 'message': str(e)}

    def set_online_status(self, online: bool):
        """Set online/offline status for sync"""
        try:
            self.sync_manager.set_online_status(online)
            self.offline_manager.set_offline_mode(not online)
        except Exception as e:
            self.logger.error(f"Error setting online status: {e}")

    def get_pending_conflicts(self) -> List[Dict[str, Any]]:
        """Get pending synchronization conflicts"""
        try:
            return self.sync_manager.get_pending_conflicts()
        except Exception as e:
            self.logger.error(f"Error getting pending conflicts: {e}")
            return []

    def resolve_conflict_manually(self, conflict_id: str, resolved_data: Dict[str, Any]) -> bool:
        """Manually resolve a synchronization conflict"""
        try:
            return self.sync_manager.resolve_conflict_manually(conflict_id, resolved_data)
        except Exception as e:
            self.logger.error(f"Error resolving conflict: {e}")
            return False
