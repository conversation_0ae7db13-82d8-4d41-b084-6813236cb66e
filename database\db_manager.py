"""
Database Manager for Restaurant Accounting and Inventory Management System
"""

import sqlite3
import os
from datetime import datetime
from typing import Optional, List, Dict, Any, Tuple
from database.models import DatabaseModels

class DatabaseManager:
    """Handles all database operations"""

    def __init__(self, db_path: str = "restaurant_system.db"):
        self.db_path = db_path
        self.performance_optimizer = None
        self.init_database()

        # Initialize performance optimizer after database is ready
        try:
            from utils.performance_optimizer import get_performance_optimizer
            self.performance_optimizer = get_performance_optimizer(self)
        except ImportError:
            pass  # Performance optimizer not available
    
    def init_database(self):
        """Initialize database and create tables"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                DatabaseModels.create_tables(conn)
                self._create_default_admin()
        except Exception as e:
            print(f"Error initializing database: {e}")
    
    def _create_default_admin(self):
        """Create default admin user if none exists"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'admin'")
                admin_count = cursor.fetchone()[0]
                
                if admin_count == 0:
                    cursor.execute('''
                        INSERT INTO users (username, password_hash, role, full_name, email)
                        VALUES (?, ?, ?, ?, ?)
                    ''', ('admin', 'admin123', 'admin', 'System Administrator', '<EMAIL>'))
                    conn.commit()
                    print("Default admin user created (username: admin, password: admin123)")
        except Exception as e:
            print(f"Error creating default admin: {e}")
    
    def get_connection(self) -> sqlite3.Connection:
        """Get database connection with row factory"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    # Sales Data Operations
    def insert_sales_data(self, sales_data: List[Dict[str, Any]]) -> bool:
        """Insert multiple sales records"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                for record in sales_data:
                    cursor.execute('''
                        INSERT INTO sales (order_date, order_number, payment_method, 
                                         department, dish_code, dish_name, quantity, 
                                         price_per_dish, total_amount)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        record['order_date'], record['order_number'], record['payment_method'],
                        record['department'], record['dish_code'], record['dish_name'],
                        record['quantity'], record['price_per_dish'], record['total_amount']
                    ))
                conn.commit()
                return True
        except Exception as e:
            print(f"Error inserting sales data: {e}")
            return False
    
    def get_sales_data(self, start_date: str = None, end_date: str = None) -> List[Dict]:
        """Retrieve sales data with optional date filtering"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                query = "SELECT * FROM sales"
                params = []
                
                if start_date and end_date:
                    query += " WHERE order_date BETWEEN ? AND ?"
                    params = [start_date, end_date]
                elif start_date:
                    query += " WHERE order_date >= ?"
                    params = [start_date]
                elif end_date:
                    query += " WHERE order_date <= ?"
                    params = [end_date]
                
                query += " ORDER BY order_date DESC"
                cursor.execute(query, params)
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"Error retrieving sales data: {e}")
            return []
    
    # Raw Materials Operations
    def insert_raw_material(self, material_data: Dict[str, Any]) -> int:
        """Insert new raw material"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO raw_materials (name, category, unit_of_measure, 
                                             current_stock, minimum_stock, supplier)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    material_data['name'], material_data.get('category', ''),
                    material_data['unit_of_measure'], material_data.get('current_stock', 0),
                    material_data.get('minimum_stock', 0), material_data.get('supplier', '')
                ))
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            print(f"Error inserting raw material: {e}")
            return 0
    
    def get_raw_materials(self) -> List[Dict]:
        """Get all raw materials"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM raw_materials ORDER BY name")
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"Error retrieving raw materials: {e}")
            return []
    
    def update_stock(self, material_id: int, new_stock: float, reason: str = "Manual adjustment"):
        """Update raw material stock with adjustment tracking"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Get current stock
                cursor.execute("SELECT current_stock FROM raw_materials WHERE id = ?", (material_id,))
                current_stock = cursor.fetchone()[0]
                
                # Update stock
                cursor.execute("UPDATE raw_materials SET current_stock = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?", 
                             (new_stock, material_id))
                
                # Record adjustment
                cursor.execute('''
                    INSERT INTO inventory_adjustments 
                    (raw_material_id, adjustment_type, quantity_before, quantity_after, 
                     adjustment_quantity, reason, adjustment_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    material_id, 'manual', current_stock, new_stock, 
                    new_stock - current_stock, reason, datetime.now().date()
                ))
                
                conn.commit()
                return True
        except Exception as e:
            print(f"Error updating stock: {e}")
            return False
    
    # Purchase Orders Operations
    def create_purchase_order(self, order_data: Dict[str, Any], items: List[Dict[str, Any]]) -> int:
        """Create new purchase order with items"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Insert purchase order
                cursor.execute('''
                    INSERT INTO purchase_orders 
                    (order_number, supplier, order_date, subtotal, vat_percentage, 
                     vat_amount, discount_percentage, discount_amount, total_amount, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    order_data['order_number'], order_data['supplier'], order_data['order_date'],
                    order_data['subtotal'], order_data.get('vat_percentage', 0),
                    order_data.get('vat_amount', 0), order_data.get('discount_percentage', 0),
                    order_data.get('discount_amount', 0), order_data['total_amount'],
                    order_data.get('notes', '')
                ))
                
                order_id = cursor.lastrowid
                
                # Insert order items
                for item in items:
                    cursor.execute('''
                        INSERT INTO purchase_order_items 
                        (purchase_order_id, raw_material_id, quantity, unit_price, total_price)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (
                        order_id, item['raw_material_id'], item['quantity'],
                        item['unit_price'], item['total_price']
                    ))
                
                conn.commit()
                return order_id
        except Exception as e:
            print(f"Error creating purchase order: {e}")
            return 0
    
    def get_purchase_orders(self) -> List[Dict]:
        """Get all purchase orders"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT po.*, COUNT(poi.id) as item_count
                    FROM purchase_orders po
                    LEFT JOIN purchase_order_items poi ON po.id = poi.purchase_order_id
                    GROUP BY po.id
                    ORDER BY po.order_date DESC
                ''')
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"Error retrieving purchase orders: {e}")
            return []

    # Performance Optimized Query Methods
    def fetch_all(self, query: str, params: List = None) -> List[Dict]:
        """Execute query and return all results"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params or [])
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"Error executing query: {e}")
            return []

    def fetch_one(self, query: str, params: List = None) -> Optional[Dict]:
        """Execute query and return first result"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params or [])
                row = cursor.fetchone()
                return dict(row) if row else None
        except Exception as e:
            print(f"Error executing query: {e}")
            return None

    def cached_fetch_all(self, query: str, params: List = None, ttl: int = None) -> List[Dict]:
        """Execute query with caching if performance optimizer is available"""
        if self.performance_optimizer:
            return self.performance_optimizer.cached_query(query, params, ttl)
        else:
            return self.fetch_all(query, params)

    def get_paginated_data(self, table: str, page: int = 0, filters: Dict = None, order_by: str = None) -> Dict:
        """Get paginated data with lazy loading if performance optimizer is available"""
        if self.performance_optimizer:
            return self.performance_optimizer.get_paginated_data(table, page, filters, order_by)
        else:
            # Fallback to regular query
            try:
                page_size = 100
                offset = page * page_size

                query = f"SELECT * FROM {table}"
                params = []

                if filters:
                    where_clauses = []
                    for field, value in filters.items():
                        where_clauses.append(f"{field} = ?")
                        params.append(value)
                    query += " WHERE " + " AND ".join(where_clauses)

                if order_by:
                    query += f" ORDER BY {order_by}"

                query += f" LIMIT {page_size} OFFSET {offset}"

                data = self.fetch_all(query, params)

                # Get total count
                count_query = f"SELECT COUNT(*) as count FROM {table}"
                if filters:
                    count_query += " WHERE " + " AND ".join([f"{k} = ?" for k in filters.keys()])

                count_result = self.fetch_one(count_query, params[:-2] if filters else [])
                total_count = count_result['count'] if count_result else 0

                return {
                    'data': data,
                    'page': page,
                    'page_size': page_size,
                    'total_count': total_count,
                    'total_pages': (total_count + page_size - 1) // page_size,
                    'has_next': (page + 1) * page_size < total_count,
                    'has_prev': page > 0
                }

            except Exception as e:
                print(f"Error getting paginated data: {e}")
                return {
                    'data': [],
                    'page': page,
                    'page_size': 100,
                    'total_count': 0,
                    'total_pages': 0,
                    'has_next': False,
                    'has_prev': False
                }

    def invalidate_cache(self, pattern: str = None):
        """Invalidate cache if performance optimizer is available"""
        if self.performance_optimizer:
            self.performance_optimizer.invalidate_cache(pattern)

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics if optimizer is available"""
        if self.performance_optimizer:
            return {
                'cache_stats': self.performance_optimizer.get_cache_stats(),
                'performance_report': self.performance_optimizer.get_performance_report(),
                'optimization_suggestions': self.performance_optimizer.get_optimization_suggestions()
            }
        else:
            return {
                'cache_stats': {},
                'performance_report': {},
                'optimization_suggestions': []
            }
