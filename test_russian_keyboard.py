#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import ctypes
import time

def test_russian_keyboard_switching():
    """Тестирование переключения на русскую раскладку клавиатуры"""
    
    print("🔧 Тестирование переключения на русскую раскладку клавиатуры...")
    print("=" * 70)
    
    user32 = ctypes.windll.user32
    
    # Метод 1: LoadKeyboardLayout и ActivateKeyboardLayout
    print("\n1️⃣ Тестирование метода LoadKeyboardLayout...")
    try:
        # Загружаем русскую раскладку
        hkl_ru = user32.LoadKeyboardLayoutW("00000419", 0x00000001)
        if hkl_ru:
            hwnd = user32.GetForegroundWindow()
            result = user32.ActivateKeyboardLayout(hkl_ru, 0)
            print(f"   ✅ Русская раскладка загружена: HKL={hkl_ru}, Result={result}")
            time.sleep(3)
            print("   📝 Попробуйте печатать - должна быть русская раскладка")
        else:
            print("   ❌ Не удалось загрузить русскую раскладку")
            
    except Exception as e:
        print(f"   ❌ Ошибка в методе 1: {e}")
    
    time.sleep(2)
    
    # Метод 2: PostMessage с WM_INPUTLANGCHANGEREQUEST
    print("\n2️⃣ Тестирование метода PostMessage...")
    try:
        HWND_BROADCAST = 0xFFFF
        WM_INPUTLANGCHANGEREQUEST = 0x0050
        
        # Переключаем на русскую
        result = user32.PostMessageW(HWND_BROADCAST, WM_INPUTLANGCHANGEREQUEST, 0, 0x04190419)
        print(f"   ✅ PostMessage на русскую: Result={result}")
        time.sleep(3)
        print("   📝 Попробуйте печатать - должна быть русская раскладка")
        
    except Exception as e:
        print(f"   ❌ Ошибка в методе 2: {e}")
    
    time.sleep(2)
    
    # Метод 3: Альтернативные коды русской раскладки
    print("\n3️⃣ Тестирование альтернативных кодов...")
    russian_codes = [
        0x00000419,  # Русская (короткий код)
        0x04190000,  # Русская (альтернативный код)
        0x04190419,  # Русская (полный код)
    ]
    
    for i, code in enumerate(russian_codes):
        try:
            result = user32.PostMessageW(HWND_BROADCAST, WM_INPUTLANGCHANGEREQUEST, 0, code)
            print(f"   ✅ Код {hex(code)}: Result={result}")
            time.sleep(2)
        except Exception as e:
            print(f"   ❌ Ошибка с кодом {hex(code)}: {e}")
    
    # Метод 4: Имитация Alt+Shift
    print("\n4️⃣ Тестирование метода Alt+Shift...")
    try:
        # Имитируем Alt+Shift
        user32.keybd_event(0x12, 0, 0, 0)  # Alt down
        user32.keybd_event(0x10, 0, 0, 0)  # Shift down
        user32.keybd_event(0x10, 0, 2, 0)  # Shift up
        user32.keybd_event(0x12, 0, 2, 0)  # Alt up
        print("   ✅ Alt+Shift отправлен")
        time.sleep(3)
        print("   📝 Попробуйте печатать - раскладка должна была переключиться")
        
    except Exception as e:
        print(f"   ❌ Ошибка в методе 4: {e}")
    
    # Метод 5: Получение текущей раскладки
    print("\n5️⃣ Получение информации о текущей раскладке...")
    try:
        # Получаем текущую раскладку
        hwnd = user32.GetForegroundWindow()
        thread_id = user32.GetWindowThreadProcessId(hwnd, None)
        hkl = user32.GetKeyboardLayout(thread_id)
        
        print(f"   📋 Текущая раскладка: {hex(hkl)} ({hkl})")
        
        # Определяем язык
        lang_id = hkl & 0xFFFF
        if lang_id == 0x0409:
            print("   🇺🇸 Текущая раскладка: Английская")
        elif lang_id == 0x0419:
            print("   🇷🇺 Текущая раскладка: Русская")
        else:
            print(f"   🌐 Текущая раскладка: Неизвестная (ID: {hex(lang_id)})")
            
    except Exception as e:
        print(f"   ❌ Ошибка в методе 5: {e}")
    
    print("\n" + "=" * 70)
    print("🏁 Тестирование завершено!")
    print("\n💡 Инструкции:")
    print("   - Проверьте, изменилась ли раскладка клавиатуры во время тестирования")
    print("   - Попробуйте печатать русские буквы для проверки")
    print("   - Обратите внимание на сообщения об успешном/неуспешном переключении")

if __name__ == "__main__":
    test_russian_keyboard_switching()
    input("\nНажмите Enter для выхода...")
