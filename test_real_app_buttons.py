#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Тест кнопок в реальном приложении
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_real_app_buttons():
    """Тест кнопок в реальном приложении"""
    
    print("🧪 ТЕСТ КНОПОК В РЕАЛЬНОМ ПРИЛОЖЕНИИ")
    print("=" * 50)
    
    try:
        # Импортировать главное окно
        from gui.main_window import MainWindow
        import tkinter as tk
        
        print("✅ Модули импортированы успешно")
        
        # Создать главное окно
        selected_db = {'name': 'Тестовый Ресторан', 'file_path': 'test_restaurant.db'}
        main_window = MainWindow(selected_db)
        print("✅ Главное окно создано")
        
        # Получить список всех методов show_*
        show_methods = []
        for attr_name in dir(main_window):
            if attr_name.startswith('show_') and callable(getattr(main_window, attr_name)):
                show_methods.append(attr_name)
        
        print(f"📊 Найдено {len(show_methods)} методов show_*")
        
        # Тестировать каждый метод
        working_methods = []
        broken_methods = []
        
        for method_name in show_methods:
            try:
                method = getattr(main_window, method_name)
                # Попробовать вызвать метод
                method()
                working_methods.append(method_name)
                print(f"✅ {method_name} - РАБОТАЕТ")
            except Exception as e:
                broken_methods.append((method_name, str(e)))
                print(f"❌ {method_name} - ОШИБКА: {str(e)[:100]}")
        
        # Результаты
        print("\n" + "=" * 50)
        print("📊 ИТОГОВЫЕ РЕЗУЛЬТАТЫ:")
        print(f"✅ Работающих методов: {len(working_methods)}")
        print(f"❌ Проблемных методов: {len(broken_methods)}")
        
        if broken_methods:
            print("\n❌ ПРОБЛЕМНЫЕ МЕТОДЫ:")
            for method_name, error in broken_methods:
                print(f"   • {method_name}: {error[:100]}")
        
        # Закрыть окно
        main_window.root.destroy()
        
        print("\n✅ Тест завершен")
        
    except Exception as e:
        print(f"❌ Критическая ошибка в тесте: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_real_app_buttons()
