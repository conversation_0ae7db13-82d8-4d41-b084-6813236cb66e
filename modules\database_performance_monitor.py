"""
Advanced Database Performance Monitor for Restaurant Management System
Provides real-time monitoring, optimization suggestions, and performance analytics
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import numpy as np
from gui.styles import ModernStyles

def format_bytes(bytes_value):
    """Format bytes to human readable format"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if bytes_value < 1024.0:
            return f"{bytes_value:.1f} {unit}"
        bytes_value /= 1024.0
    return f"{bytes_value:.1f} TB"

def format_duration(seconds):
    """Format duration in seconds to human readable format"""
    if seconds < 1:
        return f"{seconds*1000:.1f} ms"
    elif seconds < 60:
        return f"{seconds:.2f} s"
    elif seconds < 3600:
        return f"{seconds/60:.1f} min"
    else:
        return f"{seconds/3600:.1f} h"

class DatabasePerformanceMonitor:
    """Advanced database performance monitoring dashboard"""

    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.monitoring_active = False
        self.monitoring_thread = None
        self.performance_history = []
        self.max_history_points = 100

        # UI Components
        self.stats_labels = {}
        self.charts = {}
        self.tree_views = {}

    def show_monitor(self):
        """Display the database performance monitor"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return

        self.window = tk.Toplevel(self.parent)
        self.window.title("🔍 Мониторинг Производительности Базы Данных")
        self.window.geometry("1400x900")
        self.window.configure(bg=ModernStyles.COLORS['bg_main'])

        self.create_monitor_interface()
        self.start_monitoring()

    def create_monitor_interface(self):
        """Create the monitoring interface"""
        # Header
        header_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['primary'], height=60)
        header_frame.pack(fill='x', padx=10, pady=(10, 0))
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="🔍 Мониторинг Производительности Базы Данных",
                font=('Cambria', 18, 'bold'),
                fg='white', bg=ModernStyles.COLORS['primary']).pack(side='left', padx=20, pady=15)

        # Control buttons
        control_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['primary'])
        control_frame.pack(side='right', padx=20, pady=10)

        tk.Button(control_frame, text="🔄 Обновить",
                 command=self.refresh_data,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 10, 'bold'),
                 relief='flat', padx=15).pack(side='left', padx=5)

        tk.Button(control_frame, text="⚡ Оптимизировать",
                 command=self.optimize_database,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Cambria', 10, 'bold'),
                 relief='flat', padx=15).pack(side='left', padx=5)

        tk.Button(control_frame, text="📊 Экспорт Отчета",
                 command=self.export_report,
                 bg=ModernStyles.COLORS['accent'], fg='white',
                 font=('Cambria', 10, 'bold'),
                 relief='flat', padx=15).pack(side='left', padx=5)

        # Main content
        main_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Create notebook for different monitoring sections
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill='both', expand=True)

        # Overview tab
        self.create_overview_tab(notebook)

        # Connection Pool tab
        self.create_connection_pool_tab(notebook)

        # Query Performance tab
        self.create_query_performance_tab(notebook)

        # Archive Management tab
        self.create_archive_management_tab(notebook)

        # Real-time Charts tab
        self.create_charts_tab(notebook)

    def create_overview_tab(self, notebook):
        """Create overview tab with key metrics"""
        overview_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(overview_frame, text="📊 Обзор")

        # Key metrics cards
        metrics_frame = tk.Frame(overview_frame, bg=ModernStyles.COLORS['bg_main'])
        metrics_frame.pack(fill='x', padx=20, pady=20)

        # Database size card
        self.create_metric_card(metrics_frame, "💾 Размер БД", "0 MB",
                               ModernStyles.COLORS['primary'], 0, 0)

        # Total queries card
        self.create_metric_card(metrics_frame, "🔍 Всего Запросов", "0",
                               ModernStyles.COLORS['success'], 0, 1)

        # Average query time card
        self.create_metric_card(metrics_frame, "⏱️ Среднее Время", "0 ms",
                               ModernStyles.COLORS['warning'], 0, 2)

        # Active connections card
        self.create_metric_card(metrics_frame, "🔗 Активные Соединения", "0",
                               ModernStyles.COLORS['accent'], 0, 3)

        # Table statistics
        table_frame = tk.LabelFrame(overview_frame, text="📋 Статистика Таблиц",
                                   font=('Cambria', 12, 'bold'),
                                   fg=ModernStyles.COLORS['text_primary'],
                                   bg=ModernStyles.COLORS['bg_main'])
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Create table statistics tree
        columns = ('Таблица', 'Записи', 'Размер', 'Последнее Обновление')
        self.tree_views['tables'] = ttk.Treeview(table_frame, columns=columns, show='headings', height=10)

        for col in columns:
            self.tree_views['tables'].heading(col, text=col)
            self.tree_views['tables'].column(col, width=150)

        # Scrollbar for table tree
        table_scrollbar = ttk.Scrollbar(table_frame, orient='vertical',
                                       command=self.tree_views['tables'].yview)
        self.tree_views['tables'].configure(yscrollcommand=table_scrollbar.set)

        self.tree_views['tables'].pack(side='left', fill='both', expand=True, padx=10, pady=10)
        table_scrollbar.pack(side='right', fill='y', pady=10)

    def create_connection_pool_tab(self, notebook):
        """Create connection pool monitoring tab"""
        pool_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(pool_frame, text="🔗 Пул Соединений")

        # Connection pool metrics
        pool_metrics_frame = tk.Frame(pool_frame, bg=ModernStyles.COLORS['bg_main'])
        pool_metrics_frame.pack(fill='x', padx=20, pady=20)

        self.create_metric_card(pool_metrics_frame, "🏊 Размер Пула", "0",
                               ModernStyles.COLORS['primary'], 0, 0)

        self.create_metric_card(pool_metrics_frame, "🔄 Активные", "0",
                               ModernStyles.COLORS['success'], 0, 1)

        self.create_metric_card(pool_metrics_frame, "📈 Пиковое Использование", "0",
                               ModernStyles.COLORS['warning'], 0, 2)

        self.create_metric_card(pool_metrics_frame, "🏗️ Всего Создано", "0",
                               ModernStyles.COLORS['accent'], 0, 3)

        # Connection details
        details_frame = tk.LabelFrame(pool_frame, text="📊 Детали Соединений",
                                     font=('Cambria', 12, 'bold'),
                                     fg=ModernStyles.COLORS['text_primary'],
                                     bg=ModernStyles.COLORS['bg_main'])
        details_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Connection details text
        self.connection_details_text = tk.Text(details_frame, height=15,
                                              font=('Consolas', 10),
                                              bg='white', fg='black',
                                              wrap='word')

        details_scrollbar = ttk.Scrollbar(details_frame, orient='vertical',
                                         command=self.connection_details_text.yview)
        self.connection_details_text.configure(yscrollcommand=details_scrollbar.set)

        self.connection_details_text.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        details_scrollbar.pack(side='right', fill='y', pady=10)

    def create_query_performance_tab(self, notebook):
        """Create query performance monitoring tab"""
        query_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(query_frame, text="⚡ Производительность Запросов")

        # Query metrics
        query_metrics_frame = tk.Frame(query_frame, bg=ModernStyles.COLORS['bg_main'])
        query_metrics_frame.pack(fill='x', padx=20, pady=20)

        self.create_metric_card(query_metrics_frame, "🔍 Всего Запросов", "0",
                               ModernStyles.COLORS['primary'], 0, 0)

        self.create_metric_card(query_metrics_frame, "🐌 Медленных Запросов", "0",
                               ModernStyles.COLORS['danger'], 0, 1)

        self.create_metric_card(query_metrics_frame, "⚡ Среднее Время", "0 ms",
                               ModernStyles.COLORS['success'], 0, 2)

        self.create_metric_card(query_metrics_frame, "📈 Индексов Создано", "0",
                               ModernStyles.COLORS['accent'], 0, 3)

        # Slow queries list
        slow_queries_frame = tk.LabelFrame(query_frame, text="🐌 Медленные Запросы",
                                          font=('Cambria', 12, 'bold'),
                                          fg=ModernStyles.COLORS['text_primary'],
                                          bg=ModernStyles.COLORS['bg_main'])
        slow_queries_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Create slow queries tree
        slow_columns = ('Запрос', 'Время Выполнения', 'Количество', 'Последнее Выполнение')
        self.tree_views['slow_queries'] = ttk.Treeview(slow_queries_frame, columns=slow_columns,
                                                      show='headings', height=12)

        for col in slow_columns:
            self.tree_views['slow_queries'].heading(col, text=col)
            self.tree_views['slow_queries'].column(col, width=200)

        # Scrollbar for slow queries tree
        slow_scrollbar = ttk.Scrollbar(slow_queries_frame, orient='vertical',
                                      command=self.tree_views['slow_queries'].yview)
        self.tree_views['slow_queries'].configure(yscrollcommand=slow_scrollbar.set)

        self.tree_views['slow_queries'].pack(side='left', fill='both', expand=True, padx=10, pady=10)
        slow_scrollbar.pack(side='right', fill='y', pady=10)

    def create_archive_management_tab(self, notebook):
        """Create archive management tab"""
        archive_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(archive_frame, text="📦 Управление Архивами")

        # Archive metrics
        archive_metrics_frame = tk.Frame(archive_frame, bg=ModernStyles.COLORS['bg_main'])
        archive_metrics_frame.pack(fill='x', padx=20, pady=20)

        self.create_metric_card(archive_metrics_frame, "📦 Архивированных Записей", "0",
                               ModernStyles.COLORS['primary'], 0, 0)

        self.create_metric_card(archive_metrics_frame, "💾 Размер Архивов", "0 MB",
                               ModernStyles.COLORS['success'], 0, 1)

        self.create_metric_card(archive_metrics_frame, "🗜️ Коэффициент Сжатия", "0%",
                               ModernStyles.COLORS['warning'], 0, 2)

        self.create_metric_card(archive_metrics_frame, "📅 Последний Архив", "Никогда",
                               ModernStyles.COLORS['accent'], 0, 3)

        # Archive controls
        controls_frame = tk.Frame(archive_frame, bg=ModernStyles.COLORS['bg_main'])
        controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(controls_frame, text="📦 Принудительное Архивирование",
                 command=self.force_archive,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 11, 'bold'),
                 relief='flat', padx=20, pady=10).pack(side='left', padx=10)

        tk.Button(controls_frame, text="🔄 Восстановить Данные",
                 command=self.restore_data_dialog,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Cambria', 11, 'bold'),
                 relief='flat', padx=20, pady=10).pack(side='left', padx=10)

        # Archive directories list
        archive_dirs_frame = tk.LabelFrame(archive_frame, text="📁 Директории Архивов",
                                          font=('Cambria', 12, 'bold'),
                                          fg=ModernStyles.COLORS['text_primary'],
                                          bg=ModernStyles.COLORS['bg_main'])
        archive_dirs_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Create archive directories tree
        archive_columns = ('Таблица', 'Файлов', 'Размер', 'Путь')
        self.tree_views['archives'] = ttk.Treeview(archive_dirs_frame, columns=archive_columns,
                                                  show='headings', height=10)

        for col in archive_columns:
            self.tree_views['archives'].heading(col, text=col)
            self.tree_views['archives'].column(col, width=150)

        # Scrollbar for archives tree
        archive_scrollbar = ttk.Scrollbar(archive_dirs_frame, orient='vertical',
                                         command=self.tree_views['archives'].yview)
        self.tree_views['archives'].configure(yscrollcommand=archive_scrollbar.set)

        self.tree_views['archives'].pack(side='left', fill='both', expand=True, padx=10, pady=10)
        archive_scrollbar.pack(side='right', fill='y', pady=10)

    def create_charts_tab(self, notebook):
        """Create real-time charts tab"""
        charts_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(charts_frame, text="📈 Графики")

        # Create matplotlib figure
        self.fig = Figure(figsize=(12, 8), facecolor='white')

        # Query performance chart
        self.ax1 = self.fig.add_subplot(2, 2, 1)
        self.ax1.set_title('Производительность Запросов', fontsize=12, fontweight='bold')
        self.ax1.set_xlabel('Время')
        self.ax1.set_ylabel('Время Выполнения (мс)')

        # Connection pool usage chart
        self.ax2 = self.fig.add_subplot(2, 2, 2)
        self.ax2.set_title('Использование Пула Соединений', fontsize=12, fontweight='bold')
        self.ax2.set_xlabel('Время')
        self.ax2.set_ylabel('Активные Соединения')

        # Database size chart
        self.ax3 = self.fig.add_subplot(2, 2, 3)
        self.ax3.set_title('Размер Базы Данных', fontsize=12, fontweight='bold')
        self.ax3.set_xlabel('Время')
        self.ax3.set_ylabel('Размер (MB)')

        # Query types distribution
        self.ax4 = self.fig.add_subplot(2, 2, 4)
        self.ax4.set_title('Распределение Типов Запросов', fontsize=12, fontweight='bold')

        self.fig.tight_layout()

        # Create canvas
        self.canvas = FigureCanvasTkAgg(self.fig, charts_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill='both', expand=True, padx=10, pady=10)

    def create_metric_card(self, parent, title, value, color, row, col):
        """Create a metric card widget"""
        card_frame = tk.Frame(parent, bg=color, relief='raised', bd=2)
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky='ew')
        parent.grid_columnconfigure(col, weight=1)

        tk.Label(card_frame, text=title,
                font=('Cambria', 10, 'bold'),
                fg='white', bg=color).pack(pady=(10, 5))

        value_label = tk.Label(card_frame, text=value,
                              font=('Cambria', 16, 'bold'),
                              fg='white', bg=color)
        value_label.pack(pady=(0, 10))

        # Store reference for updating
        self.stats_labels[title] = value_label

    def start_monitoring(self):
        """Start the monitoring thread"""
        if not self.monitoring_active:
            self.monitoring_active = True
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()

    def stop_monitoring(self):
        """Stop the monitoring thread"""
        self.monitoring_active = False

    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                self.update_all_data()
                time.sleep(5)  # Update every 5 seconds
            except Exception as e:
                print(f"Monitoring error: {e}")
                time.sleep(10)  # Wait longer on error

    def update_all_data(self):
        """Update all monitoring data"""
        if not self.window or not self.window.winfo_exists():
            self.stop_monitoring()
            return

        try:
            # Get database statistics
            stats = self.db_manager.get_database_statistics()

            # Update overview metrics
            self.update_overview_metrics(stats)

            # Update connection pool data
            self.update_connection_pool_data(stats.get('connection_pool', {}))

            # Update query performance data
            self.update_query_performance_data(stats.get('query_optimizer', {}))

            # Update archive data
            self.update_archive_data(stats.get('data_archiver', {}))

            # Update charts
            self.update_charts(stats)

            # Store performance history
            self.performance_history.append({
                'timestamp': datetime.now(),
                'stats': stats
            })

            # Limit history size
            if len(self.performance_history) > self.max_history_points:
                self.performance_history.pop(0)

        except Exception as e:
            print(f"Error updating monitoring data: {e}")

    def update_overview_metrics(self, stats):
        """Update overview tab metrics"""
        try:
            # Database size
            db_size = stats.get('database_size', 0)
            if '💾 Размер БД' in self.stats_labels:
                self.stats_labels['💾 Размер БД'].config(text=format_bytes(db_size))

            # Total queries
            query_stats = stats.get('query_optimizer', {})
            total_queries = query_stats.get('total_queries_analyzed', 0)
            if '🔍 Всего Запросов' in self.stats_labels:
                self.stats_labels['🔍 Всего Запросов'].config(text=str(total_queries))

            # Average query time
            avg_time = 0
            if 'frequent_queries' in query_stats and query_stats['frequent_queries']:
                avg_time = sum(q['avg_time'] for q in query_stats['frequent_queries']) / len(query_stats['frequent_queries'])
            if '⏱️ Среднее Время' in self.stats_labels:
                self.stats_labels['⏱️ Среднее Время'].config(text=format_duration(avg_time))

            # Active connections
            pool_stats = stats.get('connection_pool', {})
            active_connections = pool_stats.get('active_connections', 0)
            if '🔗 Активные Соединения' in self.stats_labels:
                self.stats_labels['🔗 Активные Соединения'].config(text=str(active_connections))

            # Update table statistics
            self.update_table_statistics(stats.get('table_statistics', {}))

        except Exception as e:
            print(f"Error updating overview metrics: {e}")

    def update_table_statistics(self, table_stats):
        """Update table statistics tree view"""
        try:
            if 'tables' not in self.tree_views:
                return

            # Clear existing items
            for item in self.tree_views['tables'].get_children():
                self.tree_views['tables'].delete(item)

            # Add table statistics
            for table_name, stats in table_stats.items():
                self.tree_views['tables'].insert('', 'end', values=(
                    table_name,
                    f"{stats.get('row_count', 0):,}",
                    format_bytes(stats.get('estimated_size', 0)),
                    datetime.now().strftime('%Y-%m-%d %H:%M')
                ))

        except Exception as e:
            print(f"Error updating table statistics: {e}")

    def update_connection_pool_data(self, pool_stats):
        """Update connection pool tab data"""
        try:
            # Update pool metrics
            if '🏊 Размер Пула' in self.stats_labels:
                self.stats_labels['🏊 Размер Пула'].config(text=str(pool_stats.get('pool_size', 0)))

            if '🔄 Активные' in self.stats_labels:
                self.stats_labels['🔄 Активные'].config(text=str(pool_stats.get('active_connections', 0)))

            if '📈 Пиковое Использование' in self.stats_labels:
                self.stats_labels['📈 Пиковое Использование'].config(text=str(pool_stats.get('peak_connections', 0)))

            if '🏗️ Всего Создано' in self.stats_labels:
                self.stats_labels['🏗️ Всего Создано'].config(text=str(pool_stats.get('total_connections_created', 0)))

            # Update connection details
            if hasattr(self, 'connection_details_text'):
                details = f"""
Статистика Пула Соединений:
═══════════════════════════════════════

Размер пула: {pool_stats.get('pool_size', 0)}
Активные соединения: {pool_stats.get('active_connections', 0)}
Доступные соединения: {pool_stats.get('available_connections', 0)}
Пиковое использование: {pool_stats.get('peak_connections', 0)}
Всего создано соединений: {pool_stats.get('total_connections_created', 0)}
Всего выполнено запросов: {pool_stats.get('total_queries_executed', 0)}
Среднее время получения соединения: {format_duration(pool_stats.get('avg_connection_wait_time', 0))}
Время работы пула: {format_duration(pool_stats.get('uptime', 0))}

Последние события:
{pool_stats.get('recent_events', 'Нет событий')}
                """

                self.connection_details_text.delete(1.0, tk.END)
                self.connection_details_text.insert(1.0, details.strip())

        except Exception as e:
            print(f"Error updating connection pool data: {e}")

    def refresh_data(self):
        """Manually refresh all data"""
        self.update_all_data()
        messagebox.showinfo("Обновление", "Данные мониторинга обновлены!")

    def optimize_database(self):
        """Optimize database performance"""
        try:
            result = messagebox.askyesno("Оптимизация БД",
                                       "Выполнить оптимизацию базы данных?\n\n"
                                       "Это может занять некоторое время.")
            if result:
                # Show progress dialog
                progress_window = tk.Toplevel(self.window)
                progress_window.title("Оптимизация...")
                progress_window.geometry("400x150")
                progress_window.configure(bg=ModernStyles.COLORS['bg_main'])

                tk.Label(progress_window, text="Выполняется оптимизация базы данных...",
                        font=('Cambria', 12),
                        bg=ModernStyles.COLORS['bg_main'],
                        fg=ModernStyles.COLORS['text_primary']).pack(pady=20)

                progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
                progress_bar.pack(pady=20, padx=20, fill='x')
                progress_bar.start()

                # Run optimization in thread
                def optimize():
                    try:
                        optimization_results = self.db_manager.optimize_database()
                        progress_window.destroy()

                        # Show results
                        result_text = f"""
Результаты оптимизации:

✓ VACUUM выполнен: {'Да' if optimization_results.get('vacuum_performed') else 'Нет'}
✓ ANALYZE выполнен: {'Да' if optimization_results.get('analyze_performed') else 'Нет'}
✓ Создано индексов: {optimization_results.get('indexes_created', 0)}

Архивирование данных:
{self._format_archive_results(optimization_results.get('archive_results', {}))}

{'Ошибки: ' + ', '.join(optimization_results.get('errors', [])) if optimization_results.get('errors') else ''}
                        """

                        messagebox.showinfo("Оптимизация завершена", result_text.strip())
                        self.refresh_data()

                    except Exception as e:
                        progress_window.destroy()
                        messagebox.showerror("Ошибка", f"Ошибка оптимизации: {e}")

                threading.Thread(target=optimize, daemon=True).start()

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка запуска оптимизации: {e}")

    def _format_archive_results(self, archive_results):
        """Format archive results for display"""
        if not archive_results:
            return "Нет данных для архивирования"

        formatted = []
        for table, result in archive_results.items():
            if result.get('status') == 'success':
                formatted.append(f"  {table}: {result.get('records_archived', 0)} записей")
            elif result.get('status') == 'no_data':
                formatted.append(f"  {table}: нет данных для архивирования")
            else:
                formatted.append(f"  {table}: ошибка - {result.get('error', 'неизвестная')}")

        return '\n'.join(formatted) if formatted else "Нет результатов"