#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ПРЯМОЕ ТЕСТИРОВАНИЕ ЭКСТРЕМАЛЬНЫХ МЕТОДОВ
Тестирует методы напрямую без запуска внешних процессов
"""

import sys
import os
import ctypes
import time

def safe_print(text):
    """Безопасный вывод с обработкой Unicode ошибок"""
    try:
        print(text)
    except UnicodeEncodeError:
        ascii_text = text.encode('ascii', 'ignore').decode('ascii')
        print(ascii_text)

def check_current_layout():
    """Проверить текущую раскладку"""
    try:
        user32 = ctypes.windll.user32
        hwnd = user32.GetForegroundWindow()
        thread_id = user32.GetWindowThreadProcessId(hwnd, None)
        hkl = user32.GetKeyboardLayout(thread_id)
        layout_id = hkl & 0xFFFF
        
        safe_print(f"🔍 ТЕКУЩАЯ РАСКЛАДКА: {hex(layout_id)}")
        if layout_id == 0x0419:
            safe_print("✅ РУССКАЯ РАСКЛАДКА АКТИВНА!")
            return True
        else:
            safe_print("❌ Раскладка НЕ русская")
            return False
            
    except Exception as e:
        safe_print(f"❌ Ошибка проверки раскладки: {e}")
        return False

def test_extreme_hook_direct():
    """Прямое тестирование экстремального хука"""
    safe_print("\n" + "="*80)
    safe_print("☢️ ПРЯМОЕ ТЕСТИРОВАНИЕ ЭКСТРЕМАЛЬНОГО ХУКА")
    safe_print("="*80)
    
    try:
        # Импортируем модуль хука
        sys.path.append('.')
        from extreme_keyboard_hook import ExtremKeyboardHook
        
        safe_print("✅ Модуль экстремального хука импортирован")
        
        # Создаем экземпляр хука
        hook = ExtremKeyboardHook()
        safe_print("✅ Экземпляр хука создан")
        
        # Тестируем принудительное переключение
        safe_print("🔧 Тестирование принудительного переключения...")
        hook.force_switch_to_russian()
        
        time.sleep(1)
        
        # Проверяем результат
        current_layout = hook.get_current_layout()
        safe_print(f"📋 Результат: раскладка {hex(current_layout)}")
        
        if current_layout == 0x0419:
            safe_print("🎉 ЭКСТРЕМАЛЬНЫЙ ХУК СРАБОТАЛ!")
            return True
        else:
            safe_print("💥 Экстремальный хук не сработал")
            return False
            
    except Exception as e:
        safe_print(f"❌ Ошибка при тестировании хука: {e}")
        return False

def test_nuclear_methods_direct():
    """Прямое тестирование ядерных методов (без админ прав)"""
    safe_print("\n" + "="*80)
    safe_print("☢️ ПРЯМОЕ ТЕСТИРОВАНИЕ ЯДЕРНЫХ МЕТОДОВ")
    safe_print("="*80)
    
    try:
        # Тестируем базовые API вызовы без системных изменений
        user32 = ctypes.windll.user32
        kernel32 = ctypes.windll.kernel32
        
        safe_print("🔧 Тестирование LoadKeyboardLayout...")
        hkl_russian = user32.LoadKeyboardLayoutW("00000419", 0x00000001)
        if hkl_russian:
            safe_print("✅ LoadKeyboardLayout успешен")
            
            safe_print("🔧 Тестирование ActivateKeyboardLayout...")
            result = user32.ActivateKeyboardLayout(hkl_russian, 0x00000008)
            if result:
                safe_print("✅ ActivateKeyboardLayout успешен")
                
                time.sleep(1)
                
                # Проверяем результат
                hwnd = user32.GetForegroundWindow()
                thread_id = user32.GetWindowThreadProcessId(hwnd, None)
                current_hkl = user32.GetKeyboardLayout(thread_id)
                current_id = current_hkl & 0xFFFF
                
                safe_print(f"📋 Результат: раскладка {hex(current_id)}")
                
                if current_id == 0x0419:
                    safe_print("🎉 ЯДЕРНЫЕ МЕТОДЫ СРАБОТАЛИ!")
                    return True
                else:
                    safe_print("💥 Ядерные методы не сработали")
                    return False
            else:
                safe_print("❌ ActivateKeyboardLayout не сработал")
                return False
        else:
            safe_print("❌ LoadKeyboardLayout не сработал")
            return False
            
    except Exception as e:
        safe_print(f"❌ Ошибка при тестировании ядерных методов: {e}")
        return False

def test_driver_simulation():
    """Симуляция драйверного подхода"""
    safe_print("\n" + "="*80)
    safe_print("☢️ СИМУЛЯЦИЯ ДРАЙВЕРНОГО ПОДХОДА")
    safe_print("="*80)
    
    try:
        # Симулируем низкоуровневые вызовы
        user32 = ctypes.windll.user32
        
        safe_print("🔧 Симуляция драйверного переключения...")
        
        # Множественные попытки с разными флагами
        methods = [
            (0x4190419, 0x00000001),  # KLF_ACTIVATE
            (0x4190419, 0x00000008),  # KLF_SETFORPROCESS  
            (0x4190419, 0x00000100),  # KLF_SUBSTITUTE_OK
            (0x4190419, 0x00000109),  # Комбинация флагов
        ]
        
        for i, (layout, flags) in enumerate(methods):
            safe_print(f"   Попытка {i+1}: флаги {hex(flags)}")
            result = user32.ActivateKeyboardLayout(layout, flags)
            if result:
                safe_print(f"   ✅ Попытка {i+1} успешна")
                time.sleep(0.5)
            else:
                safe_print(f"   ❌ Попытка {i+1} не удалась")
        
        # Финальная проверка
        hwnd = user32.GetForegroundWindow()
        thread_id = user32.GetWindowThreadProcessId(hwnd, None)
        current_hkl = user32.GetKeyboardLayout(thread_id)
        current_id = current_hkl & 0xFFFF
        
        safe_print(f"📋 Финальный результат: раскладка {hex(current_id)}")
        
        if current_id == 0x0419:
            safe_print("🎉 ДРАЙВЕРНАЯ СИМУЛЯЦИЯ СРАБОТАЛА!")
            return True
        else:
            safe_print("💥 Драйверная симуляция не сработала")
            return False
            
    except Exception as e:
        safe_print(f"❌ Ошибка при симуляции драйвера: {e}")
        return False

def main():
    """Главная функция тестирования"""
    safe_print("🚀 ПРЯМОЕ ТЕСТИРОВАНИЕ ВСЕХ ЭКСТРЕМАЛЬНЫХ МЕТОДОВ")
    safe_print("="*80)
    safe_print("Этот тест выполняет прямые вызовы методов без внешних процессов")
    safe_print("="*80)
    
    # Начальная проверка
    safe_print("\n📋 НАЧАЛЬНАЯ ПРОВЕРКА:")
    initial_russian = check_current_layout()
    
    # Тестируем все методы
    results = []
    
    results.append(("Экстремальный хук", test_extreme_hook_direct()))
    results.append(("Ядерные методы", test_nuclear_methods_direct()))
    results.append(("Драйверная симуляция", test_driver_simulation()))
    
    # Финальная проверка
    safe_print("\n📋 ФИНАЛЬНАЯ ПРОВЕРКА:")
    final_russian = check_current_layout()
    
    # Результаты
    safe_print("\n" + "="*80)
    safe_print("📊 РЕЗУЛЬТАТЫ ПРЯМОГО ТЕСТИРОВАНИЯ:")
    safe_print("="*80)
    
    for method_name, success in results:
        status = "✅ УСПЕХ" if success else "❌ НЕУДАЧА"
        safe_print(f"{method_name}: {status}")
    
    safe_print(f"\nНачальная раскладка русская: {'✅ ДА' if initial_russian else '❌ НЕТ'}")
    safe_print(f"Финальная раскладка русская: {'✅ ДА' if final_russian else '❌ НЕТ'}")
    
    any_success = any(result[1] for result in results)
    layout_changed = not initial_russian and final_russian
    
    if any_success or layout_changed:
        safe_print("\n🎉 ОДИН ИЗ ЭКСТРЕМАЛЬНЫХ МЕТОДОВ СРАБОТАЛ!")
        safe_print("Проблема переключения раскладки решена!")
    else:
        safe_print("\n💀 ВСЕ ЭКСТРЕМАЛЬНЫЕ МЕТОДЫ НЕ СРАБОТАЛИ")
        safe_print("Это окончательно подтверждает абсолютную защиту Windows")
        safe_print("от программного переключения раскладки клавиатуры.")
    
    safe_print("="*80)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        safe_print("\n🛑 Тестирование прервано пользователем")
    except Exception as e:
        safe_print(f"\n❌ Критическая ошибка: {e}")
    
    input("\nНажмите Enter для выхода...")
