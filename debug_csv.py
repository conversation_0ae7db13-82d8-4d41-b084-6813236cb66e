#!/usr/bin/env python3
"""
CSV File Analyzer - Debug tool to understand CSV structure
"""

import pandas as pd
import os

def analyze_csv(file_path):
    """Analyze CSV file structure and content"""
    print(f"Analyzing CSV file: {file_path}")
    print("=" * 60)
    
    # Check if file exists
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return
    
    # Get file size
    file_size = os.path.getsize(file_path)
    print(f"📁 File size: {file_size:,} bytes")
    
    # Try different encodings and separators
    encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1', 'cp1251']
    separators = [',', ';', '\t', '|']
    
    successful_reads = []
    
    for encoding in encodings:
        for separator in separators:
            try:
                df = pd.read_csv(file_path, header=None, encoding=encoding, 
                               sep=separator, nrows=10)  # Read only first 10 rows for analysis
                
                if df.shape[1] > 1 and df.shape[0] > 0:
                    successful_reads.append({
                        'encoding': encoding,
                        'separator': separator,
                        'shape': df.shape,
                        'columns': df.shape[1],
                        'rows_sample': df.shape[0],
                        'sample_data': df.head(3)
                    })
                    print(f"✅ Success: {encoding} + '{separator}' -> {df.shape[1]} columns, {df.shape[0]} rows")
                    
            except Exception as e:
                continue
    
    if not successful_reads:
        print("❌ Could not read file with any encoding/separator combination")
        return
    
    # Show the best option (most columns)
    best_read = max(successful_reads, key=lambda x: x['columns'])
    print(f"\n🎯 Best option: {best_read['encoding']} encoding with '{best_read['separator']}' separator")
    print(f"📊 Columns: {best_read['columns']}, Sample rows: {best_read['rows_sample']}")
    
    print("\n📋 Sample data (first 3 rows):")
    print("-" * 40)
    for i, row in best_read['sample_data'].iterrows():
        print(f"Row {i+1}: {list(row.values)}")
    
    # Try to read full file with best settings
    try:
        full_df = pd.read_csv(file_path, header=None, 
                             encoding=best_read['encoding'], 
                             sep=best_read['separator'])
        print(f"\n📈 Full file: {full_df.shape[0]} rows, {full_df.shape[1]} columns")
        
        # Show column data types and sample values
        print("\n🔍 Column analysis:")
        for col_idx in range(min(10, full_df.shape[1])):  # Show first 10 columns
            col_data = full_df.iloc[:, col_idx]
            non_null_count = col_data.count()
            sample_values = col_data.dropna().head(3).tolist()
            print(f"  Column {col_idx+1}: {non_null_count}/{len(col_data)} non-null, samples: {sample_values}")
            
    except Exception as e:
        print(f"❌ Error reading full file: {e}")

if __name__ == "__main__":
    # Test with the file path from the screenshot
    file_path = r"C:\Users\<USER>\Desktop\1_20220508.csv"
    analyze_csv(file_path)

    # Also try alternative paths
    alternative_paths = [
        r"C:\Users\<USER>\Desktop\1_20230508.csv",
        r"1_20220508.csv",
        r"1_20230508.csv"
    ]

    for alt_path in alternative_paths:
        if os.path.exists(alt_path):
            print(f"\n" + "="*60)
            analyze_csv(alt_path)
