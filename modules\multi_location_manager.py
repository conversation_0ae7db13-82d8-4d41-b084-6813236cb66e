"""
Multi-Location Restaurant Management System
Supports multiple restaurant locations with centralized management and location-specific reporting
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from datetime import datetime, timedelta
import sqlite3
from gui.styles import ModernStyles, EnhancedStyles
from database.db_manager import DatabaseManager

def format_currency(amount):
    """Форматировать сумму в российском формате валюты"""
    try:
        if amount is None:
            amount = 0
        amount = float(amount)
        formatted = f"{amount:,.2f}".replace(',', ' ').replace('.', ',')
        return f"{formatted} руб"
    except:
        return "0,00 руб"

class MultiLocationManager:
    """Comprehensive multi-location restaurant management system"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.locations = []
        self.current_location = None
        self.location_tree = None
        
        # Initialize location-related database tables
        self._init_location_tables()
        
    def _init_location_tables(self):
        """Initialize multi-location database tables"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Restaurant locations table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS restaurant_locations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        location_code TEXT UNIQUE NOT NULL,
                        location_name TEXT NOT NULL,
                        address TEXT,
                        city TEXT,
                        state TEXT,
                        zip_code TEXT,
                        country TEXT DEFAULT 'Россия',
                        phone TEXT,
                        email TEXT,
                        manager_name TEXT,
                        opening_hours TEXT,
                        seating_capacity INTEGER DEFAULT 0,
                        is_active BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Add location_id to existing tables (we'll do this gradually)
                # First, check if location_id column exists in sales table
                cursor.execute("PRAGMA table_info(sales)")
                columns = [column[1] for column in cursor.fetchall()]
                
                if 'location_id' not in columns:
                    cursor.execute('ALTER TABLE sales ADD COLUMN location_id INTEGER REFERENCES restaurant_locations(id)')
                
                # Check employees table
                cursor.execute("PRAGMA table_info(employees)")
                columns = [column[1] for column in cursor.fetchall()]
                
                if 'location_id' not in columns:
                    cursor.execute('ALTER TABLE employees ADD COLUMN location_id INTEGER REFERENCES restaurant_locations(id)')
                
                # Check raw_materials table
                cursor.execute("PRAGMA table_info(raw_materials)")
                columns = [column[1] for column in cursor.fetchall()]
                
                if 'location_id' not in columns:
                    cursor.execute('ALTER TABLE raw_materials ADD COLUMN location_id INTEGER REFERENCES restaurant_locations(id)')
                
                # Location-specific settings table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS location_settings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        location_id INTEGER NOT NULL,
                        setting_key TEXT NOT NULL,
                        setting_value TEXT,
                        setting_type TEXT DEFAULT 'string', -- string, number, boolean, json
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (location_id) REFERENCES restaurant_locations (id),
                        UNIQUE(location_id, setting_key)
                    )
                ''')
                
                # Location performance metrics table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS location_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        location_id INTEGER NOT NULL,
                        metric_date DATE NOT NULL,
                        total_sales REAL DEFAULT 0,
                        total_orders INTEGER DEFAULT 0,
                        average_order_value REAL DEFAULT 0,
                        customer_count INTEGER DEFAULT 0,
                        labor_cost REAL DEFAULT 0,
                        food_cost REAL DEFAULT 0,
                        profit_margin REAL DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (location_id) REFERENCES restaurant_locations (id),
                        UNIQUE(location_id, metric_date)
                    )
                ''')
                
                conn.commit()
                print("✅ Multi-location tables initialized")
                
                # Create default location if none exists
                self._create_default_location()
                
        except Exception as e:
            print(f"Error initializing location tables: {e}")
    
    def _create_default_location(self):
        """Create default location if none exists"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT COUNT(*) as count FROM restaurant_locations')
                count = cursor.fetchone()['count']
                
                if count == 0:
                    cursor.execute('''
                        INSERT INTO restaurant_locations 
                        (location_code, location_name, address, city, manager_name, is_active)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', ('MAIN', 'Главный Ресторан', 'ул. Центральная, 1', 'Москва', 'Администратор', 1))
                    conn.commit()
                    print("✅ Default location created")
                    
        except Exception as e:
            print(f"Error creating default location: {e}")
    
    def show_location_manager(self):
        """Display the main location management interface"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
            
        self.window = tk.Toplevel(self.parent)
        self.window.title("🏢 Управление Локациями Ресторанов")
        self.window.geometry("1400x900")
        self.window.configure(bg=ModernStyles.COLORS['bg_main'])
        self.window.state('zoomed')
        
        # Apply professional styling (removed non-existent method)
        
        self.create_location_interface()
        self.load_locations()
    
    def create_location_interface(self):
        """Create the main location management interface"""
        # Main container
        main_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Header
        self.create_header(main_frame)
        
        # Content area with locations list and details
        content_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_main'])
        content_frame.pack(fill='both', expand=True, pady=(20, 0))
        
        # Left panel - Locations list
        self.create_locations_panel(content_frame)
        
        # Right panel - Location details and analytics
        self.create_details_panel(content_frame)
    
    def create_header(self, parent):
        """Create header with title and main controls"""
        header_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        header_frame.pack(fill='x', pady=(0, 20))
        
        # Title
        title_label = tk.Label(header_frame, 
                              text="🏢 Управление Локациями Ресторанов",
                              font=('Cambria', 24, 'bold italic'),
                              fg=ModernStyles.COLORS['primary'],
                              bg=ModernStyles.COLORS['bg_main'])
        title_label.pack(side='left')
        
        # Control buttons
        controls_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['bg_main'])
        controls_frame.pack(side='right')
        
        # Add location button
        add_btn = tk.Button(controls_frame, text="➕ Добавить Локацию",
                           command=self.add_location,
                           bg=ModernStyles.COLORS['success'],
                           fg='white',
                           font=('Cambria', 12, 'bold italic'),
                           relief='flat', bd=0, padx=20, pady=8)
        add_btn.pack(side='left', padx=(0, 10))
        
        # Consolidated report button
        report_btn = tk.Button(controls_frame, text="📊 Сводный Отчёт",
                              command=self.show_consolidated_report,
                              bg=ModernStyles.COLORS['primary'],
                              fg='white',
                              font=('Cambria', 12, 'bold italic'),
                              relief='flat', bd=0, padx=20, pady=8)
        report_btn.pack(side='left', padx=(0, 10))
        
        # Settings button
        settings_btn = tk.Button(controls_frame, text="⚙️ Настройки",
                                command=self.show_location_settings,
                                bg=ModernStyles.COLORS['secondary'],
                                fg='white',
                                font=('Cambria', 12, 'bold italic'),
                                relief='flat', bd=0, padx=20, pady=8)
        settings_btn.pack(side='left')
    
    def create_locations_panel(self, parent):
        """Create left panel with locations list"""
        # Locations panel
        locations_frame = tk.LabelFrame(parent, text="📍 Локации Ресторанов",
                                       font=('Cambria', 14, 'bold italic'),
                                       fg=ModernStyles.COLORS['primary'],
                                       bg=ModernStyles.COLORS['bg_main'])
        locations_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        # Locations treeview
        columns = ['code', 'name', 'city', 'manager', 'status', 'sales_today']
        
        self.location_tree = ttk.Treeview(locations_frame, columns=columns, show='headings', height=20)
        
        # Configure column headings
        self.location_tree.heading('code', text='Код')
        self.location_tree.heading('name', text='Название')
        self.location_tree.heading('city', text='Город')
        self.location_tree.heading('manager', text='Менеджер')
        self.location_tree.heading('status', text='Статус')
        self.location_tree.heading('sales_today', text='Продажи Сегодня')
        
        # Configure column widths
        self.location_tree.column('code', width=80, minwidth=60)
        self.location_tree.column('name', width=200, minwidth=150)
        self.location_tree.column('city', width=120, minwidth=100)
        self.location_tree.column('manager', width=150, minwidth=120)
        self.location_tree.column('status', width=100, minwidth=80)
        self.location_tree.column('sales_today', width=150, minwidth=120)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(locations_frame, orient='vertical', command=self.location_tree.yview)
        self.location_tree.configure(yscrollcommand=v_scrollbar.set)
        
        # Pack treeview and scrollbar
        self.location_tree.pack(side='left', fill='both', expand=True, padx=(10, 0), pady=10)
        v_scrollbar.pack(side='right', fill='y', pady=10)
        
        # Bind selection event
        self.location_tree.bind('<<TreeviewSelect>>', self.on_location_select)
        
        # Context menu
        self.create_location_context_menu()

    def create_location_context_menu(self):
        """Create context menu for location operations"""
        self.location_context_menu = tk.Menu(self.window, tearoff=0)
        self.location_context_menu.add_command(label="✏️ Редактировать", command=self.edit_location)
        self.location_context_menu.add_command(label="📊 Аналитика", command=self.show_location_analytics)
        self.location_context_menu.add_command(label="⚙️ Настройки", command=self.show_location_settings)
        self.location_context_menu.add_separator()
        self.location_context_menu.add_command(label="🔄 Синхронизация", command=self.sync_location_data)
        self.location_context_menu.add_command(label="📋 Копировать Настройки", command=self.copy_location_settings)
        self.location_context_menu.add_separator()
        self.location_context_menu.add_command(label="❌ Деактивировать", command=self.deactivate_location)

        # Bind right-click to show context menu
        self.location_tree.bind('<Button-3>', self.show_location_context_menu)

    def show_location_context_menu(self, event):
        """Show context menu at cursor position"""
        try:
            self.location_context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.location_context_menu.grab_release()

    def create_details_panel(self, parent):
        """Create right panel with location details and analytics"""
        # Details panel
        details_frame = tk.LabelFrame(parent, text="📈 Детали и Аналитика Локации",
                                     font=('Cambria', 14, 'bold italic'),
                                     fg=ModernStyles.COLORS['primary'],
                                     bg=ModernStyles.COLORS['bg_main'])
        details_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))

        # Location info section
        info_frame = tk.Frame(details_frame, bg=ModernStyles.COLORS['bg_main'])
        info_frame.pack(fill='x', padx=10, pady=10)

        # Location name and status
        self.location_name_label = tk.Label(info_frame, text="Выберите локацию",
                                           font=('Cambria', 18, 'bold italic'),
                                           fg=ModernStyles.COLORS['primary'],
                                           bg=ModernStyles.COLORS['bg_main'])
        self.location_name_label.pack(anchor='w')

        self.location_status_label = tk.Label(info_frame, text="",
                                             font=('Cambria', 12),
                                             fg=ModernStyles.COLORS['text_primary'],
                                             bg=ModernStyles.COLORS['bg_main'])
        self.location_status_label.pack(anchor='w', pady=(5, 15))

        # Performance metrics
        metrics_frame = tk.LabelFrame(details_frame, text="📊 Показатели Эффективности",
                                     font=('Cambria', 12, 'bold italic'),
                                     fg=ModernStyles.COLORS['primary'],
                                     bg=ModernStyles.COLORS['bg_main'])
        metrics_frame.pack(fill='x', padx=10, pady=(0, 10))

        # Create metrics grid
        metrics_grid = tk.Frame(metrics_frame, bg=ModernStyles.COLORS['bg_main'])
        metrics_grid.pack(fill='x', padx=10, pady=10)

        # Today's metrics
        today_frame = tk.Frame(metrics_grid, bg=ModernStyles.COLORS['bg_main'])
        today_frame.pack(fill='x', pady=(0, 10))

        tk.Label(today_frame, text="Сегодня:",
                font=('Cambria', 12, 'bold italic'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_main']).pack(anchor='w')

        self.today_sales_label = tk.Label(today_frame, text="Продажи: 0,00 руб",
                                         font=('Cambria', 11),
                                         fg=ModernStyles.COLORS['text_primary'],
                                         bg=ModernStyles.COLORS['bg_main'])
        self.today_sales_label.pack(anchor='w', padx=(20, 0))

        self.today_orders_label = tk.Label(today_frame, text="Заказы: 0",
                                          font=('Cambria', 11),
                                          fg=ModernStyles.COLORS['text_primary'],
                                          bg=ModernStyles.COLORS['bg_main'])
        self.today_orders_label.pack(anchor='w', padx=(20, 0))

        # This month metrics
        month_frame = tk.Frame(metrics_grid, bg=ModernStyles.COLORS['bg_main'])
        month_frame.pack(fill='x', pady=(0, 10))

        tk.Label(month_frame, text="Этот месяц:",
                font=('Cambria', 12, 'bold italic'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_main']).pack(anchor='w')

        self.month_sales_label = tk.Label(month_frame, text="Продажи: 0,00 руб",
                                         font=('Cambria', 11),
                                         fg=ModernStyles.COLORS['text_primary'],
                                         bg=ModernStyles.COLORS['bg_main'])
        self.month_sales_label.pack(anchor='w', padx=(20, 0))

        self.month_avg_label = tk.Label(month_frame, text="Средний чек: 0,00 руб",
                                       font=('Cambria', 11),
                                       fg=ModernStyles.COLORS['text_primary'],
                                       bg=ModernStyles.COLORS['bg_main'])
        self.month_avg_label.pack(anchor='w', padx=(20, 0))

        # Quick actions
        actions_frame = tk.LabelFrame(details_frame, text="⚡ Быстрые Действия",
                                     font=('Cambria', 12, 'bold italic'),
                                     fg=ModernStyles.COLORS['primary'],
                                     bg=ModernStyles.COLORS['bg_main'])
        actions_frame.pack(fill='x', padx=10, pady=(0, 10))

        actions_grid = tk.Frame(actions_frame, bg=ModernStyles.COLORS['bg_main'])
        actions_grid.pack(fill='x', padx=10, pady=10)

        # Action buttons
        edit_btn = tk.Button(actions_grid, text="✏️ Редактировать",
                            command=self.edit_location,
                            bg=ModernStyles.COLORS['primary'],
                            fg='white',
                            font=('Cambria', 11, 'bold italic'),
                            relief='flat', bd=0, padx=15, pady=5)
        edit_btn.pack(fill='x', pady=(0, 5))

        analytics_btn = tk.Button(actions_grid, text="📊 Подробная Аналитика",
                                 command=self.show_location_analytics,
                                 bg=ModernStyles.COLORS['accent'],
                                 fg='white',
                                 font=('Cambria', 11, 'bold italic'),
                                 relief='flat', bd=0, padx=15, pady=5)
        analytics_btn.pack(fill='x', pady=(0, 5))

        sync_btn = tk.Button(actions_grid, text="🔄 Синхронизация Данных",
                            command=self.sync_location_data,
                            bg=ModernStyles.COLORS['warning'],
                            fg='white',
                            font=('Cambria', 11, 'bold italic'),
                            relief='flat', bd=0, padx=15, pady=5)
        sync_btn.pack(fill='x')

    def load_locations(self):
        """Load locations from database"""
        try:
            # Clear existing items
            for item in self.location_tree.get_children():
                self.location_tree.delete(item)

            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT rl.*,
                           COALESCE(SUM(CASE WHEN s.order_date = date('now') THEN s.total_amount ELSE 0 END), 0) as today_sales
                    FROM restaurant_locations rl
                    LEFT JOIN sales s ON rl.id = s.location_id
                    WHERE rl.is_active = 1
                    GROUP BY rl.id
                    ORDER BY rl.location_name
                ''')

                self.locations = []
                for row in cursor.fetchall():
                    location = dict(row)
                    self.locations.append(location)

                    # Determine status
                    status = "🟢 Активен" if location['is_active'] else "🔴 Неактивен"

                    # Add to treeview
                    self.location_tree.insert('', 'end', values=[
                        location['location_code'],
                        location['location_name'],
                        location['city'] or 'Не указан',
                        location['manager_name'] or 'Не назначен',
                        status,
                        format_currency(location['today_sales'])
                    ], tags=(location['id'],))

        except Exception as e:
            print(f"Error loading locations: {e}")
            messagebox.showerror("Ошибка", f"Не удалось загрузить список локаций: {e}")

    def on_location_select(self, event):
        """Handle location selection"""
        selection = self.location_tree.selection()
        if not selection:
            return

        # Get selected location
        item = self.location_tree.item(selection[0])
        location_id = item['tags'][0] if item['tags'] else None

        if location_id:
            self.current_location = next((loc for loc in self.locations if loc['id'] == location_id), None)
            self.update_location_details()

    def update_location_details(self):
        """Update location details panel"""
        if not self.current_location:
            return

        # Update location info
        self.location_name_label.config(text=self.current_location['location_name'])

        status_text = f"📍 {self.current_location['address']}, {self.current_location['city']}"
        if self.current_location['manager_name']:
            status_text += f" | 👤 {self.current_location['manager_name']}"
        self.location_status_label.config(text=status_text)

        # Load and update metrics
        self.load_location_metrics()

    def load_location_metrics(self):
        """Load metrics for selected location"""
        if not self.current_location:
            return

        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                # Today's metrics
                cursor.execute('''
                    SELECT
                        COALESCE(SUM(total_amount), 0) as today_sales,
                        COUNT(*) as today_orders
                    FROM sales
                    WHERE location_id = ? AND order_date = date('now')
                ''', (self.current_location['id'],))

                today_data = cursor.fetchone()

                # This month's metrics
                cursor.execute('''
                    SELECT
                        COALESCE(SUM(total_amount), 0) as month_sales,
                        COALESCE(AVG(total_amount), 0) as avg_order_value,
                        COUNT(*) as month_orders
                    FROM sales
                    WHERE location_id = ?
                    AND strftime('%Y-%m', order_date) = strftime('%Y-%m', 'now')
                ''', (self.current_location['id'],))

                month_data = cursor.fetchone()

                # Update labels
                self.today_sales_label.config(text=f"Продажи: {format_currency(today_data['today_sales'])}")
                self.today_orders_label.config(text=f"Заказы: {today_data['today_orders']}")
                self.month_sales_label.config(text=f"Продажи: {format_currency(month_data['month_sales'])}")
                self.month_avg_label.config(text=f"Средний чек: {format_currency(month_data['avg_order_value'])}")

        except Exception as e:
            print(f"Error loading location metrics: {e}")

    def add_location(self):
        """Add new location"""
        self.show_location_dialog()

    def edit_location(self):
        """Edit selected location"""
        if not self.current_location:
            messagebox.showwarning("Предупреждение", "Выберите локацию для редактирования")
            return

        self.show_location_dialog(self.current_location)

    def show_location_dialog(self, location=None):
        """Show dialog for adding/editing locations"""
        dialog = tk.Toplevel(self.window)
        dialog.title("➕ Добавить/Редактировать Локацию" if not location else "✏️ Редактировать Локацию")
        dialog.geometry("600x700")
        dialog.configure(bg=ModernStyles.COLORS['bg_main'])
        dialog.transient(self.window)
        dialog.grab_set()

        # Center dialog
        dialog.geometry("+%d+%d" % (self.window.winfo_rootx() + 100, self.window.winfo_rooty() + 50))

        # Main frame
        main_frame = tk.Frame(dialog, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Title
        title_text = "➕ Новая Локация" if not location else f"✏️ Редактирование: {location['location_name']}"
        title_label = tk.Label(main_frame, text=title_text,
                              font=('Cambria', 18, 'bold italic'),
                              fg=ModernStyles.COLORS['primary'],
                              bg=ModernStyles.COLORS['bg_main'])
        title_label.pack(pady=(0, 20))

        # Form fields
        fields_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_main'])
        fields_frame.pack(fill='both', expand=True)

        # Location code
        tk.Label(fields_frame, text="Код локации:",
                font=('Cambria', 12, 'bold italic'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_main']).pack(anchor='w')

        code_var = tk.StringVar(value=location['location_code'] if location else '')
        code_entry = tk.Entry(fields_frame, textvariable=code_var,
                             font=('Cambria', 12), width=50)
        code_entry.pack(fill='x', pady=(5, 15))

        # Location name
        tk.Label(fields_frame, text="Название:",
                font=('Cambria', 12, 'bold italic'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_main']).pack(anchor='w')

        name_var = tk.StringVar(value=location['location_name'] if location else '')
        name_entry = tk.Entry(fields_frame, textvariable=name_var,
                             font=('Cambria', 12), width=50)
        name_entry.pack(fill='x', pady=(5, 15))

        # Address
        tk.Label(fields_frame, text="Адрес:",
                font=('Cambria', 12, 'bold italic'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_main']).pack(anchor='w')

        address_var = tk.StringVar(value=location['address'] if location else '')
        address_entry = tk.Entry(fields_frame, textvariable=address_var,
                                font=('Cambria', 12), width=50)
        address_entry.pack(fill='x', pady=(5, 15))

        # City and contact info in a grid
        contact_frame = tk.Frame(fields_frame, bg=ModernStyles.COLORS['bg_main'])
        contact_frame.pack(fill='x', pady=(0, 15))

        # City
        city_frame = tk.Frame(contact_frame, bg=ModernStyles.COLORS['bg_main'])
        city_frame.pack(side='left', fill='x', expand=True, padx=(0, 10))

        tk.Label(city_frame, text="Город:",
                font=('Cambria', 12, 'bold italic'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_main']).pack(anchor='w')

        city_var = tk.StringVar(value=location['city'] if location else '')
        city_entry = tk.Entry(city_frame, textvariable=city_var,
                             font=('Cambria', 12))
        city_entry.pack(fill='x', pady=(5, 0))

        # Phone
        phone_frame = tk.Frame(contact_frame, bg=ModernStyles.COLORS['bg_main'])
        phone_frame.pack(side='right', fill='x', expand=True, padx=(10, 0))

        tk.Label(phone_frame, text="Телефон:",
                font=('Cambria', 12, 'bold italic'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_main']).pack(anchor='w')

        phone_var = tk.StringVar(value=location['phone'] if location else '')
        phone_entry = tk.Entry(phone_frame, textvariable=phone_var,
                              font=('Cambria', 12))
        phone_entry.pack(fill='x', pady=(5, 0))

        # Manager name
        tk.Label(fields_frame, text="Менеджер:",
                font=('Cambria', 12, 'bold italic'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_main']).pack(anchor='w')

        manager_var = tk.StringVar(value=location['manager_name'] if location else '')
        manager_entry = tk.Entry(fields_frame, textvariable=manager_var,
                                font=('Cambria', 12), width=50)
        manager_entry.pack(fill='x', pady=(5, 15))

        # Seating capacity
        tk.Label(fields_frame, text="Количество мест:",
                font=('Cambria', 12, 'bold italic'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_main']).pack(anchor='w')

        capacity_var = tk.StringVar(value=str(location['seating_capacity']) if location else '0')
        capacity_entry = tk.Entry(fields_frame, textvariable=capacity_var,
                                 font=('Cambria', 12), width=50)
        capacity_entry.pack(fill='x', pady=(5, 20))

        # Buttons
        button_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_main'])
        button_frame.pack(fill='x')

        def save_location():
            """Save location to database"""
            try:
                with self.db_manager.get_connection() as conn:
                    cursor = conn.cursor()

                    if location:  # Edit existing
                        cursor.execute('''
                            UPDATE restaurant_locations
                            SET location_code=?, location_name=?, address=?, city=?,
                                phone=?, manager_name=?, seating_capacity=?
                            WHERE id=?
                        ''', (
                            code_var.get(),
                            name_var.get(),
                            address_var.get(),
                            city_var.get(),
                            phone_var.get(),
                            manager_var.get(),
                            int(capacity_var.get()) if capacity_var.get().isdigit() else 0,
                            location['id']
                        ))
                    else:  # Add new
                        cursor.execute('''
                            INSERT INTO restaurant_locations
                            (location_code, location_name, address, city, phone, manager_name, seating_capacity)
                            VALUES (?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            code_var.get(),
                            name_var.get(),
                            address_var.get(),
                            city_var.get(),
                            phone_var.get(),
                            manager_var.get(),
                            int(capacity_var.get()) if capacity_var.get().isdigit() else 0
                        ))

                    conn.commit()

                messagebox.showinfo("Успех", "Локация успешно сохранена")
                dialog.destroy()
                self.load_locations()

            except Exception as e:
                messagebox.showerror("Ошибка", f"Не удалось сохранить локацию: {e}")

        save_btn = tk.Button(button_frame, text="💾 Сохранить",
                            command=save_location,
                            bg=ModernStyles.COLORS['success'],
                            fg='white',
                            font=('Cambria', 12, 'bold italic'),
                            relief='flat', bd=0, padx=20, pady=8)
        save_btn.pack(side='left', padx=(0, 10))

        cancel_btn = tk.Button(button_frame, text="❌ Отмена",
                              command=dialog.destroy,
                              bg=ModernStyles.COLORS['danger'],
                              fg='white',
                              font=('Cambria', 12, 'bold italic'),
                              relief='flat', bd=0, padx=20, pady=8)
        cancel_btn.pack(side='right')

    # Additional functionality methods
    def show_location_analytics(self):
        """Show detailed analytics for selected location"""
        if not self.current_location:
            messagebox.showwarning("Предупреждение", "Выберите локацию для просмотра аналитики")
            return

        analytics_window = tk.Toplevel(self.window)
        analytics_window.title(f"📊 Аналитика: {self.current_location['location_name']}")
        analytics_window.geometry("1200x800")
        analytics_window.configure(bg=ModernStyles.COLORS['bg_main'])
        analytics_window.transient(self.window)

        tk.Label(analytics_window, text=f"📊 Аналитика: {self.current_location['location_name']}",
                font=('Cambria', 20, 'bold italic'),
                fg=ModernStyles.COLORS['primary'],
                bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

        tk.Label(analytics_window, text="Подробная аналитика в разработке...",
                font=('Cambria', 14),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_main']).pack()

    def show_consolidated_report(self):
        """Show consolidated report for all locations"""
        report_window = tk.Toplevel(self.window)
        report_window.title("📊 Сводный Отчёт по Всем Локациям")
        report_window.geometry("1400x900")
        report_window.configure(bg=ModernStyles.COLORS['bg_main'])
        report_window.transient(self.window)
        report_window.state('zoomed')

        # Header
        header_frame = tk.Frame(report_window, bg=ModernStyles.COLORS['bg_main'])
        header_frame.pack(fill='x', padx=20, pady=20)

        tk.Label(header_frame, text="📊 Сводный Отчёт по Всем Локациям",
                font=('Cambria', 24, 'bold italic'),
                fg=ModernStyles.COLORS['primary'],
                bg=ModernStyles.COLORS['bg_main']).pack()

        # Summary table
        summary_frame = tk.Frame(report_window, bg=ModernStyles.COLORS['bg_main'])
        summary_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        # Create summary treeview
        columns = ['location', 'today_sales', 'month_sales', 'avg_order', 'total_orders', 'performance']

        summary_tree = ttk.Treeview(summary_frame, columns=columns, show='headings', height=15)

        # Configure headings
        summary_tree.heading('location', text='Локация')
        summary_tree.heading('today_sales', text='Продажи Сегодня')
        summary_tree.heading('month_sales', text='Продажи за Месяц')
        summary_tree.heading('avg_order', text='Средний Чек')
        summary_tree.heading('total_orders', text='Всего Заказов')
        summary_tree.heading('performance', text='Эффективность')

        # Configure column widths
        summary_tree.column('location', width=200, minwidth=150)
        summary_tree.column('today_sales', width=150, minwidth=120)
        summary_tree.column('month_sales', width=150, minwidth=120)
        summary_tree.column('avg_order', width=120, minwidth=100)
        summary_tree.column('total_orders', width=120, minwidth=100)
        summary_tree.column('performance', width=120, minwidth=100)

        # Load consolidated data
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT
                        rl.location_name,
                        COALESCE(SUM(CASE WHEN s.order_date = date('now') THEN s.total_amount ELSE 0 END), 0) as today_sales,
                        COALESCE(SUM(CASE WHEN strftime('%Y-%m', s.order_date) = strftime('%Y-%m', 'now') THEN s.total_amount ELSE 0 END), 0) as month_sales,
                        COALESCE(AVG(CASE WHEN strftime('%Y-%m', s.order_date) = strftime('%Y-%m', 'now') THEN s.total_amount ELSE NULL END), 0) as avg_order,
                        COUNT(CASE WHEN strftime('%Y-%m', s.order_date) = strftime('%Y-%m', 'now') THEN 1 ELSE NULL END) as total_orders
                    FROM restaurant_locations rl
                    LEFT JOIN sales s ON rl.id = s.location_id
                    WHERE rl.is_active = 1
                    GROUP BY rl.id, rl.location_name
                    ORDER BY month_sales DESC
                ''')

                total_today = 0
                total_month = 0

                for row in cursor.fetchall():
                    today_sales = row['today_sales']
                    month_sales = row['month_sales']
                    avg_order = row['avg_order']
                    total_orders = row['total_orders']

                    total_today += today_sales
                    total_month += month_sales

                    # Calculate performance indicator
                    if month_sales > 100000:
                        performance = "🟢 Отлично"
                    elif month_sales > 50000:
                        performance = "🟡 Хорошо"
                    else:
                        performance = "🔴 Требует внимания"

                    summary_tree.insert('', 'end', values=[
                        row['location_name'],
                        format_currency(today_sales),
                        format_currency(month_sales),
                        format_currency(avg_order),
                        total_orders,
                        performance
                    ])

                # Add totals row
                summary_tree.insert('', 'end', values=[
                    "ИТОГО:",
                    format_currency(total_today),
                    format_currency(total_month),
                    "-",
                    "-",
                    "-"
                ], tags=('total',))

        except Exception as e:
            print(f"Error loading consolidated report: {e}")

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(summary_frame, orient='vertical', command=summary_tree.yview)
        summary_tree.configure(yscrollcommand=v_scrollbar.set)

        summary_tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')

    def show_location_settings(self):
        """Show location-specific settings"""
        if not self.current_location:
            messagebox.showwarning("Предупреждение", "Выберите локацию для настройки")
            return

        settings_window = tk.Toplevel(self.window)
        settings_window.title(f"⚙️ Настройки: {self.current_location['location_name']}")
        settings_window.geometry("800x600")
        settings_window.configure(bg=ModernStyles.COLORS['bg_main'])
        settings_window.transient(self.window)

        tk.Label(settings_window, text=f"⚙️ Настройки: {self.current_location['location_name']}",
                font=('Cambria', 20, 'bold italic'),
                fg=ModernStyles.COLORS['primary'],
                bg=ModernStyles.COLORS['bg_main']).pack(pady=20)

        tk.Label(settings_window, text="Настройки локации в разработке...",
                font=('Cambria', 14),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_main']).pack()

    def sync_location_data(self):
        """Synchronize location data"""
        if not self.current_location:
            messagebox.showwarning("Предупреждение", "Выберите локацию для синхронизации")
            return

        messagebox.showinfo("Синхронизация", f"Синхронизация данных для {self.current_location['location_name']} завершена")
        self.load_locations()
        self.load_location_metrics()

    def copy_location_settings(self):
        """Copy settings from one location to another"""
        if not self.current_location:
            messagebox.showwarning("Предупреждение", "Выберите локацию для копирования настроек")
            return

        messagebox.showinfo("Копирование настроек", "Функция копирования настроек в разработке")

    def deactivate_location(self):
        """Deactivate selected location"""
        if not self.current_location:
            messagebox.showwarning("Предупреждение", "Выберите локацию для деактивации")
            return

        if messagebox.askyesno("Подтверждение",
                              f"Вы уверены, что хотите деактивировать локацию '{self.current_location['location_name']}'?"):
            try:
                with self.db_manager.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute('UPDATE restaurant_locations SET is_active = 0 WHERE id = ?',
                                 (self.current_location['id'],))
                    conn.commit()

                messagebox.showinfo("Успех", "Локация деактивирована")
                self.load_locations()

            except Exception as e:
                messagebox.showerror("Ошибка", f"Не удалось деактивировать локацию: {e}")

def create_multi_location_manager(parent, db_manager):
    """Create and show the multi-location management system"""
    try:
        location_manager = MultiLocationManager(parent, db_manager)
        location_manager.show_location_manager()
        return location_manager
    except Exception as e:
        messagebox.showerror("Ошибка", f"Не удалось открыть систему управления локациями: {e}")
        return None
