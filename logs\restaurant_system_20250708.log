2025-07-08 19:40:24,673 - INFO - logger - Database initialized: Кафе "Библос"
2025-07-08 19:40:24,675 - INFO - logger - Backup system initialized
2025-07-08 19:40:33,649 - INFO - logger - LOGIN SUCCESS: admin from localhost | {"username": "admin", "success": true, "ip_address": "localhost", "timestamp": "2025-07-08T19:40:33.649655"}
2025-07-08 19:40:33,706 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-08 19:40:33,707 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-08 19:40:33,707 - ERROR - connection_pool - Params: None
2025-07-08 19:40:33,707 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-08 19:40:33,708 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-08 19:40:33,708 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-08 19:40:33,708 - ERROR - connection_pool - Params: None
2025-07-08 19:40:33,708 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-08 19:40:33,709 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-08 19:40:33,709 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-08 19:40:33,709 - ERROR - connection_pool - Params: None
2025-07-08 19:40:33,709 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-08 19:40:33,709 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-08 19:40:33,710 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-08 19:40:33,710 - ERROR - connection_pool - Params: None
2025-07-08 19:40:33,710 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-08 19:40:33,710 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-08 19:40:33,710 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-08 19:40:33,710 - ERROR - connection_pool - Params: None
2025-07-08 19:40:33,711 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-08 19:40:33,711 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-08 19:40:33,711 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-08 19:40:33,711 - ERROR - connection_pool - Params: None
2025-07-08 19:40:33,711 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-08 19:40:33,711 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-08 19:40:33,711 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-08 19:40:33,712 - ERROR - connection_pool - Params: None
2025-07-08 19:40:33,712 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-08 19:40:33,712 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-08 19:40:33,712 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-08 19:40:33,712 - ERROR - connection_pool - Params: None
2025-07-08 19:40:33,712 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-08 19:40:33,712 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-08 19:40:33,712 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-08 19:40:33,713 - ERROR - connection_pool - Params: None
2025-07-08 19:40:33,713 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-08 19:40:33,713 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-08 19:40:33,713 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-08 19:40:33,713 - ERROR - connection_pool - Params: None
2025-07-08 19:40:33,713 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-08 19:40:33,714 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-08 19:40:33,714 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-08 19:40:33,714 - ERROR - connection_pool - Params: None
2025-07-08 19:40:33,714 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-08 19:40:33,714 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-08 19:40:33,714 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-08 19:40:33,714 - ERROR - connection_pool - Params: None
2025-07-08 19:40:33,714 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-08 19:40:33,714 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-08 19:40:33,715 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-08 19:40:33,715 - ERROR - connection_pool - Params: None
2025-07-08 19:40:33,715 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-08 19:40:33,715 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-08 19:40:33,715 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-08 19:40:33,715 - ERROR - connection_pool - Params: None
2025-07-08 19:40:33,715 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-08 19:40:33,715 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-08 19:40:33,716 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-08 19:40:33,716 - ERROR - connection_pool - Params: None
2025-07-08 19:40:33,716 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-08 19:40:33,716 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-08 19:40:33,716 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-08 19:40:33,716 - ERROR - connection_pool - Params: None
2025-07-08 19:40:33,716 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-08 19:40:33,717 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-08 19:40:33,717 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-08 19:40:33,717 - ERROR - connection_pool - Params: None
2025-07-08 19:40:33,717 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-08 19:40:33,717 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-08 19:40:33,718 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-08 19:40:33,718 - ERROR - connection_pool - Params: None
2025-07-08 19:40:33,718 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-08 19:40:33,720 - WARNING - sync_manager - Empty schema for table sales
2025-07-08 19:40:33,720 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-08 19:40:33,721 - WARNING - sync_manager - Empty schema for table recipes
2025-07-08 19:40:33,721 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-08 19:40:33,722 - WARNING - sync_manager - Empty schema for table customers
2025-07-08 19:40:33,722 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-08 19:40:33,722 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-08 19:40:33,723 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-08 19:40:33,723 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-08 19:40:33,724 - INFO - sync_manager - Data synchronization service started
2025-07-08 19:40:33,725 - INFO - offline_manager - Offline storage initialized
2025-07-08 19:40:33,726 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-08 19:40:34,166 - INFO - logger - Notification system initialized
2025-07-08 19:40:34,197 - INFO - logger - Application initialized successfully | {"user_id": 1}
2025-07-08 19:40:45,262 - INFO - error_handling - RealTimeDashboard: Система живых обновлений запущена
2025-07-08 19:40:45,265 - INFO - error_handling - RealTimeDashboard: Панель управления в реальном времени создана
2025-07-08 19:40:45,266 - INFO - error_handling - MainWindow: Панель управления в реальном времени открыта
2025-07-08 19:40:52,423 - ERROR - error_handling - Модуль: Расширенная система отчетности, Операция: загрузка расписаний, Ошибка: 'EnhancedReportingSystem' object has no attribute 'schedules'
2025-07-08 19:40:52,444 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\enhanced_reporting_system.py", line 1215, in load_schedules
    for schedule in self.schedules:
                    ^^^^^^^^^^^^^^
AttributeError: 'EnhancedReportingSystem' object has no attribute 'schedules'

2025-07-08 19:40:56,590 - INFO - error_handling - EnhancedReportingSystem: Расширенная система отчетности создана
2025-07-08 19:40:56,590 - INFO - error_handling - EnhancedReportingSystem: Расширенная система отчетности создана
2025-07-08 19:40:56,590 - INFO - error_handling - MainWindow: Расширенная система отчетности открыта
2025-07-08 19:41:02,735 - ERROR - error_handling - Модуль: Мобильная и веб интеграция, Операция: открытие системы, Ошибка: 'bg_primary'
2025-07-08 19:41:02,755 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\enhanced_mobile_web.py", line 1269, in show_enhanced_mobile_web
    mobile_web_system = EnhancedMobileWebSystem(parent, db_manager)
  File "C:\Users\<USER>\Desktop\Restuarant\modules\enhanced_mobile_web.py", line 78, in __init__
    self.create_main_window()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\Restuarant\modules\enhanced_mobile_web.py", line 239, in create_main_window
    self.window.configure(bg=ModernStyles.COLORS['bg_primary'])
                             ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
KeyError: 'bg_primary'

2025-07-08 19:41:09,346 - INFO - error_handling - IntegrationAPIs: Конфигурации интеграций загружены
2025-07-08 19:41:09,347 - INFO - error_handling - IntegrationAPIs: Система интеграции и API создана
2025-07-08 19:41:09,347 - INFO - error_handling - MainWindow: Система интеграции и API открыта
2025-07-08 19:41:17,761 - ERROR - error_handling - Модуль: Безопасность, Операция: инициализация шифрования, Ошибка: log_info() takes 1 positional argument but 2 were given
2025-07-08 19:41:17,782 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_security.py", line 111, in init_encryption
    log_info("Система шифрования инициализирована", "AdvancedSecurity")
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: log_info() takes 1 positional argument but 2 were given

2025-07-08 19:41:20,510 - ERROR - error_handling - Модуль: Безопасность, Операция: инициализация таблиц, Ошибка: 'DatabaseManager' object has no attribute 'connection'
2025-07-08 19:41:20,515 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_security.py", line 119, in init_security_tables
    cursor = self.db_manager.connection.cursor()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DatabaseManager' object has no attribute 'connection'. Did you mean: 'get_connection'?

2025-07-08 19:41:23,308 - ERROR - error_handling - Модуль: Безопасность, Операция: загрузка пользователей, Ошибка: 'DatabaseManager' object has no attribute 'connection'
2025-07-08 19:41:23,309 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_security.py", line 819, in load_users_data
    cursor = self.db_manager.connection.cursor()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DatabaseManager' object has no attribute 'connection'. Did you mean: 'get_connection'?

2025-07-08 19:41:27,313 - ERROR - error_handling - Модуль: Безопасность, Операция: загрузка ролей, Ошибка: 'DatabaseManager' object has no attribute 'connection'
2025-07-08 19:41:27,315 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_security.py", line 857, in load_roles_data
    cursor = self.db_manager.connection.cursor()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DatabaseManager' object has no attribute 'connection'. Did you mean: 'get_connection'?

2025-07-08 19:41:28,136 - ERROR - error_handling - Модуль: Безопасность, Операция: загрузка прав доступа, Ошибка: 'DatabaseManager' object has no attribute 'connection'
2025-07-08 19:41:28,138 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_security.py", line 881, in load_permissions_checkboxes
    cursor = self.db_manager.connection.cursor()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DatabaseManager' object has no attribute 'connection'. Did you mean: 'get_connection'?

2025-07-08 19:41:28,912 - ERROR - error_handling - Модуль: Безопасность, Операция: обновление списка пользователей, Ошибка: 'DatabaseManager' object has no attribute 'connection'
2025-07-08 19:41:28,913 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_security.py", line 1143, in refresh_user_list
    cursor = self.db_manager.connection.cursor()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DatabaseManager' object has no attribute 'connection'. Did you mean: 'get_connection'?

2025-07-08 19:41:29,705 - ERROR - error_handling - Модуль: Безопасность, Операция: загрузка данных аудита, Ошибка: 'DatabaseManager' object has no attribute 'connection'
2025-07-08 19:41:29,708 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_security.py", line 959, in load_audit_data
    cursor = self.db_manager.connection.cursor()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DatabaseManager' object has no attribute 'connection'. Did you mean: 'get_connection'?

2025-07-08 19:41:30,289 - INFO - query_optimizer - Created essential index: idx_sales_order_date
2025-07-08 19:41:30,290 - INFO - query_optimizer - Created essential index: idx_sales_payment_method
2025-07-08 19:41:30,291 - INFO - query_optimizer - Created essential index: idx_sales_department
2025-07-08 19:41:30,292 - INFO - query_optimizer - Created essential index: idx_sales_dish_code
2025-07-08 19:41:30,294 - INFO - query_optimizer - Created essential index: idx_sales_order_number
2025-07-08 19:41:30,295 - INFO - query_optimizer - Created essential index: idx_sales_composite
2025-07-08 19:41:30,296 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-08 19:41:30,297 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory(item_name)
2025-07-08 19:41:30,297 - ERROR - connection_pool - Params: None
2025-07-08 19:41:30,297 - ERROR - query_optimizer - Error creating index idx_inventory_item_name: no such table: main.inventory
2025-07-08 19:41:30,298 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-08 19:41:30,299 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)
2025-07-08 19:41:30,299 - ERROR - connection_pool - Params: None
2025-07-08 19:41:30,299 - ERROR - query_optimizer - Error creating index idx_inventory_category: no such table: main.inventory
2025-07-08 19:41:30,300 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-08 19:41:30,300 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier)
2025-07-08 19:41:30,300 - ERROR - connection_pool - Params: None
2025-07-08 19:41:30,301 - ERROR - query_optimizer - Error creating index idx_inventory_supplier: no such table: main.inventory
2025-07-08 19:41:30,302 - ERROR - connection_pool - Query execution error: no such table: main.inventory
2025-07-08 19:41:30,302 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(current_stock, minimum_stock)
2025-07-08 19:41:30,303 - ERROR - connection_pool - Params: None
2025-07-08 19:41:30,303 - ERROR - query_optimizer - Error creating index idx_inventory_low_stock: no such table: main.inventory
2025-07-08 19:41:30,304 - INFO - query_optimizer - Created essential index: idx_recipes_name
2025-07-08 19:41:30,306 - INFO - query_optimizer - Created essential index: idx_recipes_category
2025-07-08 19:41:30,307 - INFO - query_optimizer - Created essential index: idx_recipe_ingredients_recipe
2025-07-08 19:41:30,309 - ERROR - connection_pool - Query execution error: no such column: ingredient_id
2025-07-08 19:41:30,309 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)
2025-07-08 19:41:30,309 - ERROR - connection_pool - Params: None
2025-07-08 19:41:30,310 - ERROR - query_optimizer - Error creating index idx_recipe_ingredients_ingredient: no such column: ingredient_id
2025-07-08 19:41:30,311 - INFO - query_optimizer - Created essential index: idx_purchase_orders_date
2025-07-08 19:41:30,312 - ERROR - connection_pool - Query execution error: no such column: supplier_id
2025-07-08 19:41:30,312 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)
2025-07-08 19:41:30,312 - ERROR - connection_pool - Params: None
2025-07-08 19:41:30,313 - ERROR - query_optimizer - Error creating index idx_purchase_orders_supplier: no such column: supplier_id
2025-07-08 19:41:30,315 - INFO - query_optimizer - Created essential index: idx_purchase_orders_status
2025-07-08 19:41:30,316 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-08 19:41:30,316 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role)
2025-07-08 19:41:30,316 - ERROR - connection_pool - Params: None
2025-07-08 19:41:30,316 - ERROR - query_optimizer - Error creating index idx_staff_role: no such table: main.staff
2025-07-08 19:41:30,317 - ERROR - connection_pool - Query execution error: no such table: main.staff
2025-07-08 19:41:30,318 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department)
2025-07-08 19:41:30,318 - ERROR - connection_pool - Params: None
2025-07-08 19:41:30,318 - ERROR - query_optimizer - Error creating index idx_staff_department: no such table: main.staff
2025-07-08 19:41:30,319 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-08 19:41:30,320 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_date ON staff_schedules(schedule_date)
2025-07-08 19:41:30,321 - ERROR - connection_pool - Params: None
2025-07-08 19:41:30,321 - ERROR - query_optimizer - Error creating index idx_schedules_date: no such table: main.staff_schedules
2025-07-08 19:41:30,323 - ERROR - connection_pool - Query execution error: no such table: main.staff_schedules
2025-07-08 19:41:30,323 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_schedules_staff ON staff_schedules(staff_id)
2025-07-08 19:41:30,323 - ERROR - connection_pool - Params: None
2025-07-08 19:41:30,323 - ERROR - query_optimizer - Error creating index idx_schedules_staff: no such table: main.staff_schedules
2025-07-08 19:41:30,324 - INFO - query_optimizer - Created essential index: idx_customers_phone
2025-07-08 19:41:30,325 - INFO - query_optimizer - Created essential index: idx_customers_email
2025-07-08 19:41:30,327 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_points
2025-07-08 19:41:30,327 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_points(customer_id)
2025-07-08 19:41:30,327 - ERROR - connection_pool - Params: None
2025-07-08 19:41:30,328 - ERROR - query_optimizer - Error creating index idx_loyalty_customer: no such table: main.loyalty_points
2025-07-08 19:41:30,329 - ERROR - connection_pool - Query execution error: no such table: main.loyalty_transactions
2025-07-08 19:41:30,330 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)
2025-07-08 19:41:30,330 - ERROR - connection_pool - Params: None
2025-07-08 19:41:30,330 - ERROR - query_optimizer - Error creating index idx_loyalty_date: no such table: main.loyalty_transactions
2025-07-08 19:41:30,331 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-08 19:41:30,331 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)
2025-07-08 19:41:30,332 - ERROR - connection_pool - Params: None
2025-07-08 19:41:30,332 - ERROR - query_optimizer - Error creating index idx_expenses_date: no such table: main.expenses
2025-07-08 19:41:30,333 - ERROR - connection_pool - Query execution error: no such table: main.expenses
2025-07-08 19:41:30,333 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)
2025-07-08 19:41:30,333 - ERROR - connection_pool - Params: None
2025-07-08 19:41:30,334 - ERROR - query_optimizer - Error creating index idx_expenses_category: no such table: main.expenses
2025-07-08 19:41:30,334 - ERROR - connection_pool - Query execution error: no such table: main.daily_revenue
2025-07-08 19:41:30,334 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_revenue_date ON daily_revenue(date)
2025-07-08 19:41:30,334 - ERROR - connection_pool - Params: None
2025-07-08 19:41:30,335 - ERROR - query_optimizer - Error creating index idx_revenue_date: no such table: main.daily_revenue
2025-07-08 19:41:30,335 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-08 19:41:30,335 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)
2025-07-08 19:41:30,335 - ERROR - connection_pool - Params: None
2025-07-08 19:41:30,336 - ERROR - query_optimizer - Error creating index idx_audit_timestamp: no such table: main.audit_log
2025-07-08 19:41:30,336 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-08 19:41:30,336 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)
2025-07-08 19:41:30,336 - ERROR - connection_pool - Params: None
2025-07-08 19:41:30,337 - ERROR - query_optimizer - Error creating index idx_audit_user: no such table: main.audit_log
2025-07-08 19:41:30,337 - ERROR - connection_pool - Query execution error: no such table: main.audit_log
2025-07-08 19:41:30,338 - ERROR - connection_pool - Query: CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)
2025-07-08 19:41:30,338 - ERROR - connection_pool - Params: None
2025-07-08 19:41:30,338 - ERROR - query_optimizer - Error creating index idx_audit_action: no such table: main.audit_log
2025-07-08 19:41:30,341 - WARNING - sync_manager - Empty schema for table sales
2025-07-08 19:41:30,341 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-08 19:41:30,342 - WARNING - sync_manager - Empty schema for table recipes
2025-07-08 19:41:30,342 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-08 19:41:30,342 - WARNING - sync_manager - Empty schema for table customers
2025-07-08 19:41:30,343 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-08 19:41:30,343 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-08 19:41:30,343 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-08 19:41:30,344 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-08 19:41:30,344 - INFO - sync_manager - Data synchronization service started
2025-07-08 19:41:30,346 - INFO - offline_manager - Offline storage initialized
2025-07-08 19:41:30,347 - INFO - db_manager - Database initialized successfully with advanced features
2025-07-08 19:41:30,524 - ERROR - error_handling - Модуль: Безопасность, Операция: загрузка данных соответствия, Ошибка: 'DatabaseManager' object has no attribute 'connection'
2025-07-08 19:41:30,526 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_security.py", line 991, in load_compliance_data
    cursor = self.db_manager.connection.cursor()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DatabaseManager' object has no attribute 'connection'. Did you mean: 'get_connection'?

2025-07-08 19:41:31,431 - ERROR - error_handling - Модуль: Безопасность, Операция: открытие главного окна, Ошибка: log_info() takes 1 positional argument but 2 were given
2025-07-08 19:41:31,432 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_security.py", line 339, in show_security_manager
    log_info("Окно расширенной безопасности открыто", "AdvancedSecurity")
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: log_info() takes 1 positional argument but 2 were given

2025-07-08 19:41:32,343 - INFO - error_handling - MainWindow: Расширенная безопасность открыта
2025-07-08 19:41:32,656 - ERROR - error_handling - Модуль: Расширенная Аналитика, Операция: открытие окна, Ошибка: 'bg_primary'
2025-07-08 19:41:32,658 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_analytics.py", line 830, in show_advanced_analytics
    AdvancedAnalyticsWindow(parent, db_manager)
    ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_analytics.py", line 34, in __init__
    self.window.configure(bg=ModernStyles.COLORS['bg_primary'])
                             ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
KeyError: 'bg_primary'

2025-07-08 19:41:37,528 - ERROR - error_handling - Модуль: Резервное копирование, Операция: инициализация базы данных, Ошибка: log_info() takes 1 positional argument but 2 were given
2025-07-08 19:41:37,530 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\automated_backup.py", line 168, in init_backup_database
    log_info("База данных резервного копирования инициализирована", "AutomatedBackup")
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: log_info() takes 1 positional argument but 2 were given

2025-07-08 19:41:39,650 - ERROR - error_handling - Модуль: Резервное копирование, Операция: загрузка конфигурации, Ошибка: log_info() takes 1 positional argument but 2 were given
2025-07-08 19:41:39,651 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\automated_backup.py", line 184, in load_backup_config
    log_info("Конфигурация резервного копирования загружена", "AutomatedBackup")
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: log_info() takes 1 positional argument but 2 were given

2025-07-08 19:41:42,348 - ERROR - error_handling - Модуль: Резервное копирование, Операция: инициализация системы, Ошибка: log_info() takes 1 positional argument but 2 were given
2025-07-08 19:41:42,349 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\automated_backup.py", line 108, in init_backup_system
    log_info("Система резервного копирования инициализирована", "AutomatedBackup")
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: log_info() takes 1 positional argument but 2 were given

2025-07-08 19:41:44,276 - ERROR - error_handling - Модуль: Резервное копирование, Операция: загрузка обзора, Ошибка: log_info() takes 1 positional argument but 2 were given
2025-07-08 19:41:44,277 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\automated_backup.py", line 872, in load_backup_overview
    log_info("Обзор резервного копирования обновлен", "AutomatedBackup")
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: log_info() takes 1 positional argument but 2 were given

2025-07-08 19:41:48,928 - ERROR - error_handling - Модуль: Резервное копирование, Операция: открытие главного окна, Ошибка: log_info() takes 1 positional argument but 2 were given
2025-07-08 19:41:48,929 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\automated_backup.py", line 257, in show_backup_manager
    log_info("Окно резервного копирования открыто", "AutomatedBackup")
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: log_info() takes 1 positional argument but 2 were given

2025-07-08 19:41:50,448 - INFO - error_handling - MainWindow: Автоматическое резервное копирование открыто
2025-07-08 19:42:05,759 - ERROR - error_handling - Модуль: Расширенная Аналитика, Операция: открытие окна, Ошибка: 'bg_primary'
2025-07-08 19:42:05,761 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_analytics.py", line 830, in show_advanced_analytics
    AdvancedAnalyticsWindow(parent, db_manager)
    ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_analytics.py", line 34, in __init__
    self.window.configure(bg=ModernStyles.COLORS['bg_primary'])
                             ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
KeyError: 'bg_primary'

2025-07-08 19:42:16,161 - ERROR - error_handling - Модуль: Управление Безопасностью, Операция: открытие окна, Ошибка: 'bg_primary'
2025-07-08 19:42:16,163 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\security_management.py", line 917, in show_security_management
    SecurityManagementWindow(parent, db_manager)
    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Restuarant\modules\security_management.py", line 31, in __init__
    self.window.configure(bg=ModernStyles.COLORS['bg_primary'])
                             ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
KeyError: 'bg_primary'

2025-07-08 19:42:21,802 - ERROR - error_handling - Модуль: Расширенное управление складом, Операция: открытие модуля, Ошибка: 'bg_primary'
2025-07-08 19:42:21,803 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_inventory.py", line 2025, in show_advanced_inventory
    AdvancedInventoryManager(parent, db_manager)
    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_inventory.py", line 35, in __init__
    self.window.configure(bg=ModernStyles.COLORS['bg_primary'])
                             ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
KeyError: 'bg_primary'

2025-07-08 19:43:06,891 - ERROR - error_handling - Модуль: Расширенное управление складом, Операция: открытие модуля, Ошибка: 'bg_primary'
2025-07-08 19:43:06,892 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_inventory.py", line 2025, in show_advanced_inventory
    AdvancedInventoryManager(parent, db_manager)
    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_inventory.py", line 35, in __init__
    self.window.configure(bg=ModernStyles.COLORS['bg_primary'])
                             ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
KeyError: 'bg_primary'

2025-07-08 19:43:10,081 - ERROR - error_handling - Модуль: Безопасность, Операция: инициализация шифрования, Ошибка: log_info() takes 1 positional argument but 2 were given
2025-07-08 19:43:10,082 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_security.py", line 111, in init_encryption
    log_info("Система шифрования инициализирована", "AdvancedSecurity")
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: log_info() takes 1 positional argument but 2 were given

2025-07-08 19:43:11,499 - ERROR - error_handling - Модуль: Безопасность, Операция: инициализация таблиц, Ошибка: 'DatabaseManager' object has no attribute 'connection'
2025-07-08 19:43:11,500 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_security.py", line 119, in init_security_tables
    cursor = self.db_manager.connection.cursor()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DatabaseManager' object has no attribute 'connection'. Did you mean: 'get_connection'?

2025-07-08 19:43:12,661 - ERROR - error_handling - Модуль: Безопасность, Операция: загрузка пользователей, Ошибка: 'DatabaseManager' object has no attribute 'connection'
2025-07-08 19:43:12,662 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_security.py", line 819, in load_users_data
    cursor = self.db_manager.connection.cursor()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DatabaseManager' object has no attribute 'connection'. Did you mean: 'get_connection'?

2025-07-08 19:43:13,785 - ERROR - error_handling - Модуль: Безопасность, Операция: загрузка ролей, Ошибка: 'DatabaseManager' object has no attribute 'connection'
2025-07-08 19:43:13,787 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_security.py", line 857, in load_roles_data
    cursor = self.db_manager.connection.cursor()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DatabaseManager' object has no attribute 'connection'. Did you mean: 'get_connection'?

2025-07-08 19:43:14,706 - ERROR - error_handling - Модуль: Безопасность, Операция: загрузка прав доступа, Ошибка: 'DatabaseManager' object has no attribute 'connection'
2025-07-08 19:43:14,707 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_security.py", line 881, in load_permissions_checkboxes
    cursor = self.db_manager.connection.cursor()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DatabaseManager' object has no attribute 'connection'. Did you mean: 'get_connection'?

2025-07-08 19:43:15,556 - ERROR - error_handling - Модуль: Безопасность, Операция: обновление списка пользователей, Ошибка: 'DatabaseManager' object has no attribute 'connection'
2025-07-08 19:43:15,557 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_security.py", line 1143, in refresh_user_list
    cursor = self.db_manager.connection.cursor()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DatabaseManager' object has no attribute 'connection'. Did you mean: 'get_connection'?

2025-07-08 19:43:16,384 - ERROR - error_handling - Модуль: Безопасность, Операция: загрузка данных аудита, Ошибка: 'DatabaseManager' object has no attribute 'connection'
2025-07-08 19:43:16,386 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_security.py", line 959, in load_audit_data
    cursor = self.db_manager.connection.cursor()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DatabaseManager' object has no attribute 'connection'. Did you mean: 'get_connection'?

2025-07-08 19:43:17,264 - ERROR - error_handling - Модуль: Безопасность, Операция: загрузка данных соответствия, Ошибка: 'DatabaseManager' object has no attribute 'connection'
2025-07-08 19:43:17,265 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_security.py", line 991, in load_compliance_data
    cursor = self.db_manager.connection.cursor()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DatabaseManager' object has no attribute 'connection'. Did you mean: 'get_connection'?

2025-07-08 19:43:18,081 - ERROR - error_handling - Модуль: Безопасность, Операция: открытие главного окна, Ошибка: log_info() takes 1 positional argument but 2 were given
2025-07-08 19:43:18,082 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\advanced_security.py", line 339, in show_security_manager
    log_info("Окно расширенной безопасности открыто", "AdvancedSecurity")
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: log_info() takes 1 positional argument but 2 were given

2025-07-08 19:43:18,925 - INFO - error_handling - MainWindow: Расширенная безопасность открыта
2025-07-08 19:43:26,655 - ERROR - error_handling - Модуль: ИИ Аналитика, Операция: инициализация базы данных, Ошибка: 'DatabaseManager' object has no attribute 'connection'
2025-07-08 19:43:26,657 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\ai_insights.py", line 94, in init_ai_database
    cursor = self.db_manager.connection.cursor()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DatabaseManager' object has no attribute 'connection'. Did you mean: 'get_connection'?

2025-07-08 19:43:28,332 - ERROR - error_handling - Модуль: ИИ Аналитика, Операция: загрузка исторических данных, Ошибка: 'DatabaseManager' object has no attribute 'connection'
2025-07-08 19:43:28,335 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\ai_insights.py", line 181, in load_historical_data
    ''', self.db_manager.connection)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DatabaseManager' object has no attribute 'connection'. Did you mean: 'get_connection'?

2025-07-08 19:43:29,120 - ERROR - error_handling - Модуль: ИИ Аналитика, Операция: инициализация моделей, Ошибка: log_info() takes 1 positional argument but 2 were given
2025-07-08 19:43:29,120 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\ai_insights.py", line 260, in initialize_models
    log_info("Модели ИИ инициализированы", "AIInsights")
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: log_info() takes 1 positional argument but 2 were given

2025-07-08 19:43:29,892 - ERROR - error_handling - Модуль: ИИ Аналитика, Операция: инициализация системы, Ошибка: log_info() takes 1 positional argument but 2 were given
2025-07-08 19:43:29,892 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\ai_insights.py", line 86, in init_ai_system
    log_info("Система ИИ инициализирована", "AIInsights")
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: log_info() takes 1 positional argument but 2 were given

2025-07-08 19:43:31,054 - ERROR - error_handling - Модуль: ИИ Аналитика, Операция: открытие главного окна, Ошибка: log_info() takes 1 positional argument but 2 were given
2025-07-08 19:43:31,055 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\ai_insights.py", line 375, in show_ai_insights
    log_info("Окно ИИ аналитики открыто", "AIInsights")
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: log_info() takes 1 positional argument but 2 were given

2025-07-08 19:43:32,846 - ERROR - error_handling - Модуль: Резервное копирование, Операция: инициализация базы данных, Ошибка: log_info() takes 1 positional argument but 2 were given
2025-07-08 19:43:32,848 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\automated_backup.py", line 168, in init_backup_database
    log_info("База данных резервного копирования инициализирована", "AutomatedBackup")
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: log_info() takes 1 positional argument but 2 were given

2025-07-08 19:43:33,738 - ERROR - error_handling - Модуль: Резервное копирование, Операция: загрузка конфигурации, Ошибка: log_info() takes 1 positional argument but 2 were given
2025-07-08 19:43:33,739 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\automated_backup.py", line 184, in load_backup_config
    log_info("Конфигурация резервного копирования загружена", "AutomatedBackup")
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: log_info() takes 1 positional argument but 2 were given

2025-07-08 19:43:36,632 - ERROR - error_handling - Модуль: Резервное копирование, Операция: инициализация системы, Ошибка: log_info() takes 1 positional argument but 2 were given
2025-07-08 19:43:36,633 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\automated_backup.py", line 108, in init_backup_system
    log_info("Система резервного копирования инициализирована", "AutomatedBackup")
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: log_info() takes 1 positional argument but 2 were given

2025-07-08 19:43:37,735 - ERROR - error_handling - Модуль: Резервное копирование, Операция: загрузка обзора, Ошибка: log_info() takes 1 positional argument but 2 were given
2025-07-08 19:43:37,736 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\automated_backup.py", line 872, in load_backup_overview
    log_info("Обзор резервного копирования обновлен", "AutomatedBackup")
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: log_info() takes 1 positional argument but 2 were given

2025-07-08 19:43:40,493 - ERROR - error_handling - Модуль: Резервное копирование, Операция: открытие главного окна, Ошибка: log_info() takes 1 positional argument but 2 were given
2025-07-08 19:43:40,495 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\automated_backup.py", line 257, in show_backup_manager
    log_info("Окно резервного копирования открыто", "AutomatedBackup")
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: log_info() takes 1 positional argument but 2 were given

2025-07-08 19:43:42,710 - INFO - error_handling - MainWindow: Автоматическое резервное копирование открыто
2025-07-08 19:43:43,422 - INFO - error_handling - MainWindow: Открытие CRM системы
2025-07-08 19:43:44,094 - INFO - error_handling - DataExportImportSystem: Открытие системы экспорта/импорта данных
2025-07-08 19:43:44,485 - ERROR - error_handling - Модуль: Мобильная и веб интеграция, Операция: открытие системы, Ошибка: 'bg_primary'
2025-07-08 19:43:44,488 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\enhanced_mobile_web.py", line 1269, in show_enhanced_mobile_web
    mobile_web_system = EnhancedMobileWebSystem(parent, db_manager)
  File "C:\Users\<USER>\Desktop\Restuarant\modules\enhanced_mobile_web.py", line 78, in __init__
    self.create_main_window()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\Restuarant\modules\enhanced_mobile_web.py", line 239, in create_main_window
    self.window.configure(bg=ModernStyles.COLORS['bg_primary'])
                             ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
KeyError: 'bg_primary'

2025-07-08 19:43:46,683 - ERROR - error_handling - Модуль: Расширенная система отчетности, Операция: загрузка расписаний, Ошибка: 'EnhancedReportingSystem' object has no attribute 'schedules'
2025-07-08 19:43:46,686 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\enhanced_reporting_system.py", line 1215, in load_schedules
    for schedule in self.schedules:
                    ^^^^^^^^^^^^^^
AttributeError: 'EnhancedReportingSystem' object has no attribute 'schedules'

2025-07-08 19:43:48,145 - INFO - error_handling - EnhancedReportingSystem: Расширенная система отчетности создана
2025-07-08 19:43:48,145 - INFO - error_handling - EnhancedReportingSystem: Расширенная система отчетности создана
2025-07-08 19:43:48,145 - INFO - error_handling - MainWindow: Расширенная система отчетности открыта
2025-07-08 19:43:48,387 - INFO - category - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-08 19:43:48,390 - INFO - category - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-08 19:43:48,392 - INFO - category - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-08 19:43:49,840 - INFO - error_handling - IntegrationAPIs: Конфигурации интеграций загружены
2025-07-08 19:43:49,840 - INFO - error_handling - IntegrationAPIs: Система интеграции и API создана
2025-07-08 19:43:49,840 - INFO - error_handling - MainWindow: Система интеграции и API открыта
2025-07-08 19:43:50,711 - INFO - error_handling - MainWindow: Открытие системы лояльности
2025-07-08 19:43:51,596 - INFO - error_handling - MobileWebInterface: Мобильный веб-интерфейс создан
2025-07-08 19:43:51,596 - INFO - error_handling - MobileWebInterface: Мобильный веб-интерфейс создан успешно
2025-07-08 19:43:51,597 - INFO - error_handling - MainWindow: Мобильный веб-интерфейс открыт
2025-07-08 19:43:52,786 - INFO - error_handling - PerformanceDashboard: Открытие панели производительности
2025-07-08 19:43:52,849 - INFO - error_handling - PerformanceDashboard: Мониторинг производительности запущен
2025-07-08 19:43:53,918 - INFO - error_handling - PerformanceDashboard: Ошибка обновления данных: main thread is not in main loop
2025-07-08 19:43:53,967 - INFO - error_handling - PerformanceOptimization: Error updating database metrics: 'DatabaseConnectionPool' object has no attribute 'get_stats'
2025-07-08 19:43:53,967 - ERROR - error_handling - Модуль: Performance Optimization, Операция: открытие системы оптимизации, Ошибка: 'bg_primary'
2025-07-08 19:43:53,971 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\performance_optimization.py", line 1470, in show_performance_optimization
    system = PerformanceOptimizationSystem(parent, db_manager)
  File "C:\Users\<USER>\Desktop\Restuarant\modules\performance_optimization.py", line 92, in __init__
    self.create_main_window()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\Restuarant\modules\performance_optimization.py", line 164, in create_main_window
    self.window.configure(bg=ModernStyles.COLORS['bg_primary'])
                             ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
KeyError: 'bg_primary'

2025-07-08 19:43:57,137 - ERROR - error_handling - Модуль: Профессиональная система отчетности, Операция: открытие системы, Ошибка: 'bg_primary'
2025-07-08 19:43:57,138 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\professional_reporting.py", line 1298, in show_professional_reporting
    reporting_system = ProfessionalReportingSystem(parent, db_manager)
  File "C:\Users\<USER>\Desktop\Restuarant\modules\professional_reporting.py", line 37, in __init__
    self.create_main_window()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\Restuarant\modules\professional_reporting.py", line 151, in create_main_window
    self.window.configure(bg=ModernStyles.COLORS['bg_primary'])
                             ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
KeyError: 'bg_primary'

2025-07-08 19:43:58,657 - INFO - error_handling - RealTimeDashboard: Система живых обновлений запущена
2025-07-08 19:43:58,662 - INFO - error_handling - RealTimeDashboard: Панель управления в реальном времени создана
2025-07-08 19:43:58,663 - INFO - error_handling - MainWindow: Панель управления в реальном времени открыта
2025-07-08 19:43:59,712 - INFO - error_handling - RealTimeDashboard: Ошибка в цикле обновления: main thread is not in main loop
2025-07-08 19:44:01,892 - INFO - security_manager - Default admin user created: admin/admin123
2025-07-08 19:44:01,893 - ERROR - error_handling - Модуль: Управление Безопасностью, Операция: открытие окна, Ошибка: 'bg_primary'
2025-07-08 19:44:01,894 - ERROR - error_handling - Трассировка: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Restuarant\modules\security_management.py", line 917, in show_security_management
    SecurityManagementWindow(parent, db_manager)
    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Restuarant\modules\security_management.py", line 31, in __init__
    self.window.configure(bg=ModernStyles.COLORS['bg_primary'])
                             ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
KeyError: 'bg_primary'

2025-07-08 19:44:05,009 - INFO - error_handling - PerformanceDashboard: Ошибка обновления данных: main thread is not in main loop
2025-07-08 19:44:05,822 - INFO - error_handling - RealTimeDashboard: Ошибка в цикле обновления: main thread is not in main loop
2025-07-08 19:44:09,237 - WARNING - sync_manager - Empty schema for table sales
2025-07-08 19:44:09,237 - WARNING - sync_manager - Table inventory does not exist, skipping trigger creation
2025-07-08 19:44:09,238 - WARNING - sync_manager - Empty schema for table recipes
2025-07-08 19:44:09,239 - WARNING - sync_manager - Table staff does not exist, skipping trigger creation
2025-07-08 19:44:09,239 - WARNING - sync_manager - Empty schema for table customers
2025-07-08 19:44:09,240 - WARNING - sync_manager - Empty schema for table purchase_orders
2025-07-08 19:44:09,240 - WARNING - sync_manager - Table expenses does not exist, skipping trigger creation
2025-07-08 19:44:09,240 - WARNING - sync_manager - Table menu_items does not exist, skipping trigger creation
2025-07-08 19:44:09,241 - WARNING - sync_manager - Table suppliers does not exist, skipping trigger creation
2025-07-08 19:44:09,241 - INFO - sync_manager - Data synchronization service started
