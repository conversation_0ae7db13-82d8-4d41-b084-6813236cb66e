#!/usr/bin/env python3
"""
Run the restaurant management system with full diagnostic capabilities
This version includes comprehensive error handling and logging
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback
import logging
from datetime import datetime

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure comprehensive logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('restaurant_debug.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def safe_import(module_name, description=""):
    """Safely import a module with detailed error reporting"""
    try:
        logger.info(f"Importing {module_name} {description}")
        module = __import__(module_name)
        logger.info(f"✅ Successfully imported {module_name}")
        return module
    except Exception as e:
        logger.error(f"❌ Failed to import {module_name}: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return None

def handle_exception(exc_type, exc_value, exc_traceback):
    """Enhanced global exception handler with detailed logging"""
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    error_msg = f"CRITICAL ERROR: {exc_type.__name__}: {exc_value}"
    logger.critical(error_msg)
    logger.critical("Full traceback:")
    logger.critical(''.join(traceback.format_exception(exc_type, exc_value, exc_traceback)))
    
    # Show user-friendly error dialog in Russian
    try:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror(
            "Критическая ошибка системы", 
            f"Произошла серьёзная ошибка в системе управления рестораном:\n\n"
            f"Тип ошибки: {exc_type.__name__}\n"
            f"Описание: {exc_value}\n\n"
            f"Подробная информация сохранена в файл restaurant_debug.log\n\n"
            f"Пожалуйста, обратитесь к системному администратору."
        )
        root.destroy()
    except Exception as dialog_error:
        logger.error(f"Failed to show error dialog: {dialog_error}")

def test_all_critical_imports():
    """Test all critical imports before starting the application"""
    logger.info("="*60)
    logger.info("TESTING CRITICAL IMPORTS")
    logger.info("="*60)
    
    critical_imports = [
        ('tkinter', 'GUI framework'),
        ('sqlite3', 'Database engine'),
        ('gui.main_window', 'Main window module'),
        ('gui.simple_login', 'Login system'),
        ('database.db_manager', 'Database manager'),
        ('modules.database_manager', 'Database selector'),
        ('utils.logger', 'Logging system'),
        ('utils.notification_system', 'Notification system'),
        ('utils.backup_system', 'Backup system'),
    ]
    
    failed_imports = []
    
    for module_name, description in critical_imports:
        result = safe_import(module_name, f"({description})")
        if result is None:
            failed_imports.append((module_name, description))
    
    if failed_imports:
        logger.error(f"❌ {len(failed_imports)} critical imports failed:")
        for module_name, description in failed_imports:
            logger.error(f"   - {module_name} ({description})")
        return False
    else:
        logger.info(f"✅ All {len(critical_imports)} critical imports successful")
        return True

def main():
    """Enhanced main function with comprehensive error handling"""
    # Set up global exception handler
    sys.excepthook = handle_exception
    
    logger.info("="*60)
    logger.info("RESTAURANT MANAGEMENT SYSTEM - DIAGNOSTIC MODE")
    logger.info("="*60)
    logger.info(f"Started at: {datetime.now()}")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Working directory: {os.getcwd()}")
    
    try:
        # Test critical imports first
        if not test_all_critical_imports():
            logger.error("Critical imports failed. Cannot start application.")
            return
        
        # Import main application
        logger.info("Importing main application...")
        from main import RestaurantManagementApp
        logger.info("✅ Main application imported successfully")
        
        # Create and run application
        logger.info("Creating application instance...")
        app = RestaurantManagementApp()
        logger.info("✅ Application instance created")
        
        logger.info("Starting application...")
        app.run()
        logger.info("✅ Application completed normally")
        
    except KeyboardInterrupt:
        logger.info("Application interrupted by user (Ctrl+C)")
    except Exception as e:
        logger.error(f"Unexpected error in main: {e}")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        
        # Show error dialog
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror(
                "Ошибка запуска",
                f"Не удалось запустить систему управления рестораном:\n\n{e}\n\n"
                f"Проверьте файл restaurant_debug.log для подробной информации."
            )
            root.destroy()
        except:
            pass
    finally:
        logger.info("Application shutdown completed")
        logger.info("="*60)

if __name__ == "__main__":
    main()
