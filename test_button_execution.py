#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Тестирование фактического выполнения функций кнопок главного меню
"""

import sys
import os
import importlib
import traceback
import tkinter as tk
from tkinter import messagebox

# Добавить путь к модулям
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

def test_button_execution():
    """Тестирование выполнения функций кнопок"""

    print("🧪 ТЕСТИРОВАНИЕ ВЫПОЛНЕНИЯ ФУНКЦИЙ КНОПОК")
    print("=" * 60)

    # Создать временное главное окно для тестирования
    root = tk.Tk()
    root.withdraw()  # Скрыть главное окно

    # Создать DatabaseManager для тестирования
    try:
        from database.db_manager import DatabaseManager
        db_manager = DatabaseManager("restaurant_system.db")
        print("✅ DatabaseManager создан успешно")
    except Exception as e:
        print(f"❌ Ошибка создания DatabaseManager: {e}")
        return 0, 1, [("DatabaseManager", "init", str(e))]
    
    # Список модулей и их функций из main_window.py
    navigation_modules = [
        ('realtime_dashboard', 'create_realtime_dashboard'),
        ('enhanced_reporting_system', 'create_enhanced_reporting_system'),
        ('enhanced_mobile_web', 'show_enhanced_mobile_web'),
        ('integration_apis', 'create_integration_apis_manager'),
        ('advanced_security', 'create_advanced_security_manager'),
        ('automated_backup', 'create_automated_backup_manager'),
        ('multi_location_manager', 'create_multi_location_manager'),
        ('advanced_analytics', 'show_advanced_analytics'),
        ('security_management', 'show_security_management'),
        ('advanced_inventory', 'show_advanced_inventory'),
        ('kitchen_display_system', 'create_kitchen_display_system'),
        ('sales_data_viewer', 'create_sales_data_viewer'),
        ('inventory_manager', 'create_inventory_manager'),
        ('recipe_manager_working', 'create_recipe_manager'),
        ('financial_dashboard', 'create_financial_dashboard'),
        ('accounting_module', 'create_accounting_module'),
        ('payroll_module', 'create_payroll_module'),
        ('staff_scheduling', 'create_staff_scheduling_system'),
        ('customer_crm', 'create_customer_crm'),
        ('table_booking', 'create_table_booking_system'),
        ('menu_planning', 'create_menu_planning_system'),
        ('quality_control', 'create_quality_control_system'),
        ('reports_system', 'create_reports_system'),
        ('notification_center', 'create_notification_center'),
        ('backup_manager', 'create_backup_manager'),
        ('style_configurator', 'create_style_configurator'),
        ('payment_codes_manager', 'create_payment_codes_manager'),
        ('settings_manager', 'create_settings_manager'),
        ('reports_simple', 'create_reports_manager'),
        ('ai_insights', 'create_ai_insights_manager'),
        ('language_settings', 'create_language_settings_manager'),
        ('advanced_reports_simple', 'create_advanced_reports_manager'),
        ('professional_reporting', 'show_professional_reporting'),
        ('mobile_web_interface', 'create_mobile_web_interface'),
        ('performance_optimization', 'show_performance_optimization'),
        ('sync_monitor', 'SyncMonitorWindow'),
        ('customer_relationship_management', 'create_customer_relationship_management'),
        ('loyalty_rewards_system', 'create_loyalty_rewards_system'),
        ('advanced_business_intelligence', 'create_advanced_business_intelligence'),
        ('table_reservation_queue_management', 'create_table_reservation_queue_management'),
        ('supply_chain_vendor_management', 'create_supply_chain_vendor_management'),
        ('financial_planning_budgeting', 'create_financial_planning_budgeting'),
        ('performance_dashboard', 'create_performance_dashboard'),
        ('data_export_import', 'create_data_export_import_system'),
        ('cost_control_simple', 'create_cost_control_system'),
        ('security_audit_system', 'create_security_audit_system'),
        ('mobile_api_integration', 'create_mobile_api_integration')
    ]
    
    working_functions = []
    broken_functions = []
    
    for module_name, function_name in navigation_modules:
        try:
            # Импортировать модуль
            module = importlib.import_module(f'modules.{module_name}')
            
            # Получить функцию
            if hasattr(module, function_name):
                func = getattr(module, function_name)
                
                # Попытаться выполнить функцию (с перехватом ошибок)
                try:
                    # Для функций, которые создают окна, передаем правильные параметры
                    if function_name == 'SyncMonitorWindow':
                        # Это класс, а не функция
                        test_window = func(root, db_manager)
                        test_window.destroy()  # Закрыть тестовое окно
                    elif function_name == 'create_style_configurator':
                        # Эта функция принимает только parent
                        test_window = func(root)
                        if hasattr(test_window, 'destroy'):
                            test_window.destroy()  # Закрыть тестовое окно
                    else:
                        # Это функция, вызываем с parent и db_manager
                        test_window = func(root, db_manager)
                        if hasattr(test_window, 'destroy'):
                            test_window.destroy()  # Закрыть тестовое окно
                    
                    print(f"✅ {module_name}.{function_name} - ВЫПОЛНЕНА УСПЕШНО")
                    working_functions.append((module_name, function_name))
                    
                except Exception as e:
                    print(f"❌ {module_name}.{function_name} - ОШИБКА ВЫПОЛНЕНИЯ: {str(e)}")
                    broken_functions.append((module_name, function_name, str(e)))
                    
            else:
                print(f"❌ {module_name}.{function_name} - ФУНКЦИЯ НЕ НАЙДЕНА")
                broken_functions.append((module_name, function_name, "Функция не найдена"))
                
        except ImportError as e:
            print(f"❌ {module_name}.{function_name} - ОШИБКА ИМПОРТА: {str(e)}")
            broken_functions.append((module_name, function_name, f"Ошибка импорта: {str(e)}"))
        except Exception as e:
            print(f"❌ {module_name}.{function_name} - НЕОЖИДАННАЯ ОШИБКА: {str(e)}")
            broken_functions.append((module_name, function_name, f"Неожиданная ошибка: {str(e)}"))
    
    # Закрыть временное окно
    root.destroy()
    
    print("\n📊 РЕЗУЛЬТАТЫ:")
    print(f"   ✅ Работающих функций: {len(working_functions)}")
    print(f"   ❌ Проблемных функций: {len(broken_functions)}")
    
    if broken_functions:
        print(f"\n🚨 ПРОБЛЕМНЫЕ ФУНКЦИИ:")
        for i, (module_name, function_name, error) in enumerate(broken_functions, 1):
            print(f"    {i}. {module_name}.{function_name}")
            print(f"       ❌ {error}")
    else:
        print(f"\n🎉 ВСЕ ФУНКЦИИ ВЫПОЛНЯЮТСЯ КОРРЕКТНО!")
    
    return len(working_functions), len(broken_functions), broken_functions

if __name__ == "__main__":
    test_button_execution()
