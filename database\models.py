"""
Database models for Restaurant Accounting and Inventory Management System
"""

import sqlite3
from datetime import datetime
from typing import Optional, List, Dict, Any

class DatabaseModels:
    """Database schema definitions and table creation"""
    
    @staticmethod
    def create_tables(conn: sqlite3.Connection):
        """Create all necessary tables for the restaurant system"""
        
        cursor = conn.cursor()
        
        # Users table for access control
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                role TEXT NOT NULL DEFAULT 'user',
                full_name TEXT,
                email TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        # Sales data table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sales (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_date DATE NOT NULL,
                order_number TEXT NOT NULL,
                payment_method TEXT,
                department TEXT,
                dish_code TEXT,
                dish_name TEXT NOT NULL,
                quantity REAL NOT NULL,
                price_per_dish REAL NOT NULL,
                total_amount REAL NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Raw materials/ingredients table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS raw_materials (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                category TEXT,
                unit_of_measure TEXT NOT NULL,
                current_stock REAL DEFAULT 0,
                minimum_stock REAL DEFAULT 0,
                average_cost REAL DEFAULT 0,
                last_purchase_price REAL DEFAULT 0,
                supplier TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Purchase orders table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS purchase_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_number TEXT UNIQUE NOT NULL,
                supplier TEXT NOT NULL,
                order_date DATE NOT NULL,
                delivery_date DATE,
                subtotal REAL NOT NULL,
                vat_percentage REAL DEFAULT 0,
                vat_amount REAL DEFAULT 0,
                discount_percentage REAL DEFAULT 0,
                discount_amount REAL DEFAULT 0,
                total_amount REAL NOT NULL,
                status TEXT DEFAULT 'pending',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Purchase order items table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS purchase_order_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                purchase_order_id INTEGER NOT NULL,
                raw_material_id INTEGER NOT NULL,
                quantity REAL NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders (id),
                FOREIGN KEY (raw_material_id) REFERENCES raw_materials (id)
            )
        ''')
        
        # Recipes/technological cards table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS recipes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                category TEXT,
                serving_size INTEGER DEFAULT 1,
                preparation_time INTEGER,
                cooking_time INTEGER,
                difficulty_level TEXT,
                cost_per_serving REAL DEFAULT 0,
                selling_price REAL DEFAULT 0,
                profit_margin REAL DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Recipe ingredients table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS recipe_ingredients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                recipe_id INTEGER NOT NULL,
                raw_material_id INTEGER NOT NULL,
                quantity REAL NOT NULL,
                unit_of_measure TEXT NOT NULL,
                cost REAL DEFAULT 0,
                FOREIGN KEY (recipe_id) REFERENCES recipes (id),
                FOREIGN KEY (raw_material_id) REFERENCES raw_materials (id)
            )
        ''')
        
        # Semi-finished products table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS semi_finished_products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                unit_of_measure TEXT NOT NULL,
                current_stock REAL DEFAULT 0,
                production_cost REAL DEFAULT 0,
                shelf_life_days INTEGER,
                recipe_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (recipe_id) REFERENCES recipes (id)
            )
        ''')
        
        # Inventory adjustments table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_adjustments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                raw_material_id INTEGER NOT NULL,
                adjustment_type TEXT NOT NULL,
                quantity_before REAL NOT NULL,
                quantity_after REAL NOT NULL,
                adjustment_quantity REAL NOT NULL,
                reason TEXT,
                adjusted_by TEXT,
                adjustment_date DATE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (raw_material_id) REFERENCES raw_materials (id)
            )
        ''')
        
        # Unit conversions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS unit_conversions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                from_unit TEXT NOT NULL,
                to_unit TEXT NOT NULL,
                conversion_factor REAL NOT NULL,
                raw_material_id INTEGER,
                FOREIGN KEY (raw_material_id) REFERENCES raw_materials (id)
            )
        ''')

        # Chart of Accounts
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS chart_of_accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_code TEXT UNIQUE NOT NULL,
                account_name TEXT NOT NULL,
                account_type TEXT NOT NULL, -- Asset, Liability, Equity, Revenue, Expense
                parent_account_id INTEGER,
                is_active BOOLEAN DEFAULT 1,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_account_id) REFERENCES chart_of_accounts (id)
            )
        ''')

        # General Ledger
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS general_ledger (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transaction_date DATE NOT NULL,
                account_id INTEGER NOT NULL,
                debit_amount REAL DEFAULT 0,
                credit_amount REAL DEFAULT 0,
                description TEXT,
                reference_number TEXT,
                journal_entry_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (account_id) REFERENCES chart_of_accounts (id)
            )
        ''')

        # Journal Entries
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS journal_entries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                entry_date DATE NOT NULL,
                entry_number TEXT UNIQUE NOT NULL,
                description TEXT NOT NULL,
                total_debit REAL NOT NULL,
                total_credit REAL NOT NULL,
                status TEXT DEFAULT 'draft', -- draft, posted, reversed
                created_by TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                posted_at TIMESTAMP
            )
        ''')

        # Accounts Payable
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS accounts_payable (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                vendor_id INTEGER NOT NULL,
                invoice_number TEXT NOT NULL,
                invoice_date DATE NOT NULL,
                due_date DATE NOT NULL,
                amount REAL NOT NULL,
                paid_amount REAL DEFAULT 0,
                status TEXT DEFAULT 'pending', -- pending, partial, paid, overdue
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (vendor_id) REFERENCES vendors (id)
            )
        ''')

        # Accounts Receivable
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS accounts_receivable (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER NOT NULL,
                invoice_number TEXT NOT NULL,
                invoice_date DATE NOT NULL,
                due_date DATE NOT NULL,
                amount REAL NOT NULL,
                paid_amount REAL DEFAULT 0,
                status TEXT DEFAULT 'pending', -- pending, partial, paid, overdue
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        ''')

        # Vendors/Suppliers
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS vendors (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                vendor_code TEXT UNIQUE NOT NULL,
                vendor_name TEXT NOT NULL,
                contact_person TEXT,
                email TEXT,
                phone TEXT,
                address TEXT,
                city TEXT,
                state TEXT,
                zip_code TEXT,
                country TEXT,
                tax_id TEXT,
                payment_terms TEXT,
                credit_limit REAL DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Customers
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_code TEXT UNIQUE NOT NULL,
                customer_name TEXT NOT NULL,
                contact_person TEXT,
                email TEXT,
                phone TEXT,
                address TEXT,
                city TEXT,
                state TEXT,
                zip_code TEXT,
                country TEXT,
                tax_id TEXT,
                credit_limit REAL DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Bank Accounts
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bank_accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_name TEXT NOT NULL,
                bank_name TEXT NOT NULL,
                account_number TEXT NOT NULL,
                account_type TEXT, -- checking, savings, credit
                routing_number TEXT,
                current_balance REAL DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Bank Transactions
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bank_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bank_account_id INTEGER NOT NULL,
                transaction_date DATE NOT NULL,
                transaction_type TEXT NOT NULL, -- deposit, withdrawal, transfer
                amount REAL NOT NULL,
                description TEXT,
                reference_number TEXT,
                reconciled BOOLEAN DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (bank_account_id) REFERENCES bank_accounts (id)
            )
        ''')

        # Tax Management
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS tax_rates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                tax_name TEXT NOT NULL,
                tax_rate REAL NOT NULL,
                tax_type TEXT, -- sales, purchase, vat
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Payroll
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id TEXT UNIQUE NOT NULL,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                position TEXT,
                department TEXT,
                hire_date DATE,
                salary REAL DEFAULT 0,
                hourly_rate REAL DEFAULT 0,
                email TEXT,
                phone TEXT,
                address TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Payroll Records
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS payroll_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                pay_period_start DATE NOT NULL,
                pay_period_end DATE NOT NULL,
                hours_worked REAL DEFAULT 0,
                overtime_hours REAL DEFAULT 0,
                gross_pay REAL NOT NULL,
                tax_deductions REAL DEFAULT 0,
                other_deductions REAL DEFAULT 0,
                net_pay REAL NOT NULL,
                pay_date DATE,
                status TEXT DEFAULT 'pending', -- pending, paid
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')

        # Fixed Assets
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fixed_assets (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                asset_name TEXT NOT NULL,
                asset_category TEXT,
                purchase_date DATE NOT NULL,
                purchase_price REAL NOT NULL,
                useful_life_years INTEGER,
                depreciation_method TEXT, -- straight-line, declining-balance
                accumulated_depreciation REAL DEFAULT 0,
                current_value REAL,
                location TEXT,
                serial_number TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Budget Planning
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS budgets (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                budget_name TEXT NOT NULL,
                budget_year INTEGER NOT NULL,
                account_id INTEGER NOT NULL,
                january REAL DEFAULT 0,
                february REAL DEFAULT 0,
                march REAL DEFAULT 0,
                april REAL DEFAULT 0,
                may REAL DEFAULT 0,
                june REAL DEFAULT 0,
                july REAL DEFAULT 0,
                august REAL DEFAULT 0,
                september REAL DEFAULT 0,
                october REAL DEFAULT 0,
                november REAL DEFAULT 0,
                december REAL DEFAULT 0,
                total_budget REAL DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (account_id) REFERENCES chart_of_accounts (id)
            )
        ''')

        # Financial Periods
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS financial_periods (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                period_name TEXT NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                is_closed BOOLEAN DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Expense Categories
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS expense_categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                category_name TEXT NOT NULL,
                category_code TEXT UNIQUE,
                description TEXT,
                account_id INTEGER,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (account_id) REFERENCES chart_of_accounts (id)
            )
        ''')

        # Daily Cash Flow
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS daily_cash_flow (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transaction_date DATE NOT NULL,
                opening_balance REAL DEFAULT 0,
                cash_sales REAL DEFAULT 0,
                card_sales REAL DEFAULT 0,
                other_income REAL DEFAULT 0,
                cash_expenses REAL DEFAULT 0,
                card_expenses REAL DEFAULT 0,
                bank_deposits REAL DEFAULT 0,
                bank_withdrawals REAL DEFAULT 0,
                closing_balance REAL DEFAULT 0,
                variance REAL DEFAULT 0,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        conn.commit()

        # Create default chart of accounts
        cursor.execute("SELECT COUNT(*) FROM chart_of_accounts")
        if cursor.fetchone()[0] == 0:
            default_accounts = [
                # Assets
                ('1000', 'Cash', 'Asset', None),
                ('1100', 'Bank Account', 'Asset', None),
                ('1200', 'Accounts Receivable', 'Asset', None),
                ('1300', 'Inventory', 'Asset', None),
                ('1400', 'Prepaid Expenses', 'Asset', None),
                ('1500', 'Equipment', 'Asset', None),
                ('1600', 'Accumulated Depreciation', 'Asset', None),

                # Liabilities
                ('2000', 'Accounts Payable', 'Liability', None),
                ('2100', 'Accrued Expenses', 'Liability', None),
                ('2200', 'Taxes Payable', 'Liability', None),
                ('2300', 'Loans Payable', 'Liability', None),

                # Equity
                ('3000', 'Owner Equity', 'Equity', None),
                ('3100', 'Retained Earnings', 'Equity', None),

                # Revenue
                ('4000', 'Food Sales', 'Revenue', None),
                ('4100', 'Beverage Sales', 'Revenue', None),
                ('4200', 'Other Revenue', 'Revenue', None),

                # Expenses
                ('5000', 'Cost of Goods Sold', 'Expense', None),
                ('5100', 'Food Costs', 'Expense', None),
                ('5200', 'Beverage Costs', 'Expense', None),
                ('6000', 'Salaries and Wages', 'Expense', None),
                ('6100', 'Rent Expense', 'Expense', None),
                ('6200', 'Utilities', 'Expense', None),
                ('6300', 'Marketing', 'Expense', None),
                ('6400', 'Insurance', 'Expense', None),
                ('6500', 'Depreciation', 'Expense', None),
                ('6600', 'Other Expenses', 'Expense', None)
            ]

            for code, name, acc_type, parent in default_accounts:
                cursor.execute('''
                    INSERT INTO chart_of_accounts (account_code, account_name, account_type, parent_account_id)
                    VALUES (?, ?, ?, ?)
                ''', (code, name, acc_type, parent))

            conn.commit()
            print("Default chart of accounts created!")

        print("Database tables created successfully!")
