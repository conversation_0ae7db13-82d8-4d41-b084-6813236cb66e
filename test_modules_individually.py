#!/usr/bin/env python3
"""
Test each Phase 3 module individually to identify which one causes the error
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_module(module_name, create_function_name):
    """Test a specific module"""
    print(f"\n{'='*60}")
    print(f"TESTING MODULE: {module_name}")
    print(f"{'='*60}")
    
    try:
        # Import the module
        print(f"1. Importing {module_name}...")
        module = __import__(module_name, fromlist=[create_function_name])
        print(f"   ✅ Module imported successfully")
        
        # Get the create function
        print(f"2. Getting function {create_function_name}...")
        create_function = getattr(module, create_function_name)
        print(f"   ✅ Function retrieved successfully")
        
        # Create a test database manager
        print("3. Creating test database manager...")
        from database.db_manager import DatabaseManager
        db_manager = DatabaseManager('test_module.db')
        print("   ✅ Database manager created")
        
        # Create a test root window
        print("4. Creating test root window...")
        root = tk.Tk()
        root.withdraw()  # Hide the window
        print("   ✅ Root window created")
        
        # Try to call the create function
        print("5. Calling module create function...")
        try:
            create_function(root, db_manager)
            print("   ✅ Module function called successfully")
        except Exception as e:
            print(f"   ❌ Module function failed: {e}")
            traceback.print_exc()
            return False
        
        # Clean up
        print("6. Cleaning up...")
        root.destroy()
        
        # Clean up test database
        if os.path.exists('test_module.db'):
            try:
                os.remove('test_module.db')
            except:
                pass
        
        print(f"   ✅ {module_name} tested successfully!")
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing {module_name}: {e}")
        traceback.print_exc()
        return False

def main():
    """Test all Phase 3 modules individually"""
    print("INDIVIDUAL MODULE TESTING")
    print("="*60)
    
    modules_to_test = [
        ('modules.customer_relationship_management', 'create_customer_relationship_management'),
        ('modules.loyalty_rewards_system', 'create_loyalty_rewards_system'),
        ('modules.advanced_business_intelligence', 'create_advanced_business_intelligence'),
        ('modules.table_reservation_queue_management', 'create_table_reservation_queue_management'),
        ('modules.supply_chain_vendor_management', 'create_supply_chain_vendor_management'),
        ('modules.financial_planning_budgeting', 'create_financial_planning_budgeting')
    ]
    
    results = {}
    
    for module_name, function_name in modules_to_test:
        success = test_module(module_name, function_name)
        results[module_name] = success
    
    print(f"\n{'='*60}")
    print("TESTING RESULTS SUMMARY")
    print(f"{'='*60}")
    
    for module_name, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{module_name}: {status}")
    
    failed_modules = [name for name, success in results.items() if not success]
    if failed_modules:
        print(f"\n❌ FAILED MODULES ({len(failed_modules)}):")
        for module in failed_modules:
            print(f"   - {module}")
        print("\nThese modules are likely causing the error in the main application.")
    else:
        print(f"\n✅ ALL MODULES PASSED ({len(results)})")
        print("The error might be occurring elsewhere in the application.")

if __name__ == "__main__":
    main()
