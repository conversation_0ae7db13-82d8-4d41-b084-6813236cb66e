# -*- coding: utf-8 -*-
"""
Multi-Language Localization Manager for Restaurant Management System
Manages language switching, currency formats, date formats, and cultural preferences
"""

import json
import os
import locale
from datetime import datetime
from typing import Dict, Any, Optional
import tkinter as tk
from tkinter import messagebox

class LocalizationManager:
    """Manages multi-language support and localization"""
    
    def __init__(self):
        self.current_language = 'ru'  # Default to Russian
        self.languages = {}
        self.currency_formats = {}
        self.date_formats = {}
        self.number_formats = {}
        self.cultural_preferences = {}
        self.load_all_languages()
        self.load_formats()
    
    def load_all_languages(self):
        """Load all available language packs"""
        try:
            # Load Russian (existing)
            self.languages['ru'] = self._extract_language_constants('russian')

            # Load English
            self.languages['en'] = self._extract_language_constants('english')

            # Load Spanish
            self.languages['es'] = self._extract_language_constants('spanish')

            # Load French
            self.languages['fr'] = self._extract_language_constants('french')

            # Load German
            self.languages['de'] = self._extract_language_constants('german')

            # Load Chinese
            self.languages['zh'] = self._extract_language_constants('chinese')

        except ImportError as e:
            print(f"Warning: Could not load some language packs: {e}")
    
    def _extract_language_constants(self, module_name):
        """Extract language constants from a module"""
        try:
            module = __import__(f'localization.{module_name}', fromlist=[''])
            constants = {}
            for attr in dir(module):
                if not attr.startswith('_') and attr.isupper():
                    constants[attr] = getattr(module, attr)
            return constants
        except Exception as e:
            print(f"Error extracting constants from {module_name}: {e}")
            return {}
    
    def load_formats(self):
        """Load currency, date, and number formats for different locales"""
        self.currency_formats = {
            'ru': {
                'symbol': 'руб.',
                'position': 'after',
                'decimal_separator': ',',
                'thousand_separator': ' ',
                'decimal_places': 2,
                'format': '{amount} {symbol}'
            },
            'en': {
                'symbol': '$',
                'position': 'before',
                'decimal_separator': '.',
                'thousand_separator': ',',
                'decimal_places': 2,
                'format': '{symbol}{amount}'
            },
            'es': {
                'symbol': '€',
                'position': 'after',
                'decimal_separator': ',',
                'thousand_separator': '.',
                'decimal_places': 2,
                'format': '{amount} {symbol}'
            },
            'fr': {
                'symbol': '€',
                'position': 'after',
                'decimal_separator': ',',
                'thousand_separator': ' ',
                'decimal_places': 2,
                'format': '{amount} {symbol}'
            },
            'de': {
                'symbol': '€',
                'position': 'after',
                'decimal_separator': ',',
                'thousand_separator': '.',
                'decimal_places': 2,
                'format': '{amount} {symbol}'
            },
            'zh': {
                'symbol': '¥',
                'position': 'before',
                'decimal_separator': '.',
                'thousand_separator': ',',
                'decimal_places': 2,
                'format': '{symbol}{amount}'
            }
        }
        
        self.date_formats = {
            'ru': '%d.%m.%Y',  # DD.MM.YYYY
            'en': '%m/%d/%Y',  # MM/DD/YYYY
            'es': '%d/%m/%Y',  # DD/MM/YYYY
            'fr': '%d/%m/%Y',  # DD/MM/YYYY
            'de': '%d.%m.%Y',  # DD.MM.YYYY
            'zh': '%Y-%m-%d'   # YYYY-MM-DD
        }
        
        self.cultural_preferences = {
            'ru': {
                'first_day_of_week': 1,  # Monday
                'time_format': '24h',
                'measurement_system': 'metric'
            },
            'en': {
                'first_day_of_week': 0,  # Sunday
                'time_format': '12h',
                'measurement_system': 'imperial'
            },
            'es': {
                'first_day_of_week': 1,  # Monday
                'time_format': '24h',
                'measurement_system': 'metric'
            },
            'fr': {
                'first_day_of_week': 1,  # Monday
                'time_format': '24h',
                'measurement_system': 'metric'
            },
            'de': {
                'first_day_of_week': 1,  # Monday
                'time_format': '24h',
                'measurement_system': 'metric'
            },
            'zh': {
                'first_day_of_week': 1,  # Monday
                'time_format': '24h',
                'measurement_system': 'metric'
            }
        }
    
    def get_available_languages(self):
        """Get list of available languages"""
        return {
            'ru': 'Русский',
            'en': 'English',
            'es': 'Español',
            'fr': 'Français',
            'de': 'Deutsch',
            'zh': '中文'
        }
    
    def set_language(self, language_code: str):
        """Set the current language"""
        if language_code in self.languages:
            self.current_language = language_code
            return True
        return False
    
    def get_text(self, key: str, default: str = None) -> str:
        """Get localized text for a key"""
        if self.current_language in self.languages:
            return self.languages[self.current_language].get(key, default or key)
        return default or key
    
    def format_currency(self, amount: float) -> str:
        """Format currency according to current locale"""
        fmt = self.currency_formats.get(self.current_language, self.currency_formats['en'])
        
        # Format the number with proper separators
        decimal_places = fmt['decimal_places']
        thousand_sep = fmt['thousand_separator']
        decimal_sep = fmt['decimal_separator']
        
        # Format the amount
        formatted_amount = f"{amount:,.{decimal_places}f}"
        formatted_amount = formatted_amount.replace(',', '|TEMP|')
        formatted_amount = formatted_amount.replace('.', decimal_sep)
        formatted_amount = formatted_amount.replace('|TEMP|', thousand_sep)
        
        # Apply currency format
        return fmt['format'].format(amount=formatted_amount, symbol=fmt['symbol'])
    
    def format_date(self, date: datetime) -> str:
        """Format date according to current locale"""
        fmt = self.date_formats.get(self.current_language, self.date_formats['en'])
        return date.strftime(fmt)
    
    def get_cultural_preference(self, key: str):
        """Get cultural preference for current language"""
        prefs = self.cultural_preferences.get(self.current_language, self.cultural_preferences['en'])
        return prefs.get(key)
    
    def create_language_selector(self, parent, callback=None):
        """Create a language selector widget"""
        frame = tk.Frame(parent)
        
        tk.Label(frame, text=self.get_text('LANGUAGE_LABEL', 'Language:')).pack(side='left', padx=5)
        
        languages = self.get_available_languages()
        language_var = tk.StringVar(value=languages[self.current_language])
        
        language_combo = tk.OptionMenu(frame, language_var, *languages.values(),
                                     command=lambda x: self._on_language_change(x, callback))
        language_combo.pack(side='left', padx=5)
        
        return frame
    
    def _on_language_change(self, selected_language, callback=None):
        """Handle language change"""
        # Find language code by name
        languages = self.get_available_languages()
        for code, name in languages.items():
            if name == selected_language:
                self.set_language(code)
                if callback:
                    callback(code)
                break


# Global localization manager instance
_localization_manager = None

def get_localization_manager():
    """Get the global localization manager instance"""
    global _localization_manager
    if _localization_manager is None:
        _localization_manager = LocalizationManager()
    return _localization_manager

def get_text(key: str, default: str = None) -> str:
    """Convenience function to get localized text"""
    return get_localization_manager().get_text(key, default)

def format_currency(amount: float) -> str:
    """Convenience function to format currency"""
    return get_localization_manager().format_currency(amount)

def format_date(date: datetime) -> str:
    """Convenience function to format date"""
    return get_localization_manager().format_date(date)

def set_language(language_code: str):
    """Convenience function to set language"""
    return get_localization_manager().set_language(language_code)
