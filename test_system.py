"""
Test script for Restaurant Management System
"""

import sys
import os

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database():
    """Test database functionality"""
    print("Testing database functionality...")
    
    try:
        from database.db_manager import DatabaseManager
        
        # Initialize database
        db = DatabaseManager()
        print("✓ Database initialized successfully")
        
        # Test raw materials
        material_data = {
            'name': 'Test Tomatoes',
            'category': 'Vegetables',
            'unit_of_measure': 'kg',
            'current_stock': 50.0,
            'minimum_stock': 10.0,
            'supplier': 'Test Supplier'
        }
        
        material_id = db.insert_raw_material(material_data)
        if material_id:
            print(f"✓ Raw material inserted with ID: {material_id}")
        else:
            print("✗ Failed to insert raw material")
        
        # Test getting raw materials
        materials = db.get_raw_materials()
        print(f"✓ Retrieved {len(materials)} raw materials")
        
        # Test sales data
        sales_data = [{
            'order_date': '2024-01-15',
            'order_number': 'TEST001',
            'payment_method': 'Cash',
            'department': 'Kitchen',
            'dish_code': 'TEST001',
            'dish_name': 'Test Dish',
            'quantity': 2.0,
            'price_per_dish': 15.50,
            'total_amount': 31.00
        }]
        
        success = db.insert_sales_data(sales_data)
        if success:
            print("✓ Sales data inserted successfully")
        else:
            print("✗ Failed to insert sales data")
        
        # Test getting sales data
        sales = db.get_sales_data()
        print(f"✓ Retrieved {len(sales)} sales records")
        
        return True
        
    except Exception as e:
        print(f"✗ Database test failed: {e}")
        return False

def test_gui_imports():
    """Test GUI module imports"""
    print("\nTesting GUI imports...")
    
    try:
        from gui.styles import ModernStyles
        print("✓ Styles module imported")
        
        from gui.main_window import MainWindow
        print("✓ Main window module imported")
        
        from modules.sales_import import SalesImportWindow
        print("✓ Sales import module imported")
        
        return True
        
    except Exception as e:
        print(f"✗ GUI import test failed: {e}")
        return False

def test_csv_parsing():
    """Test CSV parsing functionality"""
    print("\nTesting CSV parsing...")
    
    try:
        import pandas as pd
        
        # Test with sample CSV
        if os.path.exists('sample_sales_data.csv'):
            df = pd.read_csv('sample_sales_data.csv', header=None)
            print(f"✓ Sample CSV loaded with {len(df)} rows and {len(df.columns)} columns")
            
            # Test column extraction
            if len(df.columns) >= 17:
                print("✓ CSV has sufficient columns for data extraction")
            else:
                print(f"⚠ CSV has only {len(df.columns)} columns, may need adjustment")
            
            return True
        else:
            print("⚠ Sample CSV file not found")
            return False
            
    except Exception as e:
        print(f"✗ CSV parsing test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("Restaurant Management System - Test Suite")
    print("=" * 60)
    
    tests = [
        test_database,
        test_gui_imports,
        test_csv_parsing
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready to use.")
        print("\nTo start the application, run: python main.py")
    else:
        print("⚠ Some tests failed. Please check the errors above.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
