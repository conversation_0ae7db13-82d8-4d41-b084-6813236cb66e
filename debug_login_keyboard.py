#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ОТЛАДКА ПРОБЛЕМЫ ПЕРЕКЛЮЧЕНИЯ РАСКЛАДКИ ПРИ ЛОГИНЕ
Минимальная репродукция проблемы пользователя
"""

import tkinter as tk
from tkinter import messagebox
import ctypes
import time
import threading
import subprocess
import sys
import os

def safe_print(text):
    """Безопасный вывод с обработкой Unicode ошибок"""
    try:
        print(text)
    except UnicodeEncodeError:
        ascii_text = text.encode('ascii', 'ignore').decode('ascii')
        print(ascii_text)

def get_current_layout():
    """Получить текущую раскладку клавиатуры"""
    try:
        user32 = ctypes.windll.user32
        hwnd = user32.GetForegroundWindow()
        thread_id = user32.GetWindowThreadProcessId(hwnd, None)
        hkl = user32.GetKeyboardLayout(thread_id)
        layout_id = hkl & 0xFFFF
        return layout_id
    except:
        return None

def switch_to_english():
    """Переключить на английскую раскладку"""
    try:
        user32 = ctypes.windll.user32
        hkl_english = user32.LoadKeyboardLayoutW("00000409", 0x00000001)
        if hkl_english:
            result = user32.ActivateKeyboardLayout(hkl_english, 0x00000008)
            return result != 0
        return False
    except:
        return False

def switch_to_russian_basic():
    """Базовое переключение на русскую раскладку"""
    try:
        user32 = ctypes.windll.user32
        hkl_russian = user32.LoadKeyboardLayoutW("00000419", 0x00000001)
        if hkl_russian:
            result = user32.ActivateKeyboardLayout(hkl_russian, 0x00000008)
            return result != 0
        return False
    except:
        return False

def run_extreme_methods():
    """Запуск экстремальных методов переключения"""
    safe_print("🚀 ЗАПУСК ЭКСТРЕМАЛЬНЫХ МЕТОДОВ...")
    
    methods_results = []
    
    # Метод 1: Экстремальный хук
    try:
        if os.path.exists("extreme_keyboard_hook.py"):
            safe_print("   Тестирование экстремального хука...")
            result = subprocess.run([sys.executable, "extreme_keyboard_hook.py"], 
                                  timeout=5, capture_output=True, text=True)
            success = result.returncode == 0
            methods_results.append(("Экстремальный хук", success))
            safe_print(f"   Результат: {'✅ УСПЕХ' if success else '❌ НЕУДАЧА'}")
        else:
            methods_results.append(("Экстремальный хук", False))
            safe_print("   ❌ Файл экстремального хука не найден")
    except Exception as e:
        methods_results.append(("Экстремальный хук", False))
        safe_print(f"   ❌ Ошибка хука: {e}")
    
    # Метод 2: Ядерный подход (без админ прав)
    try:
        if os.path.exists("nuclear_keyboard_switch.py"):
            safe_print("   Тестирование ядерного подхода...")
            # Запускаем без ввода, чтобы избежать запроса админ прав
            result = subprocess.run([sys.executable, "nuclear_keyboard_switch.py"], 
                                  timeout=5, capture_output=True, text=True)
            success = "УСПЕХ" in result.stdout or result.returncode == 0
            methods_results.append(("Ядерный подход", success))
            safe_print(f"   Результат: {'✅ УСПЕХ' if success else '❌ НЕУДАЧА'}")
            if result.stdout:
                safe_print(f"   Вывод: {result.stdout[:100]}...")
        else:
            methods_results.append(("Ядерный подход", False))
            safe_print("   ❌ Файл ядерного подхода не найден")
    except Exception as e:
        methods_results.append(("Ядерный подход", False))
        safe_print(f"   ❌ Ошибка ядерного подхода: {e}")
    
    # Метод 3: Драйверный подход
    try:
        if os.path.exists("ultimate_driver_approach.py"):
            safe_print("   Тестирование драйверного подхода...")
            result = subprocess.run([sys.executable, "ultimate_driver_approach.py"], 
                                  input="n\n", timeout=5, capture_output=True, text=True)
            success = "ДРАЙВЕР СОЗДАН" in result.stdout
            methods_results.append(("Драйверный подход", success))
            safe_print(f"   Результат: {'✅ УСПЕХ' if success else '❌ НЕУДАЧА'}")
        else:
            methods_results.append(("Драйверный подход", False))
            safe_print("   ❌ Файл драйверного подхода не найден")
    except Exception as e:
        methods_results.append(("Драйверный подход", False))
        safe_print(f"   ❌ Ошибка драйверного подхода: {e}")
    
    return methods_results

class LoginDebugWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("ОТЛАДКА ПРОБЛЕМЫ ПЕРЕКЛЮЧЕНИЯ РАСКЛАДКИ")
        self.root.geometry("800x600")
        self.root.configure(bg='#2c3e50')
        
        self.setup_ui()
        self.update_layout_display()
        
    def setup_ui(self):
        """Настройка интерфейса"""
        # Заголовок
        tk.Label(
            self.root,
            text="ОТЛАДКА ПРОБЛЕМЫ ПЕРЕКЛЮЧЕНИЯ РАСКЛАДКИ ПРИ ЛОГИНЕ",
            font=('Cambria', 16, 'bold'),
            fg='white',
            bg='#2c3e50'
        ).pack(pady=20)
        
        # Текущая раскладка
        self.layout_label = tk.Label(
            self.root,
            text="Текущая раскладка: Проверка...",
            font=('Cambria', 14, 'bold'),
            fg='white',
            bg='#2c3e50'
        )
        self.layout_label.pack(pady=10)
        
        # Поля логина (имитация)
        login_frame = tk.Frame(self.root, bg='#2c3e50')
        login_frame.pack(pady=20)
        
        tk.Label(login_frame, text="Имя пользователя:", font=('Cambria', 12), fg='white', bg='#2c3e50').pack()
        self.username_entry = tk.Entry(login_frame, font=('Cambria', 12), width=30)
        self.username_entry.pack(pady=5)
        
        tk.Label(login_frame, text="Пароль:", font=('Cambria', 12), fg='white', bg='#2c3e50').pack()
        self.password_entry = tk.Entry(login_frame, font=('Cambria', 12), width=30, show='*')
        self.password_entry.pack(pady=5)
        
        # Кнопки управления
        button_frame = tk.Frame(self.root, bg='#2c3e50')
        button_frame.pack(pady=20)
        
        tk.Button(
            button_frame,
            text="СИМУЛЯЦИЯ ЛОГИНА",
            font=('Cambria', 14, 'bold'),
            bg='#e74c3c',
            fg='white',
            command=self.simulate_login_process,
            width=20,
            height=2
        ).pack(side=tk.LEFT, padx=10)
        
        tk.Button(
            button_frame,
            text="ТЕСТ ЭКСТРЕМАЛЬНЫХ МЕТОДОВ",
            font=('Cambria', 12, 'bold'),
            bg='#9b59b6',
            fg='white',
            command=self.test_extreme_methods,
            width=25
        ).pack(side=tk.LEFT, padx=10)
        
        # Лог
        tk.Label(
            self.root,
            text="Лог отладки:",
            font=('Cambria', 12),
            fg='white',
            bg='#2c3e50'
        ).pack(pady=(20, 5))
        
        self.log_text = tk.Text(
            self.root,
            height=15,
            width=90,
            font=('Cambria', 10),
            bg='#34495e',
            fg='white'
        )
        self.log_text.pack(pady=5, padx=20, fill=tk.BOTH, expand=True)
        
    def log(self, message):
        """Добавить сообщение в лог"""
        timestamp = time.strftime('%H:%M:%S')
        full_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, full_message)
        self.log_text.see(tk.END)
        safe_print(message)
        
    def update_layout_display(self):
        """Обновить отображение текущей раскладки"""
        layout_id = get_current_layout()
        if layout_id == 0x0419:
            self.layout_label.config(text="Текущая раскладка: РУССКАЯ ✅", fg='#27ae60')
        elif layout_id == 0x0409:
            self.layout_label.config(text="Текущая раскладка: АНГЛИЙСКАЯ ⚠️", fg='#f39c12')
        else:
            self.layout_label.config(text=f"Текущая раскладка: ДРУГАЯ ({hex(layout_id) if layout_id else 'ОШИБКА'})", fg='#e74c3c')
        
        # Автообновление каждые 2 секунды
        self.root.after(2000, self.update_layout_display)
        
    def simulate_login_process(self):
        """Симуляция полного процесса логина"""
        self.log("🚀 НАЧАЛО СИМУЛЯЦИИ ПРОЦЕССА ЛОГИНА")
        self.log("=" * 60)
        
        # Шаг 1: Переключение на английский при открытии логина
        self.log("Шаг 1: Переключение на английский для ввода логина...")
        initial_layout = get_current_layout()
        self.log(f"Начальная раскладка: {hex(initial_layout) if initial_layout else 'ОШИБКА'}")
        
        if switch_to_english():
            self.log("✅ Переключение на английский успешно")
        else:
            self.log("❌ Переключение на английский не удалось")
        
        time.sleep(1)
        english_layout = get_current_layout()
        self.log(f"Раскладка после переключения на английский: {hex(english_layout) if english_layout else 'ОШИБКА'}")
        
        # Шаг 2: Имитация ввода логина/пароля
        self.log("Шаг 2: Имитация ввода логина и пароля...")
        self.username_entry.delete(0, tk.END)
        self.username_entry.insert(0, "admin")
        self.password_entry.delete(0, tk.END)
        self.password_entry.insert(0, "password")
        time.sleep(1)
        
        # Шаг 3: Успешный логин - переключение на русский
        self.log("Шаг 3: Успешный логин - попытка переключения на русский...")
        
        # Базовое переключение
        if switch_to_russian_basic():
            self.log("✅ Базовое переключение на русский успешно")
        else:
            self.log("❌ Базовое переключение на русский не удалось")
        
        time.sleep(1)
        after_basic = get_current_layout()
        self.log(f"Раскладка после базового переключения: {hex(after_basic) if after_basic else 'ОШИБКА'}")
        
        # Если базовое не сработало, пробуем экстремальные методы
        if after_basic != 0x0419:
            self.log("⚠️ Базовое переключение не сработало - запуск экстремальных методов...")
            self.test_extreme_methods()
        
        # Финальная проверка
        final_layout = get_current_layout()
        self.log("=" * 60)
        self.log("📊 РЕЗУЛЬТАТ СИМУЛЯЦИИ ЛОГИНА:")
        self.log(f"Начальная раскладка: {hex(initial_layout) if initial_layout else 'ОШИБКА'}")
        self.log(f"После переключения на английский: {hex(english_layout) if english_layout else 'ОШИБКА'}")
        self.log(f"После переключения на русский: {hex(after_basic) if after_basic else 'ОШИБКА'}")
        self.log(f"Финальная раскладка: {hex(final_layout) if final_layout else 'ОШИБКА'}")
        
        if final_layout == 0x0419:
            self.log("🎉 ПРОБЛЕМА РЕШЕНА! Раскладка успешно переключена на русскую!")
        else:
            self.log("💥 ПРОБЛЕМА ПОДТВЕРЖДЕНА! Не удалось переключить на русскую раскладку!")
            self.log("ЭТО И ЕСТЬ ПРОБЛЕМА ПОЛЬЗОВАТЕЛЯ!")
        
        self.log("=" * 60)
        
    def test_extreme_methods(self):
        """Тестирование экстремальных методов"""
        self.log("🔥 ТЕСТИРОВАНИЕ ЭКСТРЕМАЛЬНЫХ МЕТОДОВ...")
        
        before_extreme = get_current_layout()
        self.log(f"Раскладка до экстремальных методов: {hex(before_extreme) if before_extreme else 'ОШИБКА'}")
        
        # Запуск в отдельном потоке чтобы не блокировать UI
        def run_extreme():
            results = run_extreme_methods()
            
            # Проверка результата
            after_extreme = get_current_layout()
            self.log(f"Раскладка после экстремальных методов: {hex(after_extreme) if after_extreme else 'ОШИБКА'}")
            
            self.log("📊 РЕЗУЛЬТАТЫ ЭКСТРЕМАЛЬНЫХ МЕТОДОВ:")
            for method_name, success in results:
                status = "✅ УСПЕХ" if success else "❌ НЕУДАЧА"
                self.log(f"   {method_name}: {status}")
            
            any_success = any(result[1] for result in results)
            layout_changed = before_extreme != after_extreme and after_extreme == 0x0419
            
            if any_success or layout_changed:
                self.log("🎉 ЭКСТРЕМАЛЬНЫЕ МЕТОДЫ СРАБОТАЛИ!")
            else:
                self.log("💀 ВСЕ ЭКСТРЕМАЛЬНЫЕ МЕТОДЫ НЕ СРАБОТАЛИ!")
        
        threading.Thread(target=run_extreme, daemon=True).start()
        
    def run(self):
        """Запуск окна отладки"""
        self.root.mainloop()

def main():
    """Главная функция"""
    safe_print("🚀 ЗАПУСК ОТЛАДКИ ПРОБЛЕМЫ ПЕРЕКЛЮЧЕНИЯ РАСКЛАДКИ")
    safe_print("=" * 60)
    
    app = LoginDebugWindow()
    app.run()

if __name__ == "__main__":
    main()
