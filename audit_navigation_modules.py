#!/usr/bin/env python3
"""
Audit all navigation menu items to identify missing or broken modules
"""

import sys
import os
import importlib
import inspect

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_module_and_function(module_name, function_name):
    """Check if a module and function exist and are callable"""
    try:
        # Try to import the module
        module = importlib.import_module(module_name)
        
        # Check if function exists
        if hasattr(module, function_name):
            func = getattr(module, function_name)
            if callable(func):
                return True, "✅ Module and function exist and are callable"
            else:
                return False, f"❌ {function_name} exists but is not callable"
        else:
            return False, f"❌ Function {function_name} not found in module"
            
    except ImportError as e:
        return False, f"❌ Module import failed: {e}"
    except Exception as e:
        return False, f"❌ Unexpected error: {e}"

def audit_navigation_items():
    """Audit all navigation menu items from main_window.py"""
    
    # Navigation items from main_window.py
    navigation_items = [
        # ОСНОВНЫЕ МОДУЛИ
        ("📈 Панель Управления", "show_dashboard", "Built-in method"),
        ("🏢 Управление Локациями", "show_multi_location_manager", "modules.multi_location_manager", "create_multi_location_manager"),
        ("🔮 Расширенная Аналитика", "show_advanced_analytics", "modules.advanced_analytics_dashboard", "create_advanced_analytics_dashboard"),
        ("🍳 Кухонный Дисплей", "show_kitchen_display", "modules.kitchen_display_system", "create_kitchen_display_system"),
        ("📊 Данные о Продажах", "show_sales", "modules.sales_data_viewer", "create_sales_data_viewer"),
        ("📥 Импорт Продаж", "show_import_sales", "modules.sales_import", "create_sales_import"),
        
        # СКЛАД И ЗАКУПКИ
        ("📦 Управление Складом", "show_inventory", "modules.inventory_manager", "create_inventory_manager"),
        ("🛒 Управление Закупками", "show_purchasing", "modules.purchasing", "create_purchasing_system"),
        ("🏢 Поставщики", "show_vendor_management", "modules.vendor_management", "create_vendor_manager"),
        
        # КУХНЯ И МЕНЮ
        ("📋 Технологические Карты", "show_recipes", "modules.recipe_manager_working", "create_recipe_manager"),
        ("📋 Планирование Меню", "show_menu_planning", "modules.menu_planning", "create_menu_planning_system"),
        ("📊 Контроль Затрат", "show_cost_control", "modules.cost_control", "create_cost_control_system"),
        ("🔍 Контроль Качества", "show_quality_control", "modules.quality_control", "create_quality_control_system"),
        
        # КЛИЕНТЫ И ПЕРСОНАЛ
        ("👥 CRM Клиенты", "show_customer_crm", "modules.customer_crm", "create_customer_crm"),
        ("🍽️ Управление Столами", "show_table_booking", "modules.table_booking", "create_table_booking_system"),
        ("🗓️ Планирование Смен", "show_staff_scheduling", "modules.staff_scheduling", "create_staff_scheduling"),
        ("💵 Расчёт Зарплаты", "show_payroll", "modules.payroll", "create_payroll_system"),
        
        # ФИНАНСЫ И УЧЁТ
        ("📈 Финансовая Панель", "show_financial_dashboard", "modules.financial_dashboard", "create_financial_dashboard"),
        ("📊 Бухгалтерия", "show_accounting", "modules.accounting", "create_accounting_system"),
        
        # АНАЛИТИКА И ОТЧЁТЫ
        ("📈 Система Отчётов", "show_reports_system", "modules.reports_system", "create_reports_system"),
        ("📊 Расширенная Аналитика", "show_advanced_reports", "modules.advanced_reports", "create_advanced_reports"),
        
        # ИНСТРУМЕНТЫ
        ("🎨 Конфигуратор Стилей", "show_style_configurator", "modules.style_configurator", "create_style_configurator"),
        ("💳 Коды Способов Оплаты", "show_payment_codes", "modules.payment_codes_manager", "create_payment_codes_manager"),
        ("🔒 Безопасность и Аудит", "show_security_audit", "modules.security_audit_system", "create_security_audit_system"),
        ("📱 Мобильная Интеграция", "show_mobile_integration", "modules.mobile_api_integration", "create_mobile_api_integration"),
        ("👥 CRM Система", "show_customer_relationship_management", "modules.customer_relationship_management", "create_customer_relationship_management"),
        ("🎁 Система Лояльности", "show_loyalty_rewards_system", "modules.loyalty_rewards_system", "create_loyalty_rewards_system"),
        ("📊 Бизнес-Аналитика", "show_advanced_business_intelligence", "modules.advanced_business_intelligence", "create_advanced_business_intelligence"),
        ("🍽️ Бронирование Столов", "show_table_reservation_queue_management", "modules.table_reservation_queue_management", "create_table_reservation_queue_management"),
        ("🚚 Управление Поставками", "show_supply_chain_vendor_management", "modules.supply_chain_vendor_management", "create_supply_chain_vendor_management"),
        ("💰 Финансовое Планирование", "show_financial_planning_budgeting", "modules.financial_planning_budgeting", "create_financial_planning_budgeting"),
        ("🔔 Центр Уведомлений", "show_notification_center", "modules.notification_center", "create_notification_center"),
    ]
    
    print("="*80)
    print("AUDIT OF ALL NAVIGATION MENU ITEMS")
    print("="*80)
    
    working_items = []
    broken_items = []
    
    for item in navigation_items:
        if len(item) == 3:  # Built-in method
            menu_text, method_name, description = item
            print(f"\n📋 {menu_text}")
            print(f"   Method: {method_name}")
            print(f"   Status: ✅ Built-in method - {description}")
            working_items.append(item)
        elif len(item) == 4:  # External module
            menu_text, method_name, module_name, function_name = item
            print(f"\n📋 {menu_text}")
            print(f"   Method: {method_name}")
            print(f"   Module: {module_name}")
            print(f"   Function: {function_name}")
            
            success, message = check_module_and_function(module_name, function_name)
            print(f"   Status: {message}")
            
            if success:
                working_items.append(item)
            else:
                broken_items.append(item)
    
    print("\n" + "="*80)
    print("AUDIT SUMMARY")
    print("="*80)
    
    print(f"\n✅ WORKING ITEMS ({len(working_items)}):")
    for item in working_items:
        if len(item) == 3:
            print(f"   - {item[0]} (Built-in)")
        else:
            print(f"   - {item[0]}")
    
    print(f"\n❌ BROKEN ITEMS ({len(broken_items)}):")
    for item in broken_items:
        print(f"   - {item[0]} -> {item[2]}.{item[3]}")
    
    print(f"\n📊 STATISTICS:")
    print(f"   Total Items: {len(navigation_items)}")
    print(f"   Working: {len(working_items)} ({len(working_items)/len(navigation_items)*100:.1f}%)")
    print(f"   Broken: {len(broken_items)} ({len(broken_items)/len(navigation_items)*100:.1f}%)")
    
    return working_items, broken_items

if __name__ == "__main__":
    working, broken = audit_navigation_items()
