"""
Vendor Management Module for Restaurant Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from datetime import datetime, date
from typing import List, Dict, Any
from gui.styles import ModernStyles
from utils.helpers import NumberHelper, ValidationHelper

class VendorManager:
    """Complete vendor and supplier management functionality"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
    
    def create_vendor_window(self):
        """Create vendor management window"""
        window = tk.Toplevel(self.parent)
        window.title("Vendor & Supplier Management")
        window.geometry("1400x900")
        window.configure(bg=ModernStyles.COLORS['bg_main'])
        
        # Main container
        main_frame = tk.Frame(window, **ModernStyles.WIDGET_STYLES['frame_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Title
        title_label = tk.Label(main_frame, text="Vendor & Supplier Management",
                              **ModernStyles.WIDGET_STYLES['label_title'])
        title_label.pack(anchor='w', pady=(0, 20))
        
        # Create notebook for vendor modules
        notebook = ttk.Notebook(main_frame, style="Modern.TNotebook")
        notebook.pack(fill='both', expand=True)
        
        # Vendors Tab
        vendors_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(vendors_frame, text="Vendors")
        self.create_vendors_tab(vendors_frame)
        
        # Purchase Orders Tab
        po_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(po_frame, text="Purchase Orders")
        self.create_purchase_orders_tab(po_frame)
        
        # Vendor Performance Tab
        performance_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(performance_frame, text="Performance")
        self.create_performance_tab(performance_frame)
        
        # Contracts Tab
        contracts_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(contracts_frame, text="Contracts")
        self.create_contracts_tab(contracts_frame)
        
        return window
    
    def create_vendors_tab(self, parent):
        """Create vendor management tab"""
        # Controls frame
        controls_frame = ModernStyles.create_card_frame(parent)
        controls_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(controls_frame, text="Vendor Directory",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Search and filter
        search_frame = tk.Frame(controls_frame, bg=ModernStyles.COLORS['bg_card'])
        search_frame.pack(fill='x', pady=(0, 10))
        
        tk.Label(search_frame, text="Search:",
                **ModernStyles.WIDGET_STYLES['label_body']).pack(side='left')
        
        self.vendor_search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.vendor_search_var,
                               **ModernStyles.WIDGET_STYLES['entry'], width=30)
        search_entry.pack(side='left', padx=(10, 0))
        
        tk.Button(search_frame, text="Search", command=self.search_vendors,
                 **ModernStyles.WIDGET_STYLES['button_secondary']).pack(side='left', padx=(10, 0))
        
        # Buttons
        btn_frame = tk.Frame(controls_frame, bg=ModernStyles.COLORS['bg_card'])
        btn_frame.pack(fill='x')
        
        tk.Button(btn_frame, text="Add Vendor", command=self.add_vendor,
                 **ModernStyles.WIDGET_STYLES['button_success']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Edit Vendor", command=self.edit_vendor,
                 **ModernStyles.WIDGET_STYLES['button_warning']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Deactivate", command=self.deactivate_vendor,
                 **ModernStyles.WIDGET_STYLES['button_danger']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Refresh", command=self.refresh_vendors,
                 **ModernStyles.WIDGET_STYLES['button_primary']).pack(side='left')
        
        # Vendor list
        vendor_frame = ModernStyles.create_card_frame(parent)
        vendor_frame.pack(fill='both', expand=True)
        
        columns = ('Code', 'Name', 'Contact', 'Email', 'Phone', 'City', 'Credit Limit', 'Status')
        self.vendors_tree = ttk.Treeview(vendor_frame, columns=columns, show='headings',
                                        style="Modern.Treeview")
        
        for col in columns:
            self.vendors_tree.heading(col, text=col)
            self.vendors_tree.column(col, width=120)
        
        # Scrollbars
        v_scroll = ttk.Scrollbar(vendor_frame, orient='vertical', command=self.vendors_tree.yview)
        h_scroll = ttk.Scrollbar(vendor_frame, orient='horizontal', command=self.vendors_tree.xview)
        self.vendors_tree.configure(yscrollcommand=v_scroll.set, xscrollcommand=h_scroll.set)
        
        self.vendors_tree.pack(side='left', fill='both', expand=True)
        v_scroll.pack(side='right', fill='y')
        h_scroll.pack(side='bottom', fill='x')
        
        self.refresh_vendors()
    
    def create_purchase_orders_tab(self, parent):
        """Create purchase orders tab"""
        # Controls frame
        controls_frame = ModernStyles.create_card_frame(parent)
        controls_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(controls_frame, text="Purchase Orders Management",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Buttons
        btn_frame = tk.Frame(controls_frame, bg=ModernStyles.COLORS['bg_card'])
        btn_frame.pack(fill='x')
        
        tk.Button(btn_frame, text="Create PO", command=self.create_purchase_order,
                 **ModernStyles.WIDGET_STYLES['button_success']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Receive Items", command=self.receive_items,
                 **ModernStyles.WIDGET_STYLES['button_primary']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Cancel PO", command=self.cancel_purchase_order,
                 **ModernStyles.WIDGET_STYLES['button_danger']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Print PO", command=self.print_purchase_order,
                 **ModernStyles.WIDGET_STYLES['button_secondary']).pack(side='left')
        
        # Purchase orders list
        po_frame = ModernStyles.create_card_frame(parent)
        po_frame.pack(fill='both', expand=True)
        
        columns = ('PO Number', 'Vendor', 'Date', 'Total', 'Status', 'Delivery Date')
        self.po_tree = ttk.Treeview(po_frame, columns=columns, show='headings',
                                   style="Modern.Treeview")
        
        for col in columns:
            self.po_tree.heading(col, text=col)
            self.po_tree.column(col, width=120)
        
        # Scrollbars
        po_v_scroll = ttk.Scrollbar(po_frame, orient='vertical', command=self.po_tree.yview)
        po_h_scroll = ttk.Scrollbar(po_frame, orient='horizontal', command=self.po_tree.xview)
        self.po_tree.configure(yscrollcommand=po_v_scroll.set, xscrollcommand=po_h_scroll.set)
        
        self.po_tree.pack(side='left', fill='both', expand=True)
        po_v_scroll.pack(side='right', fill='y')
        po_h_scroll.pack(side='bottom', fill='x')
    
    def create_performance_tab(self, parent):
        """Create vendor performance tab"""
        # Controls frame
        controls_frame = ModernStyles.create_card_frame(parent)
        controls_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(controls_frame, text="Vendor Performance Analysis",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Performance metrics
        metrics_frame = tk.Frame(controls_frame, bg=ModernStyles.COLORS['bg_card'])
        metrics_frame.pack(fill='x')
        
        # Sample performance metrics
        self.create_performance_metric(metrics_frame, "On-Time Delivery", "95%", ModernStyles.COLORS['success'])
        self.create_performance_metric(metrics_frame, "Quality Score", "4.2/5", ModernStyles.COLORS['primary'])
        self.create_performance_metric(metrics_frame, "Price Competitiveness", "Good", ModernStyles.COLORS['warning'])
        self.create_performance_metric(metrics_frame, "Response Time", "2.1 days", ModernStyles.COLORS['secondary'])
        
        # Performance details
        performance_frame = ModernStyles.create_card_frame(parent)
        performance_frame.pack(fill='both', expand=True)
        
        columns = ('Vendor', 'Orders', 'On-Time %', 'Quality Score', 'Avg Response', 'Rating')
        self.performance_tree = ttk.Treeview(performance_frame, columns=columns, show='headings',
                                           style="Modern.Treeview")
        
        for col in columns:
            self.performance_tree.heading(col, text=col)
            self.performance_tree.column(col, width=120)
        
        # Scrollbars
        perf_v_scroll = ttk.Scrollbar(performance_frame, orient='vertical', command=self.performance_tree.yview)
        perf_h_scroll = ttk.Scrollbar(performance_frame, orient='horizontal', command=self.performance_tree.xview)
        self.performance_tree.configure(yscrollcommand=perf_v_scroll.set, xscrollcommand=perf_h_scroll.set)
        
        self.performance_tree.pack(side='left', fill='both', expand=True)
        perf_v_scroll.pack(side='right', fill='y')
        perf_h_scroll.pack(side='bottom', fill='x')
    
    def create_contracts_tab(self, parent):
        """Create vendor contracts tab"""
        # Controls frame
        controls_frame = ModernStyles.create_card_frame(parent)
        controls_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(controls_frame, text="Vendor Contracts & Agreements",
                **ModernStyles.WIDGET_STYLES['label_heading']).pack(anchor='w', pady=(0, 15))
        
        # Buttons
        btn_frame = tk.Frame(controls_frame, bg=ModernStyles.COLORS['bg_card'])
        btn_frame.pack(fill='x')
        
        tk.Button(btn_frame, text="New Contract", command=self.create_contract,
                 **ModernStyles.WIDGET_STYLES['button_success']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Renew Contract", command=self.renew_contract,
                 **ModernStyles.WIDGET_STYLES['button_primary']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="View Details", command=self.view_contract_details,
                 **ModernStyles.WIDGET_STYLES['button_secondary']).pack(side='left')
        
        # Contracts list
        contracts_frame = ModernStyles.create_card_frame(parent)
        contracts_frame.pack(fill='both', expand=True)
        
        columns = ('Contract #', 'Vendor', 'Start Date', 'End Date', 'Value', 'Status')
        self.contracts_tree = ttk.Treeview(contracts_frame, columns=columns, show='headings',
                                          style="Modern.Treeview")
        
        for col in columns:
            self.contracts_tree.heading(col, text=col)
            self.contracts_tree.column(col, width=120)
        
        # Scrollbars
        cont_v_scroll = ttk.Scrollbar(contracts_frame, orient='vertical', command=self.contracts_tree.yview)
        cont_h_scroll = ttk.Scrollbar(contracts_frame, orient='horizontal', command=self.contracts_tree.xview)
        self.contracts_tree.configure(yscrollcommand=cont_v_scroll.set, xscrollcommand=cont_h_scroll.set)
        
        self.contracts_tree.pack(side='left', fill='both', expand=True)
        cont_v_scroll.pack(side='right', fill='y')
        cont_h_scroll.pack(side='bottom', fill='x')
    
    def create_performance_metric(self, parent, label, value, color):
        """Create performance metric display"""
        metric_frame = tk.Frame(parent, bg=color, relief='flat', bd=0)
        metric_frame.pack(side='left', fill='both', expand=True, padx=5)
        
        inner_frame = tk.Frame(metric_frame, bg=color)
        inner_frame.pack(fill='both', expand=True, padx=15, pady=15)
        
        tk.Label(inner_frame, text=value, font=ModernStyles.FONTS['heading'],
                fg=ModernStyles.COLORS['text_white'], bg=color).pack()
        
        tk.Label(inner_frame, text=label, font=ModernStyles.FONTS['body'],
                fg=ModernStyles.COLORS['text_white'], bg=color).pack()
    
    # Vendor Management Methods
    def add_vendor(self):
        """Add new vendor"""
        dialog = VendorDialog(self.parent, "Add Vendor", self.db_manager)
        if dialog.result:
            self.refresh_vendors()
    
    def edit_vendor(self):
        """Edit selected vendor"""
        selection = self.vendors_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите поставщика для редактирования.")
            return

        # Получить данные выбранного поставщика
        item = self.vendors_tree.item(selection[0])
        vendor_code = item['values'][0]

        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT vendor_code, vendor_name, contact_person, email, phone,
                           address, city, credit_limit, payment_terms, is_active
                    FROM vendors WHERE vendor_code = ?
                ''', (vendor_code,))

                vendor_data = cursor.fetchone()
                if not vendor_data:
                    messagebox.showerror("Ошибка", "Поставщик не найден")
                    return
        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка получения данных поставщика: {e}")
            return

        # Создать диалог редактирования
        try:
            from utils.window_utils import create_centered_dialog
            edit_window = create_centered_dialog(
                self.parent,
                f"✏️ Редактировать поставщика: {vendor_data[1]}",
                width=700,
                height=600,
                resizable=True
            )
        except ImportError:
            edit_window = tk.Toplevel(self.parent)
            edit_window.title(f"✏️ Редактировать поставщика: {vendor_data[1]}")
            edit_window.geometry("700x600")
            edit_window.configure(bg='white')
            edit_window.resizable(True, True)

            # Центрировать окно
            edit_window.update_idletasks()
            x = (edit_window.winfo_screenwidth() // 2) - (700 // 2)
            y = (edit_window.winfo_screenheight() // 2) - (600 // 2)
            edit_window.geometry(f"700x600+{x}+{y}")

        # Заголовок
        tk.Label(edit_window, text="Редактировать данные поставщика",
                font=('Cambria', 18, 'bold italic'), bg='white').pack(pady=20)

        # Форма
        form_frame = tk.Frame(edit_window, bg='white')
        form_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # Поля формы с предзаполненными данными
        fields = [
            ("Код поставщика:", vendor_data[0], False),  # Не редактируемое
            ("Название:", vendor_data[1], True),
            ("Контактное лицо:", vendor_data[2] or "", True),
            ("Email:", vendor_data[3] or "", True),
            ("Телефон:", vendor_data[4] or "", True),
            ("Адрес:", vendor_data[5] or "", True),
            ("Город:", vendor_data[6] or "", True),
            ("Кредитный лимит:", str(vendor_data[7] or 0), True),
            ("Условия оплаты:", vendor_data[8] or "", True)
        ]

        entries = {}

        for i, (label_text, default_value, editable) in enumerate(fields):
            tk.Label(form_frame, text=label_text, font=('Cambria', 12, 'bold'),
                    bg='white').grid(row=i, column=0, sticky='w', pady=8, padx=(0, 10))

            if editable:
                entry = tk.Entry(form_frame, font=('Cambria', 12), width=40)
                entry.grid(row=i, column=1, sticky='ew', pady=8)
                entry.insert(0, default_value)
                entries[i] = entry
            else:
                tk.Label(form_frame, text=default_value, font=('Cambria', 12),
                        bg='#f0f0f0', relief='sunken').grid(row=i, column=1, sticky='ew', pady=8)

        # Статус активности
        tk.Label(form_frame, text="Статус:", font=('Cambria', 12, 'bold'),
                bg='white').grid(row=len(fields), column=0, sticky='w', pady=8, padx=(0, 10))

        status_var = tk.BooleanVar(value=vendor_data[9])
        status_check = tk.Checkbutton(form_frame, text="Активный поставщик",
                                     variable=status_var, font=('Cambria', 12), bg='white')
        status_check.grid(row=len(fields), column=1, sticky='w', pady=8)

        # Настройка сетки
        form_frame.grid_columnconfigure(1, weight=1)

        # Кнопки
        btn_frame = tk.Frame(edit_window, bg='white')
        btn_frame.pack(fill='x', padx=30, pady=20)

        def save_changes():
            try:
                # Получить данные из формы
                new_data = [
                    vendor_data[0],  # Код не изменяется
                    entries[1].get().strip(),  # Название
                    entries[2].get().strip(),  # Контактное лицо
                    entries[3].get().strip(),  # Email
                    entries[4].get().strip(),  # Телефон
                    entries[5].get().strip(),  # Адрес
                    entries[6].get().strip(),  # Город
                    float(entries[7].get() or 0),  # Кредитный лимит
                    entries[8].get().strip(),  # Условия оплаты
                    status_var.get()  # Статус
                ]

                # Валидация
                if not new_data[1]:
                    messagebox.showerror("Ошибка", "Введите название поставщика")
                    return

                # Обновить в базе данных
                with self.db_manager.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        UPDATE vendors SET
                            vendor_name = ?, contact_person = ?, email = ?, phone = ?,
                            address = ?, city = ?, credit_limit = ?, payment_terms = ?, is_active = ?
                        WHERE vendor_code = ?
                    ''', (new_data[1], new_data[2], new_data[3], new_data[4],
                          new_data[5], new_data[6], new_data[7], new_data[8],
                          new_data[9], new_data[0]))
                    conn.commit()

                # Обновить отображение
                self.refresh_vendors()
                edit_window.destroy()
                messagebox.showinfo("Успех", f"Данные поставщика '{new_data[1]}' обновлены!")

            except ValueError:
                messagebox.showerror("Ошибка", "Проверьте правильность введённых числовых значений")
            except Exception as e:
                messagebox.showerror("Ошибка", f"Ошибка при обновлении поставщика: {e}")

        tk.Button(btn_frame, text="💾 Сохранить изменения", command=save_changes,
                 bg='#27ae60', fg='white', font=('Cambria', 12, 'bold italic'),
                 relief='flat', padx=30, pady=10).pack(side='left')

        tk.Button(btn_frame, text="❌ Отмена", command=edit_window.destroy,
                 bg='#e74c3c', fg='white', font=('Cambria', 12, 'bold italic'),
                 relief='flat', padx=30, pady=10).pack(side='right')
    
    def deactivate_vendor(self):
        """Deactivate selected vendor"""
        selection = self.vendors_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите поставщика для деактивации.")
            return

        # Получить данные выбранного поставщика
        item = self.vendors_tree.item(selection[0])
        vendor_code = item['values'][0]
        vendor_name = item['values'][1]
        current_status = item['values'][7]

        # Определить действие
        if current_status == "Active":
            action = "деактивировать"
            new_status = False
            message = f"Вы уверены, что хотите деактивировать поставщика '{vendor_name}'?\n\nДеактивированный поставщик не будет отображаться в списках для новых заказов."
        else:
            action = "активировать"
            new_status = True
            message = f"Вы уверены, что хотите активировать поставщика '{vendor_name}'?"

        if messagebox.askyesno("Подтверждение", message):
            try:
                # Обновить статус в базе данных
                with self.db_manager.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        UPDATE vendors SET is_active = ? WHERE vendor_code = ?
                    ''', (new_status, vendor_code))
                    conn.commit()

                # Обновить отображение
                self.refresh_vendors()

                status_text = "активирован" if new_status else "деактивирован"
                messagebox.showinfo("Успех", f"Поставщик '{vendor_name}' {status_text}!")

            except Exception as e:
                messagebox.showerror("Ошибка", f"Ошибка при изменении статуса поставщика: {e}")
    
    def search_vendors(self):
        """Search vendors"""
        search_term = self.vendor_search_var.get()
        if search_term:
            messagebox.showinfo("Search", f"Searching for: {search_term}")
        else:
            self.refresh_vendors()
    
    def refresh_vendors(self):
        """Refresh vendor list"""
        try:
            # Clear existing data
            for item in self.vendors_tree.get_children():
                self.vendors_tree.delete(item)
            
            # Get vendors from database
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT vendor_code, vendor_name, contact_person, email, phone, 
                           city, credit_limit, is_active
                    FROM vendors
                    ORDER BY vendor_name
                ''')
                
                vendors = cursor.fetchall()
                
                for vendor in vendors:
                    status = "Active" if vendor[7] else "Inactive"
                    credit_limit = NumberHelper.format_currency(vendor[6])
                    
                    self.vendors_tree.insert('', 'end', values=(
                        vendor[0],  # Code
                        vendor[1],  # Name
                        vendor[2] or '',  # Contact
                        vendor[3] or '',  # Email
                        vendor[4] or '',  # Phone
                        vendor[5] or '',  # City
                        credit_limit,     # Credit Limit
                        status           # Status
                    ))
        
        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh vendors: {str(e)}")
    
    # Purchase Order Methods
    def create_purchase_order(self):
        """Create new purchase order"""
        messagebox.showinfo("Feature", "Purchase order creation will be implemented.")
    
    def receive_items(self):
        """Receive items from purchase order"""
        messagebox.showinfo("Feature", "Item receiving will be implemented.")
    
    def cancel_purchase_order(self):
        """Cancel selected purchase order"""
        messagebox.showinfo("Feature", "Purchase order cancellation will be implemented.")
    
    def print_purchase_order(self):
        """Print selected purchase order"""
        messagebox.showinfo("Feature", "Purchase order printing will be implemented.")
    
    # Contract Methods
    def create_contract(self):
        """Create new vendor contract"""
        messagebox.showinfo("Feature", "Contract creation will be implemented.")
    
    def renew_contract(self):
        """Renew selected contract"""
        messagebox.showinfo("Feature", "Contract renewal will be implemented.")
    
    def view_contract_details(self):
        """View contract details"""
        messagebox.showinfo("Feature", "Contract details view will be implemented.")

class VendorDialog:
    """Dialog for adding/editing vendors"""
    
    def __init__(self, parent, title="Vendor", db_manager=None):
        self.result = None
        self.db_manager = db_manager
        self.create_dialog(parent, title)
    
    def create_dialog(self, parent, title):
        """Create the vendor dialog"""
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x700")
        self.dialog.configure(bg=ModernStyles.COLORS['bg_main'])
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Main frame
        main_frame = tk.Frame(self.dialog, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Form fields
        self.fields = {}
        field_configs = [
            ('vendor_code', 'Vendor Code*', 'text'),
            ('vendor_name', 'Vendor Name*', 'text'),
            ('contact_person', 'Contact Person', 'text'),
            ('email', 'Email', 'text'),
            ('phone', 'Phone', 'text'),
            ('address', 'Address', 'text'),
            ('city', 'City', 'text'),
            ('state', 'State', 'text'),
            ('zip_code', 'ZIP Code', 'text'),
            ('country', 'Country', 'text'),
            ('tax_id', 'Tax ID', 'text'),
            ('payment_terms', 'Payment Terms', 'text'),
            ('credit_limit', 'Credit Limit', 'float')
        ]
        
        for field_key, field_label, field_type in field_configs:
            # Label
            tk.Label(main_frame, text=field_label,
                    **ModernStyles.WIDGET_STYLES['label_body']).pack(anchor='w', pady=(10, 0))
            
            # Entry
            var = tk.StringVar()
            entry = tk.Entry(main_frame, textvariable=var,
                           **ModernStyles.WIDGET_STYLES['entry'])
            entry.pack(fill='x', pady=(0, 5))
            
            self.fields[field_key] = {'var': var, 'type': field_type}
        
        # Buttons
        button_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_main'])
        button_frame.pack(fill='x', pady=(20, 0))
        
        tk.Button(button_frame, text="Save", command=self.save_vendor,
                 **ModernStyles.WIDGET_STYLES['button_success']).pack(side='right')
        
        tk.Button(button_frame, text="Cancel", command=self.dialog.destroy,
                 **ModernStyles.WIDGET_STYLES['button_secondary']).pack(side='right', padx=(0, 10))
    
    def save_vendor(self):
        """Save the vendor"""
        try:
            # Validate required fields
            required_fields = ['vendor_code', 'vendor_name']
            for field in required_fields:
                if not self.fields[field]['var'].get().strip():
                    messagebox.showerror("Error", f"{field.replace('_', ' ').title()} is required.")
                    return
            
            # Validate email if provided
            email = self.fields['email']['var'].get().strip()
            if email and not ValidationHelper.validate_email(email):
                messagebox.showerror("Error", "Please enter a valid email address.")
                return
            
            # Collect data
            vendor_data = {}
            for field_key, field_data in self.fields.items():
                value = field_data['var'].get().strip()
                
                if field_data['type'] == 'float':
                    try:
                        vendor_data[field_key] = float(value) if value else 0.0
                    except ValueError:
                        messagebox.showerror("Error", f"Invalid numeric value for {field_key}")
                        return
                else:
                    vendor_data[field_key] = value
            
            # Insert into database
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO vendors 
                    (vendor_code, vendor_name, contact_person, email, phone, address,
                     city, state, zip_code, country, tax_id, payment_terms, credit_limit)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    vendor_data['vendor_code'],
                    vendor_data['vendor_name'],
                    vendor_data['contact_person'],
                    vendor_data['email'],
                    vendor_data['phone'],
                    vendor_data['address'],
                    vendor_data['city'],
                    vendor_data['state'],
                    vendor_data['zip_code'],
                    vendor_data['country'],
                    vendor_data['tax_id'],
                    vendor_data['payment_terms'],
                    vendor_data['credit_limit']
                ))
                conn.commit()
            
            self.result = vendor_data
            self.dialog.destroy()
            messagebox.showinfo("Success", "Vendor added successfully.")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save vendor: {str(e)}")


def create_vendor_manager(parent, db_manager):
    """Create and show the vendor management window"""
    manager = VendorManager(parent, db_manager)
    return manager.create_vendor_window()
