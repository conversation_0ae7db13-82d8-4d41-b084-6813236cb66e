#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ФИНАЛЬНЫЙ ТЕСТ ИСПРАВЛЕНИЯ ПРОБЛЕМЫ ПЕРЕКЛЮЧЕНИЯ РАСКЛАДКИ
Проверяет что исправление работает корректно
"""

import ctypes
import time
import subprocess
import sys
import os

def safe_print(text):
    """Безопасный вывод с обработкой Unicode ошибок"""
    try:
        print(text)
    except UnicodeEncodeError:
        ascii_text = text.encode('ascii', 'ignore').decode('ascii')
        print(ascii_text)

def get_current_layout():
    """Получить текущую раскладку клавиатуры"""
    try:
        user32 = ctypes.windll.user32
        hwnd = user32.GetForegroundWindow()
        thread_id = user32.GetWindowThreadProcessId(hwnd, None)
        hkl = user32.GetKeyboardLayout(thread_id)
        layout_id = hkl & 0xFFFF
        return layout_id
    except:
        return None

def test_keyboard_switching():
    """Тест переключения раскладки"""
    safe_print("🚀 ФИНАЛЬНЫЙ ТЕСТ ИСПРАВЛЕНИЯ ПРОБЛЕМЫ ПЕРЕКЛЮЧЕНИЯ РАСКЛАДКИ")
    safe_print("=" * 70)
    
    # Проверка начального состояния
    initial_layout = get_current_layout()
    safe_print(f"Начальная раскладка: {hex(initial_layout) if initial_layout else 'ОШИБКА'}")
    
    # Тест 1: Базовое переключение на русский
    safe_print("\n📋 ТЕСТ 1: Базовое переключение на русский...")
    try:
        user32 = ctypes.windll.user32
        hkl_russian = user32.LoadKeyboardLayoutW("00000419", 0x00000001)
        if hkl_russian:
            result = user32.ActivateKeyboardLayout(hkl_russian, 0x00000008)
            if result:
                safe_print("✅ Базовое переключение успешно")
            else:
                safe_print("❌ Базовое переключение не удалось")
        else:
            safe_print("❌ Не удалось загрузить русскую раскладку")
    except Exception as e:
        safe_print(f"❌ Ошибка базового переключения: {e}")
    
    time.sleep(1)
    after_basic = get_current_layout()
    safe_print(f"Раскладка после базового переключения: {hex(after_basic) if after_basic else 'ОШИБКА'}")
    
    # Тест 2: Проверка экстремальных методов
    safe_print("\n📋 ТЕСТ 2: Проверка экстремальных методов...")
    
    extreme_methods = [
        ("extreme_keyboard_hook.py", "Экстремальный хук"),
        ("nuclear_keyboard_switch.py", "Ядерный подход"),
        ("ultimate_driver_approach.py", "Драйверный подход")
    ]
    
    for filename, method_name in extreme_methods:
        if os.path.exists(filename):
            safe_print(f"   ✅ {method_name}: файл найден")
            
            # Проверяем на наличие Unicode ошибок
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'safe_safe_print' in content:
                        safe_print(f"   ❌ {method_name}: содержит ошибки (safe_safe_print)")
                    elif 'safe_print' in content:
                        safe_print(f"   ✅ {method_name}: Unicode ошибки исправлены")
                    else:
                        safe_print(f"   ⚠️ {method_name}: нет функции safe_print")
            except Exception as e:
                safe_print(f"   ❌ {method_name}: ошибка чтения файла: {e}")
        else:
            safe_print(f"   ❌ {method_name}: файл не найден")
    
    # Тест 3: Проверка login_window.py
    safe_print("\n📋 ТЕСТ 3: Проверка исправлений в login_window.py...")
    try:
        with open("gui/login_window.py", 'r', encoding='utf-8') as f:
            content = f.read()
            
            if '_close_login_and_switch_keyboard' in content:
                safe_print("   ✅ Новый метод _close_login_and_switch_keyboard найден")
            else:
                safe_print("   ❌ Новый метод _close_login_and_switch_keyboard НЕ найден")
            
            if 'switch_to_russian_keyboard_final' in content:
                safe_print("   ✅ Метод switch_to_russian_keyboard_final найден")
            else:
                safe_print("   ❌ Метод switch_to_russian_keyboard_final НЕ найден")
            
            if 'pending_user_info' in content:
                safe_print("   ✅ Переменная pending_user_info найдена")
            else:
                safe_print("   ❌ Переменная pending_user_info НЕ найдена")
                
    except Exception as e:
        safe_print(f"   ❌ Ошибка проверки login_window.py: {e}")
    
    # Тест 4: Симуляция исправленного процесса логина
    safe_print("\n📋 ТЕСТ 4: Симуляция исправленного процесса логина...")
    
    # Переключаем на английский (как при открытии логина)
    try:
        user32 = ctypes.windll.user32
        hkl_english = user32.LoadKeyboardLayoutW("00000409", 0x00000001)
        if hkl_english:
            user32.ActivateKeyboardLayout(hkl_english, 0x00000008)
            safe_print("   ✅ Переключение на английский для логина")
    except:
        safe_print("   ❌ Не удалось переключить на английский")
    
    time.sleep(1)
    english_layout = get_current_layout()
    safe_print(f"   Раскладка для логина: {hex(english_layout) if english_layout else 'ОШИБКА'}")
    
    # Имитируем закрытие окна логина и переключение
    safe_print("   Имитация: окно логина закрыто...")
    time.sleep(0.5)
    
    # Переключаем на русский (как в исправленном методе)
    try:
        hkl_russian = user32.LoadKeyboardLayoutW("00000419", 0x00000001)
        if hkl_russian:
            result = user32.ActivateKeyboardLayout(hkl_russian, 0x00000008)
            if result:
                safe_print("   ✅ Переключение на русский после логина")
            else:
                safe_print("   ❌ Переключение на русский не удалось")
    except Exception as e:
        safe_print(f"   ❌ Ошибка переключения на русский: {e}")
    
    time.sleep(1)
    final_layout = get_current_layout()
    safe_print(f"   Финальная раскладка: {hex(final_layout) if final_layout else 'ОШИБКА'}")
    
    # Финальный результат
    safe_print("\n" + "=" * 70)
    safe_print("📊 РЕЗУЛЬТАТ ФИНАЛЬНОГО ТЕСТА:")
    safe_print(f"Начальная раскладка: {hex(initial_layout) if initial_layout else 'ОШИБКА'}")
    safe_print(f"После базового переключения: {hex(after_basic) if after_basic else 'ОШИБКА'}")
    safe_print(f"После симуляции логина: {hex(final_layout) if final_layout else 'ОШИБКА'}")
    
    if final_layout == 0x0419:
        safe_print("🎉 ИСПРАВЛЕНИЕ РАБОТАЕТ! Раскладка успешно переключается на русскую!")
        safe_print("✅ Проблема пользователя должна быть решена")
    else:
        safe_print("💥 ПРОБЛЕМА ОСТАЕТСЯ! Раскладка не переключается на русскую")
        safe_print("❌ Требуется дополнительная диагностика")
    
    safe_print("=" * 70)

def main():
    """Главная функция"""
    test_keyboard_switching()

if __name__ == "__main__":
    main()
