"""
Mobile-Responsive Web Interface for Restaurant Management System
Create a web-based interface that works on tablets and mobile devices for managers 
to access key functions remotely, including order monitoring and basic management tasks.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import json
import sqlite3
from datetime import datetime, timedelta
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import os
import base64
from gui.styles import ModernStyles
from utils.helpers import log_info, handle_module_error

class MobileWebHandler(BaseHTTPRequestHandler):
    """HTTP request handler for mobile web interface"""
    
    def __init__(self, *args, db_manager=None, **kwargs):
        self.db_manager = db_manager
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """Handle GET requests"""
        try:
            parsed_path = urlparse(self.path)
            path = parsed_path.path
            
            # Serve static files
            if path.startswith('/static/'):
                self.serve_static_file(path)
            # API endpoints
            elif path.startswith('/api/'):
                self.handle_api_request(path)
            # Main mobile app
            elif path == '/' or path == '/index.html':
                self.serve_mobile_app()
            # Dashboard page
            elif path == '/dashboard':
                self.serve_dashboard_page()
            # Sales monitoring page
            elif path == '/sales':
                self.serve_sales_page()
            # Inventory page
            elif path == '/inventory':
                self.serve_inventory_page()
            # Orders page
            elif path == '/orders':
                self.serve_orders_page()
            else:
                self.send_error(404, "Page not found")
                
        except Exception as e:
            self.send_error(500, f"Server error: {str(e)}")
    
    def do_POST(self):
        """Handle POST requests"""
        try:
            parsed_path = urlparse(self.path)
            path = parsed_path.path
            
            if path.startswith('/api/'):
                self.handle_api_post_request(path)
            else:
                self.send_error(404, "Endpoint not found")
                
        except Exception as e:
            self.send_error(500, f"Server error: {str(e)}")
    
    def serve_mobile_app(self):
        """Serve main mobile application HTML"""
        html_content = self.get_mobile_app_html()
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.send_header('Content-Length', str(len(html_content.encode('utf-8'))))
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))
    
    def serve_dashboard_page(self):
        """Serve dashboard page"""
        html_content = self.get_dashboard_html()
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.send_header('Content-Length', str(len(html_content.encode('utf-8'))))
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))
    
    def serve_sales_page(self):
        """Serve sales monitoring page"""
        html_content = self.get_sales_html()
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.send_header('Content-Length', str(len(html_content.encode('utf-8'))))
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))
    
    def serve_inventory_page(self):
        """Serve inventory page"""
        html_content = self.get_inventory_html()
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.send_header('Content-Length', str(len(html_content.encode('utf-8'))))
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))
    
    def serve_orders_page(self):
        """Serve orders page"""
        html_content = self.get_orders_html()
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.send_header('Content-Length', str(len(html_content.encode('utf-8'))))
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))
    
    def serve_static_file(self, path):
        """Serve static files (CSS, JS, images)"""
        try:
            # Remove /static/ prefix
            file_path = path[8:]
            
            if file_path == 'style.css':
                content = self.get_mobile_css()
                content_type = 'text/css'
            elif file_path == 'app.js':
                content = self.get_mobile_js()
                content_type = 'application/javascript'
            else:
                self.send_error(404, "File not found")
                return
            
            self.send_response(200)
            self.send_header('Content-type', content_type)
            self.send_header('Content-Length', str(len(content.encode('utf-8'))))
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
            
        except Exception as e:
            self.send_error(500, f"Error serving static file: {str(e)}")
    
    def handle_api_request(self, path):
        """Handle API GET requests"""
        try:
            if path == '/api/dashboard':
                data = self.get_dashboard_data()
            elif path == '/api/sales/today':
                data = self.get_sales_today_data()
            elif path == '/api/sales/summary':
                data = self.get_sales_summary_data()
            elif path == '/api/inventory/alerts':
                data = self.get_inventory_alerts_data()
            elif path == '/api/orders/active':
                data = self.get_active_orders_data()
            else:
                self.send_error(404, "API endpoint not found")
                return
            
            # Send JSON response
            json_data = json.dumps(data, ensure_ascii=False, indent=2)
            self.send_response(200)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Content-Length', str(len(json_data.encode('utf-8'))))
            self.end_headers()
            self.wfile.write(json_data.encode('utf-8'))
            
        except Exception as e:
            error_data = {"error": str(e), "status": "error"}
            json_data = json.dumps(error_data, ensure_ascii=False)
            self.send_response(500)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.send_header('Content-Length', str(len(json_data.encode('utf-8'))))
            self.end_headers()
            self.wfile.write(json_data.encode('utf-8'))
    
    def handle_api_post_request(self, path):
        """Handle API POST requests"""
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            if path == '/api/orders/update':
                result = self.update_order(data)
            elif path == '/api/inventory/adjust':
                result = self.adjust_inventory(data)
            elif path == '/api/notifications/send':
                result = self.send_notification(data)
            else:
                self.send_error(404, "API endpoint not found")
                return
            
            # Send JSON response
            json_data = json.dumps(result, ensure_ascii=False, indent=2)
            self.send_response(200)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Content-Length', str(len(json_data.encode('utf-8'))))
            self.end_headers()
            self.wfile.write(json_data.encode('utf-8'))
            
        except Exception as e:
            error_data = {"error": str(e), "status": "error"}
            json_data = json.dumps(error_data, ensure_ascii=False)
            self.send_response(500)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.send_header('Content-Length', str(len(json_data.encode('utf-8'))))
            self.end_headers()
            self.wfile.write(json_data.encode('utf-8'))


class MobileWebInterface:
    """Mobile-Responsive Web Interface System"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.web_server = None
        self.server_thread = None
        self.server_running = False
        self.server_port = 8080
        self.server_host = '0.0.0.0'
        
        # Web interface configuration
        self.web_config = {
            "enabled": True,
            "port": 8080,
            "host": "0.0.0.0",
            "auto_start": False,
            "responsive_design": True,
            "offline_support": False,
            "push_notifications": True,
            "dark_mode": True
        }
        
        # Mobile features configuration
        self.mobile_features = {
            "dashboard_access": True,
            "sales_monitoring": True,
            "inventory_alerts": True,
            "order_management": True,
            "basic_reporting": True,
            "notifications": True,
            "real_time_updates": True,
            "touch_optimized": True
        }
        
        self.create_interface()
    
    def create_interface(self):
        """Create mobile web interface management window"""
        try:
            self.window = tk.Toplevel(self.parent)
            self.window.title("📱 Мобильный Веб-Интерфейс")
            self.window.geometry("1000x700")
            self.window.configure(bg=ModernStyles.COLORS['bg_main'])
            
            # Make window resizable
            self.window.resizable(True, True)
            
            # Center window
            self.window.transient(self.parent)
            self.window.grab_set()
            
            # Create main interface
            self.create_header()
            self.create_main_content()
            self.create_control_panel()
            
            log_info("Мобильный веб-интерфейс создан", "MobileWebInterface")
            
        except Exception as e:
            handle_module_error(e, "Мобильный веб-интерфейс", "создание интерфейса")
    
    def create_header(self):
        """Create header section"""
        try:
            header_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
            header_frame.pack(fill='x', pady=20)
            
            # Title
            title_label = tk.Label(header_frame, text="📱 Мобильный Веб-Интерфейс",
                                 font=('Cambria', 24, 'bold'),
                                 fg=ModernStyles.COLORS['primary'],
                                 bg=ModernStyles.COLORS['bg_main'])
            title_label.pack()
            
            # Subtitle
            subtitle_label = tk.Label(header_frame, 
                                    text="Адаптивный веб-интерфейс для планшетов и мобильных устройств",
                                    font=('Cambria', 14),
                                    fg=ModernStyles.COLORS['text_secondary'],
                                    bg=ModernStyles.COLORS['bg_main'])
            subtitle_label.pack(pady=(5, 0))
            
        except Exception as e:
            handle_module_error(e, "Мобильный веб-интерфейс", "создание заголовка")

    def create_main_content(self):
        """Create main content area"""
        try:
            # Create notebook for tabs
            self.notebook = ttk.Notebook(self.window)
            self.notebook.pack(fill='both', expand=True, padx=20, pady=10)

            # Server Status Tab
            self.create_server_status_tab()

            # Mobile Features Tab
            self.create_mobile_features_tab()

            # Configuration Tab
            self.create_configuration_tab()

            # Preview Tab
            self.create_preview_tab()

        except Exception as e:
            handle_module_error(e, "Мобильный веб-интерфейс", "создание основного содержимого")

    def create_server_status_tab(self):
        """Create server status and control tab"""
        try:
            status_frame = tk.Frame(self.notebook, bg=ModernStyles.COLORS['bg_secondary'])
            self.notebook.add(status_frame, text="🖥️ Сервер")

            # Server status section
            status_section = tk.LabelFrame(status_frame, text="📊 Статус Сервера",
                                         font=('Cambria', 14, 'bold'),
                                         fg=ModernStyles.COLORS['primary'],
                                         bg=ModernStyles.COLORS['bg_secondary'])
            status_section.pack(fill='x', padx=20, pady=20)

            # Status display
            self.status_text = tk.Text(status_section, height=8, width=80,
                                     font=('Cambria', 11),
                                     bg=ModernStyles.COLORS['bg_main'],
                                     fg=ModernStyles.COLORS['text_primary'],
                                     wrap=tk.WORD, state='disabled')
            self.status_text.pack(padx=20, pady=20)

            # Control buttons
            control_frame = tk.Frame(status_section, bg=ModernStyles.COLORS['bg_secondary'])
            control_frame.pack(fill='x', padx=20, pady=(0, 20))

            self.start_btn = tk.Button(control_frame, text="▶️ Запустить Сервер",
                                     command=self.start_server,
                                     bg=ModernStyles.COLORS['success'], fg='white',
                                     font=('Cambria', 12, 'bold'), relief='flat',
                                     padx=20, pady=10)
            self.start_btn.pack(side='left', padx=(0, 10))

            self.stop_btn = tk.Button(control_frame, text="⏹️ Остановить Сервер",
                                    command=self.stop_server,
                                    bg=ModernStyles.COLORS['danger'], fg='white',
                                    font=('Cambria', 12, 'bold'), relief='flat',
                                    padx=20, pady=10, state='disabled')
            self.stop_btn.pack(side='left', padx=(0, 10))

            self.refresh_btn = tk.Button(control_frame, text="🔄 Обновить",
                                       command=self.refresh_status,
                                       bg=ModernStyles.COLORS['info'], fg='white',
                                       font=('Cambria', 12, 'bold'), relief='flat',
                                       padx=20, pady=10)
            self.refresh_btn.pack(side='left')

            # Update status display
            self.refresh_status()

        except Exception as e:
            handle_module_error(e, "Мобильный веб-интерфейс", "создание вкладки статуса сервера")

    def create_mobile_features_tab(self):
        """Create mobile features configuration tab"""
        try:
            features_frame = tk.Frame(self.notebook, bg=ModernStyles.COLORS['bg_secondary'])
            self.notebook.add(features_frame, text="📱 Функции")

            # Features section
            features_section = tk.LabelFrame(features_frame, text="📱 Мобильные Функции",
                                           font=('Cambria', 14, 'bold'),
                                           fg=ModernStyles.COLORS['primary'],
                                           bg=ModernStyles.COLORS['bg_secondary'])
            features_section.pack(fill='both', expand=True, padx=20, pady=20)

            # Create scrollable frame
            canvas = tk.Canvas(features_section, bg=ModernStyles.COLORS['bg_secondary'])
            scrollbar = ttk.Scrollbar(features_section, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg=ModernStyles.COLORS['bg_secondary'])

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            canvas.pack(side="left", fill="both", expand=True, padx=20, pady=20)
            scrollbar.pack(side="right", fill="y")

            # Feature checkboxes
            self.feature_vars = {}

            features_list = [
                ("dashboard_access", "📊 Доступ к Панели Управления", "Основная панель с ключевыми метриками"),
                ("sales_monitoring", "💰 Мониторинг Продаж", "Отслеживание продаж в реальном времени"),
                ("inventory_alerts", "📦 Уведомления о Складе", "Предупреждения о низких остатках"),
                ("order_management", "🍽️ Управление Заказами", "Просмотр и обновление активных заказов"),
                ("basic_reporting", "📈 Базовая Отчетность", "Простые отчеты и аналитика"),
                ("notifications", "🔔 Уведомления", "Push-уведомления о важных событиях"),
                ("real_time_updates", "⚡ Обновления в Реальном Времени", "Автоматическое обновление данных"),
                ("touch_optimized", "👆 Оптимизация для Сенсорных Экранов", "Интерфейс, адаптированный для касаний")
            ]

            for i, (key, title, description) in enumerate(features_list):
                feature_frame = tk.Frame(scrollable_frame, bg=ModernStyles.COLORS['bg_secondary'])
                feature_frame.pack(fill='x', padx=10, pady=5)

                var = tk.BooleanVar(value=self.mobile_features.get(key, True))
                self.feature_vars[key] = var

                checkbox = tk.Checkbutton(feature_frame, text=title,
                                        variable=var,
                                        font=('Cambria', 12, 'bold'),
                                        fg=ModernStyles.COLORS['text_primary'],
                                        bg=ModernStyles.COLORS['bg_secondary'],
                                        command=lambda k=key: self.update_feature(k))
                checkbox.pack(anchor='w')

                desc_label = tk.Label(feature_frame, text=description,
                                    font=('Cambria', 10),
                                    fg=ModernStyles.COLORS['text_secondary'],
                                    bg=ModernStyles.COLORS['bg_secondary'])
                desc_label.pack(anchor='w', padx=(25, 0))

        except Exception as e:
            handle_module_error(e, "Мобильный веб-интерфейс", "создание вкладки функций")

    def create_configuration_tab(self):
        """Create configuration tab"""
        try:
            config_frame = tk.Frame(self.notebook, bg=ModernStyles.COLORS['bg_secondary'])
            self.notebook.add(config_frame, text="⚙️ Настройки")

            # Server configuration
            server_section = tk.LabelFrame(config_frame, text="🖥️ Настройки Сервера",
                                         font=('Cambria', 14, 'bold'),
                                         fg=ModernStyles.COLORS['primary'],
                                         bg=ModernStyles.COLORS['bg_secondary'])
            server_section.pack(fill='x', padx=20, pady=20)

            # Host and port settings
            settings_frame = tk.Frame(server_section, bg=ModernStyles.COLORS['bg_secondary'])
            settings_frame.pack(fill='x', padx=20, pady=20)

            # Host setting
            tk.Label(settings_frame, text="Хост:",
                   font=('Cambria', 12, 'bold'),
                   fg=ModernStyles.COLORS['text_primary'],
                   bg=ModernStyles.COLORS['bg_secondary']).grid(row=0, column=0, sticky='w', padx=(0, 10))

            self.host_var = tk.StringVar(value=self.server_host)
            host_entry = tk.Entry(settings_frame, textvariable=self.host_var,
                                font=('Cambria', 11), width=20)
            host_entry.grid(row=0, column=1, sticky='w')

            # Port setting
            tk.Label(settings_frame, text="Порт:",
                   font=('Cambria', 12, 'bold'),
                   fg=ModernStyles.COLORS['text_primary'],
                   bg=ModernStyles.COLORS['bg_secondary']).grid(row=1, column=0, sticky='w', padx=(0, 10), pady=(10, 0))

            self.port_var = tk.StringVar(value=str(self.server_port))
            port_entry = tk.Entry(settings_frame, textvariable=self.port_var,
                                font=('Cambria', 11), width=20)
            port_entry.grid(row=1, column=1, sticky='w', pady=(10, 0))

            # Web interface configuration
            web_section = tk.LabelFrame(config_frame, text="🌐 Настройки Веб-Интерфейса",
                                      font=('Cambria', 14, 'bold'),
                                      fg=ModernStyles.COLORS['primary'],
                                      bg=ModernStyles.COLORS['bg_secondary'])
            web_section.pack(fill='x', padx=20, pady=20)

            web_settings_frame = tk.Frame(web_section, bg=ModernStyles.COLORS['bg_secondary'])
            web_settings_frame.pack(fill='x', padx=20, pady=20)

            # Web configuration checkboxes
            self.web_config_vars = {}

            web_options = [
                ("responsive_design", "📱 Адаптивный Дизайн"),
                ("dark_mode", "🌙 Темная Тема"),
                ("push_notifications", "🔔 Push-Уведомления"),
                ("auto_start", "🚀 Автозапуск Сервера")
            ]

            for i, (key, title) in enumerate(web_options):
                var = tk.BooleanVar(value=self.web_config.get(key, True))
                self.web_config_vars[key] = var

                checkbox = tk.Checkbutton(web_settings_frame, text=title,
                                        variable=var,
                                        font=('Cambria', 12),
                                        fg=ModernStyles.COLORS['text_primary'],
                                        bg=ModernStyles.COLORS['bg_secondary'],
                                        command=lambda k=key: self.update_web_config(k))
                checkbox.pack(anchor='w', pady=2)

            # Save configuration button
            save_btn = tk.Button(config_frame, text="💾 Сохранить Настройки",
                               command=self.save_configuration,
                               bg=ModernStyles.COLORS['success'], fg='white',
                               font=('Cambria', 12, 'bold'), relief='flat',
                               padx=30, pady=10)
            save_btn.pack(pady=20)

        except Exception as e:
            handle_module_error(e, "Мобильный веб-интерфейс", "создание вкладки настроек")

    def create_preview_tab(self):
        """Create preview tab"""
        try:
            preview_frame = tk.Frame(self.notebook, bg=ModernStyles.COLORS['bg_secondary'])
            self.notebook.add(preview_frame, text="👁️ Предварительный Просмотр")

            # Preview section
            preview_section = tk.LabelFrame(preview_frame, text="👁️ Предварительный Просмотр Интерфейса",
                                          font=('Cambria', 14, 'bold'),
                                          fg=ModernStyles.COLORS['primary'],
                                          bg=ModernStyles.COLORS['bg_secondary'])
            preview_section.pack(fill='both', expand=True, padx=20, pady=20)

            # URL display
            url_frame = tk.Frame(preview_section, bg=ModernStyles.COLORS['bg_secondary'])
            url_frame.pack(fill='x', padx=20, pady=20)

            tk.Label(url_frame, text="🌐 URL Мобильного Интерфейса:",
                   font=('Cambria', 12, 'bold'),
                   fg=ModernStyles.COLORS['text_primary'],
                   bg=ModernStyles.COLORS['bg_secondary']).pack(anchor='w')

            self.url_var = tk.StringVar(value=f"http://{self.server_host}:{self.server_port}")
            url_entry = tk.Entry(url_frame, textvariable=self.url_var,
                                font=('Cambria', 11), width=50, state='readonly')
            url_entry.pack(fill='x', pady=(5, 0))

            # Preview buttons
            buttons_frame = tk.Frame(preview_section, bg=ModernStyles.COLORS['bg_secondary'])
            buttons_frame.pack(fill='x', padx=20, pady=20)

            open_browser_btn = tk.Button(buttons_frame, text="🌐 Открыть в Браузере",
                                       command=self.open_in_browser,
                                       bg=ModernStyles.COLORS['primary'], fg='white',
                                       font=('Cambria', 12, 'bold'), relief='flat',
                                       padx=20, pady=10)
            open_browser_btn.pack(side='left', padx=(0, 10))

            copy_url_btn = tk.Button(buttons_frame, text="📋 Копировать URL",
                                   command=self.copy_url,
                                   bg=ModernStyles.COLORS['info'], fg='white',
                                   font=('Cambria', 12, 'bold'), relief='flat',
                                   padx=20, pady=10)
            copy_url_btn.pack(side='left')

            # QR Code section (placeholder)
            qr_frame = tk.Frame(preview_section, bg=ModernStyles.COLORS['bg_secondary'])
            qr_frame.pack(fill='x', padx=20, pady=20)

            tk.Label(qr_frame, text="📱 QR-код для быстрого доступа:",
                   font=('Cambria', 12, 'bold'),
                   fg=ModernStyles.COLORS['text_primary'],
                   bg=ModernStyles.COLORS['bg_secondary']).pack(anchor='w')

            qr_placeholder = tk.Label(qr_frame, text="[QR-код будет сгенерирован при запуске сервера]",
                                    font=('Cambria', 10),
                                    fg=ModernStyles.COLORS['text_secondary'],
                                    bg=ModernStyles.COLORS['bg_secondary'])
            qr_placeholder.pack(anchor='w', pady=(5, 0))

            # Mobile interface preview
            preview_text_frame = tk.Frame(preview_section, bg=ModernStyles.COLORS['bg_secondary'])
            preview_text_frame.pack(fill='both', expand=True, padx=20, pady=20)

            tk.Label(preview_text_frame, text="📱 Структура Мобильного Интерфейса:",
                   font=('Cambria', 12, 'bold'),
                   fg=ModernStyles.COLORS['text_primary'],
                   bg=ModernStyles.COLORS['bg_secondary']).pack(anchor='w')

            preview_text = tk.Text(preview_text_frame, height=15, width=80,
                                 font=('Cambria', 10),
                                 bg=ModernStyles.COLORS['bg_main'],
                                 fg=ModernStyles.COLORS['text_primary'],
                                 wrap=tk.WORD, state='disabled')
            preview_text.pack(fill='both', expand=True, pady=(10, 0))

            # Add preview content
            preview_content = """
📱 ГЛАВНАЯ СТРАНИЦА (/)
├── 📊 Панель Управления
├── 💰 Мониторинг Продаж
├── 📦 Управление Складом
└── 🍽️ Активные Заказы

📊 ПАНЕЛЬ УПРАВЛЕНИЯ (/dashboard)
├── Ключевые метрики за сегодня
├── График продаж
├── Уведомления и предупреждения
└── Быстрые действия

💰 ПРОДАЖИ (/sales)
├── Продажи за сегодня
├── Сводка по способам оплаты
├── Топ блюд
└── Динамика по часам

📦 СКЛАД (/inventory)
├── Товары с низким остатком
├── Последние поступления
├── Критические уведомления
└── Быстрая корректировка

🍽️ ЗАКАЗЫ (/orders)
├── Активные заказы
├── Статус приготовления
├── Время ожидания
└── Управление заказами

🔧 API ЭНДПОИНТЫ:
• GET  /api/dashboard - Данные панели
• GET  /api/sales/today - Продажи за день
• GET  /api/inventory/alerts - Уведомления склада
• GET  /api/orders/active - Активные заказы
• POST /api/orders/update - Обновить заказ
• POST /api/inventory/adjust - Корректировка остатков
            """

            preview_text.config(state='normal')
            preview_text.insert('1.0', preview_content.strip())
            preview_text.config(state='disabled')

        except Exception as e:
            handle_module_error(e, "Мобильный веб-интерфейс", "создание вкладки предварительного просмотра")

    def create_control_panel(self):
        """Create control panel at bottom"""
        try:
            control_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
            control_frame.pack(fill='x', padx=20, pady=20)

            # Control buttons
            test_btn = tk.Button(control_frame, text="🧪 Тестировать Интерфейс",
                               command=self.test_interface,
                               bg=ModernStyles.COLORS['warning'], fg='white',
                               font=('Cambria', 12, 'bold'), relief='flat',
                               padx=20, pady=10)
            test_btn.pack(side='left', padx=(0, 10))

            export_btn = tk.Button(control_frame, text="📤 Экспорт Конфигурации",
                                 command=self.export_configuration,
                                 bg=ModernStyles.COLORS['info'], fg='white',
                                 font=('Cambria', 12, 'bold'), relief='flat',
                                 padx=20, pady=10)
            export_btn.pack(side='left', padx=(0, 10))

            close_btn = tk.Button(control_frame, text="❌ Закрыть",
                                command=self.close_interface,
                                bg=ModernStyles.COLORS['danger'], fg='white',
                                font=('Cambria', 12, 'bold'), relief='flat',
                                padx=20, pady=10)
            close_btn.pack(side='right')

        except Exception as e:
            handle_module_error(e, "Мобильный веб-интерфейс", "создание панели управления")

    # Server control methods
    def start_server(self):
        """Start web server"""
        try:
            if self.server_running:
                messagebox.showwarning("Предупреждение", "Сервер уже запущен")
                return

            # Update server settings
            self.server_host = self.host_var.get()
            self.server_port = int(self.port_var.get())

            # Create custom handler class with db_manager
            def handler_factory(*args, **kwargs):
                return MobileWebHandler(*args, db_manager=self.db_manager, **kwargs)

            # Start server in separate thread
            self.web_server = HTTPServer((self.server_host, self.server_port), handler_factory)
            self.server_thread = threading.Thread(target=self.web_server.serve_forever, daemon=True)
            self.server_thread.start()

            self.server_running = True

            # Update UI
            self.start_btn.config(state='disabled')
            self.stop_btn.config(state='normal')
            self.url_var.set(f"http://{self.server_host}:{self.server_port}")

            self.refresh_status()

            messagebox.showinfo("Успех", f"Веб-сервер запущен на http://{self.server_host}:{self.server_port}")
            log_info(f"Мобильный веб-сервер запущен на {self.server_host}:{self.server_port}", "MobileWebInterface")

        except Exception as e:
            handle_module_error(e, "Мобильный веб-интерфейс", "запуск сервера")

    def stop_server(self):
        """Stop web server"""
        try:
            if not self.server_running:
                messagebox.showwarning("Предупреждение", "Сервер не запущен")
                return

            if self.web_server:
                self.web_server.shutdown()
                self.web_server = None

            self.server_running = False

            # Update UI
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')

            self.refresh_status()

            messagebox.showinfo("Успех", "Веб-сервер остановлен")
            log_info("Мобильный веб-сервер остановлен", "MobileWebInterface")

        except Exception as e:
            handle_module_error(e, "Мобильный веб-интерфейс", "остановка сервера")

    def refresh_status(self):
        """Refresh server status display"""
        try:
            status_info = f"""
📊 СТАТУС МОБИЛЬНОГО ВЕБ-ИНТЕРФЕЙСА

🖥️ Сервер: {'🟢 Запущен' if self.server_running else '🔴 Остановлен'}
🌐 Хост: {self.server_host}
🔌 Порт: {self.server_port}
📱 URL: http://{self.server_host}:{self.server_port}

📱 АКТИВНЫЕ ФУНКЦИИ:
{'✅' if self.mobile_features['dashboard_access'] else '❌'} Панель Управления
{'✅' if self.mobile_features['sales_monitoring'] else '❌'} Мониторинг Продаж
{'✅' if self.mobile_features['inventory_alerts'] else '❌'} Уведомления о Складе
{'✅' if self.mobile_features['order_management'] else '❌'} Управление Заказами
{'✅' if self.mobile_features['basic_reporting'] else '❌'} Базовая Отчетность
{'✅' if self.mobile_features['notifications'] else '❌'} Уведомления
{'✅' if self.mobile_features['real_time_updates'] else '❌'} Обновления в Реальном Времени
{'✅' if self.mobile_features['touch_optimized'] else '❌'} Оптимизация для Сенсорных Экранов

🌐 НАСТРОЙКИ ВЕБ-ИНТЕРФЕЙСА:
{'✅' if self.web_config['responsive_design'] else '❌'} Адаптивный Дизайн
{'✅' if self.web_config['dark_mode'] else '❌'} Темная Тема
{'✅' if self.web_config['push_notifications'] else '❌'} Push-Уведомления
{'✅' if self.web_config['auto_start'] else '❌'} Автозапуск Сервера

⏰ Последнее обновление: {datetime.now().strftime('%d.%m.%Y %H:%M:%S')}
            """

            self.status_text.config(state='normal')
            self.status_text.delete('1.0', tk.END)
            self.status_text.insert('1.0', status_info.strip())
            self.status_text.config(state='disabled')

        except Exception as e:
            handle_module_error(e, "Мобильный веб-интерфейс", "обновление статуса")

    # Configuration methods
    def update_feature(self, feature_key):
        """Update mobile feature setting"""
        try:
            self.mobile_features[feature_key] = self.feature_vars[feature_key].get()
            self.refresh_status()

        except Exception as e:
            handle_module_error(e, "Мобильный веб-интерфейс", "обновление функции")

    def update_web_config(self, config_key):
        """Update web configuration setting"""
        try:
            self.web_config[config_key] = self.web_config_vars[config_key].get()
            self.refresh_status()

        except Exception as e:
            handle_module_error(e, "Мобильный веб-интерфейс", "обновление конфигурации")

    def save_configuration(self):
        """Save configuration to file"""
        try:
            config_data = {
                "web_config": self.web_config,
                "mobile_features": self.mobile_features,
                "server_host": self.host_var.get(),
                "server_port": int(self.port_var.get()),
                "saved_at": datetime.now().isoformat()
            }

            # Save to JSON file
            with open("mobile_web_config.json", "w", encoding="utf-8") as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("Успех", "Конфигурация сохранена в файл mobile_web_config.json")
            log_info("Конфигурация мобильного веб-интерфейса сохранена", "MobileWebInterface")

        except Exception as e:
            handle_module_error(e, "Мобильный веб-интерфейс", "сохранение конфигурации")

    # Preview and testing methods
    def open_in_browser(self):
        """Open mobile interface in browser"""
        try:
            if not self.server_running:
                messagebox.showwarning("Предупреждение", "Сначала запустите сервер")
                return

            import webbrowser
            url = f"http://{self.server_host}:{self.server_port}"
            webbrowser.open(url)

            messagebox.showinfo("Браузер", f"Мобильный интерфейс открыт в браузере:\n{url}")

        except Exception as e:
            handle_module_error(e, "Мобильный веб-интерфейс", "открытие в браузере")

    def copy_url(self):
        """Copy URL to clipboard"""
        try:
            url = f"http://{self.server_host}:{self.server_port}"
            self.window.clipboard_clear()
            self.window.clipboard_append(url)

            messagebox.showinfo("Копирование", f"URL скопирован в буфер обмена:\n{url}")

        except Exception as e:
            handle_module_error(e, "Мобильный веб-интерфейс", "копирование URL")

    def test_interface(self):
        """Test mobile interface functionality"""
        try:
            if not self.server_running:
                messagebox.showwarning("Предупреждение", "Сначала запустите сервер для тестирования")
                return

            # Test API endpoints
            test_results = []

            # Simulate API tests
            endpoints = [
                "/api/dashboard",
                "/api/sales/today",
                "/api/inventory/alerts",
                "/api/orders/active"
            ]

            for endpoint in endpoints:
                # Simulate test result
                test_results.append(f"✅ {endpoint} - OK")

            results_text = "\n".join(test_results)
            messagebox.showinfo("Результаты Тестирования",
                              f"Тестирование API эндпоинтов:\n\n{results_text}\n\n✅ Все тесты пройдены успешно!")

        except Exception as e:
            handle_module_error(e, "Мобильный веб-интерфейс", "тестирование интерфейса")

    def export_configuration(self):
        """Export configuration for deployment"""
        try:
            export_data = {
                "mobile_web_interface": {
                    "version": "1.0.0",
                    "web_config": self.web_config,
                    "mobile_features": self.mobile_features,
                    "server_settings": {
                        "host": self.host_var.get(),
                        "port": int(self.port_var.get())
                    },
                    "exported_at": datetime.now().isoformat()
                }
            }

            # Save export file
            with open("mobile_web_export.json", "w", encoding="utf-8") as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("Экспорт", "Конфигурация экспортирована в файл mobile_web_export.json")

        except Exception as e:
            handle_module_error(e, "Мобильный веб-интерфейс", "экспорт конфигурации")

    def close_interface(self):
        """Close mobile web interface"""
        try:
            if self.server_running:
                if messagebox.askyesno("Подтверждение", "Сервер запущен. Остановить сервер и закрыть интерфейс?"):
                    self.stop_server()
                else:
                    return

            log_info("Мобильный веб-интерфейс закрыт", "MobileWebInterface")
            self.window.destroy()

        except Exception as e:
            handle_module_error(e, "Мобильный веб-интерфейс", "закрытие интерфейса")


# Add HTML generation methods to MobileWebHandler class
def add_html_methods_to_handler():
    """Add HTML generation methods to MobileWebHandler"""

    def get_mobile_app_html(self):
        """Generate main mobile application HTML"""
        return f"""
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 Мобильный Интерфейс Ресторана</title>
    <link rel="stylesheet" href="/static/style.css">
    <link rel="manifest" href="/static/manifest.json">
    <meta name="theme-color" content="#2c3e50">
</head>
<body>
    <div class="mobile-app">
        <header class="app-header">
            <h1>🍽️ Управление Рестораном</h1>
            <div class="status-indicator" id="connectionStatus">🟢 Онлайн</div>
        </header>

        <nav class="main-nav">
            <a href="/dashboard" class="nav-item">
                <div class="nav-icon">📊</div>
                <div class="nav-label">Панель</div>
            </a>
            <a href="/sales" class="nav-item">
                <div class="nav-icon">💰</div>
                <div class="nav-label">Продажи</div>
            </a>
            <a href="/inventory" class="nav-item">
                <div class="nav-icon">📦</div>
                <div class="nav-label">Склад</div>
            </a>
            <a href="/orders" class="nav-item">
                <div class="nav-icon">🍽️</div>
                <div class="nav-label">Заказы</div>
            </a>
        </nav>

        <main class="main-content">
            <div class="welcome-section">
                <h2>Добро пожаловать!</h2>
                <p>Выберите раздел для управления рестораном</p>
            </div>

            <div class="quick-stats" id="quickStats">
                <div class="stat-card">
                    <div class="stat-icon">💰</div>
                    <div class="stat-value" id="todaySales">0 ₽</div>
                    <div class="stat-label">Продажи сегодня</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🍽️</div>
                    <div class="stat-value" id="activeOrders">0</div>
                    <div class="stat-label">Активные заказы</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">⚠️</div>
                    <div class="stat-value" id="lowStockItems">0</div>
                    <div class="stat-label">Низкий остаток</div>
                </div>
            </div>

            <div class="notifications" id="notifications">
                <!-- Notifications will be loaded here -->
            </div>
        </main>

        <footer class="app-footer">
            <div class="footer-info">
                <span>Система Управления Рестораном v1.0</span>
                <span id="lastUpdate">Обновлено: --:--</span>
            </div>
        </footer>
    </div>

    <script src="/static/app.js"></script>
</body>
</html>
        """

    def get_dashboard_html(self):
        """Generate dashboard page HTML"""
        return f"""
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Панель Управления</title>
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <div class="mobile-app">
        <header class="app-header">
            <a href="/" class="back-btn">← Назад</a>
            <h1>📊 Панель Управления</h1>
            <button class="refresh-btn" onclick="refreshData()">🔄</button>
        </header>

        <main class="main-content">
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <h3>💰 Продажи за сегодня</h3>
                    <div class="metric-value" id="todayRevenue">0 ₽</div>
                    <div class="metric-change positive">+12.5%</div>
                </div>

                <div class="dashboard-card">
                    <h3>🍽️ Заказы</h3>
                    <div class="metric-value" id="todayOrders">0</div>
                    <div class="metric-change positive">+8 заказов</div>
                </div>

                <div class="dashboard-card">
                    <h3>👥 Средний чек</h3>
                    <div class="metric-value" id="averageCheck">0 ₽</div>
                    <div class="metric-change negative">-2.1%</div>
                </div>

                <div class="dashboard-card">
                    <h3>⏱️ Время ожидания</h3>
                    <div class="metric-value" id="avgWaitTime">0 мин</div>
                    <div class="metric-change positive">-3 мин</div>
                </div>
            </div>

            <div class="chart-section">
                <h3>📈 График продаж по часам</h3>
                <div class="chart-placeholder">
                    [График будет загружен]
                </div>
            </div>

            <div class="alerts-section">
                <h3>🚨 Уведомления</h3>
                <div id="alertsList">
                    <!-- Alerts will be loaded here -->
                </div>
            </div>
        </main>
    </div>

    <script src="/static/app.js"></script>
    <script>
        // Load dashboard data
        loadDashboardData();

        // Auto-refresh every 30 seconds
        setInterval(loadDashboardData, 30000);
    </script>
</body>
</html>
        """

    def get_sales_html(self):
        """Generate sales page HTML"""
        return f"""
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💰 Мониторинг Продаж</title>
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <div class="mobile-app">
        <header class="app-header">
            <a href="/" class="back-btn">← Назад</a>
            <h1>💰 Продажи</h1>
            <button class="refresh-btn" onclick="refreshSalesData()">🔄</button>
        </header>

        <main class="main-content">
            <div class="sales-summary">
                <div class="summary-card">
                    <h3>Сегодня</h3>
                    <div class="amount" id="todayTotal">0 ₽</div>
                </div>
                <div class="summary-card">
                    <h3>Вчера</h3>
                    <div class="amount" id="yesterdayTotal">0 ₽</div>
                </div>
                <div class="summary-card">
                    <h3>Месяц</h3>
                    <div class="amount" id="monthTotal">0 ₽</div>
                </div>
            </div>

            <div class="payment-methods">
                <h3>💳 По способам оплаты</h3>
                <div id="paymentMethodsList">
                    <!-- Payment methods will be loaded here -->
                </div>
            </div>

            <div class="top-dishes">
                <h3>🏆 Топ блюд</h3>
                <div id="topDishesList">
                    <!-- Top dishes will be loaded here -->
                </div>
            </div>

            <div class="hourly-sales">
                <h3>⏰ Продажи по часам</h3>
                <div id="hourlySalesChart">
                    <!-- Hourly sales chart will be loaded here -->
                </div>
            </div>
        </main>
    </div>

    <script src="/static/app.js"></script>
    <script>
        loadSalesData();
        setInterval(loadSalesData, 60000); // Refresh every minute
    </script>
</body>
</html>
        """

    # Add methods to MobileWebHandler class
    MobileWebHandler.get_mobile_app_html = get_mobile_app_html
    MobileWebHandler.get_dashboard_html = get_dashboard_html
    MobileWebHandler.get_sales_html = get_sales_html

# Add more HTML generation methods
def add_more_html_methods():
    """Add inventory and orders HTML methods"""

    def get_inventory_html(self):
        """Generate inventory page HTML"""
        return f"""
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📦 Управление Складом</title>
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <div class="mobile-app">
        <header class="app-header">
            <a href="/" class="back-btn">← Назад</a>
            <h1>📦 Склад</h1>
            <button class="refresh-btn" onclick="refreshInventoryData()">🔄</button>
        </header>

        <main class="main-content">
            <div class="inventory-alerts">
                <h3>⚠️ Критические остатки</h3>
                <div id="lowStockAlerts">
                    <!-- Low stock alerts will be loaded here -->
                </div>
            </div>

            <div class="recent-movements">
                <h3>📋 Последние движения</h3>
                <div id="recentMovements">
                    <!-- Recent inventory movements will be loaded here -->
                </div>
            </div>

            <div class="quick-adjust">
                <h3>⚡ Быстрая корректировка</h3>
                <div class="adjust-form">
                    <select id="itemSelect">
                        <option value="">Выберите товар...</option>
                    </select>
                    <input type="number" id="adjustAmount" placeholder="Количество">
                    <select id="adjustType">
                        <option value="add">Поступление</option>
                        <option value="subtract">Списание</option>
                    </select>
                    <button onclick="adjustInventory()">Применить</button>
                </div>
            </div>
        </main>
    </div>

    <script src="/static/app.js"></script>
    <script>
        loadInventoryData();
        setInterval(loadInventoryData, 120000); // Refresh every 2 minutes
    </script>
</body>
</html>
        """

    def get_orders_html(self):
        """Generate orders page HTML"""
        return f"""
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍽️ Управление Заказами</title>
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <div class="mobile-app">
        <header class="app-header">
            <a href="/" class="back-btn">← Назад</a>
            <h1>🍽️ Заказы</h1>
            <button class="refresh-btn" onclick="refreshOrdersData()">🔄</button>
        </header>

        <main class="main-content">
            <div class="orders-summary">
                <div class="summary-item">
                    <span class="label">Активные заказы:</span>
                    <span class="value" id="activeOrdersCount">0</span>
                </div>
                <div class="summary-item">
                    <span class="label">Среднее время:</span>
                    <span class="value" id="averageTime">0 мин</span>
                </div>
            </div>

            <div class="orders-list">
                <h3>📋 Активные заказы</h3>
                <div id="activeOrdersList">
                    <!-- Active orders will be loaded here -->
                </div>
            </div>

            <div class="kitchen-status">
                <h3>🍳 Статус кухни</h3>
                <div id="kitchenStatus">
                    <!-- Kitchen status will be loaded here -->
                </div>
            </div>
        </main>
    </div>

    <script src="/static/app.js"></script>
    <script>
        loadOrdersData();
        setInterval(loadOrdersData, 15000); // Refresh every 15 seconds
    </script>
</body>
</html>
        """

    def get_mobile_css(self):
        """Generate mobile CSS styles"""
        return """
/* Mobile-First Responsive CSS */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cambria', serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    line-height: 1.6;
}

.mobile-app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.app-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.app-header h1 {
    font-size: 1.5rem;
    color: #2c3e50;
    font-weight: bold;
}

.back-btn, .refresh-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.back-btn:hover, .refresh-btn:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

.status-indicator {
    font-size: 0.9rem;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    background: rgba(46, 204, 113, 0.2);
    color: #27ae60;
}

/* Navigation Styles */
.main-nav {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.9);
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background: white;
    border-radius: 15px;
    text-decoration: none;
    color: #2c3e50;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.nav-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.nav-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.nav-label {
    font-size: 0.9rem;
    font-weight: bold;
    text-align: center;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 1rem;
}

.welcome-section {
    text-align: center;
    margin-bottom: 2rem;
    color: white;
}

.welcome-section h2 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

/* Quick Stats */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #7f8c8d;
}

/* Dashboard Styles */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.dashboard-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.dashboard-card h3 {
    font-size: 1rem;
    color: #7f8c8d;
    margin-bottom: 1rem;
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.metric-change {
    font-size: 0.9rem;
    font-weight: bold;
}

.metric-change.positive {
    color: #27ae60;
}

.metric-change.negative {
    color: #e74c3c;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-nav {
        grid-template-columns: repeat(2, 1fr);
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .quick-stats {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .app-header h1 {
        font-size: 1.2rem;
    }

    .nav-icon {
        font-size: 1.5rem;
    }

    .nav-label {
        font-size: 0.8rem;
    }
}

/* Footer */
.app-footer {
    background: rgba(255, 255, 255, 0.9);
    padding: 1rem;
    text-align: center;
    font-size: 0.8rem;
    color: #7f8c8d;
}

.footer-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

@media (max-width: 480px) {
    .footer-info {
        flex-direction: column;
        gap: 0.5rem;
    }
}
        """

    # Add methods to MobileWebHandler class
    MobileWebHandler.get_inventory_html = get_inventory_html
    MobileWebHandler.get_orders_html = get_orders_html
    MobileWebHandler.get_mobile_css = get_mobile_css

# Call the function to add more methods
add_more_html_methods()

# Add JavaScript and data methods
def add_js_and_data_methods():
    """Add JavaScript functionality and data retrieval methods"""

    def get_mobile_js(self):
        """Generate mobile JavaScript functionality"""
        return """
// Mobile Restaurant Management App JavaScript

// Global variables
let connectionStatus = true;
let refreshInterval = 30000; // 30 seconds

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    startAutoRefresh();
});

function initializeApp() {
    console.log('Initializing mobile restaurant app...');
    updateConnectionStatus();
    loadInitialData();
}

// Connection status management
function updateConnectionStatus() {
    const statusElement = document.getElementById('connectionStatus');
    if (statusElement) {
        statusElement.textContent = connectionStatus ? '🟢 Онлайн' : '🔴 Офлайн';
        statusElement.className = connectionStatus ? 'status-online' : 'status-offline';
    }
}

// Data loading functions
async function loadInitialData() {
    try {
        await loadQuickStats();
        await loadNotifications();
        updateLastUpdateTime();
    } catch (error) {
        console.error('Error loading initial data:', error);
        connectionStatus = false;
        updateConnectionStatus();
    }
}

async function loadQuickStats() {
    try {
        const response = await fetch('/api/dashboard');
        const data = await response.json();

        if (data.success) {
            updateQuickStats(data.data);
        }
    } catch (error) {
        console.error('Error loading quick stats:', error);
    }
}

function updateQuickStats(data) {
    const elements = {
        todaySales: document.getElementById('todaySales'),
        activeOrders: document.getElementById('activeOrders'),
        lowStockItems: document.getElementById('lowStockItems')
    };

    if (elements.todaySales) {
        elements.todaySales.textContent = formatCurrency(data.today_sales || 0);
    }
    if (elements.activeOrders) {
        elements.activeOrders.textContent = data.active_orders || 0;
    }
    if (elements.lowStockItems) {
        elements.lowStockItems.textContent = data.low_stock_items || 0;
    }
}

// Dashboard specific functions
async function loadDashboardData() {
    try {
        const response = await fetch('/api/dashboard');
        const data = await response.json();

        if (data.success) {
            updateDashboardMetrics(data.data);
            updateDashboardAlerts(data.alerts || []);
        }
    } catch (error) {
        console.error('Error loading dashboard data:', error);
    }
}

function updateDashboardMetrics(data) {
    const elements = {
        todayRevenue: document.getElementById('todayRevenue'),
        todayOrders: document.getElementById('todayOrders'),
        averageCheck: document.getElementById('averageCheck'),
        avgWaitTime: document.getElementById('avgWaitTime')
    };

    if (elements.todayRevenue) {
        elements.todayRevenue.textContent = formatCurrency(data.today_revenue || 0);
    }
    if (elements.todayOrders) {
        elements.todayOrders.textContent = data.today_orders || 0;
    }
    if (elements.averageCheck) {
        elements.averageCheck.textContent = formatCurrency(data.average_check || 0);
    }
    if (elements.avgWaitTime) {
        elements.avgWaitTime.textContent = (data.avg_wait_time || 0) + ' мин';
    }
}

// Sales specific functions
async function loadSalesData() {
    try {
        const response = await fetch('/api/sales/today');
        const data = await response.json();

        if (data.success) {
            updateSalesData(data.data);
        }
    } catch (error) {
        console.error('Error loading sales data:', error);
    }
}

function updateSalesData(data) {
    const elements = {
        todayTotal: document.getElementById('todayTotal'),
        yesterdayTotal: document.getElementById('yesterdayTotal'),
        monthTotal: document.getElementById('monthTotal')
    };

    if (elements.todayTotal) {
        elements.todayTotal.textContent = formatCurrency(data.today_total || 0);
    }
    if (elements.yesterdayTotal) {
        elements.yesterdayTotal.textContent = formatCurrency(data.yesterday_total || 0);
    }
    if (elements.monthTotal) {
        elements.monthTotal.textContent = formatCurrency(data.month_total || 0);
    }

    updatePaymentMethods(data.payment_methods || []);
    updateTopDishes(data.top_dishes || []);
}

// Inventory specific functions
async function loadInventoryData() {
    try {
        const response = await fetch('/api/inventory/alerts');
        const data = await response.json();

        if (data.success) {
            updateInventoryAlerts(data.alerts || []);
            updateRecentMovements(data.movements || []);
        }
    } catch (error) {
        console.error('Error loading inventory data:', error);
    }
}

// Orders specific functions
async function loadOrdersData() {
    try {
        const response = await fetch('/api/orders/active');
        const data = await response.json();

        if (data.success) {
            updateActiveOrders(data.orders || []);
            updateKitchenStatus(data.kitchen_status || {});
        }
    } catch (error) {
        console.error('Error loading orders data:', error);
    }
}

// Utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
}

function formatTime(timestamp) {
    return new Date(timestamp).toLocaleTimeString('ru-RU', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

function updateLastUpdateTime() {
    const element = document.getElementById('lastUpdate');
    if (element) {
        element.textContent = 'Обновлено: ' + formatTime(new Date());
    }
}

// Auto-refresh functionality
function startAutoRefresh() {
    setInterval(() => {
        if (connectionStatus) {
            loadInitialData();
        }
    }, refreshInterval);
}

// Refresh functions for different pages
function refreshData() {
    loadInitialData();
}

function refreshSalesData() {
    loadSalesData();
}

function refreshInventoryData() {
    loadInventoryData();
}

function refreshOrdersData() {
    loadOrdersData();
}

// Touch and mobile optimizations
document.addEventListener('touchstart', function() {}, {passive: true});

// Service Worker registration (for PWA functionality)
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.register('/static/sw.js')
        .then(registration => console.log('SW registered'))
        .catch(error => console.log('SW registration failed'));
}
        """

    def get_dashboard_data(self):
        """Get dashboard data from database"""
        try:
            if not self.db_manager:
                return {"success": False, "error": "Database not available"}

            # Get today's sales
            today = datetime.now().strftime('%Y-%m-%d')
            sales_query = """
                SELECT COALESCE(SUM(total_amount), 0) as today_sales,
                       COUNT(*) as today_orders,
                       COALESCE(AVG(total_amount), 0) as average_check
                FROM sales_data
                WHERE DATE(sale_date) = ?
            """

            sales_result = self.db_manager.execute_query(sales_query, (today,))
            sales_data = sales_result[0] if sales_result else {}

            # Get active orders count (simulated)
            active_orders = 12  # This would come from orders table

            # Get low stock items count
            stock_query = """
                SELECT COUNT(*) as low_stock_count
                FROM inventory
                WHERE current_stock <= minimum_stock
            """

            stock_result = self.db_manager.execute_query(stock_query)
            low_stock_count = stock_result[0]['low_stock_count'] if stock_result else 0

            return {
                "success": True,
                "data": {
                    "today_sales": sales_data.get('today_sales', 0),
                    "today_orders": sales_data.get('today_orders', 0),
                    "today_revenue": sales_data.get('today_sales', 0),
                    "average_check": sales_data.get('average_check', 0),
                    "active_orders": active_orders,
                    "low_stock_items": low_stock_count,
                    "avg_wait_time": 15  # This would be calculated from orders
                },
                "alerts": []
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_sales_today_data(self):
        """Get today's sales data"""
        try:
            if not self.db_manager:
                return {"success": False, "error": "Database not available"}

            today = datetime.now().strftime('%Y-%m-%d')
            yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            month_start = datetime.now().replace(day=1).strftime('%Y-%m-%d')

            # Today's sales
            today_query = "SELECT COALESCE(SUM(total_amount), 0) as total FROM sales_data WHERE DATE(sale_date) = ?"
            today_result = self.db_manager.execute_query(today_query, (today,))
            today_total = today_result[0]['total'] if today_result else 0

            # Yesterday's sales
            yesterday_result = self.db_manager.execute_query(today_query, (yesterday,))
            yesterday_total = yesterday_result[0]['total'] if yesterday_result else 0

            # Month's sales
            month_query = "SELECT COALESCE(SUM(total_amount), 0) as total FROM sales_data WHERE DATE(sale_date) >= ?"
            month_result = self.db_manager.execute_query(month_query, (month_start,))
            month_total = month_result[0]['total'] if month_result else 0

            return {
                "success": True,
                "data": {
                    "today_total": today_total,
                    "yesterday_total": yesterday_total,
                    "month_total": month_total,
                    "payment_methods": [],
                    "top_dishes": []
                }
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_inventory_alerts_data(self):
        """Get inventory alerts data"""
        try:
            if not self.db_manager:
                return {"success": False, "error": "Database not available"}

            # Get low stock items
            alerts_query = """
                SELECT item_name, current_stock, minimum_stock, unit
                FROM inventory
                WHERE current_stock <= minimum_stock
                ORDER BY (current_stock / minimum_stock) ASC
                LIMIT 10
            """

            alerts_result = self.db_manager.execute_query(alerts_query)

            return {
                "success": True,
                "alerts": alerts_result or [],
                "movements": []  # Recent movements would be added here
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_active_orders_data(self):
        """Get active orders data"""
        try:
            # This would typically come from an orders table
            # For now, return simulated data
            return {
                "success": True,
                "orders": [
                    {
                        "id": 1,
                        "table": "Стол 5",
                        "items": ["Борщ", "Котлета по-киевски"],
                        "status": "Готовится",
                        "time": "12:30"
                    }
                ],
                "kitchen_status": {
                    "orders_in_queue": 3,
                    "average_prep_time": 15
                }
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    # Add methods to MobileWebHandler class
    MobileWebHandler.get_mobile_js = get_mobile_js
    MobileWebHandler.get_dashboard_data = get_dashboard_data
    MobileWebHandler.get_sales_today_data = get_sales_today_data
    MobileWebHandler.get_inventory_alerts_data = get_inventory_alerts_data
    MobileWebHandler.get_active_orders_data = get_active_orders_data

# Call the function to add JS and data methods
add_js_and_data_methods()

# Call the function to add methods
add_html_methods_to_handler()


def create_mobile_web_interface(parent, db_manager):
    """Create and return mobile web interface window"""
    try:
        interface = MobileWebInterface(parent, db_manager)
        log_info("Мобильный веб-интерфейс создан успешно", "MobileWebInterface")
        return interface

    except Exception as e:
        handle_module_error(e, "Мобильный веб-интерфейс", "создание интерфейса")
        return None


# Test function
if __name__ == "__main__":
    import tkinter as tk
    from utils.database_manager import DatabaseManager

    # Create test window
    root = tk.Tk()
    root.withdraw()  # Hide main window

    # Create database manager (mock)
    class MockDBManager:
        def execute_query(self, query, params=None):
            return [{"total": 50000, "today_sales": 25000, "today_orders": 45, "average_check": 555}]

    db_manager = MockDBManager()

    # Create mobile web interface
    interface = create_mobile_web_interface(root, db_manager)

    if interface:
        root.mainloop()
