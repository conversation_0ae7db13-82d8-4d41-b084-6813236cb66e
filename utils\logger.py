"""
Профессиональная система логирования для ресторанной системы
Обеспечивает детальное логирование всех операций с ротацией файлов
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
import json
from typing import Dict, Any
import traceback

class RestaurantLogger:
    """Профессиональная система логирования"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = log_dir
        self.ensure_log_directory()
        self.setup_loggers()
    
    def ensure_log_directory(self):
        """Создать директорию для логов"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
    
    def setup_loggers(self):
        """Настроить различные логгеры"""
        
        # Основной логгер приложения
        self.app_logger = self.create_logger(
            'restaurant_app',
            os.path.join(self.log_dir, 'application.log'),
            logging.INFO
        )
        
        # Логгер для операций с базой данных
        self.db_logger = self.create_logger(
            'restaurant_db',
            os.path.join(self.log_dir, 'database.log'),
            logging.DEBUG
        )
        
        # Логгер для пользовательских действий
        self.user_logger = self.create_logger(
            'restaurant_user',
            os.path.join(self.log_dir, 'user_actions.log'),
            logging.INFO
        )
        
        # Логгер для ошибок
        self.error_logger = self.create_logger(
            'restaurant_errors',
            os.path.join(self.log_dir, 'errors.log'),
            logging.ERROR
        )
        
        # Логгер для аудита (финансовые операции)
        self.audit_logger = self.create_logger(
            'restaurant_audit',
            os.path.join(self.log_dir, 'audit.log'),
            logging.INFO
        )
        
        # Логгер производительности
        self.performance_logger = self.create_logger(
            'restaurant_performance',
            os.path.join(self.log_dir, 'performance.log'),
            logging.INFO
        )
    
    def create_logger(self, name: str, filename: str, level: int) -> logging.Logger:
        """Создать настроенный логгер"""
        logger = logging.getLogger(name)
        logger.setLevel(level)
        
        # Очистить существующие обработчики
        logger.handlers.clear()
        
        # Ротирующий файловый обработчик (10MB, 5 файлов)
        file_handler = logging.handlers.RotatingFileHandler(
            filename, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
        )
        file_handler.setLevel(level)
        
        # Консольный обработчик для критических ошибок
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.WARNING)
        
        # Форматтер с подробной информацией
        detailed_formatter = logging.Formatter(
            '%(asctime)s | %(name)s | %(levelname)s | %(funcName)s:%(lineno)d | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Простой форматтер для консоли
        simple_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)s | %(message)s',
            datefmt='%H:%M:%S'
        )
        
        file_handler.setFormatter(detailed_formatter)
        console_handler.setFormatter(simple_formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def log_app_event(self, message: str, level: str = "INFO", **kwargs):
        """Логировать событие приложения"""
        extra_data = json.dumps(kwargs, ensure_ascii=False) if kwargs else ""
        full_message = f"{message} | {extra_data}" if extra_data else message
        
        if level == "DEBUG":
            self.app_logger.debug(full_message)
        elif level == "INFO":
            self.app_logger.info(full_message)
        elif level == "WARNING":
            self.app_logger.warning(full_message)
        elif level == "ERROR":
            self.app_logger.error(full_message)
        elif level == "CRITICAL":
            self.app_logger.critical(full_message)
    
    def log_db_operation(self, operation: str, table: str, details: Dict[str, Any] = None):
        """Логировать операцию с базой данных"""
        message = f"DB Operation: {operation} on {table}"
        if details:
            message += f" | Details: {json.dumps(details, ensure_ascii=False)}"
        self.db_logger.info(message)
    
    def log_user_action(self, user_id: str, action: str, details: Dict[str, Any] = None):
        """Логировать действие пользователя"""
        message = f"User {user_id}: {action}"
        if details:
            message += f" | Details: {json.dumps(details, ensure_ascii=False)}"
        self.user_logger.info(message)
    
    def log_error(self, error: Exception, context: str = "", user_id: str = None):
        """Логировать ошибку с полной информацией"""
        error_info = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "context": context,
            "user_id": user_id,
            "traceback": traceback.format_exc()
        }
        
        message = f"Error in {context}: {error}"
        self.error_logger.error(f"{message} | {json.dumps(error_info, ensure_ascii=False)}")
    
    def log_audit_event(self, user_id: str, action: str, resource: str, 
                       old_value: Any = None, new_value: Any = None):
        """Логировать аудиторское событие"""
        audit_data = {
            "user_id": user_id,
            "action": action,
            "resource": resource,
            "timestamp": datetime.now().isoformat(),
            "old_value": old_value,
            "new_value": new_value
        }
        
        message = f"AUDIT: {user_id} {action} {resource}"
        self.audit_logger.info(f"{message} | {json.dumps(audit_data, ensure_ascii=False)}")
    
    def log_performance(self, operation: str, duration: float, details: Dict[str, Any] = None):
        """Логировать информацию о производительности"""
        perf_data = {
            "operation": operation,
            "duration_ms": round(duration * 1000, 2),
            "timestamp": datetime.now().isoformat()
        }
        
        if details:
            perf_data.update(details)
        
        message = f"PERFORMANCE: {operation} took {perf_data['duration_ms']}ms"
        self.performance_logger.info(f"{message} | {json.dumps(perf_data, ensure_ascii=False)}")
    
    def log_system_startup(self, version: str, user_count: int = 0):
        """Логировать запуск системы"""
        startup_info = {
            "version": version,
            "startup_time": datetime.now().isoformat(),
            "user_count": user_count,
            "python_version": sys.version,
            "platform": sys.platform
        }
        
        self.app_logger.info(f"SYSTEM STARTUP | {json.dumps(startup_info, ensure_ascii=False)}")
    
    def log_system_shutdown(self, reason: str = "Normal shutdown"):
        """Логировать завершение работы системы"""
        shutdown_info = {
            "reason": reason,
            "shutdown_time": datetime.now().isoformat()
        }
        
        self.app_logger.info(f"SYSTEM SHUTDOWN | {json.dumps(shutdown_info, ensure_ascii=False)}")
    
    def log_login_attempt(self, username: str, success: bool, ip_address: str = "localhost"):
        """Логировать попытку входа"""
        login_data = {
            "username": username,
            "success": success,
            "ip_address": ip_address,
            "timestamp": datetime.now().isoformat()
        }
        
        status = "SUCCESS" if success else "FAILED"
        message = f"LOGIN {status}: {username} from {ip_address}"
        
        if success:
            self.user_logger.info(f"{message} | {json.dumps(login_data, ensure_ascii=False)}")
        else:
            self.error_logger.warning(f"{message} | {json.dumps(login_data, ensure_ascii=False)}")
    
    def log_data_import(self, user_id: str, file_path: str, records_count: int, 
                       success: bool, errors: list = None):
        """Логировать импорт данных"""
        import_data = {
            "user_id": user_id,
            "file_path": file_path,
            "records_count": records_count,
            "success": success,
            "errors": errors or [],
            "timestamp": datetime.now().isoformat()
        }
        
        message = f"DATA IMPORT: {records_count} records from {file_path}"
        
        if success:
            self.audit_logger.info(f"{message} | {json.dumps(import_data, ensure_ascii=False)}")
        else:
            self.error_logger.error(f"{message} FAILED | {json.dumps(import_data, ensure_ascii=False)}")

# Глобальный экземпляр логгера
restaurant_logger = None

def init_logger(log_dir: str = "logs") -> RestaurantLogger:
    """Инициализировать систему логирования"""
    global restaurant_logger
    restaurant_logger = RestaurantLogger(log_dir)
    return restaurant_logger

def get_logger() -> RestaurantLogger:
    """Получить экземпляр логгера"""
    global restaurant_logger
    if restaurant_logger is None:
        restaurant_logger = RestaurantLogger()
    return restaurant_logger

# Удобные функции для быстрого логирования
def log_info(message: str, **kwargs):
    """Быстрое логирование информации"""
    get_logger().log_app_event(message, "INFO", **kwargs)

def log_warning(message: str, **kwargs):
    """Быстрое логирование предупреждения"""
    get_logger().log_app_event(message, "WARNING", **kwargs)

def log_error(message: str, **kwargs):
    """Быстрое логирование ошибки"""
    get_logger().log_app_event(message, "ERROR", **kwargs)

def log_debug(message: str, **kwargs):
    """Быстрое логирование отладочной информации"""
    get_logger().log_app_event(message, "DEBUG", **kwargs)
