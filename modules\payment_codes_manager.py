"""
Модуль управления кодами способов оплаты
Позволяет создавать таблицы соответствия кодов и названий способов оплаты
для замены в импортированных CSV файлах
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import csv
import json
import os
from datetime import datetime

# Определяем цвета напрямую
COLORS = {
    'bg_main': '#f8f9fa',
    'primary': '#3498db',
    'success': '#27ae60',
    'danger': '#e74c3c',
    'warning': '#f39c12',
    'info': '#17a2b8',
    'secondary': '#6c757d'
}

class PaymentCodesManager:
    """Менеджер кодов способов оплаты"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        
        # Файл для хранения кодов
        self.codes_file = "data/payment_codes.json"
        
        # Стандартные коды способов оплаты
        self.payment_codes = {
            "1": "Наличные",
            "2": "Банковская карта",
            "3": "Безналичный расчёт",
            "4": "Электронные деньги",
            "5": "Мобильные платежи",
            "6": "Криптовалюта",
            "7": "Подарочные карты",
            "8": "Бонусные баллы",
            "9": "Кредит/Рассрочка",
            "10": "Банковский перевод",
            "CASH": "Наличные",
            "CARD": "Банковская карта",
            "ONLINE": "Онлайн платёж",
            "MOBILE": "Мобильный платёж",
            "CRYPTO": "Криптовалюта",
            "GIFT": "Подарочная карта",
            "BONUS": "Бонусы",
            "CREDIT": "Кредит",
            "TRANSFER": "Перевод"
        }
        
        # Загрузить сохранённые коды
        self.load_codes()
    
    def create_window(self):
        """Создать окно управления кодами оплаты"""
        try:
            from utils.window_utils import create_centered_dialog
            self.window = create_centered_dialog(
                self.parent,
                "💳 Управление Кодами Способов Оплаты",
                width=1200,
                height=800,
                resizable=True
            )
        except ImportError:
            self.window = tk.Toplevel(self.parent)
            self.window.title("💳 Управление Кодами Способов Оплаты")
            self.window.geometry("1200x800")
            self.window.configure(bg=COLORS['bg_main'])
            self.window.resizable(True, True)

            # Центрировать окно
            self.window.update_idletasks()
            x = (self.window.winfo_screenwidth() // 2) - (1200 // 2)
            y = (self.window.winfo_screenheight() // 2) - (800 // 2)
            self.window.geometry(f"1200x800+{x}+{y}")
        
        self.create_interface()
    
    def create_interface(self):
        """Создать интерфейс"""
        # Заголовок
        header_frame = tk.Frame(self.window, bg=COLORS['primary'], height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="💳 Управление Кодами Способов Оплаты",
                font=('Cambria', 16, 'bold italic'), bg=COLORS['primary'],
                fg='white').pack(side='left', padx=20, pady=15)

        # Кнопки действий
        btn_frame = tk.Frame(header_frame, bg=COLORS['primary'])
        btn_frame.pack(side='right', padx=20, pady=10)

        tk.Button(btn_frame, text="➕ Добавить Код", command=self.add_code,
                 bg=COLORS['success'], fg='white',
                 font=('Cambria', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(btn_frame, text="📁 Импорт CSV", command=self.import_csv,
                 bg=COLORS['info'], fg='white',
                 font=('Cambria', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(btn_frame, text="📋 Вставить Текст", command=self.import_text,
                 bg=COLORS['secondary'], fg='white',
                 font=('Cambria', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(btn_frame, text="💾 Сохранить", command=self.save_codes,
                 bg=COLORS['warning'], fg='white',
                 font=('Cambria', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        # Основной контент
        main_frame = tk.Frame(self.window, bg=COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Создать вкладки
        self.create_tabs(main_frame)
    
    def create_tabs(self, parent):
        """Создать вкладки"""
        notebook = ttk.Notebook(parent)
        notebook.pack(fill='both', expand=True)
        
        # Вкладка управления кодами
        codes_frame = tk.Frame(notebook, bg=COLORS['bg_main'])
        notebook.add(codes_frame, text="💳 Коды Оплаты")
        self.create_codes_tab(codes_frame)

        # Вкладка обработки CSV
        csv_frame = tk.Frame(notebook, bg=COLORS['bg_main'])
        notebook.add(csv_frame, text="📊 Обработка CSV")
        self.create_csv_tab(csv_frame)

        # Вкладка статистики
        stats_frame = tk.Frame(notebook, bg=COLORS['bg_main'])
        notebook.add(stats_frame, text="📈 Статистика")
        self.create_stats_tab(stats_frame)
    
    def create_codes_tab(self, parent):
        """Создать вкладку управления кодами"""
        # Заголовок
        tk.Label(parent, text="💳 Таблица Кодирования Способов Оплаты",
                font=('Cambria', 16, 'bold italic'), bg=COLORS['bg_main']).pack(pady=10)

        # Поиск
        search_frame = tk.Frame(parent, bg=COLORS['bg_main'])
        search_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(search_frame, text="🔍 Поиск:", font=('Cambria', 12, 'bold'),
                bg=COLORS['bg_main']).pack(side='left')

        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.filter_codes)
        search_entry = tk.Entry(search_frame, textvariable=self.search_var,
                               font=('Cambria', 11), width=30)
        search_entry.pack(side='left', padx=10)

        # Таблица кодов
        table_frame = tk.Frame(parent, bg=COLORS['bg_main'])
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Создать таблицу
        columns = ('Код', 'Название способа оплаты', 'Описание', 'Статус', 'Создано', 'Изменено')
        self.codes_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # Настройка колонок
        column_widths = {'Код': 100, 'Название способа оплаты': 200, 'Описание': 250, 'Статус': 100, 'Создано': 120, 'Изменено': 120}
        for col in columns:
            self.codes_tree.heading(col, text=col)
            self.codes_tree.column(col, width=column_widths[col])
        
        # Заполнить данными
        self.refresh_codes_table()
        
        # Скроллбары
        v_scroll = ttk.Scrollbar(table_frame, orient='vertical', command=self.codes_tree.yview)
        h_scroll = ttk.Scrollbar(table_frame, orient='horizontal', command=self.codes_tree.xview)
        self.codes_tree.configure(yscrollcommand=v_scroll.set, xscrollcommand=h_scroll.set)
        
        self.codes_tree.pack(side='left', fill='both', expand=True)
        v_scroll.pack(side='right', fill='y')
        h_scroll.pack(side='bottom', fill='x')
        
        # Кнопки управления
        btn_frame = tk.Frame(parent, bg=COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Button(btn_frame, text="✏️ Редактировать", command=self.edit_code,
                 bg=COLORS['primary'], fg='white',
                 font=('Cambria', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="🗑️ Удалить", command=self.delete_code,
                 bg=COLORS['danger'], fg='white',
                 font=('Cambria', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="📤 Экспорт", command=self.export_codes,
                 bg=COLORS['secondary'], fg='white',
                 font=('Cambria', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="🔄 Обновить", command=self.refresh_codes_table,
                 bg=COLORS['info'], fg='white',
                 font=('Cambria', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='right', padx=5)
    
    def load_codes(self):
        """Загрузить коды из файла"""
        try:
            if os.path.exists(self.codes_file):
                with open(self.codes_file, 'r', encoding='utf-8') as f:
                    saved_codes = json.load(f)
                    self.payment_codes.update(saved_codes)
        except Exception as e:
            print(f"Ошибка загрузки кодов: {e}")
    
    def save_codes(self):
        """Сохранить коды в файл"""
        try:
            # Создать папку data если не существует
            os.makedirs(os.path.dirname(self.codes_file), exist_ok=True)
            
            with open(self.codes_file, 'w', encoding='utf-8') as f:
                json.dump(self.payment_codes, f, ensure_ascii=False, indent=2)
            
            messagebox.showinfo("Успех", "Коды способов оплаты сохранены успешно!")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка сохранения кодов: {e}")
    
    def refresh_codes_table(self):
        """Обновить таблицу кодов"""
        # Очистить таблицу
        for item in self.codes_tree.get_children():
            self.codes_tree.delete(item)
        
        # Заполнить данными
        for code, name in self.payment_codes.items():
            # Определить описание и статус
            if code.isdigit():
                description = f"Числовой код {code}"
                status = "🟢 Активен"
            else:
                description = f"Текстовый код {code}"
                status = "🟢 Активен"
            
            self.codes_tree.insert('', 'end', values=(
                code,
                name,
                description,
                status,
                datetime.now().strftime('%Y-%m-%d'),
                datetime.now().strftime('%Y-%m-%d')
            ))

    def filter_codes(self, *args):
        """Фильтровать коды по поиску"""
        search_text = self.search_var.get().lower()

        # Очистить таблицу
        for item in self.codes_tree.get_children():
            self.codes_tree.delete(item)

        # Заполнить отфильтрованными данными
        for code, name in self.payment_codes.items():
            if search_text in code.lower() or search_text in name.lower():
                description = f"Числовой код {code}" if code.isdigit() else f"Текстовый код {code}"
                status = "🟢 Активен"

                self.codes_tree.insert('', 'end', values=(
                    code,
                    name,
                    description,
                    status,
                    datetime.now().strftime('%Y-%m-%d'),
                    datetime.now().strftime('%Y-%m-%d')
                ))

    def add_code(self):
        """Добавить новый код"""
        try:
            from utils.window_utils import create_centered_dialog
            add_window = create_centered_dialog(
                self.window,
                "➕ Добавить Код Способа Оплаты",
                width=500,
                height=400,
                resizable=True
            )
        except ImportError:
            add_window = tk.Toplevel(self.window)
            add_window.title("➕ Добавить Код Способа Оплаты")
            add_window.geometry("500x400")
            add_window.configure(bg=COLORS['bg_main'])

            # Центрировать окно
            add_window.update_idletasks()
            x = (add_window.winfo_screenwidth() // 2) - (500 // 2)
            y = (add_window.winfo_screenheight() // 2) - (400 // 2)
            add_window.geometry(f"500x400+{x}+{y}")

        # Заголовок
        tk.Label(add_window, text="➕ Добавление Нового Кода",
                font=('Cambria', 16, 'bold italic'), bg=COLORS['bg_main']).pack(pady=20)

        # Форма
        form_frame = tk.Frame(add_window, bg=COLORS['bg_main'])
        form_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # Поля формы
        tk.Label(form_frame, text="Код:", font=('Cambria', 12, 'bold'),
                bg=COLORS['bg_main']).grid(row=0, column=0, sticky='w', pady=10)

        code_entry = tk.Entry(form_frame, font=('Cambria', 11), width=25)
        code_entry.grid(row=0, column=1, sticky='ew', padx=(10, 0), pady=10)

        tk.Label(form_frame, text="Название:", font=('Cambria', 12, 'bold'),
                bg=COLORS['bg_main']).grid(row=1, column=0, sticky='w', pady=10)

        name_entry = tk.Entry(form_frame, font=('Cambria', 11), width=25)
        name_entry.grid(row=1, column=1, sticky='ew', padx=(10, 0), pady=10)

        tk.Label(form_frame, text="Описание:", font=('Cambria', 12, 'bold'),
                bg=COLORS['bg_main']).grid(row=2, column=0, sticky='w', pady=10)

        desc_text = tk.Text(form_frame, font=('Cambria', 11), height=4, width=25)
        desc_text.grid(row=2, column=1, sticky='ew', padx=(10, 0), pady=10)

        form_frame.grid_columnconfigure(1, weight=1)

        # Кнопки
        btn_frame = tk.Frame(add_window, bg=COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=30, pady=20)

        def save_new_code():
            code = code_entry.get().strip()
            name = name_entry.get().strip()

            if not code or not name:
                messagebox.showerror("Ошибка", "Заполните код и название!")
                return

            if code in self.payment_codes:
                messagebox.showerror("Ошибка", "Такой код уже существует!")
                return

            self.payment_codes[code] = name
            self.refresh_codes_table()
            add_window.destroy()
            messagebox.showinfo("Успех", f"Код '{code}' добавлен успешно!")

        tk.Button(btn_frame, text="💾 Сохранить", command=save_new_code,
                 bg=COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat', padx=20, pady=8).pack(side='left')

        tk.Button(btn_frame, text="❌ Отмена", command=add_window.destroy,
                 bg=COLORS['danger'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat', padx=20, pady=8).pack(side='right')

    def edit_code(self):
        """Редактировать код"""
        selection = self.codes_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите код для редактирования")
            return

        item = self.codes_tree.item(selection[0])
        old_code = item['values'][0]
        old_name = item['values'][1]

        try:
            from utils.window_utils import create_centered_dialog
            edit_window = create_centered_dialog(
                self.window,
                f"✏️ Редактировать Код: {old_code}",
                width=500,
                height=400,
                resizable=True
            )
        except ImportError:
            edit_window = tk.Toplevel(self.window)
            edit_window.title(f"✏️ Редактировать Код: {old_code}")
            edit_window.geometry("500x400")
            edit_window.configure(bg=COLORS['bg_main'])

            # Центрировать окно
            edit_window.update_idletasks()
            x = (edit_window.winfo_screenwidth() // 2) - (500 // 2)
            y = (edit_window.winfo_screenheight() // 2) - (400 // 2)
            edit_window.geometry(f"500x400+{x}+{y}")

        # Заголовок
        tk.Label(edit_window, text=f"✏️ Редактирование Кода: {old_code}",
                font=('Cambria', 16, 'bold italic'), bg=COLORS['bg_main']).pack(pady=20)

        # Форма
        form_frame = tk.Frame(edit_window, bg=COLORS['bg_main'])
        form_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # Поля формы
        tk.Label(form_frame, text="Код:", font=('Cambria', 12, 'bold'),
                bg=COLORS['bg_main']).grid(row=0, column=0, sticky='w', pady=10)

        code_entry = tk.Entry(form_frame, font=('Cambria', 11), width=25)
        code_entry.insert(0, old_code)
        code_entry.grid(row=0, column=1, sticky='ew', padx=(10, 0), pady=10)

        tk.Label(form_frame, text="Название:", font=('Cambria', 12, 'bold'),
                bg=COLORS['bg_main']).grid(row=1, column=0, sticky='w', pady=10)

        name_entry = tk.Entry(form_frame, font=('Cambria', 11), width=25)
        name_entry.insert(0, old_name)
        name_entry.grid(row=1, column=1, sticky='ew', padx=(10, 0), pady=10)

        form_frame.grid_columnconfigure(1, weight=1)

        # Кнопки
        btn_frame = tk.Frame(edit_window, bg=COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=30, pady=20)

        def save_edited_code():
            new_code = code_entry.get().strip()
            new_name = name_entry.get().strip()

            if not new_code or not new_name:
                messagebox.showerror("Ошибка", "Заполните код и название!")
                return

            # Удалить старый код
            if old_code in self.payment_codes:
                del self.payment_codes[old_code]

            # Добавить новый
            self.payment_codes[new_code] = new_name
            self.refresh_codes_table()
            edit_window.destroy()
            messagebox.showinfo("Успех", f"Код '{new_code}' обновлён успешно!")

        tk.Button(btn_frame, text="💾 Сохранить", command=save_edited_code,
                 bg=COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat', padx=20, pady=8).pack(side='left')

        tk.Button(btn_frame, text="❌ Отмена", command=edit_window.destroy,
                 bg=COLORS['danger'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat', padx=20, pady=8).pack(side='right')

    def delete_code(self):
        """Удалить код"""
        selection = self.codes_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите код для удаления")
            return

        item = self.codes_tree.item(selection[0])
        code = item['values'][0]

        if messagebox.askyesno("Подтверждение", f"Удалить код '{code}'?"):
            if code in self.payment_codes:
                del self.payment_codes[code]
                self.refresh_codes_table()
                messagebox.showinfo("Успех", f"Код '{code}' удалён успешно!")

    def export_codes(self):
        """Экспорт кодов в CSV"""
        try:
            filename = filedialog.asksaveasfilename(
                title="Сохранить коды способов оплаты",
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(['Код', 'Название', 'Описание', 'Статус'])

                    for code, name in self.payment_codes.items():
                        description = f"Числовой код {code}" if code.isdigit() else f"Текстовый код {code}"
                        writer.writerow([code, name, description, 'Активен'])

                messagebox.showinfo("Успех", f"Коды экспортированы в файл:\n{filename}")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка экспорта: {e}")

    def import_csv(self):
        """Импорт и обработка CSV файла"""
        try:
            filename = filedialog.askopenfilename(
                title="Выберите CSV файл для обработки",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )

            if filename:
                self.process_csv_file(filename)
        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка импорта CSV: {e}")

    def import_text(self):
        """Импорт данных через текстовое поле"""
        try:
            from utils.window_utils import create_centered_dialog
            text_window = create_centered_dialog(
                self.window,
                "📋 Импорт Текстовых Данных",
                width=700,
                height=600,
                resizable=True
            )
        except ImportError:
            text_window = tk.Toplevel(self.window)
            text_window.title("📋 Импорт Текстовых Данных")
            text_window.geometry("700x600")
            text_window.configure(bg=COLORS['bg_main'])

            # Центрировать окно
            text_window.update_idletasks()
            x = (text_window.winfo_screenwidth() // 2) - (700 // 2)
            y = (text_window.winfo_screenheight() // 2) - (600 // 2)
            text_window.geometry(f"700x600+{x}+{y}")

        # Заголовок
        tk.Label(text_window, text="📋 Импорт Данных из Текста",
                font=('Cambria', 16, 'bold italic'), bg=COLORS['bg_main']).pack(pady=20)

        # Инструкции
        instructions = tk.Label(text_window,
            text="Вставьте данные в формате CSV (разделители: запятая, точка с запятой, табуляция)\n"
                 "Пример: код1,название1\nкод2,название2\nили скопируйте из Excel",
            font=('Cambria', 11), bg=COLORS['bg_main'], justify='left')
        instructions.pack(pady=10, padx=20)

        # Текстовое поле
        text_frame = tk.Frame(text_window, bg=COLORS['bg_main'])
        text_frame.pack(fill='both', expand=True, padx=20, pady=10)

        text_area = tk.Text(text_frame, font=('Cambria', 11), height=15, width=70)
        text_scrollbar = ttk.Scrollbar(text_frame, orient='vertical', command=text_area.yview)
        text_area.configure(yscrollcommand=text_scrollbar.set)

        text_area.pack(side='left', fill='both', expand=True)
        text_scrollbar.pack(side='right', fill='y')

        # Кнопки
        btn_frame = tk.Frame(text_window, bg=COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=20, pady=20)

        def process_text_data():
            text_data = text_area.get('1.0', tk.END).strip()
            if not text_data:
                messagebox.showerror("Ошибка", "Введите данные для обработки!")
                return

            try:
                # Разбить на строки
                lines = text_data.split('\n')
                rows = []

                for line in lines:
                    line = line.strip()
                    if not line:
                        continue

                    # Попробуем разные разделители
                    for delimiter in [';', ',', '\t', '|']:
                        if delimiter in line:
                            parts = [part.strip() for part in line.split(delimiter)]
                            if len(parts) >= 2:
                                rows.append(parts)
                                break
                    else:
                        # Если разделитель не найден, попробуем пробел
                        parts = line.split()
                        if len(parts) >= 2:
                            rows.append(parts)

                if not rows:
                    messagebox.showerror("Ошибка", "Не удалось распознать данные!\nПроверьте формат.")
                    return

                messagebox.showinfo("Успех",
                    f"✅ Данные успешно обработаны!\n\n"
                    f"📊 Строк данных: {len(rows)}\n"
                    f"📋 Колонок: {len(rows[0]) if rows else 0}")

                # Показать окно выбора колонки
                text_window.destroy()
                self.show_column_selector("Текстовые данные", rows)

            except Exception as e:
                messagebox.showerror("Ошибка", f"Ошибка обработки данных:\n{e}")

        tk.Button(btn_frame, text="✅ Обработать", command=process_text_data,
                 bg=COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat', padx=20, pady=8).pack(side='left')

        def paste_from_clipboard():
            try:
                clipboard_data = text_window.clipboard_get()
                text_area.insert(tk.END, clipboard_data)
            except tk.TclError:
                messagebox.showwarning("Предупреждение", "Буфер обмена пуст или недоступен")
            except Exception as e:
                messagebox.showerror("Ошибка", f"Ошибка вставки из буфера: {e}")

        tk.Button(btn_frame, text="📋 Вставить из Буфера", command=paste_from_clipboard,
                 bg=COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat', padx=20, pady=8).pack(side='left', padx=10)

        tk.Button(btn_frame, text="🗑️ Очистить",
                 command=lambda: text_area.delete('1.0', tk.END),
                 bg=COLORS['warning'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat', padx=20, pady=8).pack(side='left', padx=10)

        tk.Button(btn_frame, text="❌ Отмена", command=text_window.destroy,
                 bg=COLORS['danger'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat', padx=20, pady=8).pack(side='right')

    def process_csv_file(self, filename):
        """Обработать CSV файл"""
        try:
            # Попробуем различные кодировки (расширенный список)
            encodings = [
                'utf-8', 'utf-8-sig',  # UTF-8 с BOM и без
                'cp1251', 'windows-1251',  # Русская Windows
                'cp866', 'ibm866',  # DOS кодировка
                'koi8-r',  # KOI8-R
                'iso-8859-5',  # ISO для кириллицы
                'mac-cyrillic',  # Mac кириллица
                'iso-8859-1', 'latin-1',  # Латиница
                'cp1252', 'windows-1252'  # Западноевропейская Windows
            ]

            rows = None
            used_encoding = None
            delimiter = ','

            for encoding in encodings:
                try:
                    with open(filename, 'r', encoding=encoding) as csvfile:
                        # Читаем небольшой образец для определения разделителя
                        sample = csvfile.read(2048)
                        csvfile.seek(0)

                        # Определяем разделитель
                        try:
                            sniffer = csv.Sniffer()
                            delimiter = sniffer.sniff(sample, delimiters=',;\t|').delimiter
                        except:
                            # Если автоопределение не сработало, попробуем разные разделители
                            for test_delim in [';', ',', '\t', '|']:
                                if test_delim in sample:
                                    delimiter = test_delim
                                    break

                        # Читаем весь файл
                        csvfile.seek(0)
                        reader = csv.reader(csvfile, delimiter=delimiter)
                        rows = list(reader)
                        used_encoding = encoding
                        break

                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    print(f"Ошибка с кодировкой {encoding}: {e}")
                    continue

            if rows is None:
                # Попробуем прочитать как бинарный файл и показать первые байты для диагностики
                try:
                    with open(filename, 'rb') as f:
                        first_bytes = f.read(50)
                        hex_bytes = ' '.join([f'{b:02X}' for b in first_bytes[:20]])

                    error_msg = (
                        "Не удалось прочитать файл ни с одной из поддерживаемых кодировок.\n\n"
                        "Попробованные кодировки:\n"
                        "• UTF-8, UTF-8 с BOM\n"
                        "• Windows-1251 (CP1251)\n"
                        "• DOS (CP866)\n"
                        "• KOI8-R\n"
                        "• ISO-8859-5\n"
                        "• Mac Cyrillic\n"
                        "• Latin-1, Windows-1252\n\n"
                        f"Первые байты файла: {hex_bytes}\n\n"
                        "Рекомендации:\n"
                        "1. Пересохраните файл в Excel как CSV UTF-8\n"
                        "2. Или используйте Notepad++ для конвертации в UTF-8"
                    )
                except:
                    error_msg = (
                        "Не удалось прочитать файл.\n\n"
                        "Попробуйте пересохранить файл в кодировке UTF-8."
                    )

                messagebox.showerror("Ошибка кодировки", error_msg)
                return

            if not rows:
                messagebox.showerror("Ошибка", "CSV файл пуст!")
                return

            # Показать информацию об использованной кодировке
            messagebox.showinfo("Успех",
                f"✅ Файл успешно прочитан!\n\n"
                f"📄 Кодировка: {used_encoding.upper()}\n"
                f"📊 Строк данных: {len(rows)}\n"
                f"🔧 Разделитель: '{delimiter}'\n"
                f"📋 Колонок: {len(rows[0]) if rows else 0}")

            # Показать окно выбора колонки
            self.show_column_selector(filename, rows)

        except Exception as e:
            messagebox.showerror("Ошибка", f"Критическая ошибка чтения файла:\n{e}")

    def show_column_selector(self, filename, rows):
        """Показать окно выбора колонки для замены"""
        try:
            from utils.window_utils import create_centered_dialog
            selector_window = create_centered_dialog(
                self.window,
                "📊 Выбор Колонки для Замены Кодов",
                width=800,
                height=600,
                resizable=True
            )
        except ImportError:
            selector_window = tk.Toplevel(self.window)
            selector_window.title("📊 Выбор Колонки для Замены Кодов")
            selector_window.geometry("800x600")
            selector_window.configure(bg=COLORS['bg_main'])

            # Центрировать окно
            selector_window.update_idletasks()
            x = (selector_window.winfo_screenwidth() // 2) - (800 // 2)
            y = (selector_window.winfo_screenheight() // 2) - (600 // 2)
            selector_window.geometry(f"800x600+{x}+{y}")

        # Заголовок
        tk.Label(selector_window, text="📊 Обработка CSV Файла",
                font=('Cambria', 16, 'bold italic'), bg=COLORS['bg_main']).pack(pady=20)

        tk.Label(selector_window, text=f"Файл: {os.path.basename(filename)}",
                font=('Cambria', 12), bg=COLORS['bg_main']).pack(pady=5)

        # Выбор колонки
        column_frame = tk.Frame(selector_window, bg=COLORS['bg_main'])
        column_frame.pack(fill='x', padx=20, pady=20)

        tk.Label(column_frame, text="Выберите колонку с кодами способов оплаты:",
                font=('Cambria', 12, 'bold'), bg=COLORS['bg_main']).pack(anchor='w')

        # Получить заголовки
        headers = rows[0] if rows else []

        column_var = tk.StringVar()
        column_combo = ttk.Combobox(column_frame, textvariable=column_var,
                                   font=('Cambria', 11), width=40)
        column_combo['values'] = [f"{i}: {header}" for i, header in enumerate(headers)]
        column_combo.pack(anchor='w', pady=10)

        # Предварительный просмотр
        preview_frame = tk.LabelFrame(selector_window, text="📋 Предварительный просмотр (первые 10 строк)",
                                     font=('Cambria', 12, 'bold'), bg=COLORS['bg_main'])
        preview_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Таблица предварительного просмотра
        preview_tree = ttk.Treeview(preview_frame, columns=headers, show='headings', height=10)

        for header in headers:
            preview_tree.heading(header, text=header)
            preview_tree.column(header, width=100)

        # Показать первые 10 строк
        for row in rows[1:11]:  # Пропускаем заголовок, показываем первые 10 строк данных
            if len(row) == len(headers):
                preview_tree.insert('', 'end', values=row)

        preview_tree.pack(fill='both', expand=True, padx=10, pady=10)

        # Кнопки
        btn_frame = tk.Frame(selector_window, bg=COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=20, pady=20)

        def process_replacement():
            if not column_var.get():
                messagebox.showerror("Ошибка", "Выберите колонку!")
                return

            column_index = int(column_var.get().split(':')[0])
            self.replace_codes_in_csv(filename, rows, column_index)
            selector_window.destroy()

        tk.Button(btn_frame, text="🔄 Заменить Коды", command=process_replacement,
                 bg=COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat', padx=20, pady=8).pack(side='left')

        tk.Button(btn_frame, text="❌ Отмена", command=selector_window.destroy,
                 bg=COLORS['danger'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat', padx=20, pady=8).pack(side='right')

    def replace_codes_in_csv(self, filename, rows, column_index):
        """Заменить коды в CSV файле"""
        try:
            replaced_count = 0
            not_found_codes = set()

            # Заменить коды в данных
            for i, row in enumerate(rows[1:], 1):  # Пропускаем заголовок
                if column_index < len(row):
                    original_code = row[column_index]
                    if original_code in self.payment_codes:
                        row[column_index] = self.payment_codes[original_code]
                        replaced_count += 1
                    else:
                        not_found_codes.add(original_code)

            # Сохранить обработанный файл
            output_filename = filename.replace('.csv', '_processed.csv')

            # Попробуем сохранить в UTF-8, если не получится - в исходной кодировке
            try:
                with open(output_filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerows(rows)
                save_encoding = "UTF-8"
            except UnicodeEncodeError:
                # Если UTF-8 не подходит, сохраняем в Windows-1251
                with open(output_filename, 'w', newline='', encoding='cp1251') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerows(rows)
                save_encoding = "Windows-1251"

            # Показать результат
            result_message = f"""
Обработка завершена!

📊 Статистика:
• Заменено кодов: {replaced_count}
• Не найдено кодов: {len(not_found_codes)}
• Обработанный файл: {os.path.basename(output_filename)}
• Кодировка сохранения: {save_encoding}

"""

            if not_found_codes:
                result_message += f"\n❌ Коды не найдены в таблице:\n"
                for code in sorted(not_found_codes):
                    result_message += f"• {code}\n"
                result_message += f"\nДобавьте эти коды в таблицу для полной обработки."

            messagebox.showinfo("Результат обработки", result_message)

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка обработки файла: {e}")

    def create_csv_tab(self, parent):
        """Создать вкладку обработки CSV"""
        # Заголовок
        tk.Label(parent, text="📊 Обработка CSV Файлов",
                font=('Cambria', 16, 'bold italic'), bg=COLORS['bg_main']).pack(pady=20)

        # Инструкции
        instructions_frame = tk.LabelFrame(parent, text="📋 Инструкции по использованию",
                                         font=('Cambria', 14, 'bold'), bg=COLORS['bg_main'])
        instructions_frame.pack(fill='x', padx=20, pady=20)

        instructions_text = """
1. 📁 Нажмите "Импорт CSV" для выбора файла
2. 📊 Выберите колонку с кодами способов оплаты
3. 👀 Просмотрите предварительный просмотр данных
4. 🔄 Нажмите "Заменить Коды" для обработки
5. 💾 Получите обработанный файл с суффиксом "_processed"

💡 Система автоматически заменит коды на названия способов оплаты
❗ Коды, не найденные в таблице, останутся без изменений
        """

        tk.Label(instructions_frame, text=instructions_text, font=('Cambria', 11),
                bg=COLORS['bg_main'], justify='left').pack(padx=20, pady=20)

        # Примеры кодов
        examples_frame = tk.LabelFrame(parent, text="💳 Примеры кодов в системе",
                                     font=('Cambria', 14, 'bold'), bg=COLORS['bg_main'])
        examples_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Таблица примеров
        examples_columns = ('Код', 'Название')
        examples_tree = ttk.Treeview(examples_frame, columns=examples_columns, show='headings', height=10)

        for col in examples_columns:
            examples_tree.heading(col, text=col)
            examples_tree.column(col, width=200)

        # Показать первые 10 кодов как примеры
        for i, (code, name) in enumerate(list(self.payment_codes.items())[:10]):
            examples_tree.insert('', 'end', values=(code, name))

        examples_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def create_stats_tab(self, parent):
        """Создать вкладку статистики"""
        # Заголовок
        tk.Label(parent, text="📈 Статистика Кодов Способов Оплаты",
                font=('Cambria', 16, 'bold italic'), bg=COLORS['bg_main']).pack(pady=20)

        # Общая статистика
        stats_frame = tk.LabelFrame(parent, text="📊 Общая статистика",
                                   font=('Cambria', 14, 'bold'), bg=COLORS['bg_main'])
        stats_frame.pack(fill='x', padx=20, pady=20)

        total_codes = len(self.payment_codes)
        numeric_codes = len([code for code in self.payment_codes.keys() if code.isdigit()])
        text_codes = total_codes - numeric_codes

        stats_text = f"""
📊 Всего кодов в системе: {total_codes}
🔢 Числовых кодов: {numeric_codes} ({numeric_codes/total_codes*100:.1f}%)
🔤 Текстовых кодов: {text_codes} ({text_codes/total_codes*100:.1f}%)
📅 Последнее обновление: {datetime.now().strftime('%Y-%m-%d %H:%M')}
        """

        tk.Label(stats_frame, text=stats_text, font=('Cambria', 12),
                bg=COLORS['bg_main'], justify='left').pack(padx=20, pady=20)

        # Топ способов оплаты
        top_frame = tk.LabelFrame(parent, text="🏆 Популярные способы оплаты",
                                 font=('Cambria', 14, 'bold'), bg=COLORS['bg_main'])
        top_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Таблица топ способов
        top_columns = ('Ранг', 'Код', 'Название', 'Тип', 'Использование')
        top_tree = ttk.Treeview(top_frame, columns=top_columns, show='headings', height=8)

        column_widths = {'Ранг': 60, 'Код': 80, 'Название': 200, 'Тип': 120, 'Использование': 100}
        for col in top_columns:
            top_tree.heading(col, text=col)
            top_tree.column(col, width=column_widths[col])

        # Примеры популярных способов
        popular_methods = [
            (1, "CARD", "Банковская карта", "Текстовый", "85%"),
            (2, "CASH", "Наличные", "Текстовый", "12%"),
            (3, "ONLINE", "Онлайн платёж", "Текстовый", "2%"),
            (4, "MOBILE", "Мобильный платёж", "Текстовый", "1%"),
            (5, "1", "Наличные", "Числовой", "45%"),
            (6, "2", "Банковская карта", "Числовой", "40%"),
            (7, "3", "Безналичный расчёт", "Числовой", "10%"),
            (8, "4", "Электронные деньги", "Числовой", "5%")
        ]

        for method in popular_methods:
            top_tree.insert('', 'end', values=method)

        top_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def show(self):
        """Показать окно менеджера кодов оплаты"""
        if self.window is None:
            self.create_window()
        else:
            # Если окно уже существует, просто поднять его на передний план
            self.window.lift()
            self.window.focus_force()

def create_payment_codes_manager(parent, db_manager):
    """Создать менеджер кодов оплаты"""
    manager = PaymentCodesManager(parent, db_manager)
    manager.create_window()
    return manager
