"""
Утилиты для работы с окнами - центрирование, размеры, позиционирование
"""

import tkinter as tk

def center_window(window, width=None, height=None):
    """
    Центрировать окно на экране
    
    Args:
        window: Окно Tkinter для центрирования
        width: Ширина окна (если None, использует текущую)
        height: Высота окна (если None, использует текущую)
    """
    try:
        # Обновить окно для получения актуальных размеров
        window.update_idletasks()
        
        # Получить размеры экрана
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        
        # Если размеры не указаны, получить текущие
        if width is None or height is None:
            # Попытаться получить размеры из geometry
            geometry = window.geometry()
            if 'x' in geometry and '+' in geometry:
                size_part = geometry.split('+')[0]
                if 'x' in size_part:
                    current_width, current_height = map(int, size_part.split('x'))
                    width = width or current_width
                    height = height or current_height
            
            # Если всё ещё не получили размеры, использовать размеры по умолчанию
            if width is None:
                width = 800
            if height is None:
                height = 600
        
        # Рассчитать позицию для центрирования
        x = (screen_width // 2) - (width // 2)
        y = (screen_height // 2) - (height // 2)
        
        # Убедиться, что окно не выходит за границы экрана
        x = max(0, min(x, screen_width - width))
        y = max(0, min(y, screen_height - height))
        
        # Установить геометрию окна
        window.geometry(f"{width}x{height}+{x}+{y}")
        
    except Exception as e:
        print(f"Ошибка центрирования окна: {e}")
        # Fallback - простое центрирование
        try:
            window.geometry(f"{width or 800}x{height or 600}")
        except:
            pass

def auto_resize_window(window, min_width=600, min_height=400, max_width=1400, max_height=1000):
    """
    Автоматически подобрать размер окна под контент
    
    Args:
        window: Окно Tkinter
        min_width: Минимальная ширина
        min_height: Минимальная высота
        max_width: Максимальная ширина
        max_height: Максимальная высота
    """
    try:
        # Обновить окно
        window.update_idletasks()
        
        # Получить требуемые размеры
        required_width = window.winfo_reqwidth()
        required_height = window.winfo_reqheight()
        
        # Применить ограничения
        width = max(min_width, min(required_width + 100, max_width))
        height = max(min_height, min(required_height + 100, max_height))
        
        # Центрировать с новыми размерами
        center_window(window, width, height)
        
    except Exception as e:
        print(f"Ошибка автоматического изменения размера: {e}")
        # Fallback
        center_window(window, min_width, min_height)

def create_centered_dialog(parent, title, width=900, height=700, resizable=True):
    """
    Создать центрированное диалоговое окно
    
    Args:
        parent: Родительское окно
        title: Заголовок окна
        width: Ширина окна
        height: Высота окна
        resizable: Можно ли изменять размер
    
    Returns:
        Новое диалоговое окно
    """
    try:
        # Создать новое окно
        dialog = tk.Toplevel(parent)
        dialog.title(title)
        dialog.configure(bg='#f8f9fa')
        dialog.resizable(resizable, resizable)
        
        # Центрировать окно
        center_window(dialog, width, height)
        
        # Сделать окно модальным
        dialog.transient(parent)
        dialog.grab_set()
        
        # Фокус на новое окно
        dialog.focus_set()
        
        return dialog
        
    except Exception as e:
        print(f"Ошибка создания диалога: {e}")
        # Fallback - простое окно
        dialog = tk.Toplevel(parent)
        dialog.title(title)
        dialog.geometry(f"{width}x{height}")
        return dialog

def ensure_window_visible(window):
    """
    Убедиться, что окно видимо на экране
    """
    try:
        window.update_idletasks()
        
        # Получить текущую геометрию
        geometry = window.geometry()
        if '+' in geometry:
            size_part, pos_part = geometry.split('+', 1)
            width, height = map(int, size_part.split('x'))
            
            if '+' in pos_part:
                x, y = map(int, pos_part.split('+'))
            else:
                x, y = 0, 0
            
            # Получить размеры экрана
            screen_width = window.winfo_screenwidth()
            screen_height = window.winfo_screenheight()
            
            # Проверить, что окно помещается на экране
            if x + width > screen_width or y + height > screen_height or x < 0 or y < 0:
                center_window(window, width, height)
                
    except Exception as e:
        print(f"Ошибка проверки видимости окна: {e}")

def set_window_icon(window, icon_path=None):
    """
    Установить иконку окна
    """
    try:
        if icon_path:
            window.iconbitmap(icon_path)
        else:
            # Использовать иконку по умолчанию
            pass
    except Exception as e:
        print(f"Ошибка установки иконки: {e}")

# Стандартные размеры для разных типов окон (увеличены для показа всех полей)
DIALOG_SIZES = {
    'small': (600, 500),      # Увеличено с 500x400
    'medium': (900, 700),     # Увеличено с 800x600
    'large': (1300, 900),     # Увеличено с 1200x800
    'xlarge': (1500, 1100),   # Увеличено с 1400x1000
    'form': (700, 600),       # Увеличено с 600x500
    'table': (1100, 800),     # Увеличено с 1000x700
    'report': (1300, 1000),   # Увеличено с 1200x900
    'settings': (800, 700)    # Увеличено с 700x600
}

def get_dialog_size(dialog_type='medium'):
    """
    Получить стандартный размер для типа диалога
    """
    return DIALOG_SIZES.get(dialog_type, DIALOG_SIZES['medium'])


def create_standard_window(parent, title, width=1400, height=900, resizable=True,
                          maximized=False, modal=True, icon_emoji="📋"):
    """
    Создать стандартное окно для модулей ресторанной системы

    Args:
        parent: Родительское окно
        title: Заголовок окна (без эмодзи - будет добавлен автоматически)
        width: Ширина окна
        height: Высота окна
        resizable: Можно ли изменять размер
        maximized: Открыть в максимизированном состоянии
        modal: Сделать окно модальным
        icon_emoji: Эмодзи для заголовка

    Returns:
        Новое стандартизированное окно
    """
    try:
        from gui.styles import ModernStyles

        # Создать новое окно
        window = tk.Toplevel(parent)
        window.title(f"{icon_emoji} {title}")
        window.configure(bg=ModernStyles.COLORS['bg_main'])
        window.resizable(resizable, resizable)

        # Установить минимальный размер
        window.minsize(800, 600)

        if maximized:
            window.state('zoomed')
        else:
            # Центрировать окно
            center_window(window, width, height)

        if modal:
            # Сделать окно модальным
            window.transient(parent)
            window.grab_set()

        # Фокус на новое окно
        window.focus_set()

        # Настроить стили TTK
        try:
            ModernStyles.configure_ttk_styles(window)
        except:
            pass

        # Обработчик закрытия окна
        def on_closing():
            try:
                if modal:
                    window.grab_release()
                window.destroy()
            except:
                pass

        window.protocol("WM_DELETE_WINDOW", on_closing)

        return window

    except Exception as e:
        print(f"Ошибка создания стандартного окна: {e}")
        # Fallback - простое окно
        window = tk.Toplevel(parent)
        window.title(f"{icon_emoji} {title}")
        window.geometry(f"{width}x{height}")
        return window


def apply_standard_styling(window, title_text, title_emoji="📋"):
    """
    Применить стандартное оформление к окну модуля

    Args:
        window: Окно для оформления
        title_text: Текст заголовка
        title_emoji: Эмодзи для заголовка

    Returns:
        Главный контейнер для содержимого
    """
    try:
        from gui.styles import ModernStyles

        # Главный контейнер
        main_container = tk.Frame(window, bg=ModernStyles.COLORS['bg_main'])
        main_container.pack(fill='both', expand=True, padx=20, pady=20)

        # Заголовок
        header_frame = tk.Frame(main_container, bg=ModernStyles.COLORS['bg_main'])
        header_frame.pack(fill='x', pady=(0, 20))

        title_label = tk.Label(header_frame, text=f"{title_emoji} {title_text}",
                              font=('Cambria', 24, 'bold italic'),
                              fg=ModernStyles.COLORS['text_primary'],
                              bg=ModernStyles.COLORS['bg_main'])
        title_label.pack(side='left')

        return main_container

    except Exception as e:
        print(f"Ошибка применения стандартного оформления: {e}")
        # Fallback - простой контейнер
        main_container = tk.Frame(window)
        main_container.pack(fill='both', expand=True, padx=20, pady=20)
        return main_container
