"""
Purchasing Management Module for Restaurant Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
from gui.styles import ModernStyles
from datetime import datetime, timedelta


class PurchasingManager:
    """Purchasing management system"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
    
    def create_purchasing_window(self):
        """Create purchasing management window"""
        window = tk.Toplevel(self.parent)
        window.title("🛒 Управление Закупками")
        window.geometry("1400x900")
        window.configure(bg=ModernStyles.COLORS['bg_main'])
        window.resizable(True, True)
        
        # Center window
        window.update_idletasks()
        x = (window.winfo_screenwidth() // 2) - (1400 // 2)
        y = (window.winfo_screenheight() // 2) - (900 // 2)
        window.geometry(f"1400x900+{x}+{y}")
        
        # Create notebook for tabs
        notebook = ttk.Notebook(window)
        notebook.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Purchase Orders tab
        po_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(po_frame, text="📋 Заказы на Закупку")
        self.create_purchase_orders_tab(po_frame)
        
        # Suppliers tab
        suppliers_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(suppliers_frame, text="🏢 Поставщики")
        self.create_suppliers_tab(suppliers_frame)
        
        # Inventory Planning tab
        planning_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(planning_frame, text="📊 Планирование Закупок")
        self.create_inventory_planning_tab(planning_frame)
        
        # Receiving tab
        receiving_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(receiving_frame, text="📦 Приёмка Товаров")
        self.create_receiving_tab(receiving_frame)
        
        # Reports tab
        reports_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(reports_frame, text="📈 Отчёты по Закупкам")
        self.create_purchasing_reports_tab(reports_frame)
        
        return window
    
    def create_purchase_orders_tab(self, parent):
        """Create purchase orders management tab"""
        # Controls frame
        controls_frame = ModernStyles.create_card_frame(parent)
        controls_frame.pack(fill='x', pady=(0, 20))
        
        # Title
        tk.Label(controls_frame, text="📋 Управление Заказами на Закупку",
                font=ModernStyles.FONTS['heading'],
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_card']).pack(pady=10)
        
        # Buttons frame
        buttons_frame = tk.Frame(controls_frame, bg=ModernStyles.COLORS['bg_card'])
        buttons_frame.pack(fill='x', padx=20, pady=10)
        
        ModernStyles.create_button(buttons_frame, "➕ Создать Заказ", self.create_purchase_order).pack(side='left', padx=5)
        ModernStyles.create_button(buttons_frame, "✏️ Редактировать", self.edit_purchase_order).pack(side='left', padx=5)
        ModernStyles.create_button(buttons_frame, "📧 Отправить", self.send_purchase_order).pack(side='left', padx=5)
        ModernStyles.create_button(buttons_frame, "📄 Печать", self.print_purchase_order).pack(side='left', padx=5)
        ModernStyles.create_button(buttons_frame, "🔄 Обновить", self.refresh_purchase_orders).pack(side='left', padx=5)
        
        # Purchase orders list
        list_frame = ModernStyles.create_card_frame(parent)
        list_frame.pack(fill='both', expand=True)
        
        # Treeview for purchase orders
        columns = ('ID', 'Поставщик', 'Дата', 'Статус', 'Сумма', 'Примечания')
        self.po_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.po_tree.heading(col, text=col)
            self.po_tree.column(col, width=150)
        
        # Scrollbars
        po_v_scroll = ttk.Scrollbar(list_frame, orient='vertical', command=self.po_tree.yview)
        po_h_scroll = ttk.Scrollbar(list_frame, orient='horizontal', command=self.po_tree.xview)
        self.po_tree.configure(yscrollcommand=po_v_scroll.set, xscrollcommand=po_h_scroll.set)
        
        self.po_tree.pack(side='left', fill='both', expand=True)
        po_v_scroll.pack(side='right', fill='y')
        po_h_scroll.pack(side='bottom', fill='x')
        
        self.refresh_purchase_orders()
    
    def create_suppliers_tab(self, parent):
        """Create suppliers management tab"""
        # Info label
        info_frame = ModernStyles.create_card_frame(parent)
        info_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(info_frame, text="🏢 Управление Поставщиками",
                font=ModernStyles.FONTS['heading'],
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_card']).pack(pady=20)
        
        tk.Label(info_frame, text="Для полного управления поставщиками используйте модуль 'Поставщики' в главном меню.",
                font=ModernStyles.FONTS['body'],
                fg=ModernStyles.COLORS['text_secondary'],
                bg=ModernStyles.COLORS['bg_card']).pack(pady=10)
        
        # Quick supplier stats
        stats_frame = tk.Frame(info_frame, bg=ModernStyles.COLORS['bg_card'])
        stats_frame.pack(fill='x', padx=20, pady=20)
        
        self.create_supplier_stat(stats_frame, "Всего Поставщиков", "15")
        self.create_supplier_stat(stats_frame, "Активных", "12")
        self.create_supplier_stat(stats_frame, "Новых за Месяц", "3")
        
    def create_inventory_planning_tab(self, parent):
        """Create inventory planning tab"""
        # Title
        title_frame = ModernStyles.create_card_frame(parent)
        title_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(title_frame, text="📊 Планирование Закупок",
                font=ModernStyles.FONTS['heading'],
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_card']).pack(pady=20)
        
        # Planning content
        content_frame = ModernStyles.create_card_frame(parent)
        content_frame.pack(fill='both', expand=True)
        
        tk.Label(content_frame, text="Система автоматического планирования закупок на основе:",
                font=ModernStyles.FONTS['body'],
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_card']).pack(pady=10)
        
        features = [
            "• Анализ текущих остатков на складе",
            "• Прогноз потребления на основе исторических данных",
            "• Минимальные уровни запасов",
            "• Сроки поставки от поставщиков",
            "• Сезонные колебания спроса"
        ]
        
        for feature in features:
            tk.Label(content_frame, text=feature,
                    font=ModernStyles.FONTS['body'],
                    fg=ModernStyles.COLORS['text_secondary'],
                    bg=ModernStyles.COLORS['bg_card'],
                    anchor='w').pack(fill='x', padx=40, pady=2)
    
    def create_receiving_tab(self, parent):
        """Create receiving tab"""
        # Title
        title_frame = ModernStyles.create_card_frame(parent)
        title_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(title_frame, text="📦 Приёмка Товаров",
                font=ModernStyles.FONTS['heading'],
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_card']).pack(pady=20)
        
        # Receiving content
        content_frame = ModernStyles.create_card_frame(parent)
        content_frame.pack(fill='both', expand=True)
        
        tk.Label(content_frame, text="Модуль приёмки товаров включает:",
                font=ModernStyles.FONTS['body'],
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_card']).pack(pady=10)
        
        features = [
            "• Сканирование штрих-кодов",
            "• Проверка качества товаров",
            "• Сверка с заказами на закупку",
            "• Автоматическое обновление остатков",
            "• Документооборот по приёмке"
        ]
        
        for feature in features:
            tk.Label(content_frame, text=feature,
                    font=ModernStyles.FONTS['body'],
                    fg=ModernStyles.COLORS['text_secondary'],
                    bg=ModernStyles.COLORS['bg_card'],
                    anchor='w').pack(fill='x', padx=40, pady=2)
    
    def create_purchasing_reports_tab(self, parent):
        """Create purchasing reports tab"""
        # Title
        title_frame = ModernStyles.create_card_frame(parent)
        title_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(title_frame, text="📈 Отчёты по Закупкам",
                font=ModernStyles.FONTS['heading'],
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_card']).pack(pady=20)
        
        # Reports content
        content_frame = ModernStyles.create_card_frame(parent)
        content_frame.pack(fill='both', expand=True)
        
        tk.Label(content_frame, text="Доступные отчёты:",
                font=ModernStyles.FONTS['body'],
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_card']).pack(pady=10)
        
        reports = [
            "• Анализ закупок по поставщикам",
            "• Динамика цен на товары",
            "• Эффективность поставщиков",
            "• ABC-анализ закупок",
            "• Отчёт по просроченным заказам"
        ]
        
        for report in reports:
            tk.Label(content_frame, text=report,
                    font=ModernStyles.FONTS['body'],
                    fg=ModernStyles.COLORS['text_secondary'],
                    bg=ModernStyles.COLORS['bg_card'],
                    anchor='w').pack(fill='x', padx=40, pady=2)
    
    def create_supplier_stat(self, parent, label, value):
        """Create supplier statistic display"""
        stat_frame = tk.Frame(parent, bg=ModernStyles.COLORS['primary'], relief='flat', bd=0)
        stat_frame.pack(side='left', fill='both', expand=True, padx=5)
        
        inner_frame = tk.Frame(stat_frame, bg=ModernStyles.COLORS['primary'])
        inner_frame.pack(fill='both', expand=True, padx=2, pady=2)
        
        tk.Label(inner_frame, text=value,
                font=('Cambria', 24, 'bold'),
                fg='white',
                bg=ModernStyles.COLORS['primary']).pack(pady=(10, 5))
        
        tk.Label(inner_frame, text=label,
                font=('Cambria', 12, 'italic'),
                fg='white',
                bg=ModernStyles.COLORS['primary']).pack(pady=(0, 10))
    
    # Purchase Order Methods
    def create_purchase_order(self):
        """Create new purchase order"""
        messagebox.showinfo("Функция", "Создание заказа на закупку будет реализовано в следующем обновлении.")
    
    def edit_purchase_order(self):
        """Edit selected purchase order"""
        messagebox.showinfo("Функция", "Редактирование заказа будет реализовано в следующем обновлении.")
    
    def send_purchase_order(self):
        """Send purchase order to supplier"""
        messagebox.showinfo("Функция", "Отправка заказа поставщику будет реализована в следующем обновлении.")
    
    def print_purchase_order(self):
        """Print selected purchase order"""
        messagebox.showinfo("Функция", "Печать заказа будет реализована в следующем обновлении.")
    
    def refresh_purchase_orders(self):
        """Refresh purchase orders list"""
        try:
            # Clear existing items
            for item in self.po_tree.get_children():
                self.po_tree.delete(item)
            
            # Add sample data
            sample_orders = [
                ("PO001", "ООО 'Продукты'", "15.12.2024", "Отправлен", "25 000 руб", "Овощи и фрукты"),
                ("PO002", "Мясокомбинат", "14.12.2024", "Получен", "45 000 руб", "Мясная продукция"),
                ("PO003", "Молочный завод", "13.12.2024", "В обработке", "15 000 руб", "Молочные продукты"),
                ("PO004", "Хлебозавод", "12.12.2024", "Доставлен", "8 000 руб", "Хлебобулочные изделия"),
                ("PO005", "Рыбный склад", "11.12.2024", "Отправлен", "32 000 руб", "Рыба и морепродукты")
            ]
            
            for order in sample_orders:
                self.po_tree.insert('', 'end', values=order)
                
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось обновить список заказов: {e}")


def create_purchasing_system(parent, db_manager):
    """Create and show the purchasing management window"""
    manager = PurchasingManager(parent, db_manager)
    return manager.create_purchasing_window()
