"""
Modern GUI Styles for Restaurant Accounting and Inventory Management System
"""

import tkinter as tk
from tkinter import ttk

class ModernStyles:
    """Modern color scheme and styling for the application"""
    
    # Color Palette - Modern Restaurant Theme
    COLORS = {
        # Primary Colors
        'primary': '#2C3E50',          # Dark Blue-Gray
        'primary_light': '#34495E',     # Lighter Blue-Gray
        'secondary': '#E74C3C',         # Restaurant Red
        'secondary_light': '#EC7063',   # Light Red
        
        # Accent Colors
        'accent': '#F39C12',            # Orange
        'accent_light': '#F8C471',      # Light Orange
        'success': '#27AE60',           # Green
        'success_light': '#58D68D',     # Light Green
        'warning': '#F1C40F',           # Yellow
        'warning_light': '#F7DC6F',     # Light Yellow
        'danger': '#E74C3C',            # Red
        'danger_light': '#F1948A',      # Light Red
        
        # Neutral Colors
        'white': '#FFFFFF',
        'light_gray': '#F8F9FA',
        'gray': '#BDC3C7',
        'dark_gray': '#7F8C8D',
        'black': '#2C3E50',
        
        # Background Colors
        'bg_main': '#ECF0F1',           # Main background
        'bg_sidebar': '#34495E',        # Sidebar background
        'bg_card': '#FFFFFF',           # Card background
        'bg_hover': '#D5DBDB',          # Hover background
        'bg_selected': '#AED6F1',       # Selected background
        
        # Text Colors
        'text_primary': '#2C3E50',      # Primary text
        'text_secondary': '#7F8C8D',    # Secondary text
        'text_white': '#FFFFFF',        # White text
        'text_muted': '#95A5A6',        # Muted text
    }
    
    # Enhanced Font Configurations with Cambria Bold Italic
    FONTS = {
        'title': ('Cambria', 32, 'bold italic'),        # Professional title font
        'heading': ('Cambria', 24, 'bold italic'),      # Section headings
        'subheading': ('Cambria', 20, 'bold italic'),   # Subsection headings
        'body': ('Cambria', 16, 'bold italic'),         # Regular text
        'body_bold': ('Cambria', 16, 'bold italic'),    # Bold body text
        'small': ('Cambria', 14, 'bold italic'),        # Small text
        'button': ('Cambria', 18, 'bold italic'),       # Button text
        'menu': ('Cambria', 16, 'bold italic'),         # Menu text
        'large': ('Cambria', 22, 'bold italic'),        # Large text
        'xlarge': ('Cambria', 28, 'bold italic'),       # Extra large text
    }

    # Maroon color for light backgrounds
    MAROON = '#800000'

    # Dark backgrounds that need white text
    DARK_BACKGROUNDS = {
        '#2C3E50', '#34495E', '#E74C3C', '#EC7063', '#F39C12', '#F8C471',
        '#27AE60', '#58D68D', '#F1C40F', '#7F8C8D', '#95A5A6', '#800000'
    }

    # Light backgrounds that need maroon text
    LIGHT_BACKGROUNDS = {
        '#FFFFFF', '#F8F9FA', '#ECF0F1', '#BDC3C7', '#D5DBDB', '#AED6F1',
        '#F7DC6F', '#F1948A', '#e9ecef', '#dee2e6', '#ced4da', '#adb5bd'
    }

    @classmethod
    def get_text_color(cls, background_color):
        """
        Automatically determine text color based on background
        Returns 'white' for dark backgrounds, maroon for light backgrounds
        """
        bg_upper = background_color.upper()

        if bg_upper in cls.DARK_BACKGROUNDS:
            return cls.COLORS['text_white']
        elif bg_upper in cls.LIGHT_BACKGROUNDS:
            return cls.MAROON
        else:
            # For unknown colors, use luminance calculation
            bg = background_color.lstrip('#')
            if len(bg) == 6:
                try:
                    r, g, b = tuple(int(bg[i:i+2], 16) for i in (0, 2, 4))
                    luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255
                    return cls.COLORS['text_white'] if luminance < 0.5 else cls.MAROON
                except ValueError:
                    return cls.MAROON
            else:
                return cls.MAROON  # Default to maroon

    @classmethod
    def create_smart_label(cls, parent, text, bg_color, font_size='body', bold=False, **kwargs):
        """Create label with automatic text color based on background"""
        import tkinter as tk

        font_tuple = list(cls.FONTS[font_size])
        if bold and len(font_tuple) == 2:
            font_tuple.append('bold')
        elif bold and font_tuple[2] != 'bold':
            font_tuple[2] = 'bold'

        text_color = cls.get_text_color(bg_color)

        return tk.Label(parent, text=text, bg=bg_color, fg=text_color,
                       font=tuple(font_tuple), **kwargs)

    @classmethod
    def create_smart_button(cls, parent, text, bg_color, command=None, font_size='button', **kwargs):
        """Create button with automatic text color based on background"""
        import tkinter as tk

        text_color = cls.get_text_color(bg_color)

        return tk.Button(parent, text=text, bg=bg_color, fg=text_color,
                        font=cls.FONTS[font_size], command=command,
                        relief='flat', cursor='hand2', **kwargs)
    
    # Widget Styles
    WIDGET_STYLES = {
        'button_primary': {
            'bg': COLORS['secondary'],
            'fg': COLORS['text_white'],
            'font': FONTS['button'],
            'relief': 'flat',
            'bd': 0,
            'padx': 20,
            'pady': 8,
            'cursor': 'hand2'
        },
        'button_secondary': {
            'bg': COLORS['primary'],
            'fg': COLORS['text_white'],
            'font': FONTS['button'],
            'relief': 'flat',
            'bd': 0,
            'padx': 20,
            'pady': 8,
            'cursor': 'hand2'
        },
        'button_success': {
            'bg': COLORS['success'],
            'fg': COLORS['text_white'],
            'font': FONTS['button'],
            'relief': 'flat',
            'bd': 0,
            'padx': 20,
            'pady': 8,
            'cursor': 'hand2'
        },
        'button_warning': {
            'bg': COLORS['warning'],
            'fg': COLORS['text_primary'],
            'font': FONTS['button'],
            'relief': 'flat',
            'bd': 0,
            'padx': 20,
            'pady': 8,
            'cursor': 'hand2'
        },
        'button_danger': {
            'bg': COLORS['danger'],
            'fg': COLORS['text_white'],
            'font': FONTS['button'],
            'relief': 'flat',
            'bd': 0,
            'padx': 20,
            'pady': 8,
            'cursor': 'hand2'
        },
        'entry': {
            'relief': 'solid',
            'bd': 1,
            'highlightthickness': 2,
            'highlightcolor': COLORS['secondary'],
            'bg': COLORS['white'],
            'fg': COLORS['text_primary']
        },
        'label_title': {
            'font': FONTS['title'],
            'fg': COLORS['text_primary'],
            'bg': COLORS['bg_main']
        },
        'label_heading': {
            'font': FONTS['heading'],
            'fg': COLORS['text_primary'],
            'bg': COLORS['bg_main']
        },
        'label_body': {
            'font': FONTS['body'],
            'fg': COLORS['text_primary'],
            'bg': COLORS['bg_main']
        },
        'frame_main': {
            'bg': COLORS['bg_main'],
            'relief': 'flat',
            'bd': 0
        },
        'frame_sidebar': {
            'bg': COLORS['bg_sidebar'],
            'relief': 'flat',
            'bd': 0
        },
        'frame_card': {
            'bg': COLORS['bg_card'],
            'relief': 'solid',
            'bd': 1,
            'padx': 20,
            'pady': 15
        }
    }
    
    @classmethod
    def configure_ttk_styles(cls, root):
        """Configure TTK styles for modern appearance"""
        style = ttk.Style(root)
        
        # Configure Treeview with Russian text support and Cambria font
        style.configure("Modern.Treeview",
                       background='#ffffff',  # Pure white background
                       foreground='#1f2937',  # Dark gray text for contrast
                       fieldbackground='#ffffff',
                       font=('Cambria', 16, 'bold italic'),  # Professional Cambria font
                       rowheight=30)  # More space for larger font

        style.configure("Modern.Treeview.Heading",
                       background='#1e3a8a',  # Тёмно-синий цвет
                       foreground='#ffffff',  # Белый текст
                       font=('Cambria', 18, 'bold italic'),  # Professional header font
                       relief='solid',
                       borderwidth=2,
                       focuscolor='none')

        # Add hover effect for headers
        style.map("Modern.Treeview.Heading",
                 background=[('active', '#2563eb'),  # Светлее тёмно-синий при наведении
                           ('pressed', '#1d4ed8')],   # Ещё светлее при нажатии
                 foreground=[('active', '#ffffff'),
                           ('pressed', '#ffffff')])

        # Add alternating row colors and selection highlighting
        style.map("Modern.Treeview",
                 background=[('selected', '#3b82f6'),  # Blue selection
                           ('focus', '#3b82f6')],
                 foreground=[('selected', '#ffffff'),
                           ('focus', '#ffffff')])

        # Configure alternating row colors
        style.configure("Modern.Treeview",
                       selectbackground='#3b82f6',
                       selectforeground='#ffffff')
        
        # Configure Notebook
        style.configure("Modern.TNotebook",
                       background=cls.COLORS['bg_main'],
                       borderwidth=0)
        
        style.configure("Modern.TNotebook.Tab",
                       background=cls.COLORS['gray'],
                       foreground=cls.COLORS['text_primary'],
                       font=cls.FONTS['body_bold'],
                       padding=[20, 10])
        
        style.map("Modern.TNotebook.Tab",
                 background=[('selected', cls.COLORS['secondary']),
                           ('active', cls.COLORS['secondary_light'])],
                 foreground=[('selected', cls.COLORS['text_white']),
                           ('active', cls.COLORS['text_white'])])
        
        # Configure Combobox
        style.configure("Modern.TCombobox",
                       fieldbackground=cls.COLORS['white'],
                       background=cls.COLORS['white'],
                       foreground=cls.COLORS['text_primary'],
                       font=cls.FONTS['body'])
        
        # Configure Progressbar
        style.configure("Modern.TProgressbar",
                       background=cls.COLORS['success'],
                       troughcolor=cls.COLORS['light_gray'],
                       borderwidth=0,
                       lightcolor=cls.COLORS['success'],
                       darkcolor=cls.COLORS['success'])
    
    @classmethod
    def apply_hover_effect(cls, widget, enter_color=None, leave_color=None):
        """Apply hover effect to widgets"""
        if enter_color is None:
            enter_color = cls.COLORS['bg_hover']
        if leave_color is None:
            leave_color = widget.cget('bg')
        
        def on_enter(event):
            widget.configure(bg=enter_color)
        
        def on_leave(event):
            widget.configure(bg=leave_color)
        
        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)
    
    @classmethod
    def create_card_frame(cls, parent, **kwargs):
        """Create a modern card-style frame"""
        frame = tk.Frame(parent, **cls.WIDGET_STYLES['frame_card'], **kwargs)
        return frame
    
    @classmethod
    def create_sidebar_button(cls, parent, text, command=None, **kwargs):
        """Create a sidebar navigation button"""
        button = tk.Button(parent, text=text, command=command,
                          bg=cls.COLORS['bg_sidebar'],
                          fg=cls.COLORS['text_white'],
                          font=cls.FONTS['menu'],
                          relief='flat',
                          bd=0,
                          padx=20,
                          pady=15,
                          anchor='w',
                          cursor='hand2',
                          **kwargs)
        
        # Add hover effect
        def on_enter(event):
            button.configure(bg=cls.COLORS['secondary'])
        
        def on_leave(event):
            button.configure(bg=cls.COLORS['bg_sidebar'])
        
        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)
        
        return button


# Alias for backward compatibility
EnhancedStyles = ModernStyles
