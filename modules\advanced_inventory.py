"""
Advanced Inventory Management System
Enhanced inventory system with predictive ordering, supplier integration, waste tracking, and automated reordering
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
import json
import sys
import os
from typing import Dict, List, Optional, Tuple

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gui.styles import ModernStyles
from utils.error_handler import handle_module_error, log_info

class AdvancedInventoryManager:
    """Advanced inventory management with predictive analytics and automation"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.inventory_items = []
        self.suppliers = []
        self.waste_records = []
        self.purchase_orders = []
        
        # Create main window
        self.window = tk.Toplevel(parent)
        self.window.title("📦 Расширенное Управление Складом")
        self.window.geometry("1400x900")
        self.window.configure(bg=ModernStyles.COLORS['bg_primary'])
        
        # Center window
        self.window.transient(parent)
        self.window.grab_set()
        
        self.create_widgets()
        self.load_data()
        
        log_info("Расширенное управление складом открыто", "AdvancedInventory")
    
    def create_widgets(self):
        """Create the advanced inventory interface"""
        # Main container
        main_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Title
        title_label = tk.Label(
            main_frame,
            text="📦 Расширенное Управление Складом и Закупками",
            font=ModernStyles.FONTS['title'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        title_label.pack(pady=(0, 20))
        
        # Create notebook for different sections
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill='both', expand=True)
        
        # Inventory Overview Tab
        self.create_inventory_overview_tab()
        
        # Predictive Ordering Tab
        self.create_predictive_ordering_tab()
        
        # Supplier Management Tab
        self.create_supplier_management_tab()
        
        # Waste Tracking Tab
        self.create_waste_tracking_tab()
        
        # Purchase Orders Tab
        self.create_purchase_orders_tab()
        
        # Analytics Tab
        self.create_analytics_tab()
        
        # Control buttons
        self.create_control_buttons(main_frame)
    
    def create_inventory_overview_tab(self):
        """Create inventory overview tab with advanced features"""
        inventory_frame = ttk.Frame(self.notebook)
        self.notebook.add(inventory_frame, text="📋 Обзор Склада")
        
        # Quick stats frame
        stats_frame = tk.LabelFrame(
            inventory_frame,
            text="Быстрая Статистика",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        stats_frame.pack(fill='x', padx=10, pady=10)
        
        # Stats display
        stats_container = tk.Frame(stats_frame, bg=ModernStyles.COLORS['bg_secondary'])
        stats_container.pack(fill='x', padx=10, pady=10)
        
        # Create stat cards
        self.create_stat_card(stats_container, "Всего товаров", "0", "📦", 0, 0)
        self.create_stat_card(stats_container, "Низкий остаток", "0", "⚠️", 0, 1)
        self.create_stat_card(stats_container, "Стоимость склада", "0 руб", "💰", 0, 2)
        self.create_stat_card(stats_container, "Активные заказы", "0", "🛒", 0, 3)
        
        # Inventory table frame
        table_frame = tk.LabelFrame(
            inventory_frame,
            text="Товары на Складе",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        table_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Create advanced inventory table
        columns = ('ID', 'Название', 'Категория', 'Остаток', 'Ед.изм.', 'Мин.остаток', 
                  'Макс.остаток', 'Цена', 'Поставщик', 'Последнее обновление', 'Статус')
        self.inventory_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        column_widths = {
            'ID': 50, 'Название': 150, 'Категория': 100, 'Остаток': 80,
            'Ед.изм.': 60, 'Мин.остаток': 80, 'Макс.остаток': 80, 'Цена': 100,
            'Поставщик': 120, 'Последнее обновление': 120, 'Статус': 100
        }
        
        for col in columns:
            self.inventory_tree.heading(col, text=col)
            self.inventory_tree.column(col, width=column_widths.get(col, 100))
        
        # Configure tags for status colors
        self.inventory_tree.tag_configure('low_stock', background='#ffebee', foreground='#c62828')
        self.inventory_tree.tag_configure('out_of_stock', background='#f3e5f5', foreground='#7b1fa2')
        self.inventory_tree.tag_configure('overstock', background='#e8f5e8', foreground='#2e7d32')
        self.inventory_tree.tag_configure('normal', background='white', foreground='black')
        
        # Scrollbars
        inventory_v_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.inventory_tree.yview)
        inventory_h_scrollbar = ttk.Scrollbar(table_frame, orient="horizontal", command=self.inventory_tree.xview)
        self.inventory_tree.configure(yscrollcommand=inventory_v_scrollbar.set, xscrollcommand=inventory_h_scrollbar.set)
        
        self.inventory_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        inventory_v_scrollbar.pack(side="right", fill="y", pady=10)
        
        # Inventory action buttons
        inventory_buttons_frame = tk.Frame(inventory_frame, bg=ModernStyles.COLORS['bg_primary'])
        inventory_buttons_frame.pack(fill='x', padx=10, pady=10)
        
        # Add item button
        add_item_btn = tk.Button(
            inventory_buttons_frame,
            text="➕ Добавить Товар",
            command=self.add_inventory_item,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['success'],
            fg='white',
            relief='flat',
            padx=15,
            pady=8
        )
        add_item_btn.pack(side='left', padx=(0, 10))
        
        # Edit item button
        edit_item_btn = tk.Button(
            inventory_buttons_frame,
            text="✏️ Редактировать",
            command=self.edit_inventory_item,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['accent'],
            fg='white',
            relief='flat',
            padx=15,
            pady=8
        )
        edit_item_btn.pack(side='left', padx=(0, 10))
        
        # Stock adjustment button
        adjust_stock_btn = tk.Button(
            inventory_buttons_frame,
            text="📊 Корректировка Остатков",
            command=self.adjust_stock,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['warning'],
            fg='white',
            relief='flat',
            padx=15,
            pady=8
        )
        adjust_stock_btn.pack(side='left', padx=(0, 10))
        
        # Generate order button
        generate_order_btn = tk.Button(
            inventory_buttons_frame,
            text="🛒 Создать Заказ",
            command=self.generate_purchase_order,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['accent'],
            fg='white',
            relief='flat',
            padx=15,
            pady=8
        )
        generate_order_btn.pack(side='left', padx=(0, 10))
    
    def create_stat_card(self, parent, title, value, icon, row, col):
        """Create a statistics card"""
        card_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_primary'], relief='raised', bd=1)
        card_frame.grid(row=row, column=col, padx=10, pady=5, sticky='ew')
        parent.grid_columnconfigure(col, weight=1)
        
        # Icon and title
        header_frame = tk.Frame(card_frame, bg=ModernStyles.COLORS['bg_primary'])
        header_frame.pack(fill='x', padx=10, pady=(10, 5))
        
        icon_label = tk.Label(
            header_frame,
            text=icon,
            font=('Arial', 16),
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['accent']
        )
        icon_label.pack(side='left')
        
        title_label = tk.Label(
            header_frame,
            text=title,
            font=ModernStyles.FONTS['body'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_secondary']
        )
        title_label.pack(side='left', padx=(10, 0))
        
        # Value
        value_label = tk.Label(
            card_frame,
            text=value,
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        value_label.pack(padx=10, pady=(0, 10))
        
        # Store reference for updates
        setattr(self, f"stat_{title.lower().replace(' ', '_')}_label", value_label)
    
    def create_predictive_ordering_tab(self):
        """Create predictive ordering tab"""
        predict_frame = ttk.Frame(self.notebook)
        self.notebook.add(predict_frame, text="🔮 Прогнозирование")
        
        # Prediction settings
        settings_frame = tk.LabelFrame(
            predict_frame,
            text="Настройки Прогнозирования",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        settings_frame.pack(fill='x', padx=10, pady=10)
        
        settings_content = tk.Frame(settings_frame, bg=ModernStyles.COLORS['bg_secondary'])
        settings_content.pack(fill='x', padx=10, pady=10)
        
        # Prediction period
        tk.Label(settings_content, text="Период прогноза (дни):", font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_secondary'], fg=ModernStyles.COLORS['text_primary']).grid(row=0, column=0, sticky='w', padx=(0, 10))
        
        self.prediction_period_var = tk.StringVar(value="30")
        period_entry = tk.Entry(settings_content, textvariable=self.prediction_period_var, width=10)
        period_entry.grid(row=0, column=1, padx=(0, 20))
        
        # Safety stock percentage
        tk.Label(settings_content, text="Страховой запас (%):", font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_secondary'], fg=ModernStyles.COLORS['text_primary']).grid(row=0, column=2, sticky='w', padx=(0, 10))
        
        self.safety_stock_var = tk.StringVar(value="20")
        safety_entry = tk.Entry(settings_content, textvariable=self.safety_stock_var, width=10)
        safety_entry.grid(row=0, column=3, padx=(0, 20))
        
        # Generate predictions button
        predict_btn = tk.Button(
            settings_content,
            text="🔮 Создать Прогноз",
            command=self.generate_predictions,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['accent'],
            fg='white',
            relief='flat',
            padx=15,
            pady=5
        )
        predict_btn.grid(row=0, column=4, padx=(0, 10))
        
        # Predictions table
        predictions_frame = tk.LabelFrame(
            predict_frame,
            text="Рекомендации по Заказам",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        predictions_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Create predictions table
        pred_columns = ('Товар', 'Текущий остаток', 'Прогноз потребления', 'Рекомендуемый заказ', 
                       'Поставщик', 'Стоимость заказа', 'Приоритет')
        self.predictions_tree = ttk.Treeview(predictions_frame, columns=pred_columns, show='headings', height=12)
        
        for col in pred_columns:
            self.predictions_tree.heading(col, text=col)
            self.predictions_tree.column(col, width=120)
        
        # Configure priority tags
        self.predictions_tree.tag_configure('high_priority', background='#ffebee', foreground='#c62828')
        self.predictions_tree.tag_configure('medium_priority', background='#fff3e0', foreground='#ef6c00')
        self.predictions_tree.tag_configure('low_priority', background='#e8f5e8', foreground='#2e7d32')
        
        # Scrollbars for predictions
        pred_v_scrollbar = ttk.Scrollbar(predictions_frame, orient="vertical", command=self.predictions_tree.yview)
        self.predictions_tree.configure(yscrollcommand=pred_v_scrollbar.set)
        
        self.predictions_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        pred_v_scrollbar.pack(side="right", fill="y", pady=10)
    
    def create_supplier_management_tab(self):
        """Create supplier management tab"""
        supplier_frame = ttk.Frame(self.notebook)
        self.notebook.add(supplier_frame, text="🏢 Поставщики")
        
        # Supplier list
        supplier_list_frame = tk.LabelFrame(
            supplier_frame,
            text="Список Поставщиков",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        supplier_list_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Create suppliers table
        supplier_columns = ('ID', 'Название', 'Контакт', 'Телефон', 'Email', 'Адрес', 
                           'Рейтинг', 'Условия оплаты', 'Время доставки')
        self.suppliers_tree = ttk.Treeview(supplier_list_frame, columns=supplier_columns, show='headings', height=15)
        
        supplier_widths = {
            'ID': 50, 'Название': 150, 'Контакт': 120, 'Телефон': 100,
            'Email': 150, 'Адрес': 200, 'Рейтинг': 80, 'Условия оплаты': 120, 'Время доставки': 100
        }
        
        for col in supplier_columns:
            self.suppliers_tree.heading(col, text=col)
            self.suppliers_tree.column(col, width=supplier_widths.get(col, 100))
        
        # Scrollbars for suppliers
        supplier_v_scrollbar = ttk.Scrollbar(supplier_list_frame, orient="vertical", command=self.suppliers_tree.yview)
        supplier_h_scrollbar = ttk.Scrollbar(supplier_list_frame, orient="horizontal", command=self.suppliers_tree.xview)
        self.suppliers_tree.configure(yscrollcommand=supplier_v_scrollbar.set, xscrollcommand=supplier_h_scrollbar.set)
        
        self.suppliers_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        supplier_v_scrollbar.pack(side="right", fill="y", pady=10)
        
        # Supplier management buttons
        supplier_buttons_frame = tk.Frame(supplier_frame, bg=ModernStyles.COLORS['bg_primary'])
        supplier_buttons_frame.pack(fill='x', padx=10, pady=10)
        
        # Add supplier button
        add_supplier_btn = tk.Button(
            supplier_buttons_frame,
            text="➕ Добавить Поставщика",
            command=self.add_supplier,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['success'],
            fg='white',
            relief='flat',
            padx=15,
            pady=8
        )
        add_supplier_btn.pack(side='left', padx=(0, 10))
        
        # Edit supplier button
        edit_supplier_btn = tk.Button(
            supplier_buttons_frame,
            text="✏️ Редактировать",
            command=self.edit_supplier,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['accent'],
            fg='white',
            relief='flat',
            padx=15,
            pady=8
        )
        edit_supplier_btn.pack(side='left', padx=(0, 10))
        
        # Supplier performance button
        performance_btn = tk.Button(
            supplier_buttons_frame,
            text="📊 Анализ Поставщиков",
            command=self.analyze_supplier_performance,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['accent'],
            fg='white',
            relief='flat',
            padx=15,
            pady=8
        )
        performance_btn.pack(side='left', padx=(0, 10))

    def create_waste_tracking_tab(self):
        """Create waste tracking tab"""
        waste_frame = ttk.Frame(self.notebook)
        self.notebook.add(waste_frame, text="🗑️ Учет Потерь")

        # Waste entry form
        waste_entry_frame = tk.LabelFrame(
            waste_frame,
            text="Регистрация Потерь",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        waste_entry_frame.pack(fill='x', padx=10, pady=10)

        entry_content = tk.Frame(waste_entry_frame, bg=ModernStyles.COLORS['bg_secondary'])
        entry_content.pack(fill='x', padx=10, pady=10)

        # Item selection
        tk.Label(entry_content, text="Товар:", font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_secondary'], fg=ModernStyles.COLORS['text_primary']).grid(row=0, column=0, sticky='w', padx=(0, 10))

        self.waste_item_var = tk.StringVar()
        waste_item_combo = ttk.Combobox(entry_content, textvariable=self.waste_item_var, width=20)
        waste_item_combo.grid(row=0, column=1, padx=(0, 20))

        # Quantity
        tk.Label(entry_content, text="Количество:", font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_secondary'], fg=ModernStyles.COLORS['text_primary']).grid(row=0, column=2, sticky='w', padx=(0, 10))

        self.waste_quantity_var = tk.StringVar()
        waste_quantity_entry = tk.Entry(entry_content, textvariable=self.waste_quantity_var, width=10)
        waste_quantity_entry.grid(row=0, column=3, padx=(0, 20))

        # Reason
        tk.Label(entry_content, text="Причина:", font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_secondary'], fg=ModernStyles.COLORS['text_primary']).grid(row=1, column=0, sticky='w', padx=(0, 10), pady=(10, 0))

        self.waste_reason_var = tk.StringVar()
        waste_reason_combo = ttk.Combobox(entry_content, textvariable=self.waste_reason_var,
                                         values=["Истек срок годности", "Порча", "Брак", "Потеря", "Другое"], width=20)
        waste_reason_combo.grid(row=1, column=1, padx=(0, 20), pady=(10, 0))

        # Add waste button
        add_waste_btn = tk.Button(
            entry_content,
            text="➕ Зарегистрировать Потерю",
            command=self.add_waste_record,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['danger'],
            fg='white',
            relief='flat',
            padx=15,
            pady=5
        )
        add_waste_btn.grid(row=1, column=2, columnspan=2, padx=(0, 10), pady=(10, 0))

        # Waste records table
        waste_records_frame = tk.LabelFrame(
            waste_frame,
            text="История Потерь",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        waste_records_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Create waste records table
        waste_columns = ('Дата', 'Товар', 'Количество', 'Ед.изм.', 'Причина', 'Стоимость потерь', 'Ответственный')
        self.waste_tree = ttk.Treeview(waste_records_frame, columns=waste_columns, show='headings', height=12)

        for col in waste_columns:
            self.waste_tree.heading(col, text=col)
            self.waste_tree.column(col, width=120)

        # Scrollbars for waste records
        waste_v_scrollbar = ttk.Scrollbar(waste_records_frame, orient="vertical", command=self.waste_tree.yview)
        self.waste_tree.configure(yscrollcommand=waste_v_scrollbar.set)

        self.waste_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        waste_v_scrollbar.pack(side="right", fill="y", pady=10)

    def create_purchase_orders_tab(self):
        """Create purchase orders tab"""
        orders_frame = ttk.Frame(self.notebook)
        self.notebook.add(orders_frame, text="🛒 Заказы Поставщикам")

        # Orders list
        orders_list_frame = tk.LabelFrame(
            orders_frame,
            text="Заказы Поставщикам",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        orders_list_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Create orders table
        order_columns = ('№ Заказа', 'Дата', 'Поставщик', 'Статус', 'Сумма', 'Дата доставки', 'Товаров', 'Ответственный')
        self.orders_tree = ttk.Treeview(orders_list_frame, columns=order_columns, show='headings', height=15)

        order_widths = {
            '№ Заказа': 80, 'Дата': 100, 'Поставщик': 150, 'Статус': 100,
            'Сумма': 100, 'Дата доставки': 100, 'Товаров': 80, 'Ответственный': 120
        }

        for col in order_columns:
            self.orders_tree.heading(col, text=col)
            self.orders_tree.column(col, width=order_widths.get(col, 100))

        # Configure status tags
        self.orders_tree.tag_configure('pending', background='#fff3e0', foreground='#ef6c00')
        self.orders_tree.tag_configure('approved', background='#e8f5e8', foreground='#2e7d32')
        self.orders_tree.tag_configure('delivered', background='#e3f2fd', foreground='#1976d2')
        self.orders_tree.tag_configure('cancelled', background='#ffebee', foreground='#c62828')

        # Scrollbars for orders
        orders_v_scrollbar = ttk.Scrollbar(orders_list_frame, orient="vertical", command=self.orders_tree.yview)
        orders_h_scrollbar = ttk.Scrollbar(orders_list_frame, orient="horizontal", command=self.orders_tree.xview)
        self.orders_tree.configure(yscrollcommand=orders_v_scrollbar.set, xscrollcommand=orders_h_scrollbar.set)

        self.orders_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        orders_v_scrollbar.pack(side="right", fill="y", pady=10)

        # Order management buttons
        order_buttons_frame = tk.Frame(orders_frame, bg=ModernStyles.COLORS['bg_primary'])
        order_buttons_frame.pack(fill='x', padx=10, pady=10)

        # Create order button
        create_order_btn = tk.Button(
            order_buttons_frame,
            text="➕ Создать Заказ",
            command=self.create_purchase_order,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['success'],
            fg='white',
            relief='flat',
            padx=15,
            pady=8
        )
        create_order_btn.pack(side='left', padx=(0, 10))

        # View order button
        view_order_btn = tk.Button(
            order_buttons_frame,
            text="👁️ Просмотр Заказа",
            command=self.view_purchase_order,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['accent'],
            fg='white',
            relief='flat',
            padx=15,
            pady=8
        )
        view_order_btn.pack(side='left', padx=(0, 10))

        # Approve order button
        approve_order_btn = tk.Button(
            order_buttons_frame,
            text="✅ Утвердить Заказ",
            command=self.approve_purchase_order,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['success'],
            fg='white',
            relief='flat',
            padx=15,
            pady=8
        )
        approve_order_btn.pack(side='left', padx=(0, 10))

        # Receive order button
        receive_order_btn = tk.Button(
            order_buttons_frame,
            text="📦 Получить Заказ",
            command=self.receive_purchase_order,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['accent'],
            fg='white',
            relief='flat',
            padx=15,
            pady=8
        )
        receive_order_btn.pack(side='left', padx=(0, 10))

    def create_analytics_tab(self):
        """Create analytics and reporting tab"""
        analytics_frame = ttk.Frame(self.notebook)
        self.notebook.add(analytics_frame, text="📊 Аналитика")

        # Analytics dashboard
        dashboard_frame = tk.LabelFrame(
            analytics_frame,
            text="Панель Аналитики",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        dashboard_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Create analytics charts placeholder
        charts_frame = tk.Frame(dashboard_frame, bg=ModernStyles.COLORS['bg_secondary'])
        charts_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Top performing items
        top_items_frame = tk.LabelFrame(
            charts_frame,
            text="Топ Товаров по Обороту",
            font=ModernStyles.FONTS['body'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        top_items_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))

        self.top_items_listbox = tk.Listbox(top_items_frame, height=10)
        self.top_items_listbox.pack(fill='both', expand=True, padx=10, pady=10)

        # Waste analysis
        waste_analysis_frame = tk.LabelFrame(
            charts_frame,
            text="Анализ Потерь",
            font=ModernStyles.FONTS['body'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        waste_analysis_frame.pack(side='right', fill='both', expand=True)

        self.waste_analysis_listbox = tk.Listbox(waste_analysis_frame, height=10)
        self.waste_analysis_listbox.pack(fill='both', expand=True, padx=10, pady=10)

        # Analytics buttons
        analytics_buttons_frame = tk.Frame(analytics_frame, bg=ModernStyles.COLORS['bg_primary'])
        analytics_buttons_frame.pack(fill='x', padx=10, pady=10)

        # Generate report button
        generate_report_btn = tk.Button(
            analytics_buttons_frame,
            text="📊 Создать Отчет",
            command=self.generate_inventory_report,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['accent'],
            fg='white',
            relief='flat',
            padx=15,
            pady=8
        )
        generate_report_btn.pack(side='left', padx=(0, 10))

        # Export data button
        export_data_btn = tk.Button(
            analytics_buttons_frame,
            text="💾 Экспорт Данных",
            command=self.export_inventory_data,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['accent'],
            fg='white',
            relief='flat',
            padx=15,
            pady=8
        )
        export_data_btn.pack(side='left', padx=(0, 10))

    def create_control_buttons(self, parent):
        """Create control buttons"""
        buttons_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_primary'])
        buttons_frame.pack(fill='x', pady=(20, 0))

        # Refresh button
        refresh_btn = tk.Button(
            buttons_frame,
            text="🔄 Обновить Данные",
            command=self.refresh_all_data,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['accent'],
            fg='white',
            relief='flat',
            padx=20,
            pady=10
        )
        refresh_btn.pack(side='left', padx=(0, 10))

        # Settings button
        settings_btn = tk.Button(
            buttons_frame,
            text="⚙️ Настройки",
            command=self.open_settings,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['accent'],
            fg='white',
            relief='flat',
            padx=20,
            pady=10
        )
        settings_btn.pack(side='left', padx=(0, 10))

        # Close button
        close_btn = tk.Button(
            buttons_frame,
            text="❌ Закрыть",
            command=self.window.destroy,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['danger'],
            fg='white',
            relief='flat',
            padx=20,
            pady=10
        )
        close_btn.pack(side='right')

    def load_data(self):
        """Load all inventory data"""
        try:
            self.load_inventory_items()
            self.load_suppliers()
            self.load_waste_records()
            self.load_purchase_orders()
            self.update_statistics()
            self.refresh_all_displays()
        except Exception as e:
            handle_module_error(e, "Расширенное управление складом", "загрузка данных")

    def load_inventory_items(self):
        """Load inventory items from database"""
        try:
            raw_materials = self.db_manager.get_raw_materials()
            self.inventory_items = []

            for material in raw_materials:
                item = {
                    "id": material['id'],
                    "name": material['name'],
                    "category": material['category'] or "Без категории",
                    "current_stock": material['current_stock'],
                    "unit": material['unit_of_measure'],
                    "min_stock": material['minimum_stock'],
                    "max_stock": material['minimum_stock'] * 3,  # Calculate max stock
                    "cost_per_unit": material['average_cost'],
                    "supplier": material['supplier'] or "Не указан",
                    "last_updated": material.get('updated_at', datetime.now().strftime('%Y-%m-%d')),
                    "status": self.calculate_stock_status(material['current_stock'], material['minimum_stock'])
                }
                self.inventory_items.append(item)

        except Exception as e:
            print(f"Ошибка загрузки товаров: {e}")
            self.inventory_items = []

    def calculate_stock_status(self, current_stock, min_stock):
        """Calculate stock status"""
        if current_stock <= 0:
            return "Нет в наличии"
        elif current_stock <= min_stock:
            return "Низкий остаток"
        elif current_stock > min_stock * 2:
            return "Избыток"
        else:
            return "Нормальный"

    def load_suppliers(self):
        """Load suppliers data"""
        # Sample suppliers data - in real implementation, load from database
        self.suppliers = [
            {
                "id": 1,
                "name": "Овощи-Плюс",
                "contact": "Иван Петров",
                "phone": "+7 (495) 123-45-67",
                "email": "<EMAIL>",
                "address": "Москва, ул. Складская, 15",
                "rating": 4.5,
                "payment_terms": "30 дней",
                "delivery_time": "1-2 дня"
            },
            {
                "id": 2,
                "name": "Мясной Двор",
                "contact": "Анна Сидорова",
                "phone": "+7 (495) 987-65-43",
                "email": "<EMAIL>",
                "address": "Москва, пр. Мясников, 8",
                "rating": 4.8,
                "payment_terms": "14 дней",
                "delivery_time": "Ежедневно"
            }
        ]

    def load_waste_records(self):
        """Load waste records"""
        # Sample waste records - in real implementation, load from database
        self.waste_records = [
            {
                "date": "2024-01-15",
                "item": "Помидоры",
                "quantity": 2.5,
                "unit": "кг",
                "reason": "Истек срок годности",
                "cost": 125.0,
                "responsible": "Склад"
            }
        ]

    def load_purchase_orders(self):
        """Load purchase orders"""
        # Sample purchase orders - in real implementation, load from database
        self.purchase_orders = [
            {
                "order_number": "PO-2024-001",
                "date": "2024-01-20",
                "supplier": "Овощи-Плюс",
                "status": "Ожидает",
                "total": 15750.0,
                "delivery_date": "2024-01-22",
                "items_count": 5,
                "responsible": "Менеджер"
            }
        ]

    def update_statistics(self):
        """Update statistics display"""
        try:
            total_items = len(self.inventory_items)
            low_stock_items = len([item for item in self.inventory_items if item['status'] == 'Низкий остаток'])
            total_value = sum(item['current_stock'] * item['cost_per_unit'] for item in self.inventory_items)
            active_orders = len([order for order in self.purchase_orders if order['status'] in ['Ожидает', 'Утвержден']])

            # Update stat labels if they exist
            if hasattr(self, 'stat_всего_товаров_label'):
                self.stat_всего_товаров_label.config(text=str(total_items))
            if hasattr(self, 'stat_низкий_остаток_label'):
                self.stat_низкий_остаток_label.config(text=str(low_stock_items))
            if hasattr(self, 'stat_стоимость_склада_label'):
                self.stat_стоимость_склада_label.config(text=f"{total_value:,.0f} руб".replace(',', ' '))
            if hasattr(self, 'stat_активные_заказы_label'):
                self.stat_активные_заказы_label.config(text=str(active_orders))

        except Exception as e:
            print(f"Ошибка обновления статистики: {e}")

    def refresh_all_displays(self):
        """Refresh all data displays"""
        self.refresh_inventory_display()
        self.refresh_suppliers_display()
        self.refresh_waste_display()
        self.refresh_orders_display()

    def refresh_inventory_display(self):
        """Refresh inventory table display"""
        # Clear existing items
        for item in self.inventory_tree.get_children():
            self.inventory_tree.delete(item)

        # Add items to tree
        for item in self.inventory_items:
            # Determine tag based on status
            tag = 'normal'
            if item['status'] == 'Низкий остаток':
                tag = 'low_stock'
            elif item['status'] == 'Нет в наличии':
                tag = 'out_of_stock'
            elif item['status'] == 'Избыток':
                tag = 'overstock'

            self.inventory_tree.insert('', 'end', values=(
                item['id'],
                item['name'],
                item['category'],
                f"{item['current_stock']:.1f}",
                item['unit'],
                f"{item['min_stock']:.1f}",
                f"{item['max_stock']:.1f}",
                f"{item['cost_per_unit']:.2f}",
                item['supplier'],
                item['last_updated'],
                item['status']
            ), tags=(tag,))

    def refresh_suppliers_display(self):
        """Refresh suppliers table display"""
        # Clear existing items
        for item in self.suppliers_tree.get_children():
            self.suppliers_tree.delete(item)

        # Add suppliers to tree
        for supplier in self.suppliers:
            self.suppliers_tree.insert('', 'end', values=(
                supplier['id'],
                supplier['name'],
                supplier['contact'],
                supplier['phone'],
                supplier['email'],
                supplier['address'],
                f"{supplier['rating']:.1f}",
                supplier['payment_terms'],
                supplier['delivery_time']
            ))

    def refresh_waste_display(self):
        """Refresh waste records display"""
        # Clear existing items
        for item in self.waste_tree.get_children():
            self.waste_tree.delete(item)

        # Add waste records to tree
        for record in self.waste_records:
            self.waste_tree.insert('', 'end', values=(
                record['date'],
                record['item'],
                f"{record['quantity']:.1f}",
                record['unit'],
                record['reason'],
                f"{record['cost']:.2f} руб",
                record['responsible']
            ))

    def refresh_orders_display(self):
        """Refresh purchase orders display"""
        # Clear existing items
        for item in self.orders_tree.get_children():
            self.orders_tree.delete(item)

        # Add orders to tree
        for order in self.purchase_orders:
            # Determine tag based on status
            tag = 'pending'
            if order['status'] == 'Утвержден':
                tag = 'approved'
            elif order['status'] == 'Доставлен':
                tag = 'delivered'
            elif order['status'] == 'Отменен':
                tag = 'cancelled'

            self.orders_tree.insert('', 'end', values=(
                order['order_number'],
                order['date'],
                order['supplier'],
                order['status'],
                f"{order['total']:.2f} руб",
                order['delivery_date'],
                order['items_count'],
                order['responsible']
            ), tags=(tag,))

    # Action Methods
    def add_inventory_item(self):
        """Add new inventory item"""
        try:
            dialog = InventoryItemDialog(self.window, "Добавить Товар", self.db_manager)
            if dialog.result:
                # Reload data and refresh display
                self.load_inventory_items()
                self.update_statistics()
                self.refresh_inventory_display()
                messagebox.showinfo("Успех", "Товар успешно добавлен!")
        except Exception as e:
            handle_module_error(e, "Расширенное управление складом", "добавление товара")

    def edit_inventory_item(self):
        """Edit selected inventory item"""
        try:
            selection = self.inventory_tree.selection()
            if not selection:
                messagebox.showwarning("Предупреждение", "Выберите товар для редактирования")
                return

            item_id = self.inventory_tree.item(selection[0])['values'][0]
            item_data = next((item for item in self.inventory_items if item['id'] == item_id), None)

            if item_data:
                dialog = InventoryItemDialog(self.window, "Редактировать Товар", self.db_manager, item_data)
                if dialog.result:
                    # Reload data and refresh display
                    self.load_inventory_items()
                    self.update_statistics()
                    self.refresh_inventory_display()
                    messagebox.showinfo("Успех", "Товар успешно обновлен!")
        except Exception as e:
            handle_module_error(e, "Расширенное управление складом", "редактирование товара")

    def adjust_stock(self):
        """Adjust stock levels"""
        try:
            selection = self.inventory_tree.selection()
            if not selection:
                messagebox.showwarning("Предупреждение", "Выберите товар для корректировки остатков")
                return

            item_id = self.inventory_tree.item(selection[0])['values'][0]
            item_data = next((item for item in self.inventory_items if item['id'] == item_id), None)

            if item_data:
                dialog = StockAdjustmentDialog(self.window, item_data)
                if dialog.result:
                    # Process stock adjustment
                    self.process_stock_adjustment(item_data, dialog.result)
                    messagebox.showinfo("Успех", "Остатки успешно скорректированы!")
        except Exception as e:
            handle_module_error(e, "Расширенное управление складом", "корректировка остатков")

    def generate_purchase_order(self):
        """Generate purchase order based on low stock items"""
        try:
            low_stock_items = [item for item in self.inventory_items if item['status'] == 'Низкий остаток']

            if not low_stock_items:
                messagebox.showinfo("Информация", "Нет товаров с низким остатком для заказа")
                return

            dialog = PurchaseOrderDialog(self.window, low_stock_items, self.suppliers)
            if dialog.result:
                # Create purchase order
                self.create_purchase_order_from_data(dialog.result)
                messagebox.showinfo("Успех", "Заказ поставщику создан!")
        except Exception as e:
            handle_module_error(e, "Расширенное управление складом", "создание заказа")

    def generate_predictions(self):
        """Generate predictive ordering recommendations"""
        try:
            period_days = int(self.prediction_period_var.get() or 30)
            safety_percentage = int(self.safety_stock_var.get() or 20)

            # Clear existing predictions
            for item in self.predictions_tree.get_children():
                self.predictions_tree.delete(item)

            # Generate predictions for each item
            for item in self.inventory_items:
                prediction = self.calculate_prediction(item, period_days, safety_percentage)
                if prediction['recommended_order'] > 0:
                    # Determine priority
                    priority = 'high_priority' if prediction['priority'] == 'Высокий' else \
                              'medium_priority' if prediction['priority'] == 'Средний' else 'low_priority'

                    self.predictions_tree.insert('', 'end', values=(
                        item['name'],
                        f"{item['current_stock']:.1f}",
                        f"{prediction['predicted_consumption']:.1f}",
                        f"{prediction['recommended_order']:.1f}",
                        item['supplier'],
                        f"{prediction['order_cost']:.2f} руб",
                        prediction['priority']
                    ), tags=(priority,))

            messagebox.showinfo("Успех", "Прогноз создан успешно!")

        except Exception as e:
            handle_module_error(e, "Расширенное управление складом", "создание прогноза")

    def calculate_prediction(self, item, period_days, safety_percentage):
        """Calculate prediction for an item"""
        # Simple prediction algorithm - in real implementation, use historical data
        daily_consumption = item['current_stock'] / 30  # Assume current stock lasts 30 days
        predicted_consumption = daily_consumption * period_days
        safety_stock = (item['min_stock'] * safety_percentage) / 100

        current_stock = item['current_stock']
        required_stock = predicted_consumption + safety_stock
        recommended_order = max(0, required_stock - current_stock)

        # Determine priority
        if current_stock <= item['min_stock']:
            priority = 'Высокий'
        elif current_stock <= item['min_stock'] * 1.5:
            priority = 'Средний'
        else:
            priority = 'Низкий'

        return {
            'predicted_consumption': predicted_consumption,
            'recommended_order': recommended_order,
            'order_cost': recommended_order * item['cost_per_unit'],
            'priority': priority
        }

    def add_supplier(self):
        """Add new supplier"""
        try:
            dialog = SupplierDialog(self.window, "Добавить Поставщика")
            if dialog.result:
                # Add supplier to list
                new_id = max([s['id'] for s in self.suppliers], default=0) + 1
                dialog.result['id'] = new_id
                self.suppliers.append(dialog.result)
                self.refresh_suppliers_display()
                messagebox.showinfo("Успех", "Поставщик успешно добавлен!")
        except Exception as e:
            handle_module_error(e, "Расширенное управление складом", "добавление поставщика")

    def edit_supplier(self):
        """Edit selected supplier"""
        try:
            selection = self.suppliers_tree.selection()
            if not selection:
                messagebox.showwarning("Предупреждение", "Выберите поставщика для редактирования")
                return

            supplier_id = self.suppliers_tree.item(selection[0])['values'][0]
            supplier_data = next((s for s in self.suppliers if s['id'] == supplier_id), None)

            if supplier_data:
                dialog = SupplierDialog(self.window, "Редактировать Поставщика", supplier_data)
                if dialog.result:
                    # Update supplier data
                    supplier_data.update(dialog.result)
                    self.refresh_suppliers_display()
                    messagebox.showinfo("Успех", "Поставщик успешно обновлен!")
        except Exception as e:
            handle_module_error(e, "Расширенное управление складом", "редактирование поставщика")

    def analyze_supplier_performance(self):
        """Analyze supplier performance"""
        try:
            # Create supplier performance analysis window
            analysis_window = tk.Toplevel(self.window)
            analysis_window.title("Анализ Поставщиков")
            analysis_window.geometry("800x600")
            analysis_window.configure(bg=ModernStyles.COLORS['bg_primary'])

            # Title
            title_label = tk.Label(
                analysis_window,
                text="📊 Анализ Эффективности Поставщиков",
                font=ModernStyles.FONTS['title'],
                bg=ModernStyles.COLORS['bg_primary'],
                fg=ModernStyles.COLORS['text_primary']
            )
            title_label.pack(pady=20)

            # Performance metrics (placeholder)
            metrics_text = tk.Text(analysis_window, height=20, width=80)
            metrics_text.pack(padx=20, pady=20, fill='both', expand=True)

            # Sample analysis data
            analysis_data = """
АНАЛИЗ ЭФФЕКТИВНОСТИ ПОСТАВЩИКОВ

1. Овощи-Плюс
   - Рейтинг: 4.5/5.0
   - Своевременность доставки: 95%
   - Качество товара: 4.3/5.0
   - Средняя стоимость заказа: 15,750 руб
   - Количество заказов: 24
   - Рекомендация: Надежный поставщик

2. Мясной Двор
   - Рейтинг: 4.8/5.0
   - Своевременность доставки: 98%
   - Качество товара: 4.7/5.0
   - Средняя стоимость заказа: 28,500 руб
   - Количество заказов: 18
   - Рекомендация: Отличный поставщик, приоритетный партнер
            """

            metrics_text.insert('1.0', analysis_data)
            metrics_text.config(state='disabled')

        except Exception as e:
            handle_module_error(e, "Расширенное управление складом", "анализ поставщиков")

    def add_waste_record(self):
        """Add waste record"""
        try:
            item_name = self.waste_item_var.get()
            quantity = float(self.waste_quantity_var.get() or 0)
            reason = self.waste_reason_var.get()

            if not item_name or quantity <= 0 or not reason:
                messagebox.showwarning("Предупреждение", "Заполните все поля")
                return

            # Find item to get cost
            item = next((i for i in self.inventory_items if i['name'] == item_name), None)
            cost = quantity * item['cost_per_unit'] if item else 0

            # Add waste record
            waste_record = {
                "date": datetime.now().strftime('%Y-%m-%d'),
                "item": item_name,
                "quantity": quantity,
                "unit": item['unit'] if item else "шт",
                "reason": reason,
                "cost": cost,
                "responsible": "Система"
            }

            self.waste_records.append(waste_record)
            self.refresh_waste_display()

            # Clear form
            self.waste_item_var.set("")
            self.waste_quantity_var.set("")
            self.waste_reason_var.set("")

            messagebox.showinfo("Успех", "Потеря зарегистрирована!")

        except Exception as e:
            handle_module_error(e, "Расширенное управление складом", "регистрация потерь")

    def create_purchase_order(self):
        """Create new purchase order"""
        try:
            dialog = PurchaseOrderDialog(self.window, [], self.suppliers)
            if dialog.result:
                self.create_purchase_order_from_data(dialog.result)
                messagebox.showinfo("Успех", "Заказ создан!")
        except Exception as e:
            handle_module_error(e, "Расширенное управление складом", "создание заказа")

    def view_purchase_order(self):
        """View selected purchase order"""
        try:
            selection = self.orders_tree.selection()
            if not selection:
                messagebox.showwarning("Предупреждение", "Выберите заказ для просмотра")
                return

            order_number = self.orders_tree.item(selection[0])['values'][0]
            order_data = next((o for o in self.purchase_orders if o['order_number'] == order_number), None)

            if order_data:
                self.show_order_details(order_data)
        except Exception as e:
            handle_module_error(e, "Расширенное управление складом", "просмотр заказа")

    def approve_purchase_order(self):
        """Approve selected purchase order"""
        try:
            selection = self.orders_tree.selection()
            if not selection:
                messagebox.showwarning("Предупреждение", "Выберите заказ для утверждения")
                return

            order_number = self.orders_tree.item(selection[0])['values'][0]
            order_data = next((o for o in self.purchase_orders if o['order_number'] == order_number), None)

            if order_data and order_data['status'] == 'Ожидает':
                order_data['status'] = 'Утвержден'
                self.refresh_orders_display()
                messagebox.showinfo("Успех", "Заказ утвержден!")
        except Exception as e:
            handle_module_error(e, "Расширенное управление складом", "утверждение заказа")

    def receive_purchase_order(self):
        """Receive purchase order"""
        try:
            selection = self.orders_tree.selection()
            if not selection:
                messagebox.showwarning("Предупреждение", "Выберите заказ для получения")
                return

            order_number = self.orders_tree.item(selection[0])['values'][0]
            order_data = next((o for o in self.purchase_orders if o['order_number'] == order_number), None)

            if order_data and order_data['status'] == 'Утвержден':
                order_data['status'] = 'Доставлен'
                self.refresh_orders_display()
                messagebox.showinfo("Успех", "Заказ получен!")
        except Exception as e:
            handle_module_error(e, "Расширенное управление складом", "получение заказа")

    def generate_inventory_report(self):
        """Generate inventory report"""
        try:
            # Create report window
            report_window = tk.Toplevel(self.window)
            report_window.title("Отчет по Складу")
            report_window.geometry("1000x700")
            report_window.configure(bg=ModernStyles.COLORS['bg_primary'])

            # Title
            title_label = tk.Label(
                report_window,
                text="📊 Отчет по Управлению Складом",
                font=ModernStyles.FONTS['title'],
                bg=ModernStyles.COLORS['bg_primary'],
                fg=ModernStyles.COLORS['text_primary']
            )
            title_label.pack(pady=20)

            # Report content
            report_text = tk.Text(report_window, height=30, width=100)
            report_text.pack(padx=20, pady=20, fill='both', expand=True)

            # Generate report content
            report_content = self.generate_report_content()
            report_text.insert('1.0', report_content)
            report_text.config(state='disabled')

            # Export button
            export_btn = tk.Button(
                report_window,
                text="💾 Экспорт в Файл",
                command=lambda: self.export_report(report_content),
                font=ModernStyles.FONTS['button'],
                bg=ModernStyles.COLORS['accent'],
                fg='white',
                relief='flat',
                padx=15,
                pady=8
            )
            export_btn.pack(pady=10)

        except Exception as e:
            handle_module_error(e, "Расширенное управление складом", "создание отчета")

    def export_inventory_data(self):
        """Export inventory data to file"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if filename:
                export_data = {
                    "inventory_items": self.inventory_items,
                    "suppliers": self.suppliers,
                    "waste_records": self.waste_records,
                    "purchase_orders": self.purchase_orders,
                    "export_date": datetime.now().isoformat()
                }

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)

                messagebox.showinfo("Успех", f"Данные экспортированы в {filename}")

        except Exception as e:
            handle_module_error(e, "Расширенное управление складом", "экспорт данных")

    def refresh_all_data(self):
        """Refresh all data"""
        try:
            self.load_data()
            messagebox.showinfo("Успех", "Данные обновлены!")
        except Exception as e:
            handle_module_error(e, "Расширенное управление складом", "обновление данных")

    def open_settings(self):
        """Open settings dialog"""
        try:
            dialog = InventorySettingsDialog(self.window)
            if dialog.result:
                messagebox.showinfo("Успех", "Настройки сохранены!")
        except Exception as e:
            handle_module_error(e, "Расширенное управление складом", "настройки")

    # Helper methods
    def process_stock_adjustment(self, item_data, adjustment_data):
        """Process stock adjustment"""
        # In real implementation, update database and create adjustment record
        print(f"Processing stock adjustment for {item_data['name']}: {adjustment_data}")

    def create_purchase_order_from_data(self, order_data):
        """Create purchase order from dialog data"""
        # In real implementation, save to database
        new_order = {
            "order_number": f"PO-{datetime.now().strftime('%Y-%m-%d-%H%M%S')}",
            "date": datetime.now().strftime('%Y-%m-%d'),
            "supplier": order_data.get('supplier', 'Не указан'),
            "status": "Ожидает",
            "total": order_data.get('total', 0),
            "delivery_date": order_data.get('delivery_date', ''),
            "items_count": len(order_data.get('items', [])),
            "responsible": "Система"
        }

        self.purchase_orders.append(new_order)
        self.refresh_orders_display()

    def show_order_details(self, order_data):
        """Show order details in popup"""
        details_window = tk.Toplevel(self.window)
        details_window.title(f"Заказ {order_data['order_number']}")
        details_window.geometry("600x400")
        details_window.configure(bg=ModernStyles.COLORS['bg_primary'])

        # Order details text
        details_text = tk.Text(details_window, height=20, width=70)
        details_text.pack(padx=20, pady=20, fill='both', expand=True)

        details_content = f"""
ДЕТАЛИ ЗАКАЗА

Номер заказа: {order_data['order_number']}
Дата: {order_data['date']}
Поставщик: {order_data['supplier']}
Статус: {order_data['status']}
Сумма: {order_data['total']:.2f} руб
Дата доставки: {order_data['delivery_date']}
Количество товаров: {order_data['items_count']}
Ответственный: {order_data['responsible']}
        """

        details_text.insert('1.0', details_content)
        details_text.config(state='disabled')

    def generate_report_content(self):
        """Generate report content"""
        total_items = len(self.inventory_items)
        low_stock_items = len([item for item in self.inventory_items if item['status'] == 'Низкий остаток'])
        total_value = sum(item['current_stock'] * item['cost_per_unit'] for item in self.inventory_items)
        total_waste_cost = sum(record['cost'] for record in self.waste_records)

        return f"""
ОТЧЕТ ПО УПРАВЛЕНИЮ СКЛАДОМ
Дата создания: {datetime.now().strftime('%d.%m.%Y %H:%M')}

ОБЩАЯ СТАТИСТИКА:
- Всего товаров на складе: {total_items}
- Товаров с низким остатком: {low_stock_items}
- Общая стоимость склада: {total_value:,.2f} руб
- Активных заказов: {len(self.purchase_orders)}
- Общая стоимость потерь: {total_waste_cost:,.2f} руб

ТОВАРЫ С НИЗКИМ ОСТАТКОМ:
{chr(10).join([f"- {item['name']}: {item['current_stock']:.1f} {item['unit']} (мин: {item['min_stock']:.1f})"
               for item in self.inventory_items if item['status'] == 'Низкий остаток'])}

ПОСЛЕДНИЕ ПОТЕРИ:
{chr(10).join([f"- {record['date']}: {record['item']} - {record['quantity']:.1f} {record['unit']} ({record['reason']})"
               for record in self.waste_records[-10:]])}

АКТИВНЫЕ ЗАКАЗЫ:
{chr(10).join([f"- {order['order_number']}: {order['supplier']} - {order['total']:.2f} руб ({order['status']})"
               for order in self.purchase_orders if order['status'] in ['Ожидает', 'Утвержден']])}
        """

    def export_report(self, content):
        """Export report to file"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("Успех", f"Отчет экспортирован в {filename}")

        except Exception as e:
            handle_module_error(e, "Расширенное управление складом", "экспорт отчета")


# Dialog Classes
class InventoryItemDialog:
    """Dialog for adding/editing inventory items"""

    def __init__(self, parent, title, db_manager, item_data=None):
        self.result = None
        self.db_manager = db_manager

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x600")
        self.dialog.configure(bg=ModernStyles.COLORS['bg_primary'])
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center dialog
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        self.create_form(item_data)

    def create_form(self, item_data):
        """Create form fields"""
        main_frame = tk.Frame(self.dialog, bg=ModernStyles.COLORS['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Form fields
        fields = [
            ("Название:", "name"),
            ("Категория:", "category"),
            ("Текущий остаток:", "current_stock"),
            ("Единица измерения:", "unit"),
            ("Минимальный остаток:", "min_stock"),
            ("Максимальный остаток:", "max_stock"),
            ("Цена за единицу:", "cost_per_unit"),
            ("Поставщик:", "supplier")
        ]

        self.entries = {}

        for i, (label_text, field_name) in enumerate(fields):
            # Label
            label = tk.Label(
                main_frame,
                text=label_text,
                font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_primary'],
                fg=ModernStyles.COLORS['text_primary']
            )
            label.grid(row=i, column=0, sticky='w', pady=5, padx=(0, 10))

            # Entry
            entry = tk.Entry(main_frame, width=30)
            entry.grid(row=i, column=1, pady=5, sticky='ew')
            self.entries[field_name] = entry

            # Fill with existing data if editing
            if item_data and field_name in item_data:
                entry.insert(0, str(item_data[field_name]))

        main_frame.grid_columnconfigure(1, weight=1)

        # Buttons
        buttons_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_primary'])
        buttons_frame.grid(row=len(fields), column=0, columnspan=2, pady=20)

        # Save button
        save_btn = tk.Button(
            buttons_frame,
            text="💾 Сохранить",
            command=self.save_item,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['success'],
            fg='white',
            relief='flat',
            padx=20,
            pady=8
        )
        save_btn.pack(side='left', padx=(0, 10))

        # Cancel button
        cancel_btn = tk.Button(
            buttons_frame,
            text="❌ Отмена",
            command=self.dialog.destroy,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['danger'],
            fg='white',
            relief='flat',
            padx=20,
            pady=8
        )
        cancel_btn.pack(side='left')

    def save_item(self):
        """Save item data"""
        try:
            # Collect form data
            item_data = {}
            for field_name, entry in self.entries.items():
                value = entry.get().strip()
                if field_name in ['current_stock', 'min_stock', 'max_stock', 'cost_per_unit']:
                    item_data[field_name] = float(value) if value else 0.0
                else:
                    item_data[field_name] = value

            # Validate required fields
            if not item_data['name']:
                messagebox.showwarning("Предупреждение", "Введите название товара")
                return

            # Save to database
            db_data = {
                "name": item_data['name'],
                "category": item_data['category'],
                "unit_of_measure": item_data['unit'],
                "current_stock": item_data['current_stock'],
                "minimum_stock": item_data['min_stock'],
                "average_cost": item_data['cost_per_unit'],
                "supplier": item_data['supplier']
            }

            self.db_manager.insert_raw_material(db_data)
            self.result = item_data
            self.dialog.destroy()

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка сохранения: {e}")


class StockAdjustmentDialog:
    """Dialog for stock adjustments"""

    def __init__(self, parent, item_data):
        self.result = None
        self.item_data = item_data

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Корректировка Остатков")
        self.dialog.geometry("400x300")
        self.dialog.configure(bg=ModernStyles.COLORS['bg_primary'])
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.create_form()

    def create_form(self):
        """Create adjustment form"""
        main_frame = tk.Frame(self.dialog, bg=ModernStyles.COLORS['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Item info
        info_label = tk.Label(
            main_frame,
            text=f"Товар: {self.item_data['name']}\nТекущий остаток: {self.item_data['current_stock']:.1f} {self.item_data['unit']}",
            font=ModernStyles.FONTS['body'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary'],
            justify='left'
        )
        info_label.pack(pady=(0, 20))

        # Adjustment type
        tk.Label(main_frame, text="Тип корректировки:", font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_primary'], fg=ModernStyles.COLORS['text_primary']).pack(anchor='w')

        self.adjustment_type = tk.StringVar(value="add")

        add_radio = tk.Radiobutton(main_frame, text="Поступление", variable=self.adjustment_type, value="add",
                                  bg=ModernStyles.COLORS['bg_primary'], fg=ModernStyles.COLORS['text_primary'])
        add_radio.pack(anchor='w')

        remove_radio = tk.Radiobutton(main_frame, text="Списание", variable=self.adjustment_type, value="remove",
                                     bg=ModernStyles.COLORS['bg_primary'], fg=ModernStyles.COLORS['text_primary'])
        remove_radio.pack(anchor='w', pady=(0, 10))

        # Quantity
        tk.Label(main_frame, text="Количество:", font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_primary'], fg=ModernStyles.COLORS['text_primary']).pack(anchor='w')

        self.quantity_var = tk.StringVar()
        quantity_entry = tk.Entry(main_frame, textvariable=self.quantity_var, width=20)
        quantity_entry.pack(anchor='w', pady=(0, 10))

        # Reason
        tk.Label(main_frame, text="Причина:", font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_primary'], fg=ModernStyles.COLORS['text_primary']).pack(anchor='w')

        self.reason_var = tk.StringVar()
        reason_entry = tk.Entry(main_frame, textvariable=self.reason_var, width=40)
        reason_entry.pack(anchor='w', pady=(0, 20))

        # Buttons
        buttons_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_primary'])
        buttons_frame.pack()

        # Save button
        save_btn = tk.Button(
            buttons_frame,
            text="💾 Применить",
            command=self.apply_adjustment,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['success'],
            fg='white',
            relief='flat',
            padx=20,
            pady=8
        )
        save_btn.pack(side='left', padx=(0, 10))

        # Cancel button
        cancel_btn = tk.Button(
            buttons_frame,
            text="❌ Отмена",
            command=self.dialog.destroy,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['danger'],
            fg='white',
            relief='flat',
            padx=20,
            pady=8
        )
        cancel_btn.pack(side='left')

    def apply_adjustment(self):
        """Apply stock adjustment"""
        try:
            quantity = float(self.quantity_var.get() or 0)
            if quantity <= 0:
                messagebox.showwarning("Предупреждение", "Введите корректное количество")
                return

            adjustment_data = {
                'type': self.adjustment_type.get(),
                'quantity': quantity,
                'reason': self.reason_var.get()
            }

            self.result = adjustment_data
            self.dialog.destroy()

        except ValueError:
            messagebox.showerror("Ошибка", "Введите корректное числовое значение")


class PurchaseOrderDialog:
    """Dialog for creating purchase orders"""

    def __init__(self, parent, suggested_items, suppliers):
        self.result = None
        self.suggested_items = suggested_items
        self.suppliers = suppliers

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Создание Заказа Поставщику")
        self.dialog.geometry("800x600")
        self.dialog.configure(bg=ModernStyles.COLORS['bg_primary'])
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.create_form()

    def create_form(self):
        """Create order form"""
        main_frame = tk.Frame(self.dialog, bg=ModernStyles.COLORS['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Supplier selection
        tk.Label(main_frame, text="Поставщик:", font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_primary'], fg=ModernStyles.COLORS['text_primary']).pack(anchor='w')

        self.supplier_var = tk.StringVar()
        supplier_combo = ttk.Combobox(main_frame, textvariable=self.supplier_var,
                                     values=[s['name'] for s in self.suppliers], width=40)
        supplier_combo.pack(anchor='w', pady=(0, 10))

        # Delivery date
        tk.Label(main_frame, text="Дата доставки:", font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_primary'], fg=ModernStyles.COLORS['text_primary']).pack(anchor='w')

        self.delivery_date_var = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
        delivery_entry = tk.Entry(main_frame, textvariable=self.delivery_date_var, width=20)
        delivery_entry.pack(anchor='w', pady=(0, 20))

        # Items placeholder
        tk.Label(main_frame, text="Товары для заказа:", font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_primary'], fg=ModernStyles.COLORS['text_primary']).pack(anchor='w')

        items_text = tk.Text(main_frame, height=15, width=70)
        items_text.pack(fill='both', expand=True, pady=(0, 20))

        # Fill with suggested items
        if self.suggested_items:
            items_content = "РЕКОМЕНДУЕМЫЕ К ЗАКАЗУ ТОВАРЫ:\n\n"
            for item in self.suggested_items:
                items_content += f"- {item['name']}: {item['current_stock']:.1f} {item['unit']} (мин: {item['min_stock']:.1f})\n"
            items_text.insert('1.0', items_content)

        # Buttons
        buttons_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_primary'])
        buttons_frame.pack()

        # Create button
        create_btn = tk.Button(
            buttons_frame,
            text="✅ Создать Заказ",
            command=self.create_order,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['success'],
            fg='white',
            relief='flat',
            padx=20,
            pady=8
        )
        create_btn.pack(side='left', padx=(0, 10))

        # Cancel button
        cancel_btn = tk.Button(
            buttons_frame,
            text="❌ Отмена",
            command=self.dialog.destroy,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['danger'],
            fg='white',
            relief='flat',
            padx=20,
            pady=8
        )
        cancel_btn.pack(side='left')

    def create_order(self):
        """Create purchase order"""
        try:
            supplier = self.supplier_var.get()
            delivery_date = self.delivery_date_var.get()

            if not supplier:
                messagebox.showwarning("Предупреждение", "Выберите поставщика")
                return

            order_data = {
                'supplier': supplier,
                'delivery_date': delivery_date,
                'items': self.suggested_items,
                'total': sum(item['current_stock'] * item['cost_per_unit'] for item in self.suggested_items)
            }

            self.result = order_data
            self.dialog.destroy()

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка создания заказа: {e}")


class SupplierDialog:
    """Dialog for adding/editing suppliers"""

    def __init__(self, parent, title, supplier_data=None):
        self.result = None

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x400")
        self.dialog.configure(bg=ModernStyles.COLORS['bg_primary'])
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.create_form(supplier_data)

    def create_form(self, supplier_data):
        """Create supplier form"""
        main_frame = tk.Frame(self.dialog, bg=ModernStyles.COLORS['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Form fields
        fields = [
            ("Название:", "name"),
            ("Контактное лицо:", "contact"),
            ("Телефон:", "phone"),
            ("Email:", "email"),
            ("Адрес:", "address"),
            ("Условия оплаты:", "payment_terms"),
            ("Время доставки:", "delivery_time")
        ]

        self.entries = {}

        for i, (label_text, field_name) in enumerate(fields):
            # Label
            label = tk.Label(
                main_frame,
                text=label_text,
                font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_primary'],
                fg=ModernStyles.COLORS['text_primary']
            )
            label.grid(row=i, column=0, sticky='w', pady=5, padx=(0, 10))

            # Entry
            entry = tk.Entry(main_frame, width=40)
            entry.grid(row=i, column=1, pady=5, sticky='ew')
            self.entries[field_name] = entry

            # Fill with existing data if editing
            if supplier_data and field_name in supplier_data:
                entry.insert(0, str(supplier_data[field_name]))

        main_frame.grid_columnconfigure(1, weight=1)

        # Buttons
        buttons_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_primary'])
        buttons_frame.grid(row=len(fields), column=0, columnspan=2, pady=20)

        # Save button
        save_btn = tk.Button(
            buttons_frame,
            text="💾 Сохранить",
            command=self.save_supplier,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['success'],
            fg='white',
            relief='flat',
            padx=20,
            pady=8
        )
        save_btn.pack(side='left', padx=(0, 10))

        # Cancel button
        cancel_btn = tk.Button(
            buttons_frame,
            text="❌ Отмена",
            command=self.dialog.destroy,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['danger'],
            fg='white',
            relief='flat',
            padx=20,
            pady=8
        )
        cancel_btn.pack(side='left')

    def save_supplier(self):
        """Save supplier data"""
        try:
            # Collect form data
            supplier_data = {}
            for field_name, entry in self.entries.items():
                supplier_data[field_name] = entry.get().strip()

            # Validate required fields
            if not supplier_data['name']:
                messagebox.showwarning("Предупреждение", "Введите название поставщика")
                return

            # Add default rating
            supplier_data['rating'] = 4.0

            self.result = supplier_data
            self.dialog.destroy()

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка сохранения: {e}")


class InventorySettingsDialog:
    """Dialog for inventory settings"""

    def __init__(self, parent):
        self.result = None

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Настройки Склада")
        self.dialog.geometry("400x300")
        self.dialog.configure(bg=ModernStyles.COLORS['bg_primary'])
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.create_form()

    def create_form(self):
        """Create settings form"""
        main_frame = tk.Frame(self.dialog, bg=ModernStyles.COLORS['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Settings placeholder
        tk.Label(
            main_frame,
            text="Настройки Управления Складом",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary']
        ).pack(pady=(0, 20))

        # Auto-reorder checkbox
        self.auto_reorder_var = tk.BooleanVar(value=True)
        auto_reorder_check = tk.Checkbutton(
            main_frame,
            text="Автоматическое создание заказов при низком остатке",
            variable=self.auto_reorder_var,
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        auto_reorder_check.pack(anchor='w', pady=5)

        # Low stock threshold
        tk.Label(main_frame, text="Порог низкого остатка (%):", font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_primary'], fg=ModernStyles.COLORS['text_primary']).pack(anchor='w', pady=(10, 0))

        self.threshold_var = tk.StringVar(value="20")
        threshold_entry = tk.Entry(main_frame, textvariable=self.threshold_var, width=10)
        threshold_entry.pack(anchor='w', pady=(0, 10))

        # Buttons
        buttons_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_primary'])
        buttons_frame.pack(pady=20)

        # Save button
        save_btn = tk.Button(
            buttons_frame,
            text="💾 Сохранить",
            command=self.save_settings,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['success'],
            fg='white',
            relief='flat',
            padx=20,
            pady=8
        )
        save_btn.pack(side='left', padx=(0, 10))

        # Cancel button
        cancel_btn = tk.Button(
            buttons_frame,
            text="❌ Отмена",
            command=self.dialog.destroy,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['danger'],
            fg='white',
            relief='flat',
            padx=20,
            pady=8
        )
        cancel_btn.pack(side='left')

    def save_settings(self):
        """Save settings"""
        try:
            settings_data = {
                'auto_reorder': self.auto_reorder_var.get(),
                'low_stock_threshold': int(self.threshold_var.get() or 20)
            }

            self.result = settings_data
            self.dialog.destroy()

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка сохранения настроек: {e}")


def show_advanced_inventory(parent, db_manager):
    """Show advanced inventory management window"""
    try:
        AdvancedInventoryManager(parent, db_manager)
    except Exception as e:
        handle_module_error(e, "Расширенное управление складом", "открытие модуля")


if __name__ == "__main__":
    # Test the module
    root = tk.Tk()
    root.withdraw()  # Hide main window

    # Mock database manager for testing
    class MockDBManager:
        def get_raw_materials(self):
            return [
                {
                    'id': 1,
                    'name': 'Помидоры',
                    'category': 'Овощи',
                    'unit_of_measure': 'кг',
                    'current_stock': 15.0,
                    'minimum_stock': 20.0,
                    'average_cost': 80.0,
                    'supplier': 'Овощи-Плюс',
                    'updated_at': '2024-01-20'
                }
            ]

        def insert_raw_material(self, data):
            return 1

    mock_db = MockDBManager()
    show_advanced_inventory(root, mock_db)
    root.mainloop()
