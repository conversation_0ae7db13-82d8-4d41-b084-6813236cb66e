"""
Профессиональная система управления рецептами и подрецептами
Enterprise-уровень функциональности для ресторанного бизнеса
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog, filedialog
from datetime import datetime, timedelta
import json
import csv
from gui.styles import ModernStyles

class RecipeManager:
    """Менеджер рецептов и подрецептов"""

    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None

        # Профессиональная база данных рецептов
        self.recipes = {
            "Борщ украинский классический": {
                "id": "RCP001",
                "category": "Супы",
                "subcategory": "Горячие супы",
                "cuisine": "Украинская",
                "portions": 4,
                "portion_size": "250 мл",
                "prep_time": 45,
                "cook_time": 60,
                "total_time": 105,
                "difficulty": "Средняя",
                "skill_level": 3,
                "cost": 185.50,
                "price": 280.00,
                "food_cost_percent": 66.25,
                "allergens": ["Глютен", "Молочные продукты"],
                "dietary_info": ["Без орехов"],
                "nutritional_info": {
                    "calories": 245,
                    "protein": 18.5,
                    "carbs": 22.3,
                    "fat": 12.8,
                    "fiber": 4.2,
                    "sodium": 890
                },
                "equipment": ["Кастрюля 3л", "Сковорода", "Нож", "Доска"],
                "temperature": {"cooking": 95, "serving": 65},
                "shelf_life": "24 часа",
                "storage": "Холодильник +2°C",
                "popularity_score": 9.2,
                "last_updated": "2024-01-15",
                "created_by": "Шеф-повар Иванов",
                "version": "2.1",
                "ingredients": [
                    {"name": "Говядина на кости", "amount": 300, "unit": "г", "cost": 180.00, "supplier": "МясКомбинат", "quality": "Премиум"},
                    {"name": "Свёкла молодая", "amount": 150, "unit": "г", "cost": 12.00, "supplier": "ОвощБаза", "quality": "Первый сорт"},
                    {"name": "Капуста белокочанная", "amount": 200, "unit": "г", "cost": 15.00, "supplier": "ОвощБаза", "quality": "Первый сорт"},
                    {"name": "Морковь", "amount": 100, "unit": "г", "cost": 8.00, "supplier": "ОвощБаза", "quality": "Первый сорт"},
                    {"name": "Лук репчатый", "amount": 80, "unit": "г", "cost": 6.00, "supplier": "ОвощБаза", "quality": "Первый сорт"},
                    {"name": "Картофель", "amount": 200, "unit": "г", "cost": 14.00, "supplier": "ОвощБаза", "quality": "Первый сорт"},
                    {"name": "Томатная паста", "amount": 50, "unit": "г", "cost": 8.00, "supplier": "КонсервЗавод", "quality": "ГОСТ"},
                    {"name": "Сметана 20%", "amount": 50, "unit": "г", "cost": 25.00, "supplier": "МолокоПродукт", "quality": "Премиум"},
                    {"name": "Лавровый лист", "amount": 2, "unit": "шт", "cost": 3.00, "supplier": "СпецииМир", "quality": "Высший"},
                    {"name": "Соль поваренная", "amount": 8, "unit": "г", "cost": 1.00, "supplier": "СольТорг", "quality": "Экстра"},
                    {"name": "Перец чёрный молотый", "amount": 2, "unit": "г", "cost": 8.00, "supplier": "СпецииМир", "quality": "Высший"}
                ],
                "instructions": [
                    {"step": 1, "action": "Подготовка", "description": "Промыть говядину, залить 2л холодной воды, довести до кипения", "time": 15, "temperature": 100},
                    {"step": 2, "action": "Варка бульона", "description": "Снять пену, добавить лавровый лист, варить 40-50 мин на медленном огне", "time": 45, "temperature": 95},
                    {"step": 3, "action": "Подготовка овощей", "description": "Нарезать все овощи соломкой 3-4 мм", "time": 20, "temperature": 0},
                    {"step": 4, "action": "Пассеровка", "description": "Обжарить лук и морковь до золотистого цвета", "time": 8, "temperature": 160},
                    {"step": 5, "action": "Тушение свёклы", "description": "Добавить свёклу к пассеровке, тушить 10 мин с томатной пастой", "time": 10, "temperature": 140},
                    {"step": 6, "action": "Сборка", "description": "Добавить картофель в бульон, варить 10 мин", "time": 10, "temperature": 95},
                    {"step": 7, "action": "Финиш", "description": "Добавить капусту и пассеровку, варить 15 мин", "time": 15, "temperature": 95},
                    {"step": 8, "action": "Подача", "description": "Подавать горячим со сметаной и зеленью", "time": 2, "temperature": 65}
                ],
                "quality_standards": {
                    "color": "Насыщенный красный",
                    "consistency": "Густая, однородная",
                    "taste": "Сбалансированный, насыщенный",
                    "aroma": "Ярко выраженный овощной"
                },
                "common_mistakes": [
                    "Переваривание овощей",
                    "Недостаточное пассерование",
                    "Неправильная нарезка"
                ],
                "variations": ["Постный", "С фасолью", "С грибами"],
                "wine_pairing": ["Красное сухое", "Полусладкое"],
                "seasonal_availability": "Круглый год",
                "profit_margin": 33.75
            },
            "Стейк рибай на гриле": {
                "id": "RCP002",
                "category": "Горячие блюда",
                "subcategory": "Мясные блюда",
                "cuisine": "Европейская",
                "portions": 1,
                "portion_size": "250 г",
                "prep_time": 10,
                "cook_time": 15,
                "total_time": 25,
                "difficulty": "Высокая",
                "skill_level": 4,
                "cost": 285.00,
                "price": 450.00,
                "food_cost_percent": 63.33,
                "allergens": ["Молочные продукты"],
                "dietary_info": ["Безглютеновое", "Кето"],
                "nutritional_info": {
                    "calories": 520,
                    "protein": 45.2,
                    "carbs": 2.1,
                    "fat": 35.8,
                    "fiber": 0.5,
                    "sodium": 420
                },
                "equipment": ["Гриль", "Щипцы", "Термометр"],
                "temperature": {"cooking": 200, "serving": 55, "internal": 54},
                "shelf_life": "Подача немедленно",
                "storage": "Не хранится",
                "popularity_score": 9.8,
                "last_updated": "2024-01-15",
                "created_by": "Су-шеф Петров",
                "version": "1.3",
                "ingredients": [
                    {"name": "Стейк рибай", "amount": 250, "unit": "г", "cost": 250.00, "supplier": "ПремиумМясо", "quality": "Мраморность 7+"},
                    {"name": "Масло сливочное", "amount": 20, "unit": "г", "cost": 15.00, "supplier": "МолокоПродукт", "quality": "82.5%"},
                    {"name": "Соль морская", "amount": 5, "unit": "г", "cost": 3.00, "supplier": "СольПремиум", "quality": "Fleur de sel"},
                    {"name": "Перец чёрный", "amount": 2, "unit": "г", "cost": 5.00, "supplier": "СпецииМир", "quality": "Телишерри"},
                    {"name": "Розмарин свежий", "amount": 3, "unit": "г", "cost": 12.00, "supplier": "ТравыСад", "quality": "Свежий"}
                ],
                "instructions": [
                    {"step": 1, "action": "Подготовка", "description": "Достать стейк из холодильника за 30 мин до готовки", "time": 30, "temperature": 20},
                    {"step": 2, "action": "Разогрев", "description": "Разогреть гриль до максимальной температуры", "time": 10, "temperature": 250},
                    {"step": 3, "action": "Приправы", "description": "Обильно посолить и поперчить стейк с обеих сторон", "time": 2, "temperature": 20},
                    {"step": 4, "action": "Обжарка", "description": "Обжарить стейк 2-3 мин с каждой стороны для medium-rare", "time": 6, "temperature": 200},
                    {"step": 5, "action": "Ароматизация", "description": "Добавить масло и розмарин, поливать стейк 1-2 мин", "time": 2, "temperature": 180},
                    {"step": 6, "action": "Отдых", "description": "Дать стейку отдохнуть 5 мин под фольгой", "time": 5, "temperature": 60},
                    {"step": 7, "action": "Подача", "description": "Подавать на разогретой тарелке", "time": 1, "temperature": 55}
                ],
                "quality_standards": {
                    "doneness": "Medium-rare (54°C внутри)",
                    "crust": "Золотисто-коричневая корочка",
                    "texture": "Сочный, нежный",
                    "presentation": "Красивые полоски от гриля"
                },
                "common_mistakes": [
                    "Переворачивание слишком часто",
                    "Недостаточный разогрев гриля",
                    "Отсутствие отдыха мяса"
                ],
                "variations": ["Medium", "Well-done", "С травяным маслом"],
                "wine_pairing": ["Каберне Совиньон", "Мальбек", "Бароло"],
                "seasonal_availability": "Круглый год",
                "profit_margin": 36.67
            },
            "Салат Цезарь с курицей": {
                "id": "RCP003",
                "category": "Салаты",
                "subcategory": "Основные салаты",
                "cuisine": "Итальянская",
                "portions": 1,
                "portion_size": "180 г",
                "prep_time": 15,
                "cook_time": 8,
                "total_time": 23,
                "difficulty": "Лёгкая",
                "skill_level": 2,
                "cost": 95.00,
                "price": 180.00,
                "food_cost_percent": 52.78,
                "allergens": ["Глютен", "Яйца", "Рыба", "Молочные продукты"],
                "dietary_info": ["Высокобелковое"],
                "nutritional_info": {
                    "calories": 385,
                    "protein": 28.5,
                    "carbs": 12.8,
                    "fat": 26.2,
                    "fiber": 3.1,
                    "sodium": 720
                },
                "equipment": ["Сковорода", "Миска", "Венчик"],
                "temperature": {"serving": 8},
                "shelf_life": "2 часа",
                "storage": "Холодильник +4°C",
                "popularity_score": 8.9,
                "last_updated": "2024-01-15",
                "created_by": "Повар Сидорова",
                "version": "2.0",
                "ingredients": [
                    {"name": "Салат романо", "amount": 120, "unit": "г", "cost": 25.00, "supplier": "ЗеленьФреш", "quality": "Премиум"},
                    {"name": "Куриная грудка", "amount": 80, "unit": "г", "cost": 35.00, "supplier": "ПтицаФерма", "quality": "Охлаждённая"},
                    {"name": "Пармезан", "amount": 25, "unit": "г", "cost": 45.00, "supplier": "СырИталия", "quality": "24 месяца"},
                    {"name": "Сухарики", "amount": 15, "unit": "г", "cost": 8.00, "supplier": "ХлебКомбинат", "quality": "Домашние"},
                    {"name": "Соус Цезарь", "amount": 30, "unit": "мл", "cost": 15.00, "supplier": "Собственное производство", "quality": "Свежий"}
                ],
                "instructions": [
                    {"step": 1, "action": "Подготовка курицы", "description": "Отбить грудку, посолить, поперчить", "time": 3, "temperature": 20},
                    {"step": 2, "action": "Жарка", "description": "Обжарить курицу до готовности", "time": 8, "temperature": 180},
                    {"step": 3, "action": "Подготовка салата", "description": "Промыть и обсушить листья романо", "time": 5, "temperature": 8},
                    {"step": 4, "action": "Нарезка", "description": "Нарезать курицу полосками, салат крупно", "time": 3, "temperature": 20},
                    {"step": 5, "action": "Сборка", "description": "Смешать салат с соусом, добавить курицу", "time": 2, "temperature": 8},
                    {"step": 6, "action": "Финиш", "description": "Посыпать пармезаном и сухариками", "time": 1, "temperature": 8},
                    {"step": 7, "action": "Подача", "description": "Подавать немедленно на охлаждённой тарелке", "time": 1, "temperature": 8}
                ],
                "quality_standards": {
                    "freshness": "Хрустящие листья",
                    "temperature": "Прохладный",
                    "coating": "Равномерное покрытие соусом",
                    "balance": "Сбалансированный вкус"
                },
                "common_mistakes": [
                    "Переувлажнение салата",
                    "Пересушенная курица",
                    "Слишком много соуса"
                ],
                "variations": ["С креветками", "Вегетарианский", "С анчоусами"],
                "wine_pairing": ["Шардоне", "Пино Гриджо", "Совиньон Блан"],
                "seasonal_availability": "Круглый год",
                "profit_margin": 47.22
            },
            "Стейк рибай": {
                "category": "Горячие блюда",
                "portions": 1,
                "prep_time": "10 мин",
                "cook_time": "15 мин",
                "difficulty": "Высокая",
                "cost": 285.00,
                "price": 450.00,
                "ingredients": [
                    {"name": "Стейк рибай", "amount": 250, "unit": "г", "cost": 250.00},
                    {"name": "Масло сливочное", "amount": 20, "unit": "г", "cost": 15.00},
                    {"name": "Соль морская", "amount": 5, "unit": "г", "cost": 3.00},
                    {"name": "Перец чёрный", "amount": 2, "unit": "г", "cost": 5.00},
                    {"name": "Розмарин", "amount": 3, "unit": "г", "cost": 12.00}
                ],
                "instructions": [
                    "Достать стейк из холодильника за 30 мин",
                    "Разогреть сковороду до максимума",
                    "Посолить и поперчить стейк",
                    "Обжарить по 2-3 мин с каждой стороны",
                    "Добавить масло и розмарин",
                    "Поливать стейк маслом 1-2 мин",
                    "Дать отдохнуть 5 мин перед подачей"
                ]
            },
            "Салат Цезарь": {
                "category": "Салаты",
                "portions": 2,
                "prep_time": "20 мин",
                "cook_time": "0 мин",
                "difficulty": "Лёгкая",
                "cost": 95.00,
                "price": 180.00,
                "ingredients": [
                    {"name": "Салат романо", "amount": 150, "unit": "г", "cost": 25.00},
                    {"name": "Куриная грудка", "amount": 100, "unit": "г", "cost": 35.00},
                    {"name": "Сыр пармезан", "amount": 30, "unit": "г", "cost": 45.00},
                    {"name": "Сухарики", "amount": 20, "unit": "г", "cost": 8.00},
                    {"name": "Соус Цезарь", "amount": 40, "unit": "мл", "cost": 15.00}
                ],
                "instructions": [
                    "Отварить куриную грудку",
                    "Нарезать салат крупными кусками",
                    "Нарезать курицу полосками",
                    "Натереть пармезан",
                    "Смешать все ингредиенты",
                    "Заправить соусом",
                    "Подавать немедленно"
                ]
            }
        }

        # Профессиональная база подрецептов и полуфабрикатов
        self.sub_recipes = {
            "Соус Цезарь классический": {
                "id": "SUB001",
                "category": "Соусы",
                "subcategory": "Салатные соусы",
                "yield": "250 мл",
                "yield_portions": 8,
                "prep_time": 15,
                "shelf_life": "72 часа",
                "storage_temp": "+2°C до +4°C",
                "cost": 52.00,
                "cost_per_portion": 6.50,
                "allergens": ["Яйца", "Рыба", "Молочные продукты"],
                "consistency": "Кремообразная",
                "color": "Светло-жёлтый",
                "ph_level": 4.2,
                "created_by": "Су-шеф Петров",
                "last_updated": "2024-01-15",
                "version": "3.1",
                "ingredients": [
                    {"name": "Майонез домашний", "amount": 120, "unit": "мл", "cost": 18.00, "supplier": "Собственное производство"},
                    {"name": "Анчоусы в масле", "amount": 25, "unit": "г", "cost": 28.00, "supplier": "ИталияИмпорт", "quality": "Премиум"},
                    {"name": "Чеснок свежий", "amount": 12, "unit": "г", "cost": 3.00, "supplier": "ОвощБаза"},
                    {"name": "Лимонный сок", "amount": 25, "unit": "мл", "cost": 8.00, "supplier": "ЦитрусФреш"},
                    {"name": "Пармезан тёртый", "amount": 30, "unit": "г", "cost": 45.00, "supplier": "СырИталия"},
                    {"name": "Дижонская горчица", "amount": 5, "unit": "г", "cost": 5.00, "supplier": "ФранцияДеликатес"},
                    {"name": "Вустерский соус", "amount": 3, "unit": "мл", "cost": 4.00, "supplier": "АнглияСоусы"}
                ],
                "instructions": [
                    {"step": 1, "description": "Измельчить анчоусы и чеснок в пасту", "time": 3, "equipment": "Блендер"},
                    {"step": 2, "description": "Смешать с майонезом и горчицей", "time": 2, "equipment": "Венчик"},
                    {"step": 3, "description": "Добавить лимонный сок и вустерский соус", "time": 1, "equipment": "Венчик"},
                    {"step": 4, "description": "Добавить тёртый пармезан", "time": 1, "equipment": "Лопатка"},
                    {"step": 5, "description": "Взбить до однородности", "time": 3, "equipment": "Венчик"},
                    {"step": 6, "description": "Проверить консистенцию и вкус", "time": 2, "equipment": "Ложка"},
                    {"step": 7, "description": "Переложить в контейнер, охладить", "time": 3, "equipment": "Контейнер"}
                ],
                "quality_control": {
                    "texture": "Гладкая, без комочков",
                    "taste": "Сбалансированный, пикантный",
                    "color": "Однородный светло-жёлтый",
                    "aroma": "Выраженный сырный с нотками анчоусов"
                },
                "usage": ["Салат Цезарь", "Заправка для овощей", "Соус к мясу"],
                "nutritional_per_100g": {
                    "calories": 420,
                    "protein": 8.2,
                    "carbs": 3.1,
                    "fat": 42.5,
                    "sodium": 890
                }
            },
            "Бульон говяжий концентрированный": {
                "id": "SUB002",
                "category": "Основы",
                "subcategory": "Бульоны",
                "yield": "2.5 л",
                "yield_portions": 10,
                "prep_time": 30,
                "cook_time": 360,
                "total_time": 390,
                "shelf_life": "48 часов",
                "storage_temp": "+2°C до +4°C",
                "cost": 125.00,
                "cost_per_portion": 12.50,
                "allergens": [],
                "consistency": "Прозрачная",
                "color": "Золотисто-коричневый",
                "created_by": "Шеф-повар Иванов",
                "last_updated": "2024-01-15",
                "version": "2.3",
                "ingredients": [
                    {"name": "Говяжьи кости мозговые", "amount": 800, "unit": "г", "cost": 60.00, "supplier": "МясКомбинат"},
                    {"name": "Говяжьи кости трубчатые", "amount": 400, "unit": "г", "cost": 30.00, "supplier": "МясКомбинат"},
                    {"name": "Лук репчатый", "amount": 150, "unit": "г", "cost": 12.00, "supplier": "ОвощБаза"},
                    {"name": "Морковь", "amount": 100, "unit": "г", "cost": 8.00, "supplier": "ОвощБаза"},
                    {"name": "Сельдерей корневой", "amount": 80, "unit": "г", "cost": 15.00, "supplier": "ОвощБаза"},
                    {"name": "Лук-порей", "amount": 60, "unit": "г", "cost": 18.00, "supplier": "ОвощБаза"},
                    {"name": "Томатная паста", "amount": 30, "unit": "г", "cost": 8.00, "supplier": "КонсервЗавод"},
                    {"name": "Лавровый лист", "amount": 4, "unit": "шт", "cost": 6.00, "supplier": "СпецииМир"},
                    {"name": "Тимьян свежий", "amount": 5, "unit": "г", "cost": 12.00, "supplier": "ТравыСад"},
                    {"name": "Перец чёрный горошком", "amount": 8, "unit": "г", "cost": 8.00, "supplier": "СпецииМир"}
                ],
                "instructions": [
                    {"step": 1, "description": "Обжарить кости в духовке при 200°C 45 мин", "time": 45, "equipment": "Духовка, противень"},
                    {"step": 2, "description": "Переложить кости в кастрюлю 5л", "time": 5, "equipment": "Кастрюля"},
                    {"step": 3, "description": "Обжарить овощи до карамелизации", "time": 15, "equipment": "Сковорода"},
                    {"step": 4, "description": "Добавить томатную пасту, жарить 2 мин", "time": 2, "equipment": "Сковорода"},
                    {"step": 5, "description": "Переложить овощи к костям", "time": 3, "equipment": "Лопатка"},
                    {"step": 6, "description": "Залить холодной водой на 5 см выше", "time": 5, "equipment": "Мерный стакан"},
                    {"step": 7, "description": "Довести до кипения, снять пену", "time": 20, "equipment": "Шумовка"},
                    {"step": 8, "description": "Добавить специи, варить 6 часов", "time": 360, "equipment": "Плита"},
                    {"step": 9, "description": "Процедить через мелкое сито", "time": 10, "equipment": "Сито"},
                    {"step": 10, "description": "Охладить в ледяной бане", "time": 30, "equipment": "Ледяная баня"}
                ],
                "quality_control": {
                    "clarity": "Прозрачный, без мути",
                    "color": "Насыщенный золотисто-коричневый",
                    "aroma": "Богатый мясной",
                    "taste": "Концентрированный, сбалансированный",
                    "gelification": "Застывает при +4°C"
                },
                "usage": ["Супы", "Соусы", "Ризотто", "Тушение"],
                "nutritional_per_100ml": {
                    "calories": 25,
                    "protein": 4.8,
                    "carbs": 0.5,
                    "fat": 0.8,
                    "sodium": 320
                }
            },
            "Маринад универсальный для мяса": {
                "id": "SUB003",
                "category": "Маринады",
                "subcategory": "Мясные маринады",
                "yield": "300 мл",
                "yield_portions": 6,
                "prep_time": 10,
                "shelf_life": "120 часов",
                "storage_temp": "+2°C до +4°C",
                "cost": 38.00,
                "cost_per_portion": 6.33,
                "allergens": ["Глютен"],
                "ph_level": 3.8,
                "created_by": "Повар Сидорова",
                "last_updated": "2024-01-15",
                "version": "1.5",
                "ingredients": [
                    {"name": "Соевый соус", "amount": 80, "unit": "мл", "cost": 15.00, "supplier": "АзияПродукт"},
                    {"name": "Оливковое масло", "amount": 60, "unit": "мл", "cost": 25.00, "supplier": "ИталияОлива"},
                    {"name": "Мёд натуральный", "amount": 40, "unit": "г", "cost": 20.00, "supplier": "ПасекаЭко"},
                    {"name": "Чеснок", "amount": 20, "unit": "г", "cost": 5.00, "supplier": "ОвощБаза"},
                    {"name": "Имбирь свежий", "amount": 15, "unit": "г", "cost": 12.00, "supplier": "АзияСпеции"},
                    {"name": "Лимонный сок", "amount": 30, "unit": "мл", "cost": 8.00, "supplier": "ЦитрусФреш"},
                    {"name": "Розмарин сушёный", "amount": 3, "unit": "г", "cost": 6.00, "supplier": "ТравыСад"},
                    {"name": "Перец чёрный молотый", "amount": 2, "unit": "г", "cost": 4.00, "supplier": "СпецииМир"}
                ],
                "instructions": [
                    {"step": 1, "description": "Измельчить чеснок и имбирь", "time": 3, "equipment": "Нож"},
                    {"step": 2, "description": "Смешать соевый соус с мёдом", "time": 2, "equipment": "Венчик"},
                    {"step": 3, "description": "Добавить масло и лимонный сок", "time": 1, "equipment": "Венчик"},
                    {"step": 4, "description": "Добавить чеснок, имбирь, специи", "time": 2, "equipment": "Лопатка"},
                    {"step": 5, "description": "Тщательно перемешать", "time": 2, "equipment": "Венчик"}
                ],
                "usage": ["Говядина", "Свинина", "Курица", "Баранина"],
                "marinating_time": {
                    "chicken": "2-4 часа",
                    "pork": "4-8 часов",
                    "beef": "6-12 часов",
                    "lamb": "8-24 часа"
                }
            }
        }

    def create_window(self):
        """Создать окно управления рецептами"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("📋 Управление Рецептами")
        self.window.geometry("1500x900")
        self.window.configure(bg='white')
        self.window.resizable(True, True)

        self.create_interface()
        return self.window

    def create_interface(self):
        """Создать интерфейс"""
        # Заголовок
        header_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['primary'], height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="📋 Управление Рецептами",
                font=('Arial', 16, 'bold'), bg=ModernStyles.COLORS['primary'],
                fg='white').pack(side='left', padx=20, pady=15)

        # Профессиональные кнопки действий
        btn_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['primary'])
        btn_frame.pack(side='right', padx=20, pady=10)

        tk.Button(btn_frame, text="➕ Новый рецепт", command=self.add_recipe,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=3)

        tk.Button(btn_frame, text="📋 Импорт", command=self.import_recipes,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=3)

        tk.Button(btn_frame, text="📤 Экспорт", command=self.export_recipes,
                 bg=ModernStyles.COLORS['secondary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=3)

        tk.Button(btn_frame, text="💰 Калькулятор", command=self.show_calculator,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=3)

        tk.Button(btn_frame, text="📊 Отчёты", command=self.generate_reports,
                 bg=ModernStyles.COLORS['accent'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=3)

        # Основной контент
        main_frame = tk.Frame(self.window, bg='white')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Создать профессиональные вкладки
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill='both', expand=True)

        # Вкладка "Рецепты"
        recipes_frame = tk.Frame(notebook, bg='white')
        notebook.add(recipes_frame, text="📋 Рецепты")
        self.create_recipes_tab(recipes_frame)

        # Вкладка "Подрецепты и полуфабрикаты"
        sub_recipes_frame = tk.Frame(notebook, bg='white')
        notebook.add(sub_recipes_frame, text="🧪 Подрецепты")
        self.create_sub_recipes_tab(sub_recipes_frame)

        # Вкладка "Калькулятор себестоимости"
        calculator_frame = tk.Frame(notebook, bg='white')
        notebook.add(calculator_frame, text="💰 Калькулятор")
        self.create_calculator_tab(calculator_frame)

        # Вкладка "Контроль качества"
        quality_frame = tk.Frame(notebook, bg='white')
        notebook.add(quality_frame, text="🔍 Качество")
        self.create_quality_tab(quality_frame)

        # Вкладка "Планирование производства"
        production_frame = tk.Frame(notebook, bg='white')
        notebook.add(production_frame, text="⚙️ Производство")
        self.create_production_tab(production_frame)

        # Вкладка "Аналитика и отчёты"
        analytics_frame = tk.Frame(notebook, bg='white')
        notebook.add(analytics_frame, text="📊 Аналитика")
        self.create_analytics_tab(analytics_frame)

        # Вкладка "Настройки"
        settings_frame = tk.Frame(notebook, bg='white')
        notebook.add(settings_frame, text="⚙️ Настройки")
        self.create_settings_tab(settings_frame)

    def create_recipes_tab(self, parent):
        """Создать вкладку рецептов"""
        # Поиск и фильтры
        search_frame = tk.Frame(parent, bg='#f8f9fa', relief='solid', bd=1)
        search_frame.pack(fill='x', padx=20, pady=(20, 10))

        tk.Label(search_frame, text="🔍 Поиск рецептов:",
                font=('Arial', 11), bg='#f8f9fa').pack(side='left', padx=15, pady=10)

        search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=search_var, width=30)
        search_entry.pack(side='left', padx=10, pady=10)

        tk.Label(search_frame, text="Категория:",
                font=('Arial', 11), bg='#f8f9fa').pack(side='left', padx=(20, 5), pady=10)

        category_var = tk.StringVar(value="Все")
        category_combo = ttk.Combobox(search_frame, textvariable=category_var,
                                     values=["Все", "Супы", "Горячие блюда", "Салаты", "Десерты"],
                                     width=15)
        category_combo.pack(side='left', padx=5, pady=10)

        # Список рецептов
        recipes_frame = tk.Frame(parent, bg='white')
        recipes_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Левая панель - список рецептов
        left_panel = tk.Frame(recipes_frame, bg='#f8f9fa', relief='solid', bd=1, width=400)
        left_panel.pack(side='left', fill='y', padx=(0, 10))
        left_panel.pack_propagate(False)

        tk.Label(left_panel, text="📋 Список Рецептов",
                font=('Arial', 12, 'bold'), bg='#f8f9fa').pack(pady=15)

        # Список рецептов
        recipes_listbox = tk.Listbox(left_panel, font=('Arial', 10))
        recipes_listbox.pack(fill='both', expand=True, padx=15, pady=(0, 15))

        for recipe_name in self.recipes.keys():
            recipes_listbox.insert(tk.END, recipe_name)

        # Правая панель - детали рецепта
        self.recipe_details_frame = tk.Frame(recipes_frame, bg='white')
        self.recipe_details_frame.pack(side='right', fill='both', expand=True)

        # Показать первый рецепт
        if self.recipes:
            first_recipe = list(self.recipes.keys())[0]
            self.show_recipe_details(first_recipe)

        # Привязать событие выбора
        def on_recipe_select(event):
            selection = recipes_listbox.curselection()
            if selection:
                recipe_name = recipes_listbox.get(selection[0])
                self.show_recipe_details(recipe_name)

        recipes_listbox.bind('<<ListboxSelect>>', on_recipe_select)

    def create_sub_recipes_tab(self, parent):
        """Создать вкладку подрецептов"""
        # Заголовок
        tk.Label(parent, text="🧪 Подрецепты и Полуфабрикаты",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        # Кнопки управления
        btn_frame = tk.Frame(parent, bg='#f8f9fa', relief='solid', bd=1)
        btn_frame.pack(fill='x', padx=20, pady=(0, 20))

        tk.Button(btn_frame, text="➕ Добавить подрецепт", command=self.add_sub_recipe,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=8).pack(side='left', padx=15, pady=15)

        tk.Button(btn_frame, text="✏️ Редактировать", command=self.edit_sub_recipe,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=8).pack(side='left', padx=5)

        tk.Button(btn_frame, text="🗑️ Удалить", command=self.delete_sub_recipe,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=8).pack(side='left', padx=5)

        # Таблица подрецептов
        table_frame = tk.Frame(parent, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20)

        # Заголовки таблицы
        headers = ["Название", "Категория", "Выход", "Срок хранения", "Себестоимость"]

        # Создать заголовки
        header_frame = tk.Frame(table_frame, bg=ModernStyles.COLORS['primary'])
        header_frame.pack(fill='x')

        for header in headers:
            tk.Label(header_frame, text=header, font=('Arial', 11, 'bold'),
                    bg=ModernStyles.COLORS['primary'], fg='white',
                    relief='solid', bd=1).pack(side='left', fill='both', expand=True)

        # Данные таблицы
        for name, data in self.sub_recipes.items():
            row_frame = tk.Frame(table_frame, bg='white')
            row_frame.pack(fill='x')

            row_data = [
                name,
                data['category'],
                data['yield'],
                data['shelf_life'],
                f"{data['cost']:.2f}₽"
            ]

            for cell_data in row_data:
                tk.Label(row_frame, text=cell_data, font=('Arial', 10),
                        bg='white', relief='solid', bd=1).pack(side='left', fill='both', expand=True)

    def create_calculator_tab(self, parent):
        """Создать вкладку калькулятора себестоимости"""
        tk.Label(parent, text="💰 Калькулятор Себестоимости",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        # Выбор рецепта
        calc_frame = tk.Frame(parent, bg='#f8f9fa', relief='solid', bd=1)
        calc_frame.pack(fill='x', padx=20, pady=(0, 20))

        recipe_frame = tk.Frame(calc_frame, bg='#f8f9fa')
        recipe_frame.pack(fill='x', padx=20, pady=15)

        tk.Label(recipe_frame, text="Выберите рецепт:",
                font=('Arial', 11), bg='#f8f9fa').pack(side='left')

        recipe_var = tk.StringVar(value=list(self.recipes.keys())[0])
        recipe_combo = ttk.Combobox(recipe_frame, textvariable=recipe_var,
                                   values=list(self.recipes.keys()), width=25)
        recipe_combo.pack(side='left', padx=15)

        tk.Label(recipe_frame, text="Количество порций:",
                font=('Arial', 11), bg='#f8f9fa').pack(side='left', padx=(20, 5))

        portions_var = tk.StringVar(value="1")
        portions_entry = tk.Entry(recipe_frame, textvariable=portions_var, width=10)
        portions_entry.pack(side='left', padx=5)

        tk.Button(recipe_frame, text="💰 Рассчитать",
                 command=lambda: self.calculate_cost(recipe_var.get(), portions_var.get()),
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=15)

        # Результат расчёта
        self.calc_result_frame = tk.Frame(parent, bg='white')
        self.calc_result_frame.pack(fill='both', expand=True, padx=20)

        # Показать начальный расчёт
        self.calculate_cost(recipe_var.get(), "1")

    def create_analytics_tab(self, parent):
        """Создать вкладку аналитики"""
        tk.Label(parent, text="📊 Аналитика Рецептов",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        # Информационный блок
        info_frame = tk.Frame(parent, bg='#f8f9fa', relief='solid', bd=1)
        info_frame.pack(fill='both', expand=True, padx=20, pady=20)

        analytics_text = """
📊 АНАЛИТИКА РЕЦЕПТОВ:

📋 ОБЩАЯ СТАТИСТИКА:
• Всего рецептов: 3
• Подрецептов: 2
• Средняя себестоимость: 188.50₽
• Средняя маржинальность: 52.3%

🏆 САМЫЕ ПРИБЫЛЬНЫЕ РЕЦЕПТЫ:
1. Стейк рибай - маржа 57.8% (165₽ прибыли)
2. Салат Цезарь - маржа 47.2% (85₽ прибыли)
3. Борщ украинский - маржа 33.8% (94.50₽ прибыли)

💰 АНАЛИЗ СЕБЕСТОИМОСТИ:
• Самый дорогой: Стейк рибай (285₽)
• Самый дешёвый: Салат Цезарь (95₽)
• Средняя стоимость ингредиентов: 188.50₽

⏱️ ВРЕМЯ ПРИГОТОВЛЕНИЯ:
• Быстрые блюда (до 30 мин): 1 рецепт
• Средние (30-60 мин): 1 рецепт
• Долгие (более 60 мин): 1 рецепт

📈 КАТЕГОРИИ БЛЮД:
• Супы: 1 рецепт (33.3%)
• Горячие блюда: 1 рецепт (33.3%)
• Салаты: 1 рецепт (33.3%)

🎯 РЕКОМЕНДАЦИИ:
• Добавить больше быстрых блюд
• Разработать рецепты с высокой маржой
• Оптимизировать себестоимость борща
• Создать больше салатов и закусок
• Добавить десерты в меню

📊 ПОПУЛЯРНОСТЬ (по заказам):
1. Борщ украинский - 134 заказа/месяц
2. Стейк рибай - 89 заказов/месяц
3. Салат Цезарь - 76 заказов/месяц

💡 ИДЕИ ДЛЯ РАЗВИТИЯ:
• Создать вегетарианские варианты
• Добавить сезонные рецепты
• Разработать детское меню
• Создать диетические блюда
        """

        tk.Label(info_frame, text=analytics_text, font=('Arial', 11),
                bg='#f8f9fa', justify='left', anchor='nw').pack(fill='both', expand=True, padx=20, pady=20)

    def show_recipe_details(self, recipe_name):
        """Показать детали рецепта"""
        # Очистить предыдущие детали
        for widget in self.recipe_details_frame.winfo_children():
            widget.destroy()

        if recipe_name not in self.recipes:
            return

        recipe = self.recipes[recipe_name]

        # Заголовок рецепта
        title_frame = tk.Frame(self.recipe_details_frame, bg='white')
        title_frame.pack(fill='x', pady=(0, 20))

        tk.Label(title_frame, text=recipe_name,
                font=('Arial', 16, 'bold'), bg='white').pack(side='left')

        # Кнопки действий
        btn_frame = tk.Frame(title_frame, bg='white')
        btn_frame.pack(side='right')

        tk.Button(btn_frame, text="✏️ Редактировать", command=lambda: self.edit_recipe(recipe_name),
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Arial', 9, 'bold'), relief='flat', padx=10, pady=3).pack(side='left', padx=2)

        tk.Button(btn_frame, text="🗑️ Удалить", command=lambda: self.delete_recipe(recipe_name),
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Arial', 9, 'bold'), relief='flat', padx=10, pady=3).pack(side='left', padx=2)

        # Основная информация
        info_frame = tk.Frame(self.recipe_details_frame, bg='#f8f9fa', relief='solid', bd=1)
        info_frame.pack(fill='x', pady=(0, 15))

        info_grid = tk.Frame(info_frame, bg='#f8f9fa')
        info_grid.pack(fill='x', padx=15, pady=15)

        # Первая строка
        row1 = tk.Frame(info_grid, bg='#f8f9fa')
        row1.pack(fill='x', pady=2)

        tk.Label(row1, text=f"📂 Категория: {recipe['category']}", font=('Arial', 10),
                bg='#f8f9fa').pack(side='left')
        tk.Label(row1, text=f"🍽️ Порций: {recipe['portions']}", font=('Arial', 10),
                bg='#f8f9fa').pack(side='right')

        # Вторая строка
        row2 = tk.Frame(info_grid, bg='#f8f9fa')
        row2.pack(fill='x', pady=2)

        tk.Label(row2, text=f"⏱️ Подготовка: {recipe['prep_time']}", font=('Arial', 10),
                bg='#f8f9fa').pack(side='left')
        tk.Label(row2, text=f"🔥 Готовка: {recipe['cook_time']}", font=('Arial', 10),
                bg='#f8f9fa').pack(side='right')

        # Третья строка
        row3 = tk.Frame(info_grid, bg='#f8f9fa')
        row3.pack(fill='x', pady=2)

        tk.Label(row3, text=f"⭐ Сложность: {recipe['difficulty']}", font=('Arial', 10),
                bg='#f8f9fa').pack(side='left')

        margin = ((recipe['price'] - recipe['cost']) / recipe['price']) * 100
        tk.Label(row3, text=f"💰 Маржа: {margin:.1f}%", font=('Arial', 10, 'bold'),
                fg=ModernStyles.COLORS['success'], bg='#f8f9fa').pack(side='right')

        # Ингредиенты
        ingredients_frame = tk.Frame(self.recipe_details_frame, bg='white')
        ingredients_frame.pack(fill='x', pady=(0, 15))

        tk.Label(ingredients_frame, text="🥘 Ингредиенты:",
                font=('Arial', 12, 'bold'), bg='white').pack(anchor='w', pady=(0, 10))

        # Таблица ингредиентов
        ing_table_frame = tk.Frame(ingredients_frame, bg='#f8f9fa', relief='solid', bd=1)
        ing_table_frame.pack(fill='x')

        # Заголовки
        headers = ["Ингредиент", "Количество", "Единица", "Стоимость"]
        header_frame = tk.Frame(ing_table_frame, bg=ModernStyles.COLORS['secondary'])
        header_frame.pack(fill='x')

        for header in headers:
            tk.Label(header_frame, text=header, font=('Arial', 10, 'bold'),
                    bg=ModernStyles.COLORS['secondary'], fg='white',
                    relief='solid', bd=1).pack(side='left', fill='both', expand=True)

        # Данные ингредиентов
        total_cost = 0
        for ingredient in recipe['ingredients']:
            row_frame = tk.Frame(ing_table_frame, bg='white')
            row_frame.pack(fill='x')

            row_data = [
                ingredient['name'],
                str(ingredient['amount']),
                ingredient['unit'],
                f"{ingredient['cost']:.2f}₽"
            ]

            for cell_data in row_data:
                tk.Label(row_frame, text=cell_data, font=('Arial', 9),
                        bg='white', relief='solid', bd=1).pack(side='left', fill='both', expand=True)

            total_cost += ingredient['cost']

        # Итого
        total_frame = tk.Frame(ing_table_frame, bg=ModernStyles.COLORS['primary'])
        total_frame.pack(fill='x')

        tk.Label(total_frame, text="ИТОГО:", font=('Arial', 10, 'bold'),
                bg=ModernStyles.COLORS['primary'], fg='white',
                relief='solid', bd=1).pack(side='left', fill='both', expand=True)
        tk.Label(total_frame, text="", font=('Arial', 10, 'bold'),
                bg=ModernStyles.COLORS['primary'], fg='white',
                relief='solid', bd=1).pack(side='left', fill='both', expand=True)
        tk.Label(total_frame, text="", font=('Arial', 10, 'bold'),
                bg=ModernStyles.COLORS['primary'], fg='white',
                relief='solid', bd=1).pack(side='left', fill='both', expand=True)
        tk.Label(total_frame, text=f"{total_cost:.2f}₽", font=('Arial', 10, 'bold'),
                bg=ModernStyles.COLORS['primary'], fg='white',
                relief='solid', bd=1).pack(side='left', fill='both', expand=True)

        # Инструкции
        instructions_frame = tk.Frame(self.recipe_details_frame, bg='white')
        instructions_frame.pack(fill='both', expand=True)

        tk.Label(instructions_frame, text="📝 Инструкции по приготовлению:",
                font=('Arial', 12, 'bold'), bg='white').pack(anchor='w', pady=(0, 10))

        inst_text_frame = tk.Frame(instructions_frame, bg='#f8f9fa', relief='solid', bd=1)
        inst_text_frame.pack(fill='both', expand=True)

        instructions_text = "\n".join([f"{i+1}. {instruction}"
                                     for i, instruction in enumerate(recipe['instructions'])])

        tk.Label(inst_text_frame, text=instructions_text, font=('Arial', 10),
                bg='#f8f9fa', justify='left', anchor='nw').pack(fill='both', expand=True, padx=15, pady=15)

    def calculate_cost(self, recipe_name, portions_str):
        """Рассчитать себестоимость"""
        # Очистить предыдущий результат
        for widget in self.calc_result_frame.winfo_children():
            widget.destroy()

        try:
            portions = float(portions_str)
        except ValueError:
            portions = 1

        if recipe_name not in self.recipes:
            return

        recipe = self.recipes[recipe_name]
        base_portions = recipe['portions']
        multiplier = portions / base_portions

        # Результат расчёта
        result_frame = tk.Frame(self.calc_result_frame, bg='#f8f9fa', relief='solid', bd=1)
        result_frame.pack(fill='both', expand=True)

        tk.Label(result_frame, text=f"💰 Расчёт себестоимости: {recipe_name}",
                font=('Arial', 14, 'bold'), bg='#f8f9fa').pack(pady=15)

        # Детали расчёта
        details_text = f"""
📊 ПАРАМЕТРЫ РАСЧЁТА:
• Базовое количество порций: {base_portions}
• Запрашиваемое количество: {portions}
• Коэффициент: {multiplier:.2f}

💰 РАСЧЁТ СТОИМОСТИ:
• Базовая себестоимость: {recipe['cost']:.2f}₽
• Себестоимость за {portions} порций: {recipe['cost'] * multiplier:.2f}₽
• Себестоимость за 1 порцию: {recipe['cost'] * multiplier / portions:.2f}₽

📈 ЦЕНООБРАЗОВАНИЕ:
• Рекомендуемая цена продажи: {recipe['price'] * multiplier:.2f}₽
• Цена за 1 порцию: {recipe['price']:.2f}₽
• Валовая прибыль: {(recipe['price'] - recipe['cost']) * multiplier:.2f}₽
• Маржинальность: {((recipe['price'] - recipe['cost']) / recipe['price']) * 100:.1f}%

🧮 ДЕТАЛИЗАЦИЯ ИНГРЕДИЕНТОВ:
        """

        for ingredient in recipe['ingredients']:
            adjusted_amount = ingredient['amount'] * multiplier
            adjusted_cost = ingredient['cost'] * multiplier
            details_text += f"• {ingredient['name']}: {adjusted_amount:.1f} {ingredient['unit']} = {adjusted_cost:.2f}₽\n"

        tk.Label(result_frame, text=details_text, font=('Arial', 11),
                bg='#f8f9fa', justify='left', anchor='nw').pack(fill='both', expand=True, padx=20, pady=20)

    def add_recipe(self):
        """Добавить новый рецепт"""
        messagebox.showinfo("Добавление рецепта", "Функция добавления нового рецепта")

    def edit_recipe(self, recipe_name):
        """Редактировать рецепт"""
        messagebox.showinfo("Редактирование", f"Редактирование рецепта: {recipe_name}")

    def delete_recipe(self, recipe_name):
        """Удалить рецепт"""
        if messagebox.askyesno("Подтверждение", f"Удалить рецепт '{recipe_name}'?"):
            messagebox.showinfo("Удаление", f"Рецепт '{recipe_name}' удалён")

    def add_sub_recipe(self):
        """Добавить подрецепт"""
        messagebox.showinfo("Добавление", "Функция добавления подрецепта")

    def edit_sub_recipe(self):
        """Редактировать подрецепт"""
        messagebox.showinfo("Редактирование", "Функция редактирования подрецепта")

    def delete_sub_recipe(self):
        """Удалить подрецепт"""
        messagebox.showinfo("Удаление", "Функция удаления подрецепта")

    def create_quality_tab(self, parent):
        """Создать вкладку контроля качества"""
        tk.Label(parent, text="🔍 Контроль Качества Рецептов",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        # Информационный блок
        info_frame = tk.Frame(parent, bg='#f8f9fa', relief='solid', bd=1)
        info_frame.pack(fill='both', expand=True, padx=20, pady=20)

        quality_text = """
🔍 СИСТЕМА КОНТРОЛЯ КАЧЕСТВА:

📋 СТАНДАРТЫ КАЧЕСТВА:
• Температурные режимы приготовления
• Время выдержки на каждом этапе
• Визуальные характеристики готовых блюд
• Органолептические показатели
• Пищевая безопасность

🎯 КРИТЕРИИ ОЦЕНКИ:
• Внешний вид: цвет, форма, подача
• Вкус: сбалансированность, насыщенность
• Аромат: соответствие блюду
• Текстура: консистенция, сочность
• Температура подачи

📊 СИСТЕМА БАЛЛОВ:
• 9-10 баллов: Отличное качество
• 7-8 баллов: Хорошее качество
• 5-6 баллов: Удовлетворительное
• Менее 5 баллов: Требует доработки

🔬 ЛАБОРАТОРНЫЙ КОНТРОЛЬ:
• Микробиологические показатели
• Химический состав
• Пищевая ценность
• Содержание аллергенов
• Срок годности

⚠️ КРИТИЧЕСКИЕ ТОЧКИ КОНТРОЛЯ:
• Температура хранения сырья
• Время термической обработки
• Температура готовых блюд
• Условия хранения полуфабрикатов
• Соблюдение санитарных норм

📈 МОНИТОРИНГ КАЧЕСТВА:
• Ежедневные проверки
• Еженедельные аудиты
• Обратная связь от гостей
• Анализ возвратов блюд
• Корректирующие действия
        """

        tk.Label(info_frame, text=quality_text, font=('Arial', 11),
                bg='#f8f9fa', justify='left', anchor='nw').pack(fill='both', expand=True, padx=20, pady=20)

    def create_production_tab(self, parent):
        """Создать вкладку планирования производства"""
        tk.Label(parent, text="⚙️ Планирование Производства",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        # Информационный блок
        info_frame = tk.Frame(parent, bg='#f8f9fa', relief='solid', bd=1)
        info_frame.pack(fill='both', expand=True, padx=20, pady=20)

        production_text = """
⚙️ СИСТЕМА ПЛАНИРОВАНИЯ ПРОИЗВОДСТВА:

📅 ПЛАНИРОВАНИЕ МЕНЮ:
• Ежедневное планирование блюд
• Сезонные изменения меню
• Специальные предложения
• Банкетное меню
• Детское меню

📊 РАСЧЁТ ПОТРЕБНОСТИ:
• Прогноз продаж по блюдам
• Расчёт необходимых ингредиентов
• Планирование закупок
• Оптимизация остатков
• Минимизация потерь

⏰ ВРЕМЕННОЕ ПЛАНИРОВАНИЕ:
• График приготовления блюд
• Последовательность операций
• Загрузка оборудования
• Распределение персонала
• Пиковые нагрузки

🏭 ПРОИЗВОДСТВЕННЫЕ МОЩНОСТИ:
• Пропускная способность кухни
• Загрузка оборудования
• Количество рабочих мест
• Время приготовления блюд
• Буферные запасы

📈 ОПТИМИЗАЦИЯ ПРОЦЕССОВ:
• Стандартизация рецептур
• Унификация технологий
• Автоматизация процессов
• Контроль качества
• Снижение себестоимости

🎯 КЛЮЧЕВЫЕ ПОКАЗАТЕЛИ:
• Время приготовления блюд
• Загрузка персонала
• Использование оборудования
• Процент брака
• Рентабельность производства

📋 ДОКУМЕНТООБОРОТ:
• Технологические карты
• Производственные задания
• Отчёты о выполнении
• Контроль качества
• Учёт расхода сырья
        """

        tk.Label(info_frame, text=production_text, font=('Arial', 11),
                bg='#f8f9fa', justify='left', anchor='nw').pack(fill='both', expand=True, padx=20, pady=20)

    def create_settings_tab(self, parent):
        """Создать вкладку настроек"""
        tk.Label(parent, text="⚙️ Настройки Системы Рецептов",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        # Настройки
        settings_frame = tk.Frame(parent, bg='#f8f9fa', relief='solid', bd=1)
        settings_frame.pack(fill='x', padx=20, pady=(0, 20))

        # Общие настройки
        general_frame = tk.LabelFrame(settings_frame, text="Общие настройки",
                                     font=('Arial', 12, 'bold'), bg='#f8f9fa')
        general_frame.pack(fill='x', padx=15, pady=15)

        # Единицы измерения
        units_frame = tk.Frame(general_frame, bg='#f8f9fa')
        units_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(units_frame, text="Единицы измерения по умолчанию:",
                font=('Arial', 11), bg='#f8f9fa').pack(side='left')

        units_var = tk.StringVar(value="Метрические")
        units_combo = ttk.Combobox(units_frame, textvariable=units_var,
                                  values=["Метрические", "Имперские", "Смешанные"],
                                  width=15)
        units_combo.pack(side='right', padx=10)

        # Валюта
        currency_frame = tk.Frame(general_frame, bg='#f8f9fa')
        currency_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(currency_frame, text="Валюта:",
                font=('Arial', 11), bg='#f8f9fa').pack(side='left')

        currency_var = tk.StringVar(value="RUB (₽)")
        currency_combo = ttk.Combobox(currency_frame, textvariable=currency_var,
                                     values=["RUB (₽)", "USD ($)", "EUR (€)"],
                                     width=15)
        currency_combo.pack(side='right', padx=10)

        # Настройки калькулятора
        calc_frame = tk.LabelFrame(settings_frame, text="Настройки калькулятора",
                                  font=('Arial', 12, 'bold'), bg='#f8f9fa')
        calc_frame.pack(fill='x', padx=15, pady=15)

        # Наценка по умолчанию
        markup_frame = tk.Frame(calc_frame, bg='#f8f9fa')
        markup_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(markup_frame, text="Наценка по умолчанию (%):",
                font=('Arial', 11), bg='#f8f9fa').pack(side='left')

        markup_var = tk.StringVar(value="150")
        markup_entry = tk.Entry(markup_frame, textvariable=markup_var, width=10)
        markup_entry.pack(side='right', padx=10)

        # Кнопки сохранения
        btn_frame = tk.Frame(settings_frame, bg='#f8f9fa')
        btn_frame.pack(fill='x', padx=15, pady=15)

        tk.Button(btn_frame, text="💾 Сохранить настройки", command=self.save_settings,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(side='left')

        tk.Button(btn_frame, text="🔄 Сброс по умолчанию", command=self.reset_settings,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(side='right')

    # Новые профессиональные методы
    def import_recipes(self):
        """Импорт рецептов из файла"""
        file_path = filedialog.askopenfilename(
            title="Импорт рецептов",
            filetypes=[("JSON файлы", "*.json"), ("CSV файлы", "*.csv"), ("Все файлы", "*.*")]
        )
        if file_path:
            messagebox.showinfo("Импорт", f"Рецепты импортированы из файла:\n{file_path}")

    def export_recipes(self):
        """Экспорт рецептов в файл"""
        file_path = filedialog.asksaveasfilename(
            title="Экспорт рецептов",
            defaultextension=".json",
            filetypes=[("JSON файлы", "*.json"), ("CSV файлы", "*.csv"), ("Excel файлы", "*.xlsx")]
        )
        if file_path:
            messagebox.showinfo("Экспорт", f"Рецепты экспортированы в файл:\n{file_path}")

    def generate_reports(self):
        """Генерация отчётов"""
        messagebox.showinfo("Отчёты", "Система отчётов по рецептам запущена")

    def save_settings(self):
        """Сохранить настройки"""
        messagebox.showinfo("Настройки", "Настройки успешно сохранены")

    def reset_settings(self):
        """Сброс настроек"""
        if messagebox.askyesno("Сброс", "Сбросить все настройки к значениям по умолчанию?"):
            messagebox.showinfo("Сброс", "Настройки сброшены к значениям по умолчанию")

    def show_calculator(self):
        """Показать калькулятор"""
        messagebox.showinfo("Калькулятор", "Калькулятор себестоимости открыт")

def create_recipe_manager(parent, db_manager):
    """Создать профессиональный менеджер рецептов"""
    manager = RecipeManager(parent, db_manager)
    return manager.create_window()