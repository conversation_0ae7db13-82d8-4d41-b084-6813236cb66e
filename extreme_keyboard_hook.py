#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import ctypes
import ctypes.wintypes
import threading
import time
import sys
import os

# Windows API константы
WH_KEYBOARD_LL = 13
WM_KEYDOWN = 0x0100
WM_KEYUP = 0x0101
WM_SYSKEYDOWN = 0x0104
WM_SYSKEYUP = 0x0105

# Виртуальные коды клавиш
VK_LMENU = 0xA4  # Left Alt
VK_RMENU = 0xA5  # Right Alt
VK_LSHIFT = 0xA0  # Left Shift
VK_RSHIFT = 0xA1  # Right Shift

class ExtremKeyboardHook:
    """Экстремальный системный хук для принудительного переключения раскладки"""
    
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32
        self.hook = None
        self.running = False
        self.switch_attempts = 0
        self.max_attempts = 100
        
        # Определяем тип функции хука
        self.HOOKPROC = ctypes.WINFUNCTYPE(ctypes.c_int, ctypes.c_int, ctypes.wintypes.WPARAM, ctypes.wintypes.LPARAM)
        
    def low_level_keyboard_proc(self, nCode, wParam, lParam):
        """Низкоуровневый обработчик клавиатуры"""
        try:
            if nCode >= 0:
                # Проверяем текущую раскладку
                current_layout = self.get_current_layout()
                
                # Если раскладка не русская, принудительно переключаем
                if current_layout != 0x0419 and self.switch_attempts < self.max_attempts:
                    self.force_switch_to_russian()
                    self.switch_attempts += 1
                    
                    if self.switch_attempts % 10 == 0:
                        print(f"🔄 Попытка переключения #{self.switch_attempts}")
                
        except Exception as e:
            print(f"Ошибка в хуке: {e}")
        
        # Передаем управление следующему хуку
        return self.user32.CallNextHookEx(self.hook, nCode, wParam, lParam)
    
    def get_current_layout(self):
        """Получить текущую раскладку клавиатуры"""
        try:
            hwnd = self.user32.GetForegroundWindow()
            thread_id = self.user32.GetWindowThreadProcessId(hwnd, None)
            hkl = self.user32.GetKeyboardLayout(thread_id)
            return hkl & 0xFFFF
        except:
            return 0
    
    def force_switch_to_russian(self):
        """Принудительное переключение на русскую раскладку всеми методами"""
        try:
            # Метод 1: LoadKeyboardLayout + ActivateKeyboardLayout
            hkl_russian = self.user32.LoadKeyboardLayoutW("00000419", 0x00000001)
            if hkl_russian:
                self.user32.ActivateKeyboardLayout(hkl_russian, 0x00000008)
            
            # Метод 2: PostMessage для всех окон
            def enum_windows_callback(hwnd, lParam):
                try:
                    self.user32.PostMessageW(hwnd, 0x0050, 0, 0x4190419)
                except:
                    pass
                return True
            
            EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.wintypes.HWND, ctypes.wintypes.LPARAM)
            enum_proc = EnumWindowsProc(enum_windows_callback)
            self.user32.EnumWindows(enum_proc, 0)
            
            # Метод 3: SendInput для Alt+Shift
            class INPUT(ctypes.Structure):
                _fields_ = [("type", ctypes.c_ulong),
                           ("ki", ctypes.c_ulong * 6)]
            
            # Alt down
            alt_down = INPUT()
            alt_down.type = 1  # INPUT_KEYBOARD
            alt_down.ki[0] = 0x12  # VK_MENU (Alt)
            alt_down.ki[1] = 0  # scan code
            alt_down.ki[2] = 0  # flags
            alt_down.ki[3] = 0  # time
            alt_down.ki[4] = 0  # extra info
            alt_down.ki[5] = 0  # padding
            
            # Shift down
            shift_down = INPUT()
            shift_down.type = 1
            shift_down.ki[0] = 0x10  # VK_SHIFT
            shift_down.ki[1] = 0
            shift_down.ki[2] = 0
            shift_down.ki[3] = 0
            shift_down.ki[4] = 0
            shift_down.ki[5] = 0
            
            # Shift up
            shift_up = INPUT()
            shift_up.type = 1
            shift_up.ki[0] = 0x10
            shift_up.ki[1] = 0
            shift_up.ki[2] = 2  # KEYEVENTF_KEYUP
            shift_up.ki[3] = 0
            shift_up.ki[4] = 0
            shift_up.ki[5] = 0
            
            # Alt up
            alt_up = INPUT()
            alt_up.type = 1
            alt_up.ki[0] = 0x12
            alt_up.ki[1] = 0
            alt_up.ki[2] = 2  # KEYEVENTF_KEYUP
            alt_up.ki[3] = 0
            alt_up.ki[4] = 0
            alt_up.ki[5] = 0
            
            # Отправляем последовательность
            inputs = (INPUT * 4)(alt_down, shift_down, shift_up, alt_up)
            self.user32.SendInput(4, inputs, ctypes.sizeof(INPUT))
            
            # Метод 4: Прямое изменение через SetKeyboardState
            keyboard_state = (ctypes.c_ubyte * 256)()
            self.user32.GetKeyboardState(keyboard_state)
            self.user32.SetKeyboardState(keyboard_state)
            
        except Exception as e:
            print(f"Ошибка при принудительном переключении: {e}")
    
    def install_hook(self):
        """Установить системный хук"""
        try:
            print("🔧 Установка экстремального системного хука...")
            
            # Создаем функцию хука
            self.hook_proc = self.HOOKPROC(self.low_level_keyboard_proc)
            
            # Устанавливаем хук
            self.hook = self.user32.SetWindowsHookExW(
                WH_KEYBOARD_LL,
                self.hook_proc,
                self.kernel32.GetModuleHandleW(None),
                0
            )
            
            if not self.hook:
                error = self.kernel32.GetLastError()
                print(f"❌ Не удалось установить хук. Ошибка: {error}")
                return False
            
            print("✅ Экстремальный хук установлен успешно!")
            self.running = True
            return True
            
        except Exception as e:
            print(f"❌ Ошибка при установке хука: {e}")
            return False
    
    def uninstall_hook(self):
        """Удалить системный хук"""
        if self.hook:
            self.user32.UnhookWindowsHookEx(self.hook)
            self.hook = None
            self.running = False
            print("🔧 Хук удален")
    
    def run_message_loop(self):
        """Запустить цикл обработки сообщений"""
        print("🚀 Запуск цикла обработки сообщений...")
        print("Нажмите Ctrl+C для остановки")
        
        try:
            msg = ctypes.wintypes.MSG()
            while self.running:
                bRet = self.user32.GetMessageW(ctypes.byref(msg), None, 0, 0)
                if bRet == 0 or bRet == -1:
                    break
                self.user32.TranslateMessage(ctypes.byref(msg))
                self.user32.DispatchMessageW(ctypes.byref(msg))
                
        except KeyboardInterrupt:
            print("\n🛑 Получен сигнал остановки")
        finally:
            self.uninstall_hook()

def run_extreme_hook():
    """Запустить экстремальный хук"""
    print("🚀 ЭКСТРЕМАЛЬНЫЙ СИСТЕМНЫЙ ХУК ДЛЯ ПЕРЕКЛЮЧЕНИЯ РАСКЛАДКИ")
    print("=" * 80)
    
    # Проверяем права администратора
    try:
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        if not is_admin:
            print("⚠️ ВНИМАНИЕ: Рекомендуется запуск от имени администратора")
            print("Для максимальной эффективности запустите PowerShell как администратор")
    except:
        pass
    
    hook = ExtremKeyboardHook()
    
    if hook.install_hook():
        # Запускаем в отдельном потоке
        hook_thread = threading.Thread(target=hook.run_message_loop, daemon=True)
        hook_thread.start()
        
        print("\n💡 Хук активен! Теперь система будет принудительно переключать раскладку.")
        print("Попробуйте нажать любые клавиши - раскладка должна автоматически переключиться на русскую.")
        
        # Основной цикл мониторинга
        try:
            while hook.running:
                current_layout = hook.get_current_layout()
                if current_layout == 0x0419:
                    print("✅ Раскладка: РУССКАЯ")
                else:
                    print(f"⚠️ Раскладка: {hex(current_layout)} (не русская)")
                
                time.sleep(2)
                
        except KeyboardInterrupt:
            print("\n🛑 Остановка хука...")
            hook.running = False
            hook.uninstall_hook()
    
    else:
        print("❌ Не удалось установить хук")
        return False
    
    return True

if __name__ == "__main__":
    print("ЭКСТРЕМАЛЬНЫЙ МЕТОД ПЕРЕКЛЮЧЕНИЯ РАСКЛАДКИ КЛАВИАТУРЫ")
    print("Этот скрипт устанавливает системный хук для принудительного переключения")
    print("раскладки клавиатуры на русскую при каждом нажатии клавиши.")
    print()
    
    choice = input("Запустить экстремальный хук? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes', 'да', 'д']:
        success = run_extreme_hook()
        if success:
            print("✅ Хук отработал успешно")
        else:
            print("❌ Хук не удался")
    else:
        print("Операция отменена")
    
    input("\nНажмите Enter для выхода...")
