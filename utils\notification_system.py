"""
Система уведомлений для ресторанной системы управления
Обеспечивает уведомления о критических событиях, низких запасах, просроченных заказах
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
from datetime import datetime, timedelta
from typing import List, Dict, Callable
import json
import os

class NotificationLevel:
    """Уровни важности уведомлений"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class Notification:
    """Класс уведомления"""
    
    def __init__(self, title: str, message: str, level: str = NotificationLevel.INFO, 
                 action_callback: Callable = None, auto_dismiss: int = 5000):
        self.id = f"notif_{int(time.time() * 1000)}"
        self.title = title
        self.message = message
        self.level = level
        self.timestamp = datetime.now()
        self.action_callback = action_callback
        self.auto_dismiss = auto_dismiss
        self.dismissed = False

class NotificationSystem:
    """Система управления уведомлениями"""
    
    def __init__(self, parent_window=None):
        self.parent = parent_window
        self.notifications: List[Notification] = []
        self.notification_window = None
        self.notification_frame = None
        self.max_notifications = 5
        self.notification_widgets = {}
        
        # Цвета для разных уровней
        self.level_colors = {
            NotificationLevel.INFO: "#3b82f6",      # Синий
            NotificationLevel.WARNING: "#f59e0b",   # Жёлтый
            NotificationLevel.ERROR: "#ef4444",     # Красный
            NotificationLevel.CRITICAL: "#dc2626"   # Тёмно-красный
        }
        
        # Иконки для уровней
        self.level_icons = {
            NotificationLevel.INFO: "ℹ️",
            NotificationLevel.WARNING: "⚠️",
            NotificationLevel.ERROR: "❌",
            NotificationLevel.CRITICAL: "🚨"
        }
        
        self.setup_notification_area()
        self.start_monitoring()
    
    def setup_notification_area(self):
        """Создать область уведомлений"""
        try:
            if not self.parent:
                return

            # Создать окно уведомлений в правом верхнем углу
            self.notification_window = tk.Toplevel(self.parent)
            self.notification_window.title("Уведомления")
            self.notification_window.geometry("350x400+{}+50".format(
                self.parent.winfo_screenwidth() - 400
            ))
            self.notification_window.configure(bg='#f8fafc')
            self.notification_window.attributes('-topmost', True)
            self.notification_window.withdraw()  # Скрыть изначально

            # Добавить обработчик закрытия окна
            self.notification_window.protocol("WM_DELETE_WINDOW", self.on_notification_window_close)
        except Exception as e:
            print(f"Ошибка при создании области уведомлений: {e}")
            self.notification_window = None
            return
        
        # Заголовок
        header_frame = tk.Frame(self.notification_window, bg='#1e40af', height=40)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="🔔 Уведомления Системы", 
                font=('Arial', 12, 'bold'),
                bg='#1e40af', fg='white').pack(side='left', padx=10, pady=10)
        
        close_btn = tk.Button(header_frame, text="✕", 
                             command=self.hide_notifications,
                             bg='#1e40af', fg='white', 
                             relief='flat', font=('Arial', 12, 'bold'))
        close_btn.pack(side='right', padx=10, pady=5)
        
        # Область прокрутки для уведомлений
        canvas = tk.Canvas(self.notification_window, bg='#f8fafc')
        scrollbar = ttk.Scrollbar(self.notification_window, orient="vertical", command=canvas.yview)
        self.notification_frame = tk.Frame(canvas, bg='#f8fafc')
        
        self.notification_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.notification_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def add_notification(self, title: str, message: str, level: str = NotificationLevel.INFO,
                        action_callback: Callable = None, auto_dismiss: int = 5000):
        """Добавить новое уведомление"""
        notification = Notification(title, message, level, action_callback, auto_dismiss)
        self.notifications.insert(0, notification)  # Добавить в начало
        
        # Ограничить количество уведомлений
        if len(self.notifications) > self.max_notifications:
            old_notif = self.notifications.pop()
            if old_notif.id in self.notification_widgets:
                self.notification_widgets[old_notif.id].destroy()
                del self.notification_widgets[old_notif.id]
        
        self.display_notification(notification)
        self.show_notifications()
        
        # Автоматическое скрытие
        if auto_dismiss > 0:
            threading.Timer(auto_dismiss / 1000, 
                          lambda: self.dismiss_notification(notification.id)).start()
        
        # Сохранить в лог
        self.log_notification(notification)
        
        print(f"[{level.upper()}] {title}: {message}")
    
    def display_notification(self, notification: Notification):
        """Отобразить уведомление в интерфейсе"""
        if not self.notification_frame:
            return
        
        # Создать виджет уведомления
        notif_frame = tk.Frame(self.notification_frame, 
                              bg='white', relief='solid', bd=1)
        notif_frame.pack(fill='x', padx=5, pady=2)
        
        # Цветная полоса слева
        color_bar = tk.Frame(notif_frame, 
                           bg=self.level_colors[notification.level], 
                           width=4)
        color_bar.pack(side='left', fill='y')
        
        # Содержимое уведомления
        content_frame = tk.Frame(notif_frame, bg='white')
        content_frame.pack(side='left', fill='both', expand=True, padx=10, pady=5)
        
        # Заголовок с иконкой
        title_frame = tk.Frame(content_frame, bg='white')
        title_frame.pack(fill='x')
        
        icon_label = tk.Label(title_frame, 
                             text=self.level_icons[notification.level],
                             bg='white', font=('Arial', 12))
        icon_label.pack(side='left')
        
        title_label = tk.Label(title_frame, text=notification.title,
                              bg='white', font=('Arial', 10, 'bold'),
                              fg=self.level_colors[notification.level])
        title_label.pack(side='left', padx=(5, 0))
        
        # Время
        time_label = tk.Label(title_frame, 
                             text=notification.timestamp.strftime("%H:%M"),
                             bg='white', font=('Arial', 8),
                             fg='#6b7280')
        time_label.pack(side='right')
        
        # Сообщение
        message_label = tk.Label(content_frame, text=notification.message,
                                bg='white', font=('Arial', 9),
                                fg='#374151', wraplength=250, justify='left')
        message_label.pack(anchor='w', pady=(2, 0))
        
        # Кнопка действия (если есть)
        if notification.action_callback:
            action_btn = tk.Button(content_frame, text="Действие",
                                  command=notification.action_callback,
                                  bg=self.level_colors[notification.level],
                                  fg='white', font=('Arial', 8),
                                  relief='flat', padx=10, pady=2)
            action_btn.pack(anchor='w', pady=(5, 0))
        
        # Кнопка закрытия
        close_btn = tk.Button(notif_frame, text="✕",
                             command=lambda: self.dismiss_notification(notification.id),
                             bg='white', fg='#6b7280',
                             relief='flat', font=('Arial', 8))
        close_btn.pack(side='right', padx=5)
        
        self.notification_widgets[notification.id] = notif_frame
    
    def dismiss_notification(self, notification_id: str):
        """Скрыть уведомление"""
        try:
            if notification_id in self.notification_widgets:
                self.notification_widgets[notification_id].destroy()
                del self.notification_widgets[notification_id]

            # Удалить из списка
            self.notifications = [n for n in self.notifications if n.id != notification_id]

            # Скрыть окно если нет уведомлений
            if not self.notifications:
                self.hide_notifications()
        except Exception as e:
            print(f"Ошибка при удалении уведомления: {e}")
    
    def show_notifications(self):
        """Показать окно уведомлений"""
        try:
            if self.notification_window and self.notification_window.winfo_exists():
                self.notification_window.deiconify()
        except Exception as e:
            print(f"Ошибка при показе уведомлений: {e}")

    def hide_notifications(self):
        """Скрыть окно уведомлений"""
        try:
            if self.notification_window and self.notification_window.winfo_exists():
                self.notification_window.withdraw()
        except Exception as e:
            print(f"Ошибка при скрытии уведомлений: {e}")
            # Если окно уже не существует, просто очистим ссылку
            self.notification_window = None

    def on_notification_window_close(self):
        """Обработчик закрытия окна уведомлений"""
        try:
            if self.notification_window:
                self.notification_window.withdraw()
        except Exception as e:
            print(f"Ошибка при закрытии окна уведомлений: {e}")
            self.notification_window = None

    def log_notification(self, notification: Notification):
        """Сохранить уведомление в лог"""
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        log_file = os.path.join(log_dir, "notifications.log")
        
        log_entry = {
            "timestamp": notification.timestamp.isoformat(),
            "level": notification.level,
            "title": notification.title,
            "message": notification.message
        }
        
        try:
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + "\n")
        except Exception as e:
            print(f"Ошибка записи в лог: {e}")
    
    def start_monitoring(self):
        """Запустить мониторинг системы"""
        def monitor():
            while True:
                try:
                    # Здесь можно добавить проверки системы
                    # Например, проверка низких запасов, просроченных заказов и т.д.
                    time.sleep(30)  # Проверять каждые 30 секунд
                except Exception as e:
                    print(f"Ошибка мониторинга: {e}")
                    time.sleep(60)
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()

# Глобальный экземпляр системы уведомлений
notification_system = None

def init_notifications(parent_window):
    """Инициализировать систему уведомлений"""
    global notification_system
    notification_system = NotificationSystem(parent_window)
    return notification_system

def notify(title: str, message: str, level: str = NotificationLevel.INFO, 
          action_callback: Callable = None, auto_dismiss: int = 5000):
    """Быстрая функция для отправки уведомления"""
    if notification_system:
        notification_system.add_notification(title, message, level, action_callback, auto_dismiss)
    else:
        print(f"[{level.upper()}] {title}: {message}")
