#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ТЕСТ ПЕРЕКЛЮЧЕНИЯ РАСКЛАДКИ В ПРОЦЕССЕ ЛОГИНА
Проверяет что происходит с раскладкой при входе в систему
"""

import tkinter as tk
from tkinter import messagebox
import ctypes
import time
import threading

def safe_print(text):
    """Безопасный вывод с обработкой Unicode ошибок"""
    try:
        print(text)
    except UnicodeEncodeError:
        ascii_text = text.encode('ascii', 'ignore').decode('ascii')
        print(ascii_text)

def get_current_layout():
    """Получить текущую раскладку клавиатуры"""
    try:
        user32 = ctypes.windll.user32
        hwnd = user32.GetForegroundWindow()
        thread_id = user32.GetWindowThreadProcessId(hwnd, None)
        hkl = user32.GetKeyboardLayout(thread_id)
        layout_id = hkl & 0xFFFF
        return layout_id
    except:
        return None

def switch_to_russian():
    """Переключить на русскую раскладку"""
    try:
        user32 = ctypes.windll.user32
        
        # Метод 1: LoadKeyboardLayout + ActivateKeyboardLayout
        hkl_russian = user32.LoadKeyboardLayoutW("00000419", 0x00000001)
        if hkl_russian:
            result1 = user32.ActivateKeyboardLayout(hkl_russian, 0x00000008)
            if result1:
                safe_print("✅ Метод 1 (LoadKeyboardLayout) успешен")
                return True
        
        # Метод 2: Прямое переключение по ID
        result2 = user32.ActivateKeyboardLayout(0x4190419, 0x00000001)
        if result2:
            safe_print("✅ Метод 2 (прямое переключение) успешен")
            return True
            
        # Метод 3: PostMessage с WM_INPUTLANGCHANGEREQUEST
        WM_INPUTLANGCHANGEREQUEST = 0x0050
        hwnd = user32.GetForegroundWindow()
        result3 = user32.PostMessageW(hwnd, WM_INPUTLANGCHANGEREQUEST, 0, 0x4190419)
        if result3:
            safe_print("✅ Метод 3 (PostMessage) успешен")
            return True
            
        safe_print("❌ Все методы переключения не сработали")
        return False
        
    except Exception as e:
        safe_print(f"❌ Ошибка переключения: {e}")
        return False

def switch_to_english():
    """Переключить на английскую раскладку"""
    try:
        user32 = ctypes.windll.user32
        
        # Переключение на английскую раскладку
        hkl_english = user32.LoadKeyboardLayoutW("00000409", 0x00000001)
        if hkl_english:
            result = user32.ActivateKeyboardLayout(hkl_english, 0x00000008)
            if result:
                safe_print("✅ Переключение на английский успешно")
                return True
        
        safe_print("❌ Переключение на английский не удалось")
        return False
        
    except Exception as e:
        safe_print(f"❌ Ошибка переключения на английский: {e}")
        return False

class LoginKeyboardTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Тест Переключения Раскладки при Логине")
        self.root.geometry("600x400")
        self.root.configure(bg='#2c3e50')
        
        self.setup_ui()
        self.update_layout_info()
        
    def setup_ui(self):
        """Настройка интерфейса"""
        # Заголовок
        title_label = tk.Label(
            self.root, 
            text="ТЕСТ ПЕРЕКЛЮЧЕНИЯ РАСКЛАДКИ ПРИ ЛОГИНЕ",
            font=('Cambria', 16, 'bold'),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(pady=20)
        
        # Информация о текущей раскладке
        self.layout_info = tk.Label(
            self.root,
            text="Текущая раскладка: Проверка...",
            font=('Cambria', 12),
            fg='white',
            bg='#2c3e50'
        )
        self.layout_info.pack(pady=10)
        
        # Поле для ввода (для тестирования раскладки)
        tk.Label(
            self.root,
            text="Тестовое поле ввода:",
            font=('Cambria', 12),
            fg='white',
            bg='#2c3e50'
        ).pack(pady=(20, 5))
        
        self.test_entry = tk.Entry(
            self.root,
            font=('Cambria', 14),
            width=40
        )
        self.test_entry.pack(pady=5)
        self.test_entry.focus_set()
        
        # Кнопки управления
        button_frame = tk.Frame(self.root, bg='#2c3e50')
        button_frame.pack(pady=20)
        
        tk.Button(
            button_frame,
            text="Переключить на РУССКИЙ",
            font=('Cambria', 12, 'bold'),
            bg='#e74c3c',
            fg='white',
            command=self.switch_to_russian_test,
            width=20
        ).pack(side=tk.LEFT, padx=10)
        
        tk.Button(
            button_frame,
            text="Переключить на ENGLISH",
            font=('Cambria', 12, 'bold'),
            bg='#3498db',
            fg='white',
            command=self.switch_to_english_test,
            width=20
        ).pack(side=tk.LEFT, padx=10)
        
        tk.Button(
            button_frame,
            text="Обновить информацию",
            font=('Cambria', 12),
            bg='#27ae60',
            fg='white',
            command=self.update_layout_info,
            width=20
        ).pack(side=tk.LEFT, padx=10)
        
        # Лог событий
        tk.Label(
            self.root,
            text="Лог событий:",
            font=('Cambria', 12),
            fg='white',
            bg='#2c3e50'
        ).pack(pady=(20, 5))
        
        self.log_text = tk.Text(
            self.root,
            height=8,
            width=70,
            font=('Cambria', 10),
            bg='#34495e',
            fg='white'
        )
        self.log_text.pack(pady=5)
        
        # Симуляция логина
        login_frame = tk.Frame(self.root, bg='#2c3e50')
        login_frame.pack(pady=20)
        
        tk.Button(
            login_frame,
            text="СИМУЛЯЦИЯ ЛОГИНА",
            font=('Cambria', 14, 'bold'),
            bg='#9b59b6',
            fg='white',
            command=self.simulate_login,
            width=25,
            height=2
        ).pack()
        
    def log_message(self, message):
        """Добавить сообщение в лог"""
        self.log_text.insert(tk.END, f"{time.strftime('%H:%M:%S')} - {message}\n")
        self.log_text.see(tk.END)
        safe_print(message)
        
    def update_layout_info(self):
        """Обновить информацию о раскладке"""
        layout_id = get_current_layout()
        if layout_id:
            if layout_id == 0x0419:
                layout_name = "РУССКАЯ (0x0419)"
                color = '#27ae60'
            elif layout_id == 0x0409:
                layout_name = "АНГЛИЙСКАЯ (0x0409)"
                color = '#3498db'
            else:
                layout_name = f"ДРУГАЯ ({hex(layout_id)})"
                color = '#f39c12'
                
            self.layout_info.config(
                text=f"Текущая раскладка: {layout_name}",
                fg=color
            )
            self.log_message(f"Раскладка обновлена: {layout_name}")
        else:
            self.layout_info.config(
                text="Текущая раскладка: ОШИБКА ОПРЕДЕЛЕНИЯ",
                fg='#e74c3c'
            )
            self.log_message("Ошибка определения раскладки")
    
    def switch_to_russian_test(self):
        """Тест переключения на русский"""
        self.log_message("Попытка переключения на РУССКУЮ раскладку...")
        success = switch_to_russian()
        time.sleep(0.5)
        self.update_layout_info()
        
        if success:
            self.log_message("✅ Переключение на русский УСПЕШНО")
        else:
            self.log_message("❌ Переключение на русский НЕ УДАЛОСЬ")
    
    def switch_to_english_test(self):
        """Тест переключения на английский"""
        self.log_message("Попытка переключения на АНГЛИЙСКУЮ раскладку...")
        success = switch_to_english()
        time.sleep(0.5)
        self.update_layout_info()
        
        if success:
            self.log_message("✅ Переключение на английский УСПЕШНО")
        else:
            self.log_message("❌ Переключение на английский НЕ УДАЛОСЬ")
    
    def simulate_login(self):
        """Симуляция процесса логина"""
        self.log_message("🚀 НАЧАЛО СИМУЛЯЦИИ ЛОГИНА")
        self.log_message("=" * 50)
        
        # Шаг 1: Переключение на английский (как при открытии логина)
        self.log_message("Шаг 1: Переключение на английский для ввода логина...")
        switch_to_english()
        time.sleep(1)
        self.update_layout_info()
        
        # Шаг 2: Симуляция ввода логина/пароля
        self.log_message("Шаг 2: Симуляция ввода логина и пароля...")
        time.sleep(2)
        
        # Шаг 3: Симуляция успешного логина
        self.log_message("Шаг 3: Успешный логин - переключение на русский...")
        success = switch_to_russian()
        time.sleep(1)
        self.update_layout_info()
        
        if success:
            self.log_message("🎉 СИМУЛЯЦИЯ ЛОГИНА ЗАВЕРШЕНА УСПЕШНО!")
            self.log_message("Раскладка должна быть русской для работы в системе")
        else:
            self.log_message("💥 ПРОБЛЕМА: Не удалось переключить на русский после логина!")
            self.log_message("ЭТО И ЕСТЬ ПРОБЛЕМА ПОЛЬЗОВАТЕЛЯ!")
        
        self.log_message("=" * 50)
    
    def run(self):
        """Запуск теста"""
        self.root.mainloop()

def main():
    """Главная функция"""
    safe_print("🚀 ЗАПУСК ТЕСТА ПЕРЕКЛЮЧЕНИЯ РАСКЛАДКИ ПРИ ЛОГИНЕ")
    safe_print("=" * 60)
    
    app = LoginKeyboardTest()
    app.run()

if __name__ == "__main__":
    main()
