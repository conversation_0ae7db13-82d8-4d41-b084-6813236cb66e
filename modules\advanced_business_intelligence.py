"""
Advanced Business Intelligence Dashboard
Comprehensive BI dashboard with predictive analytics, machine learning insights,
trend forecasting, competitor analysis, and strategic planning tools.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from datetime import datetime, timedelta
import json
import uuid
import random
import math
from gui.styles import ModernStyles

class AdvancedBusinessIntelligence:
    """Advanced Business Intelligence Dashboard System"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.notebook = None
        
        # BI configuration
        self.bi_config = {
            "forecast_periods": 12,
            "trend_analysis_days": 90,
            "competitor_tracking_enabled": True,
            "ml_predictions_enabled": True,
            "alert_thresholds": {
                "revenue_decline": -10,
                "cost_increase": 15,
                "customer_churn": 20,
                "inventory_shortage": 5
            }
        }
        
        # KPI categories
        self.kpi_categories = {
            "financial": {
                "name": "Финансовые Показатели",
                "icon": "💰",
                "color": ModernStyles.COLORS['success'],
                "metrics": ["revenue", "profit_margin", "cost_ratio", "roi"]
            },
            "operational": {
                "name": "Операционные Показатели", 
                "icon": "⚙️",
                "color": ModernStyles.COLORS['info'],
                "metrics": ["table_turnover", "service_time", "order_accuracy", "waste_ratio"]
            },
            "customer": {
                "name": "Клиентские Показатели",
                "icon": "👥",
                "color": ModernStyles.COLORS['primary'],
                "metrics": ["satisfaction", "retention", "acquisition", "lifetime_value"]
            },
            "staff": {
                "name": "Кадровые Показатели",
                "icon": "👨‍💼",
                "color": ModernStyles.COLORS['warning'],
                "metrics": ["productivity", "turnover", "training_hours", "satisfaction"]
            }
        }
        
        # Prediction models
        self.prediction_models = {
            "revenue_forecast": "Прогноз Выручки",
            "demand_prediction": "Прогноз Спроса",
            "customer_churn": "Отток Клиентов",
            "inventory_optimization": "Оптимизация Запасов",
            "staff_scheduling": "Планирование Персонала",
            "seasonal_trends": "Сезонные Тренды"
        }
        
        # Competitor analysis categories
        self.competitor_categories = {
            "pricing": "Ценовая Политика",
            "menu": "Меню и Блюда",
            "service": "Качество Обслуживания",
            "marketing": "Маркетинговые Активности",
            "location": "Местоположение",
            "reviews": "Отзывы Клиентов"
        }
        
        # Initialize BI database tables
        self._init_bi_tables()
    
    def _init_bi_tables(self):
        """Initialize business intelligence database tables"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # BI metrics table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS bi_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        metric_name TEXT NOT NULL,
                        metric_category TEXT NOT NULL,
                        metric_value DECIMAL(15,4) NOT NULL,
                        metric_unit TEXT,
                        measurement_date DATE NOT NULL,
                        measurement_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        location_id INTEGER,
                        department TEXT,
                        notes TEXT,
                        created_by INTEGER,
                        FOREIGN KEY (location_id) REFERENCES locations (id),
                        FOREIGN KEY (created_by) REFERENCES users (id)
                    )
                ''')
                
                # Predictive models table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS bi_predictive_models (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        model_name TEXT NOT NULL,
                        model_type TEXT NOT NULL,
                        model_parameters TEXT,
                        training_data_period_start DATE,
                        training_data_period_end DATE,
                        accuracy_score DECIMAL(5,4),
                        last_trained TIMESTAMP,
                        is_active BOOLEAN DEFAULT 1,
                        created_by INTEGER,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (created_by) REFERENCES users (id)
                    )
                ''')
                
                # Forecasts table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS bi_forecasts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        model_id INTEGER NOT NULL,
                        forecast_type TEXT NOT NULL,
                        forecast_period DATE NOT NULL,
                        predicted_value DECIMAL(15,4) NOT NULL,
                        confidence_interval_lower DECIMAL(15,4),
                        confidence_interval_upper DECIMAL(15,4),
                        actual_value DECIMAL(15,4),
                        forecast_accuracy DECIMAL(5,4),
                        generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (model_id) REFERENCES bi_predictive_models (id)
                    )
                ''')
                
                # Competitor analysis table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS bi_competitor_analysis (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        competitor_name TEXT NOT NULL,
                        analysis_category TEXT NOT NULL,
                        analysis_date DATE NOT NULL,
                        metric_name TEXT NOT NULL,
                        competitor_value DECIMAL(15,4),
                        our_value DECIMAL(15,4),
                        variance_percent DECIMAL(8,4),
                        competitive_position TEXT,
                        recommendations TEXT,
                        data_source TEXT,
                        analyzed_by INTEGER,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (analyzed_by) REFERENCES users (id)
                    )
                ''')
                
                # Strategic insights table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS bi_strategic_insights (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        insight_title TEXT NOT NULL,
                        insight_category TEXT NOT NULL,
                        insight_description TEXT NOT NULL,
                        impact_level TEXT NOT NULL,
                        priority_score INTEGER DEFAULT 5,
                        recommended_actions TEXT,
                        expected_outcome TEXT,
                        implementation_timeline TEXT,
                        resource_requirements TEXT,
                        status TEXT DEFAULT 'identified',
                        assigned_to INTEGER,
                        due_date DATE,
                        created_by INTEGER,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (assigned_to) REFERENCES users (id),
                        FOREIGN KEY (created_by) REFERENCES users (id)
                    )
                ''')
                
                # Alerts and notifications table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS bi_alerts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        alert_type TEXT NOT NULL,
                        alert_title TEXT NOT NULL,
                        alert_message TEXT NOT NULL,
                        severity_level TEXT NOT NULL,
                        metric_name TEXT,
                        threshold_value DECIMAL(15,4),
                        actual_value DECIMAL(15,4),
                        variance_percent DECIMAL(8,4),
                        alert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        is_acknowledged BOOLEAN DEFAULT 0,
                        acknowledged_by INTEGER,
                        acknowledged_at TIMESTAMP,
                        action_taken TEXT,
                        FOREIGN KEY (acknowledged_by) REFERENCES users (id)
                    )
                ''')
                
                conn.commit()
                print("Business Intelligence database tables initialized successfully")
                
        except Exception as e:
            print(f"Error initializing BI tables: {e}")
    
    def show_bi_dashboard(self):
        """Show business intelligence dashboard window"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
            
        self.create_window()
        self.create_interface()
        self.load_bi_data()
    
    def create_window(self):
        """Create the BI dashboard window"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("📊 Расширенная Бизнес-Аналитика")
        self.window.geometry("1600x1000")
        self.window.configure(bg=ModernStyles.COLORS['bg_main'])
        self.window.resizable(True, True)
        self.window.state('zoomed')
        
        # Center window
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # Configure styles
        ModernStyles.configure_ttk_styles(self.window)
    
    def create_interface(self):
        """Create the BI dashboard interface"""
        # Main container
        main_container = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        main_container.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Header with real-time indicators
        header_frame = tk.Frame(main_container, bg=ModernStyles.COLORS['bg_main'])
        header_frame.pack(fill='x', pady=(0, 20))
        
        title_label = tk.Label(header_frame, text="📊 Расширенная Бизнес-Аналитика",
                              font=('Cambria', 24, 'bold italic'),
                              fg=ModernStyles.COLORS['text_primary'],
                              bg=ModernStyles.COLORS['bg_main'])
        title_label.pack(side='left')
        
        # Real-time status indicators
        self.status_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['bg_main'])
        self.status_frame.pack(side='right')
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_container)
        self.notebook.pack(fill='both', expand=True)
        
        # Create tabs
        self.create_executive_dashboard_tab()
        self.create_predictive_analytics_tab()
        self.create_competitor_analysis_tab()
        self.create_strategic_insights_tab()
        self.create_alerts_monitoring_tab()
        self.create_ml_models_tab()

    def create_executive_dashboard_tab(self):
        """Create executive dashboard tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="🎯 Исполнительная Панель")

        # Executive summary
        exec_summary_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        exec_summary_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(exec_summary_frame, text="🎯 Исполнительная Панель",
                font=('Cambria', 16, 'bold'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_secondary']).pack(pady=15)

        # Key performance indicators
        kpi_container = tk.Frame(exec_summary_frame, bg=ModernStyles.COLORS['bg_secondary'])
        kpi_container.pack(pady=(0, 15))

        # Create KPI cards
        self.create_kpi_card(kpi_container, "💰 Выручка", "2,450,000 руб", "+12.5%", ModernStyles.COLORS['success'], 0, 0)
        self.create_kpi_card(kpi_container, "📈 Прибыль", "735,000 руб", "+8.3%", ModernStyles.COLORS['primary'], 0, 1)
        self.create_kpi_card(kpi_container, "👥 Клиенты", "15,678", "+5.2%", ModernStyles.COLORS['info'], 0, 2)
        self.create_kpi_card(kpi_container, "⭐ Рейтинг", "4.7/5.0", "+0.2", ModernStyles.COLORS['warning'], 0, 3)

        # Configure grid weights
        for i in range(4):
            kpi_container.columnconfigure(i, weight=1)

        # Charts and analytics area
        analytics_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        analytics_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Revenue trend chart (placeholder)
        revenue_chart_frame = tk.LabelFrame(analytics_frame, text="📊 Тренд Выручки (12 месяцев)",
                                          font=('Cambria', 14, 'bold'),
                                          fg=ModernStyles.COLORS['text_primary'],
                                          bg=ModernStyles.COLORS['bg_secondary'])
        revenue_chart_frame.pack(fill='both', expand=True, pady=(0, 10))

        # Sample chart data visualization
        chart_text = tk.Text(revenue_chart_frame, height=8, font=('Cambria', 11),
                           bg=ModernStyles.COLORS['bg_main'],
                           fg=ModernStyles.COLORS['text_primary'],
                           wrap='word', state='disabled')
        chart_text.pack(fill='both', expand=True, padx=15, pady=15)

        chart_data = """📈 АНАЛИЗ ТРЕНДОВ ВЫРУЧКИ

Янв 2024: 2,100,000 руб ████████████████████████████████████████
Фев 2024: 2,250,000 руб ███████████████████████████████████████████
Мар 2024: 2,180,000 руб █████████████████████████████████████████
Апр 2024: 2,320,000 руб ████████████████████████████████████████████
Май 2024: 2,450,000 руб ██████████████████████████████████████████████
Июн 2024: 2,380,000 руб █████████████████████████████████████████████

🔍 КЛЮЧЕВЫЕ НАБЛЮДЕНИЯ:
• Устойчивый рост выручки на 12.5% за последние 6 месяцев
• Пиковые показатели в мае (2,450,000 руб)
• Сезонное снижение в июне (-2.9%)
• Прогноз на июль: 2,520,000 руб (+5.9%)

📊 ФАКТОРЫ РОСТА:
• Новые блюда в меню (+15% к среднему чеку)
• Программа лояльности (+8% удержание клиентов)
• Маркетинговые кампании (+12% новых клиентов)
• Оптимизация операций (-5% операционные расходы)
"""

        chart_text.config(state='normal')
        chart_text.insert('1.0', chart_data)
        chart_text.config(state='disabled')

        # Quick actions
        actions_frame = tk.Frame(analytics_frame, bg=ModernStyles.COLORS['bg_main'])
        actions_frame.pack(fill='x', pady=(10, 0))

        tk.Button(actions_frame, text="📊 Детальный Отчет",
                 command=self.generate_executive_report,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(actions_frame, text="📈 Прогноз на Квартал",
                 command=self.generate_quarterly_forecast,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(actions_frame, text="🎯 Стратегические Цели",
                 command=self.show_strategic_goals,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

    def create_kpi_card(self, parent, title, value, change, color, row, col):
        """Create a KPI card"""
        card_frame = tk.Frame(parent, bg=color, relief='raised', bd=3)
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky='nsew')

        tk.Label(card_frame, text=title, font=('Cambria', 12, 'bold'),
                fg='white', bg=color).pack(pady=(15, 5))

        tk.Label(card_frame, text=value, font=('Cambria', 18, 'bold'),
                fg='white', bg=color).pack(pady=(0, 5))

        change_color = ModernStyles.COLORS['success'] if change.startswith('+') else ModernStyles.COLORS['danger']
        tk.Label(card_frame, text=change, font=('Cambria', 12, 'bold'),
                fg='white', bg=color).pack(pady=(0, 15))

    def create_predictive_analytics_tab(self):
        """Create predictive analytics tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="🔮 Прогнозная Аналитика")

        # Prediction controls
        prediction_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        prediction_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(prediction_controls_frame, text="🔮 Прогноз Выручки",
                 command=self.generate_revenue_forecast,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(prediction_controls_frame, text="📊 Прогноз Спроса",
                 command=self.generate_demand_forecast,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(prediction_controls_frame, text="👥 Анализ Оттока",
                 command=self.analyze_customer_churn,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Predictions display
        predictions_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        predictions_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Predictions treeview
        prediction_columns = ('model', 'forecast_period', 'predicted_value', 'confidence', 'accuracy', 'last_updated')
        self.predictions_tree = ttk.Treeview(predictions_frame, columns=prediction_columns, show='headings', height=15)

        # Configure prediction columns
        self.predictions_tree.heading('model', text='Модель')
        self.predictions_tree.heading('forecast_period', text='Период Прогноза')
        self.predictions_tree.heading('predicted_value', text='Прогнозное Значение')
        self.predictions_tree.heading('confidence', text='Доверительный Интервал')
        self.predictions_tree.heading('accuracy', text='Точность (%)')
        self.predictions_tree.heading('last_updated', text='Обновлено')

        self.predictions_tree.column('model', width=200)
        self.predictions_tree.column('forecast_period', width=150)
        self.predictions_tree.column('predicted_value', width=180)
        self.predictions_tree.column('confidence', width=150)
        self.predictions_tree.column('accuracy', width=120)
        self.predictions_tree.column('last_updated', width=150)

        # Predictions scrollbar
        predictions_scrollbar = ttk.Scrollbar(predictions_frame, orient='vertical', command=self.predictions_tree.yview)
        self.predictions_tree.configure(yscrollcommand=predictions_scrollbar.set)

        self.predictions_tree.pack(side='left', fill='both', expand=True)
        predictions_scrollbar.pack(side='right', fill='y')

    def create_competitor_analysis_tab(self):
        """Create competitor analysis tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="🏆 Анализ Конкурентов")

        # Competitor controls
        competitor_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        competitor_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(competitor_controls_frame, text="➕ Добавить Конкурента",
                 command=self.add_competitor,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(competitor_controls_frame, text="📊 Анализ Цен",
                 command=self.analyze_competitor_pricing,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(competitor_controls_frame, text="⭐ Анализ Отзывов",
                 command=self.analyze_competitor_reviews,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Competitor analysis display
        competitor_analysis_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        competitor_analysis_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Competitor analysis treeview
        competitor_columns = ('competitor', 'category', 'metric', 'their_value', 'our_value', 'variance', 'position', 'last_updated')
        self.competitor_tree = ttk.Treeview(competitor_analysis_frame, columns=competitor_columns, show='headings', height=12)

        # Configure competitor columns
        self.competitor_tree.heading('competitor', text='Конкурент')
        self.competitor_tree.heading('category', text='Категория')
        self.competitor_tree.heading('metric', text='Показатель')
        self.competitor_tree.heading('their_value', text='Их Значение')
        self.competitor_tree.heading('our_value', text='Наше Значение')
        self.competitor_tree.heading('variance', text='Отклонение (%)')
        self.competitor_tree.heading('position', text='Позиция')
        self.competitor_tree.heading('last_updated', text='Обновлено')

        self.competitor_tree.column('competitor', width=150)
        self.competitor_tree.column('category', width=120)
        self.competitor_tree.column('metric', width=150)
        self.competitor_tree.column('their_value', width=120)
        self.competitor_tree.column('our_value', width=120)
        self.competitor_tree.column('variance', width=100)
        self.competitor_tree.column('position', width=100)
        self.competitor_tree.column('last_updated', width=120)

        # Competitor scrollbar
        competitor_scrollbar = ttk.Scrollbar(competitor_analysis_frame, orient='vertical', command=self.competitor_tree.yview)
        self.competitor_tree.configure(yscrollcommand=competitor_scrollbar.set)

        self.competitor_tree.pack(side='left', fill='both', expand=True)
        competitor_scrollbar.pack(side='right', fill='y')

    def create_strategic_insights_tab(self):
        """Create strategic insights tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="💡 Стратегические Инсайты")

        # Insights controls
        insights_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        insights_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(insights_controls_frame, text="🔍 Генерировать Инсайты",
                 command=self.generate_strategic_insights,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(insights_controls_frame, text="📋 План Действий",
                 command=self.create_action_plan,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(insights_controls_frame, text="🎯 Приоритизация",
                 command=self.prioritize_insights,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Strategic insights display
        insights_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        insights_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Strategic insights treeview
        insights_columns = ('title', 'category', 'impact', 'priority', 'status', 'assigned_to', 'due_date', 'progress')
        self.insights_tree = ttk.Treeview(insights_frame, columns=insights_columns, show='headings', height=12)

        # Configure insights columns
        self.insights_tree.heading('title', text='Инсайт')
        self.insights_tree.heading('category', text='Категория')
        self.insights_tree.heading('impact', text='Влияние')
        self.insights_tree.heading('priority', text='Приоритет')
        self.insights_tree.heading('status', text='Статус')
        self.insights_tree.heading('assigned_to', text='Ответственный')
        self.insights_tree.heading('due_date', text='Срок')
        self.insights_tree.heading('progress', text='Прогресс')

        self.insights_tree.column('title', width=250)
        self.insights_tree.column('category', width=120)
        self.insights_tree.column('impact', width=100)
        self.insights_tree.column('priority', width=100)
        self.insights_tree.column('status', width=120)
        self.insights_tree.column('assigned_to', width=150)
        self.insights_tree.column('due_date', width=100)
        self.insights_tree.column('progress', width=100)

        # Insights scrollbar
        insights_scrollbar = ttk.Scrollbar(insights_frame, orient='vertical', command=self.insights_tree.yview)
        self.insights_tree.configure(yscrollcommand=insights_scrollbar.set)

        self.insights_tree.pack(side='left', fill='both', expand=True)
        insights_scrollbar.pack(side='right', fill='y')

    def create_alerts_monitoring_tab(self):
        """Create alerts and monitoring tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="🚨 Мониторинг и Алерты")

        # Alert controls
        alert_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        alert_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(alert_controls_frame, text="⚙️ Настроить Алерты",
                 command=self.configure_alerts,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(alert_controls_frame, text="✅ Подтвердить Все",
                 command=self.acknowledge_all_alerts,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Alert severity filter
        tk.Label(alert_controls_frame, text="Уровень:",
                font=('Cambria', 12, 'bold'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_secondary']).pack(side='left', padx=(20, 5))

        self.alert_filter = ttk.Combobox(alert_controls_frame,
                                        values=['Все', 'Критический', 'Высокий', 'Средний', 'Низкий'],
                                        font=('Cambria', 11), width=12, state='readonly')
        self.alert_filter.set('Все')
        self.alert_filter.pack(side='left', padx=5)

        # Alerts display
        alerts_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        alerts_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Alerts treeview
        alert_columns = ('severity', 'title', 'message', 'metric', 'threshold', 'actual', 'alert_time', 'status')
        self.alerts_tree = ttk.Treeview(alerts_frame, columns=alert_columns, show='headings', height=12)

        # Configure alert columns
        self.alerts_tree.heading('severity', text='Уровень')
        self.alerts_tree.heading('title', text='Заголовок')
        self.alerts_tree.heading('message', text='Сообщение')
        self.alerts_tree.heading('metric', text='Показатель')
        self.alerts_tree.heading('threshold', text='Порог')
        self.alerts_tree.heading('actual', text='Фактическое')
        self.alerts_tree.heading('alert_time', text='Время')
        self.alerts_tree.heading('status', text='Статус')

        self.alerts_tree.column('severity', width=100)
        self.alerts_tree.column('title', width=200)
        self.alerts_tree.column('message', width=250)
        self.alerts_tree.column('metric', width=120)
        self.alerts_tree.column('threshold', width=100)
        self.alerts_tree.column('actual', width=100)
        self.alerts_tree.column('alert_time', width=120)
        self.alerts_tree.column('status', width=100)

        # Alerts scrollbar
        alerts_scrollbar = ttk.Scrollbar(alerts_frame, orient='vertical', command=self.alerts_tree.yview)
        self.alerts_tree.configure(yscrollcommand=alerts_scrollbar.set)

        self.alerts_tree.pack(side='left', fill='both', expand=True)
        alerts_scrollbar.pack(side='right', fill='y')

    def create_ml_models_tab(self):
        """Create machine learning models tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="🤖 ML Модели")

        # ML model controls
        ml_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        ml_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(ml_controls_frame, text="🔄 Обучить Модели",
                 command=self.train_ml_models,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(ml_controls_frame, text="📊 Оценить Точность",
                 command=self.evaluate_model_accuracy,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(ml_controls_frame, text="⚙️ Настроить Параметры",
                 command=self.configure_ml_parameters,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # ML models display
        ml_models_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        ml_models_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # ML models treeview
        ml_columns = ('model_name', 'model_type', 'accuracy', 'last_trained', 'training_period', 'status', 'predictions')
        self.ml_models_tree = ttk.Treeview(ml_models_frame, columns=ml_columns, show='headings', height=12)

        # Configure ML model columns
        self.ml_models_tree.heading('model_name', text='Название Модели')
        self.ml_models_tree.heading('model_type', text='Тип Модели')
        self.ml_models_tree.heading('accuracy', text='Точность (%)')
        self.ml_models_tree.heading('last_trained', text='Последнее Обучение')
        self.ml_models_tree.heading('training_period', text='Период Обучения')
        self.ml_models_tree.heading('status', text='Статус')
        self.ml_models_tree.heading('predictions', text='Прогнозов')

        self.ml_models_tree.column('model_name', width=200)
        self.ml_models_tree.column('model_type', width=150)
        self.ml_models_tree.column('accuracy', width=100)
        self.ml_models_tree.column('last_trained', width=150)
        self.ml_models_tree.column('training_period', width=150)
        self.ml_models_tree.column('status', width=100)
        self.ml_models_tree.column('predictions', width=100)

        # ML models scrollbar
        ml_models_scrollbar = ttk.Scrollbar(ml_models_frame, orient='vertical', command=self.ml_models_tree.yview)
        self.ml_models_tree.configure(yscrollcommand=ml_models_scrollbar.set)

        self.ml_models_tree.pack(side='left', fill='both', expand=True)
        ml_models_scrollbar.pack(side='right', fill='y')

    # Data loading methods
    def load_bi_data(self):
        """Load business intelligence data"""
        try:
            self.load_predictions_data()
            self.load_competitor_data()
            self.load_strategic_insights_data()
            self.load_alerts_data()
            self.load_ml_models_data()
            self.update_status_indicators()
        except Exception as e:
            print(f"Error loading BI data: {e}")

    def load_predictions_data(self):
        """Load predictions data"""
        try:
            # Clear existing data
            for item in self.predictions_tree.get_children():
                self.predictions_tree.delete(item)

            # Sample predictions data
            predictions = [
                ("Прогноз Выручки", "Июль 2024", "2,520,000 руб", "±125,000", "94.2%", "15.06.2024"),
                ("Прогноз Спроса", "Выходные", "450 заказов", "±35", "89.7%", "14.06.2024"),
                ("Отток Клиентов", "Следующий месяц", "12.3%", "±2.1%", "87.5%", "13.06.2024"),
                ("Оптимизация Запасов", "Неделя", "Сократить на 15%", "±3%", "91.8%", "12.06.2024"),
                ("Планирование Персонала", "Пиковые часы", "18 сотрудников", "±2", "93.1%", "11.06.2024")
            ]

            for prediction in predictions:
                self.predictions_tree.insert('', 'end', values=prediction)

        except Exception as e:
            print(f"Error loading predictions data: {e}")

    def load_competitor_data(self):
        """Load competitor analysis data"""
        try:
            # Clear existing data
            for item in self.competitor_tree.get_children():
                self.competitor_tree.delete(item)

            # Sample competitor data
            competitors = [
                ("Ресторан А", "Ценообразование", "Средний чек", "1,850 руб", "2,100 руб", "+13.5%", "Лидер", "10.06.2024"),
                ("Ресторан Б", "Обслуживание", "Время подачи", "18 мин", "15 мин", "-16.7%", "Лидер", "09.06.2024"),
                ("Ресторан В", "Отзывы", "Рейтинг", "4.3/5", "4.7/5", "+9.3%", "Лидер", "08.06.2024"),
                ("Ресторан Г", "Меню", "Количество блюд", "85", "120", "+41.2%", "Лидер", "07.06.2024")
            ]

            for competitor in competitors:
                self.competitor_tree.insert('', 'end', values=competitor)

        except Exception as e:
            print(f"Error loading competitor data: {e}")

    def load_strategic_insights_data(self):
        """Load strategic insights data"""
        try:
            # Clear existing data
            for item in self.insights_tree.get_children():
                self.insights_tree.delete(item)

            # Sample insights data
            insights = [
                ("Оптимизация меню", "Операционная", "Высокое", "9/10", "В работе", "Шеф-повар", "30.06.2024", "65%"),
                ("Программа лояльности", "Маркетинг", "Высокое", "8/10", "Завершено", "Менеджер", "15.06.2024", "100%"),
                ("Автоматизация заказов", "Технологии", "Среднее", "7/10", "Планирование", "IT-отдел", "31.07.2024", "15%"),
                ("Обучение персонала", "HR", "Среднее", "6/10", "В работе", "HR-менеджер", "20.06.2024", "40%")
            ]

            for insight in insights:
                self.insights_tree.insert('', 'end', values=insight)

        except Exception as e:
            print(f"Error loading strategic insights data: {e}")

    def load_alerts_data(self):
        """Load alerts data"""
        try:
            # Clear existing data
            for item in self.alerts_tree.get_children():
                self.alerts_tree.delete(item)

            # Sample alerts data
            alerts = [
                ("Критический", "Падение выручки", "Выручка снизилась на 15%", "Выручка", "2,000,000", "1,700,000", "15.06 14:30", "Новый"),
                ("Высокий", "Рост затрат", "Затраты выросли на 12%", "Затраты", "800,000", "896,000", "15.06 12:15", "Новый"),
                ("Средний", "Низкий рейтинг", "Рейтинг упал до 4.2", "Рейтинг", "4.5", "4.2", "15.06 10:45", "Подтвержден"),
                ("Низкий", "Нехватка запасов", "Заканчиваются ингредиенты", "Запасы", "100", "15", "15.06 09:20", "В работе")
            ]

            for alert in alerts:
                self.alerts_tree.insert('', 'end', values=alert)

        except Exception as e:
            print(f"Error loading alerts data: {e}")

    def load_ml_models_data(self):
        """Load ML models data"""
        try:
            # Clear existing data
            for item in self.ml_models_tree.get_children():
                self.ml_models_tree.delete(item)

            # Sample ML models data
            models = [
                ("Прогноз Выручки", "Временные ряды", "94.2%", "10.06.2024", "01.01-31.05.2024", "Активна", "156"),
                ("Сегментация Клиентов", "Кластеризация", "87.8%", "08.06.2024", "01.01-31.05.2024", "Активна", "89"),
                ("Прогноз Спроса", "Регрессия", "89.7%", "09.06.2024", "01.03-31.05.2024", "Активна", "234"),
                ("Детекция Аномалий", "Классификация", "91.3%", "07.06.2024", "01.02-31.05.2024", "Активна", "67"),
                ("Оптимизация Цен", "Оптимизация", "85.6%", "06.06.2024", "01.01-30.04.2024", "Обучение", "45")
            ]

            for model in models:
                self.ml_models_tree.insert('', 'end', values=model)

        except Exception as e:
            print(f"Error loading ML models data: {e}")

    def update_status_indicators(self):
        """Update real-time status indicators"""
        try:
            # Clear existing indicators
            for widget in self.status_frame.winfo_children():
                widget.destroy()

            # Create status indicators
            indicators = [
                ("🟢", "Система работает"),
                ("📊", "Модели обновлены"),
                ("🔄", "Синхронизация: 2 мин назад"),
                ("⚠️", "4 новых алерта")
            ]

            for i, (icon, text) in enumerate(indicators):
                indicator_frame = tk.Frame(self.status_frame, bg=ModernStyles.COLORS['bg_main'])
                indicator_frame.grid(row=0, column=i, padx=5)

                tk.Label(indicator_frame, text=icon, font=('Cambria', 12),
                        bg=ModernStyles.COLORS['bg_main']).pack(side='left')

                tk.Label(indicator_frame, text=text, font=('Cambria', 10),
                        fg=ModernStyles.COLORS['text_secondary'],
                        bg=ModernStyles.COLORS['bg_main']).pack(side='left', padx=(2, 0))

        except Exception as e:
            print(f"Error updating status indicators: {e}")

    # Placeholder action methods (to be implemented)
    def generate_executive_report(self):
        """Generate executive report"""
        messagebox.showinfo("Отчет", "Генерация исполнительного отчета")

    def generate_quarterly_forecast(self):
        """Generate quarterly forecast"""
        messagebox.showinfo("Прогноз", "Генерация квартального прогноза")

    def show_strategic_goals(self):
        """Show strategic goals"""
        messagebox.showinfo("Цели", "Отображение стратегических целей")

    def generate_revenue_forecast(self):
        """Generate revenue forecast"""
        messagebox.showinfo("Прогноз", "Генерация прогноза выручки")

    def generate_demand_forecast(self):
        """Generate demand forecast"""
        messagebox.showinfo("Спрос", "Генерация прогноза спроса")

    def analyze_customer_churn(self):
        """Analyze customer churn"""
        messagebox.showinfo("Отток", "Анализ оттока клиентов")

    def add_competitor(self):
        """Add new competitor"""
        messagebox.showinfo("Конкурент", "Добавление нового конкурента")

    def analyze_competitor_pricing(self):
        """Analyze competitor pricing"""
        messagebox.showinfo("Цены", "Анализ цен конкурентов")

    def analyze_competitor_reviews(self):
        """Analyze competitor reviews"""
        messagebox.showinfo("Отзывы", "Анализ отзывов конкурентов")

    def generate_strategic_insights(self):
        """Generate strategic insights"""
        messagebox.showinfo("Инсайты", "Генерация стратегических инсайтов")

    def create_action_plan(self):
        """Create action plan"""
        messagebox.showinfo("План", "Создание плана действий")

    def prioritize_insights(self):
        """Prioritize insights"""
        messagebox.showinfo("Приоритизация", "Приоритизация инсайтов")

    def configure_alerts(self):
        """Configure alerts"""
        messagebox.showinfo("Настройка", "Настройка алертов")

    def acknowledge_all_alerts(self):
        """Acknowledge all alerts"""
        messagebox.showinfo("Подтверждение", "Подтверждение всех алертов")

    def train_ml_models(self):
        """Train ML models"""
        messagebox.showinfo("Обучение", "Обучение ML моделей")

    def evaluate_model_accuracy(self):
        """Evaluate model accuracy"""
        messagebox.showinfo("Оценка", "Оценка точности моделей")

    def configure_ml_parameters(self):
        """Configure ML parameters"""
        messagebox.showinfo("Параметры", "Настройка параметров ML")

def create_advanced_business_intelligence(parent, db_manager):
    """Create and show the advanced business intelligence dashboard"""
    try:
        bi_system = AdvancedBusinessIntelligence(parent, db_manager)
        bi_system.show_bi_dashboard()
        return bi_system
    except Exception as e:
        messagebox.showerror("Ошибка", f"Не удалось открыть систему бизнес-аналитики: {e}")
        return None
