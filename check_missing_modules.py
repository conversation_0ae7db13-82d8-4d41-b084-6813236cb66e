#!/usr/bin/env python3
"""
Скрипт для проверки отсутствующих модулей в системе управления рестораном
"""

import os
import re

def check_missing_modules():
    """Проверить какие модули отсутствуют"""
    
    # Читаем main_window.py
    with open('gui/main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Находим все импорты модулей
    import_pattern = r'from modules\.([a-zA-Z_]+) import'
    imports = re.findall(import_pattern, content)
    
    # Получаем список существующих модулей
    modules_dir = 'modules'
    existing_modules = []
    if os.path.exists(modules_dir):
        for file in os.listdir(modules_dir):
            if file.endswith('.py') and not file.startswith('__'):
                existing_modules.append(file[:-3])  # убираем .py
    
    # Проверяем какие модули отсутствуют
    missing_modules = []
    for module in set(imports):
        if module not in existing_modules:
            missing_modules.append(module)
    
    print("🔍 ПРОВЕРКА МОДУЛЕЙ СИСТЕМЫ УПРАВЛЕНИЯ РЕСТОРАНОМ")
    print("=" * 60)
    
    print(f"\n📊 СТАТИСТИКА:")
    print(f"   Всего импортов модулей: {len(set(imports))}")
    print(f"   Существующих модулей: {len(existing_modules)}")
    print(f"   Отсутствующих модулей: {len(missing_modules)}")
    
    if missing_modules:
        print(f"\n❌ ОТСУТСТВУЮЩИЕ МОДУЛИ ({len(missing_modules)}):")
        for i, module in enumerate(sorted(missing_modules), 1):
            print(f"   {i:2d}. {module}")
    else:
        print(f"\n✅ ВСЕ МОДУЛИ НАЙДЕНЫ!")
    
    print(f"\n📁 СУЩЕСТВУЮЩИЕ МОДУЛИ ({len(existing_modules)}):")
    for i, module in enumerate(sorted(existing_modules), 1):
        status = "✅" if module in imports else "⚠️"
        print(f"   {i:2d}. {status} {module}")
    
    # Находим методы show_ которые могут не работать
    show_methods = re.findall(r'def (show_[a-zA-Z_]+)', content)
    
    print(f"\n🔧 МЕТОДЫ НАВИГАЦИИ ({len(show_methods)}):")
    for i, method in enumerate(sorted(show_methods), 1):
        print(f"   {i:2d}. {method}")
    
    return missing_modules, existing_modules

if __name__ == "__main__":
    missing, existing = check_missing_modules()
    
    if missing:
        print(f"\n🚨 НАЙДЕНО {len(missing)} ОТСУТСТВУЮЩИХ МОДУЛЕЙ!")
        print("   Эти кнопки в главном меню не будут работать.")
    else:
        print(f"\n🎉 ВСЕ МОДУЛИ НА МЕСТЕ!")
