"""
Table Reservation and Queue Management System
Create table reservation system with online booking, waitlist management,
table optimization, customer notifications, and integration with POS systems.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from datetime import datetime, timedelta
import json
import uuid
import random
from gui.styles import ModernStyles

class TableReservationQueueManagement:
    """Table Reservation and Queue Management System"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.notebook = None
        
        # Table configuration
        self.table_types = {
            "small": {"name": "Малый стол", "capacity": 2, "icon": "🪑", "color": ModernStyles.COLORS['info']},
            "medium": {"name": "Средний стол", "capacity": 4, "icon": "🍽️", "color": ModernStyles.COLORS['primary']},
            "large": {"name": "Большой стол", "capacity": 6, "icon": "🏛️", "color": ModernStyles.COLORS['success']},
            "vip": {"name": "VIP стол", "capacity": 8, "icon": "👑", "color": ModernStyles.COLORS['warning']}
        }
        
        # Reservation statuses
        self.reservation_statuses = {
            "confirmed": {"name": "Подтверждено", "color": ModernStyles.COLORS['success']},
            "pending": {"name": "Ожидает", "color": ModernStyles.COLORS['warning']},
            "cancelled": {"name": "Отменено", "color": ModernStyles.COLORS['danger']},
            "completed": {"name": "Завершено", "color": ModernStyles.COLORS['info']},
            "no_show": {"name": "Не явился", "color": ModernStyles.COLORS['secondary']}
        }
        
        # Queue priorities
        self.queue_priorities = {
            "high": {"name": "Высокий", "color": ModernStyles.COLORS['danger'], "wait_multiplier": 0.7},
            "normal": {"name": "Обычный", "color": ModernStyles.COLORS['primary'], "wait_multiplier": 1.0},
            "low": {"name": "Низкий", "color": ModernStyles.COLORS['secondary'], "wait_multiplier": 1.3}
        }
        
        # Time slots (15-minute intervals)
        self.time_slots = []
        for hour in range(10, 23):  # 10:00 to 22:45
            for minute in [0, 15, 30, 45]:
                self.time_slots.append(f"{hour:02d}:{minute:02d}")
        
        # Notification settings
        self.notification_settings = {
            "sms_enabled": True,
            "email_enabled": True,
            "reminder_minutes": [60, 30, 15],
            "queue_update_interval": 5
        }
        
        # Initialize reservation database tables
        self._init_reservation_tables()
    
    def _init_reservation_tables(self):
        """Initialize table reservation database tables"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Restaurant tables
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS restaurant_tables (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        table_number TEXT NOT NULL UNIQUE,
                        table_type TEXT NOT NULL,
                        capacity INTEGER NOT NULL,
                        location_area TEXT,
                        is_active BOOLEAN DEFAULT 1,
                        special_features TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Table reservations
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS table_reservations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        reservation_number TEXT NOT NULL UNIQUE,
                        customer_id INTEGER,
                        customer_name TEXT NOT NULL,
                        customer_phone TEXT NOT NULL,
                        customer_email TEXT,
                        table_id INTEGER NOT NULL,
                        reservation_date DATE NOT NULL,
                        reservation_time TIME NOT NULL,
                        party_size INTEGER NOT NULL,
                        duration_minutes INTEGER DEFAULT 120,
                        status TEXT DEFAULT 'pending',
                        special_requests TEXT,
                        created_by INTEGER,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        confirmed_at TIMESTAMP,
                        cancelled_at TIMESTAMP,
                        cancellation_reason TEXT,
                        FOREIGN KEY (customer_id) REFERENCES customers (id),
                        FOREIGN KEY (table_id) REFERENCES restaurant_tables (id),
                        FOREIGN KEY (created_by) REFERENCES users (id)
                    )
                ''')
                
                # Queue management
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS queue_entries (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        queue_number INTEGER NOT NULL,
                        customer_name TEXT NOT NULL,
                        customer_phone TEXT NOT NULL,
                        party_size INTEGER NOT NULL,
                        priority_level TEXT DEFAULT 'normal',
                        estimated_wait_time INTEGER,
                        actual_wait_time INTEGER,
                        queue_status TEXT DEFAULT 'waiting',
                        special_requests TEXT,
                        joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        seated_at TIMESTAMP,
                        left_at TIMESTAMP,
                        table_assigned INTEGER,
                        notes TEXT,
                        FOREIGN KEY (table_assigned) REFERENCES restaurant_tables (id)
                    )
                ''')
                
                # Table availability
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS table_availability (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        table_id INTEGER NOT NULL,
                        availability_date DATE NOT NULL,
                        time_slot TIME NOT NULL,
                        is_available BOOLEAN DEFAULT 1,
                        blocked_reason TEXT,
                        blocked_by INTEGER,
                        blocked_at TIMESTAMP,
                        FOREIGN KEY (table_id) REFERENCES restaurant_tables (id),
                        FOREIGN KEY (blocked_by) REFERENCES users (id),
                        UNIQUE(table_id, availability_date, time_slot)
                    )
                ''')
                
                # Reservation notifications
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS reservation_notifications (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        reservation_id INTEGER NOT NULL,
                        notification_type TEXT NOT NULL,
                        notification_method TEXT NOT NULL,
                        scheduled_time TIMESTAMP NOT NULL,
                        sent_at TIMESTAMP,
                        delivery_status TEXT DEFAULT 'pending',
                        message_content TEXT,
                        error_message TEXT,
                        FOREIGN KEY (reservation_id) REFERENCES table_reservations (id)
                    )
                ''')
                
                # Queue notifications
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS queue_notifications (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        queue_id INTEGER NOT NULL,
                        notification_type TEXT NOT NULL,
                        notification_method TEXT NOT NULL,
                        sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        message_content TEXT,
                        delivery_status TEXT DEFAULT 'sent',
                        FOREIGN KEY (queue_id) REFERENCES queue_entries (id)
                    )
                ''')
                
                conn.commit()
                print("Table reservation database tables initialized successfully")
                
        except Exception as e:
            print(f"Error initializing reservation tables: {e}")
    
    def show_reservation_system(self):
        """Show table reservation and queue management window"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
            
        self.create_window()
        self.create_interface()
        self.load_reservation_data()
    
    def create_window(self):
        """Create the reservation system window"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("🍽️ Система Бронирования и Управления Очередью")
        self.window.geometry("1600x1000")
        self.window.configure(bg=ModernStyles.COLORS['bg_main'])
        self.window.resizable(True, True)
        self.window.state('zoomed')
        
        # Center window
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # Configure styles
        ModernStyles.configure_ttk_styles(self.window)
    
    def create_interface(self):
        """Create the reservation system interface"""
        # Main container
        main_container = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        main_container.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Header with real-time status
        header_frame = tk.Frame(main_container, bg=ModernStyles.COLORS['bg_main'])
        header_frame.pack(fill='x', pady=(0, 20))
        
        title_label = tk.Label(header_frame, text="🍽️ Система Бронирования и Управления Очередью",
                              font=('Cambria', 24, 'bold italic'),
                              fg=ModernStyles.COLORS['text_primary'],
                              bg=ModernStyles.COLORS['bg_main'])
        title_label.pack(side='left')
        
        # Real-time status indicators
        self.status_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['bg_main'])
        self.status_frame.pack(side='right')
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_container)
        self.notebook.pack(fill='both', expand=True)
        
        # Create tabs
        self.create_table_layout_tab()
        self.create_reservations_tab()
        self.create_queue_management_tab()
        self.create_availability_tab()
        self.create_notifications_tab()
        self.create_analytics_tab()

    def create_table_layout_tab(self):
        """Create table layout and management tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="🏛️ Планировка Столов")

        # Table management controls
        table_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        table_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(table_controls_frame, text="➕ Добавить Стол",
                 command=self.add_table,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(table_controls_frame, text="✏️ Редактировать",
                 command=self.edit_table,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(table_controls_frame, text="🔄 Обновить Статус",
                 command=self.update_table_status,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Table layout display
        layout_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        layout_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Tables treeview
        table_columns = ('table_number', 'table_type', 'capacity', 'location', 'status', 'current_reservation', 'next_available')
        self.tables_tree = ttk.Treeview(layout_frame, columns=table_columns, show='headings', height=15)

        # Configure table columns
        self.tables_tree.heading('table_number', text='Номер Стола')
        self.tables_tree.heading('table_type', text='Тип')
        self.tables_tree.heading('capacity', text='Вместимость')
        self.tables_tree.heading('location', text='Расположение')
        self.tables_tree.heading('status', text='Статус')
        self.tables_tree.heading('current_reservation', text='Текущее Бронирование')
        self.tables_tree.heading('next_available', text='Следующий Доступ')

        self.tables_tree.column('table_number', width=120)
        self.tables_tree.column('table_type', width=120)
        self.tables_tree.column('capacity', width=100)
        self.tables_tree.column('location', width=150)
        self.tables_tree.column('status', width=120)
        self.tables_tree.column('current_reservation', width=200)
        self.tables_tree.column('next_available', width=150)

        # Tables scrollbar
        tables_scrollbar = ttk.Scrollbar(layout_frame, orient='vertical', command=self.tables_tree.yview)
        self.tables_tree.configure(yscrollcommand=tables_scrollbar.set)

        self.tables_tree.pack(side='left', fill='both', expand=True)
        tables_scrollbar.pack(side='right', fill='y')

    def create_reservations_tab(self):
        """Create reservations management tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="📅 Бронирования")

        # Reservation controls
        reservation_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        reservation_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(reservation_controls_frame, text="➕ Новое Бронирование",
                 command=self.create_reservation,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(reservation_controls_frame, text="✅ Подтвердить",
                 command=self.confirm_reservation,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(reservation_controls_frame, text="❌ Отменить",
                 command=self.cancel_reservation,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Date filter
        tk.Label(reservation_controls_frame, text="Дата:",
                font=('Cambria', 12, 'bold'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_secondary']).pack(side='left', padx=(20, 5))

        self.reservation_date_filter = tk.Entry(reservation_controls_frame, font=('Cambria', 11), width=12)
        self.reservation_date_filter.pack(side='left', padx=5)
        self.reservation_date_filter.insert(0, datetime.now().strftime("%d.%m.%Y"))

        tk.Button(reservation_controls_frame, text="🔍 Фильтр",
                 command=self.filter_reservations,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 10, 'bold'), relief='flat',
                 padx=15, pady=8).pack(side='left', padx=5)

        # Reservations display
        reservations_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        reservations_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Reservations treeview
        reservation_columns = ('reservation_number', 'customer_name', 'phone', 'date', 'time', 'table', 'party_size', 'status', 'special_requests')
        self.reservations_tree = ttk.Treeview(reservations_frame, columns=reservation_columns, show='headings', height=12)

        # Configure reservation columns
        self.reservations_tree.heading('reservation_number', text='№ Брони')
        self.reservations_tree.heading('customer_name', text='Имя Клиента')
        self.reservations_tree.heading('phone', text='Телефон')
        self.reservations_tree.heading('date', text='Дата')
        self.reservations_tree.heading('time', text='Время')
        self.reservations_tree.heading('table', text='Стол')
        self.reservations_tree.heading('party_size', text='Гостей')
        self.reservations_tree.heading('status', text='Статус')
        self.reservations_tree.heading('special_requests', text='Особые Пожелания')

        self.reservations_tree.column('reservation_number', width=100)
        self.reservations_tree.column('customer_name', width=150)
        self.reservations_tree.column('phone', width=120)
        self.reservations_tree.column('date', width=100)
        self.reservations_tree.column('time', width=80)
        self.reservations_tree.column('table', width=80)
        self.reservations_tree.column('party_size', width=80)
        self.reservations_tree.column('status', width=120)
        self.reservations_tree.column('special_requests', width=200)

        # Reservations scrollbar
        reservations_scrollbar = ttk.Scrollbar(reservations_frame, orient='vertical', command=self.reservations_tree.yview)
        self.reservations_tree.configure(yscrollcommand=reservations_scrollbar.set)

        self.reservations_tree.pack(side='left', fill='both', expand=True)
        reservations_scrollbar.pack(side='right', fill='y')

    def create_queue_management_tab(self):
        """Create queue management tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="⏳ Управление Очередью")

        # Queue controls
        queue_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        queue_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(queue_controls_frame, text="➕ Добавить в Очередь",
                 command=self.add_to_queue,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(queue_controls_frame, text="🪑 Посадить",
                 command=self.seat_customer,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(queue_controls_frame, text="📞 Уведомить",
                 command=self.notify_customer,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(queue_controls_frame, text="❌ Удалить из Очереди",
                 command=self.remove_from_queue,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Queue display
        queue_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        queue_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Queue treeview
        queue_columns = ('queue_number', 'customer_name', 'phone', 'party_size', 'priority', 'wait_time', 'estimated_time', 'status', 'special_requests')
        self.queue_tree = ttk.Treeview(queue_frame, columns=queue_columns, show='headings', height=12)

        # Configure queue columns
        self.queue_tree.heading('queue_number', text='№ в Очереди')
        self.queue_tree.heading('customer_name', text='Имя Клиента')
        self.queue_tree.heading('phone', text='Телефон')
        self.queue_tree.heading('party_size', text='Гостей')
        self.queue_tree.heading('priority', text='Приоритет')
        self.queue_tree.heading('wait_time', text='Ожидание')
        self.queue_tree.heading('estimated_time', text='Оценка')
        self.queue_tree.heading('status', text='Статус')
        self.queue_tree.heading('special_requests', text='Пожелания')

        self.queue_tree.column('queue_number', width=100)
        self.queue_tree.column('customer_name', width=150)
        self.queue_tree.column('phone', width=120)
        self.queue_tree.column('party_size', width=80)
        self.queue_tree.column('priority', width=100)
        self.queue_tree.column('wait_time', width=100)
        self.queue_tree.column('estimated_time', width=100)
        self.queue_tree.column('status', width=120)
        self.queue_tree.column('special_requests', width=180)

        # Queue scrollbar
        queue_scrollbar = ttk.Scrollbar(queue_frame, orient='vertical', command=self.queue_tree.yview)
        self.queue_tree.configure(yscrollcommand=queue_scrollbar.set)

        self.queue_tree.pack(side='left', fill='both', expand=True)
        queue_scrollbar.pack(side='right', fill='y')

    def create_availability_tab(self):
        """Create table availability management tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="📊 Доступность Столов")

        # Availability controls
        availability_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        availability_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(availability_controls_frame, text="🔒 Заблокировать Время",
                 command=self.block_time_slot,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(availability_controls_frame, text="🔓 Разблокировать",
                 command=self.unblock_time_slot,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(availability_controls_frame, text="📅 Календарь Доступности",
                 command=self.show_availability_calendar,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Date selection
        tk.Label(availability_controls_frame, text="Дата:",
                font=('Cambria', 12, 'bold'),
                fg=ModernStyles.COLORS['text_primary'],
                bg=ModernStyles.COLORS['bg_secondary']).pack(side='left', padx=(20, 5))

        self.availability_date = tk.Entry(availability_controls_frame, font=('Cambria', 11), width=12)
        self.availability_date.pack(side='left', padx=5)
        self.availability_date.insert(0, datetime.now().strftime("%d.%m.%Y"))

        # Availability display
        availability_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        availability_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Availability grid (time slots vs tables)
        availability_text = tk.Text(availability_frame, font=('Cambria', 11),
                                  bg=ModernStyles.COLORS['bg_main'],
                                  fg=ModernStyles.COLORS['text_primary'],
                                  wrap='word', state='disabled')
        availability_text.pack(fill='both', expand=True, padx=15, pady=15)

        # Sample availability grid
        availability_data = """📊 ДОСТУПНОСТЬ СТОЛОВ НА СЕГОДНЯ (15.06.2024)

ВРЕМЯ    | СТОЛ 1 | СТОЛ 2 | СТОЛ 3 | СТОЛ 4 | СТОЛ 5 | СТОЛ 6 | VIP 1 | VIP 2
---------|--------|--------|--------|--------|--------|--------|-------|-------
10:00    |   ✅   |   ✅   |   ✅   |   ✅   |   ✅   |   ✅   |   ✅  |   ✅
10:15    |   ✅   |   ✅   |   ✅   |   ✅   |   ✅   |   ✅   |   ✅  |   ✅
10:30    |   ✅   |   ✅   |   ✅   |   ✅   |   ✅   |   ✅   |   ✅  |   ✅
11:00    |   🔴   |   ✅   |   ✅   |   ✅   |   ✅   |   ✅   |   ✅  |   ✅
11:15    |   🔴   |   ✅   |   ✅   |   ✅   |   ✅   |   ✅   |   ✅  |   ✅
11:30    |   🔴   |   ✅   |   ✅   |   ✅   |   ✅   |   ✅   |   ✅  |   ✅
12:00    |   ✅   |   🔴   |   🔴   |   ✅   |   ✅   |   ✅   |   🔴  |   ✅
12:15    |   ✅   |   🔴   |   🔴   |   ✅   |   ✅   |   ✅   |   🔴  |   ✅
12:30    |   ✅   |   🔴   |   🔴   |   ✅   |   ✅   |   ✅   |   🔴  |   ✅
13:00    |   ✅   |   ✅   |   ✅   |   🔴   |   🔴   |   ✅   |   ✅  |   🔴
13:15    |   ✅   |   ✅   |   ✅   |   🔴   |   🔴   |   ✅   |   ✅  |   🔴
13:30    |   ✅   |   ✅   |   ✅   |   🔴   |   🔴   |   ✅   |   ✅  |   🔴

ЛЕГЕНДА:
✅ - Доступен для бронирования
🔴 - Забронирован
🔒 - Заблокирован
⚠️ - Техническое обслуживание

СТАТИСТИКА НА СЕГОДНЯ:
• Всего столов: 8
• Доступных слотов: 156 из 208 (75%)
• Забронированных слотов: 42 (20%)
• Заблокированных слотов: 10 (5%)
• Пиковое время: 19:00-21:00 (90% занятость)
"""

        availability_text.config(state='normal')
        availability_text.insert('1.0', availability_data)
        availability_text.config(state='disabled')

    def create_notifications_tab(self):
        """Create notifications management tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="📱 Уведомления")

        # Notification controls
        notification_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        notification_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(notification_controls_frame, text="📧 Отправить Email",
                 command=self.send_email_notification,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(notification_controls_frame, text="📱 Отправить SMS",
                 command=self.send_sms_notification,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(notification_controls_frame, text="⚙️ Настройки",
                 command=self.configure_notifications,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Notifications display
        notifications_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        notifications_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Notifications treeview
        notification_columns = ('type', 'recipient', 'method', 'message', 'scheduled_time', 'sent_time', 'status', 'error')
        self.notifications_tree = ttk.Treeview(notifications_frame, columns=notification_columns, show='headings', height=12)

        # Configure notification columns
        self.notifications_tree.heading('type', text='Тип')
        self.notifications_tree.heading('recipient', text='Получатель')
        self.notifications_tree.heading('method', text='Способ')
        self.notifications_tree.heading('message', text='Сообщение')
        self.notifications_tree.heading('scheduled_time', text='Запланировано')
        self.notifications_tree.heading('sent_time', text='Отправлено')
        self.notifications_tree.heading('status', text='Статус')
        self.notifications_tree.heading('error', text='Ошибка')

        self.notifications_tree.column('type', width=120)
        self.notifications_tree.column('recipient', width=150)
        self.notifications_tree.column('method', width=100)
        self.notifications_tree.column('message', width=250)
        self.notifications_tree.column('scheduled_time', width=120)
        self.notifications_tree.column('sent_time', width=120)
        self.notifications_tree.column('status', width=100)
        self.notifications_tree.column('error', width=150)

        # Notifications scrollbar
        notifications_scrollbar = ttk.Scrollbar(notifications_frame, orient='vertical', command=self.notifications_tree.yview)
        self.notifications_tree.configure(yscrollcommand=notifications_scrollbar.set)

        self.notifications_tree.pack(side='left', fill='both', expand=True)
        notifications_scrollbar.pack(side='right', fill='y')

    def create_analytics_tab(self):
        """Create reservation analytics tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="📈 Аналитика")

        # Analytics controls
        analytics_controls_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_secondary'])
        analytics_controls_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(analytics_controls_frame, text="📊 Отчет по Загруженности",
                 command=self.generate_occupancy_report,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(analytics_controls_frame, text="📈 Тренды Бронирований",
                 command=self.show_booking_trends,
                 bg=ModernStyles.COLORS['info'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(analytics_controls_frame, text="⏱️ Анализ Времени Ожидания",
                 command=self.analyze_wait_times,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Cambria', 12, 'bold'), relief='flat',
                 padx=20, pady=10).pack(side='left', padx=5)

        # Analytics display
        analytics_frame = tk.Frame(tab_frame, bg=ModernStyles.COLORS['bg_main'])
        analytics_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Analytics text display
        analytics_text = tk.Text(analytics_frame, font=('Cambria', 11),
                               bg=ModernStyles.COLORS['bg_main'],
                               fg=ModernStyles.COLORS['text_primary'],
                               wrap='word', state='disabled')
        analytics_text.pack(fill='both', expand=True, padx=15, pady=15)

        # Sample analytics data
        analytics_data = """📈 АНАЛИТИКА СИСТЕМЫ БРОНИРОВАНИЯ

📊 ОБЩАЯ СТАТИСТИКА (ПОСЛЕДНИЕ 30 ДНЕЙ):
• Всего бронирований: 1,247
• Подтвержденных: 1,156 (92.7%)
• Отмененных: 67 (5.4%)
• Не явились: 24 (1.9%)
• Средняя загруженность: 78.5%

⏰ АНАЛИЗ ВРЕМЕНИ:
• Пиковые часы: 19:00-21:00 (95% загруженность)
• Самое спокойное время: 14:00-16:00 (45% загруженность)
• Среднее время ожидания в очереди: 18 минут
• Максимальное время ожидания: 45 минут

🍽️ ПОПУЛЯРНОСТЬ СТОЛОВ:
• VIP столы: 89% загруженность
• Большие столы (6 мест): 85% загруженность
• Средние столы (4 места): 76% загруженность
• Малые столы (2 места): 68% загруженность

📱 КАНАЛЫ БРОНИРОВАНИЯ:
• Телефонные звонки: 45%
• Онлайн бронирование: 35%
• Мобильное приложение: 15%
• Прямое обращение: 5%

📈 ТРЕНДЫ:
• Рост бронирований на выходные: +15%
• Увеличение отмен в последнюю минуту: +8%
• Рост использования онлайн каналов: +22%
• Улучшение показателя "не явились": -12%

🎯 РЕКОМЕНДАЦИИ:
• Оптимизировать расписание персонала в пиковые часы
• Внедрить систему предоплаты для VIP столов
• Развивать онлайн каналы бронирования
• Улучшить систему напоминаний для снижения no-show
"""

        analytics_text.config(state='normal')
        analytics_text.insert('1.0', analytics_data)
        analytics_text.config(state='disabled')

    # Data loading methods
    def load_reservation_data(self):
        """Load reservation system data"""
        try:
            self.load_tables_data()
            self.load_reservations_data()
            self.load_queue_data()
            self.load_notifications_data()
            self.update_status_indicators()
        except Exception as e:
            print(f"Error loading reservation data: {e}")

    def load_tables_data(self):
        """Load tables data"""
        try:
            # Clear existing data
            for item in self.tables_tree.get_children():
                self.tables_tree.delete(item)

            # Sample tables data
            tables = [
                ("Стол 1", "Малый", "2", "Зал А", "Свободен", "-", "10:00"),
                ("Стол 2", "Средний", "4", "Зал А", "Занят", "Иванов И. (11:00-13:00)", "13:00"),
                ("Стол 3", "Средний", "4", "Зал А", "Забронирован", "Петров П. (13:30-15:30)", "13:30"),
                ("Стол 4", "Большой", "6", "Зал Б", "Свободен", "-", "10:00"),
                ("Стол 5", "Большой", "6", "Зал Б", "Занят", "Сидоров С. (12:00-14:00)", "14:00"),
                ("Стол 6", "Средний", "4", "Терраса", "Свободен", "-", "10:00"),
                ("VIP 1", "VIP", "8", "VIP Зал", "Забронирован", "Корпоратив (18:00-22:00)", "18:00"),
                ("VIP 2", "VIP", "8", "VIP Зал", "Свободен", "-", "10:00")
            ]

            for table in tables:
                self.tables_tree.insert('', 'end', values=table)

        except Exception as e:
            print(f"Error loading tables data: {e}")

    def load_reservations_data(self):
        """Load reservations data"""
        try:
            # Clear existing data
            for item in self.reservations_tree.get_children():
                self.reservations_tree.delete(item)

            # Sample reservations data
            reservations = [
                ("R001", "Иванов Иван", "+7-900-123-4567", "15.06.2024", "11:00", "Стол 2", "4", "Подтверждено", "У окна"),
                ("R002", "Петров Петр", "+7-900-234-5678", "15.06.2024", "13:30", "Стол 3", "4", "Подтверждено", "Детский стульчик"),
                ("R003", "Сидоров Сергей", "+7-900-345-6789", "15.06.2024", "12:00", "Стол 5", "6", "Подтверждено", "День рождения"),
                ("R004", "Корпорация АБВ", "+7-900-456-7890", "15.06.2024", "18:00", "VIP 1", "8", "Подтверждено", "Корпоратив"),
                ("R005", "Козлов Константин", "+7-900-567-8901", "15.06.2024", "19:30", "Стол 4", "6", "Ожидает", "Веганское меню"),
                ("R006", "Морозова Мария", "+7-900-678-9012", "16.06.2024", "12:00", "Стол 1", "2", "Подтверждено", "Романтический ужин")
            ]

            for reservation in reservations:
                self.reservations_tree.insert('', 'end', values=reservation)

        except Exception as e:
            print(f"Error loading reservations data: {e}")

    def load_queue_data(self):
        """Load queue data"""
        try:
            # Clear existing data
            for item in self.queue_tree.get_children():
                self.queue_tree.delete(item)

            # Sample queue data
            queue_entries = [
                ("1", "Волков Владимир", "+7-900-111-2222", "3", "Высокий", "15 мин", "20 мин", "Ожидает", "VIP клиент"),
                ("2", "Лебедева Лариса", "+7-900-222-3333", "2", "Обычный", "8 мин", "15 мин", "Ожидает", "Быстрое обслуживание"),
                ("3", "Орлов Олег", "+7-900-333-4444", "5", "Обычный", "12 мин", "25 мин", "Ожидает", "Большая компания"),
                ("4", "Соколова София", "+7-900-444-5555", "4", "Низкий", "5 мин", "30 мин", "Ожидает", "Не спешит")
            ]

            for entry in queue_entries:
                self.queue_tree.insert('', 'end', values=entry)

        except Exception as e:
            print(f"Error loading queue data: {e}")

    def load_notifications_data(self):
        """Load notifications data"""
        try:
            # Clear existing data
            for item in self.notifications_tree.get_children():
                self.notifications_tree.delete(item)

            # Sample notifications data
            notifications = [
                ("Напоминание", "Иванов И.", "SMS", "Ваше бронирование через 30 мин", "10:30", "10:30", "Отправлено", ""),
                ("Подтверждение", "Петров П.", "Email", "Бронирование подтверждено", "09:15", "09:15", "Доставлено", ""),
                ("Очередь", "Волков В.", "SMS", "Ваш стол готов", "11:45", "-", "Ожидает", ""),
                ("Отмена", "Козлов К.", "Email", "Бронирование отменено", "14:20", "14:20", "Ошибка", "Неверный email")
            ]

            for notification in notifications:
                self.notifications_tree.insert('', 'end', values=notification)

        except Exception as e:
            print(f"Error loading notifications data: {e}")

    def update_status_indicators(self):
        """Update real-time status indicators"""
        try:
            # Clear existing indicators
            for widget in self.status_frame.winfo_children():
                widget.destroy()

            # Create status indicators
            indicators = [
                ("🍽️", "8 столов"),
                ("📅", "6 броней сегодня"),
                ("⏳", "4 в очереди"),
                ("📱", "3 уведомления")
            ]

            for i, (icon, text) in enumerate(indicators):
                indicator_frame = tk.Frame(self.status_frame, bg=ModernStyles.COLORS['bg_main'])
                indicator_frame.grid(row=0, column=i, padx=5)

                tk.Label(indicator_frame, text=icon, font=('Cambria', 12),
                        bg=ModernStyles.COLORS['bg_main']).pack(side='left')

                tk.Label(indicator_frame, text=text, font=('Cambria', 10),
                        fg=ModernStyles.COLORS['text_secondary'],
                        bg=ModernStyles.COLORS['bg_main']).pack(side='left', padx=(2, 0))

        except Exception as e:
            print(f"Error updating status indicators: {e}")

    # Placeholder action methods (to be implemented)
    def add_table(self):
        """Add new table"""
        messagebox.showinfo("Стол", "Добавление нового стола")

    def edit_table(self):
        """Edit table"""
        messagebox.showinfo("Редактирование", "Редактирование стола")

    def update_table_status(self):
        """Update table status"""
        messagebox.showinfo("Статус", "Обновление статуса стола")

    def create_reservation(self):
        """Create new reservation"""
        messagebox.showinfo("Бронирование", "Создание нового бронирования")

    def confirm_reservation(self):
        """Confirm reservation"""
        messagebox.showinfo("Подтверждение", "Подтверждение бронирования")

    def cancel_reservation(self):
        """Cancel reservation"""
        messagebox.showinfo("Отмена", "Отмена бронирования")

    def filter_reservations(self):
        """Filter reservations"""
        messagebox.showinfo("Фильтр", "Фильтрация бронирований")

    def add_to_queue(self):
        """Add customer to queue"""
        messagebox.showinfo("Очередь", "Добавление в очередь")

    def seat_customer(self):
        """Seat customer from queue"""
        messagebox.showinfo("Посадка", "Посадка клиента")

    def notify_customer(self):
        """Notify customer"""
        messagebox.showinfo("Уведомление", "Уведомление клиента")

    def remove_from_queue(self):
        """Remove customer from queue"""
        messagebox.showinfo("Удаление", "Удаление из очереди")

    def block_time_slot(self):
        """Block time slot"""
        messagebox.showinfo("Блокировка", "Блокировка временного слота")

    def unblock_time_slot(self):
        """Unblock time slot"""
        messagebox.showinfo("Разблокировка", "Разблокировка временного слота")

    def show_availability_calendar(self):
        """Show availability calendar"""
        messagebox.showinfo("Календарь", "Календарь доступности")

    def send_email_notification(self):
        """Send email notification"""
        messagebox.showinfo("Email", "Отправка email уведомления")

    def send_sms_notification(self):
        """Send SMS notification"""
        messagebox.showinfo("SMS", "Отправка SMS уведомления")

    def configure_notifications(self):
        """Configure notifications"""
        messagebox.showinfo("Настройки", "Настройка уведомлений")

    def generate_occupancy_report(self):
        """Generate occupancy report"""
        messagebox.showinfo("Отчет", "Отчет по загруженности")

    def show_booking_trends(self):
        """Show booking trends"""
        messagebox.showinfo("Тренды", "Тренды бронирований")

    def analyze_wait_times(self):
        """Analyze wait times"""
        messagebox.showinfo("Анализ", "Анализ времени ожидания")

def create_table_reservation_queue_management(parent, db_manager):
    """Create and show the table reservation and queue management system"""
    try:
        reservation_system = TableReservationQueueManagement(parent, db_manager)
        reservation_system.show_reservation_system()
        return reservation_system
    except Exception as e:
        messagebox.showerror("Ошибка", f"Не удалось открыть систему бронирования: {e}")
        return None
