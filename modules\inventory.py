"""
Inventory Management Module for Restaurant System
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from datetime import datetime
from typing import List, Dict, Any
from gui.styles import ModernStyles

class InventoryManager:
    """Inventory management functionality"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.inventory_data = []
        self.load_inventory_data()
    
    def load_inventory_data(self):
        """Load inventory data from database"""
        self.inventory_data = self.db_manager.get_raw_materials()
    
    def create_inventory_window(self):
        """Create inventory management window"""
        window = tk.Toplevel(self.parent)
        window.title("Inventory Management")
        window.geometry("1200x800")
        window.configure(bg=ModernStyles.COLORS['bg_main'])
        
        # Main container
        main_frame = tk.Frame(window, **ModernStyles.WIDGET_STYLES['frame_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Title and controls
        header_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_main'])
        header_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(header_frame, text="Inventory Management",
                **ModernStyles.WIDGET_STYLES['label_title']).pack(side='left')
        
        # Control buttons
        btn_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['bg_main'])
        btn_frame.pack(side='right')
        
        tk.Button(btn_frame, text="Add Item", command=self.add_inventory_item,
                 **ModernStyles.WIDGET_STYLES['button_success']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Adjust Stock", command=self.adjust_stock,
                 **ModernStyles.WIDGET_STYLES['button_warning']).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="Refresh", command=self.refresh_inventory,
                 **ModernStyles.WIDGET_STYLES['button_primary']).pack(side='left')
        
        # Inventory table
        table_frame = ModernStyles.create_card_frame(main_frame)
        table_frame.pack(fill='both', expand=True)
        
        # Create treeview
        columns = ('ID', 'Name', 'Category', 'Current Stock', 'Unit', 'Min Stock', 'Avg Cost', 'Supplier')
        self.inventory_tree = ttk.Treeview(table_frame, columns=columns, show='headings',
                                          style="Modern.Treeview")
        
        # Configure columns
        column_widths = {'ID': 50, 'Name': 150, 'Category': 100, 'Current Stock': 100,
                        'Unit': 80, 'Min Stock': 100, 'Avg Cost': 100, 'Supplier': 120}
        
        for col in columns:
            self.inventory_tree.heading(col, text=col)
            self.inventory_tree.column(col, width=column_widths.get(col, 100))
        
        # Scrollbars
        v_scroll = ttk.Scrollbar(table_frame, orient='vertical', command=self.inventory_tree.yview)
        h_scroll = ttk.Scrollbar(table_frame, orient='horizontal', command=self.inventory_tree.xview)
        self.inventory_tree.configure(yscrollcommand=v_scroll.set, xscrollcommand=h_scroll.set)
        
        # Pack components
        self.inventory_tree.pack(side='left', fill='both', expand=True)
        v_scroll.pack(side='right', fill='y')
        h_scroll.pack(side='bottom', fill='x')
        
        # Load data
        self.refresh_inventory_display()
        
        return window
    
    def refresh_inventory_display(self):
        """Refresh inventory display"""
        if hasattr(self, 'inventory_tree'):
            # Clear existing data
            for item in self.inventory_tree.get_children():
                self.inventory_tree.delete(item)
            
            # Load fresh data
            self.load_inventory_data()
            
            # Insert data
            for item in self.inventory_data:
                # Color code based on stock levels
                stock_level = item['current_stock']
                min_stock = item['minimum_stock']
                
                tags = []
                if stock_level <= min_stock:
                    tags = ['low_stock']
                elif stock_level <= min_stock * 1.5:
                    tags = ['medium_stock']
                
                self.inventory_tree.insert('', 'end', values=(
                    item['id'],
                    item['name'],
                    item['category'] or '',
                    f"{item['current_stock']:.2f}",
                    item['unit_of_measure'],
                    f"{item['minimum_stock']:.2f}",
                    f"${item['average_cost']:.2f}",
                    item['supplier'] or ''
                ), tags=tags)
            
            # Configure tags for color coding
            self.inventory_tree.tag_configure('low_stock', background='#ffebee', foreground='#c62828')
            self.inventory_tree.tag_configure('medium_stock', background='#fff3e0', foreground='#ef6c00')
    
    def add_inventory_item(self):
        """Add new inventory item"""
        dialog = InventoryItemDialog(self.parent, "Add Inventory Item")
        if dialog.result:
            item_data = dialog.result
            item_id = self.db_manager.insert_raw_material(item_data)
            if item_id:
                messagebox.showinfo("Success", "Inventory item added successfully!")
                self.refresh_inventory_display()
            else:
                messagebox.showerror("Error", "Failed to add inventory item.")
    
    def adjust_stock(self):
        """Adjust stock for selected item"""
        selection = self.inventory_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select an item to adjust stock.")
            return
        
        item = self.inventory_tree.item(selection[0])
        item_id = item['values'][0]
        item_name = item['values'][1]
        current_stock = float(item['values'][3])
        
        # Get new stock level
        new_stock = simpledialog.askfloat(
            "Adjust Stock",
            f"Current stock for '{item_name}': {current_stock}\nEnter new stock level:",
            initialvalue=current_stock
        )
        
        if new_stock is not None:
            reason = simpledialog.askstring(
                "Adjustment Reason",
                "Enter reason for stock adjustment:",
                initialvalue="Manual adjustment"
            )
            
            if self.db_manager.update_stock(item_id, new_stock, reason or "Manual adjustment"):
                messagebox.showinfo("Success", "Stock adjusted successfully!")
                self.refresh_inventory_display()
            else:
                messagebox.showerror("Error", "Failed to adjust stock.")
    
    def refresh_inventory(self):
        """Refresh inventory data"""
        self.refresh_inventory_display()
        messagebox.showinfo("Refreshed", "Inventory data refreshed successfully!")

class InventoryItemDialog:
    """Dialog for adding/editing inventory items"""
    
    def __init__(self, parent, title="Inventory Item"):
        self.result = None
        self.create_dialog(parent, title)
    
    def create_dialog(self, parent, title):
        """Create the dialog window"""
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("600x650")  # Увеличен размер для показа всех полей
        self.dialog.configure(bg=ModernStyles.COLORS['bg_main'])
        self.dialog.transient(parent)
        self.dialog.grab_set()
        self.dialog.resizable(True, True)  # Разрешить изменение размера

        # Центрировать диалог
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (650 // 2)
        self.dialog.geometry(f"600x650+{x}+{y}")
        
        # Main frame
        main_frame = tk.Frame(self.dialog, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Form fields
        self.fields = {}
        field_configs = [
            ('name', 'Item Name*', 'text'),
            ('category', 'Category', 'text'),
            ('unit_of_measure', 'Unit of Measure*', 'text'),
            ('current_stock', 'Current Stock', 'float'),
            ('minimum_stock', 'Minimum Stock', 'float'),
            ('supplier', 'Supplier', 'text')
        ]
        
        for field_key, field_label, field_type in field_configs:
            # Label
            tk.Label(main_frame, text=field_label,
                    **ModernStyles.WIDGET_STYLES['label_body']).pack(anchor='w', pady=(10, 0))
            
            # Entry
            var = tk.StringVar()
            entry = tk.Entry(main_frame, textvariable=var,
                           **ModernStyles.WIDGET_STYLES['entry'])
            entry.pack(fill='x', pady=(0, 5))
            
            self.fields[field_key] = {'var': var, 'type': field_type}
        
        # Buttons
        button_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_main'])
        button_frame.pack(fill='x', pady=(20, 0))
        
        tk.Button(button_frame, text="Save", command=self.save_item,
                 **ModernStyles.WIDGET_STYLES['button_success']).pack(side='right')
        
        tk.Button(button_frame, text="Cancel", command=self.dialog.destroy,
                 **ModernStyles.WIDGET_STYLES['button_secondary']).pack(side='right', padx=(0, 10))
        
        # Focus on first field
        list(self.fields.values())[0]['var'].get()
    
    def save_item(self):
        """Save the inventory item"""
        try:
            # Validate required fields
            if not self.fields['name']['var'].get().strip():
                messagebox.showerror("Error", "Item name is required.")
                return
            
            if not self.fields['unit_of_measure']['var'].get().strip():
                messagebox.showerror("Error", "Unit of measure is required.")
                return
            
            # Collect data
            self.result = {}
            for field_key, field_data in self.fields.items():
                value = field_data['var'].get().strip()
                
                if field_data['type'] == 'float':
                    try:
                        self.result[field_key] = float(value) if value else 0.0
                    except ValueError:
                        messagebox.showerror("Error", f"Invalid numeric value for {field_key}")
                        return
                else:
                    self.result[field_key] = value
            
            self.dialog.destroy()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save item: {str(e)}")
