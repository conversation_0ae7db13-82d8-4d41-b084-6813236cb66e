"""
Универсальный конфигуратор стилей для всего приложения
Позволяет изменять цвет, размер, шрифт и толщину в реальном времени
"""

import tkinter as tk
from tkinter import ttk, colorchooser, messagebox, filedialog
import json
import os
from gui.enhanced_styles import EnhancedStyles

class StyleConfigurator:
    """Универсальный конфигуратор стилей"""
    
    def __init__(self, parent):
        self.parent = parent
        self.window = None
        
        # Текущие настройки стилей
        self.current_styles = {
            'fonts': {
                'small': {'family': 'Cambria', 'size': 16, 'weight': 'bold', 'slant': 'italic'},
                'normal': {'family': 'Cambria', 'size': 18, 'weight': 'bold', 'slant': 'italic'},
                'medium': {'family': 'Cambria', 'size': 20, 'weight': 'bold', 'slant': 'italic'},
                'large': {'family': 'Cambria', 'size': 24, 'weight': 'bold', 'slant': 'italic'},
                'xlarge': {'family': 'Cambria', 'size': 28, 'weight': 'bold', 'slant': 'italic'},
                'title': {'family': 'Cambria', 'size': 32, 'weight': 'bold', 'slant': 'italic'},
                'header': {'family': 'Cambria', 'size': 36, 'weight': 'bold', 'slant': 'italic'},
                'button': {'family': 'Cambria', 'size': 18, 'weight': 'bold', 'slant': 'italic'},
                'menu': {'family': 'Cambria', 'size': 18, 'weight': 'bold', 'slant': 'italic'},
                'table': {'family': 'Cambria', 'size': 16, 'weight': 'bold', 'slant': 'italic'},
                'table_header': {'family': 'Cambria', 'size': 18, 'weight': 'bold', 'slant': 'italic'},
            },
            'colors': {
                'primary': '#2c3e50',
                'secondary': '#34495e',
                'success': '#27ae60',
                'warning': '#f39c12',
                'danger': '#e74c3c',
                'info': '#3498db',
                'light': '#f8f9fa',
                'white': '#ffffff',
                'dark': '#2c3e50',
                'maroon': '#800000',
                'purple': '#8e44ad',
                'teal': '#16a085',
                'text_light': '#ffffff',
                'text_dark': '#800000',
                'bg_main': '#ecf0f1',
                'bg_sidebar': '#2c3e50',
            }
        }
        
        # Доступные шрифты
        self.available_fonts = [
            'Cambria', 'Arial', 'Times New Roman', 'Segoe UI', 'Calibri',
            'Georgia', 'Verdana', 'Tahoma', 'Comic Sans MS', 'Impact',
            'Trebuchet MS', 'Palatino', 'Garamond', 'Book Antiqua'
        ]
        
        # Доступные веса шрифта
        self.font_weights = ['normal', 'bold']
        
        # Доступные наклоны шрифта
        self.font_slants = ['roman', 'italic']
        
        # Загрузить сохранённые настройки
        self.load_styles()
    
    def create_window(self):
        """Создать окно конфигуратора стилей"""
        try:
            from utils.window_utils import create_centered_dialog
            self.window = create_centered_dialog(
                self.parent,
                "🎨 Конфигуратор Стилей",
                width=1450,
                height=950,
                resizable=True
            )
            self.window.configure(bg='white')
        except ImportError:
            # Fallback если utils не доступны
            self.window = tk.Toplevel(self.parent)
            self.window.title("🎨 Конфигуратор Стилей")
            self.window.geometry("1450x950")
            self.window.configure(bg='white')
            self.window.resizable(True, True)

            # Центрировать окно
            self.window.update_idletasks()
            x = (self.window.winfo_screenwidth() // 2) - (1450 // 2)
            y = (self.window.winfo_screenheight() // 2) - (950 // 2)
            self.window.geometry(f"1450x950+{x}+{y}")
        self.window.resizable(True, True)
        
        self.create_interface()
        return self.window
    
    def create_interface(self):
        """Создать интерфейс конфигуратора"""
        # Заголовок
        header_frame = tk.Frame(self.window, bg='#2c3e50', height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="🎨 Универсальный Конфигуратор Стилей",
                font=('Cambria', 24, 'bold italic'), bg='#2c3e50', fg='white').pack(side='left', padx=30, pady=20)
        
        # Кнопки управления
        btn_frame = tk.Frame(header_frame, bg='#2c3e50')
        btn_frame.pack(side='right', padx=30, pady=15)
        
        tk.Button(btn_frame, text="💾 Сохранить", command=self.save_styles,
                 bg='#27ae60', fg='white', font=('Cambria', 14, 'bold'),
                 relief='flat', padx=20, pady=8).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="📁 Загрузить", command=self.load_styles_dialog,
                 bg='#3498db', fg='white', font=('Cambria', 14, 'bold'),
                 relief='flat', padx=20, pady=8).pack(side='left', padx=5)

        tk.Button(btn_frame, text="🎨 Темы", command=self.show_preset_themes,
                 bg='#8e44ad', fg='white', font=('Cambria', 14, 'bold'),
                 relief='flat', padx=20, pady=8).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="🔄 Применить", command=self.apply_styles,
                 bg='#f39c12', fg='white', font=('Cambria', 14, 'bold'),
                 relief='flat', padx=20, pady=8).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="↩️ Сброс", command=self.reset_styles,
                 bg='#e74c3c', fg='white', font=('Cambria', 14, 'bold'),
                 relief='flat', padx=20, pady=8).pack(side='left', padx=5)
        
        # Основной контент
        main_frame = tk.Frame(self.window, bg='white')
        main_frame.pack(fill='both', expand=True)
        
        # Создать вкладки
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Вкладка шрифтов
        fonts_frame = tk.Frame(notebook, bg='white')
        notebook.add(fonts_frame, text="📝 Шрифты")
        self.create_fonts_tab(fonts_frame)
        
        # Вкладка цветов
        colors_frame = tk.Frame(notebook, bg='white')
        notebook.add(colors_frame, text="🎨 Цвета")
        self.create_colors_tab(colors_frame)
        
        # Вкладка предварительного просмотра
        preview_frame = tk.Frame(notebook, bg='white')
        notebook.add(preview_frame, text="👁️ Предварительный просмотр")
        self.create_preview_tab(preview_frame)
    
    def create_fonts_tab(self, parent):
        """Создать вкладку настройки шрифтов"""
        # Заголовок
        tk.Label(parent, text="📝 Настройка Шрифтов",
                font=('Cambria', 20, 'bold'), bg='white', fg='#2c3e50').pack(pady=20)
        
        # Скроллируемая область
        canvas = tk.Canvas(parent, bg='white')
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='white')
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Создать настройки для каждого типа шрифта
        self.font_vars = {}
        
        for font_type, font_config in self.current_styles['fonts'].items():
            self.create_font_control(scrollable_frame, font_type, font_config)
        
        canvas.pack(side="left", fill="both", expand=True, padx=20)
        scrollbar.pack(side="right", fill="y")
    
    def create_font_control(self, parent, font_type, font_config):
        """Создать элементы управления для одного типа шрифта"""
        # Фрейм для одного шрифта
        font_frame = tk.LabelFrame(parent, text=f"🔤 {font_type.upper()}",
                                  font=('Cambria', 14, 'bold'), bg='white', fg='#2c3e50')
        font_frame.pack(fill='x', padx=20, pady=10)
        
        # Сохранить переменные для этого шрифта
        self.font_vars[font_type] = {}
        
        # Строка 1: Семейство шрифта и размер
        row1 = tk.Frame(font_frame, bg='white')
        row1.pack(fill='x', padx=15, pady=10)
        
        tk.Label(row1, text="Шрифт:", font=('Cambria', 12, 'bold'), bg='white').pack(side='left')
        
        self.font_vars[font_type]['family'] = tk.StringVar(value=font_config['family'])
        family_combo = ttk.Combobox(row1, textvariable=self.font_vars[font_type]['family'],
                                   values=self.available_fonts, width=15)
        family_combo.pack(side='left', padx=(10, 30))
        
        tk.Label(row1, text="Размер:", font=('Cambria', 12, 'bold'), bg='white').pack(side='left')
        
        self.font_vars[font_type]['size'] = tk.StringVar(value=str(font_config['size']))
        size_spin = tk.Spinbox(row1, textvariable=self.font_vars[font_type]['size'],
                              from_=8, to=72, width=10, font=('Cambria', 12))
        size_spin.pack(side='left', padx=10)
        
        # Строка 2: Вес и наклон
        row2 = tk.Frame(font_frame, bg='white')
        row2.pack(fill='x', padx=15, pady=(0, 10))
        
        tk.Label(row2, text="Толщина:", font=('Cambria', 12, 'bold'), bg='white').pack(side='left')
        
        self.font_vars[font_type]['weight'] = tk.StringVar(value=font_config['weight'])
        weight_combo = ttk.Combobox(row2, textvariable=self.font_vars[font_type]['weight'],
                                   values=self.font_weights, width=10)
        weight_combo.pack(side='left', padx=(10, 30))
        
        tk.Label(row2, text="Наклон:", font=('Cambria', 12, 'bold'), bg='white').pack(side='left')
        
        self.font_vars[font_type]['slant'] = tk.StringVar(value=font_config['slant'])
        slant_combo = ttk.Combobox(row2, textvariable=self.font_vars[font_type]['slant'],
                                  values=self.font_slants, width=10)
        slant_combo.pack(side='left', padx=10)
        
        # Предварительный просмотр
        preview_text = f"Пример текста {font_type}"
        preview_label = tk.Label(font_frame, text=preview_text, bg='#f8f9fa',
                                relief='solid', bd=1, pady=10)
        preview_label.pack(fill='x', padx=15, pady=(0, 15))
        
        # Обновить предварительный просмотр при изменении
        def update_preview(*args):
            try:
                family = self.font_vars[font_type]['family'].get()
                size = int(self.font_vars[font_type]['size'].get())
                weight = self.font_vars[font_type]['weight'].get()
                slant = self.font_vars[font_type]['slant'].get()
                
                font_tuple = (family, size, f"{weight} {slant}".strip())
                preview_label.configure(font=font_tuple)
            except:
                pass
        
        # Привязать обновление к изменениям
        for var in self.font_vars[font_type].values():
            var.trace('w', update_preview)
        
        # Обновить сразу
        update_preview()
    
    def create_colors_tab(self, parent):
        """Создать вкладку настройки цветов"""
        # Заголовок
        tk.Label(parent, text="🎨 Настройка Цветов",
                font=('Cambria', 20, 'bold'), bg='white', fg='#2c3e50').pack(pady=20)
        
        # Скроллируемая область
        canvas = tk.Canvas(parent, bg='white')
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='white')
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Создать настройки для каждого цвета
        self.color_vars = {}
        
        # Группировать цвета по категориям
        color_groups = {
            'Основные цвета': ['primary', 'secondary', 'success', 'warning', 'danger', 'info'],
            'Фоновые цвета': ['light', 'white', 'dark', 'bg_main', 'bg_sidebar'],
            'Акцентные цвета': ['maroon', 'purple', 'teal'],
            'Цвета текста': ['text_light', 'text_dark']
        }
        
        for group_name, colors in color_groups.items():
            self.create_color_group(scrollable_frame, group_name, colors)
        
        canvas.pack(side="left", fill="both", expand=True, padx=20)
        scrollbar.pack(side="right", fill="y")
    
    def create_color_group(self, parent, group_name, colors):
        """Создать группу цветовых настроек"""
        group_frame = tk.LabelFrame(parent, text=f"🎨 {group_name}",
                                   font=('Cambria', 14, 'bold'), bg='white', fg='#2c3e50')
        group_frame.pack(fill='x', padx=20, pady=10)
        
        # Создать сетку цветов
        colors_grid = tk.Frame(group_frame, bg='white')
        colors_grid.pack(fill='x', padx=15, pady=15)
        
        for i, color_name in enumerate(colors):
            row = i // 2
            col = i % 2
            
            self.create_color_control(colors_grid, color_name, row, col)
    
    def create_color_control(self, parent, color_name, row, col):
        """Создать элемент управления цветом"""
        color_frame = tk.Frame(parent, bg='white')
        color_frame.grid(row=row, column=col, padx=10, pady=5, sticky='w')
        
        # Название цвета
        tk.Label(color_frame, text=f"{color_name}:",
                font=('Cambria', 12, 'bold'), bg='white', width=12, anchor='w').pack(side='left')
        
        # Переменная для цвета
        self.color_vars[color_name] = tk.StringVar(value=self.current_styles['colors'][color_name])
        
        # Поле ввода цвета
        color_entry = tk.Entry(color_frame, textvariable=self.color_vars[color_name],
                              width=10, font=('Cambria', 11))
        color_entry.pack(side='left', padx=5)
        
        # Образец цвета
        color_sample = tk.Label(color_frame, text="   ", width=3,
                               bg=self.current_styles['colors'][color_name],
                               relief='solid', bd=1)
        color_sample.pack(side='left', padx=5)
        
        # Кнопка выбора цвета
        def choose_color():
            color = colorchooser.askcolor(color=self.color_vars[color_name].get())[1]
            if color:
                self.color_vars[color_name].set(color)
                color_sample.configure(bg=color)
        
        tk.Button(color_frame, text="🎨", command=choose_color,
                 font=('Cambria', 10), width=3).pack(side='left', padx=5)
        
        # Обновить образец при изменении
        def update_sample(*args):
            try:
                color = self.color_vars[color_name].get()
                color_sample.configure(bg=color)
            except:
                pass
        
        self.color_vars[color_name].trace('w', update_sample)

    def create_preview_tab(self, parent):
        """Создать вкладку предварительного просмотра"""
        # Заголовок
        tk.Label(parent, text="👁️ Предварительный Просмотр",
                font=('Cambria', 20, 'bold'), bg='white', fg='#2c3e50').pack(pady=20)

        # Кнопка обновления предварительного просмотра
        tk.Button(parent, text="🔄 Обновить Предварительный Просмотр", command=self.update_preview,
                 bg='#3498db', fg='white', font=('Cambria', 14, 'bold'),
                 relief='flat', padx=30, pady=10).pack(pady=10)

        # Область предварительного просмотра
        self.preview_frame = tk.Frame(parent, bg='white', relief='solid', bd=2)
        self.preview_frame.pack(fill='both', expand=True, padx=20, pady=20)

        self.create_preview_content()

    def create_preview_content(self):
        """Создать содержимое предварительного просмотра"""
        # Очистить
        for widget in self.preview_frame.winfo_children():
            widget.destroy()

        # Получить текущие стили
        fonts = self.get_current_font_styles()
        colors = self.get_current_color_styles()

        # Заголовок
        header = tk.Frame(self.preview_frame, bg=colors.get('primary', '#2c3e50'), height=60)
        header.pack(fill='x')
        header.pack_propagate(False)

        tk.Label(header, text="📋 Пример Заголовка Системы",
                font=fonts.get('header', ('Cambria', 36, 'bold italic')),
                bg=colors.get('primary', '#2c3e50'), fg=colors.get('text_light', 'white')).pack(side='left', padx=20, pady=15)

        # Кнопки
        btn_frame = tk.Frame(header, bg=colors.get('primary', '#2c3e50'))
        btn_frame.pack(side='right', padx=20, pady=10)

        tk.Button(btn_frame, text="Кнопка Успеха",
                 bg=colors.get('success', '#27ae60'), fg=colors.get('text_light', 'white'),
                 font=fonts.get('button', ('Cambria', 18, 'bold italic')),
                 relief='flat', padx=20, pady=8).pack(side='left', padx=5)

        tk.Button(btn_frame, text="Кнопка Опасности",
                 bg=colors.get('danger', '#e74c3c'), fg=colors.get('text_light', 'white'),
                 font=fonts.get('button', ('Cambria', 18, 'bold italic')),
                 relief='flat', padx=20, pady=8).pack(side='left', padx=5)

        # Основной контент
        content = tk.Frame(self.preview_frame, bg=colors.get('light', '#f8f9fa'))
        content.pack(fill='both', expand=True, padx=20, pady=20)

        # Заголовок секции
        tk.Label(content, text="📊 Пример Секции",
                font=fonts.get('large', ('Cambria', 24, 'bold italic')),
                bg=colors.get('light', '#f8f9fa'), fg=colors.get('text_dark', '#800000')).pack(pady=15)

        # Карточки
        cards_frame = tk.Frame(content, bg=colors.get('light', '#f8f9fa'))
        cards_frame.pack(fill='x', pady=15)

        for i, (title, value) in enumerate([("Параметр 1", "Значение 1"), ("Параметр 2", "Значение 2")]):
            card = tk.Frame(cards_frame, bg=colors.get('white', 'white'), relief='solid', bd=1)
            card.pack(side='left', fill='both', expand=True, padx=10, pady=10)

            tk.Label(card, text=title,
                    font=fonts.get('small', ('Cambria', 16, 'bold italic')),
                    bg=colors.get('white', 'white'), fg=colors.get('text_dark', '#800000')).pack(pady=(15, 5))

            tk.Label(card, text=value,
                    font=fonts.get('medium', ('Cambria', 20, 'bold italic')),
                    bg=colors.get('white', 'white'), fg=colors.get('text_dark', '#800000')).pack(pady=(0, 15))

        # Пример таблицы
        table_frame = tk.Frame(content, bg=colors.get('white', 'white'))
        table_frame.pack(fill='x', pady=15)

        tk.Label(table_frame, text="📋 Пример Таблицы",
                font=fonts.get('medium', ('Cambria', 20, 'bold italic')),
                bg=colors.get('white', 'white'), fg=colors.get('text_dark', '#800000')).pack(pady=10)

        # Создать простую таблицу
        table_data = [
            ["Элемент 1", "Значение 1", "100₽"],
            ["Элемент 2", "Значение 2", "200₽"],
            ["Элемент 3", "Значение 3", "300₽"]
        ]

        for i, row in enumerate(table_data):
            row_frame = tk.Frame(table_frame, bg=colors.get('white', 'white'))
            row_frame.pack(fill='x', padx=10, pady=2)

            for j, cell in enumerate(row):
                tk.Label(row_frame, text=cell,
                        font=fonts.get('table', ('Cambria', 16, 'bold italic')),
                        bg=colors.get('white', 'white'), fg=colors.get('text_dark', '#800000'),
                        width=15, anchor='w').pack(side='left', padx=5)

    def get_current_font_styles(self):
        """Получить текущие настройки шрифтов"""
        fonts = {}
        for font_type, vars_dict in self.font_vars.items():
            try:
                family = vars_dict['family'].get()
                size = int(vars_dict['size'].get())
                weight = vars_dict['weight'].get()
                slant = vars_dict['slant'].get()

                style_parts = []
                if weight == 'bold':
                    style_parts.append('bold')
                if slant == 'italic':
                    style_parts.append('italic')

                if style_parts:
                    fonts[font_type] = (family, size, ' '.join(style_parts))
                else:
                    fonts[font_type] = (family, size)
            except:
                fonts[font_type] = ('Cambria', 16, 'bold italic')

        return fonts

    def get_current_color_styles(self):
        """Получить текущие настройки цветов"""
        colors = {}
        for color_name, var in self.color_vars.items():
            try:
                colors[color_name] = var.get()
            except:
                colors[color_name] = self.current_styles['colors'][color_name]

        return colors

    def update_preview(self):
        """Обновить предварительный просмотр"""
        self.create_preview_content()
        messagebox.showinfo("Обновлено", "Предварительный просмотр обновлён")

    def apply_styles(self):
        """Применить стили к системе"""
        try:
            # Обновить текущие стили
            for font_type, vars_dict in self.font_vars.items():
                self.current_styles['fonts'][font_type] = {
                    'family': vars_dict['family'].get(),
                    'size': int(vars_dict['size'].get()),
                    'weight': vars_dict['weight'].get(),
                    'slant': vars_dict['slant'].get()
                }

            for color_name, var in self.color_vars.items():
                self.current_styles['colors'][color_name] = var.get()

            # Обновить EnhancedStyles
            self.update_enhanced_styles()

            # Обновить предварительный просмотр
            self.create_preview_content()

            messagebox.showinfo("Успех", "Стили применены! Перезапустите приложение для полного применения.")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка применения стилей: {e}")

    def update_enhanced_styles(self):
        """Обновить EnhancedStyles новыми настройками"""
        # Обновить шрифты
        for font_type, font_config in self.current_styles['fonts'].items():
            family = font_config['family']
            size = font_config['size']
            weight = font_config['weight']
            slant = font_config['slant']

            style_parts = []
            if weight == 'bold':
                style_parts.append('bold')
            if slant == 'italic':
                style_parts.append('italic')

            if style_parts:
                EnhancedStyles.FONTS[font_type] = (family, size, ' '.join(style_parts))
            else:
                EnhancedStyles.FONTS[font_type] = (family, size)

        # Обновить цвета
        for color_name, color_value in self.current_styles['colors'].items():
            EnhancedStyles.COLORS[color_name] = color_value

    def save_styles(self):
        """Сохранить стили в файл"""
        try:
            # Обновить текущие стили из интерфейса
            for font_type, vars_dict in self.font_vars.items():
                self.current_styles['fonts'][font_type] = {
                    'family': vars_dict['family'].get(),
                    'size': int(vars_dict['size'].get()),
                    'weight': vars_dict['weight'].get(),
                    'slant': vars_dict['slant'].get()
                }

            for color_name, var in self.color_vars.items():
                self.current_styles['colors'][color_name] = var.get()

            # Сохранить в файл
            styles_file = 'config/custom_styles.json'
            os.makedirs('config', exist_ok=True)

            with open(styles_file, 'w', encoding='utf-8') as f:
                json.dump(self.current_styles, f, indent=4, ensure_ascii=False)

            messagebox.showinfo("Успех", f"Стили сохранены в {styles_file}")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка сохранения: {e}")

    def load_styles(self):
        """Загрузить стили из файла"""
        try:
            styles_file = 'config/custom_styles.json'
            if os.path.exists(styles_file):
                with open(styles_file, 'r', encoding='utf-8') as f:
                    loaded_styles = json.load(f)

                # Обновить текущие стили
                if 'fonts' in loaded_styles:
                    self.current_styles['fonts'].update(loaded_styles['fonts'])
                if 'colors' in loaded_styles:
                    self.current_styles['colors'].update(loaded_styles['colors'])

                # Обновить интерфейс если он создан
                if hasattr(self, 'font_vars'):
                    self.update_interface_from_styles()

        except Exception as e:
            print(f"Ошибка загрузки стилей: {e}")

    def load_styles_dialog(self):
        """Диалог загрузки стилей"""
        file_path = filedialog.askopenfilename(
            title="Загрузить стили",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    loaded_styles = json.load(f)

                # Обновить текущие стили
                if 'fonts' in loaded_styles:
                    self.current_styles['fonts'].update(loaded_styles['fonts'])
                if 'colors' in loaded_styles:
                    self.current_styles['colors'].update(loaded_styles['colors'])

                # Обновить интерфейс
                self.update_interface_from_styles()

                messagebox.showinfo("Успех", "Стили загружены")

            except Exception as e:
                messagebox.showerror("Ошибка", f"Ошибка загрузки: {e}")

    def update_interface_from_styles(self):
        """Обновить интерфейс из загруженных стилей"""
        # Обновить переменные шрифтов
        for font_type, font_config in self.current_styles['fonts'].items():
            if font_type in self.font_vars:
                self.font_vars[font_type]['family'].set(font_config['family'])
                self.font_vars[font_type]['size'].set(str(font_config['size']))
                self.font_vars[font_type]['weight'].set(font_config['weight'])
                self.font_vars[font_type]['slant'].set(font_config['slant'])

        # Обновить переменные цветов
        for color_name, color_value in self.current_styles['colors'].items():
            if color_name in self.color_vars:
                self.color_vars[color_name].set(color_value)

    def reset_styles(self):
        """Сбросить стили к значениям по умолчанию"""
        if messagebox.askyesno("Подтверждение", "Сбросить все стили к значениям по умолчанию?"):
            # Сбросить к исходным значениям
            self.current_styles = {
                'fonts': {
                    'small': {'family': 'Cambria', 'size': 16, 'weight': 'bold', 'slant': 'italic'},
                    'normal': {'family': 'Cambria', 'size': 18, 'weight': 'bold', 'slant': 'italic'},
                    'medium': {'family': 'Cambria', 'size': 20, 'weight': 'bold', 'slant': 'italic'},
                    'large': {'family': 'Cambria', 'size': 24, 'weight': 'bold', 'slant': 'italic'},
                    'xlarge': {'family': 'Cambria', 'size': 28, 'weight': 'bold', 'slant': 'italic'},
                    'title': {'family': 'Cambria', 'size': 32, 'weight': 'bold', 'slant': 'italic'},
                    'header': {'family': 'Cambria', 'size': 36, 'weight': 'bold', 'slant': 'italic'},
                    'button': {'family': 'Cambria', 'size': 18, 'weight': 'bold', 'slant': 'italic'},
                    'menu': {'family': 'Cambria', 'size': 18, 'weight': 'bold', 'slant': 'italic'},
                    'table': {'family': 'Cambria', 'size': 16, 'weight': 'bold', 'slant': 'italic'},
                    'table_header': {'family': 'Cambria', 'size': 18, 'weight': 'bold', 'slant': 'italic'},
                },
                'colors': {
                    'primary': '#2c3e50',
                    'secondary': '#34495e',
                    'success': '#27ae60',
                    'warning': '#f39c12',
                    'danger': '#e74c3c',
                    'info': '#3498db',
                    'light': '#f8f9fa',
                    'white': '#ffffff',
                    'dark': '#2c3e50',
                    'maroon': '#800000',
                    'purple': '#8e44ad',
                    'teal': '#16a085',
                    'text_light': '#ffffff',
                    'text_dark': '#800000',
                    'bg_main': '#ecf0f1',
                    'bg_sidebar': '#2c3e50',
                }
            }

            # Обновить интерфейс
            self.update_interface_from_styles()

            messagebox.showinfo("Сброшено", "Стили сброшены к значениям по умолчанию")

    def show_preset_themes(self):
        """Показать окно выбора предустановленных тем"""
        themes_window = tk.Toplevel(self.window)
        themes_window.title("🎨 Предустановленные Темы")
        themes_window.geometry("800x600")
        themes_window.configure(bg='white')
        themes_window.resizable(True, True)

        # Заголовок
        header_frame = tk.Frame(themes_window, bg='#8e44ad', height=70)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="🎨 Выберите Предустановленную Тему",
                font=('Cambria', 20, 'bold italic'), bg='#8e44ad', fg='white').pack(side='left', padx=25, pady=20)

        # Загрузить темы
        try:
            with open('config/preset_themes.json', 'r', encoding='utf-8') as f:
                themes_data = json.load(f)

            # Скроллируемая область
            canvas = tk.Canvas(themes_window, bg='white')
            scrollbar = ttk.Scrollbar(themes_window, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg='white')

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            # Создать карточки тем
            for theme_id, theme_data in themes_data['themes'].items():
                self.create_theme_card(scrollable_frame, theme_id, theme_data, themes_window)

            canvas.pack(side="left", fill="both", expand=True, padx=20, pady=20)
            scrollbar.pack(side="right", fill="y")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось загрузить темы: {e}")
            themes_window.destroy()

    def create_theme_card(self, parent, theme_id, theme_data, themes_window):
        """Создать карточку темы"""
        card_frame = tk.Frame(parent, bg='#f8f9fa', relief='solid', bd=2)
        card_frame.pack(fill='x', padx=20, pady=15)

        # Заголовок темы
        title_frame = tk.Frame(card_frame, bg='#f8f9fa')
        title_frame.pack(fill='x', padx=20, pady=(15, 10))

        tk.Label(title_frame, text=theme_data['name'],
                font=('Cambria', 18, 'bold'), bg='#f8f9fa', fg='#2c3e50').pack(side='left')

        # Кнопка применения
        tk.Button(title_frame, text="✅ Применить Тему",
                 command=lambda: self.apply_preset_theme(theme_data, themes_window),
                 bg='#27ae60', fg='white', font=('Cambria', 12, 'bold'),
                 relief='flat', padx=15, pady=5).pack(side='right')

        # Описание
        tk.Label(card_frame, text=theme_data['description'],
                font=('Cambria', 14), bg='#f8f9fa', fg='#666666').pack(anchor='w', padx=20, pady=(0, 10))

        # Предварительный просмотр шрифтов
        fonts_frame = tk.Frame(card_frame, bg='#f8f9fa')
        fonts_frame.pack(fill='x', padx=20, pady=(0, 10))

        tk.Label(fonts_frame, text="📝 Шрифты:",
                font=('Cambria', 12, 'bold'), bg='#f8f9fa', fg='#2c3e50').pack(anchor='w')

        # Показать примеры шрифтов
        font_examples = [
            ("Заголовок", theme_data['fonts']['header']),
            ("Обычный текст", theme_data['fonts']['normal']),
            ("Таблицы", theme_data['fonts']['table'])
        ]

        for label, font_config in font_examples:
            example_frame = tk.Frame(fonts_frame, bg='#f8f9fa')
            example_frame.pack(fill='x', pady=2)

            tk.Label(example_frame, text=f"{label}:",
                    font=('Cambria', 10), bg='#f8f9fa', fg='#666666', width=12, anchor='w').pack(side='left')

            # Создать шрифт для примера
            family = font_config['family']
            size = max(10, min(font_config['size'], 16))  # Ограничить размер для предварительного просмотра
            weight = font_config['weight']
            slant = font_config['slant']

            style_parts = []
            if weight == 'bold':
                style_parts.append('bold')
            if slant == 'italic':
                style_parts.append('italic')

            if style_parts:
                font_tuple = (family, size, ' '.join(style_parts))
            else:
                font_tuple = (family, size)

            tk.Label(example_frame, text=f"{family} {font_config['size']}pt {weight} {slant}",
                    font=font_tuple, bg='#f8f9fa', fg='#2c3e50').pack(side='left', padx=10)

        # Предварительный просмотр цветов
        colors_frame = tk.Frame(card_frame, bg='#f8f9fa')
        colors_frame.pack(fill='x', padx=20, pady=(0, 15))

        tk.Label(colors_frame, text="🎨 Цвета:",
                font=('Cambria', 12, 'bold'), bg='#f8f9fa', fg='#2c3e50').pack(anchor='w')

        # Показать основные цвета
        color_samples = tk.Frame(colors_frame, bg='#f8f9fa')
        color_samples.pack(fill='x', pady=5)

        main_colors = ['primary', 'secondary', 'success', 'warning', 'danger', 'info']
        for color_name in main_colors:
            if color_name in theme_data['colors']:
                color_sample = tk.Label(color_samples, text=f" {color_name} ",
                                       bg=theme_data['colors'][color_name], fg='white',
                                       font=('Cambria', 9, 'bold'), relief='solid', bd=1)
                color_sample.pack(side='left', padx=2)

    def apply_preset_theme(self, theme_data, themes_window):
        """Применить предустановленную тему"""
        try:
            # Обновить текущие стили
            self.current_styles['fonts'] = theme_data['fonts'].copy()
            self.current_styles['colors'] = theme_data['colors'].copy()

            # Обновить интерфейс
            self.update_interface_from_styles()

            # Закрыть окно тем
            themes_window.destroy()

            messagebox.showinfo("Успех", f"Тема '{theme_data['name']}' применена!\nНажмите 'Применить' для активации в системе.")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка применения темы: {e}")


def create_style_configurator(parent):
    """Создать конфигуратор стилей"""
    configurator = StyleConfigurator(parent)
    return configurator.create_window()
