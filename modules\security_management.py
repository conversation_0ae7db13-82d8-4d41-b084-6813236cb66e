"""
Security Management Interface
Comprehensive security management with user administration and audit trails
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from datetime import datetime, timedelta
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gui.styles import ModernStyles
from security.security_manager import SecurityManager, UserRole, Permission
from utils.error_handler import handle_module_error, log_info

class SecurityManagementWindow:
    """Security Management Interface"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.security_manager = SecurityManager(db_manager.get_connection())
        
        # Create main window
        self.window = tk.Toplevel(parent)
        self.window.title("🔒 Управление Безопасностью")
        self.window.geometry("1200x800")
        self.window.configure(bg=ModernStyles.COLORS['bg_primary'])
        
        # Center window
        self.window.transient(parent)
        self.window.grab_set()
        
        self.create_widgets()
        self.load_data()
        
        log_info("Управление безопасностью открыто", "SecurityManagement")
    
    def create_widgets(self):
        """Create the security management interface"""
        # Main container
        main_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Title
        title_label = tk.Label(
            main_frame,
            text="🔒 Управление Безопасностью и Пользователями",
            font=ModernStyles.FONTS['title'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        title_label.pack(pady=(0, 20))
        
        # Create notebook for different sections
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill='both', expand=True)
        
        # User Management Tab
        self.create_user_management_tab()
        
        # Audit Logs Tab
        self.create_audit_logs_tab()
        
        # Permissions Tab
        self.create_permissions_tab()
        
        # Security Settings Tab
        self.create_security_settings_tab()
        
        # Control buttons
        self.create_control_buttons(main_frame)
    
    def create_user_management_tab(self):
        """Create user management tab"""
        user_frame = ttk.Frame(self.notebook)
        self.notebook.add(user_frame, text="👥 Пользователи")
        
        # User list frame
        list_frame = tk.LabelFrame(
            user_frame,
            text="Список Пользователей",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        list_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Create treeview for users
        columns = ('ID', 'Имя пользователя', 'Email', 'Роль', 'Статус', 'Последний вход', 'Неудачные попытки')
        self.users_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.users_tree.heading(col, text=col)
            if col == 'ID':
                self.users_tree.column(col, width=50)
            elif col in ['Статус', 'Неудачные попытки']:
                self.users_tree.column(col, width=100)
            else:
                self.users_tree.column(col, width=150)
        
        # Scrollbars for users tree
        users_v_scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.users_tree.yview)
        users_h_scrollbar = ttk.Scrollbar(list_frame, orient="horizontal", command=self.users_tree.xview)
        self.users_tree.configure(yscrollcommand=users_v_scrollbar.set, xscrollcommand=users_h_scrollbar.set)
        
        self.users_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        users_v_scrollbar.pack(side="right", fill="y", pady=10)
        
        # User management buttons
        user_buttons_frame = tk.Frame(user_frame, bg=ModernStyles.COLORS['bg_primary'])
        user_buttons_frame.pack(fill='x', padx=10, pady=10)
        
        # Create user button
        create_user_btn = tk.Button(
            user_buttons_frame,
            text="➕ Создать Пользователя",
            command=self.create_user_dialog,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['success'],
            fg='white',
            relief='flat',
            padx=15,
            pady=8
        )
        create_user_btn.pack(side='left', padx=(0, 10))
        
        # Edit user button
        edit_user_btn = tk.Button(
            user_buttons_frame,
            text="✏️ Редактировать",
            command=self.edit_user_dialog,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['accent'],
            fg='white',
            relief='flat',
            padx=15,
            pady=8
        )
        edit_user_btn.pack(side='left', padx=(0, 10))
        
        # Change password button
        change_password_btn = tk.Button(
            user_buttons_frame,
            text="🔑 Сменить Пароль",
            command=self.change_password_dialog,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['warning'],
            fg='white',
            relief='flat',
            padx=15,
            pady=8
        )
        change_password_btn.pack(side='left', padx=(0, 10))
        
        # Deactivate user button
        deactivate_user_btn = tk.Button(
            user_buttons_frame,
            text="🚫 Деактивировать",
            command=self.deactivate_user,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['danger'],
            fg='white',
            relief='flat',
            padx=15,
            pady=8
        )
        deactivate_user_btn.pack(side='left', padx=(0, 10))
    
    def create_audit_logs_tab(self):
        """Create audit logs tab"""
        audit_frame = ttk.Frame(self.notebook)
        self.notebook.add(audit_frame, text="📋 Журнал Аудита")
        
        # Filter frame
        filter_frame = tk.LabelFrame(
            audit_frame,
            text="Фильтры",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        filter_frame.pack(fill='x', padx=10, pady=10)
        
        # Filter controls
        filter_controls = tk.Frame(filter_frame, bg=ModernStyles.COLORS['bg_secondary'])
        filter_controls.pack(fill='x', padx=10, pady=10)
        
        # User filter
        tk.Label(filter_controls, text="Пользователь:", font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_secondary'], fg=ModernStyles.COLORS['text_primary']).grid(row=0, column=0, sticky='w', padx=(0, 10))
        
        self.user_filter_var = tk.StringVar()
        user_filter_combo = ttk.Combobox(filter_controls, textvariable=self.user_filter_var, width=20)
        user_filter_combo.grid(row=0, column=1, padx=(0, 20))
        
        # Action filter
        tk.Label(filter_controls, text="Действие:", font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_secondary'], fg=ModernStyles.COLORS['text_primary']).grid(row=0, column=2, sticky='w', padx=(0, 10))
        
        self.action_filter_var = tk.StringVar()
        action_filter_combo = ttk.Combobox(filter_controls, textvariable=self.action_filter_var, width=20)
        action_filter_combo['values'] = ['', 'LOGIN_SUCCESS', 'LOGIN_FAILED', 'LOGOUT', 'CREATE_USER', 'UPDATE_USER', 'DELETE_USER']
        action_filter_combo.grid(row=0, column=3, padx=(0, 20))
        
        # Apply filter button
        apply_filter_btn = tk.Button(
            filter_controls,
            text="🔍 Применить",
            command=self.apply_audit_filter,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['accent'],
            fg='white',
            relief='flat',
            padx=15,
            pady=5
        )
        apply_filter_btn.grid(row=0, column=4, padx=(0, 10))
        
        # Audit logs list
        logs_frame = tk.LabelFrame(
            audit_frame,
            text="Журнал Событий",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        logs_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Create treeview for audit logs
        log_columns = ('Время', 'Пользователь', 'Действие', 'Ресурс', 'Детали', 'IP', 'Статус')
        self.audit_tree = ttk.Treeview(logs_frame, columns=log_columns, show='headings', height=15)
        
        for col in log_columns:
            self.audit_tree.heading(col, text=col)
            if col == 'Время':
                self.audit_tree.column(col, width=150)
            elif col in ['IP', 'Статус']:
                self.audit_tree.column(col, width=100)
            else:
                self.audit_tree.column(col, width=120)
        
        # Scrollbars for audit tree
        audit_v_scrollbar = ttk.Scrollbar(logs_frame, orient="vertical", command=self.audit_tree.yview)
        audit_h_scrollbar = ttk.Scrollbar(logs_frame, orient="horizontal", command=self.audit_tree.xview)
        self.audit_tree.configure(yscrollcommand=audit_v_scrollbar.set, xscrollcommand=audit_h_scrollbar.set)
        
        self.audit_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        audit_v_scrollbar.pack(side="right", fill="y", pady=10)
    
    def create_permissions_tab(self):
        """Create permissions management tab"""
        perm_frame = ttk.Frame(self.notebook)
        self.notebook.add(perm_frame, text="🔐 Разрешения")
        
        # Permissions info
        info_label = tk.Label(
            perm_frame,
            text="Управление разрешениями пользователей\nВыберите пользователя из списка для просмотра и изменения разрешений",
            font=ModernStyles.FONTS['body'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_secondary'],
            justify='center'
        )
        info_label.pack(pady=20)
        
        # User selection for permissions
        user_select_frame = tk.Frame(perm_frame, bg=ModernStyles.COLORS['bg_primary'])
        user_select_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Label(user_select_frame, text="Пользователь:", font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_primary'], fg=ModernStyles.COLORS['text_primary']).pack(side='left', padx=(0, 10))
        
        self.perm_user_var = tk.StringVar()
        self.perm_user_combo = ttk.Combobox(user_select_frame, textvariable=self.perm_user_var, width=30)
        self.perm_user_combo.pack(side='left', padx=(0, 10))
        self.perm_user_combo.bind('<<ComboboxSelected>>', self.load_user_permissions)
        
        # Permissions display
        perm_display_frame = tk.LabelFrame(
            perm_frame,
            text="Разрешения Пользователя",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        perm_display_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Permissions text area
        self.permissions_text = tk.Text(
            perm_display_frame,
            height=15,
            font=ModernStyles.FONTS['body'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary'],
            wrap=tk.WORD
        )
        self.permissions_text.pack(fill='both', expand=True, padx=10, pady=10)
    
    def create_security_settings_tab(self):
        """Create security settings tab"""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="⚙️ Настройки")
        
        # Security settings info
        settings_info = tk.Label(
            settings_frame,
            text="Настройки Безопасности Системы",
            font=ModernStyles.FONTS['title'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        settings_info.pack(pady=20)
        
        # Settings content
        settings_content = tk.Frame(settings_frame, bg=ModernStyles.COLORS['bg_primary'])
        settings_content.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Session timeout setting
        session_frame = tk.LabelFrame(
            settings_content,
            text="Настройки Сессии",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        session_frame.pack(fill='x', pady=10)
        
        session_info = tk.Label(
            session_frame,
            text="• Время жизни сессии: 8 часов\n• Автоматическая блокировка после 5 неудачных попыток входа\n• Блокировка на 30 минут",
            font=ModernStyles.FONTS['body'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary'],
            justify='left'
        )
        session_info.pack(padx=10, pady=10, anchor='w')
        
        # Password policy
        password_frame = tk.LabelFrame(
            settings_content,
            text="Политика Паролей",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        password_frame.pack(fill='x', pady=10)
        
        password_info = tk.Label(
            password_frame,
            text="• Минимальная длина: 6 символов\n• Рекомендуется использовать сложные пароли\n• Регулярная смена паролей",
            font=ModernStyles.FONTS['body'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary'],
            justify='left'
        )
        password_info.pack(padx=10, pady=10, anchor='w')
    
    def create_control_buttons(self, parent):
        """Create control buttons"""
        button_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_primary'])
        button_frame.pack(fill='x', pady=(20, 0))
        
        # Refresh button
        refresh_btn = tk.Button(
            button_frame,
            text="🔄 Обновить",
            command=self.load_data,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['accent'],
            fg='white',
            relief='flat',
            padx=20,
            pady=10
        )
        refresh_btn.pack(side='left', padx=(0, 10))
        
        # Export audit log button
        export_btn = tk.Button(
            button_frame,
            text="📊 Экспорт Журнала",
            command=self.export_audit_log,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['success'],
            fg='white',
            relief='flat',
            padx=20,
            pady=10
        )
        export_btn.pack(side='left', padx=(0, 10))
        
        # Close button
        close_btn = tk.Button(
            button_frame,
            text="❌ Закрыть",
            command=self.window.destroy,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['danger'],
            fg='white',
            relief='flat',
            padx=20,
            pady=10
        )
        close_btn.pack(side='right')

    def load_data(self):
        """Load all security data"""
        try:
            self.load_users()
            self.load_audit_logs()
            self.load_user_list_for_permissions()
            messagebox.showinfo("Успех", "Данные безопасности обновлены")
        except Exception as e:
            handle_module_error(e, "Управление Безопасностью", "загрузка данных")

    def load_users(self):
        """Load users list"""
        try:
            # Clear existing data
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)

            # Get users from security manager
            users = self.security_manager.get_all_users()

            for user in users:
                status = "✅ Активен" if user.is_active else "🚫 Неактивен"
                if user.locked_until and user.locked_until > datetime.now():
                    status = "🔒 Заблокирован"

                last_login = user.last_login.strftime('%d.%m.%Y %H:%M') if user.last_login else "Никогда"

                self.users_tree.insert('', 'end', values=(
                    user.id,
                    user.username,
                    user.email,
                    user.role.value,
                    status,
                    last_login,
                    user.failed_login_attempts
                ))

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось загрузить пользователей: {e}")

    def load_audit_logs(self):
        """Load audit logs"""
        try:
            # Clear existing data
            for item in self.audit_tree.get_children():
                self.audit_tree.delete(item)

            # Get audit logs
            logs = self.security_manager.get_audit_logs(limit=200)

            for log in logs:
                status = "✅ Успех" if log.success else "❌ Ошибка"
                timestamp = log.timestamp.strftime('%d.%m.%Y %H:%M:%S')

                self.audit_tree.insert('', 'end', values=(
                    timestamp,
                    log.username,
                    log.action,
                    log.resource,
                    log.details[:50] + "..." if len(log.details) > 50 else log.details,
                    log.ip_address,
                    status
                ))

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось загрузить журнал аудита: {e}")

    def load_user_list_for_permissions(self):
        """Load user list for permissions tab"""
        try:
            users = self.security_manager.get_all_users()
            user_list = [f"{user.username} ({user.role.value})" for user in users]
            self.perm_user_combo['values'] = user_list
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось загрузить список пользователей: {e}")

    def create_user_dialog(self):
        """Show create user dialog"""
        try:
            dialog = CreateUserDialog(self.window, self.security_manager)
            if dialog.result:
                self.load_users()
        except Exception as e:
            handle_module_error(e, "Создание Пользователя", "открытие диалога")

    def edit_user_dialog(self):
        """Show edit user dialog"""
        try:
            selected = self.users_tree.selection()
            if not selected:
                messagebox.showwarning("Предупреждение", "Выберите пользователя для редактирования")
                return

            user_id = self.users_tree.item(selected[0])['values'][0]
            dialog = EditUserDialog(self.window, self.security_manager, user_id)
            if dialog.result:
                self.load_users()
        except Exception as e:
            handle_module_error(e, "Редактирование Пользователя", "открытие диалога")

    def change_password_dialog(self):
        """Show change password dialog"""
        try:
            selected = self.users_tree.selection()
            if not selected:
                messagebox.showwarning("Предупреждение", "Выберите пользователя для смены пароля")
                return

            user_id = self.users_tree.item(selected[0])['values'][0]
            username = self.users_tree.item(selected[0])['values'][1]

            new_password = simpledialog.askstring(
                "Смена пароля",
                f"Введите новый пароль для пользователя {username}:",
                show='*'
            )

            if new_password:
                if len(new_password) < 6:
                    messagebox.showerror("Ошибка", "Пароль должен содержать минимум 6 символов")
                    return

                if self.security_manager.change_password(user_id, new_password):
                    messagebox.showinfo("Успех", "Пароль успешно изменен")
                else:
                    messagebox.showerror("Ошибка", "Не удалось изменить пароль")

        except Exception as e:
            handle_module_error(e, "Смена Пароля", "изменение пароля")

    def deactivate_user(self):
        """Deactivate selected user"""
        try:
            selected = self.users_tree.selection()
            if not selected:
                messagebox.showwarning("Предупреждение", "Выберите пользователя для деактивации")
                return

            user_id = self.users_tree.item(selected[0])['values'][0]
            username = self.users_tree.item(selected[0])['values'][1]

            if messagebox.askyesno("Подтверждение", f"Деактивировать пользователя {username}?"):
                if self.security_manager.delete_user(user_id):
                    messagebox.showinfo("Успех", "Пользователь деактивирован")
                    self.load_users()
                else:
                    messagebox.showerror("Ошибка", "Не удалось деактивировать пользователя")

        except Exception as e:
            handle_module_error(e, "Деактивация Пользователя", "деактивация")

    def apply_audit_filter(self):
        """Apply audit log filters"""
        try:
            # Get filter values
            user_filter = self.user_filter_var.get()
            action_filter = self.action_filter_var.get()

            # Clear existing data
            for item in self.audit_tree.get_children():
                self.audit_tree.delete(item)

            # Get filtered logs
            logs = self.security_manager.get_audit_logs(
                limit=200,
                action=action_filter if action_filter else None
            )

            # Apply user filter if specified
            if user_filter:
                logs = [log for log in logs if user_filter.lower() in log.username.lower()]

            # Display filtered logs
            for log in logs:
                status = "✅ Успех" if log.success else "❌ Ошибка"
                timestamp = log.timestamp.strftime('%d.%m.%Y %H:%M:%S')

                self.audit_tree.insert('', 'end', values=(
                    timestamp,
                    log.username,
                    log.action,
                    log.resource,
                    log.details[:50] + "..." if len(log.details) > 50 else log.details,
                    log.ip_address,
                    status
                ))

        except Exception as e:
            handle_module_error(e, "Фильтрация Журнала", "применение фильтров")

    def load_user_permissions(self, event=None):
        """Load permissions for selected user"""
        try:
            selected_user = self.perm_user_var.get()
            if not selected_user:
                return

            # Extract username from selection
            username = selected_user.split(' (')[0]

            # Find user
            users = self.security_manager.get_all_users()
            user = next((u for u in users if u.username == username), None)

            if not user:
                return

            # Clear permissions text
            self.permissions_text.delete(1.0, tk.END)

            # Display user info
            self.permissions_text.insert(tk.END, f"ПОЛЬЗОВАТЕЛЬ: {user.username}\n")
            self.permissions_text.insert(tk.END, f"РОЛЬ: {user.role.value}\n")
            self.permissions_text.insert(tk.END, f"СТАТУС: {'Активен' if user.is_active else 'Неактивен'}\n\n")

            # Display role permissions
            role_permissions = self.security_manager.ROLE_PERMISSIONS.get(user.role, [])
            self.permissions_text.insert(tk.END, "РАЗРЕШЕНИЯ РОЛИ:\n")

            for perm in role_permissions:
                self.permissions_text.insert(tk.END, f"✅ {perm.value}\n")

            if not role_permissions:
                self.permissions_text.insert(tk.END, "Нет разрешений\n")

        except Exception as e:
            handle_module_error(e, "Загрузка Разрешений", "отображение разрешений")

    def export_audit_log(self):
        """Export audit log to file"""
        try:
            from tkinter import filedialog
            import json

            # Get all audit logs
            logs = self.security_manager.get_audit_logs(limit=1000)

            # Convert to exportable format
            export_data = []
            for log in logs:
                export_data.append({
                    "timestamp": log.timestamp.isoformat(),
                    "username": log.username,
                    "action": log.action,
                    "resource": log.resource,
                    "details": log.details,
                    "ip_address": log.ip_address,
                    "success": log.success
                })

            # Ask user for save location
            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                title="Экспорт журнала аудита"
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)

                messagebox.showinfo("Успех", f"Журнал аудита экспортирован: {filename}")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось экспортировать журнал: {e}")

class CreateUserDialog:
    """Dialog for creating new user"""

    def __init__(self, parent, security_manager):
        self.parent = parent
        self.security_manager = security_manager
        self.result = False

        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Создание Пользователя")
        self.dialog.geometry("400x300")
        self.dialog.configure(bg=ModernStyles.COLORS['bg_primary'])
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.create_widgets()

        # Center dialog
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

    def create_widgets(self):
        """Create dialog widgets"""
        main_frame = tk.Frame(self.dialog, bg=ModernStyles.COLORS['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Title
        title_label = tk.Label(
            main_frame,
            text="Создание Нового Пользователя",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        title_label.pack(pady=(0, 20))

        # Username
        tk.Label(main_frame, text="Имя пользователя:", font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_primary'], fg=ModernStyles.COLORS['text_primary']).pack(anchor='w')
        self.username_entry = tk.Entry(main_frame, font=ModernStyles.FONTS['body'], width=30)
        self.username_entry.pack(fill='x', pady=(5, 10))

        # Email
        tk.Label(main_frame, text="Email:", font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_primary'], fg=ModernStyles.COLORS['text_primary']).pack(anchor='w')
        self.email_entry = tk.Entry(main_frame, font=ModernStyles.FONTS['body'], width=30)
        self.email_entry.pack(fill='x', pady=(5, 10))

        # Password
        tk.Label(main_frame, text="Пароль:", font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_primary'], fg=ModernStyles.COLORS['text_primary']).pack(anchor='w')
        self.password_entry = tk.Entry(main_frame, font=ModernStyles.FONTS['body'], width=30, show='*')
        self.password_entry.pack(fill='x', pady=(5, 10))

        # Role
        tk.Label(main_frame, text="Роль:", font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_primary'], fg=ModernStyles.COLORS['text_primary']).pack(anchor='w')
        self.role_var = tk.StringVar(value=UserRole.VIEWER.value)
        role_combo = ttk.Combobox(main_frame, textvariable=self.role_var, width=27)
        role_combo['values'] = [role.value for role in UserRole]
        role_combo.pack(fill='x', pady=(5, 20))

        # Buttons
        button_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_primary'])
        button_frame.pack(fill='x')

        create_btn = tk.Button(
            button_frame,
            text="Создать",
            command=self.create_user,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['success'],
            fg='white',
            relief='flat',
            padx=20,
            pady=8
        )
        create_btn.pack(side='left', padx=(0, 10))

        cancel_btn = tk.Button(
            button_frame,
            text="Отмена",
            command=self.dialog.destroy,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['danger'],
            fg='white',
            relief='flat',
            padx=20,
            pady=8
        )
        cancel_btn.pack(side='right')

    def create_user(self):
        """Create new user"""
        try:
            username = self.username_entry.get().strip()
            email = self.email_entry.get().strip()
            password = self.password_entry.get()
            role = UserRole(self.role_var.get())

            if not username or not email or not password:
                messagebox.showerror("Ошибка", "Заполните все поля")
                return

            if len(password) < 6:
                messagebox.showerror("Ошибка", "Пароль должен содержать минимум 6 символов")
                return

            if self.security_manager.create_user(username, email, password, role):
                messagebox.showinfo("Успех", "Пользователь создан успешно")
                self.result = True
                self.dialog.destroy()
            else:
                messagebox.showerror("Ошибка", "Не удалось создать пользователя. Возможно, имя пользователя или email уже существуют.")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка создания пользователя: {e}")

class EditUserDialog:
    """Dialog for editing user"""

    def __init__(self, parent, security_manager, user_id):
        self.parent = parent
        self.security_manager = security_manager
        self.user_id = user_id
        self.result = False

        # Get user data
        users = security_manager.get_all_users()
        self.user = next((u for u in users if u.id == user_id), None)

        if not self.user:
            messagebox.showerror("Ошибка", "Пользователь не найден")
            return

        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Редактирование Пользователя")
        self.dialog.geometry("400x250")
        self.dialog.configure(bg=ModernStyles.COLORS['bg_primary'])
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.create_widgets()

        # Center dialog
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

    def create_widgets(self):
        """Create dialog widgets"""
        main_frame = tk.Frame(self.dialog, bg=ModernStyles.COLORS['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Title
        title_label = tk.Label(
            main_frame,
            text=f"Редактирование: {self.user.username}",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        title_label.pack(pady=(0, 20))

        # Email
        tk.Label(main_frame, text="Email:", font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_primary'], fg=ModernStyles.COLORS['text_primary']).pack(anchor='w')
        self.email_entry = tk.Entry(main_frame, font=ModernStyles.FONTS['body'], width=30)
        self.email_entry.insert(0, self.user.email)
        self.email_entry.pack(fill='x', pady=(5, 10))

        # Role
        tk.Label(main_frame, text="Роль:", font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_primary'], fg=ModernStyles.COLORS['text_primary']).pack(anchor='w')
        self.role_var = tk.StringVar(value=self.user.role.value)
        role_combo = ttk.Combobox(main_frame, textvariable=self.role_var, width=27)
        role_combo['values'] = [role.value for role in UserRole]
        role_combo.pack(fill='x', pady=(5, 10))

        # Active status
        self.active_var = tk.BooleanVar(value=self.user.is_active)
        active_check = tk.Checkbutton(
            main_frame,
            text="Активный пользователь",
            variable=self.active_var,
            font=ModernStyles.FONTS['body'],
            bg=ModernStyles.COLORS['bg_primary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        active_check.pack(anchor='w', pady=10)

        # Buttons
        button_frame = tk.Frame(main_frame, bg=ModernStyles.COLORS['bg_primary'])
        button_frame.pack(fill='x')

        save_btn = tk.Button(
            button_frame,
            text="Сохранить",
            command=self.save_user,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['success'],
            fg='white',
            relief='flat',
            padx=20,
            pady=8
        )
        save_btn.pack(side='left', padx=(0, 10))

        cancel_btn = tk.Button(
            button_frame,
            text="Отмена",
            command=self.dialog.destroy,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['danger'],
            fg='white',
            relief='flat',
            padx=20,
            pady=8
        )
        cancel_btn.pack(side='right')

    def save_user(self):
        """Save user changes"""
        try:
            email = self.email_entry.get().strip()
            role = UserRole(self.role_var.get())
            is_active = self.active_var.get()

            if not email:
                messagebox.showerror("Ошибка", "Email не может быть пустым")
                return

            if self.security_manager.update_user(self.user_id, email=email, role=role, is_active=is_active):
                messagebox.showinfo("Успех", "Пользователь обновлен успешно")
                self.result = True
                self.dialog.destroy()
            else:
                messagebox.showerror("Ошибка", "Не удалось обновить пользователя")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка обновления пользователя: {e}")

def show_security_management(parent, db_manager):
    """Show security management window"""
    try:
        SecurityManagementWindow(parent, db_manager)
    except Exception as e:
        handle_module_error(e, "Управление Безопасностью", "открытие окна")
