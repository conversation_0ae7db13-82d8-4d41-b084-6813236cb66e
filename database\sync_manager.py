"""
Real-time Data Synchronization Manager for Restaurant Management System
Provides multi-location data synchronization with conflict resolution and offline support
"""

import sqlite3
import json
import time
import threading
import hashlib
import uuid
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass, asdict
from enum import Enum
# import requests  # Not needed for local sync
import queue
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed

class SyncStatus(Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CONFLICT = "conflict"

class ConflictResolution(Enum):
    LATEST_WINS = "latest_wins"
    MANUAL = "manual"
    MERGE = "merge"
    SOURCE_WINS = "source_wins"

@dataclass
class SyncRecord:
    """Represents a synchronization record"""
    id: str
    table_name: str
    record_id: str
    operation: str  # INSERT, UPDATE, DELETE
    data: Dict[str, Any]
    timestamp: datetime
    location_id: str
    checksum: str
    status: SyncStatus = SyncStatus.PENDING
    retry_count: int = 0
    error_message: Optional[str] = None

@dataclass
class SyncConflict:
    """Represents a synchronization conflict"""
    id: str
    table_name: str
    record_id: str
    local_data: Dict[str, Any]
    remote_data: Dict[str, Any]
    local_timestamp: datetime
    remote_timestamp: datetime
    resolution_strategy: ConflictResolution
    resolved: bool = False
    resolved_data: Optional[Dict[str, Any]] = None

class DataSyncManager:
    """Advanced data synchronization manager for multi-location restaurants"""
    
    def __init__(self, connection_pool, location_id: str, sync_config: Dict[str, Any] = None):
        self.connection_pool = connection_pool
        self.location_id = location_id
        self.sync_config = sync_config or self._get_default_config()
        self.logger = logging.getLogger(__name__)
        
        # Sync state
        self.is_online = True
        self.sync_active = False
        self.sync_thread = None
        self.pending_changes = queue.Queue()
        self.conflicts = []
        
        # Statistics
        self.sync_stats = {
            'total_synced': 0,
            'conflicts_resolved': 0,
            'failed_syncs': 0,
            'last_sync': None,
            'sync_duration': 0.0
        }
        
        # Initialize sync infrastructure
        self._initialize_sync_tables()
        self._start_change_tracking()
        
        # Start sync thread
        self.start_sync_service()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default synchronization configuration"""
        return {
            'sync_interval': 30,  # seconds
            'batch_size': 100,
            'max_retries': 3,
            'conflict_resolution': ConflictResolution.LATEST_WINS,
            'sync_tables': [
                'sales', 'inventory', 'recipes', 'staff', 'customers',
                'purchase_orders', 'expenses', 'menu_items', 'suppliers'
            ],
            'exclude_columns': {
                'sales': ['id'],  # Auto-increment columns
                'inventory': ['id'],
                'recipes': ['id']
            },
            'central_server_url': 'http://localhost:8080/api/sync',
            'auth_token': None,
            'encryption_key': None
        }
    
    def _initialize_sync_tables(self):
        """Initialize synchronization tracking tables"""
        sync_tables_sql = [
            '''
            CREATE TABLE IF NOT EXISTS sync_log (
                id TEXT PRIMARY KEY,
                table_name TEXT NOT NULL,
                record_id TEXT NOT NULL,
                operation TEXT NOT NULL,
                data TEXT NOT NULL,
                timestamp DATETIME NOT NULL,
                location_id TEXT NOT NULL,
                checksum TEXT NOT NULL,
                status TEXT DEFAULT 'pending',
                retry_count INTEGER DEFAULT 0,
                error_message TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            ''',
            '''
            CREATE TABLE IF NOT EXISTS sync_conflicts (
                id TEXT PRIMARY KEY,
                table_name TEXT NOT NULL,
                record_id TEXT NOT NULL,
                local_data TEXT NOT NULL,
                remote_data TEXT NOT NULL,
                local_timestamp DATETIME NOT NULL,
                remote_timestamp DATETIME NOT NULL,
                resolution_strategy TEXT NOT NULL,
                resolved BOOLEAN DEFAULT FALSE,
                resolved_data TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            ''',
            '''
            CREATE TABLE IF NOT EXISTS sync_metadata (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            ''',
            '''
            CREATE INDEX IF NOT EXISTS idx_sync_log_status ON sync_log(status)
            ''',
            '''
            CREATE INDEX IF NOT EXISTS idx_sync_log_timestamp ON sync_log(timestamp)
            ''',
            '''
            CREATE INDEX IF NOT EXISTS idx_sync_conflicts_resolved ON sync_conflicts(resolved)
            '''
        ]
        
        for sql in sync_tables_sql:
            self.connection_pool.execute_query(sql)
        
        # Set initial metadata
        self._set_sync_metadata('location_id', self.location_id)
        self._set_sync_metadata('last_sync', datetime.now().isoformat())
    
    def _start_change_tracking(self):
        """Start tracking changes to synchronized tables"""
        for table_name in self.sync_config['sync_tables']:
            self._create_change_triggers(table_name)
    
    def _create_change_triggers(self, table_name: str):
        """Create triggers to track changes for a table"""
        # Check if table exists
        table_exists = self.connection_pool.execute_query(
            "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
            (table_name,)
        )
        
        if not table_exists:
            self.logger.warning(f"Table {table_name} does not exist, skipping trigger creation")
            return
        
        # Get table schema
        schema = self.connection_pool.execute_query(f"PRAGMA table_info({table_name})")
        if not schema:
            self.logger.warning(f"Could not get schema for table {table_name}")
            return

        # Handle different return formats from PRAGMA
        if isinstance(schema, list) and len(schema) > 0:
            if isinstance(schema[0], dict):
                columns = [col['name'] for col in schema]
            elif isinstance(schema[0], (list, tuple)):
                columns = [col[1] for col in schema]  # Column name is at index 1
            else:
                self.logger.warning(f"Unexpected schema format for table {table_name}")
                return
        else:
            self.logger.warning(f"Empty schema for table {table_name}")
            return
        
        # Create triggers for INSERT, UPDATE, DELETE
        triggers = [
            f'''
            CREATE TRIGGER IF NOT EXISTS sync_insert_{table_name}
            AFTER INSERT ON {table_name}
            BEGIN
                INSERT INTO sync_log (id, table_name, record_id, operation, data, timestamp, location_id, checksum)
                VALUES (
                    '{uuid.uuid4().hex}',
                    '{table_name}',
                    COALESCE(NEW.id, NEW.rowid),
                    'INSERT',
                    json_object({', '.join([f"'{col}', NEW.{col}" for col in columns])}),
                    datetime('now'),
                    '{self.location_id}',
                    ''
                );
            END
            ''',
            f'''
            CREATE TRIGGER IF NOT EXISTS sync_update_{table_name}
            AFTER UPDATE ON {table_name}
            BEGIN
                INSERT INTO sync_log (id, table_name, record_id, operation, data, timestamp, location_id, checksum)
                VALUES (
                    '{uuid.uuid4().hex}',
                    '{table_name}',
                    COALESCE(NEW.id, NEW.rowid),
                    'UPDATE',
                    json_object({', '.join([f"'{col}', NEW.{col}" for col in columns])}),
                    datetime('now'),
                    '{self.location_id}',
                    ''
                );
            END
            ''',
            f'''
            CREATE TRIGGER IF NOT EXISTS sync_delete_{table_name}
            AFTER DELETE ON {table_name}
            BEGIN
                INSERT INTO sync_log (id, table_name, record_id, operation, data, timestamp, location_id, checksum)
                VALUES (
                    '{uuid.uuid4().hex}',
                    '{table_name}',
                    COALESCE(OLD.id, OLD.rowid),
                    'DELETE',
                    json_object({', '.join([f"'{col}', OLD.{col}" for col in columns])}),
                    datetime('now'),
                    '{self.location_id}',
                    ''
                );
            END
            '''
        ]
        
        for trigger_sql in triggers:
            try:
                self.connection_pool.execute_query(trigger_sql)
            except Exception as e:
                self.logger.error(f"Error creating trigger for {table_name}: {e}")
    
    def start_sync_service(self):
        """Start the synchronization service"""
        if not self.sync_active:
            self.sync_active = True
            self.sync_thread = threading.Thread(target=self._sync_loop, daemon=True)
            self.sync_thread.start()
            self.logger.info("Data synchronization service started")
    
    def stop_sync_service(self):
        """Stop the synchronization service"""
        self.sync_active = False
        if self.sync_thread:
            self.sync_thread.join(timeout=5)
        self.logger.info("Data synchronization service stopped")
    
    def _sync_loop(self):
        """Main synchronization loop"""
        while self.sync_active:
            try:
                if self.is_online:
                    self._perform_sync_cycle()
                else:
                    self.logger.debug("Offline mode - skipping sync cycle")
                
                time.sleep(self.sync_config['sync_interval'])
                
            except Exception as e:
                self.logger.error(f"Sync loop error: {e}")
                time.sleep(60)  # Wait longer on error
    
    def _perform_sync_cycle(self):
        """Perform a complete synchronization cycle"""
        start_time = time.time()
        
        try:
            # 1. Send pending changes to central server
            self._send_pending_changes()
            
            # 2. Receive changes from central server
            self._receive_remote_changes()
            
            # 3. Resolve any conflicts
            self._resolve_conflicts()
            
            # 4. Update sync metadata
            self.sync_stats['last_sync'] = datetime.now()
            self.sync_stats['sync_duration'] = time.time() - start_time
            self._set_sync_metadata('last_sync', self.sync_stats['last_sync'].isoformat())
            
            self.logger.debug(f"Sync cycle completed in {self.sync_stats['sync_duration']:.2f} seconds")
            
        except Exception as e:
            self.logger.error(f"Sync cycle failed: {e}")
            self.sync_stats['failed_syncs'] += 1
    
    def _send_pending_changes(self):
        """Send pending changes to the central server"""
        pending_records = self.connection_pool.execute_query(
            "SELECT * FROM sync_log WHERE status = 'pending' ORDER BY timestamp LIMIT ?",
            (self.sync_config['batch_size'],)
        )
        
        if not pending_records:
            return
        
        # Group by operation for efficient processing
        changes_by_operation = {'INSERT': [], 'UPDATE': [], 'DELETE': []}
        
        for record in pending_records:
            sync_record = SyncRecord(
                id=record['id'],
                table_name=record['table_name'],
                record_id=record['record_id'],
                operation=record['operation'],
                data=json.loads(record['data']),
                timestamp=datetime.fromisoformat(record['timestamp']),
                location_id=record['location_id'],
                checksum=record['checksum'],
                status=SyncStatus(record['status']),
                retry_count=record['retry_count']
            )
            
            # Calculate checksum
            sync_record.checksum = self._calculate_checksum(sync_record.data)
            changes_by_operation[sync_record.operation].append(sync_record)
        
        # Send to central server (simulated for now)
        success_count = self._send_to_central_server(changes_by_operation)
        
        # Update sync status
        if success_count > 0:
            record_ids = [r['id'] for r in pending_records[:success_count]]
            placeholders = ','.join(['?' for _ in record_ids])
            self.connection_pool.execute_query(
                f"UPDATE sync_log SET status = 'completed' WHERE id IN ({placeholders})",
                tuple(record_ids)
            )
            
            self.sync_stats['total_synced'] += success_count
    
    def _send_to_central_server(self, changes: Dict[str, List[SyncRecord]]) -> int:
        """Send changes to central server (simulated)"""
        # In a real implementation, this would send HTTP requests to the central server
        # For now, we'll simulate successful sync
        total_changes = sum(len(records) for records in changes.values())
        
        self.logger.debug(f"Simulating sync of {total_changes} changes to central server")
        
        # Simulate network delay
        time.sleep(0.1)
        
        return total_changes
    
    def _calculate_checksum(self, data: Dict[str, Any]) -> str:
        """Calculate checksum for data integrity"""
        data_str = json.dumps(data, sort_keys=True, default=str)
        return hashlib.md5(data_str.encode()).hexdigest()
    
    def _set_sync_metadata(self, key: str, value: str):
        """Set synchronization metadata"""
        self.connection_pool.execute_query(
            "INSERT OR REPLACE INTO sync_metadata (key, value, updated_at) VALUES (?, ?, ?)",
            (key, value, datetime.now().isoformat())
        )

    def _receive_remote_changes(self):
        """Receive and apply changes from remote locations"""
        # In a real implementation, this would fetch from central server
        # For now, we'll simulate receiving changes

        # Get last sync timestamp
        last_sync = self._get_sync_metadata('last_sync')
        if last_sync:
            last_sync_time = datetime.fromisoformat(last_sync)
        else:
            last_sync_time = datetime.now() - timedelta(days=1)

        # Simulate receiving remote changes
        remote_changes = self._fetch_remote_changes(last_sync_time)

        for change in remote_changes:
            self._apply_remote_change(change)

    def _fetch_remote_changes(self, since: datetime) -> List[SyncRecord]:
        """Fetch changes from central server (simulated)"""
        # In a real implementation, this would make HTTP requests
        # For now, return empty list
        return []

    def _apply_remote_change(self, change: SyncRecord):
        """Apply a remote change to local database"""
        try:
            # Check for conflicts
            conflict = self._detect_conflict(change)
            if conflict:
                self._handle_conflict(conflict)
                return

            # Apply the change
            if change.operation == 'INSERT':
                self._apply_insert(change)
            elif change.operation == 'UPDATE':
                self._apply_update(change)
            elif change.operation == 'DELETE':
                self._apply_delete(change)

            self.logger.debug(f"Applied remote change: {change.operation} on {change.table_name}")

        except Exception as e:
            self.logger.error(f"Error applying remote change: {e}")

    def _detect_conflict(self, remote_change: SyncRecord) -> Optional[SyncConflict]:
        """Detect synchronization conflicts"""
        # Check if we have a local change for the same record
        local_changes = self.connection_pool.execute_query(
            "SELECT * FROM sync_log WHERE table_name = ? AND record_id = ? AND timestamp > ?",
            (remote_change.table_name, remote_change.record_id,
             (remote_change.timestamp - timedelta(minutes=5)).isoformat())
        )

        if not local_changes:
            return None

        # Get current local data
        local_data = self._get_current_record(remote_change.table_name, remote_change.record_id)
        if not local_data:
            return None

        # Check if data is different
        local_checksum = self._calculate_checksum(local_data)
        if local_checksum == remote_change.checksum:
            return None  # No conflict, data is the same

        # Create conflict record
        conflict = SyncConflict(
            id=uuid.uuid4().hex,
            table_name=remote_change.table_name,
            record_id=remote_change.record_id,
            local_data=local_data,
            remote_data=remote_change.data,
            local_timestamp=datetime.now(),
            remote_timestamp=remote_change.timestamp,
            resolution_strategy=self.sync_config['conflict_resolution']
        )

        return conflict

    def _get_current_record(self, table_name: str, record_id: str) -> Optional[Dict[str, Any]]:
        """Get current record from database"""
        try:
            result = self.connection_pool.execute_query(
                f"SELECT * FROM {table_name} WHERE id = ? OR rowid = ?",
                (record_id, record_id),
                fetch_all=False
            )
            return dict(result) if result else None
        except Exception as e:
            self.logger.error(f"Error getting current record: {e}")
            return None

    def _handle_conflict(self, conflict: SyncConflict):
        """Handle synchronization conflict"""
        # Store conflict for manual resolution if needed
        self.connection_pool.execute_query(
            '''INSERT INTO sync_conflicts
               (id, table_name, record_id, local_data, remote_data,
                local_timestamp, remote_timestamp, resolution_strategy)
               VALUES (?, ?, ?, ?, ?, ?, ?, ?)''',
            (conflict.id, conflict.table_name, conflict.record_id,
             json.dumps(conflict.local_data, default=str),
             json.dumps(conflict.remote_data, default=str),
             conflict.local_timestamp.isoformat(),
             conflict.remote_timestamp.isoformat(),
             conflict.resolution_strategy.value)
        )

        self.conflicts.append(conflict)

        # Auto-resolve based on strategy
        if conflict.resolution_strategy == ConflictResolution.LATEST_WINS:
            if conflict.remote_timestamp > conflict.local_timestamp:
                conflict.resolved_data = conflict.remote_data
            else:
                conflict.resolved_data = conflict.local_data
            self._resolve_conflict(conflict)
        elif conflict.resolution_strategy == ConflictResolution.MERGE:
            conflict.resolved_data = self._merge_data(conflict.local_data, conflict.remote_data)
            self._resolve_conflict(conflict)
        # MANUAL resolution requires user intervention

    def _merge_data(self, local_data: Dict[str, Any], remote_data: Dict[str, Any]) -> Dict[str, Any]:
        """Merge local and remote data intelligently"""
        merged = local_data.copy()

        # Simple merge strategy: take non-null values from remote
        for key, value in remote_data.items():
            if value is not None and (key not in merged or merged[key] is None):
                merged[key] = value

        return merged

    def _resolve_conflict(self, conflict: SyncConflict):
        """Resolve a conflict with the determined data"""
        if not conflict.resolved_data:
            return

        try:
            # Apply resolved data
            self._update_record(conflict.table_name, conflict.record_id, conflict.resolved_data)

            # Mark conflict as resolved
            self.connection_pool.execute_query(
                "UPDATE sync_conflicts SET resolved = TRUE, resolved_data = ? WHERE id = ?",
                (json.dumps(conflict.resolved_data, default=str), conflict.id)
            )

            conflict.resolved = True
            self.sync_stats['conflicts_resolved'] += 1

            self.logger.info(f"Resolved conflict for {conflict.table_name}:{conflict.record_id}")

        except Exception as e:
            self.logger.error(f"Error resolving conflict: {e}")

    def _update_record(self, table_name: str, record_id: str, data: Dict[str, Any]):
        """Update a record in the database"""
        # Get table schema to build proper UPDATE query
        schema = self.connection_pool.execute_query(f"PRAGMA table_info({table_name})")
        columns = [col['name'] for col in schema if col['name'] in data]

        if not columns:
            return

        set_clause = ', '.join([f"{col} = ?" for col in columns])
        values = [data[col] for col in columns]
        values.append(record_id)

        query = f"UPDATE {table_name} SET {set_clause} WHERE id = ? OR rowid = ?"
        self.connection_pool.execute_query(query, tuple(values))

    def _apply_insert(self, change: SyncRecord):
        """Apply INSERT operation"""
        columns = list(change.data.keys())
        placeholders = ', '.join(['?' for _ in columns])
        column_names = ', '.join(columns)
        values = [change.data[col] for col in columns]

        query = f"INSERT OR REPLACE INTO {change.table_name} ({column_names}) VALUES ({placeholders})"
        self.connection_pool.execute_query(query, tuple(values))

    def _apply_update(self, change: SyncRecord):
        """Apply UPDATE operation"""
        self._update_record(change.table_name, change.record_id, change.data)

    def _apply_delete(self, change: SyncRecord):
        """Apply DELETE operation"""
        query = f"DELETE FROM {change.table_name} WHERE id = ? OR rowid = ?"
        self.connection_pool.execute_query(query, (change.record_id, change.record_id))

    def _resolve_conflicts(self):
        """Resolve pending conflicts"""
        unresolved_conflicts = self.connection_pool.execute_query(
            "SELECT * FROM sync_conflicts WHERE resolved = FALSE"
        )

        for conflict_data in unresolved_conflicts:
            conflict = SyncConflict(
                id=conflict_data['id'],
                table_name=conflict_data['table_name'],
                record_id=conflict_data['record_id'],
                local_data=json.loads(conflict_data['local_data']),
                remote_data=json.loads(conflict_data['remote_data']),
                local_timestamp=datetime.fromisoformat(conflict_data['local_timestamp']),
                remote_timestamp=datetime.fromisoformat(conflict_data['remote_timestamp']),
                resolution_strategy=ConflictResolution(conflict_data['resolution_strategy'])
            )

            if conflict.resolution_strategy != ConflictResolution.MANUAL:
                self._handle_conflict(conflict)

    def _get_sync_metadata(self, key: str) -> Optional[str]:
        """Get synchronization metadata"""
        result = self.connection_pool.execute_query(
            "SELECT value FROM sync_metadata WHERE key = ?",
            (key,),
            fetch_all=False
        )
        return result['value'] if result else None

    def get_sync_status(self) -> Dict[str, Any]:
        """Get current synchronization status"""
        return {
            'location_id': self.location_id,
            'is_online': self.is_online,
            'sync_active': self.sync_active,
            'pending_changes': self.connection_pool.execute_query(
                "SELECT COUNT(*) as count FROM sync_log WHERE status = 'pending'",
                fetch_all=False
            )['count'],
            'unresolved_conflicts': len([c for c in self.conflicts if not c.resolved]),
            'statistics': self.sync_stats.copy(),
            'last_sync': self._get_sync_metadata('last_sync')
        }

    def force_sync(self) -> Dict[str, Any]:
        """Force immediate synchronization"""
        if not self.sync_active:
            return {'status': 'error', 'message': 'Sync service not active'}

        try:
            self._perform_sync_cycle()
            return {'status': 'success', 'message': 'Forced sync completed'}
        except Exception as e:
            return {'status': 'error', 'message': f'Forced sync failed: {e}'}

    def set_online_status(self, online: bool):
        """Set online/offline status"""
        self.is_online = online
        self.logger.info(f"Sync status changed to: {'online' if online else 'offline'}")

    def get_pending_conflicts(self) -> List[Dict[str, Any]]:
        """Get list of pending conflicts for manual resolution"""
        conflicts = self.connection_pool.execute_query(
            "SELECT * FROM sync_conflicts WHERE resolved = FALSE ORDER BY created_at"
        )

        return [dict(conflict) for conflict in conflicts]

    def resolve_conflict_manually(self, conflict_id: str, resolved_data: Dict[str, Any]) -> bool:
        """Manually resolve a conflict"""
        try:
            # Get conflict details
            conflict_data = self.connection_pool.execute_query(
                "SELECT * FROM sync_conflicts WHERE id = ?",
                (conflict_id,),
                fetch_all=False
            )

            if not conflict_data:
                return False

            # Apply resolved data
            self._update_record(
                conflict_data['table_name'],
                conflict_data['record_id'],
                resolved_data
            )

            # Mark as resolved
            self.connection_pool.execute_query(
                "UPDATE sync_conflicts SET resolved = TRUE, resolved_data = ? WHERE id = ?",
                (json.dumps(resolved_data, default=str), conflict_id)
            )

            self.sync_stats['conflicts_resolved'] += 1
            return True

        except Exception as e:
            self.logger.error(f"Error manually resolving conflict: {e}")
            return False
