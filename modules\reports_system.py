"""
Полностью функциональная система отчётов
Генерация различных отчётов, экспорт, планирование отчётов
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
import json
import os
from gui.styles import ModernStyles

class ReportsSystem:
    """Система отчётов"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        
        # Типы отчётов
        self.report_types = {
            "Финансовые отчёты": [
                {"name": "Отчёт о прибылях и убытках", "description": "Доходы и расходы за период", "frequency": "Ежемесячно"},
                {"name": "Отчёт о движении денежных средств", "description": "Поступления и выплаты", "frequency": "Еженедельно"},
                {"name": "Баланс", "description": "Активы и пассивы на дату", "frequency": "Ежемесячно"},
                {"name": "Анализ рентабельности", "description": "Прибыльность по направлениям", "frequency": "Ежемесячно"}
            ],
            "Операционные отчёты": [
                {"name": "Отчёт по продажам", "description": "Анализ продаж по периодам", "frequency": "Ежедневно"},
                {"name": "Отчёт по складу", "description": "Остатки и движение товаров", "frequency": "Еженедельно"},
                {"name": "Отчёт по персоналу", "description": "Рабочее время и зарплаты", "frequency": "Ежемесячно"},
                {"name": "Отчёт по поставщикам", "description": "Анализ работы поставщиков", "frequency": "Ежемесячно"}
            ],
            "Аналитические отчёты": [
                {"name": "ABC анализ товаров", "description": "Классификация товаров по важности", "frequency": "Ежемесячно"},
                {"name": "Анализ клиентской базы", "description": "Сегментация и лояльность клиентов", "frequency": "Ежемесячно"},
                {"name": "Анализ меню", "description": "Популярность и прибыльность блюд", "frequency": "Еженедельно"},
                {"name": "Прогноз продаж", "description": "Планирование на основе трендов", "frequency": "Ежемесячно"}
            ],
            "Контрольные отчёты": [
                {"name": "Отчёт по качеству", "description": "Соблюдение стандартов качества", "frequency": "Еженедельно"},
                {"name": "Отчёт по безопасности", "description": "Инциденты и нарушения", "frequency": "Ежемесячно"},
                {"name": "Отчёт по сертификации", "description": "Статус сертификатов и лицензий", "frequency": "Ежеквартально"},
                {"name": "Аудиторский отчёт", "description": "Результаты внутренних аудитов", "frequency": "Ежеквартально"}
            ]
        }
        
        # История отчётов
        self.report_history = [
            {
                "date": "2024-01-15", "type": "Отчёт по продажам", "period": "14.01.2024",
                "generated_by": "Иванов И.И.", "format": "PDF", "status": "Готов",
                "file_size": "2.3 MB", "recipients": ["Директор", "Бухгалтер"]
            },
            {
                "date": "2024-01-14", "type": "Отчёт о прибылях и убытках", "period": "Декабрь 2023",
                "generated_by": "Петрова М.С.", "format": "Excel", "status": "Готов",
                "file_size": "1.8 MB", "recipients": ["Директор", "Финансовый директор"]
            },
            {
                "date": "2024-01-13", "type": "Отчёт по складу", "period": "Неделя 2",
                "generated_by": "Сидоров А.П.", "format": "PDF", "status": "Готов",
                "file_size": "1.2 MB", "recipients": ["Заведующий складом"]
            },
            {
                "date": "2024-01-12", "type": "ABC анализ товаров", "period": "Декабрь 2023",
                "generated_by": "Козлова Е.В.", "format": "Excel", "status": "Готов",
                "file_size": "3.1 MB", "recipients": ["Директор", "Менеджер закупок"]
            }
        ]
        
        # Шаблоны отчётов
        self.report_templates = [
            {"name": "Стандартный финансовый", "description": "Базовый шаблон для финансовых отчётов"},
            {"name": "Детальный операционный", "description": "Подробный шаблон для операционных отчётов"},
            {"name": "Аналитический с графиками", "description": "Шаблон с диаграммами и графиками"},
            {"name": "Краткий управленческий", "description": "Сжатый формат для руководства"}
        ]
    
    def create_window(self):
        """Создать окно системы отчётов"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("📈 Система Отчётов")
        self.window.geometry("1400x900")
        self.window.configure(bg=ModernStyles.COLORS['bg_main'])
        self.window.resizable(True, True)
        
        self.create_interface()
    
    def create_interface(self):
        """Создать интерфейс системы отчётов"""
        # Заголовок
        header_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['primary'], height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="📈 Система Отчётов",
                font=('Arial', 16, 'bold'), bg=ModernStyles.COLORS['primary'], 
                fg='white').pack(side='left', padx=20, pady=15)
        
        # Кнопки действий
        btn_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['primary'])
        btn_frame.pack(side='right', padx=20, pady=10)
        
        tk.Button(btn_frame, text="📊 Создать Отчёт", command=self.create_report,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="⏰ Планировщик", command=self.schedule_reports,
                 bg=ModernStyles.COLORS['warning'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="📤 Экспорт", command=self.export_reports,
                 bg=ModernStyles.COLORS['secondary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        # Основной контент
        main_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_main'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Создать вкладки
        self.create_tabs(main_frame)
    
    def create_tabs(self, parent):
        """Создать вкладки модуля"""
        notebook = ttk.Notebook(parent)
        notebook.pack(fill='both', expand=True)
        
        # Вкладка типов отчётов
        types_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(types_frame, text="📋 Типы Отчётов")
        self.create_types_tab(types_frame)
        
        # Вкладка истории
        history_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(history_frame, text="📚 История Отчётов")
        self.create_history_tab(history_frame)
        
        # Вкладка шаблонов
        templates_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(templates_frame, text="📄 Шаблоны")
        self.create_templates_tab(templates_frame)
        
        # Вкладка планировщика
        scheduler_frame = tk.Frame(notebook, bg=ModernStyles.COLORS['bg_main'])
        notebook.add(scheduler_frame, text="⏰ Планировщик")
        self.create_scheduler_tab(scheduler_frame)
    
    def create_types_tab(self, parent):
        """Создать вкладку типов отчётов"""
        # Заголовок
        tk.Label(parent, text="Доступные Типы Отчётов",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)
        
        # Создать фреймы для каждой категории
        categories_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        categories_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Разместить категории в сетке 2x2
        row = 0
        col = 0
        for category, reports in self.report_types.items():
            category_frame = tk.LabelFrame(categories_frame, text=category,
                                          font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
            category_frame.grid(row=row, column=col, sticky='nsew', padx=10, pady=10)
            
            for report in reports:
                report_row = tk.Frame(category_frame, bg=ModernStyles.COLORS['bg_card'], relief='solid', bd=1)
                report_row.pack(fill='x', padx=5, pady=3)
                
                # Название отчёта
                tk.Label(report_row, text=report['name'], font=('Arial', 10, 'bold'),
                        bg=ModernStyles.COLORS['bg_card']).pack(anchor='w', padx=10, pady=2)
                
                # Описание
                tk.Label(report_row, text=report['description'], font=('Arial', 9),
                        bg=ModernStyles.COLORS['bg_card'], fg='gray').pack(anchor='w', padx=10)
                
                # Частота
                tk.Label(report_row, text=f"Частота: {report['frequency']}", font=('Arial', 9),
                        bg=ModernStyles.COLORS['bg_card'], fg='blue').pack(anchor='w', padx=10, pady=2)
            
            col += 1
            if col > 1:
                col = 0
                row += 1
        
        # Настроить веса для равномерного распределения
        categories_frame.grid_columnconfigure(0, weight=1)
        categories_frame.grid_columnconfigure(1, weight=1)
        categories_frame.grid_rowconfigure(0, weight=1)
        categories_frame.grid_rowconfigure(1, weight=1)
    
    def create_history_tab(self, parent):
        """Создать вкладку истории отчётов"""
        # Заголовок
        tk.Label(parent, text="История Созданных Отчётов",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)
        
        # Статистика
        stats_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        stats_frame.pack(fill='x', padx=20, pady=10)
        
        total_reports = len(self.report_history)
        total_size = sum(float(r['file_size'].split()[0]) for r in self.report_history)
        
        self.create_stat_card(stats_frame, "Всего отчётов", str(total_reports), ModernStyles.COLORS['primary'])
        self.create_stat_card(stats_frame, "Общий размер", f"{total_size:.1f} MB", ModernStyles.COLORS['secondary'])
        self.create_stat_card(stats_frame, "За сегодня", "3", ModernStyles.COLORS['success'])
        
        # Таблица истории
        columns = ('Дата', 'Тип отчёта', 'Период', 'Создал', 'Формат', 'Размер', 'Статус')
        history_tree = ttk.Treeview(parent, columns=columns, show='headings', style="Modern.Treeview")
        
        for col in columns:
            history_tree.heading(col, text=col)
            history_tree.column(col, width=120)
        
        # Заполнить историей
        for report in self.report_history:
            status_icon = "✅" if report['status'] == 'Готов' else "⏳"
            
            history_tree.insert('', 'end', values=(
                report['date'],
                report['type'],
                report['period'],
                report['generated_by'],
                report['format'],
                report['file_size'],
                f"{status_icon} {report['status']}"
            ))
        
        history_tree.pack(fill='both', expand=True, padx=10, pady=10)
    
    def create_templates_tab(self, parent):
        """Создать вкладку шаблонов"""
        # Заголовок
        tk.Label(parent, text="Шаблоны Отчётов",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)
        
        # Список шаблонов
        templates_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        templates_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        for i, template in enumerate(self.report_templates):
            template_card = tk.Frame(templates_frame, bg=ModernStyles.COLORS['bg_card'], relief='solid', bd=1)
            template_card.pack(fill='x', pady=10)
            
            # Заголовок шаблона
            header = tk.Frame(template_card, bg=ModernStyles.COLORS['primary'])
            header.pack(fill='x')
            
            tk.Label(header, text=f"📄 {template['name']}", font=('Arial', 12, 'bold'),
                    bg=ModernStyles.COLORS['primary'], fg='white').pack(side='left', padx=15, pady=10)
            
            tk.Button(header, text="Использовать", command=lambda t=template: self.use_template(t),
                     bg=ModernStyles.COLORS['success'], fg='white',
                     font=('Arial', 9, 'bold'), relief='flat', padx=10, pady=5).pack(side='right', padx=15, pady=5)
            
            # Описание
            tk.Label(template_card, text=template['description'], font=('Arial', 10),
                    bg=ModernStyles.COLORS['bg_card']).pack(anchor='w', padx=15, pady=10)
    
    def create_scheduler_tab(self, parent):
        """Создать вкладку планировщика"""
        # Заголовок
        tk.Label(parent, text="Планировщик Отчётов",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=10)
        
        # Запланированные отчёты
        scheduled_frame = tk.LabelFrame(parent, text="Запланированные Отчёты",
                                       font=('Arial', 12, 'bold'), bg=ModernStyles.COLORS['bg_main'])
        scheduled_frame.pack(fill='x', padx=20, pady=10)
        
        scheduled_reports = [
            {"name": "Ежедневный отчёт по продажам", "time": "09:00", "recipients": "Директор"},
            {"name": "Еженедельный отчёт по складу", "time": "Понедельник 08:00", "recipients": "Заведующий складом"},
            {"name": "Месячный финансовый отчёт", "time": "1 число 10:00", "recipients": "Бухгалтер, Директор"}
        ]
        
        for report in scheduled_reports:
            row = tk.Frame(scheduled_frame, bg=ModernStyles.COLORS['bg_main'])
            row.pack(fill='x', padx=10, pady=5)
            
            tk.Label(row, text=f"⏰ {report['name']}", font=('Arial', 11, 'bold'),
                    bg=ModernStyles.COLORS['bg_main']).pack(side='left')
            
            tk.Label(row, text=f"{report['time']} → {report['recipients']}", font=('Arial', 10),
                    bg=ModernStyles.COLORS['bg_main']).pack(side='right')
        
        # Кнопки управления планировщиком
        scheduler_btn_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_main'])
        scheduler_btn_frame.pack(fill='x', padx=20, pady=20)
        
        tk.Button(scheduler_btn_frame, text="➕ Добавить Расписание", command=self.add_schedule,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(side='left')
        
        tk.Button(scheduler_btn_frame, text="✏️ Редактировать", command=self.edit_schedule,
                 bg=ModernStyles.COLORS['primary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(side='left', padx=10)
    
    def create_stat_card(self, parent, title, value, color):
        """Создать карточку статистики"""
        card = tk.Frame(parent, bg=color, relief='solid', bd=1)
        card.pack(side='left', fill='both', expand=True, padx=5)
        
        tk.Label(card, text=title, font=('Arial', 10), bg=color, fg='white').pack(pady=(15, 5))
        tk.Label(card, text=value, font=('Arial', 14, 'bold'), bg=color, fg='white').pack(pady=(0, 15))
    
    def create_report(self):
        """Создать новый отчёт"""
        create_window = tk.Toplevel(self.window)
        create_window.title("📊 Создание Отчёта")
        create_window.geometry("500x400")
        create_window.configure(bg=ModernStyles.COLORS['bg_main'])
        
        # Заголовок
        tk.Label(create_window, text="Создание Нового Отчёта",
                font=('Arial', 14, 'bold'), bg=ModernStyles.COLORS['bg_main']).pack(pady=20)
        
        # Форма создания
        form_frame = tk.Frame(create_window, bg=ModernStyles.COLORS['bg_main'])
        form_frame.pack(fill='both', expand=True, padx=20)
        
        # Тип отчёта
        tk.Label(form_frame, text="Тип отчёта:", font=('Arial', 10, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).grid(row=0, column=0, sticky='w', pady=5)
        
        all_reports = []
        for reports in self.report_types.values():
            all_reports.extend([r['name'] for r in reports])
        
        report_combo = ttk.Combobox(form_frame, values=all_reports)
        report_combo.grid(row=0, column=1, sticky='ew', padx=(10, 0), pady=5)
        
        # Период
        tk.Label(form_frame, text="Период:", font=('Arial', 10, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).grid(row=1, column=0, sticky='w', pady=5)
        period_combo = ttk.Combobox(form_frame, values=["Сегодня", "Вчера", "Эта неделя", "Прошлая неделя", "Этот месяц", "Прошлый месяц"])
        period_combo.grid(row=1, column=1, sticky='ew', padx=(10, 0), pady=5)
        
        # Формат
        tk.Label(form_frame, text="Формат:", font=('Arial', 10, 'bold'),
                bg=ModernStyles.COLORS['bg_main']).grid(row=2, column=0, sticky='w', pady=5)
        format_combo = ttk.Combobox(form_frame, values=["PDF", "Excel", "Word", "CSV"])
        format_combo.grid(row=2, column=1, sticky='ew', padx=(10, 0), pady=5)
        
        form_frame.grid_columnconfigure(1, weight=1)
        
        # Кнопки
        btn_frame = tk.Frame(create_window, bg=ModernStyles.COLORS['bg_main'])
        btn_frame.pack(fill='x', padx=20, pady=20)
        
        def generate_report():
            messagebox.showinfo("Отчёт создан", f"Отчёт '{report_combo.get()}' успешно создан!")
            create_window.destroy()
        
        tk.Button(btn_frame, text="📊 Создать", command=generate_report,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(side='left')
        
        tk.Button(btn_frame, text="❌ Отмена", command=create_window.destroy,
                 bg=ModernStyles.COLORS['danger'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=20, pady=8).pack(side='right')
    
    def use_template(self, template):
        """Использовать шаблон"""
        messagebox.showinfo("Шаблон", f"Выбран шаблон: {template['name']}")
    
    def schedule_reports(self):
        """Планировщик отчётов"""
        messagebox.showinfo("Планировщик", "Планировщик отчётов открыт")
    
    def add_schedule(self):
        """Добавить расписание отчета"""
        try:
            from utils.window_utils import create_centered_dialog
            schedule_window = create_centered_dialog(
                self.window,
                "📅 Добавить Расписание Отчета",
                width=600,
                height=500,
                resizable=True
            )
        except ImportError:
            schedule_window = tk.Toplevel(self.window)
            schedule_window.title("📅 Добавить Расписание Отчета")
            schedule_window.geometry("600x500")
            schedule_window.configure(bg='white')

            # Центрировать окно
            schedule_window.update_idletasks()
            x = (schedule_window.winfo_screenwidth() // 2) - (600 // 2)
            y = (schedule_window.winfo_screenheight() // 2) - (500 // 2)
            schedule_window.geometry(f"600x500+{x}+{y}")

        # Заголовок
        tk.Label(schedule_window, text="📅 Создание Расписания Отчета",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Форма
        form_frame = tk.Frame(schedule_window, bg='white')
        form_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # Поля формы
        fields = [
            ("Название отчета:", "report_name"),
            ("Тип отчета:", "report_type"),
            ("Периодичность:", "frequency"),
            ("Время выполнения:", "execution_time"),
            ("Email получателей:", "recipients"),
            ("Описание:", "description")
        ]

        entries = {}
        for i, (label, field) in enumerate(fields):
            tk.Label(form_frame, text=label, font=('Cambria', 12, 'bold'),
                    bg='white').grid(row=i, column=0, sticky='w', pady=10)

            if field == "report_type":
                entry = ttk.Combobox(form_frame, font=('Cambria', 11), width=30)
                entry['values'] = ['Продажи', 'Склад', 'Финансы', 'Персонал', 'Клиенты']
            elif field == "frequency":
                entry = ttk.Combobox(form_frame, font=('Cambria', 11), width=30)
                entry['values'] = ['Ежедневно', 'Еженедельно', 'Ежемесячно', 'Ежеквартально']
            elif field == "description":
                entry = tk.Text(form_frame, font=('Cambria', 11), height=4, width=30)
            else:
                entry = tk.Entry(form_frame, font=('Cambria', 11), width=30)

            entry.grid(row=i, column=1, sticky='ew', padx=(10, 0), pady=10)
            entries[field] = entry

        form_frame.grid_columnconfigure(1, weight=1)

        # Кнопки
        btn_frame = tk.Frame(schedule_window, bg='white')
        btn_frame.pack(fill='x', padx=30, pady=20)

        def save_schedule():
            try:
                schedule_data = {
                    'name': entries['report_name'].get(),
                    'type': entries['report_type'].get(),
                    'frequency': entries['frequency'].get(),
                    'time': entries['execution_time'].get(),
                    'recipients': entries['recipients'].get(),
                    'description': entries['description'].get('1.0', 'end-1c') if hasattr(entries['description'], 'get') else entries['description'].get(),
                    'created': datetime.now().strftime('%Y-%m-%d %H:%M'),
                    'status': 'Активно'
                }

                # Здесь можно сохранить в базу данных
                messagebox.showinfo("Успех", f"Расписание '{schedule_data['name']}' создано успешно!")
                schedule_window.destroy()

            except Exception as e:
                messagebox.showerror("Ошибка", f"Ошибка создания расписания: {e}")

        tk.Button(btn_frame, text="💾 Сохранить", command=save_schedule,
                 bg='#27ae60', fg='white', font=('Cambria', 12, 'bold'),
                 relief='flat', padx=20, pady=8).pack(side='left')

        tk.Button(btn_frame, text="❌ Отмена", command=schedule_window.destroy,
                 bg='#e74c3c', fg='white', font=('Cambria', 12, 'bold'),
                 relief='flat', padx=20, pady=8).pack(side='right')

    def edit_schedule(self):
        """Редактировать расписание"""
        try:
            from utils.window_utils import create_centered_dialog
            edit_window = create_centered_dialog(
                self.window,
                "✏️ Управление Расписаниями",
                width=800,
                height=600,
                resizable=True
            )
        except ImportError:
            edit_window = tk.Toplevel(self.window)
            edit_window.title("✏️ Управление Расписаниями")
            edit_window.geometry("800x600")
            edit_window.configure(bg='white')

            # Центрировать окно
            edit_window.update_idletasks()
            x = (edit_window.winfo_screenwidth() // 2) - (800 // 2)
            y = (edit_window.winfo_screenheight() // 2) - (600 // 2)
            edit_window.geometry(f"800x600+{x}+{y}")

        # Заголовок
        tk.Label(edit_window, text="✏️ Управление Расписаниями Отчетов",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Таблица расписаний
        columns = ('ID', 'Название', 'Тип', 'Периодичность', 'Время', 'Статус', 'Создано')
        schedule_tree = ttk.Treeview(edit_window, columns=columns, show='headings', height=15)

        # Настройка колонок
        column_widths = {'ID': 50, 'Название': 150, 'Тип': 100, 'Периодичность': 120, 'Время': 80, 'Статус': 80, 'Создано': 120}
        for col in columns:
            schedule_tree.heading(col, text=col)
            schedule_tree.column(col, width=column_widths[col])

        # Примеры данных
        sample_schedules = [
            (1, "Ежедневные продажи", "Продажи", "Ежедневно", "09:00", "🟢 Активно", "2024-01-10"),
            (2, "Недельный склад", "Склад", "Еженедельно", "08:00", "🟢 Активно", "2024-01-08"),
            (3, "Месячная прибыль", "Финансы", "Ежемесячно", "10:00", "🟡 Пауза", "2024-01-01"),
            (4, "Отчет по персоналу", "Персонал", "Еженедельно", "17:00", "🟢 Активно", "2024-01-05"),
            (5, "Анализ клиентов", "Клиенты", "Ежемесячно", "12:00", "🔴 Остановлен", "2023-12-15")
        ]

        for schedule in sample_schedules:
            schedule_tree.insert('', 'end', values=schedule)

        schedule_tree.pack(fill='both', expand=True, padx=20, pady=20)

        # Кнопки управления
        btn_frame = tk.Frame(edit_window, bg='white')
        btn_frame.pack(fill='x', padx=20, pady=20)

        tk.Button(btn_frame, text="▶️ Запустить", command=lambda: messagebox.showinfo("Запуск", "Расписание запущено"),
                 bg='#27ae60', fg='white', font=('Cambria', 10, 'bold'),
                 relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(btn_frame, text="⏸️ Пауза", command=lambda: messagebox.showinfo("Пауза", "Расписание приостановлено"),
                 bg='#f39c12', fg='white', font=('Cambria', 10, 'bold'),
                 relief='flat', padx=15, pady=5).pack(side='left', padx=5)

        tk.Button(btn_frame, text="🗑️ Удалить", command=lambda: messagebox.showinfo("Удаление", "Расписание удалено"),
                 bg='#e74c3c', fg='white', font=('Cambria', 10, 'bold'),
                 relief='flat', padx=15, pady=5).pack(side='left', padx=5)
    
    def export_reports(self):
        """Экспорт отчётов"""
        messagebox.showinfo("Экспорт", "Отчёты экспортированы в папку exports/")

def create_reports_system(parent, db_manager):
    """Создать систему отчётов"""
    system = ReportsSystem(parent, db_manager)
    system.create_window()
    return system
