"""
Профессиональная система отчётов с красивыми таблицами
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
import csv
import json
from gui.styles import ModernStyles

class ReportsManager:
    """Менеджер отчётов"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None

    def format_currency(self, amount):
        """Форматировать сумму в российском формате: 25 952,59 руб"""
        try:
            if amount is None:
                amount = 0
            amount = float(amount)
            formatted = f"{amount:,.2f}".replace(',', ' ').replace('.', ',')
            return f"{formatted} руб"
        except:
            return "0,00 руб"

    def convert_old_currency_format(self, text):
        """Конвертировать старый формат валюты (₽) в новый российский формат"""
        import re

        # Найти все числа с символом ₽
        pattern = r'(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)₽'

        def replace_currency(match):
            amount_str = match.group(1)
            # Преобразовать в число
            amount = float(amount_str.replace(',', ''))
            # Вернуть в российском формате
            return self.format_currency(amount)

        return re.sub(pattern, replace_currency, text)

    def create_reports_window(self):
        """Создать окно отчётов"""
        try:
            from utils.window_utils import create_centered_dialog
            self.window = create_centered_dialog(
                self.parent,
                "📊 Система Отчётов",
                width=1450,
                height=950,
                resizable=True
            )
            self.window.configure(bg='white')
        except ImportError:
            # Fallback если utils не доступны
            self.window = tk.Toplevel(self.parent)
            self.window.title("📊 Система Отчётов")
            self.window.geometry("1450x950")
            self.window.configure(bg='white')
            self.window.resizable(True, True)

            # Центрировать окно
            self.window.update_idletasks()
            x = (self.window.winfo_screenwidth() // 2) - (1450 // 2)
            y = (self.window.winfo_screenheight() // 2) - (950 // 2)
            self.window.geometry(f"1450x950+{x}+{y}")
        self.window.resizable(True, True)
        
        self.create_interface()
        return self.window
    
    def create_interface(self):
        """Создать интерфейс"""
        # Заголовок
        header_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['primary'], height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="📊 Система Отчётов",
                font=('Arial', 16, 'bold'), bg=ModernStyles.COLORS['primary'], 
                fg='white').pack(side='left', padx=20, pady=15)
        
        # Кнопки действий
        btn_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['primary'])
        btn_frame.pack(side='right', padx=20, pady=10)
        
        tk.Button(btn_frame, text="📤 Экспорт", command=self.export_report,
                 bg=ModernStyles.COLORS['success'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="🔄 Обновить", command=self.refresh_data,
                 bg=ModernStyles.COLORS['secondary'], fg='white',
                 font=('Arial', 10, 'bold'), relief='flat', padx=15, pady=5).pack(side='left', padx=5)
        
        # Основной контент
        main_frame = tk.Frame(self.window, bg='white')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Создать вкладки
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill='both', expand=True)
        
        # Вкладка "Отчёты по продажам"
        sales_frame = tk.Frame(notebook, bg='white')
        notebook.add(sales_frame, text="📈 Продажи")
        self.create_sales_tab(sales_frame)
        
        # Вкладка "Отчёты по складу"
        inventory_frame = tk.Frame(notebook, bg='white')
        notebook.add(inventory_frame, text="📦 Склад")
        self.create_inventory_tab(inventory_frame)
        
        # Вкладка "Финансовые отчёты"
        financial_frame = tk.Frame(notebook, bg='white')
        notebook.add(financial_frame, text="💰 Финансы")
        self.create_financial_tab(financial_frame)
        
        # Вкладка "Операционные отчёты"
        operations_frame = tk.Frame(notebook, bg='white')
        notebook.add(operations_frame, text="⚙️ Операции")
        self.create_operations_tab(operations_frame)
        
        # Вкладка "Аналитика"
        analytics_frame = tk.Frame(notebook, bg='white')
        notebook.add(analytics_frame, text="📊 Аналитика")
        self.create_analytics_tab(analytics_frame)
    
    def create_sales_tab(self, parent):
        """Создать вкладку отчётов по продажам"""
        # Заголовок
        header_frame = tk.Frame(parent, bg='#2c3e50', height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="📈 Отчёты по Продажам",
                font=('Cambria', 18, 'bold italic'), bg='#2c3e50', fg='white').pack(side='left', padx=30, pady=20)

        # Контролы
        controls_frame = tk.Frame(parent, bg='#ecf0f1', relief='flat', bd=0)
        controls_frame.pack(fill='x', padx=0, pady=0)

        # Период и фильтры
        filter_frame = tk.Frame(controls_frame, bg='#ecf0f1')
        filter_frame.pack(fill='x', padx=30, pady=20)

        # Период
        tk.Label(filter_frame, text="Период:", font=('Cambria', 12, 'bold'),
                bg='#ecf0f1').grid(row=0, column=0, sticky='w', padx=(0, 10))

        self.period_var = tk.StringVar(value="Последние 30 дней")
        period_combo = ttk.Combobox(filter_frame, textvariable=self.period_var,
                                   values=["Сегодня", "Вчера", "Последние 7 дней", "Последние 30 дней", "Текущий месяц"],
                                   width=20, font=('Cambria', 11))
        period_combo.grid(row=0, column=1, padx=10)

        # Категория
        tk.Label(filter_frame, text="Категория:", font=('Cambria', 12, 'bold'),
                bg='#ecf0f1').grid(row=0, column=2, sticky='w', padx=(20, 10))

        self.category_var = tk.StringVar(value="Все категории")
        category_combo = ttk.Combobox(filter_frame, textvariable=self.category_var,
                                     values=["Все категории", "Горячие блюда", "Салаты", "Супы", "Десерты", "Напитки"],
                                     width=15, font=('Cambria', 11))
        category_combo.grid(row=0, column=3, padx=10)

        # Кнопки
        tk.Button(filter_frame, text="📊 Сформировать отчёт",
                 command=self.generate_sales_report,
                 bg='#27ae60', fg='white', font=('Cambria', 12, 'bold italic'),
                 relief='flat', padx=20, pady=8).grid(row=0, column=4, padx=20)

        tk.Button(filter_frame, text="📤 Экспорт Excel",
                 command=self.export_sales_report,
                 bg='#3498db', fg='white', font=('Cambria', 12, 'bold italic'),
                 relief='flat', padx=20, pady=8).grid(row=0, column=5, padx=10)

        # Область отчёта
        self.sales_report_frame = tk.Frame(parent, bg='white')
        self.sales_report_frame.pack(fill='both', expand=True, padx=0, pady=0)

        # Показать начальный отчёт
        self.generate_sales_report()
    
    def create_inventory_tab(self, parent):
        """Создать вкладку отчётов по складу"""
        # Заголовок
        header_frame = tk.Frame(parent, bg='#34495e', height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="📦 Отчёты по Складу",
                font=('Cambria', 18, 'bold italic'), bg='#34495e', fg='white').pack(side='left', padx=30, pady=20)

        # Кнопки отчётов
        buttons_frame = tk.Frame(parent, bg='#ecf0f1', relief='flat', bd=0)
        buttons_frame.pack(fill='x', padx=0, pady=0)

        btn_row1 = tk.Frame(buttons_frame, bg='#ecf0f1')
        btn_row1.pack(fill='x', padx=30, pady=20)

        tk.Button(btn_row1, text="📊 Текущие остатки",
                 command=lambda: self.show_inventory_report("current_stock"),
                 bg='#27ae60', fg='white', font=('Cambria', 12, 'bold italic'),
                 relief='flat', padx=20, pady=8).pack(side='left', padx=10)

        tk.Button(btn_row1, text="⚠️ Критические остатки",
                 command=lambda: self.show_inventory_report("low_stock"),
                 bg='#e74c3c', fg='white', font=('Cambria', 12, 'bold italic'),
                 relief='flat', padx=20, pady=8).pack(side='left', padx=10)

        tk.Button(btn_row1, text="📈 Движение товаров",
                 command=lambda: self.show_inventory_report("movement"),
                 bg='#3498db', fg='white', font=('Cambria', 12, 'bold italic'),
                 relief='flat', padx=20, pady=8).pack(side='left', padx=10)

        tk.Button(btn_row1, text="📤 Экспорт Excel",
                 command=self.export_inventory_report,
                 bg='#9b59b6', fg='white', font=('Cambria', 12, 'bold italic'),
                 relief='flat', padx=20, pady=8).pack(side='left', padx=10)

        # Область отчёта
        self.inventory_report_frame = tk.Frame(parent, bg='white')
        self.inventory_report_frame.pack(fill='both', expand=True, padx=0, pady=0)

        # Показать начальный отчёт
        self.show_inventory_report("current_stock")
    
    def create_financial_tab(self, parent):
        """Создать вкладку финансовых отчётов"""
        # Заголовок
        header_frame = tk.Frame(parent, bg='#8e44ad', height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="💰 Финансовые Отчёты",
                font=('Cambria', 18, 'bold italic'), bg='#8e44ad', fg='white').pack(side='left', padx=30, pady=20)

        # Создать notebook для финансовых отчётов
        financial_notebook = ttk.Notebook(parent)
        financial_notebook.pack(fill='both', expand=True, padx=20, pady=20)

        # Вкладка P&L
        pnl_frame = tk.Frame(financial_notebook, bg='white')
        financial_notebook.add(pnl_frame, text="📊 P&L Отчёт")
        self.create_pnl_table(pnl_frame)

        # Вкладка Cash Flow
        cashflow_frame = tk.Frame(financial_notebook, bg='white')
        financial_notebook.add(cashflow_frame, text="💸 Cash Flow")
        self.create_cashflow_table(cashflow_frame)

        # Вкладка Показатели
        metrics_frame = tk.Frame(financial_notebook, bg='white')
        financial_notebook.add(metrics_frame, text="📈 Показатели")
        self.create_financial_metrics_table(metrics_frame)

    def create_pnl_table(self, parent):
        """Создать таблицу P&L отчёта"""
        # Заголовок
        tk.Label(parent, text="📊 Отчёт о прибылях и убытках за май 2024",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Таблица P&L
        pnl_frame = tk.Frame(parent, bg='white')
        pnl_frame.pack(fill='both', expand=True, padx=40, pady=20)

        # Создать таблицу
        pnl_columns = ('Статья', 'Сумма', 'Доля %', 'Прошлый месяц', 'Изменение', 'Тренд')
        pnl_tree = ttk.Treeview(pnl_frame, columns=pnl_columns, show='headings', height=15)

        # Настройка колонок
        column_widths = {'Статья': 200, 'Сумма': 120, 'Доля %': 80, 'Прошлый месяц': 120, 'Изменение': 100, 'Тренд': 80}
        for col in pnl_columns:
            pnl_tree.heading(col, text=col)
            pnl_tree.column(col, width=column_widths[col], anchor='center')

        # Данные P&L (обновлено с правильным форматом валюты)
        pnl_data = [
            ("📈 ДОХОДЫ", "", "", "", "", ""),
            ("Продажи блюд", "485 600,00 руб", "77.3%", "448 200,00 руб", "+37 400,00 руб", "📈 +8.3%"),
            ("Продажи напитков", "124 800,00 руб", "19.9%", "118 600,00 руб", "+6 200,00 руб", "📈 +5.2%"),
            ("Дополнительные услуги", "18 200,00 руб", "2.9%", "16 800,00 руб", "+1 400,00 руб", "📈 +8.3%"),
            ("ИТОГО ДОХОДЫ", "628 600,00 руб", "100.0%", "583 600,00 руб", "+45 000,00 руб", "📈 +7.7%"),
            ("", "", "", "", "", ""),
            ("📉 РАСХОДЫ", "", "", "", "", ""),
            ("Продукты питания", "312 400,00 руб", "49.7%", "319 200,00 руб", "-6 800,00 руб", "📉 -2.1%"),
            ("Заработная плата", "156 200,00 руб", "24.8%", "152 400,00 руб", "+3 800,00 руб", "📈 +2.5%"),
            ("Аренда помещения", "45 000,00 руб", "7.2%", "45 000,00 руб", "0,00 руб", "📊 0.0%"),
            ("Коммунальные услуги", "18 500,00 руб", "2.9%", "17 200,00 руб", "+1 300,00 руб", "📈 +7.6%"),
            ("Маркетинг и реклама", "8 200,00 руб", "1.3%", "6 800,00 руб", "+1 400,00 руб", "📈 +20.6%"),
            ("Прочие расходы", "4 100,00 руб", "0.7%", "3 900,00 руб", "+200,00 руб", "📈 +5.1%"),
            ("ИТОГО РАСХОДЫ", "544 400,00 руб", "86.6%", "544 500,00 руб", "-100,00 руб", "📉 -0.0%"),
            ("", "", "", "", "", ""),
            ("💰 ПРИБЫЛЬ", "", "", "", "", ""),
            ("Валовая прибыль", "84 200,00 руб", "13.4%", "39 100,00 руб", "+45 100,00 руб", "📈 +115.3%"),
            ("Налоги (20%)", "16 840,00 руб", "2.7%", "7 820,00 руб", "+9 020,00 руб", "📈 +115.3%"),
            ("ЧИСТАЯ ПРИБЫЛЬ", "67 360,00 руб", "10.7%", "31 280,00 руб", "+36 080,00 руб", "📈 +115.3%")
        ]

        for data in pnl_data:
            pnl_tree.insert('', 'end', values=data)

        # Скроллбар
        pnl_scrollbar = ttk.Scrollbar(pnl_frame, orient='vertical', command=pnl_tree.yview)
        pnl_tree.configure(yscrollcommand=pnl_scrollbar.set)

        pnl_tree.pack(side='left', fill='both', expand=True)
        pnl_scrollbar.pack(side='right', fill='y')

    def create_cashflow_table(self, parent):
        """Создать таблицу движения денежных средств"""
        # Заголовок
        tk.Label(parent, text="💸 Движение денежных средств за май 2024",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Таблица Cash Flow
        cf_frame = tk.Frame(parent, bg='white')
        cf_frame.pack(fill='both', expand=True, padx=40, pady=20)

        # Создать таблицу
        cf_columns = ('Категория', 'Поступления', 'Выплаты', 'Чистый поток', 'Накопительно')
        cf_tree = ttk.Treeview(cf_frame, columns=cf_columns, show='headings', height=12)

        # Настройка колонок
        column_widths = {'Категория': 200, 'Поступления': 120, 'Выплаты': 120, 'Чистый поток': 120, 'Накопительно': 120}
        for col in cf_columns:
            cf_tree.heading(col, text=col)
            cf_tree.column(col, width=column_widths[col], anchor='center')

        # Данные Cash Flow
        cf_data = [
            ("💰 ОПЕРАЦИОННАЯ ДЕЯТЕЛЬНОСТЬ", "", "", "", ""),
            ("Поступления от продаж", "628,600₽", "0₽", "+628,600₽", "+628,600₽"),
            ("Закупка продуктов", "0₽", "312,400₽", "-312,400₽", "+316,200₽"),
            ("Зарплата персонала", "0₽", "156,200₽", "-156,200₽", "+160,000₽"),
            ("Аренда", "0₽", "45,000₽", "-45,000₽", "+115,000₽"),
            ("Коммунальные услуги", "0₽", "18,500₽", "-18,500₽", "+96,500₽"),
            ("Прочие операционные", "0₽", "12,300₽", "-12,300₽", "+84,200₽"),
            ("", "", "", "", ""),
            ("🏗️ ИНВЕСТИЦИОННАЯ ДЕЯТЕЛЬНОСТЬ", "", "", "", ""),
            ("Покупка оборудования", "0₽", "25,000₽", "-25,000₽", "+59,200₽"),
            ("Ремонт помещения", "0₽", "8,500₽", "-8,500₽", "+50,700₽"),
            ("", "", "", "", ""),
            ("💳 ФИНАНСОВАЯ ДЕЯТЕЛЬНОСТЬ", "", "", "", ""),
            ("Кредитные поступления", "0₽", "0₽", "0₽", "+50,700₽"),
            ("Выплаты по кредитам", "0₽", "15,000₽", "-15,000₽", "+35,700₽"),
            ("Налоги", "0₽", "16,840₽", "-16,840₽", "+18,860₽"),
            ("", "", "", "", ""),
            ("💰 ИТОГО", "628,600₽", "609,740₽", "+18,860₽", "+18,860₽")
        ]

        for data in cf_data:
            cf_tree.insert('', 'end', values=data)

        # Скроллбар
        cf_scrollbar = ttk.Scrollbar(cf_frame, orient='vertical', command=cf_tree.yview)
        cf_tree.configure(yscrollcommand=cf_scrollbar.set)

        cf_tree.pack(side='left', fill='both', expand=True)
        cf_scrollbar.pack(side='right', fill='y')

    def create_financial_metrics_table(self, parent):
        """Создать таблицу финансовых показателей"""
        # Заголовок
        tk.Label(parent, text="📈 Ключевые финансовые показатели",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Таблица показателей
        metrics_frame = tk.Frame(parent, bg='white')
        metrics_frame.pack(fill='both', expand=True, padx=40, pady=20)

        # Создать таблицу
        metrics_columns = ('Показатель', 'Значение', 'Норма', 'Статус', 'Прошлый месяц', 'Динамика')
        metrics_tree = ttk.Treeview(metrics_frame, columns=metrics_columns, show='headings', height=12)

        # Настройка колонок
        column_widths = {'Показатель': 200, 'Значение': 100, 'Норма': 100, 'Статус': 100, 'Прошлый месяц': 120, 'Динамика': 100}
        for col in metrics_columns:
            metrics_tree.heading(col, text=col)
            metrics_tree.column(col, width=column_widths[col], anchor='center')

        # Данные показателей
        metrics_data = [
            ("💰 РЕНТАБЕЛЬНОСТЬ", "", "", "", "", ""),
            ("Валовая рентабельность", "13.4%", ">15%", "🟡 Ниже нормы", "6.7%", "📈 +6.7%"),
            ("Чистая рентабельность", "10.7%", ">10%", "🟢 Норма", "5.4%", "📈 +5.3%"),
            ("Рентабельность продаж", "10.7%", ">8%", "🟢 Хорошо", "5.4%", "📈 +5.3%"),
            ("", "", "", "", "", ""),
            ("📊 ЭФФЕКТИВНОСТЬ", "", "", "", "", ""),
            ("Средний чек", "385₽", ">350₽", "🟢 Хорошо", "365₽", "📈 +5.5%"),
            ("Оборачиваемость активов", "2.3", ">2.0", "🟢 Хорошо", "2.1", "📈 +9.5%"),
            ("Производительность труда", "26,192₽", ">25,000₽", "🟢 Хорошо", "24,317₽", "📈 +7.7%"),
            ("", "", "", "", "", ""),
            ("💳 ЛИКВИДНОСТЬ", "", "", "", "", ""),
            ("Денежные средства", "156,400₽", ">100,000₽", "🟢 Хорошо", "137,540₽", "📈 +13.7%"),
            ("Коэффициент покрытия", "1.8", ">1.5", "🟢 Хорошо", "1.6", "📈 +12.5%"),
            ("", "", "", "", "", ""),
            ("🎯 ОПЕРАЦИОННЫЕ", "", "", "", "", ""),
            ("Food Cost %", "49.7%", "<50%", "🟢 Хорошо", "54.7%", "📉 -5.0%"),
            ("Labor Cost %", "24.8%", "<30%", "🟢 Хорошо", "26.1%", "📉 -1.3%"),
            ("Количество чеков", "1,632", ">1,500", "🟢 Хорошо", "1,598", "📈 +2.1%")
        ]

        for data in metrics_data:
            metrics_tree.insert('', 'end', values=data)

        # Скроллбар
        metrics_scrollbar = ttk.Scrollbar(metrics_frame, orient='vertical', command=metrics_tree.yview)
        metrics_tree.configure(yscrollcommand=metrics_scrollbar.set)

        metrics_tree.pack(side='left', fill='both', expand=True)
        metrics_scrollbar.pack(side='right', fill='y')
    
    def create_operations_tab(self, parent):
        """Создать вкладку операционных отчётов"""
        # Заголовок
        header_frame = tk.Frame(parent, bg='#e67e22', height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="⚙️ Операционные Отчёты",
                font=('Cambria', 18, 'bold italic'), bg='#e67e22', fg='white').pack(side='left', padx=30, pady=20)

        # Создать notebook для операционных отчётов
        operations_notebook = ttk.Notebook(parent)
        operations_notebook.pack(fill='both', expand=True, padx=20, pady=20)

        # Вкладка производства
        production_frame = tk.Frame(operations_notebook, bg='white')
        operations_notebook.add(production_frame, text="🍽️ Производство")
        self.create_production_table(production_frame)

        # Вкладка обслуживания
        service_frame = tk.Frame(operations_notebook, bg='white')
        operations_notebook.add(service_frame, text="📋 Обслуживание")
        self.create_service_table(service_frame)

        # Вкладка оборудования
        equipment_frame = tk.Frame(operations_notebook, bg='white')
        operations_notebook.add(equipment_frame, text="🔧 Оборудование")
        self.create_equipment_table(equipment_frame)

        # Вкладка KPI
        kpi_frame = tk.Frame(operations_notebook, bg='white')
        operations_notebook.add(kpi_frame, text="📊 KPI")
        self.create_operations_kpi_table(kpi_frame)

    def create_production_table(self, parent):
        """Создать таблицу производства"""
        # Заголовок
        tk.Label(parent, text="🍽️ Отчёт по производству за май 2024",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Таблица производства
        production_frame = tk.Frame(parent, bg='white')
        production_frame.pack(fill='both', expand=True, padx=40, pady=20)

        # Создать таблицу
        production_columns = ('Блюдо', 'Приготовлено', 'Время готовки', 'Возвраты', '% возвратов', 'Рейтинг', 'Статус')
        production_tree = ttk.Treeview(production_frame, columns=production_columns, show='headings', height=12)

        # Настройка колонок
        column_widths = {'Блюдо': 180, 'Приготовлено': 100, 'Время готовки': 100, 'Возвраты': 80, '% возвратов': 100, 'Рейтинг': 100, 'Статус': 120}
        for col in production_columns:
            production_tree.heading(col, text=col)
            production_tree.column(col, width=column_widths[col], anchor='center')

        # Данные производства
        production_data = [
            ('Стейк Рибай', '156', '18 мин', '2', '1.3%', '⭐⭐⭐⭐⭐', '🟢 Отлично'),
            ('Борщ Украинский', '134', '25 мин', '1', '0.7%', '⭐⭐⭐⭐⭐', '🟢 Отлично'),
            ('Салат Цезарь', '128', '8 мин', '0', '0.0%', '⭐⭐⭐⭐⭐', '🟢 Отлично'),
            ('Котлета по-Киевски', '98', '22 мин', '3', '3.1%', '⭐⭐⭐⭐', '🟡 Хорошо'),
            ('Пельмени Домашние', '87', '15 мин', '1', '1.1%', '⭐⭐⭐⭐', '🟢 Хорошо'),
            ('Тирамису', '76', '12 мин', '0', '0.0%', '⭐⭐⭐⭐⭐', '🟢 Отлично'),
            ('Солянка Мясная', '72', '30 мин', '2', '2.8%', '⭐⭐⭐⭐', '🟡 Хорошо'),
            ('Оливье', '68', '10 мин', '0', '0.0%', '⭐⭐⭐⭐', '🟢 Хорошо'),
            ('Шашлык из Свинины', '65', '35 мин', '4', '6.2%', '⭐⭐⭐', '🔴 Требует внимания'),
            ('Чизкейк', '62', '15 мин', '1', '1.6%', '⭐⭐⭐⭐', '🟢 Хорошо'),
            ('Греческий Салат', '58', '7 мин', '0', '0.0%', '⭐⭐⭐⭐⭐', '🟢 Отлично'),
            ('Лагман', '54', '28 мин', '3', '5.6%', '⭐⭐⭐', '🟡 Средне')
        ]

        for data in production_data:
            production_tree.insert('', 'end', values=data)

        # Скроллбар
        production_scrollbar = ttk.Scrollbar(production_frame, orient='vertical', command=production_tree.yview)
        production_tree.configure(yscrollcommand=production_scrollbar.set)

        production_tree.pack(side='left', fill='both', expand=True)
        production_scrollbar.pack(side='right', fill='y')

        # Итоги
        totals_frame = tk.Frame(parent, bg='#ecf0f1', relief='flat', bd=1)
        totals_frame.pack(fill='x', padx=40, pady=20)

        tk.Label(totals_frame, text="📊 Всего приготовлено: 1,158 блюд",
                font=('Cambria', 12, 'bold'), bg='#ecf0f1').pack(side='left', padx=20, pady=10)

        tk.Label(totals_frame, text="⚠️ Общий % возвратов: 1.5%",
                font=('Cambria', 12, 'bold'), bg='#ecf0f1', fg='#e67e22').pack(side='right', padx=20, pady=10)

    def create_service_table(self, parent):
        """Создать таблицу обслуживания"""
        # Заголовок
        tk.Label(parent, text="📋 Отчёт по обслуживанию клиентов",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Таблица обслуживания
        service_frame = tk.Frame(parent, bg='white')
        service_frame.pack(fill='both', expand=True, padx=40, pady=20)

        # Создать таблицу
        service_columns = ('Официант', 'Столов обслужено', 'Среднее время', 'Жалобы', 'Рейтинг', 'Чаевые', 'Статус')
        service_tree = ttk.Treeview(service_frame, columns=service_columns, show='headings', height=10)

        # Настройка колонок
        column_widths = {'Официант': 150, 'Столов обслужено': 120, 'Среднее время': 120, 'Жалобы': 80, 'Рейтинг': 100, 'Чаевые': 100, 'Статус': 120}
        for col in service_columns:
            service_tree.heading(col, text=col)
            service_tree.column(col, width=column_widths[col], anchor='center')

        # Данные обслуживания
        service_data = [
            ('Петрова С.В.', '342', '38 мин', '0', '⭐⭐⭐⭐⭐', '18,400₽', '🟢 Отлично'),
            ('Козлова В.П.', '298', '42 мин', '1', '⭐⭐⭐⭐', '15,200₽', '🟢 Хорошо'),
            ('Волкова М.А.', '276', '45 мин', '2', '⭐⭐⭐⭐', '12,800₽', '🟡 Хорошо'),
            ('Смирнов А.К.', '254', '48 мин', '1', '⭐⭐⭐⭐', '11,600₽', '🟡 Хорошо'),
            ('Иванова Е.С.', '232', '52 мин', '3', '⭐⭐⭐', '9,400₽', '🟡 Средне'),
            ('Николаев П.Д.', '198', '58 мин', '4', '⭐⭐⭐', '7,200₽', '🔴 Требует внимания')
        ]

        for data in service_data:
            service_tree.insert('', 'end', values=data)

        # Скроллбар
        service_scrollbar = ttk.Scrollbar(service_frame, orient='vertical', command=service_tree.yview)
        service_tree.configure(yscrollcommand=service_scrollbar.set)

        service_tree.pack(side='left', fill='both', expand=True)
        service_scrollbar.pack(side='right', fill='y')

        # Итоги
        totals_frame = tk.Frame(parent, bg='#ecf0f1', relief='flat', bd=1)
        totals_frame.pack(fill='x', padx=40, pady=20)

        tk.Label(totals_frame, text="📊 Общий рейтинг сервиса: 4.6/5",
                font=('Cambria', 12, 'bold'), bg='#ecf0f1').pack(side='left', padx=20, pady=10)

        tk.Label(totals_frame, text="💰 Общие чаевые: 74,600₽",
                font=('Cambria', 12, 'bold'), bg='#ecf0f1', fg='#27ae60').pack(side='right', padx=20, pady=10)

    def create_equipment_table(self, parent):
        """Создать таблицу оборудования"""
        # Заголовок
        tk.Label(parent, text="🔧 Отчёт по оборудованию и техническому обслуживанию",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Таблица оборудования
        equipment_frame = tk.Frame(parent, bg='white')
        equipment_frame.pack(fill='both', expand=True, padx=40, pady=20)

        # Создать таблицу
        equipment_columns = ('Оборудование', 'Время работы', 'Последнее ТО', 'Следующее ТО', 'Поломки', 'Простои', 'Статус')
        equipment_tree = ttk.Treeview(equipment_frame, columns=equipment_columns, show='headings', height=12)

        # Настройка колонок
        column_widths = {'Оборудование': 180, 'Время работы': 100, 'Последнее ТО': 100, 'Следующее ТО': 100, 'Поломки': 80, 'Простои': 80, 'Статус': 120}
        for col in equipment_columns:
            equipment_tree.heading(col, text=col)
            equipment_tree.column(col, width=column_widths[col], anchor='center')

        # Данные оборудования
        equipment_data = [
            ('Плита газовая №1', '720 ч', '15.04.24', '15.07.24', '0', '0 ч', '🟢 Отлично'),
            ('Плита газовая №2', '718 ч', '12.04.24', '12.07.24', '0', '0 ч', '🟢 Отлично'),
            ('Духовой шкаф №1', '680 ч', '20.04.24', '20.07.24', '1', '4 ч', '🟡 Хорошо'),
            ('Холодильник №1', '744 ч', '10.04.24', '10.07.24', '0', '0 ч', '🟢 Отлично'),
            ('Холодильник №2', '744 ч', '08.04.24', '08.07.24', '0', '0 ч', '🟢 Отлично'),
            ('Морозильная камера', '744 ч', '05.04.24', '05.07.24', '0', '0 ч', '🟢 Отлично'),
            ('Посудомоечная машина', '520 ч', '18.04.24', '18.07.24', '1', '2 ч', '🟡 Хорошо'),
            ('Кофемашина', '680 ч', '22.04.24', '22.07.24', '0', '0 ч', '🟢 Отлично'),
            ('Миксер планетарный', '240 ч', '25.04.24', '25.07.24', '0', '0 ч', '🟢 Отлично'),
            ('Слайсер', '180 ч', '28.04.24', '28.07.24', '0', '0 ч', '🟢 Отлично'),
            ('Фритюрница', '420 ч', '14.04.24', '14.07.24', '1', '2 ч', '🟡 Хорошо'),
            ('Гриль контактный', '380 ч', '16.04.24', '16.07.24', '0', '0 ч', '🟢 Отлично')
        ]

        for data in equipment_data:
            equipment_tree.insert('', 'end', values=data)

        # Скроллбар
        equipment_scrollbar = ttk.Scrollbar(equipment_frame, orient='vertical', command=equipment_tree.yview)
        equipment_tree.configure(yscrollcommand=equipment_scrollbar.set)

        equipment_tree.pack(side='left', fill='both', expand=True)
        equipment_scrollbar.pack(side='right', fill='y')

        # Итоги
        totals_frame = tk.Frame(parent, bg='#ecf0f1', relief='flat', bd=1)
        totals_frame.pack(fill='x', padx=40, pady=20)

        tk.Label(totals_frame, text="🔧 Общее время работы: 6,970 часов",
                font=('Cambria', 12, 'bold'), bg='#ecf0f1').pack(side='left', padx=20, pady=10)

        tk.Label(totals_frame, text="⚠️ Общие простои: 8 часов (0.1%)",
                font=('Cambria', 12, 'bold'), bg='#ecf0f1', fg='#e67e22').pack(side='right', padx=20, pady=10)

    def create_operations_kpi_table(self, parent):
        """Создать таблицу операционных KPI"""
        # Заголовок
        tk.Label(parent, text="📊 Ключевые операционные показатели (KPI)",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Таблица KPI
        kpi_frame = tk.Frame(parent, bg='white')
        kpi_frame.pack(fill='both', expand=True, padx=40, pady=20)

        # Создать таблицу
        kpi_columns = ('Показатель', 'Текущее значение', 'Цель', 'Статус', 'Прошлый месяц', 'Динамика', 'Тренд')
        kpi_tree = ttk.Treeview(kpi_frame, columns=kpi_columns, show='headings', height=15)

        # Настройка колонок
        column_widths = {'Показатель': 200, 'Текущее значение': 120, 'Цель': 80, 'Статус': 100, 'Прошлый месяц': 120, 'Динамика': 100, 'Тренд': 80}
        for col in kpi_columns:
            kpi_tree.heading(col, text=col)
            kpi_tree.column(col, width=column_widths[col], anchor='center')

        # Данные KPI
        kpi_data = [
            ('🍽️ ПРОИЗВОДСТВО', '', '', '', '', '', ''),
            ('Среднее время приготовления', '12 мин', '≤15 мин', '🟢 Цель достигнута', '13 мин', '-1 мин', '📈'),
            ('% возвратов блюд', '1.5%', '≤2%', '🟢 Цель достигнута', '1.8%', '-0.3%', '📈'),
            ('Загрузка кухни', '78%', '≥75%', '🟢 Цель достигнута', '74%', '+4%', '📈'),
            ('Производительность повара', '48 блюд/смена', '≥45', '🟢 Цель достигнута', '46 блюд', '+2', '📈'),
            ('', '', '', '', '', '', ''),
            ('📋 ОБСЛУЖИВАНИЕ', '', '', '', '', '', ''),
            ('Среднее время обслуживания', '45 мин', '≤50 мин', '🟢 Цель достигнута', '48 мин', '-3 мин', '📈'),
            ('Рейтинг сервиса', '4.6/5', '≥4.5', '🟢 Цель достигнута', '4.4/5', '+0.2', '📈'),
            ('Количество жалоб', '8', '≤10', '🟢 Цель достигнута', '12', '-4', '📈'),
            ('Оборачиваемость столов', '3.2 раза/день', '≥3.0', '🟢 Цель достигнута', '2.9', '+0.3', '📈'),
            ('', '', '', '', '', '', ''),
            ('👥 ПЕРСОНАЛ', '', '', '', '', '', ''),
            ('Производительность на сотрудника', '26,192₽', '≥25,000₽', '🟢 Цель достигнута', '24,317₽', '+1,875₽', '📈'),
            ('Количество опозданий', '12', '≤15', '🟢 Цель достигнута', '18', '-6', '📈'),
            ('Больничные дни', '18', '≤20', '🟢 Цель достигнута', '22', '-4', '📈'),
            ('', '', '', '', '', '', ''),
            ('🔧 ОБОРУДОВАНИЕ', '', '', '', '', '', ''),
            ('Время простоев', '8 ч', '≤12 ч', '🟢 Цель достигнута', '15 ч', '-7 ч', '📈'),
            ('% исправности оборудования', '99.9%', '≥99%', '🟢 Цель достигнута', '99.2%', '+0.7%', '📈'),
            ('Внеплановые ремонты', '2', '≤3', '🟢 Цель достигнута', '4', '-2', '📈')
        ]

        for data in kpi_data:
            kpi_tree.insert('', 'end', values=data)

        # Скроллбар
        kpi_scrollbar = ttk.Scrollbar(kpi_frame, orient='vertical', command=kpi_tree.yview)
        kpi_tree.configure(yscrollcommand=kpi_scrollbar.set)

        kpi_tree.pack(side='left', fill='both', expand=True)
        kpi_scrollbar.pack(side='right', fill='y')

        # Итоги
        totals_frame = tk.Frame(parent, bg='#ecf0f1', relief='flat', bd=1)
        totals_frame.pack(fill='x', padx=40, pady=20)

        tk.Label(totals_frame, text="🎯 Достигнуто целей: 17 из 17 (100%)",
                font=('Cambria', 14, 'bold'), bg='#ecf0f1', fg='#27ae60').pack(pady=15)
    
    def create_analytics_tab(self, parent):
        """Создать вкладку профессиональной аналитики"""
        # Заголовок
        tk.Label(parent, text="📊 Бизнес-Аналитика и Прогнозирование",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Создать notebook для разных типов аналитики
        analytics_notebook = ttk.Notebook(parent)
        analytics_notebook.pack(fill='both', expand=True, padx=20, pady=20)

        # Вкладка трендов продаж
        trends_frame = tk.Frame(analytics_notebook, bg='white')
        analytics_notebook.add(trends_frame, text="📈 Тренды продаж")
        self.create_sales_trends_table(trends_frame)

        # Вкладка анализа клиентов
        customers_frame = tk.Frame(analytics_notebook, bg='white')
        analytics_notebook.add(customers_frame, text="👥 Анализ клиентов")
        self.create_customer_analysis_table(customers_frame)

        # Вкладка финансовых показателей
        financial_frame = tk.Frame(analytics_notebook, bg='white')
        analytics_notebook.add(financial_frame, text="💰 Финансовые показатели")
        self.create_financial_indicators_table(financial_frame)

        # Вкладка прогнозов
        forecast_frame = tk.Frame(analytics_notebook, bg='white')
        analytics_notebook.add(forecast_frame, text="🎯 Прогнозы")
        self.create_forecast_table(forecast_frame)

    def create_sales_trends_table(self, parent):
        """Создать таблицу трендов продаж"""
        # Заголовок
        tk.Label(parent, text="📈 Анализ трендов продаж за май 2024",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Таблица трендов
        trends_frame = tk.Frame(parent, bg='white')
        trends_frame.pack(fill='both', expand=True, padx=40, pady=20)

        # Создать таблицу
        trends_columns = ('Показатель', 'Текущий период', 'Предыдущий период', 'Изменение', 'Тренд', 'Статус')
        trends_tree = ttk.Treeview(trends_frame, columns=trends_columns, show='headings', height=12)

        # Настройка колонок
        column_widths = {'Показатель': 200, 'Текущий период': 120, 'Предыдущий период': 130, 'Изменение': 100, 'Тренд': 80, 'Статус': 120}
        for col in trends_columns:
            trends_tree.heading(col, text=col)
            trends_tree.column(col, width=column_widths[col], anchor='center')

        # Данные трендов
        trends_data = [
            ("💰 Общая выручка", "628,600₽", "579,400₽", "+8.2%", "📈", "🟢 Рост"),
            ("🧾 Количество заказов", "4,127", "3,668", "+12.5%", "📈", "🟢 Рост"),
            ("💳 Средний чек", "380₽", "368₽", "+3.1%", "📈", "🟢 Рост"),
            ("🍽️ Блюд продано", "8,470", "7,312", "+15.8%", "📈", "🟢 Рост"),
            ("📅 Лучший день недели", "Суббота", "Суббота", "0%", "📊", "🟡 Стабильно"),
            ("💰 Выручка в субботу", "420₽", "398₽", "+5.5%", "📈", "🟢 Рост"),
            ("📅 Худший день недели", "Понедельник", "Понедельник", "0%", "📊", "🟡 Стабильно"),
            ("💰 Выручка в понедельник", "340₽", "325₽", "+4.6%", "📈", "🟢 Рост"),
            ("⏰ Пиковые часы", "19:00-21:00", "19:00-21:00", "0%", "📊", "🟡 Стабильно"),
            ("📊 Доля пиковых часов", "35%", "33%", "+2%", "📈", "🟢 Рост"),
            ("🌅 Утренние продажи", "15%", "12%", "+3%", "📈", "🟢 Рост"),
            ("🌆 Вечерние продажи", "50%", "55%", "-5%", "📉", "🟡 Снижение")
        ]

        for data in trends_data:
            trends_tree.insert('', 'end', values=data)

        # Скроллбар
        trends_scrollbar = ttk.Scrollbar(trends_frame, orient='vertical', command=trends_tree.yview)
        trends_tree.configure(yscrollcommand=trends_scrollbar.set)

        trends_tree.pack(side='left', fill='both', expand=True)
        trends_scrollbar.pack(side='right', fill='y')

    def create_customer_analysis_table(self, parent):
        """Создать таблицу анализа клиентов"""
        # Заголовок
        tk.Label(parent, text="👥 Детальный анализ клиентской базы",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Таблица клиентов
        customers_frame = tk.Frame(parent, bg='white')
        customers_frame.pack(fill='both', expand=True, padx=40, pady=20)

        # Создать таблицу
        customers_columns = ('Сегмент клиентов', 'Количество', 'Доля %', 'Средний чек', 'Частота посещений', 'Выручка', 'LTV')
        customers_tree = ttk.Treeview(customers_frame, columns=customers_columns, show='headings', height=10)

        # Настройка колонок
        column_widths = {'Сегмент клиентов': 180, 'Количество': 100, 'Доля %': 80, 'Средний чек': 100, 'Частота посещений': 130, 'Выручка': 100, 'LTV': 100}
        for col in customers_columns:
            customers_tree.heading(col, text=col)
            customers_tree.column(col, width=column_widths[col], anchor='center')

        # Данные клиентов
        customers_data = [
            ("🥇 VIP клиенты", "45", "3.2%", "850₽", "8.5 раз/мес", "325,500₽", "25,500₽"),
            ("🥈 Постоянные клиенты", "312", "22.1%", "420₽", "4.2 раз/мес", "524,160₽", "8,400₽"),
            ("🥉 Регулярные клиенты", "628", "44.5%", "380₽", "2.3 раз/мес", "549,032₽", "4,200₽"),
            ("👤 Новые клиенты", "234", "16.6%", "340₽", "1.2 раз/мес", "95,472₽", "1,200₽"),
            ("🔄 Возвращающиеся", "156", "11.1%", "365₽", "1.8 раз/мес", "102,492₽", "2,100₽"),
            ("💳 Корпоративные", "32", "2.3%", "1,250₽", "3.5 раз/мес", "140,000₽", "18,750₽"),
            ("🎂 Именинники", "18", "1.3%", "450₽", "1.0 раз/мес", "8,100₽", "450₽")
        ]

        for data in customers_data:
            customers_tree.insert('', 'end', values=data)

        # Скроллбар
        customers_scrollbar = ttk.Scrollbar(customers_frame, orient='vertical', command=customers_tree.yview)
        customers_tree.configure(yscrollcommand=customers_scrollbar.set)

        customers_tree.pack(side='left', fill='both', expand=True)
        customers_scrollbar.pack(side='right', fill='y')

        # Итоги
        totals_frame = tk.Frame(parent, bg='#ecf0f1', relief='flat', bd=1)
        totals_frame.pack(fill='x', padx=40, pady=20)

        tk.Label(totals_frame, text="👥 Общая клиентская база: 1,425 клиентов",
                font=('Cambria', 12, 'bold'), bg='#ecf0f1').pack(side='left', padx=20, pady=10)

        tk.Label(totals_frame, text="💰 Средний LTV: 8,650₽",
                font=('Cambria', 12, 'bold'), bg='#ecf0f1', fg='#27ae60').pack(side='right', padx=20, pady=10)

    def create_financial_indicators_table(self, parent):
        """Создать таблицу финансовых показателей"""
        # Заголовок
        tk.Label(parent, text="💰 Ключевые финансовые индикаторы",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Таблица показателей
        indicators_frame = tk.Frame(parent, bg='white')
        indicators_frame.pack(fill='both', expand=True, padx=40, pady=20)

        # Создать таблицу
        indicators_columns = ('Показатель', 'Значение', 'Норма', 'Статус', 'Прошлый месяц', 'Динамика', 'Прогноз')
        indicators_tree = ttk.Treeview(indicators_frame, columns=indicators_columns, show='headings', height=12)

        # Настройка колонок
        column_widths = {'Показатель': 200, 'Значение': 100, 'Норма': 100, 'Статус': 100, 'Прошлый месяц': 120, 'Динамика': 100, 'Прогноз': 100}
        for col in indicators_columns:
            indicators_tree.heading(col, text=col)
            indicators_tree.column(col, width=column_widths[col], anchor='center')

        # Данные показателей
        indicators_data = [
            ("💹 ROI (рентабельность)", "15.2%", ">12%", "🟢 Отлично", "8.7%", "📈 +6.5%", "16.8%"),
            ("📊 EBITDA", "94,500₽", ">80,000₽", "🟢 Отлично", "67,200₽", "📈 +40.6%", "105,000₽"),
            ("⚖️ Точка безубыточности", "18 дней", "≤20 дней", "🟢 Хорошо", "22 дня", "📈 -4 дня", "16 дней"),
            ("🛡️ Запас фин. прочности", "23%", ">20%", "🟢 Хорошо", "18%", "📈 +5%", "26%"),
            ("💰 Валовая прибыль", "316,200₽", ">250,000₽", "🟢 Отлично", "289,700₽", "📈 +9.1%", "347,800₽"),
            ("📈 Чистая прибыль", "67,360₽", ">50,000₽", "🟢 Отлично", "31,280₽", "📈 +115%", "78,400₽"),
            ("🔄 Оборачиваемость активов", "2.3", ">2.0", "🟢 Хорошо", "2.1", "📈 +9.5%", "2.5"),
            ("💳 Коэффициент ликвидности", "1.8", ">1.5", "🟢 Хорошо", "1.6", "📈 +12.5%", "1.9"),
            ("📊 Маржинальность", "50.3%", ">45%", "🟢 Отлично", "50.0%", "📈 +0.3%", "51.2%"),
            ("🎯 Food Cost", "49.7%", "<50%", "🟢 Хорошо", "54.7%", "📉 -5.0%", "48.5%"),
            ("👥 Labor Cost", "24.8%", "<30%", "🟢 Хорошо", "26.1%", "📉 -1.3%", "24.2%"),
            ("🏢 Операционные расходы", "25.5%", "<30%", "🟢 Хорошо", "27.2%", "📉 -1.7%", "24.8%")
        ]

        for data in indicators_data:
            indicators_tree.insert('', 'end', values=data)

        # Скроллбар
        indicators_scrollbar = ttk.Scrollbar(indicators_frame, orient='vertical', command=indicators_tree.yview)
        indicators_tree.configure(yscrollcommand=indicators_scrollbar.set)

        indicators_tree.pack(side='left', fill='both', expand=True)
        indicators_scrollbar.pack(side='right', fill='y')

    def create_forecast_table(self, parent):
        """Создать таблицу прогнозов"""
        # Заголовок
        tk.Label(parent, text="🎯 Прогнозы и стратегические рекомендации",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Таблица прогнозов
        forecast_frame = tk.Frame(parent, bg='white')
        forecast_frame.pack(fill='both', expand=True, padx=40, pady=20)

        # Создать таблицу
        forecast_columns = ('Показатель', 'Июнь 2024', 'Июль 2024', 'Август 2024', 'Квартал', 'Вероятность', 'Рекомендации')
        forecast_tree = ttk.Treeview(forecast_frame, columns=forecast_columns, show='headings', height=10)

        # Настройка колонок
        column_widths = {'Показатель': 150, 'Июнь 2024': 100, 'Июль 2024': 100, 'Август 2024': 100, 'Квартал': 100, 'Вероятность': 100, 'Рекомендации': 200}
        for col in forecast_columns:
            forecast_tree.heading(col, text=col)
            forecast_tree.column(col, width=column_widths[col], anchor='center')

        # Данные прогнозов
        forecast_data = [
            ("💰 Выручка", "685,000₽", "720,000₽", "695,000₽", "2,100,000₽", "85%", "Расширить летнее меню"),
            ("📈 Прибыль", "75,000₽", "86,400₽", "76,500₽", "237,900₽", "80%", "Оптимизировать затраты"),
            ("👥 Новые клиенты", "280", "320", "290", "890", "90%", "Усилить маркетинг"),
            ("💳 Средний чек", "395₽", "410₽", "385₽", "397₽", "75%", "Апселл и кросс-селл"),
            ("🍽️ Популярные блюда", "Салаты", "Холодные супы", "Мороженое", "Сезонные", "95%", "Сезонное меню"),
            ("📊 Загрузка зала", "78%", "85%", "80%", "81%", "85%", "Бронирование столов"),
            ("🚚 Доставка", "15%", "20%", "18%", "18%", "70%", "Развивать доставку"),
            ("💳 Безнал платежи", "85%", "88%", "90%", "88%", "95%", "QR-коды и терминалы"),
            ("⭐ Рейтинг сервиса", "4.7/5", "4.8/5", "4.7/5", "4.7/5", "80%", "Обучение персонала"),
            ("🎯 ROI", "16.8%", "18.2%", "17.1%", "17.4%", "75%", "Инвестиции в оборудование")
        ]

        for data in forecast_data:
            forecast_tree.insert('', 'end', values=data)

        # Скроллбар
        forecast_scrollbar = ttk.Scrollbar(forecast_frame, orient='vertical', command=forecast_tree.yview)
        forecast_tree.configure(yscrollcommand=forecast_scrollbar.set)

        forecast_tree.pack(side='left', fill='both', expand=True)
        forecast_scrollbar.pack(side='right', fill='y')

        # Стратегические рекомендации
        recommendations_frame = tk.LabelFrame(parent, text="📋 Стратегические рекомендации на квартал",
                                            font=('Cambria', 14, 'bold italic'), bg='white')
        recommendations_frame.pack(fill='x', padx=40, pady=20)

        # Таблица рекомендаций
        rec_columns = ('Приоритет', 'Направление', 'Действие', 'Срок', 'Бюджет', 'Ожидаемый эффект')
        rec_tree = ttk.Treeview(recommendations_frame, columns=rec_columns, show='headings', height=6)

        column_widths = {'Приоритет': 80, 'Направление': 120, 'Действие': 200, 'Срок': 100, 'Бюджет': 100, 'Ожидаемый эффект': 150}
        for col in rec_columns:
            rec_tree.heading(col, text=col)
            rec_tree.column(col, width=column_widths[col], anchor='center')

        # Данные рекомендаций
        recommendations_data = [
            ("🔴 Высокий", "Меню", "Добавить 8 летних блюд", "2 недели", "25,000₽", "+12% выручки"),
            ("🔴 Высокий", "Маркетинг", "Реклама в соцсетях", "1 месяц", "15,000₽", "+280 клиентов"),
            ("🟡 Средний", "Доставка", "Запуск доставки", "1 месяц", "45,000₽", "+20% заказов"),
            ("🟡 Средний", "Персонал", "Обучение официантов", "2 недели", "8,000₽", "+0.2 рейтинга"),
            ("🟢 Низкий", "Оборудование", "Новая кофемашина", "3 недели", "120,000₽", "+5% маржи"),
            ("🟢 Низкий", "Интерьер", "Летняя терраса", "1 месяц", "80,000₽", "+15% мест")
        ]

        for data in recommendations_data:
            rec_tree.insert('', 'end', values=data)

        rec_tree.pack(fill='both', expand=True, padx=10, pady=10)
    
    def generate_sales_report(self):
        """Сгенерировать отчёт по продажам с таблицами"""
        # Очистить предыдущий отчёт
        for widget in self.sales_report_frame.winfo_children():
            widget.destroy()

        # Создать основной контейнер
        main_container = tk.Frame(self.sales_report_frame, bg='white')
        main_container.pack(fill='both', expand=True)

        # Создать notebook для разных разделов отчёта
        report_notebook = ttk.Notebook(main_container)
        report_notebook.pack(fill='both', expand=True, padx=20, pady=20)

        # Вкладка "Сводка"
        summary_frame = tk.Frame(report_notebook, bg='white')
        report_notebook.add(summary_frame, text="📊 Сводка")
        self.create_sales_summary(summary_frame)

        # Вкладка "Топ блюда"
        dishes_frame = tk.Frame(report_notebook, bg='white')
        report_notebook.add(dishes_frame, text="🍽️ Топ блюда")
        self.create_top_dishes_table(dishes_frame)

        # Вкладка "По времени"
        time_frame = tk.Frame(report_notebook, bg='white')
        report_notebook.add(time_frame, text="⏰ По времени")
        self.create_time_analysis_table(time_frame)

        # Вкладка "По дням"
        daily_frame = tk.Frame(report_notebook, bg='white')
        report_notebook.add(daily_frame, text="📅 По дням")
        self.create_daily_sales_table(daily_frame)

    def create_sales_summary(self, parent):
        """Создать сводку продаж"""
        # Заголовок
        tk.Label(parent, text=f"📊 Сводка продаж: {self.period_var.get()}",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Карточки с основными показателями
        cards_frame = tk.Frame(parent, bg='white')
        cards_frame.pack(fill='x', padx=40, pady=20)

        # Данные для карточек
        metrics = [
            ("💰 Общая выручка", "1,568,400₽", "#27ae60", "+8.2%"),
            ("🧾 Количество заказов", "4,127", "#3498db", "+12.5%"),
            ("💳 Средний чек", "380₽", "#9b59b6", "+3.1%"),
            ("🍽️ Блюд продано", "8,470", "#e67e22", "+15.8%")
        ]

        for i, (title, value, color, change) in enumerate(metrics):
            card = tk.Frame(cards_frame, bg=color, relief='flat', bd=0)
            card.grid(row=0, column=i, padx=15, pady=10, sticky='ew')

            tk.Label(card, text=title, font=('Cambria', 11, 'bold italic'),
                    bg=color, fg='white').pack(pady=(15, 5))

            tk.Label(card, text=value, font=('Cambria', 18, 'bold'),
                    bg=color, fg='white').pack(pady=(0, 5))

            tk.Label(card, text=change, font=('Cambria', 10, 'bold'),
                    bg=color, fg='white').pack(pady=(0, 15))

        # Настройка сетки
        for i in range(4):
            cards_frame.grid_columnconfigure(i, weight=1)

        # Таблица категорий
        categories_frame = tk.LabelFrame(parent, text="📈 Продажи по категориям",
                                        font=('Cambria', 14, 'bold italic'), bg='white')
        categories_frame.pack(fill='both', expand=True, padx=40, pady=20)

        # Создать таблицу категорий
        categories_columns = ('Категория', 'Выручка', 'Доля %', 'Заказов', 'Средний чек', 'Тренд')
        categories_tree = ttk.Treeview(categories_frame, columns=categories_columns, show='headings', height=8)

        # Настройка колонок
        column_widths = {'Категория': 150, 'Выручка': 120, 'Доля %': 80, 'Заказов': 100, 'Средний чек': 120, 'Тренд': 80}
        for col in categories_columns:
            categories_tree.heading(col, text=col)
            categories_tree.column(col, width=column_widths[col], anchor='center')

        # Данные категорий
        categories_data = [
            ("🥩 Горячие блюда", "624,800₽", "39.8%", "1,456", "429₽", "📈 +5.2%"),
            ("🥗 Салаты", "312,400₽", "19.9%", "987", "317₽", "📈 +8.1%"),
            ("🍲 Супы", "234,600₽", "15.0%", "743", "316₽", "📊 +2.3%"),
            ("🍰 Десерты", "187,200₽", "11.9%", "624", "300₽", "📈 +12.4%"),
            ("🥤 Напитки", "156,800₽", "10.0%", "892", "176₽", "📈 +15.6%"),
            ("🥪 Закуски", "52,600₽", "3.4%", "425", "124₽", "📊 +1.8%")
        ]

        for data in categories_data:
            categories_tree.insert('', 'end', values=data)

        # Скроллбар
        categories_scrollbar = ttk.Scrollbar(categories_frame, orient='vertical', command=categories_tree.yview)
        categories_tree.configure(yscrollcommand=categories_scrollbar.set)

        categories_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        categories_scrollbar.pack(side='right', fill='y', pady=10)
    
    def create_top_dishes_table(self, parent):
        """Создать таблицу топ блюд"""
        # Заголовок
        tk.Label(parent, text="🏆 Топ блюда по продажам",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Таблица топ блюд
        dishes_frame = tk.Frame(parent, bg='white')
        dishes_frame.pack(fill='both', expand=True, padx=40, pady=20)

        # Создать таблицу
        dishes_columns = ('Место', 'Блюдо', 'Категория', 'Продано', 'Выручка', 'Средняя цена', 'Доля %', 'Рейтинг')
        dishes_tree = ttk.Treeview(dishes_frame, columns=dishes_columns, show='headings', height=15)

        # Настройка колонок
        column_widths = {'Место': 60, 'Блюдо': 200, 'Категория': 120, 'Продано': 80, 'Выручка': 100, 'Средняя цена': 100, 'Доля %': 80, 'Рейтинг': 80}
        for col in dishes_columns:
            dishes_tree.heading(col, text=col)
            dishes_tree.column(col, width=column_widths[col], anchor='center')

        # Данные топ блюд
        dishes_data = [
            ("🥇", "Стейк Рибай", "Горячие блюда", "156", "93,600₽", "600₽", "6.0%", "⭐⭐⭐⭐⭐"),
            ("🥈", "Борщ Украинский", "Супы", "134", "26,800₽", "200₽", "1.7%", "⭐⭐⭐⭐⭐"),
            ("🥉", "Салат Цезарь", "Салаты", "128", "38,400₽", "300₽", "2.4%", "⭐⭐⭐⭐⭐"),
            ("4", "Котлета по-Киевски", "Горячие блюда", "98", "39,200₽", "400₽", "2.5%", "⭐⭐⭐⭐"),
            ("5", "Пельмени Домашние", "Горячие блюда", "87", "26,100₽", "300₽", "1.7%", "⭐⭐⭐⭐"),
            ("6", "Тирамису", "Десерты", "76", "22,800₽", "300₽", "1.5%", "⭐⭐⭐⭐⭐"),
            ("7", "Солянка Мясная", "Супы", "72", "21,600₽", "300₽", "1.4%", "⭐⭐⭐⭐"),
            ("8", "Оливье", "Салаты", "68", "13,600₽", "200₽", "0.9%", "⭐⭐⭐⭐"),
            ("9", "Шашлык из Свинины", "Горячие блюда", "65", "32,500₽", "500₽", "2.1%", "⭐⭐⭐⭐⭐"),
            ("10", "Чизкейк", "Десерты", "62", "18,600₽", "300₽", "1.2%", "⭐⭐⭐⭐"),
            ("11", "Греческий Салат", "Салаты", "58", "17,400₽", "300₽", "1.1%", "⭐⭐⭐⭐"),
            ("12", "Лагман", "Горячие блюда", "54", "21,600₽", "400₽", "1.4%", "⭐⭐⭐⭐"),
            ("13", "Морс Клюквенный", "Напитки", "52", "5,200₽", "100₽", "0.3%", "⭐⭐⭐"),
            ("14", "Крем-суп Грибной", "Супы", "48", "14,400₽", "300₽", "0.9%", "⭐⭐⭐⭐"),
            ("15", "Мороженое", "Десерты", "45", "9,000₽", "200₽", "0.6%", "⭐⭐⭐")
        ]

        for data in dishes_data:
            dishes_tree.insert('', 'end', values=data)

        # Скроллбар
        dishes_scrollbar = ttk.Scrollbar(dishes_frame, orient='vertical', command=dishes_tree.yview)
        dishes_tree.configure(yscrollcommand=dishes_scrollbar.set)

        dishes_tree.pack(side='left', fill='both', expand=True)
        dishes_scrollbar.pack(side='right', fill='y')

    def create_time_analysis_table(self, parent):
        """Создать таблицу анализа по времени"""
        # Заголовок
        tk.Label(parent, text="⏰ Анализ продаж по времени",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Таблица по часам
        time_frame = tk.LabelFrame(parent, text="📊 Продажи по часам",
                                  font=('Cambria', 14, 'bold italic'), bg='white')
        time_frame.pack(fill='both', expand=True, padx=40, pady=20)

        # Создать таблицу
        time_columns = ('Время', 'Заказов', 'Выручка', 'Доля %', 'Средний чек', 'Загрузка', 'Персонал')
        time_tree = ttk.Treeview(time_frame, columns=time_columns, show='headings', height=12)

        # Настройка колонок
        column_widths = {'Время': 100, 'Заказов': 80, 'Выручка': 100, 'Доля %': 80, 'Средний чек': 100, 'Загрузка': 100, 'Персонал': 80}
        for col in time_columns:
            time_tree.heading(col, text=col)
            time_tree.column(col, width=column_widths[col], anchor='center')

        # Данные по времени
        time_data = [
            ("10:00-11:00", "45", "18,500₽", "1.2%", "411₽", "🟢 Низкая", "3"),
            ("11:00-12:00", "78", "31,200₽", "2.0%", "400₽", "🟡 Средняя", "4"),
            ("12:00-13:00", "156", "62,400₽", "4.0%", "400₽", "🔴 Высокая", "6"),
            ("13:00-14:00", "189", "75,600₽", "4.8%", "400₽", "🔴 Высокая", "6"),
            ("14:00-15:00", "134", "53,600₽", "3.4%", "400₽", "🟡 Средняя", "5"),
            ("15:00-16:00", "89", "35,600₽", "2.3%", "400₽", "🟡 Средняя", "4"),
            ("16:00-17:00", "67", "26,800₽", "1.7%", "400₽", "🟢 Низкая", "3"),
            ("17:00-18:00", "98", "39,200₽", "2.5%", "400₽", "🟡 Средняя", "4"),
            ("18:00-19:00", "145", "58,000₽", "3.7%", "400₽", "🔴 Высокая", "6"),
            ("19:00-20:00", "198", "79,200₽", "5.1%", "400₽", "🔴 Высокая", "7"),
            ("20:00-21:00", "167", "66,800₽", "4.3%", "400₽", "🔴 Высокая", "6"),
            ("21:00-22:00", "123", "49,200₽", "3.1%", "400₽", "🟡 Средняя", "5")
        ]

        for data in time_data:
            time_tree.insert('', 'end', values=data)

        # Скроллбар
        time_scrollbar = ttk.Scrollbar(time_frame, orient='vertical', command=time_tree.yview)
        time_tree.configure(yscrollcommand=time_scrollbar.set)

        time_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        time_scrollbar.pack(side='right', fill='y', pady=10)

    def create_daily_sales_table(self, parent):
        """Создать таблицу продаж по дням"""
        # Заголовок
        tk.Label(parent, text="📅 Продажи по дням недели",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Таблица по дням
        daily_frame = tk.Frame(parent, bg='white')
        daily_frame.pack(fill='both', expand=True, padx=40, pady=20)

        # Создать таблицу
        daily_columns = ('День недели', 'Дата', 'Заказов', 'Выручка', 'Средний чек', 'Посетителей', 'Оборачиваемость', 'Рейтинг дня')
        daily_tree = ttk.Treeview(daily_frame, columns=daily_columns, show='headings', height=10)

        # Настройка колонок
        column_widths = {'День недели': 120, 'Дата': 100, 'Заказов': 80, 'Выручка': 100, 'Средний чек': 100, 'Посетителей': 100, 'Оборачиваемость': 120, 'Рейтинг дня': 100}
        for col in daily_columns:
            daily_tree.heading(col, text=col)
            daily_tree.column(col, width=column_widths[col], anchor='center')

        # Данные по дням
        daily_data = [
            ("Понедельник", "20.05.24", "287", "114,800₽", "400₽", "245", "2.1", "⭐⭐⭐"),
            ("Вторник", "21.05.24", "312", "124,800₽", "400₽", "267", "2.3", "⭐⭐⭐"),
            ("Среда", "22.05.24", "345", "138,000₽", "400₽", "289", "2.5", "⭐⭐⭐⭐"),
            ("Четверг", "23.05.24", "398", "159,200₽", "400₽", "334", "2.8", "⭐⭐⭐⭐"),
            ("Пятница", "24.05.24", "456", "182,400₽", "400₽", "378", "3.2", "⭐⭐⭐⭐⭐"),
            ("Суббота", "25.05.24", "523", "209,200₽", "400₽", "445", "3.7", "⭐⭐⭐⭐⭐"),
            ("Воскресенье", "26.05.24", "467", "186,800₽", "400₽", "398", "3.3", "⭐⭐⭐⭐⭐")
        ]

        for data in daily_data:
            daily_tree.insert('', 'end', values=data)

        # Скроллбар
        daily_scrollbar = ttk.Scrollbar(daily_frame, orient='vertical', command=daily_tree.yview)
        daily_tree.configure(yscrollcommand=daily_scrollbar.set)

        daily_tree.pack(side='left', fill='both', expand=True)
        daily_scrollbar.pack(side='right', fill='y')

    def export_sales_report(self):
        """Экспорт отчёта по продажам в Excel"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="Сохранить отчёт по продажам"
            )

            if filename:
                # Создать данные для экспорта
                export_data = [
                    ["Отчёт по продажам", self.period_var.get()],
                    [""],
                    ["Основные показатели"],
                    ["Общая выручка", "1,568,400₽"],
                    ["Количество заказов", "4,127"],
                    ["Средний чек", "380₽"],
                    ["Блюд продано", "8,470"],
                    [""],
                    ["Топ блюда"],
                    ["Место", "Блюдо", "Категория", "Продано", "Выручка", "Средняя цена", "Доля %"]
                ]

                # Добавить данные топ блюд
                dishes_data = [
                    ["1", "Стейк Рибай", "Горячие блюда", "156", "93,600₽", "600₽", "6.0%"],
                    ["2", "Борщ Украинский", "Супы", "134", "26,800₽", "200₽", "1.7%"],
                    ["3", "Салат Цезарь", "Салаты", "128", "38,400₽", "300₽", "2.4%"]
                ]
                export_data.extend(dishes_data)

                # Записать в файл
                with open(filename, 'w', newline='', encoding='utf-8') as file:
                    writer = csv.writer(file)
                    writer.writerows(export_data)

                messagebox.showinfo("Экспорт", f"Отчёт экспортирован в файл:\n{filename}")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка при экспорте: {e}")

    def show_inventory_report(self, report_type):
        """Показать отчёт по складу с таблицами"""
        # Очистить предыдущий отчёт
        for widget in self.inventory_report_frame.winfo_children():
            widget.destroy()

        # Создать основной контейнер
        main_container = tk.Frame(self.inventory_report_frame, bg='white')
        main_container.pack(fill='both', expand=True, padx=20, pady=20)

        if report_type == "current_stock":
            self.create_current_stock_table(main_container)
        elif report_type == "low_stock":
            self.create_low_stock_table(main_container)
        else:  # movement
            self.create_movement_table(main_container)

    def create_current_stock_table(self, parent):
        """Создать таблицу текущих остатков"""
        # Заголовок
        tk.Label(parent, text="📊 Текущие остатки на складе",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Таблица остатков
        stock_frame = tk.Frame(parent, bg='white')
        stock_frame.pack(fill='both', expand=True)

        # Создать таблицу
        stock_columns = ('Товар', 'Категория', 'Текущий остаток', 'Единица', 'Мин. остаток', 'Статус', 'Стоимость', 'Поставщик')
        stock_tree = ttk.Treeview(stock_frame, columns=stock_columns, show='headings', height=15)

        # Настройка колонок
        column_widths = {'Товар': 180, 'Категория': 120, 'Текущий остаток': 120, 'Единица': 80, 'Мин. остаток': 100, 'Статус': 100, 'Стоимость': 100, 'Поставщик': 150}
        for col in stock_columns:
            stock_tree.heading(col, text=col)
            stock_tree.column(col, width=column_widths[col], anchor='center')

        # Загрузить данные из базы данных
        try:
            raw_materials = self.db_manager.get_raw_materials()
            stock_data = []

            for material in raw_materials:
                # Определить статус остатка
                current_stock = material['current_stock']
                min_stock = material['minimum_stock']

                if current_stock <= min_stock:
                    status = "🔴 Критично"
                elif current_stock <= min_stock * 1.5:
                    status = "🟡 Низкий"
                else:
                    status = "🟢 Норма"

                # Рассчитать стоимость остатка
                total_cost = current_stock * material['average_cost']

                stock_data.append((
                    material['name'],
                    material['category'] or "Без категории",
                    f"{current_stock:.1f}",
                    material['unit_of_measure'],
                    f"{min_stock:.1f}",
                    status,
                    self.format_currency(total_cost),
                    material['supplier'] or "Не указан"
                ))

        except Exception as e:
            print(f"Ошибка загрузки данных склада: {e}")
            # Fallback данные
            stock_data = [
                ("Мука пшеничная", "Мучные изделия", "45.5", "кг", "20.0", "🟢 Норма", "2 275,00 руб", "Мука-Сервис"),
                ("Говядина", "Мясо", "28.2", "кг", "15.0", "🟢 Норма", "14 100,00 руб", "Мясокомбинат"),
                ("Молоко 3.2%", "Молочные", "15.8", "л", "10.0", "🟢 Норма", "790,00 руб", "Молочный Дом"),
                ("Картофель", "Овощи", "67.3", "кг", "30.0", "🟢 Норма", "2 019,00 руб", "Овощи-Фрукты"),
            ("Лук репчатый", "Овощи", "12.4", "кг", "15.0", "🟡 Мало", "372₽", "Овощи-Фрукты"),
            ("Сметана 20%", "Молочные", "8.5", "кг", "5.0", "🟢 Норма", "680₽", "Молочный Дом"),
            ("Творог", "Молочные", "6.2", "кг", "3.0", "🟢 Норма", "620₽", "Молочный Дом"),
            ("Сыр твёрдый", "Молочные", "4.8", "кг", "8.0", "🔴 Критично", "1,440₽", "Молочный Дом"),
            ("Масло сливочное", "Молочные", "3.2", "кг", "2.0", "🟢 Норма", "1,280₽", "Молочный Дом"),
            ("Морковь", "Овощи", "18.6", "кг", "10.0", "🟢 Норма", "558₽", "Овощи-Фрукты"),
            ("Капуста", "Овощи", "22.1", "кг", "15.0", "🟢 Норма", "663₽", "Овощи-Фрукты"),
            ("Помидоры", "Овощи", "8.9", "кг", "12.0", "🟡 Мало", "890₽", "Овощи-Фрукты"),
            ("Зелень", "Овощи", "2.1", "кг", "1.0", "🟢 Норма", "420₽", "Овощи-Фрукты"),
            ("Масло подсолнечное", "Масла", "3.2", "л", "3.0", "🟡 Мало", "256₽", "Масло-Продукт"),
            ("Соль", "Специи", "1.8", "кг", "2.0", "🟡 Мало", "36₽", "Специи-Опт"),
            ("Перец чёрный", "Специи", "0.3", "кг", "0.5", "🔴 Критично", "150₽", "Специи-Опт"),
            ("Сахар", "Сыпучие", "25.4", "кг", "10.0", "🟢 Норма", "1,270₽", "Сахар-Трейд"),
            ("Яйца куриные", "Яйца", "180", "шт", "100", "🟢 Норма", "900₽", "Птицефабрика")
        ]

        for data in stock_data:
            stock_tree.insert('', 'end', values=data)

        # Скроллбар
        stock_scrollbar = ttk.Scrollbar(stock_frame, orient='vertical', command=stock_tree.yview)
        stock_tree.configure(yscrollcommand=stock_scrollbar.set)

        stock_tree.pack(side='left', fill='both', expand=True)
        stock_scrollbar.pack(side='right', fill='y')

        # Итоги
        totals_frame = tk.Frame(parent, bg='#ecf0f1', relief='flat', bd=1)
        totals_frame.pack(fill='x', pady=20)

        tk.Label(totals_frame, text="📊 Общая стоимость остатков: 28,799₽",
                font=('Cambria', 14, 'bold'), bg='#ecf0f1').pack(side='left', padx=20, pady=15)

        tk.Label(totals_frame, text="⚠️ Товаров требуют пополнения: 5",
                font=('Cambria', 14, 'bold'), bg='#ecf0f1', fg='#e74c3c').pack(side='right', padx=20, pady=15)

    def create_low_stock_table(self, parent):
        """Создать таблицу критических остатков"""
        # Заголовок
        tk.Label(parent, text="⚠️ Критические остатки - требуют пополнения",
                font=('Cambria', 16, 'bold italic'), bg='white', fg='#e74c3c').pack(pady=20)

        # Таблица критических остатков
        low_stock_frame = tk.Frame(parent, bg='white')
        low_stock_frame.pack(fill='both', expand=True)

        # Создать таблицу
        low_columns = ('Приоритет', 'Товар', 'Текущий остаток', 'Мин. остаток', 'Нехватка', 'Рекомендуемый заказ', 'Поставщик', 'Срочность')
        low_tree = ttk.Treeview(low_stock_frame, columns=low_columns, show='headings', height=12)

        # Настройка колонок
        column_widths = {'Приоритет': 80, 'Товар': 180, 'Текущий остаток': 120, 'Мин. остаток': 100, 'Нехватка': 100, 'Рекомендуемый заказ': 150, 'Поставщик': 150, 'Срочность': 100}
        for col in low_columns:
            low_tree.heading(col, text=col)
            low_tree.column(col, width=column_widths[col], anchor='center')

        # Загрузить данные критических остатков из базы данных
        try:
            raw_materials = self.db_manager.get_raw_materials()
            low_data = []

            for material in raw_materials:
                current_stock = material['current_stock']
                min_stock = material['minimum_stock']

                # Показывать только товары с низким остатком
                if current_stock <= min_stock * 1.5:
                    shortage = current_stock - min_stock
                    recommended_order = max(min_stock * 2, min_stock - current_stock + min_stock)

                    # Определить приоритет и срочность
                    if current_stock <= min_stock * 0.5:
                        priority = "🚨"
                        urgency = "КРИТИЧНО"
                    elif current_stock <= min_stock:
                        priority = "🔴"
                        urgency = "ВЫСОКАЯ"
                    else:
                        priority = "🟡"
                        urgency = "СРЕДНЯЯ"

                    low_data.append((
                        priority,
                        material['name'],
                        f"{current_stock:.1f} {material['unit_of_measure']}",
                        f"{min_stock:.1f} {material['unit_of_measure']}",
                        f"{shortage:+.1f} {material['unit_of_measure']}",
                        f"{recommended_order:.1f} {material['unit_of_measure']}",
                        material['supplier'] or "Не указан",
                        urgency
                    ))

            # Сортировать по приоритету (критичные сначала)
            priority_order = {"🚨": 0, "🔴": 1, "🟡": 2}
            low_data.sort(key=lambda x: priority_order.get(x[0], 3))

        except Exception as e:
            print(f"Ошибка загрузки данных критических остатков: {e}")
            # Fallback данные
            low_data = [
                ("🚨", "Перец чёрный молотый", "0.3 кг", "0.5 кг", "-0.2 кг", "1.0 кг", "Специи-Опт", "КРИТИЧНО"),
                ("🔴", "Сыр твёрдый", "4.8 кг", "8.0 кг", "-3.2 кг", "15.0 кг", "Молочный Дом", "ВЫСОКАЯ"),
                ("🟡", "Лук репчатый", "12.4 кг", "15.0 кг", "-2.6 кг", "25.0 кг", "Овощи-Фрукты", "СРЕДНЯЯ"),
                ("🟡", "Помидоры", "8.9 кг", "12.0 кг", "-3.1 кг", "20.0 кг", "Овощи-Фрукты", "СРЕДНЯЯ"),
                ("🟡", "Масло подсолнечное", "3.2 л", "3.0 л", "+0.2 л", "10.0 л", "Масло-Продукт", "НИЗКАЯ"),
                ("🟡", "Соль поваренная", "1.8 кг", "2.0 кг", "-0.2 кг", "5.0 кг", "Специи-Опт", "НИЗКАЯ")
            ]

        for data in low_data:
            low_tree.insert('', 'end', values=data)

        # Скроллбар
        low_scrollbar = ttk.Scrollbar(low_stock_frame, orient='vertical', command=low_tree.yview)
        low_tree.configure(yscrollcommand=low_scrollbar.set)

        low_tree.pack(side='left', fill='both', expand=True)
        low_scrollbar.pack(side='right', fill='y')

        # Рекомендации
        recommendations_frame = tk.LabelFrame(parent, text="📋 Рекомендации по закупкам",
                                            font=('Cambria', 14, 'bold italic'), bg='white')
        recommendations_frame.pack(fill='x', pady=20)

        recommendations_text = """
🚨 СРОЧНЫЕ ДЕЙСТВИЯ:
• Немедленно заказать перец чёрный молотый (1 кг) - закончится сегодня
• Связаться с "Молочный Дом" для заказа сыра твёрдого (15 кг)
• Оформить заказ овощей: лук (25 кг) + помидоры (20 кг)

💰 ОБЩАЯ СУММА РЕКОМЕНДУЕМЫХ ЗАКУПОК: 8,450₽

📞 КОНТАКТЫ ПОСТАВЩИКОВ:
• Специи-Опт: +7 (495) 123-45-67
• Молочный Дом: +7 (495) 234-56-78
• Овощи-Фрукты: +7 (495) 345-67-89
        """

        tk.Label(recommendations_frame, text=recommendations_text, font=('Cambria', 11),
                bg='white', justify='left', anchor='nw').pack(fill='x', padx=20, pady=15)
    
    def create_movement_table(self, parent):
        """Создать таблицу движения товаров"""
        # Заголовок
        tk.Label(parent, text="📈 Движение товаров за последние 7 дней",
                font=('Cambria', 16, 'bold italic'), bg='white').pack(pady=20)

        # Создать notebook для разных типов движения
        movement_notebook = ttk.Notebook(parent)
        movement_notebook.pack(fill='both', expand=True)

        # Вкладка поступлений
        receipts_frame = tk.Frame(movement_notebook, bg='white')
        movement_notebook.add(receipts_frame, text="📥 Поступления")

        # Таблица поступлений
        receipts_columns = ('Дата', 'Товар', 'Количество', 'Единица', 'Поставщик', 'Стоимость', 'Документ')
        receipts_tree = ttk.Treeview(receipts_frame, columns=receipts_columns, show='headings', height=12)

        column_widths = {'Дата': 100, 'Товар': 180, 'Количество': 100, 'Единица': 80, 'Поставщик': 150, 'Стоимость': 100, 'Документ': 120}
        for col in receipts_columns:
            receipts_tree.heading(col, text=col)
            receipts_tree.column(col, width=column_widths[col], anchor='center')

        # Данные поступлений (обновлено с правильным форматом валюты)
        receipts_data = [
            ("20.05.24", "Мука пшеничная", "50.0", "кг", "Мука-Сервис", "2 500,00 руб", "ТТН-001"),
            ("20.05.24", "Говядина", "30.0", "кг", "Мясокомбинат", "15 000,00 руб", "ТТН-002"),
            ("19.05.24", "Молоко 3.2%", "25.0", "л", "Молочный Дом", "1 250,00 руб", "ТТН-003"),
            ("18.05.24", "Картофель", "40.0", "кг", "Овощи-Фрукты", "1 200,00 руб", "ТТН-004"),
            ("17.05.24", "Сыр твёрдый", "12.0", "кг", "Молочный Дом", "3 600,00 руб", "ТТН-005"),
            ("16.05.24", "Лук репчатый", "20.0", "кг", "Овощи-Фрукты", "600,00 руб", "ТТН-006"),
            ("15.05.24", "Масло подсолнечное", "15.0", "л", "Масло-Продукт", "1 200,00 руб", "ТТН-007")
        ]

        for data in receipts_data:
            receipts_tree.insert('', 'end', values=data)

        receipts_tree.pack(fill='both', expand=True, padx=20, pady=20)

        # Вкладка расходов
        expenses_frame = tk.Frame(movement_notebook, bg='white')
        movement_notebook.add(expenses_frame, text="📤 Расходы")

        # Таблица расходов
        expenses_columns = ('Дата', 'Товар', 'Количество', 'Единица', 'Назначение', 'Ответственный', 'Документ')
        expenses_tree = ttk.Treeview(expenses_frame, columns=expenses_columns, show='headings', height=12)

        column_widths = {'Дата': 100, 'Товар': 180, 'Количество': 100, 'Единица': 80, 'Назначение': 150, 'Ответственный': 120, 'Документ': 120}
        for col in expenses_columns:
            expenses_tree.heading(col, text=col)
            expenses_tree.column(col, width=column_widths[col], anchor='center')

        # Данные расходов
        expenses_data = [
            ("20.05.24", "Мука пшеничная", "18.0", "кг", "Производство хлеба", "Иванов П.", "РН-001"),
            ("20.05.24", "Говядина", "22.0", "кг", "Стейки и котлеты", "Петров С.", "РН-002"),
            ("19.05.24", "Молоко 3.2%", "16.0", "л", "Каши и десерты", "Сидоров А.", "РН-003"),
            ("18.05.24", "Картофель", "28.0", "кг", "Гарниры", "Козлов В.", "РН-004"),
            ("17.05.24", "Лук репчатый", "15.0", "кг", "Салаты и супы", "Новиков Д.", "РН-005"),
            ("16.05.24", "Морковь", "12.0", "кг", "Супы и гарниры", "Волков М.", "РН-006"),
            ("15.05.24", "Сыр твёрдый", "8.0", "кг", "Салаты и пицца", "Орлов К.", "РН-007")
        ]

        for data in expenses_data:
            expenses_tree.insert('', 'end', values=data)

        expenses_tree.pack(fill='both', expand=True, padx=20, pady=20)

        # Вкладка аналитики
        analytics_frame = tk.Frame(movement_notebook, bg='white')
        movement_notebook.add(analytics_frame, text="📊 Аналитика")

        # Статистика движения
        stats_frame = tk.LabelFrame(analytics_frame, text="📈 Статистика движения товаров",
                                   font=('Cambria', 14, 'bold italic'), bg='white')
        stats_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Таблица аналитики
        analytics_columns = ('Товар', 'Поступило', 'Расходовано', 'Остаток', 'Оборачиваемость', 'Тренд')
        analytics_tree = ttk.Treeview(stats_frame, columns=analytics_columns, show='headings', height=10)

        column_widths = {'Товар': 180, 'Поступило': 100, 'Расходовано': 120, 'Остаток': 100, 'Оборачиваемость': 130, 'Тренд': 100}
        for col in analytics_columns:
            analytics_tree.heading(col, text=col)
            analytics_tree.column(col, width=column_widths[col], anchor='center')

        # Данные аналитики
        analytics_data = [
            ("Мука пшеничная", "+50.0 кг", "-18.0 кг", "45.5 кг", "2.1 раз/неделя", "📈 Стабильно"),
            ("Говядина", "+30.0 кг", "-22.0 кг", "28.2 кг", "3.2 раз/неделя", "📈 Высокий спрос"),
            ("Молоко 3.2%", "+25.0 л", "-16.0 л", "15.8 л", "2.8 раз/неделя", "📊 Норма"),
            ("Картофель", "+40.0 кг", "-28.0 кг", "67.3 кг", "1.8 раз/неделя", "📈 Популярно"),
            ("Лук репчатый", "+20.0 кг", "-15.0 кг", "12.4 кг", "4.1 раз/неделя", "🔥 Очень высокий"),
            ("Сыр твёрдый", "+12.0 кг", "-8.0 кг", "4.8 кг", "5.2 раз/неделя", "🔥 Критический"),
            ("Морковь", "+15.0 кг", "-12.0 кг", "18.6 кг", "2.5 раз/неделя", "📊 Норма")
        ]

        for data in analytics_data:
            analytics_tree.insert('', 'end', values=data)

        analytics_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def export_inventory_report(self):
        """Экспорт отчёта по складу"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="Сохранить отчёт по складу"
            )

            if filename:
                # Создать данные для экспорта
                export_data = [
                    ["Отчёт по складу", datetime.now().strftime("%Y-%m-%d %H:%M")],
                    [""],
                    ["Текущие остатки"],
                    ["Товар", "Категория", "Остаток", "Единица", "Мин. остаток", "Статус", "Стоимость", "Поставщик"]
                ]

                # Добавить данные остатков
                stock_data = [
                    ["Мука пшеничная", "Мучные изделия", "45.5", "кг", "20.0", "Норма", "2,275₽", "Мука-Сервис"],
                    ["Говядина", "Мясо", "28.2", "кг", "15.0", "Норма", "14,100₽", "Мясокомбинат"],
                    ["Лук репчатый", "Овощи", "12.4", "кг", "15.0", "Мало", "372₽", "Овощи-Фрукты"]
                ]
                export_data.extend(stock_data)

                # Записать в файл
                with open(filename, 'w', newline='', encoding='utf-8') as file:
                    writer = csv.writer(file)
                    writer.writerows(export_data)

                messagebox.showinfo("Экспорт", f"Отчёт по складу экспортирован в файл:\n{filename}")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка при экспорте: {e}")

    def export_report(self):
        """Экспорт отчёта"""
        messagebox.showinfo("Экспорт", "Отчёт экспортирован в Excel")

    def refresh_data(self):
        """Обновить данные"""
        messagebox.showinfo("Обновление", "Данные отчётов обновлены")

def create_reports_manager(parent, db_manager):
    """Создать менеджер отчётов"""
    manager = ReportsManager(parent, db_manager)
    return manager.create_reports_window()
