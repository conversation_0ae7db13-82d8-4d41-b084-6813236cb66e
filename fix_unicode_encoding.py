#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Исправление проблем с Unicode кодировкой в экстремальных методах
Заменяет все print() с Unicode символами на safe_print()
"""

import re
import os

def fix_file_unicode(filename):
    """Исправить Unicode проблемы в файле"""
    print(f"Исправление файла: {filename}")
    
    if not os.path.exists(filename):
        print(f"Файл {filename} не найден")
        return False
    
    try:
        # Читаем файл
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Добавляем функцию safe_print если её нет
        if 'def safe_print(' not in content:
            safe_print_function = '''
def safe_print(text):
    """Безопасный вывод с обработкой Unicode ошибок"""
    try:
        print(text)
    except UnicodeEncodeError:
        # Удаляем все Unicode символы и заменяем на ASCII
        ascii_text = text.encode('ascii', 'ignore').decode('ascii')
        print(ascii_text)

'''
            # Вставляем после импортов
            import_end = content.find('\n\n')
            if import_end != -1:
                content = content[:import_end] + safe_print_function + content[import_end:]
            else:
                content = safe_print_function + content
        
        # Заменяем все print() с Unicode символами на safe_print()
        unicode_patterns = [
            r'print\("([^"]*[🔧💀❌✅🚀⚠️☢️🔐💡📋🎉💥⏳🛑🔄][^"]*)"',
            r"print\('([^']*[🔧💀❌✅🚀⚠️☢️🔐💡📋🎉💥⏳🛑🔄][^']*)'",
            r'print\(f"([^"]*[🔧💀❌✅🚀⚠️☢️🔐💡📋🎉💥⏳🛑🔄][^"]*)"',
            r"print\(f'([^']*[🔧💀❌✅🚀⚠️☢️🔐💡📋🎉💥⏳🛑🔄][^']*)'",
        ]
        
        for pattern in unicode_patterns:
            content = re.sub(pattern, r'safe_print("\1"', content)
            content = re.sub(pattern.replace('"', "'"), r"safe_print('\1'", content)
        
        # Специальные случаи для f-строк
        content = re.sub(r'print\(f"([^"]*[🔧💀❌✅🚀⚠️☢️🔐💡📋🎉💥⏳🛑🔄][^"]*{[^}]+}[^"]*)"', r'safe_print(f"\1"', content)
        
        # Сохраняем исправленный файл
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✓ Файл {filename} исправлен")
        return True
        
    except Exception as e:
        print(f"Ошибка при исправлении {filename}: {e}")
        return False

def main():
    """Главная функция исправления"""
    print("ИСПРАВЛЕНИЕ UNICODE ПРОБЛЕМ В ЭКСТРЕМАЛЬНЫХ МЕТОДАХ")
    print("=" * 60)
    
    files_to_fix = [
        "extreme_keyboard_hook.py",
        "nuclear_keyboard_switch.py", 
        "ultimate_driver_approach.py"
    ]
    
    fixed_count = 0
    
    for filename in files_to_fix:
        if fix_file_unicode(filename):
            fixed_count += 1
    
    print("\n" + "=" * 60)
    print(f"РЕЗУЛЬТАТ: Исправлено {fixed_count} из {len(files_to_fix)} файлов")
    
    if fixed_count == len(files_to_fix):
        print("✓ ВСЕ ФАЙЛЫ УСПЕШНО ИСПРАВЛЕНЫ!")
        print("Теперь экстремальные методы должны работать без ошибок кодировки")
    else:
        print("⚠ Некоторые файлы не удалось исправить")
    
    return fixed_count == len(files_to_fix)

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\nТеперь можно запустить тест:")
            print("python test_extreme_methods.py")
        else:
            print("\nНекоторые файлы требуют ручного исправления")
    except Exception as e:
        print(f"Критическая ошибка: {e}")
    
    input("\nНажмите Enter для выхода...")
