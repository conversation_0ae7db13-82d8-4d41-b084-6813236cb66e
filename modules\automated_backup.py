"""
Automated Backup and Disaster Recovery Module
Implements comprehensive backup system with automated daily backups, 
cloud storage integration, and disaster recovery procedures.
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import os
import shutil
import zipfile
import json
import threading
import schedule
import time
from datetime import datetime, timedelta
import hashlib
import subprocess
import ftplib
import boto3
from botocore.exceptions import ClientError
import dropbox
import requests

# Import utility functions
try:
    from utils.error_handler import handle_module_error
    from utils.logger import log_info, log_error
    from utils.database_manager import DatabaseManager
except ImportError:
    # Fallback error handling
    def handle_module_error(error, module_name, operation):
        print(f"Error in {module_name} during {operation}: {str(error)}")
    
    def log_info(message, module_name):
        print(f"INFO [{module_name}]: {message}")
    
    def log_error(message, module_name):
        print(f"ERROR [{module_name}]: {message}")


class AutomatedBackupManager:
    """Manager for Automated Backup and Disaster Recovery"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.current_frame = None
        
        # Backup configurations
        self.backup_config = {
            'local_backup': {
                'enabled': True,
                'path': 'backups/local',
                'retention_days': 30,
                'schedule': 'daily',
                'time': '02:00'
            },
            'cloud_backup': {
                'enabled': False,
                'provider': 'none',  # aws_s3, dropbox, google_drive, ftp
                'credentials': {},
                'retention_days': 90,
                'schedule': 'daily',
                'time': '03:00'
            },
            'compression': {
                'enabled': True,
                'level': 6,
                'password_protected': False,
                'password': ''
            },
            'verification': {
                'enabled': True,
                'checksum_algorithm': 'sha256',
                'test_restore': False
            }
        }
        
        # Backup scheduler
        self.scheduler_thread = None
        self.scheduler_running = False
        
        # Initialize backup system
        self.init_backup_system()
    
    def init_backup_system(self):
        """Initialize backup system"""
        try:
            # Create backup directories
            os.makedirs('backups/local', exist_ok=True)
            os.makedirs('backups/cloud', exist_ok=True)
            os.makedirs('backups/temp', exist_ok=True)
            os.makedirs('backups/restore', exist_ok=True)
            
            # Initialize backup database
            self.init_backup_database()
            
            # Load configuration
            self.load_backup_config()
            
            # Start scheduler if enabled
            if self.backup_config['local_backup']['enabled']:
                self.start_backup_scheduler()
            
            log_info("Система резервного копирования инициализирована", "AutomatedBackup")
            
        except Exception as e:
            handle_module_error(e, "Резервное копирование", "инициализация системы")
    
    def init_backup_database(self):
        """Initialize backup tracking database"""
        try:
            backup_db_path = 'backups/backup_log.db'
            self.backup_connection = sqlite3.connect(backup_db_path, check_same_thread=False)
            cursor = self.backup_connection.cursor()
            
            # Backup log table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS backup_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    backup_type TEXT NOT NULL,  -- local, cloud, manual
                    backup_name TEXT NOT NULL,
                    file_path TEXT,
                    file_size INTEGER,
                    checksum TEXT,
                    start_time DATETIME,
                    end_time DATETIME,
                    status TEXT,  -- success, failed, in_progress
                    error_message TEXT,
                    compression_ratio REAL,
                    cloud_provider TEXT,
                    cloud_location TEXT,
                    created_date DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Restore log table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS restore_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    backup_id INTEGER,
                    restore_type TEXT,  -- full, partial, test
                    restore_path TEXT,
                    start_time DATETIME,
                    end_time DATETIME,
                    status TEXT,
                    error_message TEXT,
                    verified BOOLEAN DEFAULT 0,
                    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (backup_id) REFERENCES backup_log (id)
                )
            ''')
            
            # Configuration table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS backup_config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    config_key TEXT UNIQUE NOT NULL,
                    config_value TEXT,
                    updated_date DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            self.backup_connection.commit()
            log_info("База данных резервного копирования инициализирована", "AutomatedBackup")
            
        except Exception as e:
            handle_module_error(e, "Резервное копирование", "инициализация базы данных")
    
    def load_backup_config(self):
        """Load backup configuration from database"""
        try:
            cursor = self.backup_connection.cursor()
            cursor.execute('SELECT config_key, config_value FROM backup_config')
            config_data = cursor.fetchall()
            
            for key, value in config_data:
                if key in ['local_backup', 'cloud_backup', 'compression', 'verification']:
                    self.backup_config[key] = json.loads(value)
            
            log_info("Конфигурация резервного копирования загружена", "AutomatedBackup")
            
        except Exception as e:
            handle_module_error(e, "Резервное копирование", "загрузка конфигурации")
    
    def save_backup_config(self):
        """Save backup configuration to database"""
        try:
            cursor = self.backup_connection.cursor()
            
            for key, value in self.backup_config.items():
                cursor.execute('''
                    INSERT OR REPLACE INTO backup_config (config_key, config_value, updated_date)
                    VALUES (?, ?, CURRENT_TIMESTAMP)
                ''', (key, json.dumps(value)))
            
            self.backup_connection.commit()
            log_info("Конфигурация резервного копирования сохранена", "AutomatedBackup")
            
        except Exception as e:
            handle_module_error(e, "Резервное копирование", "сохранение конфигурации")
    
    def show_backup_manager(self):
        """Show the main backup management window"""
        try:
            if self.window and self.window.winfo_exists():
                self.window.lift()
                return
            
            self.window = tk.Toplevel(self.parent)
            self.window.title("💾 Автоматическое Резервное Копирование и Восстановление")
            self.window.geometry("1400x900")
            self.window.configure(bg='white')
            
            # Center window
            x = (self.window.winfo_screenwidth() // 2) - (1400 // 2)
            y = (self.window.winfo_screenheight() // 2) - (900 // 2)
            self.window.geometry(f"1400x900+{x}+{y}")
            
            # Header
            header_frame = tk.Frame(self.window, bg='#2c3e50', height=80)
            header_frame.pack(fill='x')
            header_frame.pack_propagate(False)
            
            header_label = tk.Label(header_frame, 
                                   text="💾 Автоматическое Резервное Копирование и Восстановление",
                                   font=('Cambria', 24, 'bold'), 
                                   fg='white', bg='#2c3e50')
            header_label.pack(expand=True)
            
            # Status bar
            status_frame = tk.Frame(self.window, bg='#34495e', height=30)
            status_frame.pack(fill='x', side='bottom')
            status_frame.pack_propagate(False)
            
            self.status_label = tk.Label(status_frame, 
                                        text="Система резервного копирования готова к работе",
                                        font=('Cambria', 10), 
                                        fg='white', bg='#34495e')
            self.status_label.pack(side='left', padx=10, pady=5)
            
            # Main content with notebook
            self.notebook = ttk.Notebook(self.window)
            self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
            
            # Create tabs
            self.create_backup_overview_tab()
            self.create_backup_settings_tab()
            self.create_manual_backup_tab()
            self.create_restore_tab()
            self.create_cloud_storage_tab()
            self.create_disaster_recovery_tab()
            
            log_info("Окно резервного копирования открыто", "AutomatedBackup")
            
        except Exception as e:
            handle_module_error(e, "Резервное копирование", "открытие главного окна")
    
    def create_backup_overview_tab(self):
        """Create Backup Overview tab"""
        overview_frame = ttk.Frame(self.notebook)
        self.notebook.add(overview_frame, text="📊 Обзор")
        
        # Header
        header = tk.Label(overview_frame, text="Обзор Резервного Копирования",
                         font=('Cambria', 20, 'bold'), fg='maroon')
        header.pack(pady=20)
        
        # Status cards frame
        cards_frame = tk.Frame(overview_frame)
        cards_frame.pack(fill='x', padx=20, pady=10)
        
        # Last backup card
        last_backup_card = tk.LabelFrame(cards_frame, text="Последнее Резервное Копирование",
                                        font=('Cambria', 12, 'bold'), fg='maroon', bg='white')
        last_backup_card.pack(side='left', fill='both', expand=True, padx=5)
        
        self.last_backup_label = tk.Label(last_backup_card, text="Загрузка...",
                                         font=('Cambria', 14), bg='white')
        self.last_backup_label.pack(pady=20)
        
        # Next backup card
        next_backup_card = tk.LabelFrame(cards_frame, text="Следующее Резервное Копирование",
                                        font=('Cambria', 12, 'bold'), fg='maroon', bg='white')
        next_backup_card.pack(side='left', fill='both', expand=True, padx=5)
        
        self.next_backup_label = tk.Label(next_backup_card, text="Загрузка...",
                                         font=('Cambria', 14), bg='white')
        self.next_backup_label.pack(pady=20)
        
        # Storage usage card
        storage_card = tk.LabelFrame(cards_frame, text="Использование Хранилища",
                                    font=('Cambria', 12, 'bold'), fg='maroon', bg='white')
        storage_card.pack(side='left', fill='both', expand=True, padx=5)
        
        self.storage_label = tk.Label(storage_card, text="Загрузка...",
                                     font=('Cambria', 14), bg='white')
        self.storage_label.pack(pady=20)
        
        # Recent backups list
        recent_frame = tk.LabelFrame(overview_frame, text="Последние Резервные Копии",
                                    font=('Cambria', 14, 'bold'), fg='maroon', bg='white')
        recent_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Recent backups treeview
        recent_columns = ('Дата', 'Тип', 'Размер', 'Статус', 'Местоположение')
        self.recent_tree = ttk.Treeview(recent_frame, columns=recent_columns, show='headings', height=12)
        
        for col in recent_columns:
            self.recent_tree.heading(col, text=col)
            self.recent_tree.column(col, width=150)
        
        recent_scrollbar = ttk.Scrollbar(recent_frame, orient='vertical', command=self.recent_tree.yview)
        self.recent_tree.configure(yscrollcommand=recent_scrollbar.set)
        
        self.recent_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        recent_scrollbar.pack(side='right', fill='y', pady=10)
        
        # Load overview data
        self.load_backup_overview()
        
        # Control buttons
        overview_buttons_frame = tk.Frame(overview_frame)
        overview_buttons_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Button(overview_buttons_frame, text="🔄 Обновить",
                 command=self.load_backup_overview,
                 font=('Cambria', 12, 'bold'), bg='#2ecc71', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)
        
        tk.Button(overview_buttons_frame, text="💾 Создать Резервную Копию",
                 command=self.create_manual_backup,
                 font=('Cambria', 12, 'bold'), bg='#3498db', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)
        
        tk.Button(overview_buttons_frame, text="📊 Подробный Отчет",
                 command=self.generate_backup_report,
                 font=('Cambria', 12, 'bold'), bg='#f39c12', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

    def create_backup_settings_tab(self):
        """Create Backup Settings tab"""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="⚙️ Настройки")

        # Header
        header = tk.Label(settings_frame, text="Настройки Резервного Копирования",
                         font=('Cambria', 20, 'bold'), fg='maroon')
        header.pack(pady=20)

        # Local backup settings
        local_frame = tk.LabelFrame(settings_frame, text="Локальное Резервное Копирование",
                                   font=('Cambria', 14, 'bold'), fg='maroon', bg='white')
        local_frame.pack(fill='x', padx=20, pady=10)

        local_controls = tk.Frame(local_frame)
        local_controls.pack(fill='x', padx=10, pady=10)

        self.local_enabled_var = tk.BooleanVar(value=self.backup_config['local_backup']['enabled'])
        tk.Checkbutton(local_controls, text="Включить локальное резервное копирование",
                      variable=self.local_enabled_var,
                      font=('Cambria', 12, 'bold')).grid(row=0, column=0, columnspan=2, sticky='w', pady=5)

        tk.Label(local_controls, text="Путь для сохранения:",
                font=('Cambria', 11, 'bold')).grid(row=1, column=0, sticky='w', padx=5, pady=5)
        self.local_path_entry = tk.Entry(local_controls, font=('Cambria', 11), width=40)
        self.local_path_entry.grid(row=1, column=1, padx=5, pady=5)
        self.local_path_entry.insert(0, self.backup_config['local_backup']['path'])

        tk.Button(local_controls, text="📁 Выбрать",
                 command=self.select_local_backup_path,
                 font=('Cambria', 10, 'bold')).grid(row=1, column=2, padx=5, pady=5)

        tk.Label(local_controls, text="Расписание:",
                font=('Cambria', 11, 'bold')).grid(row=2, column=0, sticky='w', padx=5, pady=5)
        self.local_schedule_combo = ttk.Combobox(local_controls, font=('Cambria', 11), width=15)
        self.local_schedule_combo.grid(row=2, column=1, sticky='w', padx=5, pady=5)
        self.local_schedule_combo['values'] = ('daily', 'weekly', 'monthly')
        self.local_schedule_combo.set(self.backup_config['local_backup']['schedule'])

        tk.Label(local_controls, text="Время:",
                font=('Cambria', 11, 'bold')).grid(row=3, column=0, sticky='w', padx=5, pady=5)
        self.local_time_entry = tk.Entry(local_controls, font=('Cambria', 11), width=10)
        self.local_time_entry.grid(row=3, column=1, sticky='w', padx=5, pady=5)
        self.local_time_entry.insert(0, self.backup_config['local_backup']['time'])

        tk.Label(local_controls, text="Хранить дней:",
                font=('Cambria', 11, 'bold')).grid(row=4, column=0, sticky='w', padx=5, pady=5)
        self.local_retention_entry = tk.Entry(local_controls, font=('Cambria', 11), width=10)
        self.local_retention_entry.grid(row=4, column=1, sticky='w', padx=5, pady=5)
        self.local_retention_entry.insert(0, str(self.backup_config['local_backup']['retention_days']))

        # Compression settings
        compression_frame = tk.LabelFrame(settings_frame, text="Настройки Сжатия",
                                         font=('Cambria', 14, 'bold'), fg='maroon', bg='white')
        compression_frame.pack(fill='x', padx=20, pady=10)

        compression_controls = tk.Frame(compression_frame)
        compression_controls.pack(fill='x', padx=10, pady=10)

        self.compression_enabled_var = tk.BooleanVar(value=self.backup_config['compression']['enabled'])
        tk.Checkbutton(compression_controls, text="Включить сжатие",
                      variable=self.compression_enabled_var,
                      font=('Cambria', 12, 'bold')).grid(row=0, column=0, sticky='w', pady=5)

        tk.Label(compression_controls, text="Уровень сжатия (1-9):",
                font=('Cambria', 11, 'bold')).grid(row=1, column=0, sticky='w', padx=5, pady=5)
        self.compression_level_scale = tk.Scale(compression_controls, from_=1, to=9, orient='horizontal')
        self.compression_level_scale.grid(row=1, column=1, padx=5, pady=5)
        self.compression_level_scale.set(self.backup_config['compression']['level'])

        self.password_protected_var = tk.BooleanVar(value=self.backup_config['compression']['password_protected'])
        tk.Checkbutton(compression_controls, text="Защитить паролем",
                      variable=self.password_protected_var,
                      font=('Cambria', 11, 'bold')).grid(row=2, column=0, sticky='w', pady=5)

        # Verification settings
        verification_frame = tk.LabelFrame(settings_frame, text="Настройки Проверки",
                                          font=('Cambria', 14, 'bold'), fg='maroon', bg='white')
        verification_frame.pack(fill='x', padx=20, pady=10)

        verification_controls = tk.Frame(verification_frame)
        verification_controls.pack(fill='x', padx=10, pady=10)

        self.verification_enabled_var = tk.BooleanVar(value=self.backup_config['verification']['enabled'])
        tk.Checkbutton(verification_controls, text="Включить проверку целостности",
                      variable=self.verification_enabled_var,
                      font=('Cambria', 12, 'bold')).grid(row=0, column=0, sticky='w', pady=5)

        tk.Label(verification_controls, text="Алгоритм контрольной суммы:",
                font=('Cambria', 11, 'bold')).grid(row=1, column=0, sticky='w', padx=5, pady=5)
        self.checksum_combo = ttk.Combobox(verification_controls, font=('Cambria', 11), width=15)
        self.checksum_combo.grid(row=1, column=1, padx=5, pady=5)
        self.checksum_combo['values'] = ('md5', 'sha1', 'sha256', 'sha512')
        self.checksum_combo.set(self.backup_config['verification']['checksum_algorithm'])

        # Settings buttons
        settings_buttons_frame = tk.Frame(settings_frame)
        settings_buttons_frame.pack(fill='x', padx=20, pady=20)

        tk.Button(settings_buttons_frame, text="💾 Сохранить Настройки",
                 command=self.save_settings,
                 font=('Cambria', 12, 'bold'), bg='#2ecc71', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(settings_buttons_frame, text="🔄 Сбросить",
                 command=self.reset_settings,
                 font=('Cambria', 12, 'bold'), bg='#e74c3c', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(settings_buttons_frame, text="🧪 Тест Настроек",
                 command=self.test_backup_settings,
                 font=('Cambria', 12, 'bold'), bg='#f39c12', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

    def create_manual_backup_tab(self):
        """Create Manual Backup tab"""
        manual_frame = ttk.Frame(self.notebook)
        self.notebook.add(manual_frame, text="🔧 Ручное Копирование")

        # Header
        header = tk.Label(manual_frame, text="Ручное Резервное Копирование",
                         font=('Cambria', 20, 'bold'), fg='maroon')
        header.pack(pady=20)

        # Backup options frame
        options_frame = tk.LabelFrame(manual_frame, text="Параметры Резервного Копирования",
                                     font=('Cambria', 14, 'bold'), fg='maroon', bg='white')
        options_frame.pack(fill='x', padx=20, pady=10)

        options_controls = tk.Frame(options_frame)
        options_controls.pack(fill='x', padx=10, pady=10)

        # Backup type selection
        tk.Label(options_controls, text="Тип резервной копии:",
                font=('Cambria', 12, 'bold')).grid(row=0, column=0, sticky='w', padx=5, pady=10)

        self.backup_type_var = tk.StringVar(value='full')
        tk.Radiobutton(options_controls, text="Полная копия", variable=self.backup_type_var,
                      value='full', font=('Cambria', 11)).grid(row=0, column=1, sticky='w', padx=5)
        tk.Radiobutton(options_controls, text="Только база данных", variable=self.backup_type_var,
                      value='database', font=('Cambria', 11)).grid(row=0, column=2, sticky='w', padx=5)
        tk.Radiobutton(options_controls, text="Только файлы", variable=self.backup_type_var,
                      value='files', font=('Cambria', 11)).grid(row=0, column=3, sticky='w', padx=5)

        # Destination selection
        tk.Label(options_controls, text="Место сохранения:",
                font=('Cambria', 12, 'bold')).grid(row=1, column=0, sticky='w', padx=5, pady=10)
        self.manual_path_entry = tk.Entry(options_controls, font=('Cambria', 11), width=50)
        self.manual_path_entry.grid(row=1, column=1, columnspan=2, padx=5, pady=10)

        tk.Button(options_controls, text="📁 Выбрать",
                 command=self.select_manual_backup_path,
                 font=('Cambria', 10, 'bold')).grid(row=1, column=3, padx=5, pady=10)

        # Progress frame
        progress_frame = tk.LabelFrame(manual_frame, text="Прогресс Резервного Копирования",
                                      font=('Cambria', 14, 'bold'), fg='maroon', bg='white')
        progress_frame.pack(fill='x', padx=20, pady=10)

        self.backup_progress = ttk.Progressbar(progress_frame, mode='determinate')
        self.backup_progress.pack(fill='x', padx=10, pady=10)

        self.backup_status_label = tk.Label(progress_frame, text="Готов к созданию резервной копии",
                                           font=('Cambria', 11), bg='white')
        self.backup_status_label.pack(pady=5)

        # Manual backup buttons
        manual_buttons_frame = tk.Frame(manual_frame)
        manual_buttons_frame.pack(fill='x', padx=20, pady=20)

        tk.Button(manual_buttons_frame, text="💾 Создать Резервную Копию",
                 command=self.start_manual_backup,
                 font=('Cambria', 12, 'bold'), bg='#2ecc71', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(manual_buttons_frame, text="⏹️ Остановить",
                 command=self.stop_backup,
                 font=('Cambria', 12, 'bold'), bg='#e74c3c', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(manual_buttons_frame, text="📊 История Копирования",
                 command=self.show_backup_history,
                 font=('Cambria', 12, 'bold'), bg='#3498db', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

    def create_restore_tab(self):
        """Create Restore tab"""
        restore_frame = ttk.Frame(self.notebook)
        self.notebook.add(restore_frame, text="🔄 Восстановление")

        # Header
        header = tk.Label(restore_frame, text="Восстановление из Резервной Копии",
                         font=('Cambria', 20, 'bold'), fg='maroon')
        header.pack(pady=20)

        # Available backups frame
        backups_frame = tk.LabelFrame(restore_frame, text="Доступные Резервные Копии",
                                     font=('Cambria', 14, 'bold'), fg='maroon', bg='white')
        backups_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Backups treeview
        backup_columns = ('Дата', 'Тип', 'Размер', 'Местоположение', 'Статус')
        self.backups_tree = ttk.Treeview(backups_frame, columns=backup_columns, show='headings', height=12)

        for col in backup_columns:
            self.backups_tree.heading(col, text=col)
            self.backups_tree.column(col, width=150)

        backups_scrollbar = ttk.Scrollbar(backups_frame, orient='vertical', command=self.backups_tree.yview)
        self.backups_tree.configure(yscrollcommand=backups_scrollbar.set)

        self.backups_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        backups_scrollbar.pack(side='right', fill='y', pady=10)

        # Load available backups
        self.load_available_backups()

        # Restore options frame
        restore_options_frame = tk.LabelFrame(restore_frame, text="Параметры Восстановления",
                                             font=('Cambria', 14, 'bold'), fg='maroon', bg='white')
        restore_options_frame.pack(fill='x', padx=20, pady=10)

        restore_controls = tk.Frame(restore_options_frame)
        restore_controls.pack(fill='x', padx=10, pady=10)

        # Restore type
        tk.Label(restore_controls, text="Тип восстановления:",
                font=('Cambria', 12, 'bold')).grid(row=0, column=0, sticky='w', padx=5, pady=5)

        self.restore_type_var = tk.StringVar(value='full')
        tk.Radiobutton(restore_controls, text="Полное восстановление",
                      variable=self.restore_type_var, value='full',
                      font=('Cambria', 11)).grid(row=0, column=1, sticky='w', padx=5)
        tk.Radiobutton(restore_controls, text="Только база данных",
                      variable=self.restore_type_var, value='database',
                      font=('Cambria', 11)).grid(row=0, column=2, sticky='w', padx=5)
        tk.Radiobutton(restore_controls, text="Тестовое восстановление",
                      variable=self.restore_type_var, value='test',
                      font=('Cambria', 11)).grid(row=0, column=3, sticky='w', padx=5)

        # Restore destination
        tk.Label(restore_controls, text="Место восстановления:",
                font=('Cambria', 12, 'bold')).grid(row=1, column=0, sticky='w', padx=5, pady=5)
        self.restore_path_entry = tk.Entry(restore_controls, font=('Cambria', 11), width=50)
        self.restore_path_entry.grid(row=1, column=1, columnspan=2, padx=5, pady=5)
        self.restore_path_entry.insert(0, 'backups/restore')

        tk.Button(restore_controls, text="📁 Выбрать",
                 command=self.select_restore_path,
                 font=('Cambria', 10, 'bold')).grid(row=1, column=3, padx=5, pady=5)

        # Restore progress
        restore_progress_frame = tk.Frame(restore_frame)
        restore_progress_frame.pack(fill='x', padx=20, pady=10)

        self.restore_progress = ttk.Progressbar(restore_progress_frame, mode='determinate')
        self.restore_progress.pack(fill='x', pady=5)

        self.restore_status_label = tk.Label(restore_progress_frame, text="Готов к восстановлению",
                                            font=('Cambria', 11))
        self.restore_status_label.pack(pady=5)

        # Restore buttons
        restore_buttons_frame = tk.Frame(restore_frame)
        restore_buttons_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(restore_buttons_frame, text="🔄 Восстановить",
                 command=self.start_restore,
                 font=('Cambria', 12, 'bold'), bg='#2ecc71', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(restore_buttons_frame, text="🧪 Тест Восстановления",
                 command=self.test_restore,
                 font=('Cambria', 12, 'bold'), bg='#f39c12', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(restore_buttons_frame, text="🔍 Проверить Целостность",
                 command=self.verify_backup,
                 font=('Cambria', 12, 'bold'), bg='#9b59b6', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

    def create_cloud_storage_tab(self):
        """Create Cloud Storage tab"""
        cloud_frame = ttk.Frame(self.notebook)
        self.notebook.add(cloud_frame, text="☁️ Облачное Хранилище")

        # Header
        header = tk.Label(cloud_frame, text="Настройки Облачного Хранилища",
                         font=('Cambria', 20, 'bold'), fg='maroon')
        header.pack(pady=20)

        # Cloud provider selection
        provider_frame = tk.LabelFrame(cloud_frame, text="Выбор Провайдера",
                                      font=('Cambria', 14, 'bold'), fg='maroon', bg='white')
        provider_frame.pack(fill='x', padx=20, pady=10)

        provider_controls = tk.Frame(provider_frame)
        provider_controls.pack(fill='x', padx=10, pady=10)

        self.cloud_enabled_var = tk.BooleanVar(value=self.backup_config['cloud_backup']['enabled'])
        tk.Checkbutton(provider_controls, text="Включить облачное резервное копирование",
                      variable=self.cloud_enabled_var,
                      font=('Cambria', 12, 'bold')).grid(row=0, column=0, columnspan=3, sticky='w', pady=10)

        tk.Label(provider_controls, text="Провайдер:",
                font=('Cambria', 12, 'bold')).grid(row=1, column=0, sticky='w', padx=5, pady=5)

        self.cloud_provider_var = tk.StringVar(value=self.backup_config['cloud_backup']['provider'])
        providers = [('none', 'Не выбран'), ('aws_s3', 'Amazon S3'), ('dropbox', 'Dropbox'),
                    ('google_drive', 'Google Drive'), ('ftp', 'FTP Сервер')]

        for i, (value, text) in enumerate(providers):
            tk.Radiobutton(provider_controls, text=text, variable=self.cloud_provider_var,
                          value=value, font=('Cambria', 11)).grid(row=2+i//3, column=i%3, sticky='w', padx=10, pady=2)

        # Cloud credentials frame
        credentials_frame = tk.LabelFrame(cloud_frame, text="Учетные Данные",
                                         font=('Cambria', 14, 'bold'), fg='maroon', bg='white')
        credentials_frame.pack(fill='x', padx=20, pady=10)

        cred_controls = tk.Frame(credentials_frame)
        cred_controls.pack(fill='x', padx=10, pady=10)

        # Generic credential fields
        tk.Label(cred_controls, text="API Key / Access Key:",
                font=('Cambria', 11, 'bold')).grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.api_key_entry = tk.Entry(cred_controls, font=('Cambria', 11), width=40, show='*')
        self.api_key_entry.grid(row=0, column=1, padx=5, pady=5)

        tk.Label(cred_controls, text="Secret Key:",
                font=('Cambria', 11, 'bold')).grid(row=1, column=0, sticky='w', padx=5, pady=5)
        self.secret_key_entry = tk.Entry(cred_controls, font=('Cambria', 11), width=40, show='*')
        self.secret_key_entry.grid(row=1, column=1, padx=5, pady=5)

        tk.Label(cred_controls, text="Bucket / Folder:",
                font=('Cambria', 11, 'bold')).grid(row=2, column=0, sticky='w', padx=5, pady=5)
        self.bucket_entry = tk.Entry(cred_controls, font=('Cambria', 11), width=40)
        self.bucket_entry.grid(row=2, column=1, padx=5, pady=5)

        # Cloud settings
        cloud_settings_frame = tk.LabelFrame(cloud_frame, text="Настройки Облачного Копирования",
                                            font=('Cambria', 14, 'bold'), fg='maroon', bg='white')
        cloud_settings_frame.pack(fill='x', padx=20, pady=10)

        cloud_set_controls = tk.Frame(cloud_settings_frame)
        cloud_set_controls.pack(fill='x', padx=10, pady=10)

        tk.Label(cloud_set_controls, text="Расписание:",
                font=('Cambria', 11, 'bold')).grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.cloud_schedule_combo = ttk.Combobox(cloud_set_controls, font=('Cambria', 11), width=15)
        self.cloud_schedule_combo.grid(row=0, column=1, padx=5, pady=5)
        self.cloud_schedule_combo['values'] = ('daily', 'weekly', 'monthly')
        self.cloud_schedule_combo.set(self.backup_config['cloud_backup']['schedule'])

        tk.Label(cloud_set_controls, text="Хранить дней:",
                font=('Cambria', 11, 'bold')).grid(row=0, column=2, sticky='w', padx=5, pady=5)
        self.cloud_retention_entry = tk.Entry(cloud_set_controls, font=('Cambria', 11), width=10)
        self.cloud_retention_entry.grid(row=0, column=3, padx=5, pady=5)
        self.cloud_retention_entry.insert(0, str(self.backup_config['cloud_backup']['retention_days']))

        # Cloud buttons
        cloud_buttons_frame = tk.Frame(cloud_frame)
        cloud_buttons_frame.pack(fill='x', padx=20, pady=20)

        tk.Button(cloud_buttons_frame, text="🧪 Тест Подключения",
                 command=self.test_cloud_connection,
                 font=('Cambria', 12, 'bold'), bg='#f39c12', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(cloud_buttons_frame, text="💾 Сохранить Настройки",
                 command=self.save_cloud_settings,
                 font=('Cambria', 12, 'bold'), bg='#2ecc71', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(cloud_buttons_frame, text="☁️ Загрузить в Облако",
                 command=self.upload_to_cloud,
                 font=('Cambria', 12, 'bold'), bg='#3498db', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

    def create_disaster_recovery_tab(self):
        """Create Disaster Recovery tab"""
        disaster_frame = ttk.Frame(self.notebook)
        self.notebook.add(disaster_frame, text="🚨 Аварийное Восстановление")

        # Header
        header = tk.Label(disaster_frame, text="План Аварийного Восстановления",
                         font=('Cambria', 20, 'bold'), fg='maroon')
        header.pack(pady=20)

        # Recovery plan frame
        plan_frame = tk.LabelFrame(disaster_frame, text="План Восстановления",
                                  font=('Cambria', 14, 'bold'), fg='maroon', bg='white')
        plan_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Recovery steps
        steps_text = """
ПЛАН АВАРИЙНОГО ВОССТАНОВЛЕНИЯ СИСТЕМЫ УПРАВЛЕНИЯ РЕСТОРАНОМ

1. ОЦЕНКА СИТУАЦИИ
   • Определить масштаб повреждений
   • Проверить доступность резервных копий
   • Оценить время восстановления

2. ВОССТАНОВЛЕНИЕ БАЗЫ ДАННЫХ
   • Найти последнюю рабочую резервную копию
   • Проверить целостность резервной копии
   • Восстановить базу данных из резервной копии

3. ВОССТАНОВЛЕНИЕ ФАЙЛОВ СИСТЕМЫ
   • Восстановить файлы приложения
   • Проверить конфигурационные файлы
   • Восстановить пользовательские данные

4. ПРОВЕРКА СИСТЕМЫ
   • Запустить тесты целостности
   • Проверить все модули системы
   • Убедиться в корректности данных

5. ВОЗОБНОВЛЕНИЕ РАБОТЫ
   • Уведомить пользователей о восстановлении
   • Провести обучение при необходимости
   • Мониторинг стабильности системы

КОНТАКТЫ ЭКСТРЕННОГО ВОССТАНОВЛЕНИЯ:
• Системный администратор: +7 (XXX) XXX-XX-XX
• Техническая поддержка: <EMAIL>
• Резервный центр данных: <EMAIL>
        """

        plan_text = tk.Text(plan_frame, font=('Cambria', 11), wrap='word', height=20)
        plan_text.pack(fill='both', expand=True, padx=10, pady=10)
        plan_text.insert('1.0', steps_text)
        plan_text.config(state='disabled')

        # Recovery tools frame
        tools_frame = tk.LabelFrame(disaster_frame, text="Инструменты Восстановления",
                                   font=('Cambria', 14, 'bold'), fg='maroon', bg='white')
        tools_frame.pack(fill='x', padx=20, pady=10)

        tools_buttons_frame = tk.Frame(tools_frame)
        tools_buttons_frame.pack(fill='x', padx=10, pady=10)

        tk.Button(tools_buttons_frame, text="🔍 Диагностика Системы",
                 command=self.run_system_diagnostics,
                 font=('Cambria', 12, 'bold'), bg='#f39c12', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(tools_buttons_frame, text="🚨 Экстренное Восстановление",
                 command=self.emergency_restore,
                 font=('Cambria', 12, 'bold'), bg='#e74c3c', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(tools_buttons_frame, text="📋 Создать Отчет",
                 command=self.create_disaster_report,
                 font=('Cambria', 12, 'bold'), bg='#3498db', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

        tk.Button(tools_buttons_frame, text="📞 Связаться с Поддержкой",
                 command=self.contact_support,
                 font=('Cambria', 12, 'bold'), bg='#9b59b6', fg='white',
                 padx=20, pady=10).pack(side='left', padx=5)

    # Implementation methods
    def load_backup_overview(self):
        """Load backup overview data"""
        try:
            # Clear existing data
            for item in self.recent_tree.get_children():
                self.recent_tree.delete(item)

            cursor = self.backup_connection.cursor()

            # Get last backup info
            cursor.execute('''
                SELECT backup_name, end_time, status
                FROM backup_log
                WHERE status = 'success'
                ORDER BY end_time DESC
                LIMIT 1
            ''')
            last_backup = cursor.fetchone()

            if last_backup:
                last_time = datetime.fromisoformat(last_backup[1]).strftime("%d.%m.%Y %H:%M")
                self.last_backup_label.config(text=f"{last_backup[0]}\n{last_time}")
            else:
                self.last_backup_label.config(text="Резервные копии не найдены")

            # Calculate next backup time
            if self.backup_config['local_backup']['enabled']:
                next_time = "Сегодня в " + self.backup_config['local_backup']['time']
                self.next_backup_label.config(text=f"Локальное копирование\n{next_time}")
            else:
                self.next_backup_label.config(text="Автоматическое копирование\nотключено")

            # Calculate storage usage
            backup_path = self.backup_config['local_backup']['path']
            if os.path.exists(backup_path):
                total_size = 0
                for root, dirs, files in os.walk(backup_path):
                    for file in files:
                        total_size += os.path.getsize(os.path.join(root, file))

                size_mb = total_size / (1024 * 1024)
                self.storage_label.config(text=f"Локальное хранилище\n{size_mb:.1f} МБ")
            else:
                self.storage_label.config(text="Локальное хранилище\n0 МБ")

            # Load recent backups
            cursor.execute('''
                SELECT end_time, backup_type, file_size, status, file_path
                FROM backup_log
                ORDER BY end_time DESC
                LIMIT 10
            ''')

            recent_backups = cursor.fetchall()
            for backup in recent_backups:
                backup_time = datetime.fromisoformat(backup[0]).strftime("%d.%m.%Y %H:%M")
                size_mb = (backup[2] / (1024 * 1024)) if backup[2] else 0
                location = os.path.basename(backup[4]) if backup[4] else 'N/A'

                self.recent_tree.insert('', 'end', values=(
                    backup_time, backup[1], f"{size_mb:.1f} МБ",
                    backup[3], location
                ))

            log_info("Обзор резервного копирования обновлен", "AutomatedBackup")

        except Exception as e:
            handle_module_error(e, "Резервное копирование", "загрузка обзора")

    def create_backup(self, backup_type='full', destination=None):
        """Create backup with specified type and destination"""
        try:
            if not destination:
                destination = self.backup_config['local_backup']['path']

            # Create backup filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"restaurant_backup_{backup_type}_{timestamp}"

            # Log backup start
            cursor = self.backup_connection.cursor()
            cursor.execute('''
                INSERT INTO backup_log
                (backup_type, backup_name, start_time, status)
                VALUES (?, ?, CURRENT_TIMESTAMP, 'in_progress')
            ''', ('manual', backup_name))
            backup_id = cursor.lastrowid
            self.backup_connection.commit()

            # Create backup based on type
            if backup_type == 'full':
                backup_path = self.create_full_backup(backup_name, destination)
            elif backup_type == 'database':
                backup_path = self.create_database_backup(backup_name, destination)
            elif backup_type == 'files':
                backup_path = self.create_files_backup(backup_name, destination)
            else:
                raise ValueError(f"Unknown backup type: {backup_type}")

            # Calculate file size and checksum
            file_size = os.path.getsize(backup_path)
            checksum = self.calculate_checksum(backup_path)

            # Update backup log
            cursor.execute('''
                UPDATE backup_log
                SET end_time = CURRENT_TIMESTAMP, status = 'success',
                    file_path = ?, file_size = ?, checksum = ?
                WHERE id = ?
            ''', (backup_path, file_size, checksum, backup_id))
            self.backup_connection.commit()

            log_info(f"Резервная копия создана: {backup_name}", "AutomatedBackup")
            return backup_path

        except Exception as e:
            # Update backup log with error
            cursor.execute('''
                UPDATE backup_log
                SET end_time = CURRENT_TIMESTAMP, status = 'failed',
                    error_message = ?
                WHERE id = ?
            ''', (str(e), backup_id))
            self.backup_connection.commit()

            handle_module_error(e, "Резервное копирование", "создание резервной копии")
            return None

    def create_full_backup(self, backup_name, destination):
        """Create full system backup"""
        backup_path = os.path.join(destination, f"{backup_name}.zip")

        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # Add database files
            for root, dirs, files in os.walk('.'):
                for file in files:
                    if file.endswith('.db') or file.endswith('.sqlite'):
                        file_path = os.path.join(root, file)
                        zipf.write(file_path, file_path)

            # Add configuration files
            config_files = ['config.json', 'settings.ini']
            for config_file in config_files:
                if os.path.exists(config_file):
                    zipf.write(config_file, config_file)

            # Add modules and GUI files
            for folder in ['modules', 'gui', 'utils']:
                if os.path.exists(folder):
                    for root, dirs, files in os.walk(folder):
                        for file in files:
                            if file.endswith('.py'):
                                file_path = os.path.join(root, file)
                                zipf.write(file_path, file_path)

        return backup_path

    def calculate_checksum(self, file_path):
        """Calculate file checksum"""
        algorithm = self.backup_config['verification']['checksum_algorithm']
        hash_obj = hashlib.new(algorithm)

        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_obj.update(chunk)

        return hash_obj.hexdigest()

    # Placeholder methods for UI actions
    def select_local_backup_path(self):
        path = filedialog.askdirectory(title="Выберите папку для резервных копий")
        if path:
            self.local_path_entry.delete(0, tk.END)
            self.local_path_entry.insert(0, path)

    def save_settings(self):
        messagebox.showinfo("В разработке", "Функция сохранения настроек будет реализована")

    def reset_settings(self):
        messagebox.showinfo("В разработке", "Функция сброса настроек будет реализована")

    def test_backup_settings(self):
        messagebox.showinfo("В разработке", "Функция тестирования настроек будет реализована")

    def start_manual_backup(self):
        messagebox.showinfo("В разработке", "Функция ручного резервного копирования будет реализована")

    def stop_backup(self):
        messagebox.showinfo("В разработке", "Функция остановки резервного копирования будет реализована")

    def show_backup_history(self):
        messagebox.showinfo("В разработке", "Функция истории резервного копирования будет реализована")

    def load_available_backups(self):
        messagebox.showinfo("В разработке", "Функция загрузки доступных резервных копий будет реализована")

    def start_restore(self):
        messagebox.showinfo("В разработке", "Функция восстановления будет реализована")

    def test_restore(self):
        messagebox.showinfo("В разработке", "Функция тестового восстановления будет реализована")

    def verify_backup(self):
        messagebox.showinfo("В разработке", "Функция проверки целостности будет реализована")

    def test_cloud_connection(self):
        messagebox.showinfo("В разработке", "Функция тестирования облачного подключения будет реализована")

    def save_cloud_settings(self):
        messagebox.showinfo("В разработке", "Функция сохранения облачных настроек будет реализована")

    def upload_to_cloud(self):
        messagebox.showinfo("В разработке", "Функция загрузки в облако будет реализована")

    def run_system_diagnostics(self):
        messagebox.showinfo("В разработке", "Функция диагностики системы будет реализована")

    def emergency_restore(self):
        messagebox.showinfo("В разработке", "Функция экстренного восстановления будет реализована")

    def create_disaster_report(self):
        messagebox.showinfo("В разработке", "Функция создания отчета будет реализована")

    def contact_support(self):
        messagebox.showinfo("В разработке", "Функция связи с поддержкой будет реализована")

    def start_backup_scheduler(self):
        """Start backup scheduler"""
        messagebox.showinfo("В разработке", "Планировщик резервного копирования будет реализован")

    def create_manual_backup(self):
        """Create manual backup"""
        messagebox.showinfo("В разработке", "Функция создания ручной резервной копии будет реализована")

    def generate_backup_report(self):
        """Generate backup report"""
        messagebox.showinfo("В разработке", "Функция генерации отчета будет реализована")

    def select_manual_backup_path(self):
        """Select manual backup path"""
        path = filedialog.askdirectory(title="Выберите папку для сохранения резервной копии")
        if path:
            self.manual_path_entry.delete(0, tk.END)
            self.manual_path_entry.insert(0, path)

    def select_restore_path(self):
        """Select restore path"""
        path = filedialog.askdirectory(title="Выберите папку для восстановления")
        if path:
            self.restore_path_entry.delete(0, tk.END)
            self.restore_path_entry.insert(0, path)

    def create_database_backup(self, backup_name, destination):
        """Create database-only backup"""
        backup_path = os.path.join(destination, f"{backup_name}_db.zip")

        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk('.'):
                for file in files:
                    if file.endswith('.db') or file.endswith('.sqlite'):
                        file_path = os.path.join(root, file)
                        zipf.write(file_path, file_path)

        return backup_path

    def create_files_backup(self, backup_name, destination):
        """Create files-only backup"""
        backup_path = os.path.join(destination, f"{backup_name}_files.zip")

        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for folder in ['modules', 'gui', 'utils', 'reports']:
                if os.path.exists(folder):
                    for root, dirs, files in os.walk(folder):
                        for file in files:
                            file_path = os.path.join(root, file)
                            zipf.write(file_path, file_path)

        return backup_path


def create_automated_backup_manager(parent, db_manager):
    """Create and return Automated Backup Manager instance"""
    try:
        manager = AutomatedBackupManager(parent, db_manager)
        manager.show_backup_manager()
        return manager
    except Exception as e:
        handle_module_error(e, "Резервное копирование", "создание менеджера резервного копирования")
        return None
