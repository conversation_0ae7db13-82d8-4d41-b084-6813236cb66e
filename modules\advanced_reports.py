"""
Расширенная система отчётов с графиками и аналитикой
Профессиональные отчёты для ресторанного бизнеса
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json
import os

# Проверяем наличие matplotlib и других библиотек
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    import numpy as np
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

try:
    import seaborn as sns
    SEABORN_AVAILABLE = True
except ImportError:
    SEABORN_AVAILABLE = False

# Настройка стиля для графиков
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class AdvancedReportsManager:
    """Менеджер расширенных отчётов"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.current_report = None
        
        # Цвета для графиков
        self.colors = {
            'primary': '#1e40af',
            'secondary': '#7c3aed',
            'success': '#059669',
            'warning': '#d97706',
            'danger': '#dc2626',
            'info': '#0891b2'
        }
    
    def create_reports_window(self):
        """Создать окно расширенных отчётов"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("📊 Расширенные Отчёты и Аналитика")
        self.window.geometry("1400x900")
        self.window.configure(bg='#f8fafc')
        
        # Сделать окно изменяемым
        self.window.resizable(True, True)
        self.window.minsize(1200, 700)
        
        self.create_interface()
    
    def create_interface(self):
        """Создать интерфейс отчётов"""
        # Главный контейнер
        main_frame = tk.Frame(self.window, bg='#f8fafc')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Заголовок
        header_frame = tk.Frame(main_frame, bg='#1e40af', height=60)
        header_frame.pack(fill='x', pady=(0, 10))
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="📊 Расширенные Отчёты и Аналитика",
                font=('Arial', 16, 'bold'), bg='#1e40af', fg='white').pack(side='left', padx=20, pady=15)
        
        # Кнопка экспорта
        export_btn = tk.Button(header_frame, text="📤 Экспорт",
                              command=self.export_report,
                              bg='#059669', fg='white',
                              font=('Arial', 10, 'bold'),
                              relief='flat', padx=20, pady=8)
        export_btn.pack(side='right', padx=20, pady=15)
        
        # Основной контент
        content_frame = tk.Frame(main_frame, bg='#f8fafc')
        content_frame.pack(fill='both', expand=True)
        
        # Левая панель с типами отчётов
        left_panel = tk.Frame(content_frame, bg='white', width=300, relief='solid', bd=1)
        left_panel.pack(side='left', fill='y', padx=(0, 10))
        left_panel.pack_propagate(False)
        
        # Правая панель с отчётом
        self.report_panel = tk.Frame(content_frame, bg='white', relief='solid', bd=1)
        self.report_panel.pack(side='right', fill='both', expand=True)
        
        self.create_reports_menu(left_panel)
        self.show_dashboard()
    
    def create_reports_menu(self, parent):
        """Создать меню типов отчётов"""
        tk.Label(parent, text="Типы Отчётов", font=('Arial', 14, 'bold'),
                bg='white', fg='#1f2937').pack(pady=20)
        
        # Группы отчётов
        report_groups = {
            "📈 Продажи": [
                ("Динамика продаж", self.show_sales_dynamics),
                ("Топ блюд", self.show_top_dishes),
                ("Анализ по времени", self.show_time_analysis),
                ("Сравнение периодов", self.show_period_comparison)
            ],
            "💰 Финансы": [
                ("Прибыльность", self.show_profitability),
                ("Структура доходов", self.show_revenue_structure),
                ("Анализ затрат", self.show_cost_analysis),
                ("ROI по блюдам", self.show_dish_roi)
            ],
            "📦 Склад": [
                ("Оборачиваемость", self.show_inventory_turnover),
                ("ABC анализ", self.show_abc_analysis),
                ("Прогноз потребности", self.show_demand_forecast),
                ("Анализ поставщиков", self.show_supplier_analysis)
            ],
            "👥 Персонал": [
                ("Производительность", self.show_staff_performance),
                ("Анализ смен", self.show_shift_analysis),
                ("Затраты на персонал", self.show_staff_costs)
            ]
        }
        
        for group_name, reports in report_groups.items():
            # Заголовок группы
            group_frame = tk.Frame(parent, bg='#f3f4f6')
            group_frame.pack(fill='x', padx=10, pady=5)
            
            tk.Label(group_frame, text=group_name, font=('Arial', 11, 'bold'),
                    bg='#f3f4f6', fg='#374151').pack(anchor='w', padx=10, pady=5)
            
            # Отчёты в группе
            for report_name, command in reports:
                btn = tk.Button(parent, text=f"  {report_name}",
                               command=command,
                               bg='white', fg='#4b5563',
                               font=('Arial', 10),
                               relief='flat', anchor='w',
                               padx=20, pady=8)
                btn.pack(fill='x', padx=15, pady=1)
                
                # Эффект наведения
                def on_enter(e, button=btn):
                    button.configure(bg='#e5e7eb')
                
                def on_leave(e, button=btn):
                    button.configure(bg='white')
                
                btn.bind("<Enter>", on_enter)
                btn.bind("<Leave>", on_leave)
    
    def clear_report_panel(self):
        """Очистить панель отчёта"""
        for widget in self.report_panel.winfo_children():
            widget.destroy()
    
    def show_dashboard(self):
        """Показать главную панель"""
        self.clear_report_panel()
        
        # Заголовок
        tk.Label(self.report_panel, text="📊 Панель Аналитики",
                font=('Arial', 18, 'bold'), bg='white').pack(pady=20)
        
        # Создать фрейм для графиков
        charts_frame = tk.Frame(self.report_panel, bg='white')
        charts_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Создать несколько мини-графиков
        self.create_mini_dashboard(charts_frame)
    
    def create_mini_dashboard(self, parent):
        """Создать мини-панель с основными метриками"""
        # Верхний ряд - ключевые метрики
        metrics_frame = tk.Frame(parent, bg='white')
        metrics_frame.pack(fill='x', pady=(0, 20))
        
        metrics = [
            ("Продажи сегодня", "45 600₽", "#059669"),
            ("Средний чек", "385₽", "#1e40af"),
            ("Заказов сегодня", "118", "#7c3aed"),
            ("Прибыль", "12 400₽", "#d97706")
        ]
        
        for i, (title, value, color) in enumerate(metrics):
            metric_card = tk.Frame(metrics_frame, bg=color, relief='solid', bd=1)
            metric_card.pack(side='left', fill='both', expand=True, padx=5)
            
            tk.Label(metric_card, text=title, font=('Arial', 10),
                    bg=color, fg='white').pack(pady=(10, 5))
            tk.Label(metric_card, text=value, font=('Arial', 16, 'bold'),
                    bg=color, fg='white').pack(pady=(0, 10))
        
        # Нижний ряд - графики
        charts_container = tk.Frame(parent, bg='white')
        charts_container.pack(fill='both', expand=True)
        
        # Создать простые демо-графики
        self.create_demo_charts(charts_container)
    
    def create_demo_charts(self, parent):
        """Создать демонстрационные графики"""
        # Левый график - продажи по дням
        left_frame = tk.Frame(parent, bg='white')
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        fig1, ax1 = plt.subplots(figsize=(6, 4))
        days = ['Пн', 'Вт', 'Ср', 'Чт', 'Пт', 'Сб', 'Вс']
        sales = [35000, 42000, 38000, 45000, 52000, 68000, 58000]
        
        ax1.plot(days, sales, marker='o', linewidth=2, color=self.colors['primary'])
        ax1.set_title('Продажи по дням недели', fontsize=12, fontweight='bold')
        ax1.set_ylabel('Сумма (₽)')
        ax1.grid(True, alpha=0.3)
        
        canvas1 = FigureCanvasTkAgg(fig1, left_frame)
        canvas1.draw()
        canvas1.get_tk_widget().pack(fill='both', expand=True)
        
        # Правый график - топ блюд
        right_frame = tk.Frame(parent, bg='white')
        right_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        fig2, ax2 = plt.subplots(figsize=(6, 4))
        dishes = ['Борщ', 'Котлета', 'Салат Цезарь', 'Пельмени', 'Солянка']
        counts = [45, 38, 32, 28, 25]
        
        bars = ax2.bar(dishes, counts, color=[self.colors['success'], self.colors['warning'], 
                                            self.colors['info'], self.colors['secondary'], 
                                            self.colors['danger']])
        ax2.set_title('Топ-5 популярных блюд', fontsize=12, fontweight='bold')
        ax2.set_ylabel('Количество заказов')
        
        # Добавить значения на столбцы
        for bar, count in zip(bars, counts):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{count}', ha='center', va='bottom')
        
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        canvas2 = FigureCanvasTkAgg(fig2, right_frame)
        canvas2.draw()
        canvas2.get_tk_widget().pack(fill='both', expand=True)
    
    def show_sales_dynamics(self):
        """Показать динамику продаж"""
        self.clear_report_panel()
        
        tk.Label(self.report_panel, text="📈 Динамика Продаж",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)
        
        # Создать детальный график продаж
        chart_frame = tk.Frame(self.report_panel, bg='white')
        chart_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        
        # График 1: Продажи по месяцам
        months = ['Янв', 'Фев', 'Мар', 'Апр', 'Май', 'Июн']
        sales_2023 = [450000, 520000, 480000, 580000, 620000, 680000]
        sales_2024 = [480000, 550000, 510000, 610000, 650000, 720000]
        
        x = np.arange(len(months))
        width = 0.35
        
        ax1.bar(x - width/2, sales_2023, width, label='2023', color=self.colors['secondary'])
        ax1.bar(x + width/2, sales_2024, width, label='2024', color=self.colors['primary'])
        
        ax1.set_title('Сравнение продаж по месяцам', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Продажи (₽)')
        ax1.set_xticks(x)
        ax1.set_xticklabels(months)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # График 2: Тренд продаж
        days = list(range(1, 31))
        trend = [35000 + 500*i + np.random.normal(0, 2000) for i in days]
        
        ax2.plot(days, trend, color=self.colors['success'], linewidth=2)
        ax2.fill_between(days, trend, alpha=0.3, color=self.colors['success'])
        ax2.set_title('Тренд продаж за месяц', fontsize=14, fontweight='bold')
        ax2.set_xlabel('День месяца')
        ax2.set_ylabel('Продажи (₽)')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True)
    
    def show_top_dishes(self):
        """Показать топ блюд"""
        self.clear_report_panel()
        
        tk.Label(self.report_panel, text="🍽️ Анализ Популярности Блюд",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)
        
        chart_frame = tk.Frame(self.report_panel, bg='white')
        chart_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
        
        # График 1: Круговая диаграмма топ блюд
        dishes = ['Борщ украинский', 'Котлета по-киевски', 'Салат Цезарь', 
                 'Пельмени', 'Солянка', 'Другие']
        sales = [18, 15, 12, 10, 8, 37]
        colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99', '#ff99cc', '#c2c2f0']
        
        wedges, texts, autotexts = ax1.pie(sales, labels=dishes, autopct='%1.1f%%',
                                          colors=colors, startangle=90)
        ax1.set_title('Доля блюд в общих продажах', fontsize=12, fontweight='bold')
        
        # График 2: Столбчатая диаграмма по выручке
        revenue = [125000, 98000, 85000, 72000, 58000]
        dish_names = dishes[:5]
        
        bars = ax2.bar(dish_names, revenue, color=[self.colors['primary'], self.colors['success'],
                                                  self.colors['warning'], self.colors['info'],
                                                  self.colors['danger']])
        ax2.set_title('Выручка по блюдам', fontsize=12, fontweight='bold')
        ax2.set_ylabel('Выручка (₽)')
        
        # Повернуть подписи
        plt.setp(ax2.get_xticklabels(), rotation=45, ha='right')
        
        # Добавить значения на столбцы
        for bar, value in zip(bars, revenue):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 1000,
                    f'{value:,}₽', ha='center', va='bottom', fontsize=9)
        
        plt.tight_layout()
        
        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True)
    
    def show_profitability(self):
        """Показать анализ прибыльности"""
        self.clear_report_panel()
        
        tk.Label(self.report_panel, text="💰 Анализ Прибыльности",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)
        
        chart_frame = tk.Frame(self.report_panel, bg='white')
        chart_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Создать график прибыльности
        fig, ax = plt.subplots(figsize=(12, 6))
        
        categories = ['Супы', 'Горячие блюда', 'Салаты', 'Напитки', 'Десерты']
        revenue = [180000, 320000, 150000, 95000, 75000]
        costs = [85000, 180000, 75000, 35000, 30000]
        profit = [r - c for r, c in zip(revenue, costs)]
        
        x = np.arange(len(categories))
        width = 0.25
        
        ax.bar(x - width, revenue, width, label='Выручка', color=self.colors['primary'])
        ax.bar(x, costs, width, label='Затраты', color=self.colors['danger'])
        ax.bar(x + width, profit, width, label='Прибыль', color=self.colors['success'])
        
        ax.set_title('Анализ прибыльности по категориям', fontsize=14, fontweight='bold')
        ax.set_ylabel('Сумма (₽)')
        ax.set_xticks(x)
        ax.set_xticklabels(categories)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Добавить процент прибыльности
        for i, (r, c, p) in enumerate(zip(revenue, costs, profit)):
            margin = (p / r) * 100 if r > 0 else 0
            ax.text(i + width, p + 5000, f'{margin:.1f}%', 
                   ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        
        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True)
    
    def export_report(self):
        """Экспортировать текущий отчёт"""
        if not self.current_report:
            messagebox.showwarning("Предупреждение", "Нет активного отчёта для экспорта")
            return
        
        file_path = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[("PNG files", "*.png"), ("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                # Здесь можно добавить логику экспорта
                messagebox.showinfo("Успех", f"Отчёт экспортирован: {file_path}")
            except Exception as e:
                messagebox.showerror("Ошибка", f"Ошибка экспорта: {e}")
    
    def show_time_analysis(self):
        """Показать анализ по времени"""
        self.clear_report_panel()

        tk.Label(self.report_panel, text="⏰ Анализ Продаж по Времени",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        chart_frame = tk.Frame(self.report_panel, bg='white')
        chart_frame.pack(fill='both', expand=True, padx=20, pady=10)

        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))

        # График 1: Продажи по часам
        hours = list(range(10, 23))  # 10:00 - 22:00
        sales_by_hour = [5000, 8000, 12000, 18000, 25000, 35000, 42000, 38000,
                        45000, 52000, 48000, 35000, 15000]

        ax1.bar(hours, sales_by_hour, color=self.colors['primary'], alpha=0.7)
        ax1.set_title('Продажи по часам дня', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Час')
        ax1.set_ylabel('Продажи (₽)')
        ax1.grid(True, alpha=0.3)

        # График 2: Продажи по дням недели
        days = ['Пн', 'Вт', 'Ср', 'Чт', 'Пт', 'Сб', 'Вс']
        daily_sales = [35000, 42000, 38000, 45000, 52000, 68000, 58000]

        ax2.plot(days, daily_sales, marker='o', linewidth=3, markersize=8, color=self.colors['success'])
        ax2.fill_between(days, daily_sales, alpha=0.3, color=self.colors['success'])
        ax2.set_title('Продажи по дням недели', fontsize=14, fontweight='bold')
        ax2.set_ylabel('Продажи (₽)')
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()

        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True)

    def show_period_comparison(self):
        """Показать сравнение периодов"""
        self.clear_report_panel()

        tk.Label(self.report_panel, text="📊 Сравнение Периодов",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        chart_frame = tk.Frame(self.report_panel, bg='white')
        chart_frame.pack(fill='both', expand=True, padx=20, pady=10)

        fig, ax = plt.subplots(figsize=(12, 6))

        # Данные для сравнения
        months = ['Янв', 'Фев', 'Мар', 'Апр', 'Май', 'Июн']
        current_year = [480000, 550000, 510000, 610000, 650000, 720000]
        previous_year = [450000, 520000, 480000, 580000, 620000, 680000]

        x = np.arange(len(months))
        width = 0.35

        bars1 = ax.bar(x - width/2, previous_year, width, label='2023',
                      color=self.colors['secondary'], alpha=0.8)
        bars2 = ax.bar(x + width/2, current_year, width, label='2024',
                      color=self.colors['primary'], alpha=0.8)

        ax.set_title('Сравнение продаж: 2023 vs 2024', fontsize=14, fontweight='bold')
        ax.set_ylabel('Продажи (₽)')
        ax.set_xticks(x)
        ax.set_xticklabels(months)
        ax.legend()
        ax.grid(True, alpha=0.3)

        # Добавить проценты роста
        for i, (prev, curr) in enumerate(zip(previous_year, current_year)):
            growth = ((curr - prev) / prev) * 100
            ax.text(i, max(prev, curr) + 10000, f'+{growth:.1f}%',
                   ha='center', va='bottom', fontweight='bold', color='green' if growth > 0 else 'red')

        plt.tight_layout()

        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True)

    def show_revenue_structure(self):
        """Показать структуру доходов"""
        self.clear_report_panel()

        tk.Label(self.report_panel, text="💰 Структура Доходов",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        chart_frame = tk.Frame(self.report_panel, bg='white')
        chart_frame.pack(fill='both', expand=True, padx=20, pady=10)

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))

        # График 1: Структура по категориям
        categories = ['Горячие блюда', 'Супы', 'Салаты', 'Напитки', 'Десерты']
        revenue = [320000, 180000, 150000, 95000, 75000]
        colors = [self.colors['primary'], self.colors['success'], self.colors['warning'],
                 self.colors['info'], self.colors['danger']]

        wedges, texts, autotexts = ax1.pie(revenue, labels=categories, autopct='%1.1f%%',
                                          colors=colors, startangle=90)
        ax1.set_title('Структура доходов по категориям', fontsize=12, fontweight='bold')

        # График 2: Динамика по категориям
        months = ['Янв', 'Фев', 'Мар', 'Апр', 'Май']
        hot_dishes = [280000, 300000, 310000, 320000, 340000]
        soups = [160000, 170000, 175000, 180000, 185000]

        ax2.plot(months, hot_dishes, marker='o', label='Горячие блюда', color=self.colors['primary'])
        ax2.plot(months, soups, marker='s', label='Супы', color=self.colors['success'])
        ax2.set_title('Динамика доходов по категориям', fontsize=12, fontweight='bold')
        ax2.set_ylabel('Доходы (₽)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()

        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True)

    def show_cost_analysis(self):
        """Показать анализ затрат"""
        self.clear_report_panel()

        tk.Label(self.report_panel, text="📉 Анализ Затрат",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        chart_frame = tk.Frame(self.report_panel, bg='white')
        chart_frame.pack(fill='both', expand=True, padx=20, pady=10)

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))

        # График 1: Структура затрат
        cost_categories = ['Продукты', 'Зарплата', 'Аренда', 'Коммунальные', 'Прочие']
        costs = [180000, 150000, 80000, 25000, 35000]

        wedges, texts, autotexts = ax1.pie(costs, labels=cost_categories, autopct='%1.1f%%',
                                          startangle=90)
        ax1.set_title('Структура затрат', fontsize=12, fontweight='bold')

        # График 2: Динамика затрат
        months = ['Янв', 'Фев', 'Мар', 'Апр', 'Май']
        total_costs = [450000, 460000, 470000, 480000, 470000]

        ax2.bar(months, total_costs, color=self.colors['danger'], alpha=0.7)
        ax2.set_title('Динамика общих затрат', fontsize=12, fontweight='bold')
        ax2.set_ylabel('Затраты (₽)')
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()

        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True)

    def show_dish_roi(self):
        """Показать ROI по блюдам"""
        self.clear_report_panel()

        tk.Label(self.report_panel, text="📈 ROI по Блюдам",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        chart_frame = tk.Frame(self.report_panel, bg='white')
        chart_frame.pack(fill='both', expand=True, padx=20, pady=10)

        fig, ax = plt.subplots(figsize=(12, 6))

        # Данные ROI по блюдам
        dishes = ['Стейк рибай', 'Борщ', 'Салат Цезарь', 'Котлета', 'Пельмени']
        roi_values = [85, 65, 78, 55, 45]  # ROI в процентах
        colors_roi = [self.colors['success'] if roi > 60 else self.colors['warning'] if roi > 40 else self.colors['danger'] for roi in roi_values]

        bars = ax.bar(dishes, roi_values, color=colors_roi, alpha=0.8)
        ax.set_title('ROI по блюдам (%)', fontsize=14, fontweight='bold')
        ax.set_ylabel('ROI (%)')
        ax.grid(True, alpha=0.3)

        # Добавить значения на столбцы
        for bar, roi in zip(bars, roi_values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                   f'{roi}%', ha='center', va='bottom', fontweight='bold')

        # Добавить линию целевого ROI
        ax.axhline(y=60, color='red', linestyle='--', alpha=0.7, label='Целевой ROI (60%)')
        ax.legend()

        plt.xticks(rotation=45)
        plt.tight_layout()

        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True)
    
    def show_inventory_turnover(self):
        """Показать оборачиваемость склада"""
        self.clear_report_panel()

        tk.Label(self.report_panel, text="📦 Оборачиваемость Склада",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        chart_frame = tk.Frame(self.report_panel, bg='white')
        chart_frame.pack(fill='both', expand=True, padx=20, pady=10)

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))

        # График 1: Оборачиваемость по категориям
        categories = ['Мясо', 'Овощи', 'Молочные', 'Крупы', 'Специи']
        turnover_days = [3, 2, 5, 15, 30]  # дни

        bars = ax1.bar(categories, turnover_days, color=[self.colors['success'] if days <= 7 else
                                                        self.colors['warning'] if days <= 14 else
                                                        self.colors['danger'] for days in turnover_days])
        ax1.set_title('Оборачиваемость по категориям (дни)', fontsize=12, fontweight='bold')
        ax1.set_ylabel('Дни')

        # График 2: Динамика оборачиваемости
        months = ['Янв', 'Фев', 'Мар', 'Апр', 'Май']
        avg_turnover = [8, 7, 6, 7, 6]

        ax2.plot(months, avg_turnover, marker='o', linewidth=2, color=self.colors['primary'])
        ax2.set_title('Средняя оборачиваемость склада', fontsize=12, fontweight='bold')
        ax2.set_ylabel('Дни')
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()

        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True)

    def show_abc_analysis(self):
        """Показать ABC анализ"""
        self.clear_report_panel()

        tk.Label(self.report_panel, text="📊 ABC Анализ Товаров",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        chart_frame = tk.Frame(self.report_panel, bg='white')
        chart_frame.pack(fill='both', expand=True, padx=20, pady=10)

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))

        # График 1: Распределение ABC
        abc_categories = ['A (20%)', 'B (30%)', 'C (50%)']
        abc_revenue = [70, 20, 10]  # проценты от выручки
        colors_abc = [self.colors['success'], self.colors['warning'], self.colors['danger']]

        wedges, texts, autotexts = ax1.pie(abc_revenue, labels=abc_categories, autopct='%1.1f%%',
                                          colors=colors_abc, startangle=90)
        ax1.set_title('ABC анализ: доля в выручке', fontsize=12, fontweight='bold')

        # График 2: Количество товаров по категориям
        items_count = [25, 45, 80]
        ax2.bar(abc_categories, items_count, color=colors_abc, alpha=0.7)
        ax2.set_title('Количество товаров по категориям', fontsize=12, fontweight='bold')
        ax2.set_ylabel('Количество товаров')

        plt.tight_layout()

        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True)

    def show_demand_forecast(self):
        """Показать прогноз потребности"""
        self.clear_report_panel()

        tk.Label(self.report_panel, text="🔮 Прогноз Потребности",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        chart_frame = tk.Frame(self.report_panel, bg='white')
        chart_frame.pack(fill='both', expand=True, padx=20, pady=10)

        fig, ax = plt.subplots(figsize=(12, 6))

        # Исторические данные и прогноз
        days = list(range(1, 31))
        historical = [100 + 10*np.sin(i/5) + np.random.normal(0, 5) for i in days[:20]]
        forecast = [100 + 10*np.sin(i/5) + 2*i for i in days[20:]]

        ax.plot(days[:20], historical, 'o-', label='Исторические данные', color=self.colors['primary'])
        ax.plot(days[19:], [historical[-1]] + forecast, '--', label='Прогноз', color=self.colors['danger'])

        ax.set_title('Прогноз потребности в продуктах', fontsize=14, fontweight='bold')
        ax.set_xlabel('День месяца')
        ax.set_ylabel('Потребность (кг)')
        ax.legend()
        ax.grid(True, alpha=0.3)

        # Добавить доверительный интервал
        forecast_upper = [f + 10 for f in forecast]
        forecast_lower = [f - 10 for f in forecast]
        ax.fill_between(days[20:], forecast_lower, forecast_upper, alpha=0.2, color=self.colors['danger'])

        plt.tight_layout()

        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True)

    def show_supplier_analysis(self):
        """Показать анализ поставщиков"""
        self.clear_report_panel()

        tk.Label(self.report_panel, text="🏢 Анализ Поставщиков",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        chart_frame = tk.Frame(self.report_panel, bg='white')
        chart_frame.pack(fill='both', expand=True, padx=20, pady=10)

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))

        # График 1: Рейтинг поставщиков
        suppliers = ['Мука-Сервис', 'Молочный Дом', 'Мясокомбинат', 'Овощи-Плюс']
        ratings = [4.8, 4.5, 4.9, 4.2]

        bars = ax1.bar(suppliers, ratings, color=[self.colors['success'] if r >= 4.5 else
                                                 self.colors['warning'] for r in ratings])
        ax1.set_title('Рейтинг поставщиков', fontsize=12, fontweight='bold')
        ax1.set_ylabel('Рейтинг')
        ax1.set_ylim(0, 5)
        plt.setp(ax1.get_xticklabels(), rotation=45, ha='right')

        # График 2: Объёмы поставок
        volumes = [125000, 89000, 256000, 64000]
        ax2.pie(volumes, labels=suppliers, autopct='%1.1f%%', startangle=90)
        ax2.set_title('Доля в объёме поставок', fontsize=12, fontweight='bold')

        plt.tight_layout()

        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True)

    def show_staff_performance(self):
        """Показать производительность персонала"""
        self.clear_report_panel()

        tk.Label(self.report_panel, text="👥 Производительность Персонала",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        chart_frame = tk.Frame(self.report_panel, bg='white')
        chart_frame.pack(fill='both', expand=True, padx=20, pady=10)

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))

        # График 1: Производительность по сотрудникам
        staff = ['Иванов И.', 'Петрова М.', 'Сидоров А.', 'Козлова Е.']
        performance = [95, 88, 92, 85]  # процент выполнения плана

        bars = ax1.bar(staff, performance, color=[self.colors['success'] if p >= 90 else
                                                 self.colors['warning'] if p >= 80 else
                                                 self.colors['danger'] for p in performance])
        ax1.set_title('Выполнение плана (%)', fontsize=12, fontweight='bold')
        ax1.set_ylabel('Процент выполнения')
        ax1.axhline(y=90, color='red', linestyle='--', alpha=0.7, label='Норма (90%)')
        ax1.legend()

        # График 2: Динамика производительности
        months = ['Янв', 'Фев', 'Мар', 'Апр', 'Май']
        avg_performance = [85, 87, 90, 92, 90]

        ax2.plot(months, avg_performance, marker='o', linewidth=2, color=self.colors['primary'])
        ax2.set_title('Средняя производительность команды', fontsize=12, fontweight='bold')
        ax2.set_ylabel('Процент выполнения')
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()

        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True)

    def show_shift_analysis(self):
        """Показать анализ смен"""
        self.clear_report_panel()

        tk.Label(self.report_panel, text="⏰ Анализ Смен",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        chart_frame = tk.Frame(self.report_panel, bg='white')
        chart_frame.pack(fill='both', expand=True, padx=20, pady=10)

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))

        # График 1: Загрузка по сменам
        shifts = ['Утренняя\n(8-16)', 'Дневная\n(12-20)', 'Вечерняя\n(16-24)']
        workload = [75, 95, 85]  # процент загрузки

        bars = ax1.bar(shifts, workload, color=[self.colors['warning'], self.colors['danger'], self.colors['success']])
        ax1.set_title('Загрузка по сменам (%)', fontsize=12, fontweight='bold')
        ax1.set_ylabel('Процент загрузки')
        ax1.axhline(y=80, color='red', linestyle='--', alpha=0.7, label='Оптимальная загрузка')
        ax1.legend()

        # График 2: Выручка по сменам
        revenue_by_shift = [85000, 145000, 120000]
        ax2.pie(revenue_by_shift, labels=shifts, autopct='%1.1f%%', startangle=90)
        ax2.set_title('Распределение выручки по сменам', fontsize=12, fontweight='bold')

        plt.tight_layout()

        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True)

    def show_staff_costs(self):
        """Показать затраты на персонал"""
        self.clear_report_panel()

        tk.Label(self.report_panel, text="💰 Затраты на Персонал",
                font=('Arial', 16, 'bold'), bg='white').pack(pady=20)

        chart_frame = tk.Frame(self.report_panel, bg='white')
        chart_frame.pack(fill='both', expand=True, padx=20, pady=10)

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))

        # График 1: Структура затрат на персонал
        cost_types = ['Зарплата', 'Премии', 'Соц.взносы', 'Обучение', 'Прочее']
        costs = [120000, 25000, 36000, 8000, 5000]

        wedges, texts, autotexts = ax1.pie(costs, labels=cost_types, autopct='%1.1f%%', startangle=90)
        ax1.set_title('Структура затрат на персонал', fontsize=12, fontweight='bold')

        # График 2: Динамика затрат
        months = ['Янв', 'Фев', 'Мар', 'Апр', 'Май']
        total_staff_costs = [180000, 185000, 190000, 194000, 194000]

        ax2.plot(months, total_staff_costs, marker='o', linewidth=2, color=self.colors['primary'])
        ax2.set_title('Динамика затрат на персонал', fontsize=12, fontweight='bold')
        ax2.set_ylabel('Затраты (₽)')
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()

        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True)


def create_advanced_reports(parent, db_manager):
    """Create and show the advanced reports window"""
    reports = AdvancedReportsSystem(parent, db_manager)
    reports.create_reports_window()
    return reports
