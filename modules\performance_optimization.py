"""
Performance Optimization System
Comprehensive performance monitoring, optimization, and system enhancement tools
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
import json
import sys
import os
import threading
import time
import psutil
import gc
from typing import Dict, List, Optional, Tuple, Any
import sqlite3

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gui.styles import ModernStyles
from utils.error_handling import handle_module_error, log_info
from utils.performance_optimizer import OptimizedDatabaseManager, PerformanceMonitor, QueryCache, LazyDataLoader

class PerformanceOptimizationSystem:
    """Comprehensive performance optimization and monitoring system"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.performance_monitor = PerformanceMonitor()
        self.query_cache = QueryCache()
        self.lazy_loader = LazyDataLoader(db_manager)
        self.optimized_db = OptimizedDatabaseManager(db_manager)
        
        # Performance metrics
        self.metrics = {
            'system': {
                'cpu_usage': 0.0,
                'memory_usage': 0.0,
                'disk_usage': 0.0,
                'network_io': 0.0
            },
            'database': {
                'query_count': 0,
                'avg_query_time': 0.0,
                'slow_queries': 0,
                'cache_hit_rate': 0.0,
                'connection_pool_usage': 0.0
            },
            'ui': {
                'window_count': 0,
                'memory_usage': 0.0,
                'response_time': 0.0,
                'update_frequency': 0.0
            }
        }
        
        # Optimization settings
        self.optimization_settings = {
            'database': {
                'enable_query_cache': True,
                'cache_ttl': 300,
                'enable_lazy_loading': True,
                'page_size': 100,
                'enable_connection_pooling': True,
                'max_connections': 20
            },
            'ui': {
                'enable_virtual_scrolling': True,
                'lazy_load_images': True,
                'debounce_updates': True,
                'update_interval': 1000
            },
            'system': {
                'enable_garbage_collection': True,
                'gc_interval': 60,
                'enable_memory_monitoring': True,
                'memory_threshold': 80.0
            }
        }
        
        # Monitoring thread
        self.monitoring_active = False
        self.monitoring_thread = None
        
        # Initialize data
        self.load_performance_data()
        
        # Create main window
        self.create_main_window()
        
        # Start monitoring
        self.start_monitoring()
    
    def load_performance_data(self):
        """Load performance data and metrics"""
        self.update_system_metrics()
        self.update_database_metrics()
        self.update_ui_metrics()
    
    def update_system_metrics(self):
        """Update system performance metrics"""
        try:
            # CPU usage
            self.metrics['system']['cpu_usage'] = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            self.metrics['system']['memory_usage'] = memory.percent
            
            # Disk usage
            disk = psutil.disk_usage('/')
            self.metrics['system']['disk_usage'] = (disk.used / disk.total) * 100
            
            # Network I/O
            net_io = psutil.net_io_counters()
            self.metrics['system']['network_io'] = net_io.bytes_sent + net_io.bytes_recv
            
        except Exception as e:
            log_info(f"Error updating system metrics: {e}", "PerformanceOptimization")
    
    def update_database_metrics(self):
        """Update database performance metrics"""
        try:
            # Get database connection pool stats
            if hasattr(self.db_manager, 'connection_pool'):
                pool_stats = self.db_manager.connection_pool.get_stats()
                self.metrics['database']['connection_pool_usage'] = (
                    pool_stats['active_count'] / pool_stats.get('max_connections', 20) * 100
                )
                self.metrics['database']['query_count'] = pool_stats['query_count']
                self.metrics['database']['avg_query_time'] = pool_stats['avg_query_time']
            
            # Get cache stats
            cache_stats = self.query_cache.get_stats()
            if cache_stats['total_requests'] > 0:
                self.metrics['database']['cache_hit_rate'] = (
                    cache_stats['hits'] / cache_stats['total_requests'] * 100
                )
            
        except Exception as e:
            log_info(f"Error updating database metrics: {e}", "PerformanceOptimization")
    
    def update_ui_metrics(self):
        """Update UI performance metrics"""
        try:
            # Count open windows
            self.metrics['ui']['window_count'] = len(tk._default_root.winfo_children()) if tk._default_root else 0
            
            # Memory usage (approximate)
            process = psutil.Process()
            self.metrics['ui']['memory_usage'] = process.memory_info().rss / 1024 / 1024  # MB
            
        except Exception as e:
            log_info(f"Error updating UI metrics: {e}", "PerformanceOptimization")
    
    def create_main_window(self):
        """Create main performance optimization window"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("⚡ Оптимизация Производительности")
        self.window.geometry("1400x900")
        self.window.configure(bg=ModernStyles.COLORS['bg_primary'])
        
        # Make window resizable and center it
        self.window.resizable(True, True)
        self.center_window()
        
        # Create header
        self.create_header()
        
        # Create main content
        self.create_main_content()
        
        # Create status bar
        self.create_status_bar()
        
        # Load initial data
        self.refresh_all_displays()
    
    def center_window(self):
        """Center window on screen"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (1400 // 2)
        y = (self.window.winfo_screenheight() // 2) - (900 // 2)
        self.window.geometry(f"1400x900+{x}+{y}")
    
    def create_header(self):
        """Create header section"""
        header_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['primary'], height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        # Title
        title_label = tk.Label(
            header_frame,
            text="⚡ Оптимизация Производительности",
            font=ModernStyles.FONTS['title'],
            bg=ModernStyles.COLORS['primary'],
            fg='white'
        )
        title_label.pack(side='left', padx=20, pady=20)
        
        # Performance status
        self.performance_status_label = tk.Label(
            header_frame,
            text="📊 Мониторинг активен",
            font=ModernStyles.FONTS['body'],
            bg=ModernStyles.COLORS['primary'],
            fg='white'
        )
        self.performance_status_label.pack(side='left', padx=20, pady=20)
        
        # Action buttons
        buttons_frame = tk.Frame(header_frame, bg=ModernStyles.COLORS['primary'])
        buttons_frame.pack(side='right', padx=20, pady=20)
        
        action_buttons = [
            ("🚀 Оптимизировать", self.run_optimization),
            ("🧹 Очистить Кэш", self.clear_cache),
            ("📊 Анализ", self.run_analysis),
            ("⚙️ Настройки", self.open_settings)
        ]
        
        for text, command in action_buttons:
            btn = tk.Button(
                buttons_frame,
                text=text,
                command=command,
                font=ModernStyles.FONTS['button'],
                bg='white',
                fg=ModernStyles.COLORS['primary'],
                relief='flat',
                padx=15,
                pady=5
            )
            btn.pack(side='left', padx=5)
    
    def create_main_content(self):
        """Create main content area"""
        # Create notebook for different sections
        self.notebook = ttk.Notebook(self.window)
        self.notebook.pack(fill='both', expand=True, padx=20, pady=(0, 20))
        
        # Create tabs
        self.create_overview_tab()
        self.create_database_optimization_tab()
        self.create_ui_optimization_tab()
        self.create_system_monitoring_tab()
        self.create_cache_management_tab()
        self.create_query_analysis_tab()
        self.create_recommendations_tab()
    
    def create_status_bar(self):
        """Create status bar"""
        self.status_frame = tk.Frame(self.window, bg=ModernStyles.COLORS['bg_secondary'], height=30)
        self.status_frame.pack(fill='x', side='bottom')
        self.status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(
            self.status_frame,
            text="Система оптимизации готова к работе",
            font=ModernStyles.FONTS['small'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        self.status_label.pack(side='left', padx=10, pady=5)
        
        # Performance indicator
        self.performance_indicator = tk.Label(
            self.status_frame,
            text="⚡ Производительность: Отлично",
            font=ModernStyles.FONTS['small'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg='green'
        )
        self.performance_indicator.pack(side='right', padx=10, pady=5)
    
    def start_monitoring(self):
        """Start performance monitoring"""
        if not self.monitoring_active:
            self.monitoring_active = True
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()
    
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.monitoring_active = False
    
    def _monitoring_loop(self):
        """Performance monitoring loop"""
        while self.monitoring_active:
            try:
                self.update_system_metrics()
                self.update_database_metrics()
                self.update_ui_metrics()
                
                # Update UI if window is still open
                if self.window.winfo_exists():
                    self.window.after(0, self.update_performance_displays)
                
                time.sleep(5)  # Update every 5 seconds
                
            except Exception as e:
                log_info(f"Error in monitoring loop: {e}", "PerformanceOptimization")
                time.sleep(10)  # Wait longer on error
    
    def update_performance_displays(self):
        """Update performance displays in UI"""
        try:
            # Update performance indicator
            cpu_usage = self.metrics['system']['cpu_usage']
            memory_usage = self.metrics['system']['memory_usage']
            
            if cpu_usage < 50 and memory_usage < 70:
                status = "Отлично"
                color = "green"
            elif cpu_usage < 80 and memory_usage < 85:
                status = "Хорошо"
                color = "orange"
            else:
                status = "Требует внимания"
                color = "red"
            
            self.performance_indicator.config(
                text=f"⚡ Производительность: {status}",
                fg=color
            )
            
        except Exception as e:
            log_info(f"Error updating performance displays: {e}", "PerformanceOptimization")

    def create_overview_tab(self):
        """Create overview tab with performance metrics"""
        overview_frame = ttk.Frame(self.notebook)
        self.notebook.add(overview_frame, text="📊 Обзор")

        # Create scrollable frame
        canvas = tk.Canvas(overview_frame, bg='white')
        scrollbar = ttk.Scrollbar(overview_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Performance metrics cards
        metrics_frame = tk.Frame(scrollable_frame, bg='white')
        metrics_frame.pack(fill='x', padx=20, pady=20)

        # System metrics
        system_frame = self.create_metrics_section(metrics_frame, "🖥️ Система")
        self.create_metric_card(system_frame, "Процессор", "cpu_usage", "%", 'system')
        self.create_metric_card(system_frame, "Память", "memory_usage", "%", 'system')
        self.create_metric_card(system_frame, "Диск", "disk_usage", "%", 'system')

        # Database metrics
        db_frame = self.create_metrics_section(metrics_frame, "🗄️ База Данных")
        self.create_metric_card(db_frame, "Запросы", "query_count", "", 'database')
        self.create_metric_card(db_frame, "Время запроса", "avg_query_time", "мс", 'database')
        self.create_metric_card(db_frame, "Кэш", "cache_hit_rate", "%", 'database')

        # UI metrics
        ui_frame = self.create_metrics_section(metrics_frame, "🖼️ Интерфейс")
        self.create_metric_card(ui_frame, "Окна", "window_count", "", 'ui')
        self.create_metric_card(ui_frame, "Память UI", "memory_usage", "МБ", 'ui')

        # Performance chart
        chart_frame = tk.Frame(scrollable_frame, bg='white', relief='solid', bd=1)
        chart_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        chart_title = tk.Label(
            chart_frame,
            text="📈 График Производительности",
            font=ModernStyles.FONTS['subtitle'],
            bg='white',
            fg=ModernStyles.COLORS['text_primary']
        )
        chart_title.pack(pady=10)

        # Placeholder for performance chart
        chart_placeholder = tk.Label(
            chart_frame,
            text="График производительности будет отображаться здесь\n(CPU, память, запросы в реальном времени)",
            font=ModernStyles.FONTS['body'],
            bg='white',
            fg=ModernStyles.COLORS['text_secondary'],
            height=8
        )
        chart_placeholder.pack(fill='both', expand=True, padx=20, pady=20)

    def create_metrics_section(self, parent, title):
        """Create a metrics section with title"""
        section_frame = tk.Frame(parent, bg='white')
        section_frame.pack(fill='x', pady=(0, 20))

        title_label = tk.Label(
            section_frame,
            text=title,
            font=ModernStyles.FONTS['subtitle'],
            bg='white',
            fg=ModernStyles.COLORS['text_primary']
        )
        title_label.pack(anchor='w', pady=(0, 10))

        cards_frame = tk.Frame(section_frame, bg='white')
        cards_frame.pack(fill='x')

        return cards_frame

    def create_metric_card(self, parent, title, metric_key, unit, category):
        """Create a metric display card"""
        card_frame = tk.Frame(parent, bg=ModernStyles.COLORS['bg_secondary'], relief='solid', bd=1)
        card_frame.pack(side='left', fill='both', expand=True, padx=5, pady=5)

        # Title
        title_label = tk.Label(
            card_frame,
            text=title,
            font=ModernStyles.FONTS['small'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_secondary']
        )
        title_label.pack(pady=(10, 5))

        # Value
        value = self.metrics[category].get(metric_key, 0)
        if isinstance(value, float):
            display_value = f"{value:.1f}"
        else:
            display_value = str(value)

        value_label = tk.Label(
            card_frame,
            text=f"{display_value} {unit}",
            font=ModernStyles.FONTS['heading'],
            bg=ModernStyles.COLORS['bg_secondary'],
            fg=ModernStyles.COLORS['text_primary']
        )
        value_label.pack(pady=(0, 10))

        # Store reference for updates
        setattr(self, f"{category}_{metric_key}_label", value_label)

    def create_database_optimization_tab(self):
        """Create database optimization tab"""
        db_frame = ttk.Frame(self.notebook)
        self.notebook.add(db_frame, text="🗄️ База Данных")

        # Create main container
        main_container = tk.Frame(db_frame, bg='white')
        main_container.pack(fill='both', expand=True, padx=20, pady=20)

        # Database optimization controls
        controls_frame = tk.Frame(main_container, bg='white', relief='solid', bd=1)
        controls_frame.pack(fill='x', pady=(0, 20))

        controls_title = tk.Label(
            controls_frame,
            text="🔧 Оптимизация База Данных",
            font=ModernStyles.FONTS['subtitle'],
            bg='white',
            fg=ModernStyles.COLORS['text_primary']
        )
        controls_title.pack(pady=10)

        # Optimization buttons
        buttons_frame = tk.Frame(controls_frame, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=(0, 20))

        optimization_buttons = [
            ("🚀 Оптимизировать Индексы", self.optimize_indexes),
            ("🧹 Очистить Кэш Запросов", self.clear_query_cache),
            ("📊 Анализ Запросов", self.analyze_queries),
            ("🔄 Обновить Статистику", self.update_db_statistics),
            ("💾 Сжать БД", self.compress_database),
            ("🔍 VACUUM", self.vacuum_database)
        ]

        for i, (text, command) in enumerate(optimization_buttons):
            row = i // 3
            col = i % 3

            btn = tk.Button(
                buttons_frame,
                text=text,
                command=command,
                font=ModernStyles.FONTS['button'],
                bg=ModernStyles.COLORS['primary'],
                fg='white',
                relief='flat',
                padx=15,
                pady=8
            )
            btn.grid(row=row, column=col, padx=5, pady=5, sticky='ew')

        # Configure grid weights
        for i in range(3):
            buttons_frame.columnconfigure(i, weight=1)

        # Database statistics
        stats_frame = tk.Frame(main_container, bg='white', relief='solid', bd=1)
        stats_frame.pack(fill='both', expand=True)

        stats_title = tk.Label(
            stats_frame,
            text="📈 Статистика База Данных",
            font=ModernStyles.FONTS['subtitle'],
            bg='white',
            fg=ModernStyles.COLORS['text_primary']
        )
        stats_title.pack(pady=10)

        # Create treeview for database statistics
        columns = ('Таблица', 'Записи', 'Размер', 'Индексы', 'Последнее обновление')
        self.db_stats_tree = ttk.Treeview(stats_frame, columns=columns, show='headings', height=12)

        # Configure columns
        column_widths = {'Таблица': 200, 'Записи': 100, 'Размер': 100, 'Индексы': 100, 'Последнее обновление': 150}
        for col in columns:
            self.db_stats_tree.heading(col, text=col)
            self.db_stats_tree.column(col, width=column_widths.get(col, 100), anchor='center')

        # Add scrollbar
        db_scrollbar = ttk.Scrollbar(stats_frame, orient="vertical", command=self.db_stats_tree.yview)
        self.db_stats_tree.configure(yscrollcommand=db_scrollbar.set)

        self.db_stats_tree.pack(side="left", fill="both", expand=True, padx=20, pady=(0, 20))
        db_scrollbar.pack(side="right", fill="y", pady=(0, 20))

    def create_ui_optimization_tab(self):
        """Create UI optimization tab"""
        ui_frame = ttk.Frame(self.notebook)
        self.notebook.add(ui_frame, text="🖼️ Интерфейс")

        # Create main container
        main_container = tk.Frame(ui_frame, bg='white')
        main_container.pack(fill='both', expand=True, padx=20, pady=20)

        # UI optimization settings
        settings_frame = tk.Frame(main_container, bg='white', relief='solid', bd=1)
        settings_frame.pack(fill='x', pady=(0, 20))

        settings_title = tk.Label(
            settings_frame,
            text="⚙️ Настройки Оптимизации UI",
            font=ModernStyles.FONTS['subtitle'],
            bg='white',
            fg=ModernStyles.COLORS['text_primary']
        )
        settings_title.pack(pady=10)

        # Settings grid
        settings_grid = tk.Frame(settings_frame, bg='white')
        settings_grid.pack(fill='x', padx=20, pady=(0, 20))

        # Virtual scrolling
        self.virtual_scrolling_var = tk.BooleanVar(value=self.optimization_settings['ui']['enable_virtual_scrolling'])
        virtual_cb = tk.Checkbutton(
            settings_grid,
            text="Включить виртуальную прокрутку для больших списков",
            variable=self.virtual_scrolling_var,
            font=ModernStyles.FONTS['body'],
            bg='white'
        )
        virtual_cb.grid(row=0, column=0, sticky='w', pady=5)

        # Lazy loading
        self.lazy_loading_var = tk.BooleanVar(value=self.optimization_settings['ui']['lazy_load_images'])
        lazy_cb = tk.Checkbutton(
            settings_grid,
            text="Ленивая загрузка изображений",
            variable=self.lazy_loading_var,
            font=ModernStyles.FONTS['body'],
            bg='white'
        )
        lazy_cb.grid(row=1, column=0, sticky='w', pady=5)

        # Debounce updates
        self.debounce_var = tk.BooleanVar(value=self.optimization_settings['ui']['debounce_updates'])
        debounce_cb = tk.Checkbutton(
            settings_grid,
            text="Отложенное обновление UI",
            variable=self.debounce_var,
            font=ModernStyles.FONTS['body'],
            bg='white'
        )
        debounce_cb.grid(row=2, column=0, sticky='w', pady=5)

        # Update interval
        interval_frame = tk.Frame(settings_grid, bg='white')
        interval_frame.grid(row=3, column=0, sticky='w', pady=5)

        tk.Label(
            interval_frame,
            text="Интервал обновления (мс):",
            font=ModernStyles.FONTS['body'],
            bg='white'
        ).pack(side='left')

        self.update_interval_var = tk.StringVar(value=str(self.optimization_settings['ui']['update_interval']))
        interval_entry = tk.Entry(
            interval_frame,
            textvariable=self.update_interval_var,
            font=ModernStyles.FONTS['body'],
            width=10
        )
        interval_entry.pack(side='left', padx=(10, 0))

        # Apply settings button
        apply_btn = tk.Button(
            settings_frame,
            text="✅ Применить Настройки",
            command=self.apply_ui_settings,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['success'],
            fg='white',
            relief='flat',
            padx=20,
            pady=8
        )
        apply_btn.pack(pady=(0, 20))

        # UI performance metrics
        metrics_frame = tk.Frame(main_container, bg='white', relief='solid', bd=1)
        metrics_frame.pack(fill='both', expand=True)

        metrics_title = tk.Label(
            metrics_frame,
            text="📊 Метрики Производительности UI",
            font=ModernStyles.FONTS['subtitle'],
            bg='white',
            fg=ModernStyles.COLORS['text_primary']
        )
        metrics_title.pack(pady=10)

        # Metrics display
        metrics_display = tk.Frame(metrics_frame, bg='white')
        metrics_display.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        # Create metrics labels
        self.ui_metrics_labels = {}
        metrics_info = [
            ("Открытые окна", "window_count"),
            ("Использование памяти UI", "memory_usage"),
            ("Время отклика", "response_time"),
            ("Частота обновлений", "update_frequency")
        ]

        for i, (label, key) in enumerate(metrics_info):
            row = i // 2
            col = i % 2

            metric_frame = tk.Frame(metrics_display, bg=ModernStyles.COLORS['bg_secondary'], relief='solid', bd=1)
            metric_frame.grid(row=row, column=col, padx=10, pady=10, sticky='ew')

            tk.Label(
                metric_frame,
                text=label,
                font=ModernStyles.FONTS['body'],
                bg=ModernStyles.COLORS['bg_secondary'],
                fg=ModernStyles.COLORS['text_primary']
            ).pack(pady=(10, 5))

            value_label = tk.Label(
                metric_frame,
                text="0",
                font=ModernStyles.FONTS['heading'],
                bg=ModernStyles.COLORS['bg_secondary'],
                fg=ModernStyles.COLORS['primary']
            )
            value_label.pack(pady=(0, 10))

            self.ui_metrics_labels[key] = value_label

        # Configure grid weights
        for i in range(2):
            metrics_display.columnconfigure(i, weight=1)

    def create_system_monitoring_tab(self):
        """Create system monitoring tab"""
        system_frame = ttk.Frame(self.notebook)
        self.notebook.add(system_frame, text="🖥️ Система")

        # Create main container
        main_container = tk.Frame(system_frame, bg='white')
        main_container.pack(fill='both', expand=True, padx=20, pady=20)

        # System information
        info_frame = tk.Frame(main_container, bg='white', relief='solid', bd=1)
        info_frame.pack(fill='x', pady=(0, 20))

        info_title = tk.Label(
            info_frame,
            text="ℹ️ Информация о Системе",
            font=ModernStyles.FONTS['subtitle'],
            bg='white',
            fg=ModernStyles.COLORS['text_primary']
        )
        info_title.pack(pady=10)

        # System info display
        info_display = tk.Frame(info_frame, bg='white')
        info_display.pack(fill='x', padx=20, pady=(0, 20))

        # Get system information
        try:
            import platform
            system_info = {
                "Операционная система": f"{platform.system()} {platform.release()}",
                "Архитектура": platform.machine(),
                "Процессор": platform.processor() or "Неизвестно",
                "Python версия": platform.python_version(),
                "Память": f"{psutil.virtual_memory().total / (1024**3):.1f} ГБ"
            }
        except:
            system_info = {"Ошибка": "Не удалось получить информацию о системе"}

        for i, (key, value) in enumerate(system_info.items()):
            info_row = tk.Frame(info_display, bg='white')
            info_row.pack(fill='x', pady=2)

            tk.Label(
                info_row,
                text=f"{key}:",
                font=ModernStyles.FONTS['body_bold'],
                bg='white',
                fg=ModernStyles.COLORS['text_primary']
            ).pack(side='left')

            tk.Label(
                info_row,
                text=value,
                font=ModernStyles.FONTS['body'],
                bg='white',
                fg=ModernStyles.COLORS['text_secondary']
            ).pack(side='left', padx=(10, 0))

        # Real-time monitoring
        monitoring_frame = tk.Frame(main_container, bg='white', relief='solid', bd=1)
        monitoring_frame.pack(fill='both', expand=True)

        monitoring_title = tk.Label(
            monitoring_frame,
            text="📊 Мониторинг в Реальном Времени",
            font=ModernStyles.FONTS['subtitle'],
            bg='white',
            fg=ModernStyles.COLORS['text_primary']
        )
        monitoring_title.pack(pady=10)

        # Monitoring controls
        controls_frame = tk.Frame(monitoring_frame, bg='white')
        controls_frame.pack(fill='x', padx=20, pady=(0, 10))

        self.monitoring_status_label = tk.Label(
            controls_frame,
            text="🟢 Мониторинг активен",
            font=ModernStyles.FONTS['body'],
            bg='white',
            fg='green'
        )
        self.monitoring_status_label.pack(side='left')

        toggle_btn = tk.Button(
            controls_frame,
            text="⏸️ Остановить",
            command=self.toggle_monitoring,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['warning'],
            fg='white',
            relief='flat',
            padx=15,
            pady=5
        )
        toggle_btn.pack(side='right')

        # System metrics display
        metrics_canvas = tk.Canvas(monitoring_frame, bg='white', height=200)
        metrics_canvas.pack(fill='x', padx=20, pady=(0, 20))

        # Placeholder for real-time charts
        metrics_canvas.create_text(
            400, 100,
            text="Графики системных метрик в реальном времени\n(CPU, память, диск, сеть)",
            font=ModernStyles.FONTS['body'],
            fill=ModernStyles.COLORS['text_secondary']
        )

    def create_cache_management_tab(self):
        """Create cache management tab"""
        cache_frame = ttk.Frame(self.notebook)
        self.notebook.add(cache_frame, text="💾 Кэш")

        # Create main container
        main_container = tk.Frame(cache_frame, bg='white')
        main_container.pack(fill='both', expand=True, padx=20, pady=20)

        # Cache controls
        controls_frame = tk.Frame(main_container, bg='white', relief='solid', bd=1)
        controls_frame.pack(fill='x', pady=(0, 20))

        controls_title = tk.Label(
            controls_frame,
            text="🔧 Управление Кэшем",
            font=ModernStyles.FONTS['subtitle'],
            bg='white',
            fg=ModernStyles.COLORS['text_primary']
        )
        controls_title.pack(pady=10)

        # Cache action buttons
        buttons_frame = tk.Frame(controls_frame, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=(0, 20))

        cache_buttons = [
            ("🧹 Очистить Весь Кэш", self.clear_all_cache),
            ("🔄 Обновить Кэш", self.refresh_cache),
            ("📊 Статистика Кэша", self.show_cache_stats),
            ("⚙️ Настройки Кэша", self.configure_cache)
        ]

        for i, (text, command) in enumerate(cache_buttons):
            btn = tk.Button(
                buttons_frame,
                text=text,
                command=command,
                font=ModernStyles.FONTS['button'],
                bg=ModernStyles.COLORS['primary'],
                fg='white',
                relief='flat',
                padx=15,
                pady=8
            )
            btn.grid(row=i//2, column=i%2, padx=5, pady=5, sticky='ew')

        # Configure grid weights
        buttons_frame.columnconfigure(0, weight=1)
        buttons_frame.columnconfigure(1, weight=1)

        # Cache statistics
        stats_frame = tk.Frame(main_container, bg='white', relief='solid', bd=1)
        stats_frame.pack(fill='both', expand=True)

        stats_title = tk.Label(
            stats_frame,
            text="📈 Статистика Кэша",
            font=ModernStyles.FONTS['subtitle'],
            bg='white',
            fg=ModernStyles.COLORS['text_primary']
        )
        stats_title.pack(pady=10)

        # Cache stats display
        stats_display = tk.Frame(stats_frame, bg='white')
        stats_display.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        # Create cache statistics tree
        columns = ('Тип Кэша', 'Размер', 'Записи', 'Попадания', 'Промахи', 'Коэффициент')
        self.cache_stats_tree = ttk.Treeview(stats_display, columns=columns, show='headings', height=8)

        # Configure columns
        column_widths = {
            'Тип Кэша': 150,
            'Размер': 100,
            'Записи': 80,
            'Попадания': 100,
            'Промахи': 100,
            'Коэффициент': 100
        }

        for col in columns:
            self.cache_stats_tree.heading(col, text=col)
            self.cache_stats_tree.column(col, width=column_widths.get(col, 100), anchor='center')

        # Add scrollbar
        cache_scrollbar = ttk.Scrollbar(stats_display, orient="vertical", command=self.cache_stats_tree.yview)
        self.cache_stats_tree.configure(yscrollcommand=cache_scrollbar.set)

        self.cache_stats_tree.pack(side="left", fill="both", expand=True)
        cache_scrollbar.pack(side="right", fill="y")

    def create_query_analysis_tab(self):
        """Create query analysis tab"""
        query_frame = ttk.Frame(self.notebook)
        self.notebook.add(query_frame, text="🔍 Запросы")

        # Create main container
        main_container = tk.Frame(query_frame, bg='white')
        main_container.pack(fill='both', expand=True, padx=20, pady=20)

        # Query analysis controls
        controls_frame = tk.Frame(main_container, bg='white', relief='solid', bd=1)
        controls_frame.pack(fill='x', pady=(0, 20))

        controls_title = tk.Label(
            controls_frame,
            text="🔍 Анализ Запросов",
            font=ModernStyles.FONTS['subtitle'],
            bg='white',
            fg=ModernStyles.COLORS['text_primary']
        )
        controls_title.pack(pady=10)

        # Analysis buttons
        buttons_frame = tk.Frame(controls_frame, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=(0, 20))

        analysis_buttons = [
            ("🐌 Медленные Запросы", self.show_slow_queries),
            ("📊 Частые Запросы", self.show_frequent_queries),
            ("🔧 Рекомендации", self.show_query_recommendations),
            ("📈 Статистика", self.show_query_statistics)
        ]

        for i, (text, command) in enumerate(analysis_buttons):
            btn = tk.Button(
                buttons_frame,
                text=text,
                command=command,
                font=ModernStyles.FONTS['button'],
                bg=ModernStyles.COLORS['primary'],
                fg='white',
                relief='flat',
                padx=15,
                pady=8
            )
            btn.grid(row=i//2, column=i%2, padx=5, pady=5, sticky='ew')

        # Configure grid weights
        buttons_frame.columnconfigure(0, weight=1)
        buttons_frame.columnconfigure(1, weight=1)

        # Query analysis results
        results_frame = tk.Frame(main_container, bg='white', relief='solid', bd=1)
        results_frame.pack(fill='both', expand=True)

        results_title = tk.Label(
            results_frame,
            text="📋 Результаты Анализа",
            font=ModernStyles.FONTS['subtitle'],
            bg='white',
            fg=ModernStyles.COLORS['text_primary']
        )
        results_title.pack(pady=10)

        # Query results tree
        columns = ('Запрос', 'Время (мс)', 'Частота', 'Последнее выполнение', 'Статус')
        self.query_results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=12)

        # Configure columns
        column_widths = {
            'Запрос': 300,
            'Время (мс)': 100,
            'Частота': 80,
            'Последнее выполнение': 150,
            'Статус': 100
        }

        for col in columns:
            self.query_results_tree.heading(col, text=col)
            self.query_results_tree.column(col, width=column_widths.get(col, 100), anchor='center')

        # Add scrollbar
        query_scrollbar = ttk.Scrollbar(results_frame, orient="vertical", command=self.query_results_tree.yview)
        self.query_results_tree.configure(yscrollcommand=query_scrollbar.set)

        self.query_results_tree.pack(side="left", fill="both", expand=True, padx=20, pady=(0, 20))
        query_scrollbar.pack(side="right", fill="y", pady=(0, 20))

    def create_recommendations_tab(self):
        """Create recommendations tab"""
        recommendations_frame = ttk.Frame(self.notebook)
        self.notebook.add(recommendations_frame, text="💡 Рекомендации")

        # Create main container
        main_container = tk.Frame(recommendations_frame, bg='white')
        main_container.pack(fill='both', expand=True, padx=20, pady=20)

        # Recommendations header
        header_frame = tk.Frame(main_container, bg='white', relief='solid', bd=1)
        header_frame.pack(fill='x', pady=(0, 20))

        header_title = tk.Label(
            header_frame,
            text="💡 Рекомендации по Оптимизации",
            font=ModernStyles.FONTS['subtitle'],
            bg='white',
            fg=ModernStyles.COLORS['text_primary']
        )
        header_title.pack(pady=10)

        # Generate recommendations button
        generate_btn = tk.Button(
            header_frame,
            text="🔄 Обновить Рекомендации",
            command=self.generate_recommendations,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['primary'],
            fg='white',
            relief='flat',
            padx=20,
            pady=8
        )
        generate_btn.pack(pady=(0, 20))

        # Recommendations display
        recommendations_display = tk.Frame(main_container, bg='white', relief='solid', bd=1)
        recommendations_display.pack(fill='both', expand=True)

        # Create scrollable text widget for recommendations
        text_frame = tk.Frame(recommendations_display, bg='white')
        text_frame.pack(fill='both', expand=True, padx=20, pady=20)

        self.recommendations_text = tk.Text(
            text_frame,
            font=ModernStyles.FONTS['body'],
            bg='white',
            fg=ModernStyles.COLORS['text_primary'],
            wrap='word',
            height=20
        )

        recommendations_scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=self.recommendations_text.yview)
        self.recommendations_text.configure(yscrollcommand=recommendations_scrollbar.set)

        self.recommendations_text.pack(side="left", fill="both", expand=True)
        recommendations_scrollbar.pack(side="right", fill="y")

        # Load initial recommendations
        self.generate_recommendations()

    # Optimization Methods
    def run_optimization(self):
        """Run comprehensive system optimization"""
        try:
            self.status_label.config(text="Выполняется оптимизация системы...")

            # Database optimization
            self.optimize_indexes()
            self.update_db_statistics()

            # Cache optimization
            self.refresh_cache()

            # UI optimization
            self.apply_ui_settings()

            # System cleanup
            self.cleanup_system()

            self.status_label.config(text="Оптимизация завершена успешно")
            messagebox.showinfo("Успех", "Система оптимизирована успешно!")

        except Exception as e:
            self.status_label.config(text="Ошибка при оптимизации")
            messagebox.showerror("Ошибка", f"Ошибка при оптимизации: {e}")

    def optimize_indexes(self):
        """Optimize database indexes"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                # Get all tables
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()

                optimizations_applied = 0

                for table in tables:
                    table_name = table[0]

                    # Skip system tables
                    if table_name.startswith('sqlite_'):
                        continue

                    # Create common indexes if they don't exist
                    common_indexes = [
                        f"CREATE INDEX IF NOT EXISTS idx_{table_name}_created_at ON {table_name}(created_at)",
                        f"CREATE INDEX IF NOT EXISTS idx_{table_name}_updated_at ON {table_name}(updated_at)",
                        f"CREATE INDEX IF NOT EXISTS idx_{table_name}_is_active ON {table_name}(is_active)"
                    ]

                    for index_sql in common_indexes:
                        try:
                            cursor.execute(index_sql)
                            optimizations_applied += 1
                        except sqlite3.Error:
                            pass  # Index might not be applicable to this table

                conn.commit()
                self.status_label.config(text=f"Создано {optimizations_applied} индексов")

        except Exception as e:
            log_info(f"Error optimizing indexes: {e}", "PerformanceOptimization")

    def clear_cache(self):
        """Clear all caches"""
        try:
            self.query_cache.clear()
            self.lazy_loader.clear_cache()

            # Force garbage collection
            gc.collect()

            self.status_label.config(text="Кэш очищен")
            messagebox.showinfo("Успех", "Кэш успешно очищен!")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка при очистке кэша: {e}")

    def clear_query_cache(self):
        """Clear query cache specifically"""
        try:
            self.query_cache.clear()
            self.status_label.config(text="Кэш запросов очищен")

        except Exception as e:
            log_info(f"Error clearing query cache: {e}", "PerformanceOptimization")

    def analyze_queries(self):
        """Analyze database queries"""
        try:
            # Get query statistics from performance monitor
            report = self.performance_monitor.get_performance_report()

            # Display in query analysis tab
            self.notebook.select(5)  # Switch to query analysis tab

            # Clear existing results
            for item in self.query_results_tree.get_children():
                self.query_results_tree.delete(item)

            # Add query analysis results
            for query_hash, stats in report.get('slow_queries', {}).items():
                self.query_results_tree.insert('', 'end', values=(
                    stats.get('query', 'Unknown')[:50] + "...",
                    f"{stats.get('avg_time', 0):.2f}",
                    stats.get('count', 0),
                    stats.get('last_executed', 'Unknown'),
                    "Медленный" if stats.get('avg_time', 0) > 100 else "Нормальный"
                ))

            self.status_label.config(text="Анализ запросов завершен")

        except Exception as e:
            log_info(f"Error analyzing queries: {e}", "PerformanceOptimization")

    def run_analysis(self):
        """Run comprehensive performance analysis"""
        try:
            self.status_label.config(text="Выполняется анализ производительности...")

            # Update all metrics
            self.update_system_metrics()
            self.update_database_metrics()
            self.update_ui_metrics()

            # Analyze queries
            self.analyze_queries()

            # Generate recommendations
            self.generate_recommendations()

            self.status_label.config(text="Анализ производительности завершен")
            messagebox.showinfo("Анализ", "Анализ производительности завершен. Проверьте рекомендации.")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка при анализе: {e}")

    def open_settings(self):
        """Open optimization settings dialog"""
        settings_window = tk.Toplevel(self.window)
        settings_window.title("⚙️ Настройки Оптимизации")
        settings_window.geometry("600x500")
        settings_window.configure(bg='white')

        # Center the window
        settings_window.transient(self.window)
        settings_window.grab_set()

        # Settings content
        tk.Label(
            settings_window,
            text="⚙️ Настройки Оптимизации",
            font=ModernStyles.FONTS['title'],
            bg='white',
            fg=ModernStyles.COLORS['text_primary']
        ).pack(pady=20)

        # Settings will be implemented here
        tk.Label(
            settings_window,
            text="Настройки оптимизации будут доступны в следующей версии",
            font=ModernStyles.FONTS['body'],
            bg='white',
            fg=ModernStyles.COLORS['text_secondary']
        ).pack(pady=50)

        # Close button
        tk.Button(
            settings_window,
            text="Закрыть",
            command=settings_window.destroy,
            font=ModernStyles.FONTS['button'],
            bg=ModernStyles.COLORS['primary'],
            fg='white',
            relief='flat',
            padx=20,
            pady=8
        ).pack(pady=20)

    def update_db_statistics(self):
        """Update database statistics"""
        try:
            # Clear existing data
            for item in self.db_stats_tree.get_children():
                self.db_stats_tree.delete(item)

            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                # Get table statistics
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()

                for table in tables:
                    table_name = table[0]

                    if table_name.startswith('sqlite_'):
                        continue

                    # Get row count
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    row_count = cursor.fetchone()[0]

                    # Get table size (approximate)
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = cursor.fetchall()

                    # Get index count
                    cursor.execute(f"PRAGMA index_list({table_name})")
                    indexes = cursor.fetchall()

                    self.db_stats_tree.insert('', 'end', values=(
                        table_name,
                        f"{row_count:,}",
                        "~KB",  # Placeholder for size
                        len(indexes),
                        datetime.now().strftime("%d.%m.%Y %H:%M")
                    ))

            self.status_label.config(text="Статистика базы данных обновлена")

        except Exception as e:
            log_info(f"Error updating database statistics: {e}", "PerformanceOptimization")

    def compress_database(self):
        """Compress database using VACUUM"""
        try:
            with self.db_manager.get_connection() as conn:
                conn.execute("VACUUM")

            self.status_label.config(text="База данных сжата")
            messagebox.showinfo("Успех", "База данных успешно сжата!")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка при сжатии базы данных: {e}")

    def vacuum_database(self):
        """Run VACUUM on database"""
        self.compress_database()

    def apply_ui_settings(self):
        """Apply UI optimization settings"""
        try:
            # Update settings from UI
            self.optimization_settings['ui']['enable_virtual_scrolling'] = self.virtual_scrolling_var.get()
            self.optimization_settings['ui']['lazy_load_images'] = self.lazy_loading_var.get()
            self.optimization_settings['ui']['debounce_updates'] = self.debounce_var.get()

            try:
                self.optimization_settings['ui']['update_interval'] = int(self.update_interval_var.get())
            except ValueError:
                self.optimization_settings['ui']['update_interval'] = 1000

            self.status_label.config(text="Настройки UI применены")
            messagebox.showinfo("Успех", "Настройки интерфейса применены!")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка при применении настроек: {e}")

    def toggle_monitoring(self):
        """Toggle performance monitoring"""
        if self.monitoring_active:
            self.stop_monitoring()
            self.monitoring_status_label.config(text="🔴 Мониторинг остановлен", fg='red')
        else:
            self.start_monitoring()
            self.monitoring_status_label.config(text="🟢 Мониторинг активен", fg='green')

    def clear_all_cache(self):
        """Clear all cache types"""
        self.clear_cache()

    def refresh_cache(self):
        """Refresh cache with current data"""
        try:
            # Clear old cache
            self.clear_cache()

            # Preload common queries
            common_queries = [
                "SELECT * FROM recipes LIMIT 10",
                "SELECT * FROM raw_materials LIMIT 10",
                "SELECT * FROM sales ORDER BY order_date DESC LIMIT 10"
            ]

            for query in common_queries:
                try:
                    self.optimized_db.cached_query(query)
                except:
                    pass  # Ignore errors for non-existent tables

            self.status_label.config(text="Кэш обновлен")

        except Exception as e:
            log_info(f"Error refreshing cache: {e}", "PerformanceOptimization")

    def show_cache_stats(self):
        """Show cache statistics"""
        try:
            # Clear existing data
            for item in self.cache_stats_tree.get_children():
                self.cache_stats_tree.delete(item)

            # Get cache statistics
            cache_stats = self.query_cache.get_stats()

            # Add cache statistics
            hit_rate = 0
            if cache_stats['total_requests'] > 0:
                hit_rate = (cache_stats['hits'] / cache_stats['total_requests']) * 100

            self.cache_stats_tree.insert('', 'end', values=(
                "Кэш запросов",
                cache_stats.get('memory_usage', 'Unknown'),
                cache_stats.get('entries', 0),
                cache_stats.get('hits', 0),
                cache_stats.get('misses', 0),
                f"{hit_rate:.1f}%"
            ))

            # Switch to cache tab
            self.notebook.select(4)

        except Exception as e:
            log_info(f"Error showing cache stats: {e}", "PerformanceOptimization")

    def configure_cache(self):
        """Configure cache settings"""
        # Placeholder for cache configuration
        messagebox.showinfo("Информация", "Настройка кэша будет доступна в следующей версии")

    def show_slow_queries(self):
        """Show slow queries analysis"""
        self.analyze_queries()

    def show_frequent_queries(self):
        """Show frequent queries analysis"""
        self.analyze_queries()

    def show_query_recommendations(self):
        """Show query optimization recommendations"""
        self.generate_recommendations()
        self.notebook.select(6)  # Switch to recommendations tab

    def show_query_statistics(self):
        """Show query statistics"""
        self.analyze_queries()

    def generate_recommendations(self):
        """Generate performance optimization recommendations"""
        try:
            recommendations = []

            # System recommendations
            cpu_usage = self.metrics['system']['cpu_usage']
            memory_usage = self.metrics['system']['memory_usage']

            if cpu_usage > 80:
                recommendations.append("🔴 КРИТИЧНО: Высокая загрузка процессора ({}%). Рекомендуется оптимизировать запросы и алгоритмы.".format(cpu_usage))
            elif cpu_usage > 60:
                recommendations.append("🟡 ВНИМАНИЕ: Повышенная загрузка процессора ({}%). Рассмотрите возможность оптимизации.".format(cpu_usage))

            if memory_usage > 85:
                recommendations.append("🔴 КРИТИЧНО: Высокое использование памяти ({}%). Необходимо освободить память.".format(memory_usage))
            elif memory_usage > 70:
                recommendations.append("🟡 ВНИМАНИЕ: Повышенное использование памяти ({}%). Рекомендуется мониторинг.".format(memory_usage))

            # Database recommendations
            cache_hit_rate = self.metrics['database']['cache_hit_rate']
            if cache_hit_rate < 50:
                recommendations.append("🔴 БАЗА ДАННЫХ: Низкий коэффициент попаданий в кэш ({}%). Увеличьте размер кэша или оптимизируйте запросы.".format(cache_hit_rate))
            elif cache_hit_rate < 80:
                recommendations.append("🟡 БАЗА ДАННЫХ: Средний коэффициент попаданий в кэш ({}%). Есть возможности для улучшения.".format(cache_hit_rate))

            # UI recommendations
            window_count = self.metrics['ui']['window_count']
            if window_count > 10:
                recommendations.append("🟡 ИНТЕРФЕЙС: Много открытых окон ({}). Закройте неиспользуемые окна для экономии памяти.".format(window_count))

            # General recommendations
            recommendations.extend([
                "✅ ОБЩИЕ РЕКОМЕНДАЦИИ:",
                "• Регулярно очищайте кэш для освобождения памяти",
                "• Используйте индексы для часто запрашиваемых полей",
                "• Ограничивайте количество записей в больших запросах",
                "• Закрывайте неиспользуемые окна и модули",
                "• Выполняйте VACUUM для сжатия базы данных",
                "• Мониторьте производительность системы регулярно"
            ])

            # Display recommendations
            self.recommendations_text.delete(1.0, tk.END)
            self.recommendations_text.insert(tk.END, "\n".join(recommendations))

        except Exception as e:
            log_info(f"Error generating recommendations: {e}", "PerformanceOptimization")

    def cleanup_system(self):
        """Perform system cleanup"""
        try:
            # Force garbage collection
            gc.collect()

            # Clear temporary data
            if hasattr(self, 'temp_data'):
                self.temp_data.clear()

            self.status_label.config(text="Система очищена")

        except Exception as e:
            log_info(f"Error during system cleanup: {e}", "PerformanceOptimization")

    def refresh_all_displays(self):
        """Refresh all performance displays"""
        try:
            self.load_performance_data()
            self.update_db_statistics()
            self.show_cache_stats()
            self.generate_recommendations()

        except Exception as e:
            log_info(f"Error refreshing displays: {e}", "PerformanceOptimization")


def show_performance_optimization(parent, db_manager):
    """Show performance optimization system"""
    try:
        system = PerformanceOptimizationSystem(parent, db_manager)
        return system
    except Exception as e:
        handle_module_error(e, "Performance Optimization", "открытие системы оптимизации")
        return None


if __name__ == "__main__":
    # Test the module
    root = tk.Tk()
    root.withdraw()

    # Mock database manager for testing
    class MockDBManager:
        def get_connection(self):
            return None

    db_manager = MockDBManager()
    show_performance_optimization(root, db_manager)
